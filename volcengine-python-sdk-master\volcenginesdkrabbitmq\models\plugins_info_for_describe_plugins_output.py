# coding: utf-8

"""
    rabbitmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PluginsInfoForDescribePluginsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'disable_prompt': 'str',
        'enable_prompt': 'str',
        'enabled': 'bool',
        'need_reboot_on_change': 'bool',
        'plugin_name': 'str',
        'port': 'int',
        'version': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'disable_prompt': 'DisablePrompt',
        'enable_prompt': 'EnablePrompt',
        'enabled': 'Enabled',
        'need_reboot_on_change': 'NeedRebootOnChange',
        'plugin_name': 'PluginName',
        'port': 'Port',
        'version': 'Version'
    }

    def __init__(self, description=None, disable_prompt=None, enable_prompt=None, enabled=None, need_reboot_on_change=None, plugin_name=None, port=None, version=None, _configuration=None):  # noqa: E501
        """PluginsInfoForDescribePluginsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._disable_prompt = None
        self._enable_prompt = None
        self._enabled = None
        self._need_reboot_on_change = None
        self._plugin_name = None
        self._port = None
        self._version = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if disable_prompt is not None:
            self.disable_prompt = disable_prompt
        if enable_prompt is not None:
            self.enable_prompt = enable_prompt
        if enabled is not None:
            self.enabled = enabled
        if need_reboot_on_change is not None:
            self.need_reboot_on_change = need_reboot_on_change
        if plugin_name is not None:
            self.plugin_name = plugin_name
        if port is not None:
            self.port = port
        if version is not None:
            self.version = version

    @property
    def description(self):
        """Gets the description of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The description of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this PluginsInfoForDescribePluginsOutput.


        :param description: The description of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def disable_prompt(self):
        """Gets the disable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The disable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: str
        """
        return self._disable_prompt

    @disable_prompt.setter
    def disable_prompt(self, disable_prompt):
        """Sets the disable_prompt of this PluginsInfoForDescribePluginsOutput.


        :param disable_prompt: The disable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: str
        """

        self._disable_prompt = disable_prompt

    @property
    def enable_prompt(self):
        """Gets the enable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The enable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_prompt

    @enable_prompt.setter
    def enable_prompt(self, enable_prompt):
        """Sets the enable_prompt of this PluginsInfoForDescribePluginsOutput.


        :param enable_prompt: The enable_prompt of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: str
        """

        self._enable_prompt = enable_prompt

    @property
    def enabled(self):
        """Gets the enabled of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The enabled of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this PluginsInfoForDescribePluginsOutput.


        :param enabled: The enabled of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def need_reboot_on_change(self):
        """Gets the need_reboot_on_change of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The need_reboot_on_change of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._need_reboot_on_change

    @need_reboot_on_change.setter
    def need_reboot_on_change(self, need_reboot_on_change):
        """Sets the need_reboot_on_change of this PluginsInfoForDescribePluginsOutput.


        :param need_reboot_on_change: The need_reboot_on_change of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: bool
        """

        self._need_reboot_on_change = need_reboot_on_change

    @property
    def plugin_name(self):
        """Gets the plugin_name of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The plugin_name of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: str
        """
        return self._plugin_name

    @plugin_name.setter
    def plugin_name(self, plugin_name):
        """Sets the plugin_name of this PluginsInfoForDescribePluginsOutput.


        :param plugin_name: The plugin_name of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: str
        """

        self._plugin_name = plugin_name

    @property
    def port(self):
        """Gets the port of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The port of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this PluginsInfoForDescribePluginsOutput.


        :param port: The port of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def version(self):
        """Gets the version of this PluginsInfoForDescribePluginsOutput.  # noqa: E501


        :return: The version of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this PluginsInfoForDescribePluginsOutput.


        :param version: The version of this PluginsInfoForDescribePluginsOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PluginsInfoForDescribePluginsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PluginsInfoForDescribePluginsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PluginsInfoForDescribePluginsOutput):
            return True

        return self.to_dict() != other.to_dict()
