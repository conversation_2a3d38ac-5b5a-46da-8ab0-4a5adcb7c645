# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SlowLogForDescribeSlowLogsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'connection_id': 'int',
        'db': 'str',
        'lock_time': 'float',
        'query_time': 'float',
        'rows_examined': 'int',
        'rows_sent': 'int',
        'sql_template': 'str',
        'sql_text': 'str',
        'source_ip': 'str',
        'sql_fingerprint': 'str',
        'sql_method': 'str',
        'table': 'str',
        'timestamp': 'int',
        'user': 'str'
    }

    attribute_map = {
        'connection_id': 'ConnectionId',
        'db': 'DB',
        'lock_time': 'LockTime',
        'query_time': 'QueryTime',
        'rows_examined': 'RowsExamined',
        'rows_sent': 'RowsSent',
        'sql_template': 'SQLTemplate',
        'sql_text': 'SQLText',
        'source_ip': 'SourceIP',
        'sql_fingerprint': 'SqlFingerprint',
        'sql_method': 'SqlMethod',
        'table': 'Table',
        'timestamp': 'Timestamp',
        'user': 'User'
    }

    def __init__(self, connection_id=None, db=None, lock_time=None, query_time=None, rows_examined=None, rows_sent=None, sql_template=None, sql_text=None, source_ip=None, sql_fingerprint=None, sql_method=None, table=None, timestamp=None, user=None, _configuration=None):  # noqa: E501
        """SlowLogForDescribeSlowLogsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._connection_id = None
        self._db = None
        self._lock_time = None
        self._query_time = None
        self._rows_examined = None
        self._rows_sent = None
        self._sql_template = None
        self._sql_text = None
        self._source_ip = None
        self._sql_fingerprint = None
        self._sql_method = None
        self._table = None
        self._timestamp = None
        self._user = None
        self.discriminator = None

        if connection_id is not None:
            self.connection_id = connection_id
        if db is not None:
            self.db = db
        if lock_time is not None:
            self.lock_time = lock_time
        if query_time is not None:
            self.query_time = query_time
        if rows_examined is not None:
            self.rows_examined = rows_examined
        if rows_sent is not None:
            self.rows_sent = rows_sent
        if sql_template is not None:
            self.sql_template = sql_template
        if sql_text is not None:
            self.sql_text = sql_text
        if source_ip is not None:
            self.source_ip = source_ip
        if sql_fingerprint is not None:
            self.sql_fingerprint = sql_fingerprint
        if sql_method is not None:
            self.sql_method = sql_method
        if table is not None:
            self.table = table
        if timestamp is not None:
            self.timestamp = timestamp
        if user is not None:
            self.user = user

    @property
    def connection_id(self):
        """Gets the connection_id of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The connection_id of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._connection_id

    @connection_id.setter
    def connection_id(self, connection_id):
        """Sets the connection_id of this SlowLogForDescribeSlowLogsOutput.


        :param connection_id: The connection_id of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._connection_id = connection_id

    @property
    def db(self):
        """Gets the db of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The db of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db

    @db.setter
    def db(self, db):
        """Sets the db of this SlowLogForDescribeSlowLogsOutput.


        :param db: The db of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._db = db

    @property
    def lock_time(self):
        """Gets the lock_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The lock_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: float
        """
        return self._lock_time

    @lock_time.setter
    def lock_time(self, lock_time):
        """Sets the lock_time of this SlowLogForDescribeSlowLogsOutput.


        :param lock_time: The lock_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: float
        """

        self._lock_time = lock_time

    @property
    def query_time(self):
        """Gets the query_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The query_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: float
        """
        return self._query_time

    @query_time.setter
    def query_time(self, query_time):
        """Sets the query_time of this SlowLogForDescribeSlowLogsOutput.


        :param query_time: The query_time of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: float
        """

        self._query_time = query_time

    @property
    def rows_examined(self):
        """Gets the rows_examined of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The rows_examined of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rows_examined

    @rows_examined.setter
    def rows_examined(self, rows_examined):
        """Sets the rows_examined of this SlowLogForDescribeSlowLogsOutput.


        :param rows_examined: The rows_examined of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._rows_examined = rows_examined

    @property
    def rows_sent(self):
        """Gets the rows_sent of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The rows_sent of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rows_sent

    @rows_sent.setter
    def rows_sent(self, rows_sent):
        """Sets the rows_sent of this SlowLogForDescribeSlowLogsOutput.


        :param rows_sent: The rows_sent of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._rows_sent = rows_sent

    @property
    def sql_template(self):
        """Gets the sql_template of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The sql_template of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_template

    @sql_template.setter
    def sql_template(self, sql_template):
        """Sets the sql_template of this SlowLogForDescribeSlowLogsOutput.


        :param sql_template: The sql_template of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._sql_template = sql_template

    @property
    def sql_text(self):
        """Gets the sql_text of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The sql_text of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_text

    @sql_text.setter
    def sql_text(self, sql_text):
        """Sets the sql_text of this SlowLogForDescribeSlowLogsOutput.


        :param sql_text: The sql_text of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._sql_text = sql_text

    @property
    def source_ip(self):
        """Gets the source_ip of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The source_ip of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_ip

    @source_ip.setter
    def source_ip(self, source_ip):
        """Sets the source_ip of this SlowLogForDescribeSlowLogsOutput.


        :param source_ip: The source_ip of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._source_ip = source_ip

    @property
    def sql_fingerprint(self):
        """Gets the sql_fingerprint of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The sql_fingerprint of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_fingerprint

    @sql_fingerprint.setter
    def sql_fingerprint(self, sql_fingerprint):
        """Sets the sql_fingerprint of this SlowLogForDescribeSlowLogsOutput.


        :param sql_fingerprint: The sql_fingerprint of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._sql_fingerprint = sql_fingerprint

    @property
    def sql_method(self):
        """Gets the sql_method of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The sql_method of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_method

    @sql_method.setter
    def sql_method(self, sql_method):
        """Sets the sql_method of this SlowLogForDescribeSlowLogsOutput.


        :param sql_method: The sql_method of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._sql_method = sql_method

    @property
    def table(self):
        """Gets the table of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The table of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._table

    @table.setter
    def table(self, table):
        """Sets the table of this SlowLogForDescribeSlowLogsOutput.


        :param table: The table of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._table = table

    @property
    def timestamp(self):
        """Gets the timestamp of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The timestamp of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this SlowLogForDescribeSlowLogsOutput.


        :param timestamp: The timestamp of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._timestamp = timestamp

    @property
    def user(self):
        """Gets the user of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501


        :return: The user of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this SlowLogForDescribeSlowLogsOutput.


        :param user: The user of this SlowLogForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SlowLogForDescribeSlowLogsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SlowLogForDescribeSlowLogsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SlowLogForDescribeSlowLogsOutput):
            return True

        return self.to_dict() != other.to_dict()
