# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForGetRecordListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_id': 'str',
        'end_time': 'str',
        'end_time_stamp': 'int',
        'file_size': 'str',
        'name': 'str',
        'secrecy': 'str',
        'start_time': 'str',
        'start_time_stamp': 'int',
        'type': 'str'
    }

    attribute_map = {
        'channel_id': 'ChannelID',
        'end_time': 'EndTime',
        'end_time_stamp': 'EndTimeStamp',
        'file_size': 'FileSize',
        'name': 'Name',
        'secrecy': 'Secrecy',
        'start_time': 'StartTime',
        'start_time_stamp': 'StartTimeStamp',
        'type': 'Type'
    }

    def __init__(self, channel_id=None, end_time=None, end_time_stamp=None, file_size=None, name=None, secrecy=None, start_time=None, start_time_stamp=None, type=None, _configuration=None):  # noqa: E501
        """ItemForGetRecordListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_id = None
        self._end_time = None
        self._end_time_stamp = None
        self._file_size = None
        self._name = None
        self._secrecy = None
        self._start_time = None
        self._start_time_stamp = None
        self._type = None
        self.discriminator = None

        if channel_id is not None:
            self.channel_id = channel_id
        if end_time is not None:
            self.end_time = end_time
        if end_time_stamp is not None:
            self.end_time_stamp = end_time_stamp
        if file_size is not None:
            self.file_size = file_size
        if name is not None:
            self.name = name
        if secrecy is not None:
            self.secrecy = secrecy
        if start_time is not None:
            self.start_time = start_time
        if start_time_stamp is not None:
            self.start_time_stamp = start_time_stamp
        if type is not None:
            self.type = type

    @property
    def channel_id(self):
        """Gets the channel_id of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The channel_id of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_id

    @channel_id.setter
    def channel_id(self, channel_id):
        """Sets the channel_id of this ItemForGetRecordListOutput.


        :param channel_id: The channel_id of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._channel_id = channel_id

    @property
    def end_time(self):
        """Gets the end_time of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The end_time of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ItemForGetRecordListOutput.


        :param end_time: The end_time of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def end_time_stamp(self):
        """Gets the end_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The end_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time_stamp

    @end_time_stamp.setter
    def end_time_stamp(self, end_time_stamp):
        """Sets the end_time_stamp of this ItemForGetRecordListOutput.


        :param end_time_stamp: The end_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501
        :type: int
        """

        self._end_time_stamp = end_time_stamp

    @property
    def file_size(self):
        """Gets the file_size of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The file_size of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_size

    @file_size.setter
    def file_size(self, file_size):
        """Sets the file_size of this ItemForGetRecordListOutput.


        :param file_size: The file_size of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._file_size = file_size

    @property
    def name(self):
        """Gets the name of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The name of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForGetRecordListOutput.


        :param name: The name of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def secrecy(self):
        """Gets the secrecy of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The secrecy of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._secrecy

    @secrecy.setter
    def secrecy(self, secrecy):
        """Sets the secrecy of this ItemForGetRecordListOutput.


        :param secrecy: The secrecy of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._secrecy = secrecy

    @property
    def start_time(self):
        """Gets the start_time of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The start_time of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForGetRecordListOutput.


        :param start_time: The start_time of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def start_time_stamp(self):
        """Gets the start_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The start_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time_stamp

    @start_time_stamp.setter
    def start_time_stamp(self, start_time_stamp):
        """Sets the start_time_stamp of this ItemForGetRecordListOutput.


        :param start_time_stamp: The start_time_stamp of this ItemForGetRecordListOutput.  # noqa: E501
        :type: int
        """

        self._start_time_stamp = start_time_stamp

    @property
    def type(self):
        """Gets the type of this ItemForGetRecordListOutput.  # noqa: E501


        :return: The type of this ItemForGetRecordListOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForGetRecordListOutput.


        :param type: The type of this ItemForGetRecordListOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForGetRecordListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForGetRecordListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForGetRecordListOutput):
            return True

        return self.to_dict() != other.to_dict()
