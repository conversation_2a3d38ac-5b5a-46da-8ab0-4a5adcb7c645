# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigForGetVirusTaskInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu_i_dle': 'str',
        'file_path': 'str',
        'timeout': 'str'
    }

    attribute_map = {
        'cpu_i_dle': 'CpuIDle',
        'file_path': 'FilePath',
        'timeout': 'Timeout'
    }

    def __init__(self, cpu_i_dle=None, file_path=None, timeout=None, _configuration=None):  # noqa: E501
        """ConfigForGetVirusTaskInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu_i_dle = None
        self._file_path = None
        self._timeout = None
        self.discriminator = None

        if cpu_i_dle is not None:
            self.cpu_i_dle = cpu_i_dle
        if file_path is not None:
            self.file_path = file_path
        if timeout is not None:
            self.timeout = timeout

    @property
    def cpu_i_dle(self):
        """Gets the cpu_i_dle of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501


        :return: The cpu_i_dle of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cpu_i_dle

    @cpu_i_dle.setter
    def cpu_i_dle(self, cpu_i_dle):
        """Sets the cpu_i_dle of this ConfigForGetVirusTaskInfoOutput.


        :param cpu_i_dle: The cpu_i_dle of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :type: str
        """

        self._cpu_i_dle = cpu_i_dle

    @property
    def file_path(self):
        """Gets the file_path of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501


        :return: The file_path of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this ConfigForGetVirusTaskInfoOutput.


        :param file_path: The file_path of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def timeout(self):
        """Gets the timeout of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501


        :return: The timeout of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this ConfigForGetVirusTaskInfoOutput.


        :param timeout: The timeout of this ConfigForGetVirusTaskInfoOutput.  # noqa: E501
        :type: str
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigForGetVirusTaskInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigForGetVirusTaskInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigForGetVirusTaskInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
