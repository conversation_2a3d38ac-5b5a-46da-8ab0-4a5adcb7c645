# coding: utf-8

"""
    translate20250301

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TranslationListForTranslateTextOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detected_source_language': 'str',
        'translation': 'str'
    }

    attribute_map = {
        'detected_source_language': 'DetectedSourceLanguage',
        'translation': 'Translation'
    }

    def __init__(self, detected_source_language=None, translation=None, _configuration=None):  # noqa: E501
        """TranslationListForTranslateTextOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detected_source_language = None
        self._translation = None
        self.discriminator = None

        if detected_source_language is not None:
            self.detected_source_language = detected_source_language
        if translation is not None:
            self.translation = translation

    @property
    def detected_source_language(self):
        """Gets the detected_source_language of this TranslationListForTranslateTextOutput.  # noqa: E501


        :return: The detected_source_language of this TranslationListForTranslateTextOutput.  # noqa: E501
        :rtype: str
        """
        return self._detected_source_language

    @detected_source_language.setter
    def detected_source_language(self, detected_source_language):
        """Sets the detected_source_language of this TranslationListForTranslateTextOutput.


        :param detected_source_language: The detected_source_language of this TranslationListForTranslateTextOutput.  # noqa: E501
        :type: str
        """

        self._detected_source_language = detected_source_language

    @property
    def translation(self):
        """Gets the translation of this TranslationListForTranslateTextOutput.  # noqa: E501


        :return: The translation of this TranslationListForTranslateTextOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation

    @translation.setter
    def translation(self, translation):
        """Sets the translation of this TranslationListForTranslateTextOutput.


        :param translation: The translation of this TranslationListForTranslateTextOutput.  # noqa: E501
        :type: str
        """

        self._translation = translation

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TranslationListForTranslateTextOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TranslationListForTranslateTextOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TranslationListForTranslateTextOutput):
            return True

        return self.to_dict() != other.to_dict()
