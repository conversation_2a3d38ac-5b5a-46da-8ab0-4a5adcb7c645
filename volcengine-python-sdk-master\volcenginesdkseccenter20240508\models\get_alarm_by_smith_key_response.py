# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAlarmBySmithKeyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_data_type2439': 'AlarmDataType2439ForGetAlarmBySmithKeyOutput',
        'base_alarm_info': 'BaseAlarmInfoForGetAlarmBySmithKeyOutput',
        'base_info': 'BaseInfoForGetAlarmBySmithKeyOutput',
        'comm_alarm_info': 'CommAlarmInfoForGetAlarmBySmithKeyOutput',
        'container_info': 'ContainerInfoForGetAlarmBySmithKeyOutput',
        'data_type': 'str',
        'data_type_str': 'str',
        'endpoint': 'str',
        'hub_trace_data': 'HubTraceDataForGetAlarmBySmithKeyOutput',
        'plus_alarm_info101': 'PlusAlarmInfo101ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info3004': 'PlusAlarmInfo3004ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info356': 'PlusAlarmInfo356ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info4000': 'PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info42': 'PlusAlarmInfo42ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info49': 'PlusAlarmInfo49ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info59': 'PlusAlarmInfo59ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info6001': 'PlusAlarmInfo6001ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info6002': 'PlusAlarmInfo6002ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info6003': 'PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info601': 'PlusAlarmInfo601ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info602': 'PlusAlarmInfo602ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info603': 'PlusAlarmInfo603ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info604': 'PlusAlarmInfo604ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info700': 'PlusAlarmInfo700ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info701': 'PlusAlarmInfo701ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info702': 'PlusAlarmInfo702ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info703': 'PlusAlarmInfo703ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info82': 'PlusAlarmInfo82ForGetAlarmBySmithKeyOutput',
        'plus_alarm_info86': 'PlusAlarmInfo86ForGetAlarmBySmithKeyOutput',
        'plus_kill_chain': 'PlusKillChainForGetAlarmBySmithKeyOutput'
    }

    attribute_map = {
        'alarm_data_type2439': 'AlarmDataType2439',
        'base_alarm_info': 'BaseAlarmInfo',
        'base_info': 'BaseInfo',
        'comm_alarm_info': 'CommAlarmInfo',
        'container_info': 'ContainerInfo',
        'data_type': 'DataType',
        'data_type_str': 'DataTypeStr',
        'endpoint': 'Endpoint',
        'hub_trace_data': 'HubTraceData',
        'plus_alarm_info101': 'PlusAlarmInfo101',
        'plus_alarm_info3004': 'PlusAlarmInfo3004',
        'plus_alarm_info356': 'PlusAlarmInfo356',
        'plus_alarm_info4000': 'PlusAlarmInfo4000',
        'plus_alarm_info42': 'PlusAlarmInfo42',
        'plus_alarm_info49': 'PlusAlarmInfo49',
        'plus_alarm_info59': 'PlusAlarmInfo59',
        'plus_alarm_info6001': 'PlusAlarmInfo6001',
        'plus_alarm_info6002': 'PlusAlarmInfo6002',
        'plus_alarm_info6003': 'PlusAlarmInfo6003',
        'plus_alarm_info601': 'PlusAlarmInfo601',
        'plus_alarm_info602': 'PlusAlarmInfo602',
        'plus_alarm_info603': 'PlusAlarmInfo603',
        'plus_alarm_info604': 'PlusAlarmInfo604',
        'plus_alarm_info700': 'PlusAlarmInfo700',
        'plus_alarm_info701': 'PlusAlarmInfo701',
        'plus_alarm_info702': 'PlusAlarmInfo702',
        'plus_alarm_info703': 'PlusAlarmInfo703',
        'plus_alarm_info82': 'PlusAlarmInfo82',
        'plus_alarm_info86': 'PlusAlarmInfo86',
        'plus_kill_chain': 'PlusKillChain'
    }

    def __init__(self, alarm_data_type2439=None, base_alarm_info=None, base_info=None, comm_alarm_info=None, container_info=None, data_type=None, data_type_str=None, endpoint=None, hub_trace_data=None, plus_alarm_info101=None, plus_alarm_info3004=None, plus_alarm_info356=None, plus_alarm_info4000=None, plus_alarm_info42=None, plus_alarm_info49=None, plus_alarm_info59=None, plus_alarm_info6001=None, plus_alarm_info6002=None, plus_alarm_info6003=None, plus_alarm_info601=None, plus_alarm_info602=None, plus_alarm_info603=None, plus_alarm_info604=None, plus_alarm_info700=None, plus_alarm_info701=None, plus_alarm_info702=None, plus_alarm_info703=None, plus_alarm_info82=None, plus_alarm_info86=None, plus_kill_chain=None, _configuration=None):  # noqa: E501
        """GetAlarmBySmithKeyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_data_type2439 = None
        self._base_alarm_info = None
        self._base_info = None
        self._comm_alarm_info = None
        self._container_info = None
        self._data_type = None
        self._data_type_str = None
        self._endpoint = None
        self._hub_trace_data = None
        self._plus_alarm_info101 = None
        self._plus_alarm_info3004 = None
        self._plus_alarm_info356 = None
        self._plus_alarm_info4000 = None
        self._plus_alarm_info42 = None
        self._plus_alarm_info49 = None
        self._plus_alarm_info59 = None
        self._plus_alarm_info6001 = None
        self._plus_alarm_info6002 = None
        self._plus_alarm_info6003 = None
        self._plus_alarm_info601 = None
        self._plus_alarm_info602 = None
        self._plus_alarm_info603 = None
        self._plus_alarm_info604 = None
        self._plus_alarm_info700 = None
        self._plus_alarm_info701 = None
        self._plus_alarm_info702 = None
        self._plus_alarm_info703 = None
        self._plus_alarm_info82 = None
        self._plus_alarm_info86 = None
        self._plus_kill_chain = None
        self.discriminator = None

        if alarm_data_type2439 is not None:
            self.alarm_data_type2439 = alarm_data_type2439
        if base_alarm_info is not None:
            self.base_alarm_info = base_alarm_info
        if base_info is not None:
            self.base_info = base_info
        if comm_alarm_info is not None:
            self.comm_alarm_info = comm_alarm_info
        if container_info is not None:
            self.container_info = container_info
        if data_type is not None:
            self.data_type = data_type
        if data_type_str is not None:
            self.data_type_str = data_type_str
        if endpoint is not None:
            self.endpoint = endpoint
        if hub_trace_data is not None:
            self.hub_trace_data = hub_trace_data
        if plus_alarm_info101 is not None:
            self.plus_alarm_info101 = plus_alarm_info101
        if plus_alarm_info3004 is not None:
            self.plus_alarm_info3004 = plus_alarm_info3004
        if plus_alarm_info356 is not None:
            self.plus_alarm_info356 = plus_alarm_info356
        if plus_alarm_info4000 is not None:
            self.plus_alarm_info4000 = plus_alarm_info4000
        if plus_alarm_info42 is not None:
            self.plus_alarm_info42 = plus_alarm_info42
        if plus_alarm_info49 is not None:
            self.plus_alarm_info49 = plus_alarm_info49
        if plus_alarm_info59 is not None:
            self.plus_alarm_info59 = plus_alarm_info59
        if plus_alarm_info6001 is not None:
            self.plus_alarm_info6001 = plus_alarm_info6001
        if plus_alarm_info6002 is not None:
            self.plus_alarm_info6002 = plus_alarm_info6002
        if plus_alarm_info6003 is not None:
            self.plus_alarm_info6003 = plus_alarm_info6003
        if plus_alarm_info601 is not None:
            self.plus_alarm_info601 = plus_alarm_info601
        if plus_alarm_info602 is not None:
            self.plus_alarm_info602 = plus_alarm_info602
        if plus_alarm_info603 is not None:
            self.plus_alarm_info603 = plus_alarm_info603
        if plus_alarm_info604 is not None:
            self.plus_alarm_info604 = plus_alarm_info604
        if plus_alarm_info700 is not None:
            self.plus_alarm_info700 = plus_alarm_info700
        if plus_alarm_info701 is not None:
            self.plus_alarm_info701 = plus_alarm_info701
        if plus_alarm_info702 is not None:
            self.plus_alarm_info702 = plus_alarm_info702
        if plus_alarm_info703 is not None:
            self.plus_alarm_info703 = plus_alarm_info703
        if plus_alarm_info82 is not None:
            self.plus_alarm_info82 = plus_alarm_info82
        if plus_alarm_info86 is not None:
            self.plus_alarm_info86 = plus_alarm_info86
        if plus_kill_chain is not None:
            self.plus_kill_chain = plus_kill_chain

    @property
    def alarm_data_type2439(self):
        """Gets the alarm_data_type2439 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The alarm_data_type2439 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: AlarmDataType2439ForGetAlarmBySmithKeyOutput
        """
        return self._alarm_data_type2439

    @alarm_data_type2439.setter
    def alarm_data_type2439(self, alarm_data_type2439):
        """Sets the alarm_data_type2439 of this GetAlarmBySmithKeyResponse.


        :param alarm_data_type2439: The alarm_data_type2439 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: AlarmDataType2439ForGetAlarmBySmithKeyOutput
        """

        self._alarm_data_type2439 = alarm_data_type2439

    @property
    def base_alarm_info(self):
        """Gets the base_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The base_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: BaseAlarmInfoForGetAlarmBySmithKeyOutput
        """
        return self._base_alarm_info

    @base_alarm_info.setter
    def base_alarm_info(self, base_alarm_info):
        """Sets the base_alarm_info of this GetAlarmBySmithKeyResponse.


        :param base_alarm_info: The base_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: BaseAlarmInfoForGetAlarmBySmithKeyOutput
        """

        self._base_alarm_info = base_alarm_info

    @property
    def base_info(self):
        """Gets the base_info of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The base_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: BaseInfoForGetAlarmBySmithKeyOutput
        """
        return self._base_info

    @base_info.setter
    def base_info(self, base_info):
        """Sets the base_info of this GetAlarmBySmithKeyResponse.


        :param base_info: The base_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: BaseInfoForGetAlarmBySmithKeyOutput
        """

        self._base_info = base_info

    @property
    def comm_alarm_info(self):
        """Gets the comm_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The comm_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: CommAlarmInfoForGetAlarmBySmithKeyOutput
        """
        return self._comm_alarm_info

    @comm_alarm_info.setter
    def comm_alarm_info(self, comm_alarm_info):
        """Sets the comm_alarm_info of this GetAlarmBySmithKeyResponse.


        :param comm_alarm_info: The comm_alarm_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: CommAlarmInfoForGetAlarmBySmithKeyOutput
        """

        self._comm_alarm_info = comm_alarm_info

    @property
    def container_info(self):
        """Gets the container_info of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The container_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: ContainerInfoForGetAlarmBySmithKeyOutput
        """
        return self._container_info

    @container_info.setter
    def container_info(self, container_info):
        """Sets the container_info of this GetAlarmBySmithKeyResponse.


        :param container_info: The container_info of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: ContainerInfoForGetAlarmBySmithKeyOutput
        """

        self._container_info = container_info

    @property
    def data_type(self):
        """Gets the data_type of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The data_type of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this GetAlarmBySmithKeyResponse.


        :param data_type: The data_type of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def data_type_str(self):
        """Gets the data_type_str of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The data_type_str of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_type_str

    @data_type_str.setter
    def data_type_str(self, data_type_str):
        """Sets the data_type_str of this GetAlarmBySmithKeyResponse.


        :param data_type_str: The data_type_str of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: str
        """

        self._data_type_str = data_type_str

    @property
    def endpoint(self):
        """Gets the endpoint of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The endpoint of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this GetAlarmBySmithKeyResponse.


        :param endpoint: The endpoint of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def hub_trace_data(self):
        """Gets the hub_trace_data of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The hub_trace_data of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: HubTraceDataForGetAlarmBySmithKeyOutput
        """
        return self._hub_trace_data

    @hub_trace_data.setter
    def hub_trace_data(self, hub_trace_data):
        """Sets the hub_trace_data of this GetAlarmBySmithKeyResponse.


        :param hub_trace_data: The hub_trace_data of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: HubTraceDataForGetAlarmBySmithKeyOutput
        """

        self._hub_trace_data = hub_trace_data

    @property
    def plus_alarm_info101(self):
        """Gets the plus_alarm_info101 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info101 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo101ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info101

    @plus_alarm_info101.setter
    def plus_alarm_info101(self, plus_alarm_info101):
        """Sets the plus_alarm_info101 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info101: The plus_alarm_info101 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo101ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info101 = plus_alarm_info101

    @property
    def plus_alarm_info3004(self):
        """Gets the plus_alarm_info3004 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info3004 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo3004ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info3004

    @plus_alarm_info3004.setter
    def plus_alarm_info3004(self, plus_alarm_info3004):
        """Sets the plus_alarm_info3004 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info3004: The plus_alarm_info3004 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo3004ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info3004 = plus_alarm_info3004

    @property
    def plus_alarm_info356(self):
        """Gets the plus_alarm_info356 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info356 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo356ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info356

    @plus_alarm_info356.setter
    def plus_alarm_info356(self, plus_alarm_info356):
        """Sets the plus_alarm_info356 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info356: The plus_alarm_info356 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo356ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info356 = plus_alarm_info356

    @property
    def plus_alarm_info4000(self):
        """Gets the plus_alarm_info4000 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info4000 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info4000

    @plus_alarm_info4000.setter
    def plus_alarm_info4000(self, plus_alarm_info4000):
        """Sets the plus_alarm_info4000 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info4000: The plus_alarm_info4000 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info4000 = plus_alarm_info4000

    @property
    def plus_alarm_info42(self):
        """Gets the plus_alarm_info42 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info42 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo42ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info42

    @plus_alarm_info42.setter
    def plus_alarm_info42(self, plus_alarm_info42):
        """Sets the plus_alarm_info42 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info42: The plus_alarm_info42 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo42ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info42 = plus_alarm_info42

    @property
    def plus_alarm_info49(self):
        """Gets the plus_alarm_info49 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info49 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo49ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info49

    @plus_alarm_info49.setter
    def plus_alarm_info49(self, plus_alarm_info49):
        """Sets the plus_alarm_info49 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info49: The plus_alarm_info49 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo49ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info49 = plus_alarm_info49

    @property
    def plus_alarm_info59(self):
        """Gets the plus_alarm_info59 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info59 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo59ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info59

    @plus_alarm_info59.setter
    def plus_alarm_info59(self, plus_alarm_info59):
        """Sets the plus_alarm_info59 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info59: The plus_alarm_info59 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo59ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info59 = plus_alarm_info59

    @property
    def plus_alarm_info6001(self):
        """Gets the plus_alarm_info6001 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info6001 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo6001ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info6001

    @plus_alarm_info6001.setter
    def plus_alarm_info6001(self, plus_alarm_info6001):
        """Sets the plus_alarm_info6001 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info6001: The plus_alarm_info6001 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo6001ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info6001 = plus_alarm_info6001

    @property
    def plus_alarm_info6002(self):
        """Gets the plus_alarm_info6002 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info6002 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo6002ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info6002

    @plus_alarm_info6002.setter
    def plus_alarm_info6002(self, plus_alarm_info6002):
        """Sets the plus_alarm_info6002 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info6002: The plus_alarm_info6002 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo6002ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info6002 = plus_alarm_info6002

    @property
    def plus_alarm_info6003(self):
        """Gets the plus_alarm_info6003 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info6003 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info6003

    @plus_alarm_info6003.setter
    def plus_alarm_info6003(self, plus_alarm_info6003):
        """Sets the plus_alarm_info6003 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info6003: The plus_alarm_info6003 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info6003 = plus_alarm_info6003

    @property
    def plus_alarm_info601(self):
        """Gets the plus_alarm_info601 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info601 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo601ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info601

    @plus_alarm_info601.setter
    def plus_alarm_info601(self, plus_alarm_info601):
        """Sets the plus_alarm_info601 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info601: The plus_alarm_info601 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo601ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info601 = plus_alarm_info601

    @property
    def plus_alarm_info602(self):
        """Gets the plus_alarm_info602 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info602 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo602ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info602

    @plus_alarm_info602.setter
    def plus_alarm_info602(self, plus_alarm_info602):
        """Sets the plus_alarm_info602 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info602: The plus_alarm_info602 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo602ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info602 = plus_alarm_info602

    @property
    def plus_alarm_info603(self):
        """Gets the plus_alarm_info603 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info603 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo603ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info603

    @plus_alarm_info603.setter
    def plus_alarm_info603(self, plus_alarm_info603):
        """Sets the plus_alarm_info603 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info603: The plus_alarm_info603 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo603ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info603 = plus_alarm_info603

    @property
    def plus_alarm_info604(self):
        """Gets the plus_alarm_info604 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info604 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo604ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info604

    @plus_alarm_info604.setter
    def plus_alarm_info604(self, plus_alarm_info604):
        """Sets the plus_alarm_info604 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info604: The plus_alarm_info604 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo604ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info604 = plus_alarm_info604

    @property
    def plus_alarm_info700(self):
        """Gets the plus_alarm_info700 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info700 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo700ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info700

    @plus_alarm_info700.setter
    def plus_alarm_info700(self, plus_alarm_info700):
        """Sets the plus_alarm_info700 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info700: The plus_alarm_info700 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo700ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info700 = plus_alarm_info700

    @property
    def plus_alarm_info701(self):
        """Gets the plus_alarm_info701 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info701 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo701ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info701

    @plus_alarm_info701.setter
    def plus_alarm_info701(self, plus_alarm_info701):
        """Sets the plus_alarm_info701 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info701: The plus_alarm_info701 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo701ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info701 = plus_alarm_info701

    @property
    def plus_alarm_info702(self):
        """Gets the plus_alarm_info702 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info702 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo702ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info702

    @plus_alarm_info702.setter
    def plus_alarm_info702(self, plus_alarm_info702):
        """Sets the plus_alarm_info702 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info702: The plus_alarm_info702 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo702ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info702 = plus_alarm_info702

    @property
    def plus_alarm_info703(self):
        """Gets the plus_alarm_info703 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info703 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo703ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info703

    @plus_alarm_info703.setter
    def plus_alarm_info703(self, plus_alarm_info703):
        """Sets the plus_alarm_info703 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info703: The plus_alarm_info703 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo703ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info703 = plus_alarm_info703

    @property
    def plus_alarm_info82(self):
        """Gets the plus_alarm_info82 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info82 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo82ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info82

    @plus_alarm_info82.setter
    def plus_alarm_info82(self, plus_alarm_info82):
        """Sets the plus_alarm_info82 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info82: The plus_alarm_info82 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo82ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info82 = plus_alarm_info82

    @property
    def plus_alarm_info86(self):
        """Gets the plus_alarm_info86 of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_alarm_info86 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusAlarmInfo86ForGetAlarmBySmithKeyOutput
        """
        return self._plus_alarm_info86

    @plus_alarm_info86.setter
    def plus_alarm_info86(self, plus_alarm_info86):
        """Sets the plus_alarm_info86 of this GetAlarmBySmithKeyResponse.


        :param plus_alarm_info86: The plus_alarm_info86 of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusAlarmInfo86ForGetAlarmBySmithKeyOutput
        """

        self._plus_alarm_info86 = plus_alarm_info86

    @property
    def plus_kill_chain(self):
        """Gets the plus_kill_chain of this GetAlarmBySmithKeyResponse.  # noqa: E501


        :return: The plus_kill_chain of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :rtype: PlusKillChainForGetAlarmBySmithKeyOutput
        """
        return self._plus_kill_chain

    @plus_kill_chain.setter
    def plus_kill_chain(self, plus_kill_chain):
        """Sets the plus_kill_chain of this GetAlarmBySmithKeyResponse.


        :param plus_kill_chain: The plus_kill_chain of this GetAlarmBySmithKeyResponse.  # noqa: E501
        :type: PlusKillChainForGetAlarmBySmithKeyOutput
        """

        self._plus_kill_chain = plus_kill_chain

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAlarmBySmithKeyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAlarmBySmithKeyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAlarmBySmithKeyResponse):
            return True

        return self.to_dict() != other.to_dict()
