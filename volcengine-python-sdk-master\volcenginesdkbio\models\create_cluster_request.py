# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateClusterRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'shared_config': 'SharedConfigForCreateClusterInput'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'shared_config': 'SharedConfig'
    }

    def __init__(self, description=None, name=None, shared_config=None, _configuration=None):  # noqa: E501
        """CreateClusterRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._shared_config = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.name = name
        if shared_config is not None:
            self.shared_config = shared_config

    @property
    def description(self):
        """Gets the description of this CreateClusterRequest.  # noqa: E501


        :return: The description of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateClusterRequest.


        :param description: The description of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 300):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `300`")  # noqa: E501

        self._description = description

    @property
    def name(self):
        """Gets the name of this CreateClusterRequest.  # noqa: E501


        :return: The name of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateClusterRequest.


        :param name: The name of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 64):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `64`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 2):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `2`")  # noqa: E501

        self._name = name

    @property
    def shared_config(self):
        """Gets the shared_config of this CreateClusterRequest.  # noqa: E501


        :return: The shared_config of this CreateClusterRequest.  # noqa: E501
        :rtype: SharedConfigForCreateClusterInput
        """
        return self._shared_config

    @shared_config.setter
    def shared_config(self, shared_config):
        """Sets the shared_config of this CreateClusterRequest.


        :param shared_config: The shared_config of this CreateClusterRequest.  # noqa: E501
        :type: SharedConfigForCreateClusterInput
        """

        self._shared_config = shared_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateClusterRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateClusterRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateClusterRequest):
            return True

        return self.to_dict() != other.to_dict()
