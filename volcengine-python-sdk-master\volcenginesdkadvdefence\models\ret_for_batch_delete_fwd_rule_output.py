# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RetForBatchDeleteFwdRuleOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'err_message': 'str',
        'id': 'int',
        'ret': 'int'
    }

    attribute_map = {
        'err_message': 'ErrMessage',
        'id': 'Id',
        'ret': 'Ret'
    }

    def __init__(self, err_message=None, id=None, ret=None, _configuration=None):  # noqa: E501
        """RetForBatchDeleteFwdRuleOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._err_message = None
        self._id = None
        self._ret = None
        self.discriminator = None

        if err_message is not None:
            self.err_message = err_message
        if id is not None:
            self.id = id
        if ret is not None:
            self.ret = ret

    @property
    def err_message(self):
        """Gets the err_message of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501


        :return: The err_message of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_message

    @err_message.setter
    def err_message(self, err_message):
        """Sets the err_message of this RetForBatchDeleteFwdRuleOutput.


        :param err_message: The err_message of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :type: str
        """

        self._err_message = err_message

    @property
    def id(self):
        """Gets the id of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501


        :return: The id of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this RetForBatchDeleteFwdRuleOutput.


        :param id: The id of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def ret(self):
        """Gets the ret of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501


        :return: The ret of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :rtype: int
        """
        return self._ret

    @ret.setter
    def ret(self, ret):
        """Sets the ret of this RetForBatchDeleteFwdRuleOutput.


        :param ret: The ret of this RetForBatchDeleteFwdRuleOutput.  # noqa: E501
        :type: int
        """

        self._ret = ret

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RetForBatchDeleteFwdRuleOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RetForBatchDeleteFwdRuleOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RetForBatchDeleteFwdRuleOutput):
            return True

        return self.to_dict() != other.to_dict()
