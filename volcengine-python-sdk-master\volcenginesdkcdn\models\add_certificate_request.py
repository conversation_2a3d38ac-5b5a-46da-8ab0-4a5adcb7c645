# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddCertificateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_type': 'str',
        'certificate': 'str',
        'desc': 'str',
        'encry_type': 'str',
        'encryption_cert': 'str',
        'encryption_key': 'str',
        'private_key': 'str',
        'repeatable': 'bool',
        'source': 'str'
    }

    attribute_map = {
        'cert_type': 'CertType',
        'certificate': 'Certificate',
        'desc': 'Desc',
        'encry_type': 'EncryType',
        'encryption_cert': 'EncryptionCert',
        'encryption_key': 'EncryptionKey',
        'private_key': 'PrivateKey',
        'repeatable': 'Repeatable',
        'source': 'Source'
    }

    def __init__(self, cert_type=None, certificate=None, desc=None, encry_type=None, encryption_cert=None, encryption_key=None, private_key=None, repeatable=None, source=None, _configuration=None):  # noqa: E501
        """AddCertificateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_type = None
        self._certificate = None
        self._desc = None
        self._encry_type = None
        self._encryption_cert = None
        self._encryption_key = None
        self._private_key = None
        self._repeatable = None
        self._source = None
        self.discriminator = None

        if cert_type is not None:
            self.cert_type = cert_type
        self.certificate = certificate
        if desc is not None:
            self.desc = desc
        if encry_type is not None:
            self.encry_type = encry_type
        if encryption_cert is not None:
            self.encryption_cert = encryption_cert
        if encryption_key is not None:
            self.encryption_key = encryption_key
        self.private_key = private_key
        if repeatable is not None:
            self.repeatable = repeatable
        if source is not None:
            self.source = source

    @property
    def cert_type(self):
        """Gets the cert_type of this AddCertificateRequest.  # noqa: E501


        :return: The cert_type of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_type

    @cert_type.setter
    def cert_type(self, cert_type):
        """Sets the cert_type of this AddCertificateRequest.


        :param cert_type: The cert_type of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._cert_type = cert_type

    @property
    def certificate(self):
        """Gets the certificate of this AddCertificateRequest.  # noqa: E501


        :return: The certificate of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate

    @certificate.setter
    def certificate(self, certificate):
        """Sets the certificate of this AddCertificateRequest.


        :param certificate: The certificate of this AddCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and certificate is None:
            raise ValueError("Invalid value for `certificate`, must not be `None`")  # noqa: E501

        self._certificate = certificate

    @property
    def desc(self):
        """Gets the desc of this AddCertificateRequest.  # noqa: E501


        :return: The desc of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this AddCertificateRequest.


        :param desc: The desc of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def encry_type(self):
        """Gets the encry_type of this AddCertificateRequest.  # noqa: E501


        :return: The encry_type of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._encry_type

    @encry_type.setter
    def encry_type(self, encry_type):
        """Sets the encry_type of this AddCertificateRequest.


        :param encry_type: The encry_type of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._encry_type = encry_type

    @property
    def encryption_cert(self):
        """Gets the encryption_cert of this AddCertificateRequest.  # noqa: E501


        :return: The encryption_cert of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._encryption_cert

    @encryption_cert.setter
    def encryption_cert(self, encryption_cert):
        """Sets the encryption_cert of this AddCertificateRequest.


        :param encryption_cert: The encryption_cert of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._encryption_cert = encryption_cert

    @property
    def encryption_key(self):
        """Gets the encryption_key of this AddCertificateRequest.  # noqa: E501


        :return: The encryption_key of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._encryption_key

    @encryption_key.setter
    def encryption_key(self, encryption_key):
        """Sets the encryption_key of this AddCertificateRequest.


        :param encryption_key: The encryption_key of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._encryption_key = encryption_key

    @property
    def private_key(self):
        """Gets the private_key of this AddCertificateRequest.  # noqa: E501


        :return: The private_key of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this AddCertificateRequest.


        :param private_key: The private_key of this AddCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and private_key is None:
            raise ValueError("Invalid value for `private_key`, must not be `None`")  # noqa: E501

        self._private_key = private_key

    @property
    def repeatable(self):
        """Gets the repeatable of this AddCertificateRequest.  # noqa: E501


        :return: The repeatable of this AddCertificateRequest.  # noqa: E501
        :rtype: bool
        """
        return self._repeatable

    @repeatable.setter
    def repeatable(self, repeatable):
        """Sets the repeatable of this AddCertificateRequest.


        :param repeatable: The repeatable of this AddCertificateRequest.  # noqa: E501
        :type: bool
        """

        self._repeatable = repeatable

    @property
    def source(self):
        """Gets the source of this AddCertificateRequest.  # noqa: E501


        :return: The source of this AddCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this AddCertificateRequest.


        :param source: The source of this AddCertificateRequest.  # noqa: E501
        :type: str
        """

        self._source = source

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddCertificateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddCertificateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddCertificateRequest):
            return True

        return self.to_dict() != other.to_dict()
