# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlertingRuleQueryForGetAlertOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'prom_ql': 'str',
        'workspace_id': 'str'
    }

    attribute_map = {
        'prom_ql': 'PromQL',
        'workspace_id': 'WorkspaceId'
    }

    def __init__(self, prom_ql=None, workspace_id=None, _configuration=None):  # noqa: E501
        """AlertingRuleQueryForGetAlertOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._prom_ql = None
        self._workspace_id = None
        self.discriminator = None

        if prom_ql is not None:
            self.prom_ql = prom_ql
        if workspace_id is not None:
            self.workspace_id = workspace_id

    @property
    def prom_ql(self):
        """Gets the prom_ql of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501


        :return: The prom_ql of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._prom_ql

    @prom_ql.setter
    def prom_ql(self, prom_ql):
        """Sets the prom_ql of this AlertingRuleQueryForGetAlertOutput.


        :param prom_ql: The prom_ql of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501
        :type: str
        """

        self._prom_ql = prom_ql

    @property
    def workspace_id(self):
        """Gets the workspace_id of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501


        :return: The workspace_id of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this AlertingRuleQueryForGetAlertOutput.


        :param workspace_id: The workspace_id of this AlertingRuleQueryForGetAlertOutput.  # noqa: E501
        :type: str
        """

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlertingRuleQueryForGetAlertOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlertingRuleQueryForGetAlertOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlertingRuleQueryForGetAlertOutput):
            return True

        return self.to_dict() != other.to_dict()
