# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RunInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affinity_group_size': 'int',
        'auto_renew': 'bool',
        'auto_renew_period': 'int',
        'client_token': 'str',
        'count': 'int',
        'cpu_max_frequency': 'float',
        'credit_specification': 'str',
        'deletion_protection': 'bool',
        'deployment_set_group_number': 'int',
        'deployment_set_id': 'str',
        'description': 'str',
        'dry_run': 'bool',
        'eip_address': 'EipAddressForRunInstancesInput',
        'host_name': 'str',
        'hostname': 'str',
        'hpc_cluster_id': 'str',
        'image_id': 'str',
        'image_release_version': 'str',
        'install_run_command_agent': 'bool',
        'instance_charge_type': 'str',
        'instance_name': 'str',
        'instance_type': 'str',
        'instance_type_id': 'str',
        'keep_image_credential': 'bool',
        'key_pair_name': 'str',
        'min_count': 'int',
        'network_interfaces': 'list[NetworkInterfaceForRunInstancesInput]',
        'password': 'str',
        'period': 'int',
        'period_unit': 'str',
        'placement': 'PlacementForRunInstancesInput',
        'project_name': 'str',
        'security_enhancement_strategy': 'str',
        'spot_price_limit': 'float',
        'spot_strategy': 'str',
        'suffix_index': 'int',
        'tags': 'list[TagForRunInstancesInput]',
        'unique_suffix': 'bool',
        'user_data': 'str',
        'volumes': 'list[VolumeForRunInstancesInput]',
        'zone_id': 'str'
    }

    attribute_map = {
        'affinity_group_size': 'AffinityGroupSize',
        'auto_renew': 'AutoRenew',
        'auto_renew_period': 'AutoRenewPeriod',
        'client_token': 'ClientToken',
        'count': 'Count',
        'cpu_max_frequency': 'CpuMaxFrequency',
        'credit_specification': 'CreditSpecification',
        'deletion_protection': 'DeletionProtection',
        'deployment_set_group_number': 'DeploymentSetGroupNumber',
        'deployment_set_id': 'DeploymentSetId',
        'description': 'Description',
        'dry_run': 'DryRun',
        'eip_address': 'EipAddress',
        'host_name': 'HostName',
        'hostname': 'Hostname',
        'hpc_cluster_id': 'HpcClusterId',
        'image_id': 'ImageId',
        'image_release_version': 'ImageReleaseVersion',
        'install_run_command_agent': 'InstallRunCommandAgent',
        'instance_charge_type': 'InstanceChargeType',
        'instance_name': 'InstanceName',
        'instance_type': 'InstanceType',
        'instance_type_id': 'InstanceTypeId',
        'keep_image_credential': 'KeepImageCredential',
        'key_pair_name': 'KeyPairName',
        'min_count': 'MinCount',
        'network_interfaces': 'NetworkInterfaces',
        'password': 'Password',
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'placement': 'Placement',
        'project_name': 'ProjectName',
        'security_enhancement_strategy': 'SecurityEnhancementStrategy',
        'spot_price_limit': 'SpotPriceLimit',
        'spot_strategy': 'SpotStrategy',
        'suffix_index': 'SuffixIndex',
        'tags': 'Tags',
        'unique_suffix': 'UniqueSuffix',
        'user_data': 'UserData',
        'volumes': 'Volumes',
        'zone_id': 'ZoneId'
    }

    def __init__(self, affinity_group_size=None, auto_renew=None, auto_renew_period=None, client_token=None, count=None, cpu_max_frequency=None, credit_specification=None, deletion_protection=None, deployment_set_group_number=None, deployment_set_id=None, description=None, dry_run=None, eip_address=None, host_name=None, hostname=None, hpc_cluster_id=None, image_id=None, image_release_version=None, install_run_command_agent=None, instance_charge_type=None, instance_name=None, instance_type=None, instance_type_id=None, keep_image_credential=None, key_pair_name=None, min_count=None, network_interfaces=None, password=None, period=None, period_unit=None, placement=None, project_name=None, security_enhancement_strategy=None, spot_price_limit=None, spot_strategy=None, suffix_index=None, tags=None, unique_suffix=None, user_data=None, volumes=None, zone_id=None, _configuration=None):  # noqa: E501
        """RunInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affinity_group_size = None
        self._auto_renew = None
        self._auto_renew_period = None
        self._client_token = None
        self._count = None
        self._cpu_max_frequency = None
        self._credit_specification = None
        self._deletion_protection = None
        self._deployment_set_group_number = None
        self._deployment_set_id = None
        self._description = None
        self._dry_run = None
        self._eip_address = None
        self._host_name = None
        self._hostname = None
        self._hpc_cluster_id = None
        self._image_id = None
        self._image_release_version = None
        self._install_run_command_agent = None
        self._instance_charge_type = None
        self._instance_name = None
        self._instance_type = None
        self._instance_type_id = None
        self._keep_image_credential = None
        self._key_pair_name = None
        self._min_count = None
        self._network_interfaces = None
        self._password = None
        self._period = None
        self._period_unit = None
        self._placement = None
        self._project_name = None
        self._security_enhancement_strategy = None
        self._spot_price_limit = None
        self._spot_strategy = None
        self._suffix_index = None
        self._tags = None
        self._unique_suffix = None
        self._user_data = None
        self._volumes = None
        self._zone_id = None
        self.discriminator = None

        if affinity_group_size is not None:
            self.affinity_group_size = affinity_group_size
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_renew_period is not None:
            self.auto_renew_period = auto_renew_period
        if client_token is not None:
            self.client_token = client_token
        if count is not None:
            self.count = count
        if cpu_max_frequency is not None:
            self.cpu_max_frequency = cpu_max_frequency
        if credit_specification is not None:
            self.credit_specification = credit_specification
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if deployment_set_group_number is not None:
            self.deployment_set_group_number = deployment_set_group_number
        if deployment_set_id is not None:
            self.deployment_set_id = deployment_set_id
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if eip_address is not None:
            self.eip_address = eip_address
        if host_name is not None:
            self.host_name = host_name
        if hostname is not None:
            self.hostname = hostname
        if hpc_cluster_id is not None:
            self.hpc_cluster_id = hpc_cluster_id
        self.image_id = image_id
        if image_release_version is not None:
            self.image_release_version = image_release_version
        if install_run_command_agent is not None:
            self.install_run_command_agent = install_run_command_agent
        if instance_charge_type is not None:
            self.instance_charge_type = instance_charge_type
        self.instance_name = instance_name
        if instance_type is not None:
            self.instance_type = instance_type
        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if keep_image_credential is not None:
            self.keep_image_credential = keep_image_credential
        if key_pair_name is not None:
            self.key_pair_name = key_pair_name
        if min_count is not None:
            self.min_count = min_count
        if network_interfaces is not None:
            self.network_interfaces = network_interfaces
        if password is not None:
            self.password = password
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if placement is not None:
            self.placement = placement
        if project_name is not None:
            self.project_name = project_name
        if security_enhancement_strategy is not None:
            self.security_enhancement_strategy = security_enhancement_strategy
        if spot_price_limit is not None:
            self.spot_price_limit = spot_price_limit
        if spot_strategy is not None:
            self.spot_strategy = spot_strategy
        if suffix_index is not None:
            self.suffix_index = suffix_index
        if tags is not None:
            self.tags = tags
        if unique_suffix is not None:
            self.unique_suffix = unique_suffix
        if user_data is not None:
            self.user_data = user_data
        if volumes is not None:
            self.volumes = volumes
        self.zone_id = zone_id

    @property
    def affinity_group_size(self):
        """Gets the affinity_group_size of this RunInstancesRequest.  # noqa: E501


        :return: The affinity_group_size of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._affinity_group_size

    @affinity_group_size.setter
    def affinity_group_size(self, affinity_group_size):
        """Sets the affinity_group_size of this RunInstancesRequest.


        :param affinity_group_size: The affinity_group_size of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._affinity_group_size = affinity_group_size

    @property
    def auto_renew(self):
        """Gets the auto_renew of this RunInstancesRequest.  # noqa: E501


        :return: The auto_renew of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this RunInstancesRequest.


        :param auto_renew: The auto_renew of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_renew_period(self):
        """Gets the auto_renew_period of this RunInstancesRequest.  # noqa: E501


        :return: The auto_renew_period of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._auto_renew_period

    @auto_renew_period.setter
    def auto_renew_period(self, auto_renew_period):
        """Sets the auto_renew_period of this RunInstancesRequest.


        :param auto_renew_period: The auto_renew_period of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._auto_renew_period = auto_renew_period

    @property
    def client_token(self):
        """Gets the client_token of this RunInstancesRequest.  # noqa: E501


        :return: The client_token of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this RunInstancesRequest.


        :param client_token: The client_token of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def count(self):
        """Gets the count of this RunInstancesRequest.  # noqa: E501


        :return: The count of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this RunInstancesRequest.


        :param count: The count of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def cpu_max_frequency(self):
        """Gets the cpu_max_frequency of this RunInstancesRequest.  # noqa: E501


        :return: The cpu_max_frequency of this RunInstancesRequest.  # noqa: E501
        :rtype: float
        """
        return self._cpu_max_frequency

    @cpu_max_frequency.setter
    def cpu_max_frequency(self, cpu_max_frequency):
        """Sets the cpu_max_frequency of this RunInstancesRequest.


        :param cpu_max_frequency: The cpu_max_frequency of this RunInstancesRequest.  # noqa: E501
        :type: float
        """

        self._cpu_max_frequency = cpu_max_frequency

    @property
    def credit_specification(self):
        """Gets the credit_specification of this RunInstancesRequest.  # noqa: E501


        :return: The credit_specification of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._credit_specification

    @credit_specification.setter
    def credit_specification(self, credit_specification):
        """Sets the credit_specification of this RunInstancesRequest.


        :param credit_specification: The credit_specification of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._credit_specification = credit_specification

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this RunInstancesRequest.  # noqa: E501


        :return: The deletion_protection of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this RunInstancesRequest.


        :param deletion_protection: The deletion_protection of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._deletion_protection = deletion_protection

    @property
    def deployment_set_group_number(self):
        """Gets the deployment_set_group_number of this RunInstancesRequest.  # noqa: E501


        :return: The deployment_set_group_number of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._deployment_set_group_number

    @deployment_set_group_number.setter
    def deployment_set_group_number(self, deployment_set_group_number):
        """Sets the deployment_set_group_number of this RunInstancesRequest.


        :param deployment_set_group_number: The deployment_set_group_number of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._deployment_set_group_number = deployment_set_group_number

    @property
    def deployment_set_id(self):
        """Gets the deployment_set_id of this RunInstancesRequest.  # noqa: E501


        :return: The deployment_set_id of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._deployment_set_id

    @deployment_set_id.setter
    def deployment_set_id(self, deployment_set_id):
        """Sets the deployment_set_id of this RunInstancesRequest.


        :param deployment_set_id: The deployment_set_id of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._deployment_set_id = deployment_set_id

    @property
    def description(self):
        """Gets the description of this RunInstancesRequest.  # noqa: E501


        :return: The description of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this RunInstancesRequest.


        :param description: The description of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this RunInstancesRequest.  # noqa: E501


        :return: The dry_run of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this RunInstancesRequest.


        :param dry_run: The dry_run of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def eip_address(self):
        """Gets the eip_address of this RunInstancesRequest.  # noqa: E501


        :return: The eip_address of this RunInstancesRequest.  # noqa: E501
        :rtype: EipAddressForRunInstancesInput
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this RunInstancesRequest.


        :param eip_address: The eip_address of this RunInstancesRequest.  # noqa: E501
        :type: EipAddressForRunInstancesInput
        """

        self._eip_address = eip_address

    @property
    def host_name(self):
        """Gets the host_name of this RunInstancesRequest.  # noqa: E501


        :return: The host_name of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this RunInstancesRequest.


        :param host_name: The host_name of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def hostname(self):
        """Gets the hostname of this RunInstancesRequest.  # noqa: E501


        :return: The hostname of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this RunInstancesRequest.


        :param hostname: The hostname of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def hpc_cluster_id(self):
        """Gets the hpc_cluster_id of this RunInstancesRequest.  # noqa: E501


        :return: The hpc_cluster_id of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._hpc_cluster_id

    @hpc_cluster_id.setter
    def hpc_cluster_id(self, hpc_cluster_id):
        """Sets the hpc_cluster_id of this RunInstancesRequest.


        :param hpc_cluster_id: The hpc_cluster_id of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._hpc_cluster_id = hpc_cluster_id

    @property
    def image_id(self):
        """Gets the image_id of this RunInstancesRequest.  # noqa: E501


        :return: The image_id of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this RunInstancesRequest.


        :param image_id: The image_id of this RunInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_id is None:
            raise ValueError("Invalid value for `image_id`, must not be `None`")  # noqa: E501

        self._image_id = image_id

    @property
    def image_release_version(self):
        """Gets the image_release_version of this RunInstancesRequest.  # noqa: E501


        :return: The image_release_version of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_release_version

    @image_release_version.setter
    def image_release_version(self, image_release_version):
        """Sets the image_release_version of this RunInstancesRequest.


        :param image_release_version: The image_release_version of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._image_release_version = image_release_version

    @property
    def install_run_command_agent(self):
        """Gets the install_run_command_agent of this RunInstancesRequest.  # noqa: E501


        :return: The install_run_command_agent of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._install_run_command_agent

    @install_run_command_agent.setter
    def install_run_command_agent(self, install_run_command_agent):
        """Sets the install_run_command_agent of this RunInstancesRequest.


        :param install_run_command_agent: The install_run_command_agent of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._install_run_command_agent = install_run_command_agent

    @property
    def instance_charge_type(self):
        """Gets the instance_charge_type of this RunInstancesRequest.  # noqa: E501


        :return: The instance_charge_type of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_charge_type

    @instance_charge_type.setter
    def instance_charge_type(self, instance_charge_type):
        """Sets the instance_charge_type of this RunInstancesRequest.


        :param instance_charge_type: The instance_charge_type of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_charge_type = instance_charge_type

    @property
    def instance_name(self):
        """Gets the instance_name of this RunInstancesRequest.  # noqa: E501


        :return: The instance_name of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this RunInstancesRequest.


        :param instance_name: The instance_name of this RunInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_name is None:
            raise ValueError("Invalid value for `instance_name`, must not be `None`")  # noqa: E501

        self._instance_name = instance_name

    @property
    def instance_type(self):
        """Gets the instance_type of this RunInstancesRequest.  # noqa: E501


        :return: The instance_type of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this RunInstancesRequest.


        :param instance_type: The instance_type of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this RunInstancesRequest.  # noqa: E501


        :return: The instance_type_id of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this RunInstancesRequest.


        :param instance_type_id: The instance_type_id of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def keep_image_credential(self):
        """Gets the keep_image_credential of this RunInstancesRequest.  # noqa: E501


        :return: The keep_image_credential of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._keep_image_credential

    @keep_image_credential.setter
    def keep_image_credential(self, keep_image_credential):
        """Sets the keep_image_credential of this RunInstancesRequest.


        :param keep_image_credential: The keep_image_credential of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._keep_image_credential = keep_image_credential

    @property
    def key_pair_name(self):
        """Gets the key_pair_name of this RunInstancesRequest.  # noqa: E501


        :return: The key_pair_name of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_name

    @key_pair_name.setter
    def key_pair_name(self, key_pair_name):
        """Sets the key_pair_name of this RunInstancesRequest.


        :param key_pair_name: The key_pair_name of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._key_pair_name = key_pair_name

    @property
    def min_count(self):
        """Gets the min_count of this RunInstancesRequest.  # noqa: E501


        :return: The min_count of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._min_count

    @min_count.setter
    def min_count(self, min_count):
        """Sets the min_count of this RunInstancesRequest.


        :param min_count: The min_count of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._min_count = min_count

    @property
    def network_interfaces(self):
        """Gets the network_interfaces of this RunInstancesRequest.  # noqa: E501


        :return: The network_interfaces of this RunInstancesRequest.  # noqa: E501
        :rtype: list[NetworkInterfaceForRunInstancesInput]
        """
        return self._network_interfaces

    @network_interfaces.setter
    def network_interfaces(self, network_interfaces):
        """Sets the network_interfaces of this RunInstancesRequest.


        :param network_interfaces: The network_interfaces of this RunInstancesRequest.  # noqa: E501
        :type: list[NetworkInterfaceForRunInstancesInput]
        """

        self._network_interfaces = network_interfaces

    @property
    def password(self):
        """Gets the password of this RunInstancesRequest.  # noqa: E501


        :return: The password of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this RunInstancesRequest.


        :param password: The password of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def period(self):
        """Gets the period of this RunInstancesRequest.  # noqa: E501


        :return: The period of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this RunInstancesRequest.


        :param period: The period of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this RunInstancesRequest.  # noqa: E501


        :return: The period_unit of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this RunInstancesRequest.


        :param period_unit: The period_unit of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def placement(self):
        """Gets the placement of this RunInstancesRequest.  # noqa: E501


        :return: The placement of this RunInstancesRequest.  # noqa: E501
        :rtype: PlacementForRunInstancesInput
        """
        return self._placement

    @placement.setter
    def placement(self, placement):
        """Sets the placement of this RunInstancesRequest.


        :param placement: The placement of this RunInstancesRequest.  # noqa: E501
        :type: PlacementForRunInstancesInput
        """

        self._placement = placement

    @property
    def project_name(self):
        """Gets the project_name of this RunInstancesRequest.  # noqa: E501


        :return: The project_name of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this RunInstancesRequest.


        :param project_name: The project_name of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def security_enhancement_strategy(self):
        """Gets the security_enhancement_strategy of this RunInstancesRequest.  # noqa: E501


        :return: The security_enhancement_strategy of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_enhancement_strategy

    @security_enhancement_strategy.setter
    def security_enhancement_strategy(self, security_enhancement_strategy):
        """Sets the security_enhancement_strategy of this RunInstancesRequest.


        :param security_enhancement_strategy: The security_enhancement_strategy of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._security_enhancement_strategy = security_enhancement_strategy

    @property
    def spot_price_limit(self):
        """Gets the spot_price_limit of this RunInstancesRequest.  # noqa: E501


        :return: The spot_price_limit of this RunInstancesRequest.  # noqa: E501
        :rtype: float
        """
        return self._spot_price_limit

    @spot_price_limit.setter
    def spot_price_limit(self, spot_price_limit):
        """Sets the spot_price_limit of this RunInstancesRequest.


        :param spot_price_limit: The spot_price_limit of this RunInstancesRequest.  # noqa: E501
        :type: float
        """

        self._spot_price_limit = spot_price_limit

    @property
    def spot_strategy(self):
        """Gets the spot_strategy of this RunInstancesRequest.  # noqa: E501


        :return: The spot_strategy of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._spot_strategy

    @spot_strategy.setter
    def spot_strategy(self, spot_strategy):
        """Sets the spot_strategy of this RunInstancesRequest.


        :param spot_strategy: The spot_strategy of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._spot_strategy = spot_strategy

    @property
    def suffix_index(self):
        """Gets the suffix_index of this RunInstancesRequest.  # noqa: E501


        :return: The suffix_index of this RunInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._suffix_index

    @suffix_index.setter
    def suffix_index(self, suffix_index):
        """Sets the suffix_index of this RunInstancesRequest.


        :param suffix_index: The suffix_index of this RunInstancesRequest.  # noqa: E501
        :type: int
        """

        self._suffix_index = suffix_index

    @property
    def tags(self):
        """Gets the tags of this RunInstancesRequest.  # noqa: E501


        :return: The tags of this RunInstancesRequest.  # noqa: E501
        :rtype: list[TagForRunInstancesInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this RunInstancesRequest.


        :param tags: The tags of this RunInstancesRequest.  # noqa: E501
        :type: list[TagForRunInstancesInput]
        """

        self._tags = tags

    @property
    def unique_suffix(self):
        """Gets the unique_suffix of this RunInstancesRequest.  # noqa: E501


        :return: The unique_suffix of this RunInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._unique_suffix

    @unique_suffix.setter
    def unique_suffix(self, unique_suffix):
        """Sets the unique_suffix of this RunInstancesRequest.


        :param unique_suffix: The unique_suffix of this RunInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._unique_suffix = unique_suffix

    @property
    def user_data(self):
        """Gets the user_data of this RunInstancesRequest.  # noqa: E501


        :return: The user_data of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_data

    @user_data.setter
    def user_data(self, user_data):
        """Sets the user_data of this RunInstancesRequest.


        :param user_data: The user_data of this RunInstancesRequest.  # noqa: E501
        :type: str
        """

        self._user_data = user_data

    @property
    def volumes(self):
        """Gets the volumes of this RunInstancesRequest.  # noqa: E501


        :return: The volumes of this RunInstancesRequest.  # noqa: E501
        :rtype: list[VolumeForRunInstancesInput]
        """
        return self._volumes

    @volumes.setter
    def volumes(self, volumes):
        """Sets the volumes of this RunInstancesRequest.


        :param volumes: The volumes of this RunInstancesRequest.  # noqa: E501
        :type: list[VolumeForRunInstancesInput]
        """

        self._volumes = volumes

    @property
    def zone_id(self):
        """Gets the zone_id of this RunInstancesRequest.  # noqa: E501


        :return: The zone_id of this RunInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this RunInstancesRequest.


        :param zone_id: The zone_id of this RunInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_id is None:
            raise ValueError("Invalid value for `zone_id`, must not be `None`")  # noqa: E501

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RunInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RunInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RunInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
