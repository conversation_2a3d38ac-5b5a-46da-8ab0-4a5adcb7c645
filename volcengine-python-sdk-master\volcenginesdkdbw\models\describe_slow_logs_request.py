# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSlowLogsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'instance_id': 'str',
        'instance_type': 'str',
        'node_id': 'str',
        'order_by': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'region_id': 'str',
        'search_param': 'SearchParamForDescribeSlowLogsInput',
        'sort_by': 'str',
        'start_time': 'int'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'node_id': 'NodeId',
        'order_by': 'OrderBy',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'region_id': 'RegionId',
        'search_param': 'SearchParam',
        'sort_by': 'SortBy',
        'start_time': 'StartTime'
    }

    def __init__(self, end_time=None, instance_id=None, instance_type=None, node_id=None, order_by=None, page_number=None, page_size=None, region_id=None, search_param=None, sort_by=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeSlowLogsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._instance_id = None
        self._instance_type = None
        self._node_id = None
        self._order_by = None
        self._page_number = None
        self._page_size = None
        self._region_id = None
        self._search_param = None
        self._sort_by = None
        self._start_time = None
        self.discriminator = None

        self.end_time = end_time
        self.instance_id = instance_id
        if instance_type is not None:
            self.instance_type = instance_type
        if node_id is not None:
            self.node_id = node_id
        if order_by is not None:
            self.order_by = order_by
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        self.region_id = region_id
        if search_param is not None:
            self.search_param = search_param
        if sort_by is not None:
            self.sort_by = sort_by
        self.start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The end_time of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeSlowLogsRequest.


        :param end_time: The end_time of this DescribeSlowLogsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The instance_id of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeSlowLogsRequest.


        :param instance_id: The instance_id of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The instance_type of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DescribeSlowLogsRequest.


        :param instance_type: The instance_type of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MySQL", "Postgres", "Mongo", "Redis", "VeDBMySQL", "MetaRDS", "MSSQL", "ByteRDS", "MySQLSharding", "MetaMySQL"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def node_id(self):
        """Gets the node_id of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The node_id of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this DescribeSlowLogsRequest.


        :param node_id: The node_id of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def order_by(self):
        """Gets the order_by of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The order_by of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this DescribeSlowLogsRequest.


        :param order_by: The order_by of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Timestamp", "QueryTime", "LockTime", "RowsExamined", "RowsSent"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_by not in allowed_values):
            raise ValueError(
                "Invalid value for `order_by` ({0}), must be one of {1}"  # noqa: E501
                .format(order_by, allowed_values)
            )

        self._order_by = order_by

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The page_number of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSlowLogsRequest.


        :param page_number: The page_number of this DescribeSlowLogsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The page_size of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSlowLogsRequest.


        :param page_size: The page_size of this DescribeSlowLogsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def region_id(self):
        """Gets the region_id of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The region_id of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this DescribeSlowLogsRequest.


        :param region_id: The region_id of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region_id is None:
            raise ValueError("Invalid value for `region_id`, must not be `None`")  # noqa: E501

        self._region_id = region_id

    @property
    def search_param(self):
        """Gets the search_param of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The search_param of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: SearchParamForDescribeSlowLogsInput
        """
        return self._search_param

    @search_param.setter
    def search_param(self, search_param):
        """Sets the search_param of this DescribeSlowLogsRequest.


        :param search_param: The search_param of this DescribeSlowLogsRequest.  # noqa: E501
        :type: SearchParamForDescribeSlowLogsInput
        """

        self._search_param = search_param

    @property
    def sort_by(self):
        """Gets the sort_by of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The sort_by of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this DescribeSlowLogsRequest.


        :param sort_by: The sort_by of this DescribeSlowLogsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["ASC", "DESC"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def start_time(self):
        """Gets the start_time of this DescribeSlowLogsRequest.  # noqa: E501


        :return: The start_time of this DescribeSlowLogsRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeSlowLogsRequest.


        :param start_time: The start_time of this DescribeSlowLogsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSlowLogsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSlowLogsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSlowLogsRequest):
            return True

        return self.to_dict() != other.to_dict()
