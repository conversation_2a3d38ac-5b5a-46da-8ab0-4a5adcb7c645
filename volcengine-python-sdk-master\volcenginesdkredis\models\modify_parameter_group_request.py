# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyParameterGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'param_values': 'list[ParamValueForModifyParameterGroupInput]',
        'parameter_group_id': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'param_values': 'ParamValues',
        'parameter_group_id': 'ParameterGroupId'
    }

    def __init__(self, description=None, name=None, param_values=None, parameter_group_id=None, _configuration=None):  # noqa: E501
        """ModifyParameterGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._param_values = None
        self._parameter_group_id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.name = name
        if param_values is not None:
            self.param_values = param_values
        self.parameter_group_id = parameter_group_id

    @property
    def description(self):
        """Gets the description of this ModifyParameterGroupRequest.  # noqa: E501


        :return: The description of this ModifyParameterGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyParameterGroupRequest.


        :param description: The description of this ModifyParameterGroupRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this ModifyParameterGroupRequest.  # noqa: E501


        :return: The name of this ModifyParameterGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ModifyParameterGroupRequest.


        :param name: The name of this ModifyParameterGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def param_values(self):
        """Gets the param_values of this ModifyParameterGroupRequest.  # noqa: E501


        :return: The param_values of this ModifyParameterGroupRequest.  # noqa: E501
        :rtype: list[ParamValueForModifyParameterGroupInput]
        """
        return self._param_values

    @param_values.setter
    def param_values(self, param_values):
        """Sets the param_values of this ModifyParameterGroupRequest.


        :param param_values: The param_values of this ModifyParameterGroupRequest.  # noqa: E501
        :type: list[ParamValueForModifyParameterGroupInput]
        """

        self._param_values = param_values

    @property
    def parameter_group_id(self):
        """Gets the parameter_group_id of this ModifyParameterGroupRequest.  # noqa: E501


        :return: The parameter_group_id of this ModifyParameterGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._parameter_group_id

    @parameter_group_id.setter
    def parameter_group_id(self, parameter_group_id):
        """Sets the parameter_group_id of this ModifyParameterGroupRequest.


        :param parameter_group_id: The parameter_group_id of this ModifyParameterGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and parameter_group_id is None:
            raise ValueError("Invalid value for `parameter_group_id`, must not be `None`")  # noqa: E501

        self._parameter_group_id = parameter_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyParameterGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyParameterGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyParameterGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
