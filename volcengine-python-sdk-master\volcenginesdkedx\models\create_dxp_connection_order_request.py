# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDXPConnectionOrderRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'billing_mode': 'str',
        'buy_duration': 'str',
        'instance_id': 'str',
        'pay_mode': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'billing_mode': 'BillingMode',
        'buy_duration': 'BuyDuration',
        'instance_id': 'InstanceId',
        'pay_mode': 'PayMode'
    }

    def __init__(self, auto_renew=None, billing_mode=None, buy_duration=None, instance_id=None, pay_mode=None, _configuration=None):  # noqa: E501
        """CreateDXPConnectionOrderRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._billing_mode = None
        self._buy_duration = None
        self._instance_id = None
        self._pay_mode = None
        self.discriminator = None

        self.auto_renew = auto_renew
        self.billing_mode = billing_mode
        self.buy_duration = buy_duration
        self.instance_id = instance_id
        self.pay_mode = pay_mode

    @property
    def auto_renew(self):
        """Gets the auto_renew of this CreateDXPConnectionOrderRequest.  # noqa: E501


        :return: The auto_renew of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this CreateDXPConnectionOrderRequest.


        :param auto_renew: The auto_renew of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and auto_renew is None:
            raise ValueError("Invalid value for `auto_renew`, must not be `None`")  # noqa: E501

        self._auto_renew = auto_renew

    @property
    def billing_mode(self):
        """Gets the billing_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501


        :return: The billing_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this CreateDXPConnectionOrderRequest.


        :param billing_mode: The billing_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and billing_mode is None:
            raise ValueError("Invalid value for `billing_mode`, must not be `None`")  # noqa: E501

        self._billing_mode = billing_mode

    @property
    def buy_duration(self):
        """Gets the buy_duration of this CreateDXPConnectionOrderRequest.  # noqa: E501


        :return: The buy_duration of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._buy_duration

    @buy_duration.setter
    def buy_duration(self, buy_duration):
        """Sets the buy_duration of this CreateDXPConnectionOrderRequest.


        :param buy_duration: The buy_duration of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and buy_duration is None:
            raise ValueError("Invalid value for `buy_duration`, must not be `None`")  # noqa: E501

        self._buy_duration = buy_duration

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDXPConnectionOrderRequest.  # noqa: E501


        :return: The instance_id of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDXPConnectionOrderRequest.


        :param instance_id: The instance_id of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def pay_mode(self):
        """Gets the pay_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501


        :return: The pay_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :rtype: str
        """
        return self._pay_mode

    @pay_mode.setter
    def pay_mode(self, pay_mode):
        """Sets the pay_mode of this CreateDXPConnectionOrderRequest.


        :param pay_mode: The pay_mode of this CreateDXPConnectionOrderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and pay_mode is None:
            raise ValueError("Invalid value for `pay_mode`, must not be `None`")  # noqa: E501

        self._pay_mode = pay_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDXPConnectionOrderRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDXPConnectionOrderRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDXPConnectionOrderRequest):
            return True

        return self.to_dict() != other.to_dict()
