# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateOfficeConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allowed_size': 'list[str]',
        'area_id': 'str',
        'enable_extranet_url': 'bool',
        'lb_strategy': 'str',
        'office_ip': 'OfficeIpForCreateOfficeConfigInput',
        'office_name': 'str',
        'recommended_size': 'str'
    }

    attribute_map = {
        'allowed_size': 'AllowedSize',
        'area_id': 'AreaId',
        'enable_extranet_url': 'EnableExtranetUrl',
        'lb_strategy': 'LBStrategy',
        'office_ip': 'OfficeIp',
        'office_name': 'OfficeName',
        'recommended_size': 'RecommendedSize'
    }

    def __init__(self, allowed_size=None, area_id=None, enable_extranet_url=None, lb_strategy=None, office_ip=None, office_name=None, recommended_size=None, _configuration=None):  # noqa: E501
        """CreateOfficeConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allowed_size = None
        self._area_id = None
        self._enable_extranet_url = None
        self._lb_strategy = None
        self._office_ip = None
        self._office_name = None
        self._recommended_size = None
        self.discriminator = None

        if allowed_size is not None:
            self.allowed_size = allowed_size
        self.area_id = area_id
        self.enable_extranet_url = enable_extranet_url
        self.lb_strategy = lb_strategy
        if office_ip is not None:
            self.office_ip = office_ip
        self.office_name = office_name
        if recommended_size is not None:
            self.recommended_size = recommended_size

    @property
    def allowed_size(self):
        """Gets the allowed_size of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The allowed_size of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_size

    @allowed_size.setter
    def allowed_size(self, allowed_size):
        """Sets the allowed_size of this CreateOfficeConfigRequest.


        :param allowed_size: The allowed_size of this CreateOfficeConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._allowed_size = allowed_size

    @property
    def area_id(self):
        """Gets the area_id of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The area_id of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._area_id

    @area_id.setter
    def area_id(self, area_id):
        """Sets the area_id of this CreateOfficeConfigRequest.


        :param area_id: The area_id of this CreateOfficeConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and area_id is None:
            raise ValueError("Invalid value for `area_id`, must not be `None`")  # noqa: E501

        self._area_id = area_id

    @property
    def enable_extranet_url(self):
        """Gets the enable_extranet_url of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The enable_extranet_url of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_extranet_url

    @enable_extranet_url.setter
    def enable_extranet_url(self, enable_extranet_url):
        """Sets the enable_extranet_url of this CreateOfficeConfigRequest.


        :param enable_extranet_url: The enable_extranet_url of this CreateOfficeConfigRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable_extranet_url is None:
            raise ValueError("Invalid value for `enable_extranet_url`, must not be `None`")  # noqa: E501

        self._enable_extranet_url = enable_extranet_url

    @property
    def lb_strategy(self):
        """Gets the lb_strategy of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The lb_strategy of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._lb_strategy

    @lb_strategy.setter
    def lb_strategy(self, lb_strategy):
        """Sets the lb_strategy of this CreateOfficeConfigRequest.


        :param lb_strategy: The lb_strategy of this CreateOfficeConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lb_strategy is None:
            raise ValueError("Invalid value for `lb_strategy`, must not be `None`")  # noqa: E501

        self._lb_strategy = lb_strategy

    @property
    def office_ip(self):
        """Gets the office_ip of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The office_ip of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: OfficeIpForCreateOfficeConfigInput
        """
        return self._office_ip

    @office_ip.setter
    def office_ip(self, office_ip):
        """Sets the office_ip of this CreateOfficeConfigRequest.


        :param office_ip: The office_ip of this CreateOfficeConfigRequest.  # noqa: E501
        :type: OfficeIpForCreateOfficeConfigInput
        """

        self._office_ip = office_ip

    @property
    def office_name(self):
        """Gets the office_name of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The office_name of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_name

    @office_name.setter
    def office_name(self, office_name):
        """Sets the office_name of this CreateOfficeConfigRequest.


        :param office_name: The office_name of this CreateOfficeConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and office_name is None:
            raise ValueError("Invalid value for `office_name`, must not be `None`")  # noqa: E501

        self._office_name = office_name

    @property
    def recommended_size(self):
        """Gets the recommended_size of this CreateOfficeConfigRequest.  # noqa: E501


        :return: The recommended_size of this CreateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._recommended_size

    @recommended_size.setter
    def recommended_size(self, recommended_size):
        """Sets the recommended_size of this CreateOfficeConfigRequest.


        :param recommended_size: The recommended_size of this CreateOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._recommended_size = recommended_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateOfficeConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateOfficeConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateOfficeConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
