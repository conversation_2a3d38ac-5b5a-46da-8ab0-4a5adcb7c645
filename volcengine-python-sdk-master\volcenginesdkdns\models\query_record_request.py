# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryRecordRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fqdn': 'str',
        'line': 'str',
        'pqdn': 'str',
        'project_name': 'str',
        'record_id': 'str',
        'type': 'str',
        'value': 'str'
    }

    attribute_map = {
        'fqdn': 'FQDN',
        'line': 'Line',
        'pqdn': 'PQDN',
        'project_name': 'ProjectName',
        'record_id': 'RecordID',
        'type': 'Type',
        'value': 'Value'
    }

    def __init__(self, fqdn=None, line=None, pqdn=None, project_name=None, record_id=None, type=None, value=None, _configuration=None):  # noqa: E501
        """QueryRecordRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fqdn = None
        self._line = None
        self._pqdn = None
        self._project_name = None
        self._record_id = None
        self._type = None
        self._value = None
        self.discriminator = None

        if fqdn is not None:
            self.fqdn = fqdn
        if line is not None:
            self.line = line
        if pqdn is not None:
            self.pqdn = pqdn
        if project_name is not None:
            self.project_name = project_name
        if record_id is not None:
            self.record_id = record_id
        if type is not None:
            self.type = type
        if value is not None:
            self.value = value

    @property
    def fqdn(self):
        """Gets the fqdn of this QueryRecordRequest.  # noqa: E501


        :return: The fqdn of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._fqdn

    @fqdn.setter
    def fqdn(self, fqdn):
        """Sets the fqdn of this QueryRecordRequest.


        :param fqdn: The fqdn of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._fqdn = fqdn

    @property
    def line(self):
        """Gets the line of this QueryRecordRequest.  # noqa: E501


        :return: The line of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this QueryRecordRequest.


        :param line: The line of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def pqdn(self):
        """Gets the pqdn of this QueryRecordRequest.  # noqa: E501


        :return: The pqdn of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._pqdn

    @pqdn.setter
    def pqdn(self, pqdn):
        """Sets the pqdn of this QueryRecordRequest.


        :param pqdn: The pqdn of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._pqdn = pqdn

    @property
    def project_name(self):
        """Gets the project_name of this QueryRecordRequest.  # noqa: E501


        :return: The project_name of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this QueryRecordRequest.


        :param project_name: The project_name of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def record_id(self):
        """Gets the record_id of this QueryRecordRequest.  # noqa: E501


        :return: The record_id of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._record_id

    @record_id.setter
    def record_id(self, record_id):
        """Sets the record_id of this QueryRecordRequest.


        :param record_id: The record_id of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._record_id = record_id

    @property
    def type(self):
        """Gets the type of this QueryRecordRequest.  # noqa: E501


        :return: The type of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this QueryRecordRequest.


        :param type: The type of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def value(self):
        """Gets the value of this QueryRecordRequest.  # noqa: E501


        :return: The value of this QueryRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this QueryRecordRequest.


        :param value: The value of this QueryRecordRequest.  # noqa: E501
        :type: str
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryRecordRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryRecordRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryRecordRequest):
            return True

        return self.to_dict() != other.to_dict()
