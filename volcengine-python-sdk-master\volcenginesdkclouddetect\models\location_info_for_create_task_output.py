# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LocationInfoForCreateTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'city': 'str',
        'continent': 'str',
        'country': 'str',
        'isp': 'str',
        'province': 'str'
    }

    attribute_map = {
        'city': 'City',
        'continent': 'Continent',
        'country': 'Country',
        'isp': 'Isp',
        'province': 'Province'
    }

    def __init__(self, city=None, continent=None, country=None, isp=None, province=None, _configuration=None):  # noqa: E501
        """LocationInfoForCreateTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._city = None
        self._continent = None
        self._country = None
        self._isp = None
        self._province = None
        self.discriminator = None

        if city is not None:
            self.city = city
        if continent is not None:
            self.continent = continent
        if country is not None:
            self.country = country
        if isp is not None:
            self.isp = isp
        if province is not None:
            self.province = province

    @property
    def city(self):
        """Gets the city of this LocationInfoForCreateTaskOutput.  # noqa: E501


        :return: The city of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this LocationInfoForCreateTaskOutput.


        :param city: The city of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def continent(self):
        """Gets the continent of this LocationInfoForCreateTaskOutput.  # noqa: E501


        :return: The continent of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._continent

    @continent.setter
    def continent(self, continent):
        """Sets the continent of this LocationInfoForCreateTaskOutput.


        :param continent: The continent of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :type: str
        """

        self._continent = continent

    @property
    def country(self):
        """Gets the country of this LocationInfoForCreateTaskOutput.  # noqa: E501


        :return: The country of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this LocationInfoForCreateTaskOutput.


        :param country: The country of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def isp(self):
        """Gets the isp of this LocationInfoForCreateTaskOutput.  # noqa: E501


        :return: The isp of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this LocationInfoForCreateTaskOutput.


        :param isp: The isp of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def province(self):
        """Gets the province of this LocationInfoForCreateTaskOutput.  # noqa: E501


        :return: The province of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this LocationInfoForCreateTaskOutput.


        :param province: The province of this LocationInfoForCreateTaskOutput.  # noqa: E501
        :type: str
        """

        self._province = province

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LocationInfoForCreateTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LocationInfoForCreateTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LocationInfoForCreateTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
