# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StopStreamResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'stop_res': 'bool'
    }

    attribute_map = {
        'id': 'ID',
        'stop_res': 'StopRes'
    }

    def __init__(self, id=None, stop_res=None, _configuration=None):  # noqa: E501
        """StopStreamResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._stop_res = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if stop_res is not None:
            self.stop_res = stop_res

    @property
    def id(self):
        """Gets the id of this StopStreamResponse.  # noqa: E501


        :return: The id of this StopStreamResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this StopStreamResponse.


        :param id: The id of this StopStreamResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def stop_res(self):
        """Gets the stop_res of this StopStreamResponse.  # noqa: E501


        :return: The stop_res of this StopStreamResponse.  # noqa: E501
        :rtype: bool
        """
        return self._stop_res

    @stop_res.setter
    def stop_res(self, stop_res):
        """Sets the stop_res of this StopStreamResponse.


        :param stop_res: The stop_res of this StopStreamResponse.  # noqa: E501
        :type: bool
        """

        self._stop_res = stop_res

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StopStreamResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StopStreamResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StopStreamResponse):
            return True

        return self.to_dict() != other.to_dict()
