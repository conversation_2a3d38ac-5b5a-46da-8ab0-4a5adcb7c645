# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckZoneResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_sub_domain': 'bool',
        'message': 'str',
        'name_server': 'list[str]',
        'registered': 'bool',
        'zone_name': 'str',
        'zone_owned_by_other_user': 'bool'
    }

    attribute_map = {
        'is_sub_domain': 'IsSubDomain',
        'message': 'Message',
        'name_server': 'NameServer',
        'registered': 'Registered',
        'zone_name': 'ZoneName',
        'zone_owned_by_other_user': 'ZoneOwnedByOtherUser'
    }

    def __init__(self, is_sub_domain=None, message=None, name_server=None, registered=None, zone_name=None, zone_owned_by_other_user=None, _configuration=None):  # noqa: E501
        """CheckZoneResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_sub_domain = None
        self._message = None
        self._name_server = None
        self._registered = None
        self._zone_name = None
        self._zone_owned_by_other_user = None
        self.discriminator = None

        if is_sub_domain is not None:
            self.is_sub_domain = is_sub_domain
        if message is not None:
            self.message = message
        if name_server is not None:
            self.name_server = name_server
        if registered is not None:
            self.registered = registered
        if zone_name is not None:
            self.zone_name = zone_name
        if zone_owned_by_other_user is not None:
            self.zone_owned_by_other_user = zone_owned_by_other_user

    @property
    def is_sub_domain(self):
        """Gets the is_sub_domain of this CheckZoneResponse.  # noqa: E501


        :return: The is_sub_domain of this CheckZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_sub_domain

    @is_sub_domain.setter
    def is_sub_domain(self, is_sub_domain):
        """Sets the is_sub_domain of this CheckZoneResponse.


        :param is_sub_domain: The is_sub_domain of this CheckZoneResponse.  # noqa: E501
        :type: bool
        """

        self._is_sub_domain = is_sub_domain

    @property
    def message(self):
        """Gets the message of this CheckZoneResponse.  # noqa: E501


        :return: The message of this CheckZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this CheckZoneResponse.


        :param message: The message of this CheckZoneResponse.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def name_server(self):
        """Gets the name_server of this CheckZoneResponse.  # noqa: E501


        :return: The name_server of this CheckZoneResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._name_server

    @name_server.setter
    def name_server(self, name_server):
        """Sets the name_server of this CheckZoneResponse.


        :param name_server: The name_server of this CheckZoneResponse.  # noqa: E501
        :type: list[str]
        """

        self._name_server = name_server

    @property
    def registered(self):
        """Gets the registered of this CheckZoneResponse.  # noqa: E501


        :return: The registered of this CheckZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._registered

    @registered.setter
    def registered(self, registered):
        """Sets the registered of this CheckZoneResponse.


        :param registered: The registered of this CheckZoneResponse.  # noqa: E501
        :type: bool
        """

        self._registered = registered

    @property
    def zone_name(self):
        """Gets the zone_name of this CheckZoneResponse.  # noqa: E501


        :return: The zone_name of this CheckZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this CheckZoneResponse.


        :param zone_name: The zone_name of this CheckZoneResponse.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    @property
    def zone_owned_by_other_user(self):
        """Gets the zone_owned_by_other_user of this CheckZoneResponse.  # noqa: E501


        :return: The zone_owned_by_other_user of this CheckZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._zone_owned_by_other_user

    @zone_owned_by_other_user.setter
    def zone_owned_by_other_user(self, zone_owned_by_other_user):
        """Sets the zone_owned_by_other_user of this CheckZoneResponse.


        :param zone_owned_by_other_user: The zone_owned_by_other_user of this CheckZoneResponse.  # noqa: E501
        :type: bool
        """

        self._zone_owned_by_other_user = zone_owned_by_other_user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckZoneResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckZoneResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckZoneResponse):
            return True

        return self.to_dict() != other.to_dict()
