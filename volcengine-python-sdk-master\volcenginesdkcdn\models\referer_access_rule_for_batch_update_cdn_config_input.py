# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RefererAccessRuleForBatchUpdateCdnConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_empty': 'bool',
        'referers': 'list[str]',
        'referers_type': 'ReferersTypeForBatchUpdateCdnConfigInput',
        'rule_type': 'str',
        'shared_config': 'SharedConfigForBatchUpdateCdnConfigInput',
        'switch': 'bool'
    }

    attribute_map = {
        'allow_empty': 'AllowEmpty',
        'referers': 'Referers',
        'referers_type': 'ReferersType',
        'rule_type': 'RuleType',
        'shared_config': 'SharedConfig',
        'switch': 'Switch'
    }

    def __init__(self, allow_empty=None, referers=None, referers_type=None, rule_type=None, shared_config=None, switch=None, _configuration=None):  # noqa: E501
        """RefererAccessRuleForBatchUpdateCdnConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_empty = None
        self._referers = None
        self._referers_type = None
        self._rule_type = None
        self._shared_config = None
        self._switch = None
        self.discriminator = None

        if allow_empty is not None:
            self.allow_empty = allow_empty
        if referers is not None:
            self.referers = referers
        if referers_type is not None:
            self.referers_type = referers_type
        if rule_type is not None:
            self.rule_type = rule_type
        if shared_config is not None:
            self.shared_config = shared_config
        if switch is not None:
            self.switch = switch

    @property
    def allow_empty(self):
        """Gets the allow_empty of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The allow_empty of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._allow_empty

    @allow_empty.setter
    def allow_empty(self, allow_empty):
        """Sets the allow_empty of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param allow_empty: The allow_empty of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: bool
        """

        self._allow_empty = allow_empty

    @property
    def referers(self):
        """Gets the referers of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The referers of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._referers

    @referers.setter
    def referers(self, referers):
        """Sets the referers of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param referers: The referers of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._referers = referers

    @property
    def referers_type(self):
        """Gets the referers_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The referers_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: ReferersTypeForBatchUpdateCdnConfigInput
        """
        return self._referers_type

    @referers_type.setter
    def referers_type(self, referers_type):
        """Sets the referers_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param referers_type: The referers_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: ReferersTypeForBatchUpdateCdnConfigInput
        """

        self._referers_type = referers_type

    @property
    def rule_type(self):
        """Gets the rule_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The rule_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._rule_type

    @rule_type.setter
    def rule_type(self, rule_type):
        """Sets the rule_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param rule_type: The rule_type of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: str
        """

        self._rule_type = rule_type

    @property
    def shared_config(self):
        """Gets the shared_config of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The shared_config of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: SharedConfigForBatchUpdateCdnConfigInput
        """
        return self._shared_config

    @shared_config.setter
    def shared_config(self, shared_config):
        """Sets the shared_config of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param shared_config: The shared_config of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: SharedConfigForBatchUpdateCdnConfigInput
        """

        self._shared_config = shared_config

    @property
    def switch(self):
        """Gets the switch of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The switch of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._switch

    @switch.setter
    def switch(self, switch):
        """Sets the switch of this RefererAccessRuleForBatchUpdateCdnConfigInput.


        :param switch: The switch of this RefererAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: bool
        """

        self._switch = switch

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RefererAccessRuleForBatchUpdateCdnConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RefererAccessRuleForBatchUpdateCdnConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RefererAccessRuleForBatchUpdateCdnConfigInput):
            return True

        return self.to_dict() != other.to_dict()
