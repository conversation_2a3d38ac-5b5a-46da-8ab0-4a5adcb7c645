# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyIpAllowListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'component': 'str',
        'instance_id': 'str',
        'ip_list': 'str',
        'type': 'str'
    }

    attribute_map = {
        'component': 'Component',
        'instance_id': 'InstanceId',
        'ip_list': 'IpList',
        'type': 'Type'
    }

    def __init__(self, component=None, instance_id=None, ip_list=None, type=None, _configuration=None):  # noqa: E501
        """ModifyIpAllowListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._component = None
        self._instance_id = None
        self._ip_list = None
        self._type = None
        self.discriminator = None

        self.component = component
        self.instance_id = instance_id
        self.ip_list = ip_list
        self.type = type

    @property
    def component(self):
        """Gets the component of this ModifyIpAllowListRequest.  # noqa: E501


        :return: The component of this ModifyIpAllowListRequest.  # noqa: E501
        :rtype: str
        """
        return self._component

    @component.setter
    def component(self, component):
        """Sets the component of this ModifyIpAllowListRequest.


        :param component: The component of this ModifyIpAllowListRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and component is None:
            raise ValueError("Invalid value for `component`, must not be `None`")  # noqa: E501

        self._component = component

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyIpAllowListRequest.  # noqa: E501


        :return: The instance_id of this ModifyIpAllowListRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyIpAllowListRequest.


        :param instance_id: The instance_id of this ModifyIpAllowListRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def ip_list(self):
        """Gets the ip_list of this ModifyIpAllowListRequest.  # noqa: E501


        :return: The ip_list of this ModifyIpAllowListRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this ModifyIpAllowListRequest.


        :param ip_list: The ip_list of this ModifyIpAllowListRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_list is None:
            raise ValueError("Invalid value for `ip_list`, must not be `None`")  # noqa: E501

        self._ip_list = ip_list

    @property
    def type(self):
        """Gets the type of this ModifyIpAllowListRequest.  # noqa: E501


        :return: The type of this ModifyIpAllowListRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ModifyIpAllowListRequest.


        :param type: The type of this ModifyIpAllowListRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyIpAllowListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyIpAllowListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyIpAllowListRequest):
            return True

        return self.to_dict() != other.to_dict()
