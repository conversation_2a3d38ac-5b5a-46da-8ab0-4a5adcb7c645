# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourcePoolRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name_key': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'project_id': 'str',
        'status_key': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'name_key': 'NameKey',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_id': 'ProjectId',
        'status_key': 'StatusKey',
        'zone_id': 'ZoneId'
    }

    def __init__(self, name_key=None, page_num=None, page_size=None, project_id=None, status_key=None, zone_id=None, _configuration=None):  # noqa: E501
        """ListResourcePoolRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name_key = None
        self._page_num = None
        self._page_size = None
        self._project_id = None
        self._status_key = None
        self._zone_id = None
        self.discriminator = None

        if name_key is not None:
            self.name_key = name_key
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if project_id is not None:
            self.project_id = project_id
        if status_key is not None:
            self.status_key = status_key
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def name_key(self):
        """Gets the name_key of this ListResourcePoolRequest.  # noqa: E501


        :return: The name_key of this ListResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._name_key

    @name_key.setter
    def name_key(self, name_key):
        """Sets the name_key of this ListResourcePoolRequest.


        :param name_key: The name_key of this ListResourcePoolRequest.  # noqa: E501
        :type: str
        """

        self._name_key = name_key

    @property
    def page_num(self):
        """Gets the page_num of this ListResourcePoolRequest.  # noqa: E501


        :return: The page_num of this ListResourcePoolRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListResourcePoolRequest.


        :param page_num: The page_num of this ListResourcePoolRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListResourcePoolRequest.  # noqa: E501


        :return: The page_size of this ListResourcePoolRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListResourcePoolRequest.


        :param page_size: The page_size of this ListResourcePoolRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_id(self):
        """Gets the project_id of this ListResourcePoolRequest.  # noqa: E501


        :return: The project_id of this ListResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this ListResourcePoolRequest.


        :param project_id: The project_id of this ListResourcePoolRequest.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def status_key(self):
        """Gets the status_key of this ListResourcePoolRequest.  # noqa: E501


        :return: The status_key of this ListResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._status_key

    @status_key.setter
    def status_key(self, status_key):
        """Sets the status_key of this ListResourcePoolRequest.


        :param status_key: The status_key of this ListResourcePoolRequest.  # noqa: E501
        :type: str
        """

        self._status_key = status_key

    @property
    def zone_id(self):
        """Gets the zone_id of this ListResourcePoolRequest.  # noqa: E501


        :return: The zone_id of this ListResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ListResourcePoolRequest.


        :param zone_id: The zone_id of this ListResourcePoolRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourcePoolRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourcePoolRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourcePoolRequest):
            return True

        return self.to_dict() != other.to_dict()
