# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'dry_run': 'bool',
        'model_reference': 'ModelReferenceForCreateEndpointInput',
        'model_unit_id': 'str',
        'moderation': 'ModerationForCreateEndpointInput',
        'name': 'str',
        'project_name': 'str',
        'rate_limit': 'RateLimitForCreateEndpointInput',
        'tags': 'list[TagForCreateEndpointInput]'
    }

    attribute_map = {
        'description': 'Description',
        'dry_run': 'DryRun',
        'model_reference': 'ModelReference',
        'model_unit_id': 'ModelUnitId',
        'moderation': 'Moderation',
        'name': 'Name',
        'project_name': 'ProjectName',
        'rate_limit': 'RateLimit',
        'tags': 'Tags'
    }

    def __init__(self, description=None, dry_run=None, model_reference=None, model_unit_id=None, moderation=None, name=None, project_name=None, rate_limit=None, tags=None, _configuration=None):  # noqa: E501
        """CreateEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._dry_run = None
        self._model_reference = None
        self._model_unit_id = None
        self._moderation = None
        self._name = None
        self._project_name = None
        self._rate_limit = None
        self._tags = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if model_reference is not None:
            self.model_reference = model_reference
        if model_unit_id is not None:
            self.model_unit_id = model_unit_id
        if moderation is not None:
            self.moderation = moderation
        self.name = name
        if project_name is not None:
            self.project_name = project_name
        if rate_limit is not None:
            self.rate_limit = rate_limit
        if tags is not None:
            self.tags = tags

    @property
    def description(self):
        """Gets the description of this CreateEndpointRequest.  # noqa: E501


        :return: The description of this CreateEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateEndpointRequest.


        :param description: The description of this CreateEndpointRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateEndpointRequest.  # noqa: E501


        :return: The dry_run of this CreateEndpointRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateEndpointRequest.


        :param dry_run: The dry_run of this CreateEndpointRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def model_reference(self):
        """Gets the model_reference of this CreateEndpointRequest.  # noqa: E501


        :return: The model_reference of this CreateEndpointRequest.  # noqa: E501
        :rtype: ModelReferenceForCreateEndpointInput
        """
        return self._model_reference

    @model_reference.setter
    def model_reference(self, model_reference):
        """Sets the model_reference of this CreateEndpointRequest.


        :param model_reference: The model_reference of this CreateEndpointRequest.  # noqa: E501
        :type: ModelReferenceForCreateEndpointInput
        """

        self._model_reference = model_reference

    @property
    def model_unit_id(self):
        """Gets the model_unit_id of this CreateEndpointRequest.  # noqa: E501


        :return: The model_unit_id of this CreateEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._model_unit_id

    @model_unit_id.setter
    def model_unit_id(self, model_unit_id):
        """Sets the model_unit_id of this CreateEndpointRequest.


        :param model_unit_id: The model_unit_id of this CreateEndpointRequest.  # noqa: E501
        :type: str
        """

        self._model_unit_id = model_unit_id

    @property
    def moderation(self):
        """Gets the moderation of this CreateEndpointRequest.  # noqa: E501


        :return: The moderation of this CreateEndpointRequest.  # noqa: E501
        :rtype: ModerationForCreateEndpointInput
        """
        return self._moderation

    @moderation.setter
    def moderation(self, moderation):
        """Sets the moderation of this CreateEndpointRequest.


        :param moderation: The moderation of this CreateEndpointRequest.  # noqa: E501
        :type: ModerationForCreateEndpointInput
        """

        self._moderation = moderation

    @property
    def name(self):
        """Gets the name of this CreateEndpointRequest.  # noqa: E501


        :return: The name of this CreateEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateEndpointRequest.


        :param name: The name of this CreateEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateEndpointRequest.  # noqa: E501


        :return: The project_name of this CreateEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateEndpointRequest.


        :param project_name: The project_name of this CreateEndpointRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rate_limit(self):
        """Gets the rate_limit of this CreateEndpointRequest.  # noqa: E501


        :return: The rate_limit of this CreateEndpointRequest.  # noqa: E501
        :rtype: RateLimitForCreateEndpointInput
        """
        return self._rate_limit

    @rate_limit.setter
    def rate_limit(self, rate_limit):
        """Sets the rate_limit of this CreateEndpointRequest.


        :param rate_limit: The rate_limit of this CreateEndpointRequest.  # noqa: E501
        :type: RateLimitForCreateEndpointInput
        """

        self._rate_limit = rate_limit

    @property
    def tags(self):
        """Gets the tags of this CreateEndpointRequest.  # noqa: E501


        :return: The tags of this CreateEndpointRequest.  # noqa: E501
        :rtype: list[TagForCreateEndpointInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateEndpointRequest.


        :param tags: The tags of this CreateEndpointRequest.  # noqa: E501
        :type: list[TagForCreateEndpointInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
