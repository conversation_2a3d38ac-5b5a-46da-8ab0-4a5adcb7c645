# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionForDescribeCdnConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'condition_rule': 'list[ConditionRuleForDescribeCdnConfigOutput]',
        'connective': 'str'
    }

    attribute_map = {
        'condition_rule': 'ConditionRule',
        'connective': 'Connective'
    }

    def __init__(self, condition_rule=None, connective=None, _configuration=None):  # noqa: E501
        """ConditionForDescribeCdnConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._condition_rule = None
        self._connective = None
        self.discriminator = None

        if condition_rule is not None:
            self.condition_rule = condition_rule
        if connective is not None:
            self.connective = connective

    @property
    def condition_rule(self):
        """Gets the condition_rule of this ConditionForDescribeCdnConfigOutput.  # noqa: E501


        :return: The condition_rule of this ConditionForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: list[ConditionRuleForDescribeCdnConfigOutput]
        """
        return self._condition_rule

    @condition_rule.setter
    def condition_rule(self, condition_rule):
        """Sets the condition_rule of this ConditionForDescribeCdnConfigOutput.


        :param condition_rule: The condition_rule of this ConditionForDescribeCdnConfigOutput.  # noqa: E501
        :type: list[ConditionRuleForDescribeCdnConfigOutput]
        """

        self._condition_rule = condition_rule

    @property
    def connective(self):
        """Gets the connective of this ConditionForDescribeCdnConfigOutput.  # noqa: E501


        :return: The connective of this ConditionForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._connective

    @connective.setter
    def connective(self, connective):
        """Sets the connective of this ConditionForDescribeCdnConfigOutput.


        :param connective: The connective of this ConditionForDescribeCdnConfigOutput.  # noqa: E501
        :type: str
        """

        self._connective = connective

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionForDescribeCdnConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionForDescribeCdnConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionForDescribeCdnConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
