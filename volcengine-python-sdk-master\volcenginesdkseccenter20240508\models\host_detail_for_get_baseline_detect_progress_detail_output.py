# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HostDetailForGetBaselineDetectProgressDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detecting_host': 'list[str]',
        'failed_host': 'list[FailedHostForGetBaselineDetectProgressDetailOutput]'
    }

    attribute_map = {
        'detecting_host': 'DetectingHost',
        'failed_host': 'FailedHost'
    }

    def __init__(self, detecting_host=None, failed_host=None, _configuration=None):  # noqa: E501
        """HostDetailForGetBaselineDetectProgressDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detecting_host = None
        self._failed_host = None
        self.discriminator = None

        if detecting_host is not None:
            self.detecting_host = detecting_host
        if failed_host is not None:
            self.failed_host = failed_host

    @property
    def detecting_host(self):
        """Gets the detecting_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The detecting_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._detecting_host

    @detecting_host.setter
    def detecting_host(self, detecting_host):
        """Sets the detecting_host of this HostDetailForGetBaselineDetectProgressDetailOutput.


        :param detecting_host: The detecting_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._detecting_host = detecting_host

    @property
    def failed_host(self):
        """Gets the failed_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The failed_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: list[FailedHostForGetBaselineDetectProgressDetailOutput]
        """
        return self._failed_host

    @failed_host.setter
    def failed_host(self, failed_host):
        """Sets the failed_host of this HostDetailForGetBaselineDetectProgressDetailOutput.


        :param failed_host: The failed_host of this HostDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: list[FailedHostForGetBaselineDetectProgressDetailOutput]
        """

        self._failed_host = failed_host

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HostDetailForGetBaselineDetectProgressDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HostDetailForGetBaselineDetectProgressDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HostDetailForGetBaselineDetectProgressDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
