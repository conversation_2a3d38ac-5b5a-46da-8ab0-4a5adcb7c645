# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListClusterUsersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'create_time': 'int',
        'creator_name': 'str',
        'description': 'str',
        'update_time': 'int',
        'user_name': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'create_time': 'CreateTime',
        'creator_name': 'CreatorName',
        'description': 'Description',
        'update_time': 'UpdateTime',
        'user_name': 'UserName'
    }

    def __init__(self, cluster_id=None, create_time=None, creator_name=None, description=None, update_time=None, user_name=None, _configuration=None):  # noqa: E501
        """ItemForListClusterUsersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._create_time = None
        self._creator_name = None
        self._description = None
        self._update_time = None
        self._user_name = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if create_time is not None:
            self.create_time = create_time
        if creator_name is not None:
            self.creator_name = creator_name
        if description is not None:
            self.description = description
        if update_time is not None:
            self.update_time = update_time
        if user_name is not None:
            self.user_name = user_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListClusterUsersOutput.


        :param cluster_id: The cluster_id of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The create_time of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListClusterUsersOutput.


        :param create_time: The create_time of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def creator_name(self):
        """Gets the creator_name of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The creator_name of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._creator_name

    @creator_name.setter
    def creator_name(self, creator_name):
        """Sets the creator_name of this ItemForListClusterUsersOutput.


        :param creator_name: The creator_name of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: str
        """

        self._creator_name = creator_name

    @property
    def description(self):
        """Gets the description of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The description of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListClusterUsersOutput.


        :param description: The description of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The update_time of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListClusterUsersOutput.


        :param update_time: The update_time of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def user_name(self):
        """Gets the user_name of this ItemForListClusterUsersOutput.  # noqa: E501


        :return: The user_name of this ItemForListClusterUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ItemForListClusterUsersOutput.


        :param user_name: The user_name of this ItemForListClusterUsersOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListClusterUsersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListClusterUsersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListClusterUsersOutput):
            return True

        return self.to_dict() != other.to_dict()
