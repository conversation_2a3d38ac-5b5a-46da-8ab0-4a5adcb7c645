# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListInteractionScriptCommentsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comment_id': 'int',
        'interaction_script_id': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'send_time': 'int'
    }

    attribute_map = {
        'comment_id': 'CommentId',
        'interaction_script_id': 'InteractionScriptId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'send_time': 'SendTime'
    }

    def __init__(self, comment_id=None, interaction_script_id=None, page_number=None, page_size=None, send_time=None, _configuration=None):  # noqa: E501
        """ListInteractionScriptCommentsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comment_id = None
        self._interaction_script_id = None
        self._page_number = None
        self._page_size = None
        self._send_time = None
        self.discriminator = None

        if comment_id is not None:
            self.comment_id = comment_id
        self.interaction_script_id = interaction_script_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if send_time is not None:
            self.send_time = send_time

    @property
    def comment_id(self):
        """Gets the comment_id of this ListInteractionScriptCommentsRequest.  # noqa: E501


        :return: The comment_id of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._comment_id

    @comment_id.setter
    def comment_id(self, comment_id):
        """Sets the comment_id of this ListInteractionScriptCommentsRequest.


        :param comment_id: The comment_id of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :type: int
        """

        self._comment_id = comment_id

    @property
    def interaction_script_id(self):
        """Gets the interaction_script_id of this ListInteractionScriptCommentsRequest.  # noqa: E501


        :return: The interaction_script_id of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._interaction_script_id

    @interaction_script_id.setter
    def interaction_script_id(self, interaction_script_id):
        """Sets the interaction_script_id of this ListInteractionScriptCommentsRequest.


        :param interaction_script_id: The interaction_script_id of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and interaction_script_id is None:
            raise ValueError("Invalid value for `interaction_script_id`, must not be `None`")  # noqa: E501

        self._interaction_script_id = interaction_script_id

    @property
    def page_number(self):
        """Gets the page_number of this ListInteractionScriptCommentsRequest.  # noqa: E501


        :return: The page_number of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListInteractionScriptCommentsRequest.


        :param page_number: The page_number of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListInteractionScriptCommentsRequest.  # noqa: E501


        :return: The page_size of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListInteractionScriptCommentsRequest.


        :param page_size: The page_size of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def send_time(self):
        """Gets the send_time of this ListInteractionScriptCommentsRequest.  # noqa: E501


        :return: The send_time of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this ListInteractionScriptCommentsRequest.


        :param send_time: The send_time of this ListInteractionScriptCommentsRequest.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListInteractionScriptCommentsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListInteractionScriptCommentsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListInteractionScriptCommentsRequest):
            return True

        return self.to_dict() != other.to_dict()
