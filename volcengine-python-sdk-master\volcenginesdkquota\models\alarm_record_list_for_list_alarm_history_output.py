# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmRecordListForListAlarmHistoryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_time': 'str',
        'alarm_type': 'str',
        'alarm_value': 'str',
        'dimensions': 'str',
        'metric_unit': 'str',
        'product_name': 'str',
        'provider_code': 'str',
        'quota_code': 'str',
        'quota_type': 'str',
        'rule_id': 'str',
        'rule_name': 'str',
        'send_message_status': 'int',
        'threshold': 'str'
    }

    attribute_map = {
        'alarm_time': 'AlarmTime',
        'alarm_type': 'AlarmType',
        'alarm_value': 'AlarmValue',
        'dimensions': 'Dimensions',
        'metric_unit': 'MetricUnit',
        'product_name': 'ProductName',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode',
        'quota_type': 'QuotaType',
        'rule_id': 'RuleID',
        'rule_name': 'RuleName',
        'send_message_status': 'SendMessageStatus',
        'threshold': 'Threshold'
    }

    def __init__(self, alarm_time=None, alarm_type=None, alarm_value=None, dimensions=None, metric_unit=None, product_name=None, provider_code=None, quota_code=None, quota_type=None, rule_id=None, rule_name=None, send_message_status=None, threshold=None, _configuration=None):  # noqa: E501
        """AlarmRecordListForListAlarmHistoryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_time = None
        self._alarm_type = None
        self._alarm_value = None
        self._dimensions = None
        self._metric_unit = None
        self._product_name = None
        self._provider_code = None
        self._quota_code = None
        self._quota_type = None
        self._rule_id = None
        self._rule_name = None
        self._send_message_status = None
        self._threshold = None
        self.discriminator = None

        if alarm_time is not None:
            self.alarm_time = alarm_time
        if alarm_type is not None:
            self.alarm_type = alarm_type
        if alarm_value is not None:
            self.alarm_value = alarm_value
        if dimensions is not None:
            self.dimensions = dimensions
        if metric_unit is not None:
            self.metric_unit = metric_unit
        if product_name is not None:
            self.product_name = product_name
        if provider_code is not None:
            self.provider_code = provider_code
        if quota_code is not None:
            self.quota_code = quota_code
        if quota_type is not None:
            self.quota_type = quota_type
        if rule_id is not None:
            self.rule_id = rule_id
        if rule_name is not None:
            self.rule_name = rule_name
        if send_message_status is not None:
            self.send_message_status = send_message_status
        if threshold is not None:
            self.threshold = threshold

    @property
    def alarm_time(self):
        """Gets the alarm_time of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The alarm_time of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_time

    @alarm_time.setter
    def alarm_time(self, alarm_time):
        """Sets the alarm_time of this AlarmRecordListForListAlarmHistoryOutput.


        :param alarm_time: The alarm_time of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._alarm_time = alarm_time

    @property
    def alarm_type(self):
        """Gets the alarm_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The alarm_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this AlarmRecordListForListAlarmHistoryOutput.


        :param alarm_type: The alarm_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def alarm_value(self):
        """Gets the alarm_value of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The alarm_value of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_value

    @alarm_value.setter
    def alarm_value(self, alarm_value):
        """Sets the alarm_value of this AlarmRecordListForListAlarmHistoryOutput.


        :param alarm_value: The alarm_value of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._alarm_value = alarm_value

    @property
    def dimensions(self):
        """Gets the dimensions of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The dimensions of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this AlarmRecordListForListAlarmHistoryOutput.


        :param dimensions: The dimensions of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._dimensions = dimensions

    @property
    def metric_unit(self):
        """Gets the metric_unit of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The metric_unit of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_unit

    @metric_unit.setter
    def metric_unit(self, metric_unit):
        """Sets the metric_unit of this AlarmRecordListForListAlarmHistoryOutput.


        :param metric_unit: The metric_unit of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._metric_unit = metric_unit

    @property
    def product_name(self):
        """Gets the product_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The product_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this AlarmRecordListForListAlarmHistoryOutput.


        :param product_name: The product_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def provider_code(self):
        """Gets the provider_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The provider_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this AlarmRecordListForListAlarmHistoryOutput.


        :param provider_code: The provider_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The quota_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this AlarmRecordListForListAlarmHistoryOutput.


        :param quota_code: The quota_code of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._quota_code = quota_code

    @property
    def quota_type(self):
        """Gets the quota_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The quota_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this AlarmRecordListForListAlarmHistoryOutput.


        :param quota_type: The quota_type of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    @property
    def rule_id(self):
        """Gets the rule_id of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The rule_id of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this AlarmRecordListForListAlarmHistoryOutput.


        :param rule_id: The rule_id of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The rule_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this AlarmRecordListForListAlarmHistoryOutput.


        :param rule_name: The rule_name of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    @property
    def send_message_status(self):
        """Gets the send_message_status of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The send_message_status of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_message_status

    @send_message_status.setter
    def send_message_status(self, send_message_status):
        """Sets the send_message_status of this AlarmRecordListForListAlarmHistoryOutput.


        :param send_message_status: The send_message_status of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: int
        """

        self._send_message_status = send_message_status

    @property
    def threshold(self):
        """Gets the threshold of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501


        :return: The threshold of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this AlarmRecordListForListAlarmHistoryOutput.


        :param threshold: The threshold of this AlarmRecordListForListAlarmHistoryOutput.  # noqa: E501
        :type: str
        """

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmRecordListForListAlarmHistoryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmRecordListForListAlarmHistoryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmRecordListForListAlarmHistoryOutput):
            return True

        return self.to_dict() != other.to_dict()
