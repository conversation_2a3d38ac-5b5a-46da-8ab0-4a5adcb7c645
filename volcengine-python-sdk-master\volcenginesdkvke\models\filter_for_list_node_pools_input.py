# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListNodePoolsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_scaling_enabled': 'bool',
        'cluster_ids': 'list[str]',
        'create_client_token': 'str',
        'ids': 'list[str]',
        'name': 'str',
        'statuses': 'list[StatusForListNodePoolsInput]',
        'update_client_token': 'str'
    }

    attribute_map = {
        'auto_scaling_enabled': 'AutoScaling.Enabled',
        'cluster_ids': 'ClusterIds',
        'create_client_token': 'CreateClientToken',
        'ids': 'Ids',
        'name': 'Name',
        'statuses': 'Statuses',
        'update_client_token': 'UpdateClientToken'
    }

    def __init__(self, auto_scaling_enabled=None, cluster_ids=None, create_client_token=None, ids=None, name=None, statuses=None, update_client_token=None, _configuration=None):  # noqa: E501
        """FilterForListNodePoolsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_scaling_enabled = None
        self._cluster_ids = None
        self._create_client_token = None
        self._ids = None
        self._name = None
        self._statuses = None
        self._update_client_token = None
        self.discriminator = None

        if auto_scaling_enabled is not None:
            self.auto_scaling_enabled = auto_scaling_enabled
        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name
        if statuses is not None:
            self.statuses = statuses
        if update_client_token is not None:
            self.update_client_token = update_client_token

    @property
    def auto_scaling_enabled(self):
        """Gets the auto_scaling_enabled of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The auto_scaling_enabled of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_scaling_enabled

    @auto_scaling_enabled.setter
    def auto_scaling_enabled(self, auto_scaling_enabled):
        """Sets the auto_scaling_enabled of this FilterForListNodePoolsInput.


        :param auto_scaling_enabled: The auto_scaling_enabled of this FilterForListNodePoolsInput.  # noqa: E501
        :type: bool
        """

        self._auto_scaling_enabled = auto_scaling_enabled

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The cluster_ids of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this FilterForListNodePoolsInput.


        :param cluster_ids: The cluster_ids of this FilterForListNodePoolsInput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def create_client_token(self):
        """Gets the create_client_token of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The create_client_token of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this FilterForListNodePoolsInput.


        :param create_client_token: The create_client_token of this FilterForListNodePoolsInput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def ids(self):
        """Gets the ids of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The ids of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListNodePoolsInput.


        :param ids: The ids of this FilterForListNodePoolsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The name of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListNodePoolsInput.


        :param name: The name of this FilterForListNodePoolsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def statuses(self):
        """Gets the statuses of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The statuses of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: list[StatusForListNodePoolsInput]
        """
        return self._statuses

    @statuses.setter
    def statuses(self, statuses):
        """Sets the statuses of this FilterForListNodePoolsInput.


        :param statuses: The statuses of this FilterForListNodePoolsInput.  # noqa: E501
        :type: list[StatusForListNodePoolsInput]
        """

        self._statuses = statuses

    @property
    def update_client_token(self):
        """Gets the update_client_token of this FilterForListNodePoolsInput.  # noqa: E501


        :return: The update_client_token of this FilterForListNodePoolsInput.  # noqa: E501
        :rtype: str
        """
        return self._update_client_token

    @update_client_token.setter
    def update_client_token(self, update_client_token):
        """Sets the update_client_token of this FilterForListNodePoolsInput.


        :param update_client_token: The update_client_token of this FilterForListNodePoolsInput.  # noqa: E501
        :type: str
        """

        self._update_client_token = update_client_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListNodePoolsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListNodePoolsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListNodePoolsInput):
            return True

        return self.to_dict() != other.to_dict()
