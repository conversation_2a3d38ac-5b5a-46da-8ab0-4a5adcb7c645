# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceSpecForDescribeDBInstanceSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'arch_type': 'str',
        'instance_class': 'str',
        'max_connections_per_shard': 'int',
        'node_numbers': 'list[int]',
        'shard_capacity_specs': 'list[ShardCapacitySpecForDescribeDBInstanceSpecsOutput]',
        'shard_numbers': 'list[int]'
    }

    attribute_map = {
        'arch_type': 'ArchType',
        'instance_class': 'InstanceClass',
        'max_connections_per_shard': 'MaxConnectionsPerShard',
        'node_numbers': 'NodeNumbers',
        'shard_capacity_specs': 'ShardCapacitySpecs',
        'shard_numbers': 'ShardNumbers'
    }

    def __init__(self, arch_type=None, instance_class=None, max_connections_per_shard=None, node_numbers=None, shard_capacity_specs=None, shard_numbers=None, _configuration=None):  # noqa: E501
        """InstanceSpecForDescribeDBInstanceSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._arch_type = None
        self._instance_class = None
        self._max_connections_per_shard = None
        self._node_numbers = None
        self._shard_capacity_specs = None
        self._shard_numbers = None
        self.discriminator = None

        if arch_type is not None:
            self.arch_type = arch_type
        if instance_class is not None:
            self.instance_class = instance_class
        if max_connections_per_shard is not None:
            self.max_connections_per_shard = max_connections_per_shard
        if node_numbers is not None:
            self.node_numbers = node_numbers
        if shard_capacity_specs is not None:
            self.shard_capacity_specs = shard_capacity_specs
        if shard_numbers is not None:
            self.shard_numbers = shard_numbers

    @property
    def arch_type(self):
        """Gets the arch_type of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The arch_type of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._arch_type

    @arch_type.setter
    def arch_type(self, arch_type):
        """Sets the arch_type of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param arch_type: The arch_type of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._arch_type = arch_type

    @property
    def instance_class(self):
        """Gets the instance_class of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The instance_class of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_class

    @instance_class.setter
    def instance_class(self, instance_class):
        """Sets the instance_class of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param instance_class: The instance_class of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._instance_class = instance_class

    @property
    def max_connections_per_shard(self):
        """Gets the max_connections_per_shard of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The max_connections_per_shard of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_connections_per_shard

    @max_connections_per_shard.setter
    def max_connections_per_shard(self, max_connections_per_shard):
        """Sets the max_connections_per_shard of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param max_connections_per_shard: The max_connections_per_shard of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._max_connections_per_shard = max_connections_per_shard

    @property
    def node_numbers(self):
        """Gets the node_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The node_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._node_numbers

    @node_numbers.setter
    def node_numbers(self, node_numbers):
        """Sets the node_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param node_numbers: The node_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: list[int]
        """

        self._node_numbers = node_numbers

    @property
    def shard_capacity_specs(self):
        """Gets the shard_capacity_specs of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The shard_capacity_specs of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: list[ShardCapacitySpecForDescribeDBInstanceSpecsOutput]
        """
        return self._shard_capacity_specs

    @shard_capacity_specs.setter
    def shard_capacity_specs(self, shard_capacity_specs):
        """Sets the shard_capacity_specs of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param shard_capacity_specs: The shard_capacity_specs of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: list[ShardCapacitySpecForDescribeDBInstanceSpecsOutput]
        """

        self._shard_capacity_specs = shard_capacity_specs

    @property
    def shard_numbers(self):
        """Gets the shard_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The shard_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._shard_numbers

    @shard_numbers.setter
    def shard_numbers(self, shard_numbers):
        """Sets the shard_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.


        :param shard_numbers: The shard_numbers of this InstanceSpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: list[int]
        """

        self._shard_numbers = shard_numbers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceSpecForDescribeDBInstanceSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceSpecForDescribeDBInstanceSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceSpecForDescribeDBInstanceSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
