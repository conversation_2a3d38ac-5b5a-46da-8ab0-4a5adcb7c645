# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SessionDataForStatStreamOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'online_user': 'int',
        'request': 'int',
        'timestamp': 'int'
    }

    attribute_map = {
        'online_user': 'OnlineUser',
        'request': 'Request',
        'timestamp': 'Timestamp'
    }

    def __init__(self, online_user=None, request=None, timestamp=None, _configuration=None):  # noqa: E501
        """SessionDataForStatStreamOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._online_user = None
        self._request = None
        self._timestamp = None
        self.discriminator = None

        if online_user is not None:
            self.online_user = online_user
        if request is not None:
            self.request = request
        if timestamp is not None:
            self.timestamp = timestamp

    @property
    def online_user(self):
        """Gets the online_user of this SessionDataForStatStreamOutput.  # noqa: E501


        :return: The online_user of this SessionDataForStatStreamOutput.  # noqa: E501
        :rtype: int
        """
        return self._online_user

    @online_user.setter
    def online_user(self, online_user):
        """Sets the online_user of this SessionDataForStatStreamOutput.


        :param online_user: The online_user of this SessionDataForStatStreamOutput.  # noqa: E501
        :type: int
        """

        self._online_user = online_user

    @property
    def request(self):
        """Gets the request of this SessionDataForStatStreamOutput.  # noqa: E501


        :return: The request of this SessionDataForStatStreamOutput.  # noqa: E501
        :rtype: int
        """
        return self._request

    @request.setter
    def request(self, request):
        """Sets the request of this SessionDataForStatStreamOutput.


        :param request: The request of this SessionDataForStatStreamOutput.  # noqa: E501
        :type: int
        """

        self._request = request

    @property
    def timestamp(self):
        """Gets the timestamp of this SessionDataForStatStreamOutput.  # noqa: E501


        :return: The timestamp of this SessionDataForStatStreamOutput.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this SessionDataForStatStreamOutput.


        :param timestamp: The timestamp of this SessionDataForStatStreamOutput.  # noqa: E501
        :type: int
        """

        self._timestamp = timestamp

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SessionDataForStatStreamOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SessionDataForStatStreamOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SessionDataForStatStreamOutput):
            return True

        return self.to_dict() != other.to_dict()
