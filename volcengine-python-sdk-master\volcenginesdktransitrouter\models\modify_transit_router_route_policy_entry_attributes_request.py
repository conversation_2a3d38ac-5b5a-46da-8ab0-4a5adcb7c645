# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTransitRouterRoutePolicyEntryAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_result': 'str',
        'apply_as_path_values': 'list[str]',
        'as_path_operate_mode': 'str',
        'description': 'str',
        'destination_resource_ids': 'list[str]',
        'destination_resource_types': 'list[str]',
        'ip_prefixes': 'list[str]',
        'priority': 'int',
        'source_resource_ids': 'list[str]',
        'source_resource_types': 'list[str]',
        'transit_router_route_policy_entry_id': 'str'
    }

    attribute_map = {
        'action_result': 'ActionResult',
        'apply_as_path_values': 'ApplyAsPathValues',
        'as_path_operate_mode': 'AsPathOperateMode',
        'description': 'Description',
        'destination_resource_ids': 'DestinationResourceIds',
        'destination_resource_types': 'DestinationResourceTypes',
        'ip_prefixes': 'IpPrefixes',
        'priority': 'Priority',
        'source_resource_ids': 'SourceResourceIds',
        'source_resource_types': 'SourceResourceTypes',
        'transit_router_route_policy_entry_id': 'TransitRouterRoutePolicyEntryId'
    }

    def __init__(self, action_result=None, apply_as_path_values=None, as_path_operate_mode=None, description=None, destination_resource_ids=None, destination_resource_types=None, ip_prefixes=None, priority=None, source_resource_ids=None, source_resource_types=None, transit_router_route_policy_entry_id=None, _configuration=None):  # noqa: E501
        """ModifyTransitRouterRoutePolicyEntryAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_result = None
        self._apply_as_path_values = None
        self._as_path_operate_mode = None
        self._description = None
        self._destination_resource_ids = None
        self._destination_resource_types = None
        self._ip_prefixes = None
        self._priority = None
        self._source_resource_ids = None
        self._source_resource_types = None
        self._transit_router_route_policy_entry_id = None
        self.discriminator = None

        if action_result is not None:
            self.action_result = action_result
        if apply_as_path_values is not None:
            self.apply_as_path_values = apply_as_path_values
        if as_path_operate_mode is not None:
            self.as_path_operate_mode = as_path_operate_mode
        if description is not None:
            self.description = description
        if destination_resource_ids is not None:
            self.destination_resource_ids = destination_resource_ids
        if destination_resource_types is not None:
            self.destination_resource_types = destination_resource_types
        if ip_prefixes is not None:
            self.ip_prefixes = ip_prefixes
        if priority is not None:
            self.priority = priority
        if source_resource_ids is not None:
            self.source_resource_ids = source_resource_ids
        if source_resource_types is not None:
            self.source_resource_types = source_resource_types
        self.transit_router_route_policy_entry_id = transit_router_route_policy_entry_id

    @property
    def action_result(self):
        """Gets the action_result of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The action_result of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._action_result

    @action_result.setter
    def action_result(self, action_result):
        """Sets the action_result of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param action_result: The action_result of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._action_result = action_result

    @property
    def apply_as_path_values(self):
        """Gets the apply_as_path_values of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The apply_as_path_values of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._apply_as_path_values

    @apply_as_path_values.setter
    def apply_as_path_values(self, apply_as_path_values):
        """Sets the apply_as_path_values of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param apply_as_path_values: The apply_as_path_values of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._apply_as_path_values = apply_as_path_values

    @property
    def as_path_operate_mode(self):
        """Gets the as_path_operate_mode of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The as_path_operate_mode of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._as_path_operate_mode

    @as_path_operate_mode.setter
    def as_path_operate_mode(self, as_path_operate_mode):
        """Sets the as_path_operate_mode of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param as_path_operate_mode: The as_path_operate_mode of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._as_path_operate_mode = as_path_operate_mode

    @property
    def description(self):
        """Gets the description of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The description of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param description: The description of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_resource_ids(self):
        """Gets the destination_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The destination_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_resource_ids

    @destination_resource_ids.setter
    def destination_resource_ids(self, destination_resource_ids):
        """Sets the destination_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param destination_resource_ids: The destination_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._destination_resource_ids = destination_resource_ids

    @property
    def destination_resource_types(self):
        """Gets the destination_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The destination_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_resource_types

    @destination_resource_types.setter
    def destination_resource_types(self, destination_resource_types):
        """Sets the destination_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param destination_resource_types: The destination_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._destination_resource_types = destination_resource_types

    @property
    def ip_prefixes(self):
        """Gets the ip_prefixes of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The ip_prefixes of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_prefixes

    @ip_prefixes.setter
    def ip_prefixes(self, ip_prefixes):
        """Sets the ip_prefixes of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param ip_prefixes: The ip_prefixes of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_prefixes = ip_prefixes

    @property
    def priority(self):
        """Gets the priority of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The priority of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param priority: The priority of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def source_resource_ids(self):
        """Gets the source_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The source_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_resource_ids

    @source_resource_ids.setter
    def source_resource_ids(self, source_resource_ids):
        """Sets the source_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param source_resource_ids: The source_resource_ids of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._source_resource_ids = source_resource_ids

    @property
    def source_resource_types(self):
        """Gets the source_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The source_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_resource_types

    @source_resource_types.setter
    def source_resource_types(self, source_resource_types):
        """Sets the source_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param source_resource_types: The source_resource_types of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._source_resource_types = source_resource_types

    @property
    def transit_router_route_policy_entry_id(self):
        """Gets the transit_router_route_policy_entry_id of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501


        :return: The transit_router_route_policy_entry_id of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_entry_id

    @transit_router_route_policy_entry_id.setter
    def transit_router_route_policy_entry_id(self, transit_router_route_policy_entry_id):
        """Sets the transit_router_route_policy_entry_id of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.


        :param transit_router_route_policy_entry_id: The transit_router_route_policy_entry_id of this ModifyTransitRouterRoutePolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_route_policy_entry_id is None:
            raise ValueError("Invalid value for `transit_router_route_policy_entry_id`, must not be `None`")  # noqa: E501

        self._transit_router_route_policy_entry_id = transit_router_route_policy_entry_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTransitRouterRoutePolicyEntryAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTransitRouterRoutePolicyEntryAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTransitRouterRoutePolicyEntryAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
