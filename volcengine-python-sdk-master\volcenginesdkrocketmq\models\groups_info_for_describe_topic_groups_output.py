# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GroupsInfoForDescribeTopicGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group_id': 'str',
        'message_model': 'str',
        'sub_string': 'str'
    }

    attribute_map = {
        'group_id': 'GroupId',
        'message_model': 'MessageModel',
        'sub_string': 'SubString'
    }

    def __init__(self, group_id=None, message_model=None, sub_string=None, _configuration=None):  # noqa: E501
        """GroupsInfoForDescribeTopicGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group_id = None
        self._message_model = None
        self._sub_string = None
        self.discriminator = None

        if group_id is not None:
            self.group_id = group_id
        if message_model is not None:
            self.message_model = message_model
        if sub_string is not None:
            self.sub_string = sub_string

    @property
    def group_id(self):
        """Gets the group_id of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501


        :return: The group_id of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this GroupsInfoForDescribeTopicGroupsOutput.


        :param group_id: The group_id of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def message_model(self):
        """Gets the message_model of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501


        :return: The message_model of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message_model

    @message_model.setter
    def message_model(self, message_model):
        """Sets the message_model of this GroupsInfoForDescribeTopicGroupsOutput.


        :param message_model: The message_model of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :type: str
        """

        self._message_model = message_model

    @property
    def sub_string(self):
        """Gets the sub_string of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501


        :return: The sub_string of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_string

    @sub_string.setter
    def sub_string(self, sub_string):
        """Sets the sub_string of this GroupsInfoForDescribeTopicGroupsOutput.


        :param sub_string: The sub_string of this GroupsInfoForDescribeTopicGroupsOutput.  # noqa: E501
        :type: str
        """

        self._sub_string = sub_string

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GroupsInfoForDescribeTopicGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GroupsInfoForDescribeTopicGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GroupsInfoForDescribeTopicGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
