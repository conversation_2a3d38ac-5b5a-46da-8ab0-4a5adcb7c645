# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSnapshotsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'snapshot_ids': 'str',
        'snapshot_name': 'str',
        'snapshot_type': 'str',
        'status': 'str'
    }

    attribute_map = {
        'file_system_id': 'FileSystemId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'snapshot_ids': 'SnapshotIds',
        'snapshot_name': 'SnapshotName',
        'snapshot_type': 'SnapshotType',
        'status': 'Status'
    }

    def __init__(self, file_system_id=None, page_number=None, page_size=None, snapshot_ids=None, snapshot_name=None, snapshot_type=None, status=None, _configuration=None):  # noqa: E501
        """DescribeSnapshotsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_id = None
        self._page_number = None
        self._page_size = None
        self._snapshot_ids = None
        self._snapshot_name = None
        self._snapshot_type = None
        self._status = None
        self.discriminator = None

        if file_system_id is not None:
            self.file_system_id = file_system_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if snapshot_ids is not None:
            self.snapshot_ids = snapshot_ids
        if snapshot_name is not None:
            self.snapshot_name = snapshot_name
        if snapshot_type is not None:
            self.snapshot_type = snapshot_type
        if status is not None:
            self.status = status

    @property
    def file_system_id(self):
        """Gets the file_system_id of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The file_system_id of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this DescribeSnapshotsRequest.


        :param file_system_id: The file_system_id of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The page_number of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSnapshotsRequest.


        :param page_number: The page_number of this DescribeSnapshotsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The page_size of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSnapshotsRequest.


        :param page_size: The page_size of this DescribeSnapshotsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def snapshot_ids(self):
        """Gets the snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_ids

    @snapshot_ids.setter
    def snapshot_ids(self, snapshot_ids):
        """Sets the snapshot_ids of this DescribeSnapshotsRequest.


        :param snapshot_ids: The snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._snapshot_ids = snapshot_ids

    @property
    def snapshot_name(self):
        """Gets the snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_name

    @snapshot_name.setter
    def snapshot_name(self, snapshot_name):
        """Sets the snapshot_name of this DescribeSnapshotsRequest.


        :param snapshot_name: The snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._snapshot_name = snapshot_name

    @property
    def snapshot_type(self):
        """Gets the snapshot_type of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_type of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_type

    @snapshot_type.setter
    def snapshot_type(self, snapshot_type):
        """Sets the snapshot_type of this DescribeSnapshotsRequest.


        :param snapshot_type: The snapshot_type of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Manual", "Auto"]  # noqa: E501
        if (self._configuration.client_side_validation and
                snapshot_type not in allowed_values):
            raise ValueError(
                "Invalid value for `snapshot_type` ({0}), must be one of {1}"  # noqa: E501
                .format(snapshot_type, allowed_values)
            )

        self._snapshot_type = snapshot_type

    @property
    def status(self):
        """Gets the status of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The status of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeSnapshotsRequest.


        :param status: The status of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Progressing", "Accomplished", "Failed", "Deleting"]  # noqa: E501
        if (self._configuration.client_side_validation and
                status not in allowed_values):
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSnapshotsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSnapshotsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSnapshotsRequest):
            return True

        return self.to_dict() != other.to_dict()
