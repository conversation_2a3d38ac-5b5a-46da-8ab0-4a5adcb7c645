# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCustomLinesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip_segment': 'str',
        'line': 'str',
        'name_cn': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'remark': 'str'
    }

    attribute_map = {
        'ip_segment': 'IPSegment',
        'line': 'Line',
        'name_cn': 'NameCN',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'remark': 'Remark'
    }

    def __init__(self, ip_segment=None, line=None, name_cn=None, page_number=None, page_size=None, remark=None, _configuration=None):  # noqa: E501
        """ListCustomLinesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip_segment = None
        self._line = None
        self._name_cn = None
        self._page_number = None
        self._page_size = None
        self._remark = None
        self.discriminator = None

        if ip_segment is not None:
            self.ip_segment = ip_segment
        if line is not None:
            self.line = line
        if name_cn is not None:
            self.name_cn = name_cn
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if remark is not None:
            self.remark = remark

    @property
    def ip_segment(self):
        """Gets the ip_segment of this ListCustomLinesRequest.  # noqa: E501


        :return: The ip_segment of this ListCustomLinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_segment

    @ip_segment.setter
    def ip_segment(self, ip_segment):
        """Sets the ip_segment of this ListCustomLinesRequest.


        :param ip_segment: The ip_segment of this ListCustomLinesRequest.  # noqa: E501
        :type: str
        """

        self._ip_segment = ip_segment

    @property
    def line(self):
        """Gets the line of this ListCustomLinesRequest.  # noqa: E501


        :return: The line of this ListCustomLinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this ListCustomLinesRequest.


        :param line: The line of this ListCustomLinesRequest.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def name_cn(self):
        """Gets the name_cn of this ListCustomLinesRequest.  # noqa: E501


        :return: The name_cn of this ListCustomLinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name_cn

    @name_cn.setter
    def name_cn(self, name_cn):
        """Sets the name_cn of this ListCustomLinesRequest.


        :param name_cn: The name_cn of this ListCustomLinesRequest.  # noqa: E501
        :type: str
        """

        self._name_cn = name_cn

    @property
    def page_number(self):
        """Gets the page_number of this ListCustomLinesRequest.  # noqa: E501


        :return: The page_number of this ListCustomLinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListCustomLinesRequest.


        :param page_number: The page_number of this ListCustomLinesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListCustomLinesRequest.  # noqa: E501


        :return: The page_size of this ListCustomLinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCustomLinesRequest.


        :param page_size: The page_size of this ListCustomLinesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def remark(self):
        """Gets the remark of this ListCustomLinesRequest.  # noqa: E501


        :return: The remark of this ListCustomLinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this ListCustomLinesRequest.


        :param remark: The remark of this ListCustomLinesRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCustomLinesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCustomLinesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCustomLinesRequest):
            return True

        return self.to_dict() != other.to_dict()
