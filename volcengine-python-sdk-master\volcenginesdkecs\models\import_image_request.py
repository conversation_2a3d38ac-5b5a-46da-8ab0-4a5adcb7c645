# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImportImageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'architecture': 'str',
        'boot_mode': 'str',
        'description': 'str',
        'image_name': 'str',
        'import_data_volumes': 'list[ImportDataVolumeForImportImageInput]',
        'license_type': 'str',
        'need_detection': 'bool',
        'os_type': 'str',
        'platform': 'str',
        'platform_version': 'str',
        'project_name': 'str',
        'tags': 'list[TagForImportImageInput]',
        'url': 'str'
    }

    attribute_map = {
        'architecture': 'Architecture',
        'boot_mode': 'BootMode',
        'description': 'Description',
        'image_name': 'ImageName',
        'import_data_volumes': 'ImportDataVolumes',
        'license_type': 'LicenseType',
        'need_detection': 'NeedDetection',
        'os_type': 'OsType',
        'platform': 'Platform',
        'platform_version': 'PlatformVersion',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'url': 'Url'
    }

    def __init__(self, architecture=None, boot_mode=None, description=None, image_name=None, import_data_volumes=None, license_type=None, need_detection=None, os_type=None, platform=None, platform_version=None, project_name=None, tags=None, url=None, _configuration=None):  # noqa: E501
        """ImportImageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._architecture = None
        self._boot_mode = None
        self._description = None
        self._image_name = None
        self._import_data_volumes = None
        self._license_type = None
        self._need_detection = None
        self._os_type = None
        self._platform = None
        self._platform_version = None
        self._project_name = None
        self._tags = None
        self._url = None
        self.discriminator = None

        if architecture is not None:
            self.architecture = architecture
        if boot_mode is not None:
            self.boot_mode = boot_mode
        if description is not None:
            self.description = description
        self.image_name = image_name
        if import_data_volumes is not None:
            self.import_data_volumes = import_data_volumes
        if license_type is not None:
            self.license_type = license_type
        if need_detection is not None:
            self.need_detection = need_detection
        if os_type is not None:
            self.os_type = os_type
        self.platform = platform
        if platform_version is not None:
            self.platform_version = platform_version
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        self.url = url

    @property
    def architecture(self):
        """Gets the architecture of this ImportImageRequest.  # noqa: E501


        :return: The architecture of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._architecture

    @architecture.setter
    def architecture(self, architecture):
        """Sets the architecture of this ImportImageRequest.


        :param architecture: The architecture of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._architecture = architecture

    @property
    def boot_mode(self):
        """Gets the boot_mode of this ImportImageRequest.  # noqa: E501


        :return: The boot_mode of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._boot_mode

    @boot_mode.setter
    def boot_mode(self, boot_mode):
        """Sets the boot_mode of this ImportImageRequest.


        :param boot_mode: The boot_mode of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._boot_mode = boot_mode

    @property
    def description(self):
        """Gets the description of this ImportImageRequest.  # noqa: E501


        :return: The description of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ImportImageRequest.


        :param description: The description of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def image_name(self):
        """Gets the image_name of this ImportImageRequest.  # noqa: E501


        :return: The image_name of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this ImportImageRequest.


        :param image_name: The image_name of this ImportImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_name is None:
            raise ValueError("Invalid value for `image_name`, must not be `None`")  # noqa: E501

        self._image_name = image_name

    @property
    def import_data_volumes(self):
        """Gets the import_data_volumes of this ImportImageRequest.  # noqa: E501


        :return: The import_data_volumes of this ImportImageRequest.  # noqa: E501
        :rtype: list[ImportDataVolumeForImportImageInput]
        """
        return self._import_data_volumes

    @import_data_volumes.setter
    def import_data_volumes(self, import_data_volumes):
        """Sets the import_data_volumes of this ImportImageRequest.


        :param import_data_volumes: The import_data_volumes of this ImportImageRequest.  # noqa: E501
        :type: list[ImportDataVolumeForImportImageInput]
        """

        self._import_data_volumes = import_data_volumes

    @property
    def license_type(self):
        """Gets the license_type of this ImportImageRequest.  # noqa: E501


        :return: The license_type of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._license_type

    @license_type.setter
    def license_type(self, license_type):
        """Sets the license_type of this ImportImageRequest.


        :param license_type: The license_type of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._license_type = license_type

    @property
    def need_detection(self):
        """Gets the need_detection of this ImportImageRequest.  # noqa: E501


        :return: The need_detection of this ImportImageRequest.  # noqa: E501
        :rtype: bool
        """
        return self._need_detection

    @need_detection.setter
    def need_detection(self, need_detection):
        """Sets the need_detection of this ImportImageRequest.


        :param need_detection: The need_detection of this ImportImageRequest.  # noqa: E501
        :type: bool
        """

        self._need_detection = need_detection

    @property
    def os_type(self):
        """Gets the os_type of this ImportImageRequest.  # noqa: E501


        :return: The os_type of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._os_type

    @os_type.setter
    def os_type(self, os_type):
        """Sets the os_type of this ImportImageRequest.


        :param os_type: The os_type of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._os_type = os_type

    @property
    def platform(self):
        """Gets the platform of this ImportImageRequest.  # noqa: E501


        :return: The platform of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this ImportImageRequest.


        :param platform: The platform of this ImportImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and platform is None:
            raise ValueError("Invalid value for `platform`, must not be `None`")  # noqa: E501

        self._platform = platform

    @property
    def platform_version(self):
        """Gets the platform_version of this ImportImageRequest.  # noqa: E501


        :return: The platform_version of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._platform_version

    @platform_version.setter
    def platform_version(self, platform_version):
        """Sets the platform_version of this ImportImageRequest.


        :param platform_version: The platform_version of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._platform_version = platform_version

    @property
    def project_name(self):
        """Gets the project_name of this ImportImageRequest.  # noqa: E501


        :return: The project_name of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ImportImageRequest.


        :param project_name: The project_name of this ImportImageRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this ImportImageRequest.  # noqa: E501


        :return: The tags of this ImportImageRequest.  # noqa: E501
        :rtype: list[TagForImportImageInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ImportImageRequest.


        :param tags: The tags of this ImportImageRequest.  # noqa: E501
        :type: list[TagForImportImageInput]
        """

        self._tags = tags

    @property
    def url(self):
        """Gets the url of this ImportImageRequest.  # noqa: E501


        :return: The url of this ImportImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ImportImageRequest.


        :param url: The url of this ImportImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and url is None:
            raise ValueError("Invalid value for `url`, must not be `None`")  # noqa: E501

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImportImageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImportImageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImportImageRequest):
            return True

        return self.to_dict() != other.to_dict()
