# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVolumeTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter': 'str',
        'order_by': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'resource_class': 'str',
        'volume_type_ids': 'list[str]',
        'zone_id': 'str'
    }

    attribute_map = {
        'filter': 'Filter',
        'order_by': 'OrderBy',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'resource_class': 'ResourceClass',
        'volume_type_ids': 'VolumeTypeIds',
        'zone_id': 'ZoneId'
    }

    def __init__(self, filter=None, order_by=None, page_number=None, page_size=None, resource_class=None, volume_type_ids=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeVolumeTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter = None
        self._order_by = None
        self._page_number = None
        self._page_size = None
        self._resource_class = None
        self._volume_type_ids = None
        self._zone_id = None
        self.discriminator = None

        if filter is not None:
            self.filter = filter
        if order_by is not None:
            self.order_by = order_by
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if resource_class is not None:
            self.resource_class = resource_class
        if volume_type_ids is not None:
            self.volume_type_ids = volume_type_ids
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def filter(self):
        """Gets the filter of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The filter of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this DescribeVolumeTypeRequest.


        :param filter: The filter of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: str
        """

        self._filter = filter

    @property
    def order_by(self):
        """Gets the order_by of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The order_by of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this DescribeVolumeTypeRequest.


        :param order_by: The order_by of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: str
        """

        self._order_by = order_by

    @property
    def page_number(self):
        """Gets the page_number of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The page_number of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeVolumeTypeRequest.


        :param page_number: The page_number of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The page_size of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeVolumeTypeRequest.


        :param page_size: The page_size of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def resource_class(self):
        """Gets the resource_class of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The resource_class of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_class

    @resource_class.setter
    def resource_class(self, resource_class):
        """Sets the resource_class of this DescribeVolumeTypeRequest.


        :param resource_class: The resource_class of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: str
        """

        self._resource_class = resource_class

    @property
    def volume_type_ids(self):
        """Gets the volume_type_ids of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The volume_type_ids of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._volume_type_ids

    @volume_type_ids.setter
    def volume_type_ids(self, volume_type_ids):
        """Sets the volume_type_ids of this DescribeVolumeTypeRequest.


        :param volume_type_ids: The volume_type_ids of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: list[str]
        """

        self._volume_type_ids = volume_type_ids

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeVolumeTypeRequest.  # noqa: E501


        :return: The zone_id of this DescribeVolumeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeVolumeTypeRequest.


        :param zone_id: The zone_id of this DescribeVolumeTypeRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVolumeTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVolumeTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVolumeTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
