# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateActivityQuizConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'deadline': 'int',
        'id': 'int',
        'quiz_analysis': 'str',
        'quiz_options': 'list[QuizOptionForUpdateActivityQuizConfigInput]',
        'quiz_result_type': 'int',
        'quiz_title': 'str',
        'quiz_title_type': 'int',
        'quiz_type': 'int',
        'right_options': 'list[str]'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'deadline': 'Deadline',
        'id': 'Id',
        'quiz_analysis': 'QuizAnalysis',
        'quiz_options': 'QuizOptions',
        'quiz_result_type': 'QuizResultType',
        'quiz_title': 'QuizTitle',
        'quiz_title_type': 'QuizTitleType',
        'quiz_type': 'QuizType',
        'right_options': 'RightOptions'
    }

    def __init__(self, activity_id=None, deadline=None, id=None, quiz_analysis=None, quiz_options=None, quiz_result_type=None, quiz_title=None, quiz_title_type=None, quiz_type=None, right_options=None, _configuration=None):  # noqa: E501
        """UpdateActivityQuizConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._deadline = None
        self._id = None
        self._quiz_analysis = None
        self._quiz_options = None
        self._quiz_result_type = None
        self._quiz_title = None
        self._quiz_title_type = None
        self._quiz_type = None
        self._right_options = None
        self.discriminator = None

        self.activity_id = activity_id
        self.deadline = deadline
        self.id = id
        if quiz_analysis is not None:
            self.quiz_analysis = quiz_analysis
        if quiz_options is not None:
            self.quiz_options = quiz_options
        self.quiz_result_type = quiz_result_type
        if quiz_title is not None:
            self.quiz_title = quiz_title
        self.quiz_title_type = quiz_title_type
        self.quiz_type = quiz_type
        if right_options is not None:
            self.right_options = right_options

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The activity_id of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateActivityQuizConfigRequest.


        :param activity_id: The activity_id of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def deadline(self):
        """Gets the deadline of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The deadline of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._deadline

    @deadline.setter
    def deadline(self, deadline):
        """Sets the deadline of this UpdateActivityQuizConfigRequest.


        :param deadline: The deadline of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and deadline is None:
            raise ValueError("Invalid value for `deadline`, must not be `None`")  # noqa: E501

        self._deadline = deadline

    @property
    def id(self):
        """Gets the id of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The id of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateActivityQuizConfigRequest.


        :param id: The id of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def quiz_analysis(self):
        """Gets the quiz_analysis of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_analysis of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._quiz_analysis

    @quiz_analysis.setter
    def quiz_analysis(self, quiz_analysis):
        """Sets the quiz_analysis of this UpdateActivityQuizConfigRequest.


        :param quiz_analysis: The quiz_analysis of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: str
        """

        self._quiz_analysis = quiz_analysis

    @property
    def quiz_options(self):
        """Gets the quiz_options of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_options of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: list[QuizOptionForUpdateActivityQuizConfigInput]
        """
        return self._quiz_options

    @quiz_options.setter
    def quiz_options(self, quiz_options):
        """Sets the quiz_options of this UpdateActivityQuizConfigRequest.


        :param quiz_options: The quiz_options of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: list[QuizOptionForUpdateActivityQuizConfigInput]
        """

        self._quiz_options = quiz_options

    @property
    def quiz_result_type(self):
        """Gets the quiz_result_type of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_result_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._quiz_result_type

    @quiz_result_type.setter
    def quiz_result_type(self, quiz_result_type):
        """Sets the quiz_result_type of this UpdateActivityQuizConfigRequest.


        :param quiz_result_type: The quiz_result_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and quiz_result_type is None:
            raise ValueError("Invalid value for `quiz_result_type`, must not be `None`")  # noqa: E501

        self._quiz_result_type = quiz_result_type

    @property
    def quiz_title(self):
        """Gets the quiz_title of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_title of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._quiz_title

    @quiz_title.setter
    def quiz_title(self, quiz_title):
        """Sets the quiz_title of this UpdateActivityQuizConfigRequest.


        :param quiz_title: The quiz_title of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: str
        """

        self._quiz_title = quiz_title

    @property
    def quiz_title_type(self):
        """Gets the quiz_title_type of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_title_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._quiz_title_type

    @quiz_title_type.setter
    def quiz_title_type(self, quiz_title_type):
        """Sets the quiz_title_type of this UpdateActivityQuizConfigRequest.


        :param quiz_title_type: The quiz_title_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and quiz_title_type is None:
            raise ValueError("Invalid value for `quiz_title_type`, must not be `None`")  # noqa: E501

        self._quiz_title_type = quiz_title_type

    @property
    def quiz_type(self):
        """Gets the quiz_type of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The quiz_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._quiz_type

    @quiz_type.setter
    def quiz_type(self, quiz_type):
        """Sets the quiz_type of this UpdateActivityQuizConfigRequest.


        :param quiz_type: The quiz_type of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and quiz_type is None:
            raise ValueError("Invalid value for `quiz_type`, must not be `None`")  # noqa: E501

        self._quiz_type = quiz_type

    @property
    def right_options(self):
        """Gets the right_options of this UpdateActivityQuizConfigRequest.  # noqa: E501


        :return: The right_options of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._right_options

    @right_options.setter
    def right_options(self, right_options):
        """Sets the right_options of this UpdateActivityQuizConfigRequest.


        :param right_options: The right_options of this UpdateActivityQuizConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._right_options = right_options

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateActivityQuizConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateActivityQuizConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateActivityQuizConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
