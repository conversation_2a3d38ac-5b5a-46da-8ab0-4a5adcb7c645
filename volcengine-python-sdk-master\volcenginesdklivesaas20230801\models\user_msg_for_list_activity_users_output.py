# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserMsgForListActivityUsersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audience_group_id': 'int',
        'external_user_id': 'str',
        'ip': 'str',
        'is_banned_ip': 'int',
        'is_banned_user': 'int',
        'is_presenter': 'int',
        'mute_number': 'int',
        'no_interact_number': 'int',
        'online_status': 'int',
        'shift_screen_number': 'int',
        'silence_status': 'int',
        'user_cookie': 'int',
        'user_credit': 'int',
        'user_id': 'int',
        'user_name': 'str'
    }

    attribute_map = {
        'audience_group_id': 'AudienceGroupId',
        'external_user_id': 'ExternalUserId',
        'ip': 'IP',
        'is_banned_ip': 'IsBannedIP',
        'is_banned_user': 'IsBannedUser',
        'is_presenter': 'IsPresenter',
        'mute_number': 'MuteNumber',
        'no_interact_number': 'NoInteractNumber',
        'online_status': 'OnlineStatus',
        'shift_screen_number': 'ShiftScreenNumber',
        'silence_status': 'SilenceStatus',
        'user_cookie': 'UserCookie',
        'user_credit': 'UserCredit',
        'user_id': 'UserId',
        'user_name': 'UserName'
    }

    def __init__(self, audience_group_id=None, external_user_id=None, ip=None, is_banned_ip=None, is_banned_user=None, is_presenter=None, mute_number=None, no_interact_number=None, online_status=None, shift_screen_number=None, silence_status=None, user_cookie=None, user_credit=None, user_id=None, user_name=None, _configuration=None):  # noqa: E501
        """UserMsgForListActivityUsersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audience_group_id = None
        self._external_user_id = None
        self._ip = None
        self._is_banned_ip = None
        self._is_banned_user = None
        self._is_presenter = None
        self._mute_number = None
        self._no_interact_number = None
        self._online_status = None
        self._shift_screen_number = None
        self._silence_status = None
        self._user_cookie = None
        self._user_credit = None
        self._user_id = None
        self._user_name = None
        self.discriminator = None

        if audience_group_id is not None:
            self.audience_group_id = audience_group_id
        if external_user_id is not None:
            self.external_user_id = external_user_id
        if ip is not None:
            self.ip = ip
        if is_banned_ip is not None:
            self.is_banned_ip = is_banned_ip
        if is_banned_user is not None:
            self.is_banned_user = is_banned_user
        if is_presenter is not None:
            self.is_presenter = is_presenter
        if mute_number is not None:
            self.mute_number = mute_number
        if no_interact_number is not None:
            self.no_interact_number = no_interact_number
        if online_status is not None:
            self.online_status = online_status
        if shift_screen_number is not None:
            self.shift_screen_number = shift_screen_number
        if silence_status is not None:
            self.silence_status = silence_status
        if user_cookie is not None:
            self.user_cookie = user_cookie
        if user_credit is not None:
            self.user_credit = user_credit
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name

    @property
    def audience_group_id(self):
        """Gets the audience_group_id of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The audience_group_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._audience_group_id

    @audience_group_id.setter
    def audience_group_id(self, audience_group_id):
        """Sets the audience_group_id of this UserMsgForListActivityUsersOutput.


        :param audience_group_id: The audience_group_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._audience_group_id = audience_group_id

    @property
    def external_user_id(self):
        """Gets the external_user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The external_user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this UserMsgForListActivityUsersOutput.


        :param external_user_id: The external_user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def ip(self):
        """Gets the ip of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The ip of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this UserMsgForListActivityUsersOutput.


        :param ip: The ip of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_banned_ip(self):
        """Gets the is_banned_ip of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The is_banned_ip of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_banned_ip

    @is_banned_ip.setter
    def is_banned_ip(self, is_banned_ip):
        """Sets the is_banned_ip of this UserMsgForListActivityUsersOutput.


        :param is_banned_ip: The is_banned_ip of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._is_banned_ip = is_banned_ip

    @property
    def is_banned_user(self):
        """Gets the is_banned_user of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The is_banned_user of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_banned_user

    @is_banned_user.setter
    def is_banned_user(self, is_banned_user):
        """Sets the is_banned_user of this UserMsgForListActivityUsersOutput.


        :param is_banned_user: The is_banned_user of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._is_banned_user = is_banned_user

    @property
    def is_presenter(self):
        """Gets the is_presenter of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The is_presenter of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_presenter

    @is_presenter.setter
    def is_presenter(self, is_presenter):
        """Sets the is_presenter of this UserMsgForListActivityUsersOutput.


        :param is_presenter: The is_presenter of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._is_presenter = is_presenter

    @property
    def mute_number(self):
        """Gets the mute_number of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The mute_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._mute_number

    @mute_number.setter
    def mute_number(self, mute_number):
        """Sets the mute_number of this UserMsgForListActivityUsersOutput.


        :param mute_number: The mute_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._mute_number = mute_number

    @property
    def no_interact_number(self):
        """Gets the no_interact_number of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The no_interact_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._no_interact_number

    @no_interact_number.setter
    def no_interact_number(self, no_interact_number):
        """Sets the no_interact_number of this UserMsgForListActivityUsersOutput.


        :param no_interact_number: The no_interact_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._no_interact_number = no_interact_number

    @property
    def online_status(self):
        """Gets the online_status of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The online_status of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._online_status

    @online_status.setter
    def online_status(self, online_status):
        """Sets the online_status of this UserMsgForListActivityUsersOutput.


        :param online_status: The online_status of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._online_status = online_status

    @property
    def shift_screen_number(self):
        """Gets the shift_screen_number of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The shift_screen_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._shift_screen_number

    @shift_screen_number.setter
    def shift_screen_number(self, shift_screen_number):
        """Sets the shift_screen_number of this UserMsgForListActivityUsersOutput.


        :param shift_screen_number: The shift_screen_number of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._shift_screen_number = shift_screen_number

    @property
    def silence_status(self):
        """Gets the silence_status of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The silence_status of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._silence_status

    @silence_status.setter
    def silence_status(self, silence_status):
        """Sets the silence_status of this UserMsgForListActivityUsersOutput.


        :param silence_status: The silence_status of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._silence_status = silence_status

    @property
    def user_cookie(self):
        """Gets the user_cookie of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The user_cookie of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_cookie

    @user_cookie.setter
    def user_cookie(self, user_cookie):
        """Sets the user_cookie of this UserMsgForListActivityUsersOutput.


        :param user_cookie: The user_cookie of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._user_cookie = user_cookie

    @property
    def user_credit(self):
        """Gets the user_credit of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The user_credit of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_credit

    @user_credit.setter
    def user_credit(self, user_credit):
        """Sets the user_credit of this UserMsgForListActivityUsersOutput.


        :param user_credit: The user_credit of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._user_credit = user_credit

    @property
    def user_id(self):
        """Gets the user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UserMsgForListActivityUsersOutput.


        :param user_id: The user_id of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this UserMsgForListActivityUsersOutput.  # noqa: E501


        :return: The user_name of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this UserMsgForListActivityUsersOutput.


        :param user_name: The user_name of this UserMsgForListActivityUsersOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserMsgForListActivityUsersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserMsgForListActivityUsersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserMsgForListActivityUsersOutput):
            return True

        return self.to_dict() != other.to_dict()
