# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ManualTriggerInspectRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'inspect_name': 'str',
        'instance_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'inspect_name': 'InspectName',
        'instance_id': 'InstanceId',
        'type': 'Type'
    }

    def __init__(self, inspect_name=None, instance_id=None, type=None, _configuration=None):  # noqa: E501
        """ManualTriggerInspectRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._inspect_name = None
        self._instance_id = None
        self._type = None
        self.discriminator = None

        self.inspect_name = inspect_name
        self.instance_id = instance_id
        self.type = type

    @property
    def inspect_name(self):
        """Gets the inspect_name of this ManualTriggerInspectRequest.  # noqa: E501


        :return: The inspect_name of this ManualTriggerInspectRequest.  # noqa: E501
        :rtype: str
        """
        return self._inspect_name

    @inspect_name.setter
    def inspect_name(self, inspect_name):
        """Sets the inspect_name of this ManualTriggerInspectRequest.


        :param inspect_name: The inspect_name of this ManualTriggerInspectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and inspect_name is None:
            raise ValueError("Invalid value for `inspect_name`, must not be `None`")  # noqa: E501

        self._inspect_name = inspect_name

    @property
    def instance_id(self):
        """Gets the instance_id of this ManualTriggerInspectRequest.  # noqa: E501


        :return: The instance_id of this ManualTriggerInspectRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ManualTriggerInspectRequest.


        :param instance_id: The instance_id of this ManualTriggerInspectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def type(self):
        """Gets the type of this ManualTriggerInspectRequest.  # noqa: E501


        :return: The type of this ManualTriggerInspectRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ManualTriggerInspectRequest.


        :param type: The type of this ManualTriggerInspectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ManualTriggerInspectRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ManualTriggerInspectRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ManualTriggerInspectRequest):
            return True

        return self.to_dict() != other.to_dict()
