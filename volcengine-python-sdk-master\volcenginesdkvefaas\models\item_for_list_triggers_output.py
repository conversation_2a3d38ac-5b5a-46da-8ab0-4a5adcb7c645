# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListTriggersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'creation_time': 'str',
        'description': 'str',
        'detailed_config': 'str',
        'enabled': 'bool',
        'function_id': 'str',
        'id': 'str',
        'image_version': 'str',
        'last_update_time': 'str',
        'name': 'str',
        'type': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'detailed_config': 'DetailedConfig',
        'enabled': 'Enabled',
        'function_id': 'FunctionID',
        'id': 'Id',
        'image_version': 'ImageVersion',
        'last_update_time': 'LastUpdateTime',
        'name': 'Name',
        'type': 'Type'
    }

    def __init__(self, account_id=None, creation_time=None, description=None, detailed_config=None, enabled=None, function_id=None, id=None, image_version=None, last_update_time=None, name=None, type=None, _configuration=None):  # noqa: E501
        """ItemForListTriggersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._creation_time = None
        self._description = None
        self._detailed_config = None
        self._enabled = None
        self._function_id = None
        self._id = None
        self._image_version = None
        self._last_update_time = None
        self._name = None
        self._type = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if detailed_config is not None:
            self.detailed_config = detailed_config
        if enabled is not None:
            self.enabled = enabled
        if function_id is not None:
            self.function_id = function_id
        if id is not None:
            self.id = id
        if image_version is not None:
            self.image_version = image_version
        if last_update_time is not None:
            self.last_update_time = last_update_time
        if name is not None:
            self.name = name
        if type is not None:
            self.type = type

    @property
    def account_id(self):
        """Gets the account_id of this ItemForListTriggersOutput.  # noqa: E501


        :return: The account_id of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ItemForListTriggersOutput.


        :param account_id: The account_id of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def creation_time(self):
        """Gets the creation_time of this ItemForListTriggersOutput.  # noqa: E501


        :return: The creation_time of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this ItemForListTriggersOutput.


        :param creation_time: The creation_time of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this ItemForListTriggersOutput.  # noqa: E501


        :return: The description of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListTriggersOutput.


        :param description: The description of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def detailed_config(self):
        """Gets the detailed_config of this ItemForListTriggersOutput.  # noqa: E501


        :return: The detailed_config of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._detailed_config

    @detailed_config.setter
    def detailed_config(self, detailed_config):
        """Sets the detailed_config of this ItemForListTriggersOutput.


        :param detailed_config: The detailed_config of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._detailed_config = detailed_config

    @property
    def enabled(self):
        """Gets the enabled of this ItemForListTriggersOutput.  # noqa: E501


        :return: The enabled of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this ItemForListTriggersOutput.


        :param enabled: The enabled of this ItemForListTriggersOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def function_id(self):
        """Gets the function_id of this ItemForListTriggersOutput.  # noqa: E501


        :return: The function_id of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this ItemForListTriggersOutput.


        :param function_id: The function_id of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._function_id = function_id

    @property
    def id(self):
        """Gets the id of this ItemForListTriggersOutput.  # noqa: E501


        :return: The id of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListTriggersOutput.


        :param id: The id of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def image_version(self):
        """Gets the image_version of this ItemForListTriggersOutput.  # noqa: E501


        :return: The image_version of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_version

    @image_version.setter
    def image_version(self, image_version):
        """Sets the image_version of this ItemForListTriggersOutput.


        :param image_version: The image_version of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._image_version = image_version

    @property
    def last_update_time(self):
        """Gets the last_update_time of this ItemForListTriggersOutput.  # noqa: E501


        :return: The last_update_time of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_update_time

    @last_update_time.setter
    def last_update_time(self, last_update_time):
        """Sets the last_update_time of this ItemForListTriggersOutput.


        :param last_update_time: The last_update_time of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._last_update_time = last_update_time

    @property
    def name(self):
        """Gets the name of this ItemForListTriggersOutput.  # noqa: E501


        :return: The name of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListTriggersOutput.


        :param name: The name of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def type(self):
        """Gets the type of this ItemForListTriggersOutput.  # noqa: E501


        :return: The type of this ItemForListTriggersOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListTriggersOutput.


        :param type: The type of this ItemForListTriggersOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListTriggersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListTriggersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListTriggersOutput):
            return True

        return self.to_dict() != other.to_dict()
