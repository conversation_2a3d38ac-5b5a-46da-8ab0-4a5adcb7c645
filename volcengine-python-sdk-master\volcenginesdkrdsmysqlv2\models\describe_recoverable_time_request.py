# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeRecoverableTimeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_region': 'str',
        'instance_id': 'str',
        'restore_type': 'str'
    }

    attribute_map = {
        'backup_region': 'BackupRegion',
        'instance_id': 'InstanceId',
        'restore_type': 'RestoreType'
    }

    def __init__(self, backup_region=None, instance_id=None, restore_type=None, _configuration=None):  # noqa: E501
        """DescribeRecoverableTimeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_region = None
        self._instance_id = None
        self._restore_type = None
        self.discriminator = None

        if backup_region is not None:
            self.backup_region = backup_region
        self.instance_id = instance_id
        if restore_type is not None:
            self.restore_type = restore_type

    @property
    def backup_region(self):
        """Gets the backup_region of this DescribeRecoverableTimeRequest.  # noqa: E501


        :return: The backup_region of this DescribeRecoverableTimeRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_region

    @backup_region.setter
    def backup_region(self, backup_region):
        """Sets the backup_region of this DescribeRecoverableTimeRequest.


        :param backup_region: The backup_region of this DescribeRecoverableTimeRequest.  # noqa: E501
        :type: str
        """

        self._backup_region = backup_region

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeRecoverableTimeRequest.  # noqa: E501


        :return: The instance_id of this DescribeRecoverableTimeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeRecoverableTimeRequest.


        :param instance_id: The instance_id of this DescribeRecoverableTimeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def restore_type(self):
        """Gets the restore_type of this DescribeRecoverableTimeRequest.  # noqa: E501


        :return: The restore_type of this DescribeRecoverableTimeRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_type

    @restore_type.setter
    def restore_type(self, restore_type):
        """Sets the restore_type of this DescribeRecoverableTimeRequest.


        :param restore_type: The restore_type of this DescribeRecoverableTimeRequest.  # noqa: E501
        :type: str
        """

        self._restore_type = restore_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeRecoverableTimeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeRecoverableTimeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeRecoverableTimeRequest):
            return True

        return self.to_dict() != other.to_dict()
