# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CommentForAddInteractionScriptCommentsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'nickname': 'str',
        'role': 'int',
        'send_time': 'int'
    }

    attribute_map = {
        'content': 'Content',
        'nickname': 'Nickname',
        'role': 'Role',
        'send_time': 'SendTime'
    }

    def __init__(self, content=None, nickname=None, role=None, send_time=None, _configuration=None):  # noqa: E501
        """CommentForAddInteractionScriptCommentsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._nickname = None
        self._role = None
        self._send_time = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if nickname is not None:
            self.nickname = nickname
        if role is not None:
            self.role = role
        if send_time is not None:
            self.send_time = send_time

    @property
    def content(self):
        """Gets the content of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501


        :return: The content of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this CommentForAddInteractionScriptCommentsInput.


        :param content: The content of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def nickname(self):
        """Gets the nickname of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501


        :return: The nickname of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :rtype: str
        """
        return self._nickname

    @nickname.setter
    def nickname(self, nickname):
        """Sets the nickname of this CommentForAddInteractionScriptCommentsInput.


        :param nickname: The nickname of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :type: str
        """

        self._nickname = nickname

    @property
    def role(self):
        """Gets the role of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501


        :return: The role of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :rtype: int
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this CommentForAddInteractionScriptCommentsInput.


        :param role: The role of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :type: int
        """

        self._role = role

    @property
    def send_time(self):
        """Gets the send_time of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501


        :return: The send_time of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this CommentForAddInteractionScriptCommentsInput.


        :param send_time: The send_time of this CommentForAddInteractionScriptCommentsInput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CommentForAddInteractionScriptCommentsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CommentForAddInteractionScriptCommentsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CommentForAddInteractionScriptCommentsInput):
            return True

        return self.to_dict() != other.to_dict()
