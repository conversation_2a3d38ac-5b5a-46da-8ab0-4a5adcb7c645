# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetRenewalTypeResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'success_instance_list': 'list[SuccessInstanceListForSetRenewalTypeOutput]'
    }

    attribute_map = {
        'success_instance_list': 'SuccessInstanceList'
    }

    def __init__(self, success_instance_list=None, _configuration=None):  # noqa: E501
        """SetRenewalTypeResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._success_instance_list = None
        self.discriminator = None

        if success_instance_list is not None:
            self.success_instance_list = success_instance_list

    @property
    def success_instance_list(self):
        """Gets the success_instance_list of this SetRenewalTypeResponse.  # noqa: E501


        :return: The success_instance_list of this SetRenewalTypeResponse.  # noqa: E501
        :rtype: list[SuccessInstanceListForSetRenewalTypeOutput]
        """
        return self._success_instance_list

    @success_instance_list.setter
    def success_instance_list(self, success_instance_list):
        """Sets the success_instance_list of this SetRenewalTypeResponse.


        :param success_instance_list: The success_instance_list of this SetRenewalTypeResponse.  # noqa: E501
        :type: list[SuccessInstanceListForSetRenewalTypeOutput]
        """

        self._success_instance_list = success_instance_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetRenewalTypeResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetRenewalTypeResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetRenewalTypeResponse):
            return True

        return self.to_dict() != other.to_dict()
