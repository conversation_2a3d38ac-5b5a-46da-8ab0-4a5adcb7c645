# coding: utf-8

"""
    httpdns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddDomainRecordRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'domain': 'str',
        'dry_run': 'bool',
        'enable': 'bool',
        'line': 'str',
        'target': 'list[str]',
        'ttl': 'int',
        'type': 'str',
        'weights': 'list[WeightForAddDomainRecordInput]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'domain': 'Domain',
        'dry_run': 'DryRun',
        'enable': 'Enable',
        'line': 'Line',
        'target': 'Target',
        'ttl': 'Ttl',
        'type': 'Type',
        'weights': 'Weights'
    }

    def __init__(self, client_token=None, domain=None, dry_run=None, enable=None, line=None, target=None, ttl=None, type=None, weights=None, _configuration=None):  # noqa: E501
        """AddDomainRecordRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._domain = None
        self._dry_run = None
        self._enable = None
        self._line = None
        self._target = None
        self._ttl = None
        self._type = None
        self._weights = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.domain = domain
        if dry_run is not None:
            self.dry_run = dry_run
        self.enable = enable
        self.line = line
        if target is not None:
            self.target = target
        self.ttl = ttl
        self.type = type
        if weights is not None:
            self.weights = weights

    @property
    def client_token(self):
        """Gets the client_token of this AddDomainRecordRequest.  # noqa: E501


        :return: The client_token of this AddDomainRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this AddDomainRecordRequest.


        :param client_token: The client_token of this AddDomainRecordRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def domain(self):
        """Gets the domain of this AddDomainRecordRequest.  # noqa: E501


        :return: The domain of this AddDomainRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this AddDomainRecordRequest.


        :param domain: The domain of this AddDomainRecordRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def dry_run(self):
        """Gets the dry_run of this AddDomainRecordRequest.  # noqa: E501


        :return: The dry_run of this AddDomainRecordRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this AddDomainRecordRequest.


        :param dry_run: The dry_run of this AddDomainRecordRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def enable(self):
        """Gets the enable of this AddDomainRecordRequest.  # noqa: E501


        :return: The enable of this AddDomainRecordRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this AddDomainRecordRequest.


        :param enable: The enable of this AddDomainRecordRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable is None:
            raise ValueError("Invalid value for `enable`, must not be `None`")  # noqa: E501

        self._enable = enable

    @property
    def line(self):
        """Gets the line of this AddDomainRecordRequest.  # noqa: E501


        :return: The line of this AddDomainRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this AddDomainRecordRequest.


        :param line: The line of this AddDomainRecordRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and line is None:
            raise ValueError("Invalid value for `line`, must not be `None`")  # noqa: E501

        self._line = line

    @property
    def target(self):
        """Gets the target of this AddDomainRecordRequest.  # noqa: E501


        :return: The target of this AddDomainRecordRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this AddDomainRecordRequest.


        :param target: The target of this AddDomainRecordRequest.  # noqa: E501
        :type: list[str]
        """

        self._target = target

    @property
    def ttl(self):
        """Gets the ttl of this AddDomainRecordRequest.  # noqa: E501


        :return: The ttl of this AddDomainRecordRequest.  # noqa: E501
        :rtype: int
        """
        return self._ttl

    @ttl.setter
    def ttl(self, ttl):
        """Sets the ttl of this AddDomainRecordRequest.


        :param ttl: The ttl of this AddDomainRecordRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and ttl is None:
            raise ValueError("Invalid value for `ttl`, must not be `None`")  # noqa: E501

        self._ttl = ttl

    @property
    def type(self):
        """Gets the type of this AddDomainRecordRequest.  # noqa: E501


        :return: The type of this AddDomainRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this AddDomainRecordRequest.


        :param type: The type of this AddDomainRecordRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        allowed_values = ["A", "AAAA", "CNAME"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    @property
    def weights(self):
        """Gets the weights of this AddDomainRecordRequest.  # noqa: E501


        :return: The weights of this AddDomainRecordRequest.  # noqa: E501
        :rtype: list[WeightForAddDomainRecordInput]
        """
        return self._weights

    @weights.setter
    def weights(self, weights):
        """Sets the weights of this AddDomainRecordRequest.


        :param weights: The weights of this AddDomainRecordRequest.  # noqa: E501
        :type: list[WeightForAddDomainRecordInput]
        """

        self._weights = weights

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddDomainRecordRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddDomainRecordRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddDomainRecordRequest):
            return True

        return self.to_dict() != other.to_dict()
