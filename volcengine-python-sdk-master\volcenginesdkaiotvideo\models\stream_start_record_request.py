# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StreamStartRecordRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_pull': 'bool',
        'expire': 'int',
        'record_time': 'int',
        'remux': 'str',
        'space_id': 'str',
        'stream_id': 'str',
        'timeout': 'int'
    }

    attribute_map = {
        'auto_pull': 'AutoPull',
        'expire': 'Expire',
        'record_time': 'RecordTime',
        'remux': 'Remux',
        'space_id': 'SpaceID',
        'stream_id': 'StreamID',
        'timeout': 'Timeout'
    }

    def __init__(self, auto_pull=None, expire=None, record_time=None, remux=None, space_id=None, stream_id=None, timeout=None, _configuration=None):  # noqa: E501
        """StreamStartRecordRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_pull = None
        self._expire = None
        self._record_time = None
        self._remux = None
        self._space_id = None
        self._stream_id = None
        self._timeout = None
        self.discriminator = None

        self.auto_pull = auto_pull
        if expire is not None:
            self.expire = expire
        if record_time is not None:
            self.record_time = record_time
        if remux is not None:
            self.remux = remux
        if space_id is not None:
            self.space_id = space_id
        self.stream_id = stream_id
        self.timeout = timeout

    @property
    def auto_pull(self):
        """Gets the auto_pull of this StreamStartRecordRequest.  # noqa: E501


        :return: The auto_pull of this StreamStartRecordRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pull

    @auto_pull.setter
    def auto_pull(self, auto_pull):
        """Sets the auto_pull of this StreamStartRecordRequest.


        :param auto_pull: The auto_pull of this StreamStartRecordRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and auto_pull is None:
            raise ValueError("Invalid value for `auto_pull`, must not be `None`")  # noqa: E501

        self._auto_pull = auto_pull

    @property
    def expire(self):
        """Gets the expire of this StreamStartRecordRequest.  # noqa: E501


        :return: The expire of this StreamStartRecordRequest.  # noqa: E501
        :rtype: int
        """
        return self._expire

    @expire.setter
    def expire(self, expire):
        """Sets the expire of this StreamStartRecordRequest.


        :param expire: The expire of this StreamStartRecordRequest.  # noqa: E501
        :type: int
        """

        self._expire = expire

    @property
    def record_time(self):
        """Gets the record_time of this StreamStartRecordRequest.  # noqa: E501


        :return: The record_time of this StreamStartRecordRequest.  # noqa: E501
        :rtype: int
        """
        return self._record_time

    @record_time.setter
    def record_time(self, record_time):
        """Sets the record_time of this StreamStartRecordRequest.


        :param record_time: The record_time of this StreamStartRecordRequest.  # noqa: E501
        :type: int
        """

        self._record_time = record_time

    @property
    def remux(self):
        """Gets the remux of this StreamStartRecordRequest.  # noqa: E501


        :return: The remux of this StreamStartRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._remux

    @remux.setter
    def remux(self, remux):
        """Sets the remux of this StreamStartRecordRequest.


        :param remux: The remux of this StreamStartRecordRequest.  # noqa: E501
        :type: str
        """

        self._remux = remux

    @property
    def space_id(self):
        """Gets the space_id of this StreamStartRecordRequest.  # noqa: E501


        :return: The space_id of this StreamStartRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this StreamStartRecordRequest.


        :param space_id: The space_id of this StreamStartRecordRequest.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def stream_id(self):
        """Gets the stream_id of this StreamStartRecordRequest.  # noqa: E501


        :return: The stream_id of this StreamStartRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this StreamStartRecordRequest.


        :param stream_id: The stream_id of this StreamStartRecordRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_id is None:
            raise ValueError("Invalid value for `stream_id`, must not be `None`")  # noqa: E501

        self._stream_id = stream_id

    @property
    def timeout(self):
        """Gets the timeout of this StreamStartRecordRequest.  # noqa: E501


        :return: The timeout of this StreamStartRecordRequest.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this StreamStartRecordRequest.


        :param timeout: The timeout of this StreamStartRecordRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and timeout is None:
            raise ValueError("Invalid value for `timeout`, must not be `None`")  # noqa: E501

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StreamStartRecordRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StreamStartRecordRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StreamStartRecordRequest):
            return True

        return self.to_dict() != other.to_dict()
