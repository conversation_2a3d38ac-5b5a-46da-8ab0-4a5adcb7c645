# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListWeakPasswordCheckDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detect_status': 'str',
        'weak_password': 'list[WeakPasswordForListWeakPasswordCheckDetailOutput]'
    }

    attribute_map = {
        'detect_status': 'DetectStatus',
        'weak_password': 'WeakPassword'
    }

    def __init__(self, detect_status=None, weak_password=None, _configuration=None):  # noqa: E501
        """DataForListWeakPasswordCheckDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detect_status = None
        self._weak_password = None
        self.discriminator = None

        if detect_status is not None:
            self.detect_status = detect_status
        if weak_password is not None:
            self.weak_password = weak_password

    @property
    def detect_status(self):
        """Gets the detect_status of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The detect_status of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_status

    @detect_status.setter
    def detect_status(self, detect_status):
        """Sets the detect_status of this DataForListWeakPasswordCheckDetailOutput.


        :param detect_status: The detect_status of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._detect_status = detect_status

    @property
    def weak_password(self):
        """Gets the weak_password of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The weak_password of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: list[WeakPasswordForListWeakPasswordCheckDetailOutput]
        """
        return self._weak_password

    @weak_password.setter
    def weak_password(self, weak_password):
        """Sets the weak_password of this DataForListWeakPasswordCheckDetailOutput.


        :param weak_password: The weak_password of this DataForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: list[WeakPasswordForListWeakPasswordCheckDetailOutput]
        """

        self._weak_password = weak_password

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListWeakPasswordCheckDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListWeakPasswordCheckDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListWeakPasswordCheckDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
