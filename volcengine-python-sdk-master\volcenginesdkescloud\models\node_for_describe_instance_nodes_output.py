# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeForDescribeInstanceNodesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'is_cold': 'bool',
        'is_coordinator': 'bool',
        'is_hot': 'bool',
        'is_kibana': 'bool',
        'is_master': 'bool',
        'is_warm': 'bool',
        'node_display_name': 'str',
        'node_name': 'str',
        'resource_spec': 'ResourceSpecForDescribeInstanceNodesOutput',
        'restart_number': 'int',
        'start_time': 'str',
        'status': 'str',
        'storage_spec': 'StorageSpecForDescribeInstanceNodesOutput'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'is_cold': 'IsCold',
        'is_coordinator': 'IsCoordinator',
        'is_hot': 'IsHot',
        'is_kibana': 'IsKibana',
        'is_master': 'IsMaster',
        'is_warm': 'IsWarm',
        'node_display_name': 'NodeDisplayName',
        'node_name': 'NodeName',
        'resource_spec': 'ResourceSpec',
        'restart_number': 'RestartNumber',
        'start_time': 'StartTime',
        'status': 'Status',
        'storage_spec': 'StorageSpec'
    }

    def __init__(self, instance_id=None, is_cold=None, is_coordinator=None, is_hot=None, is_kibana=None, is_master=None, is_warm=None, node_display_name=None, node_name=None, resource_spec=None, restart_number=None, start_time=None, status=None, storage_spec=None, _configuration=None):  # noqa: E501
        """NodeForDescribeInstanceNodesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._is_cold = None
        self._is_coordinator = None
        self._is_hot = None
        self._is_kibana = None
        self._is_master = None
        self._is_warm = None
        self._node_display_name = None
        self._node_name = None
        self._resource_spec = None
        self._restart_number = None
        self._start_time = None
        self._status = None
        self._storage_spec = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if is_cold is not None:
            self.is_cold = is_cold
        if is_coordinator is not None:
            self.is_coordinator = is_coordinator
        if is_hot is not None:
            self.is_hot = is_hot
        if is_kibana is not None:
            self.is_kibana = is_kibana
        if is_master is not None:
            self.is_master = is_master
        if is_warm is not None:
            self.is_warm = is_warm
        if node_display_name is not None:
            self.node_display_name = node_display_name
        if node_name is not None:
            self.node_name = node_name
        if resource_spec is not None:
            self.resource_spec = resource_spec
        if restart_number is not None:
            self.restart_number = restart_number
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if storage_spec is not None:
            self.storage_spec = storage_spec

    @property
    def instance_id(self):
        """Gets the instance_id of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The instance_id of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this NodeForDescribeInstanceNodesOutput.


        :param instance_id: The instance_id of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def is_cold(self):
        """Gets the is_cold of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_cold of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_cold

    @is_cold.setter
    def is_cold(self, is_cold):
        """Sets the is_cold of this NodeForDescribeInstanceNodesOutput.


        :param is_cold: The is_cold of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_cold = is_cold

    @property
    def is_coordinator(self):
        """Gets the is_coordinator of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_coordinator of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_coordinator

    @is_coordinator.setter
    def is_coordinator(self, is_coordinator):
        """Sets the is_coordinator of this NodeForDescribeInstanceNodesOutput.


        :param is_coordinator: The is_coordinator of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_coordinator = is_coordinator

    @property
    def is_hot(self):
        """Gets the is_hot of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_hot of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_hot

    @is_hot.setter
    def is_hot(self, is_hot):
        """Sets the is_hot of this NodeForDescribeInstanceNodesOutput.


        :param is_hot: The is_hot of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_hot = is_hot

    @property
    def is_kibana(self):
        """Gets the is_kibana of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_kibana of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_kibana

    @is_kibana.setter
    def is_kibana(self, is_kibana):
        """Sets the is_kibana of this NodeForDescribeInstanceNodesOutput.


        :param is_kibana: The is_kibana of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_kibana = is_kibana

    @property
    def is_master(self):
        """Gets the is_master of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_master of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_master

    @is_master.setter
    def is_master(self, is_master):
        """Sets the is_master of this NodeForDescribeInstanceNodesOutput.


        :param is_master: The is_master of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_master = is_master

    @property
    def is_warm(self):
        """Gets the is_warm of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The is_warm of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_warm

    @is_warm.setter
    def is_warm(self, is_warm):
        """Sets the is_warm of this NodeForDescribeInstanceNodesOutput.


        :param is_warm: The is_warm of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_warm = is_warm

    @property
    def node_display_name(self):
        """Gets the node_display_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The node_display_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_display_name

    @node_display_name.setter
    def node_display_name(self, node_display_name):
        """Sets the node_display_name of this NodeForDescribeInstanceNodesOutput.


        :param node_display_name: The node_display_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_display_name = node_display_name

    @property
    def node_name(self):
        """Gets the node_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The node_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this NodeForDescribeInstanceNodesOutput.


        :param node_name: The node_name of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def resource_spec(self):
        """Gets the resource_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The resource_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: ResourceSpecForDescribeInstanceNodesOutput
        """
        return self._resource_spec

    @resource_spec.setter
    def resource_spec(self, resource_spec):
        """Sets the resource_spec of this NodeForDescribeInstanceNodesOutput.


        :param resource_spec: The resource_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: ResourceSpecForDescribeInstanceNodesOutput
        """

        self._resource_spec = resource_spec

    @property
    def restart_number(self):
        """Gets the restart_number of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The restart_number of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: int
        """
        return self._restart_number

    @restart_number.setter
    def restart_number(self, restart_number):
        """Sets the restart_number of this NodeForDescribeInstanceNodesOutput.


        :param restart_number: The restart_number of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: int
        """

        self._restart_number = restart_number

    @property
    def start_time(self):
        """Gets the start_time of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The start_time of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this NodeForDescribeInstanceNodesOutput.


        :param start_time: The start_time of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The status of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this NodeForDescribeInstanceNodesOutput.


        :param status: The status of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def storage_spec(self):
        """Gets the storage_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501


        :return: The storage_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :rtype: StorageSpecForDescribeInstanceNodesOutput
        """
        return self._storage_spec

    @storage_spec.setter
    def storage_spec(self, storage_spec):
        """Sets the storage_spec of this NodeForDescribeInstanceNodesOutput.


        :param storage_spec: The storage_spec of this NodeForDescribeInstanceNodesOutput.  # noqa: E501
        :type: StorageSpecForDescribeInstanceNodesOutput
        """

        self._storage_spec = storage_spec

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeForDescribeInstanceNodesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeForDescribeInstanceNodesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeForDescribeInstanceNodesOutput):
            return True

        return self.to_dict() != other.to_dict()
