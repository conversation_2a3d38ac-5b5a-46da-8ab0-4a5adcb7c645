# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicConfigForQueryDataMigrateTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'enable_range_check': 'bool',
        'failed_num_to_abort': 'int',
        'overwrite_policy': 'str',
        'source_type': 'str',
        'storage_class': 'str',
        'task_name': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'enable_range_check': 'EnableRangeCheck',
        'failed_num_to_abort': 'FailedNumToAbort',
        'overwrite_policy': 'OverwritePolicy',
        'source_type': 'SourceType',
        'storage_class': 'StorageClass',
        'task_name': 'TaskName'
    }

    def __init__(self, bandwidth=None, enable_range_check=None, failed_num_to_abort=None, overwrite_policy=None, source_type=None, storage_class=None, task_name=None, _configuration=None):  # noqa: E501
        """BasicConfigForQueryDataMigrateTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._enable_range_check = None
        self._failed_num_to_abort = None
        self._overwrite_policy = None
        self._source_type = None
        self._storage_class = None
        self._task_name = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if enable_range_check is not None:
            self.enable_range_check = enable_range_check
        if failed_num_to_abort is not None:
            self.failed_num_to_abort = failed_num_to_abort
        if overwrite_policy is not None:
            self.overwrite_policy = overwrite_policy
        if source_type is not None:
            self.source_type = source_type
        if storage_class is not None:
            self.storage_class = storage_class
        if task_name is not None:
            self.task_name = task_name

    @property
    def bandwidth(self):
        """Gets the bandwidth of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The bandwidth of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this BasicConfigForQueryDataMigrateTaskOutput.


        :param bandwidth: The bandwidth of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def enable_range_check(self):
        """Gets the enable_range_check of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The enable_range_check of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_range_check

    @enable_range_check.setter
    def enable_range_check(self, enable_range_check):
        """Sets the enable_range_check of this BasicConfigForQueryDataMigrateTaskOutput.


        :param enable_range_check: The enable_range_check of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: bool
        """

        self._enable_range_check = enable_range_check

    @property
    def failed_num_to_abort(self):
        """Gets the failed_num_to_abort of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The failed_num_to_abort of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._failed_num_to_abort

    @failed_num_to_abort.setter
    def failed_num_to_abort(self, failed_num_to_abort):
        """Sets the failed_num_to_abort of this BasicConfigForQueryDataMigrateTaskOutput.


        :param failed_num_to_abort: The failed_num_to_abort of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._failed_num_to_abort = failed_num_to_abort

    @property
    def overwrite_policy(self):
        """Gets the overwrite_policy of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The overwrite_policy of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._overwrite_policy

    @overwrite_policy.setter
    def overwrite_policy(self, overwrite_policy):
        """Sets the overwrite_policy of this BasicConfigForQueryDataMigrateTaskOutput.


        :param overwrite_policy: The overwrite_policy of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._overwrite_policy = overwrite_policy

    @property
    def source_type(self):
        """Gets the source_type of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The source_type of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this BasicConfigForQueryDataMigrateTaskOutput.


        :param source_type: The source_type of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def storage_class(self):
        """Gets the storage_class of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The storage_class of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_class

    @storage_class.setter
    def storage_class(self, storage_class):
        """Sets the storage_class of this BasicConfigForQueryDataMigrateTaskOutput.


        :param storage_class: The storage_class of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._storage_class = storage_class

    @property
    def task_name(self):
        """Gets the task_name of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The task_name of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this BasicConfigForQueryDataMigrateTaskOutput.


        :param task_name: The task_name of this BasicConfigForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicConfigForQueryDataMigrateTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicConfigForQueryDataMigrateTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicConfigForQueryDataMigrateTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
