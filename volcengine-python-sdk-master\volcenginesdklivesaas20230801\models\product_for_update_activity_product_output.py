# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProductForUpdateActivityProductOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direct_urls': 'list[str]',
        'enable_status': 'int',
        'explain_status': 'int',
        'explain_time': 'int',
        'floating_status': 'int',
        'highlight': 'str',
        'hot_sale': 'str',
        'id': 'int',
        'index': 'int',
        'introduce_image': 'str',
        'is_order_msg_enable': 'int',
        'mini_app_direct_url': 'str',
        'page_advertisement_type': 'int',
        'promotion_tag_type': 'int',
        'promotion_tag_url': 'str',
        'redirect_image': 'str',
        'redirect_url': 'str',
        'remark': 'str',
        'reminder_type': 'int',
        'selling_point_tag': 'str',
        'stock': 'str',
        'strikethrough': 'str',
        'title': 'str'
    }

    attribute_map = {
        'direct_urls': 'DirectUrls',
        'enable_status': 'EnableStatus',
        'explain_status': 'ExplainStatus',
        'explain_time': 'ExplainTime',
        'floating_status': 'FloatingStatus',
        'highlight': 'Highlight',
        'hot_sale': 'HotSale',
        'id': 'Id',
        'index': 'Index',
        'introduce_image': 'IntroduceImage',
        'is_order_msg_enable': 'IsOrderMsgEnable',
        'mini_app_direct_url': 'MiniAppDirectUrl',
        'page_advertisement_type': 'PageAdvertisementType',
        'promotion_tag_type': 'PromotionTagType',
        'promotion_tag_url': 'PromotionTagUrl',
        'redirect_image': 'RedirectImage',
        'redirect_url': 'RedirectUrl',
        'remark': 'Remark',
        'reminder_type': 'ReminderType',
        'selling_point_tag': 'SellingPointTag',
        'stock': 'Stock',
        'strikethrough': 'Strikethrough',
        'title': 'Title'
    }

    def __init__(self, direct_urls=None, enable_status=None, explain_status=None, explain_time=None, floating_status=None, highlight=None, hot_sale=None, id=None, index=None, introduce_image=None, is_order_msg_enable=None, mini_app_direct_url=None, page_advertisement_type=None, promotion_tag_type=None, promotion_tag_url=None, redirect_image=None, redirect_url=None, remark=None, reminder_type=None, selling_point_tag=None, stock=None, strikethrough=None, title=None, _configuration=None):  # noqa: E501
        """ProductForUpdateActivityProductOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direct_urls = None
        self._enable_status = None
        self._explain_status = None
        self._explain_time = None
        self._floating_status = None
        self._highlight = None
        self._hot_sale = None
        self._id = None
        self._index = None
        self._introduce_image = None
        self._is_order_msg_enable = None
        self._mini_app_direct_url = None
        self._page_advertisement_type = None
        self._promotion_tag_type = None
        self._promotion_tag_url = None
        self._redirect_image = None
        self._redirect_url = None
        self._remark = None
        self._reminder_type = None
        self._selling_point_tag = None
        self._stock = None
        self._strikethrough = None
        self._title = None
        self.discriminator = None

        if direct_urls is not None:
            self.direct_urls = direct_urls
        if enable_status is not None:
            self.enable_status = enable_status
        if explain_status is not None:
            self.explain_status = explain_status
        if explain_time is not None:
            self.explain_time = explain_time
        if floating_status is not None:
            self.floating_status = floating_status
        if highlight is not None:
            self.highlight = highlight
        if hot_sale is not None:
            self.hot_sale = hot_sale
        if id is not None:
            self.id = id
        if index is not None:
            self.index = index
        if introduce_image is not None:
            self.introduce_image = introduce_image
        if is_order_msg_enable is not None:
            self.is_order_msg_enable = is_order_msg_enable
        if mini_app_direct_url is not None:
            self.mini_app_direct_url = mini_app_direct_url
        if page_advertisement_type is not None:
            self.page_advertisement_type = page_advertisement_type
        if promotion_tag_type is not None:
            self.promotion_tag_type = promotion_tag_type
        if promotion_tag_url is not None:
            self.promotion_tag_url = promotion_tag_url
        if redirect_image is not None:
            self.redirect_image = redirect_image
        if redirect_url is not None:
            self.redirect_url = redirect_url
        if remark is not None:
            self.remark = remark
        if reminder_type is not None:
            self.reminder_type = reminder_type
        if selling_point_tag is not None:
            self.selling_point_tag = selling_point_tag
        if stock is not None:
            self.stock = stock
        if strikethrough is not None:
            self.strikethrough = strikethrough
        if title is not None:
            self.title = title

    @property
    def direct_urls(self):
        """Gets the direct_urls of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The direct_urls of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._direct_urls

    @direct_urls.setter
    def direct_urls(self, direct_urls):
        """Sets the direct_urls of this ProductForUpdateActivityProductOutput.


        :param direct_urls: The direct_urls of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: list[str]
        """

        self._direct_urls = direct_urls

    @property
    def enable_status(self):
        """Gets the enable_status of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The enable_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._enable_status

    @enable_status.setter
    def enable_status(self, enable_status):
        """Sets the enable_status of this ProductForUpdateActivityProductOutput.


        :param enable_status: The enable_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._enable_status = enable_status

    @property
    def explain_status(self):
        """Gets the explain_status of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The explain_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._explain_status

    @explain_status.setter
    def explain_status(self, explain_status):
        """Sets the explain_status of this ProductForUpdateActivityProductOutput.


        :param explain_status: The explain_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._explain_status = explain_status

    @property
    def explain_time(self):
        """Gets the explain_time of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The explain_time of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._explain_time

    @explain_time.setter
    def explain_time(self, explain_time):
        """Sets the explain_time of this ProductForUpdateActivityProductOutput.


        :param explain_time: The explain_time of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._explain_time = explain_time

    @property
    def floating_status(self):
        """Gets the floating_status of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The floating_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._floating_status

    @floating_status.setter
    def floating_status(self, floating_status):
        """Sets the floating_status of this ProductForUpdateActivityProductOutput.


        :param floating_status: The floating_status of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._floating_status = floating_status

    @property
    def highlight(self):
        """Gets the highlight of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The highlight of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._highlight

    @highlight.setter
    def highlight(self, highlight):
        """Sets the highlight of this ProductForUpdateActivityProductOutput.


        :param highlight: The highlight of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._highlight = highlight

    @property
    def hot_sale(self):
        """Gets the hot_sale of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The hot_sale of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._hot_sale

    @hot_sale.setter
    def hot_sale(self, hot_sale):
        """Sets the hot_sale of this ProductForUpdateActivityProductOutput.


        :param hot_sale: The hot_sale of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._hot_sale = hot_sale

    @property
    def id(self):
        """Gets the id of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The id of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ProductForUpdateActivityProductOutput.


        :param id: The id of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def index(self):
        """Gets the index of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The index of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this ProductForUpdateActivityProductOutput.


        :param index: The index of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._index = index

    @property
    def introduce_image(self):
        """Gets the introduce_image of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The introduce_image of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._introduce_image

    @introduce_image.setter
    def introduce_image(self, introduce_image):
        """Sets the introduce_image of this ProductForUpdateActivityProductOutput.


        :param introduce_image: The introduce_image of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._introduce_image = introduce_image

    @property
    def is_order_msg_enable(self):
        """Gets the is_order_msg_enable of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The is_order_msg_enable of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_order_msg_enable

    @is_order_msg_enable.setter
    def is_order_msg_enable(self, is_order_msg_enable):
        """Sets the is_order_msg_enable of this ProductForUpdateActivityProductOutput.


        :param is_order_msg_enable: The is_order_msg_enable of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._is_order_msg_enable = is_order_msg_enable

    @property
    def mini_app_direct_url(self):
        """Gets the mini_app_direct_url of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The mini_app_direct_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_direct_url

    @mini_app_direct_url.setter
    def mini_app_direct_url(self, mini_app_direct_url):
        """Sets the mini_app_direct_url of this ProductForUpdateActivityProductOutput.


        :param mini_app_direct_url: The mini_app_direct_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._mini_app_direct_url = mini_app_direct_url

    @property
    def page_advertisement_type(self):
        """Gets the page_advertisement_type of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The page_advertisement_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_advertisement_type

    @page_advertisement_type.setter
    def page_advertisement_type(self, page_advertisement_type):
        """Sets the page_advertisement_type of this ProductForUpdateActivityProductOutput.


        :param page_advertisement_type: The page_advertisement_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._page_advertisement_type = page_advertisement_type

    @property
    def promotion_tag_type(self):
        """Gets the promotion_tag_type of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The promotion_tag_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._promotion_tag_type

    @promotion_tag_type.setter
    def promotion_tag_type(self, promotion_tag_type):
        """Sets the promotion_tag_type of this ProductForUpdateActivityProductOutput.


        :param promotion_tag_type: The promotion_tag_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._promotion_tag_type = promotion_tag_type

    @property
    def promotion_tag_url(self):
        """Gets the promotion_tag_url of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The promotion_tag_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._promotion_tag_url

    @promotion_tag_url.setter
    def promotion_tag_url(self, promotion_tag_url):
        """Sets the promotion_tag_url of this ProductForUpdateActivityProductOutput.


        :param promotion_tag_url: The promotion_tag_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._promotion_tag_url = promotion_tag_url

    @property
    def redirect_image(self):
        """Gets the redirect_image of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The redirect_image of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._redirect_image

    @redirect_image.setter
    def redirect_image(self, redirect_image):
        """Sets the redirect_image of this ProductForUpdateActivityProductOutput.


        :param redirect_image: The redirect_image of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._redirect_image = redirect_image

    @property
    def redirect_url(self):
        """Gets the redirect_url of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The redirect_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._redirect_url

    @redirect_url.setter
    def redirect_url(self, redirect_url):
        """Sets the redirect_url of this ProductForUpdateActivityProductOutput.


        :param redirect_url: The redirect_url of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._redirect_url = redirect_url

    @property
    def remark(self):
        """Gets the remark of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The remark of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this ProductForUpdateActivityProductOutput.


        :param remark: The remark of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def reminder_type(self):
        """Gets the reminder_type of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The reminder_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: int
        """
        return self._reminder_type

    @reminder_type.setter
    def reminder_type(self, reminder_type):
        """Sets the reminder_type of this ProductForUpdateActivityProductOutput.


        :param reminder_type: The reminder_type of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: int
        """

        self._reminder_type = reminder_type

    @property
    def selling_point_tag(self):
        """Gets the selling_point_tag of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The selling_point_tag of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._selling_point_tag

    @selling_point_tag.setter
    def selling_point_tag(self, selling_point_tag):
        """Sets the selling_point_tag of this ProductForUpdateActivityProductOutput.


        :param selling_point_tag: The selling_point_tag of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._selling_point_tag = selling_point_tag

    @property
    def stock(self):
        """Gets the stock of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The stock of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._stock

    @stock.setter
    def stock(self, stock):
        """Sets the stock of this ProductForUpdateActivityProductOutput.


        :param stock: The stock of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._stock = stock

    @property
    def strikethrough(self):
        """Gets the strikethrough of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The strikethrough of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._strikethrough

    @strikethrough.setter
    def strikethrough(self, strikethrough):
        """Sets the strikethrough of this ProductForUpdateActivityProductOutput.


        :param strikethrough: The strikethrough of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._strikethrough = strikethrough

    @property
    def title(self):
        """Gets the title of this ProductForUpdateActivityProductOutput.  # noqa: E501


        :return: The title of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this ProductForUpdateActivityProductOutput.


        :param title: The title of this ProductForUpdateActivityProductOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProductForUpdateActivityProductOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProductForUpdateActivityProductOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProductForUpdateActivityProductOutput):
            return True

        return self.to_dict() != other.to_dict()
