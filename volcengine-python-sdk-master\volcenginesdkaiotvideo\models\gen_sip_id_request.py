# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GenSipIDRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'device_type': 'str',
        'sip_server_id': 'str',
        'space_id': 'str'
    }

    attribute_map = {
        'device_type': 'DeviceType',
        'sip_server_id': 'SipServerID',
        'space_id': 'SpaceID'
    }

    def __init__(self, device_type=None, sip_server_id=None, space_id=None, _configuration=None):  # noqa: E501
        """GenSipIDRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._device_type = None
        self._sip_server_id = None
        self._space_id = None
        self.discriminator = None

        self.device_type = device_type
        if sip_server_id is not None:
            self.sip_server_id = sip_server_id
        if space_id is not None:
            self.space_id = space_id

    @property
    def device_type(self):
        """Gets the device_type of this GenSipIDRequest.  # noqa: E501


        :return: The device_type of this GenSipIDRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_type

    @device_type.setter
    def device_type(self, device_type):
        """Sets the device_type of this GenSipIDRequest.


        :param device_type: The device_type of this GenSipIDRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and device_type is None:
            raise ValueError("Invalid value for `device_type`, must not be `None`")  # noqa: E501

        self._device_type = device_type

    @property
    def sip_server_id(self):
        """Gets the sip_server_id of this GenSipIDRequest.  # noqa: E501


        :return: The sip_server_id of this GenSipIDRequest.  # noqa: E501
        :rtype: str
        """
        return self._sip_server_id

    @sip_server_id.setter
    def sip_server_id(self, sip_server_id):
        """Sets the sip_server_id of this GenSipIDRequest.


        :param sip_server_id: The sip_server_id of this GenSipIDRequest.  # noqa: E501
        :type: str
        """

        self._sip_server_id = sip_server_id

    @property
    def space_id(self):
        """Gets the space_id of this GenSipIDRequest.  # noqa: E501


        :return: The space_id of this GenSipIDRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this GenSipIDRequest.


        :param space_id: The space_id of this GenSipIDRequest.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GenSipIDRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GenSipIDRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GenSipIDRequest):
            return True

        return self.to_dict() != other.to_dict()
