# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceConfigForListJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'holding_time_seconds': 'int',
        'max_runtime_seconds': 'int',
        'numa_affinity': 'str',
        'only_use_suspected_node': 'bool',
        'preemptible': 'bool',
        'priority': 'int',
        'resource_queue_id': 'str',
        'resource_reservation': 'bool',
        'resource_reservation_plan_id': 'str',
        'roles': 'list[RoleForListJobsOutput]'
    }

    attribute_map = {
        'holding_time_seconds': 'HoldingTimeSeconds',
        'max_runtime_seconds': 'MaxRuntimeSeconds',
        'numa_affinity': 'NumaAffinity',
        'only_use_suspected_node': 'OnlyUseSuspectedNode',
        'preemptible': 'Preemptible',
        'priority': 'Priority',
        'resource_queue_id': 'ResourceQueueId',
        'resource_reservation': 'ResourceReservation',
        'resource_reservation_plan_id': 'ResourceReservationPlanId',
        'roles': 'Roles'
    }

    def __init__(self, holding_time_seconds=None, max_runtime_seconds=None, numa_affinity=None, only_use_suspected_node=None, preemptible=None, priority=None, resource_queue_id=None, resource_reservation=None, resource_reservation_plan_id=None, roles=None, _configuration=None):  # noqa: E501
        """ResourceConfigForListJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._holding_time_seconds = None
        self._max_runtime_seconds = None
        self._numa_affinity = None
        self._only_use_suspected_node = None
        self._preemptible = None
        self._priority = None
        self._resource_queue_id = None
        self._resource_reservation = None
        self._resource_reservation_plan_id = None
        self._roles = None
        self.discriminator = None

        if holding_time_seconds is not None:
            self.holding_time_seconds = holding_time_seconds
        if max_runtime_seconds is not None:
            self.max_runtime_seconds = max_runtime_seconds
        if numa_affinity is not None:
            self.numa_affinity = numa_affinity
        if only_use_suspected_node is not None:
            self.only_use_suspected_node = only_use_suspected_node
        if preemptible is not None:
            self.preemptible = preemptible
        if priority is not None:
            self.priority = priority
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if resource_reservation is not None:
            self.resource_reservation = resource_reservation
        if resource_reservation_plan_id is not None:
            self.resource_reservation_plan_id = resource_reservation_plan_id
        if roles is not None:
            self.roles = roles

    @property
    def holding_time_seconds(self):
        """Gets the holding_time_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The holding_time_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._holding_time_seconds

    @holding_time_seconds.setter
    def holding_time_seconds(self, holding_time_seconds):
        """Sets the holding_time_seconds of this ResourceConfigForListJobsOutput.


        :param holding_time_seconds: The holding_time_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: int
        """

        self._holding_time_seconds = holding_time_seconds

    @property
    def max_runtime_seconds(self):
        """Gets the max_runtime_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The max_runtime_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_runtime_seconds

    @max_runtime_seconds.setter
    def max_runtime_seconds(self, max_runtime_seconds):
        """Sets the max_runtime_seconds of this ResourceConfigForListJobsOutput.


        :param max_runtime_seconds: The max_runtime_seconds of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: int
        """

        self._max_runtime_seconds = max_runtime_seconds

    @property
    def numa_affinity(self):
        """Gets the numa_affinity of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The numa_affinity of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._numa_affinity

    @numa_affinity.setter
    def numa_affinity(self, numa_affinity):
        """Sets the numa_affinity of this ResourceConfigForListJobsOutput.


        :param numa_affinity: The numa_affinity of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: str
        """

        self._numa_affinity = numa_affinity

    @property
    def only_use_suspected_node(self):
        """Gets the only_use_suspected_node of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The only_use_suspected_node of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._only_use_suspected_node

    @only_use_suspected_node.setter
    def only_use_suspected_node(self, only_use_suspected_node):
        """Sets the only_use_suspected_node of this ResourceConfigForListJobsOutput.


        :param only_use_suspected_node: The only_use_suspected_node of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: bool
        """

        self._only_use_suspected_node = only_use_suspected_node

    @property
    def preemptible(self):
        """Gets the preemptible of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The preemptible of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._preemptible

    @preemptible.setter
    def preemptible(self, preemptible):
        """Sets the preemptible of this ResourceConfigForListJobsOutput.


        :param preemptible: The preemptible of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: bool
        """

        self._preemptible = preemptible

    @property
    def priority(self):
        """Gets the priority of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The priority of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ResourceConfigForListJobsOutput.


        :param priority: The priority of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The resource_queue_id of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this ResourceConfigForListJobsOutput.


        :param resource_queue_id: The resource_queue_id of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def resource_reservation(self):
        """Gets the resource_reservation of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The resource_reservation of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._resource_reservation

    @resource_reservation.setter
    def resource_reservation(self, resource_reservation):
        """Sets the resource_reservation of this ResourceConfigForListJobsOutput.


        :param resource_reservation: The resource_reservation of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: bool
        """

        self._resource_reservation = resource_reservation

    @property
    def resource_reservation_plan_id(self):
        """Gets the resource_reservation_plan_id of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The resource_reservation_plan_id of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_reservation_plan_id

    @resource_reservation_plan_id.setter
    def resource_reservation_plan_id(self, resource_reservation_plan_id):
        """Sets the resource_reservation_plan_id of this ResourceConfigForListJobsOutput.


        :param resource_reservation_plan_id: The resource_reservation_plan_id of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_reservation_plan_id = resource_reservation_plan_id

    @property
    def roles(self):
        """Gets the roles of this ResourceConfigForListJobsOutput.  # noqa: E501


        :return: The roles of this ResourceConfigForListJobsOutput.  # noqa: E501
        :rtype: list[RoleForListJobsOutput]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this ResourceConfigForListJobsOutput.


        :param roles: The roles of this ResourceConfigForListJobsOutput.  # noqa: E501
        :type: list[RoleForListJobsOutput]
        """

        self._roles = roles

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceConfigForListJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceConfigForListJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceConfigForListJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
