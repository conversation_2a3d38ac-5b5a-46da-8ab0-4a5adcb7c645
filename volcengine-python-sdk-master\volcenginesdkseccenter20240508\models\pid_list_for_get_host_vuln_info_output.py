# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PidListForGetHostVulnInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cmd': 'str',
        'pid': 'str'
    }

    attribute_map = {
        'cmd': 'Cmd',
        'pid': 'Pid'
    }

    def __init__(self, cmd=None, pid=None, _configuration=None):  # noqa: E501
        """PidListForGetHostVulnInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cmd = None
        self._pid = None
        self.discriminator = None

        if cmd is not None:
            self.cmd = cmd
        if pid is not None:
            self.pid = pid

    @property
    def cmd(self):
        """Gets the cmd of this PidListForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cmd of this PidListForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmd

    @cmd.setter
    def cmd(self, cmd):
        """Sets the cmd of this PidListForGetHostVulnInfoOutput.


        :param cmd: The cmd of this PidListForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cmd = cmd

    @property
    def pid(self):
        """Gets the pid of this PidListForGetHostVulnInfoOutput.  # noqa: E501


        :return: The pid of this PidListForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this PidListForGetHostVulnInfoOutput.


        :param pid: The pid of this PidListForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PidListForGetHostVulnInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PidListForGetHostVulnInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PidListForGetHostVulnInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
