# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusForListDeploymentsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_replicas': 'int',
        'expected_replicias': 'int',
        'expected_updated_replicas': 'int',
        'total_replicas': 'int',
        'updated_available_replicas': 'int',
        'updated_replicas': 'int'
    }

    attribute_map = {
        'available_replicas': 'AvailableReplicas',
        'expected_replicias': 'ExpectedReplicias',
        'expected_updated_replicas': 'ExpectedUpdatedReplicas',
        'total_replicas': 'TotalReplicas',
        'updated_available_replicas': 'UpdatedAvailableReplicas',
        'updated_replicas': 'UpdatedReplicas'
    }

    def __init__(self, available_replicas=None, expected_replicias=None, expected_updated_replicas=None, total_replicas=None, updated_available_replicas=None, updated_replicas=None, _configuration=None):  # noqa: E501
        """StatusForListDeploymentsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_replicas = None
        self._expected_replicias = None
        self._expected_updated_replicas = None
        self._total_replicas = None
        self._updated_available_replicas = None
        self._updated_replicas = None
        self.discriminator = None

        if available_replicas is not None:
            self.available_replicas = available_replicas
        if expected_replicias is not None:
            self.expected_replicias = expected_replicias
        if expected_updated_replicas is not None:
            self.expected_updated_replicas = expected_updated_replicas
        if total_replicas is not None:
            self.total_replicas = total_replicas
        if updated_available_replicas is not None:
            self.updated_available_replicas = updated_available_replicas
        if updated_replicas is not None:
            self.updated_replicas = updated_replicas

    @property
    def available_replicas(self):
        """Gets the available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._available_replicas

    @available_replicas.setter
    def available_replicas(self, available_replicas):
        """Sets the available_replicas of this StatusForListDeploymentsOutput.


        :param available_replicas: The available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._available_replicas = available_replicas

    @property
    def expected_replicias(self):
        """Gets the expected_replicias of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The expected_replicias of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._expected_replicias

    @expected_replicias.setter
    def expected_replicias(self, expected_replicias):
        """Sets the expected_replicias of this StatusForListDeploymentsOutput.


        :param expected_replicias: The expected_replicias of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._expected_replicias = expected_replicias

    @property
    def expected_updated_replicas(self):
        """Gets the expected_updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The expected_updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._expected_updated_replicas

    @expected_updated_replicas.setter
    def expected_updated_replicas(self, expected_updated_replicas):
        """Sets the expected_updated_replicas of this StatusForListDeploymentsOutput.


        :param expected_updated_replicas: The expected_updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._expected_updated_replicas = expected_updated_replicas

    @property
    def total_replicas(self):
        """Gets the total_replicas of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The total_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_replicas

    @total_replicas.setter
    def total_replicas(self, total_replicas):
        """Sets the total_replicas of this StatusForListDeploymentsOutput.


        :param total_replicas: The total_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._total_replicas = total_replicas

    @property
    def updated_available_replicas(self):
        """Gets the updated_available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The updated_available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._updated_available_replicas

    @updated_available_replicas.setter
    def updated_available_replicas(self, updated_available_replicas):
        """Sets the updated_available_replicas of this StatusForListDeploymentsOutput.


        :param updated_available_replicas: The updated_available_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._updated_available_replicas = updated_available_replicas

    @property
    def updated_replicas(self):
        """Gets the updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501


        :return: The updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :rtype: int
        """
        return self._updated_replicas

    @updated_replicas.setter
    def updated_replicas(self, updated_replicas):
        """Sets the updated_replicas of this StatusForListDeploymentsOutput.


        :param updated_replicas: The updated_replicas of this StatusForListDeploymentsOutput.  # noqa: E501
        :type: int
        """

        self._updated_replicas = updated_replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusForListDeploymentsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusForListDeploymentsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusForListDeploymentsOutput):
            return True

        return self.to_dict() != other.to_dict()
