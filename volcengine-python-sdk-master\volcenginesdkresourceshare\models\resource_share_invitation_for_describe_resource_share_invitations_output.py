# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceShareInvitationForDescribeResourceShareInvitationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'invitated_time': 'str',
        'owning_account_id': 'int',
        'receiver_account_id': 'int',
        'resource_share_id': 'str',
        'resource_share_invitation_trn': 'str',
        'resource_share_name': 'str',
        'resource_share_trn': 'str',
        'status': 'str'
    }

    attribute_map = {
        'invitated_time': 'InvitatedTime',
        'owning_account_id': 'OwningAccountId',
        'receiver_account_id': 'ReceiverAccountId',
        'resource_share_id': 'ResourceShareId',
        'resource_share_invitation_trn': 'ResourceShareInvitationTrn',
        'resource_share_name': 'ResourceShareName',
        'resource_share_trn': 'ResourceShareTrn',
        'status': 'Status'
    }

    def __init__(self, invitated_time=None, owning_account_id=None, receiver_account_id=None, resource_share_id=None, resource_share_invitation_trn=None, resource_share_name=None, resource_share_trn=None, status=None, _configuration=None):  # noqa: E501
        """ResourceShareInvitationForDescribeResourceShareInvitationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._invitated_time = None
        self._owning_account_id = None
        self._receiver_account_id = None
        self._resource_share_id = None
        self._resource_share_invitation_trn = None
        self._resource_share_name = None
        self._resource_share_trn = None
        self._status = None
        self.discriminator = None

        if invitated_time is not None:
            self.invitated_time = invitated_time
        if owning_account_id is not None:
            self.owning_account_id = owning_account_id
        if receiver_account_id is not None:
            self.receiver_account_id = receiver_account_id
        if resource_share_id is not None:
            self.resource_share_id = resource_share_id
        if resource_share_invitation_trn is not None:
            self.resource_share_invitation_trn = resource_share_invitation_trn
        if resource_share_name is not None:
            self.resource_share_name = resource_share_name
        if resource_share_trn is not None:
            self.resource_share_trn = resource_share_trn
        if status is not None:
            self.status = status

    @property
    def invitated_time(self):
        """Gets the invitated_time of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The invitated_time of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._invitated_time

    @invitated_time.setter
    def invitated_time(self, invitated_time):
        """Sets the invitated_time of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param invitated_time: The invitated_time of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._invitated_time = invitated_time

    @property
    def owning_account_id(self):
        """Gets the owning_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The owning_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._owning_account_id

    @owning_account_id.setter
    def owning_account_id(self, owning_account_id):
        """Sets the owning_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param owning_account_id: The owning_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: int
        """

        self._owning_account_id = owning_account_id

    @property
    def receiver_account_id(self):
        """Gets the receiver_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The receiver_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._receiver_account_id

    @receiver_account_id.setter
    def receiver_account_id(self, receiver_account_id):
        """Sets the receiver_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param receiver_account_id: The receiver_account_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: int
        """

        self._receiver_account_id = receiver_account_id

    @property
    def resource_share_id(self):
        """Gets the resource_share_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The resource_share_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_id

    @resource_share_id.setter
    def resource_share_id(self, resource_share_id):
        """Sets the resource_share_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param resource_share_id: The resource_share_id of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_id = resource_share_id

    @property
    def resource_share_invitation_trn(self):
        """Gets the resource_share_invitation_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The resource_share_invitation_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_invitation_trn

    @resource_share_invitation_trn.setter
    def resource_share_invitation_trn(self, resource_share_invitation_trn):
        """Sets the resource_share_invitation_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param resource_share_invitation_trn: The resource_share_invitation_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_invitation_trn = resource_share_invitation_trn

    @property
    def resource_share_name(self):
        """Gets the resource_share_name of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The resource_share_name of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_name

    @resource_share_name.setter
    def resource_share_name(self, resource_share_name):
        """Sets the resource_share_name of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param resource_share_name: The resource_share_name of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_name = resource_share_name

    @property
    def resource_share_trn(self):
        """Gets the resource_share_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The resource_share_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trn

    @resource_share_trn.setter
    def resource_share_trn(self, resource_share_trn):
        """Sets the resource_share_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param resource_share_trn: The resource_share_trn of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_trn = resource_share_trn

    @property
    def status(self):
        """Gets the status of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501


        :return: The status of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.


        :param status: The status of this ResourceShareInvitationForDescribeResourceShareInvitationsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceShareInvitationForDescribeResourceShareInvitationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceShareInvitationForDescribeResourceShareInvitationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceShareInvitationForDescribeResourceShareInvitationsOutput):
            return True

        return self.to_dict() != other.to_dict()
