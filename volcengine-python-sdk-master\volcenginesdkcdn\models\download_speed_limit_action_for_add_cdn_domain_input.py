# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DownloadSpeedLimitActionForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'speed_limit_rate': 'int',
        'speed_limit_rate_after': 'int',
        'speed_limit_time': 'SpeedLimitTimeForAddCdnDomainInput'
    }

    attribute_map = {
        'speed_limit_rate': 'SpeedLimitRate',
        'speed_limit_rate_after': 'SpeedLimitRateAfter',
        'speed_limit_time': 'SpeedLimitTime'
    }

    def __init__(self, speed_limit_rate=None, speed_limit_rate_after=None, speed_limit_time=None, _configuration=None):  # noqa: E501
        """DownloadSpeedLimitActionForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._speed_limit_rate = None
        self._speed_limit_rate_after = None
        self._speed_limit_time = None
        self.discriminator = None

        if speed_limit_rate is not None:
            self.speed_limit_rate = speed_limit_rate
        if speed_limit_rate_after is not None:
            self.speed_limit_rate_after = speed_limit_rate_after
        if speed_limit_time is not None:
            self.speed_limit_time = speed_limit_time

    @property
    def speed_limit_rate(self):
        """Gets the speed_limit_rate of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501


        :return: The speed_limit_rate of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :rtype: int
        """
        return self._speed_limit_rate

    @speed_limit_rate.setter
    def speed_limit_rate(self, speed_limit_rate):
        """Sets the speed_limit_rate of this DownloadSpeedLimitActionForAddCdnDomainInput.


        :param speed_limit_rate: The speed_limit_rate of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :type: int
        """

        self._speed_limit_rate = speed_limit_rate

    @property
    def speed_limit_rate_after(self):
        """Gets the speed_limit_rate_after of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501


        :return: The speed_limit_rate_after of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :rtype: int
        """
        return self._speed_limit_rate_after

    @speed_limit_rate_after.setter
    def speed_limit_rate_after(self, speed_limit_rate_after):
        """Sets the speed_limit_rate_after of this DownloadSpeedLimitActionForAddCdnDomainInput.


        :param speed_limit_rate_after: The speed_limit_rate_after of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :type: int
        """

        self._speed_limit_rate_after = speed_limit_rate_after

    @property
    def speed_limit_time(self):
        """Gets the speed_limit_time of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501


        :return: The speed_limit_time of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :rtype: SpeedLimitTimeForAddCdnDomainInput
        """
        return self._speed_limit_time

    @speed_limit_time.setter
    def speed_limit_time(self, speed_limit_time):
        """Sets the speed_limit_time of this DownloadSpeedLimitActionForAddCdnDomainInput.


        :param speed_limit_time: The speed_limit_time of this DownloadSpeedLimitActionForAddCdnDomainInput.  # noqa: E501
        :type: SpeedLimitTimeForAddCdnDomainInput
        """

        self._speed_limit_time = speed_limit_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DownloadSpeedLimitActionForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DownloadSpeedLimitActionForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DownloadSpeedLimitActionForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
