import base64
import io
import os

def find_image_path(filename, images_dir, train_dir):
    """在images_dir和train_dir目录下查找指定文件名的图片文件
    
    Args:
        filename (str): 要查找的文件名
        images_dir (str): 图片目录路径
        train_dir (str): 训练数据目录路径
        
    Returns:
        str: 找到的图片文件路径，如果未找到则返回None
    """
    # 定义支持的图片扩展名
    image_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"]
    
    # 在images_dir中查找
    if os.path.exists(images_dir):
        for root, _, files in os.walk(images_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    if os.path.splitext(file)[0] == filename:
                        return os.path.join(root, file)
    
    # 在train_dir中查找
    if train_dir and os.path.exists(train_dir):
        for root, _, files in os.walk(train_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    if os.path.splitext(file)[0] == filename:
                        return os.path.join(root, file)
    
    return None
import cv2
import numpy as np
from PIL import Image

def enhance_marks_to_black(image_data, threshold=200, use_pixel_connection=False):
    """
    将图片中的灰色或浅黑色标记增强为纯黑色，并将背景转换为纯白色。
    可选择是否采用黑色像素粘连处理。

    Args:
        image_data: 图片数据
        threshold: 阈值，低于该值的像素变为黑色，否则为白色
        use_pixel_connection: 是否采用黑色像素粘连处理
    """
    image = Image.open(io.BytesIO(image_data))
    grayscale_image = image.convert('L')
    threshold_image = grayscale_image.point(lambda p: 0 if p < threshold else 255)

    if use_pixel_connection:
        # 应用黑色像素粘连处理
        arr = np.array(threshold_image)

        # 横向扫描，相差两个像素进行粘连
        for y in range(arr.shape[0]):
            for x in range(2, arr.shape[1] - 2):
                if arr[y, x - 2] == 0 and arr[y, x - 1] == 255 and arr[y, x] == 255 and arr[y, x + 1] == 255 and \
                        arr[y, x + 2] == 0:
                    arr[y, x - 1] = 0
                    arr[y, x] = 0
                    arr[y, x + 1] = 0

        # 纵向扫描，相差两个像素进行粘连
        for x in range(arr.shape[1]):
            for y in range(2, arr.shape[0] - 2):
                if arr[y - 2, x] == 0 and arr[y - 1, x] == 255 and arr[y, x] == 255 and arr[y + 1, x] == 255 and \
                        arr[y + 2, x] == 0:
                    arr[y - 1, x] = 0
                    arr[y, x] = 0
                    arr[y + 1, x] = 0

        final_image = Image.fromarray(arr).convert('RGB')
    else:
        final_image = threshold_image.convert('RGB')

    buffer = io.BytesIO()
    final_image.save(buffer, format='JPEG')
    return buffer.getvalue()

def image_to_base64(image_path, use_enhance=True, enhance_threshold=200, scale=1.0, use_pixel_connection=False):
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
    if use_enhance:
        try:
            enhanced_image_data = enhance_marks_to_black(image_data, enhance_threshold, use_pixel_connection)
        except Exception as e:
            print(f"错误：增强标记时发生错误: {e}")
            enhanced_image_data = image_data
    else:
        enhanced_image_data = image_data
    image = Image.open(io.BytesIO(enhanced_image_data))
    if scale != 1.0:
        original_width, original_height = image.size
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)
        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    buffer = io.BytesIO()
    ext = os.path.splitext(image_path)[1].lower()
    img_format = image.format if image.format else 'JPEG'
    image.save(buffer, format=img_format)
    image_bytes = buffer.getvalue()
    if ext in ['.jpg', '.jpeg']:
        mime_type = 'image/jpeg'
    elif ext == '.png':
        mime_type = 'image/jpeg'
    elif ext == '.gif':
        mime_type = 'image/gif'
    elif ext == '.webp':
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'
    encoded_string = base64.b64encode(image_bytes).decode('utf-8')
    return f"data:{mime_type};base64,{encoded_string}"

def validate_base64(b64_str):
    try:
        if not b64_str.startswith("data:image/"):
            return False
        header, data = b64_str.split(",", 1)
        base64.b64decode(data)
        return True
    except Exception:
        return False