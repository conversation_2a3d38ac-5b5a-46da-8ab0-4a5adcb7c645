# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TemplateListForGetAccountTemplateAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_image': 'str',
        'describe': 'str',
        'is_default_template': 'int',
        'is_system_template': 'int',
        'live_layout': 'int',
        'modify_time': 'int',
        'status': 'int',
        'template_id': 'int',
        'template_name': 'str'
    }

    attribute_map = {
        'cover_image': 'CoverImage',
        'describe': 'Describe',
        'is_default_template': 'IsDefaultTemplate',
        'is_system_template': 'IsSystemTemplate',
        'live_layout': 'LiveLayout',
        'modify_time': 'ModifyTime',
        'status': 'Status',
        'template_id': 'TemplateId',
        'template_name': 'TemplateName'
    }

    def __init__(self, cover_image=None, describe=None, is_default_template=None, is_system_template=None, live_layout=None, modify_time=None, status=None, template_id=None, template_name=None, _configuration=None):  # noqa: E501
        """TemplateListForGetAccountTemplateAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_image = None
        self._describe = None
        self._is_default_template = None
        self._is_system_template = None
        self._live_layout = None
        self._modify_time = None
        self._status = None
        self._template_id = None
        self._template_name = None
        self.discriminator = None

        if cover_image is not None:
            self.cover_image = cover_image
        if describe is not None:
            self.describe = describe
        if is_default_template is not None:
            self.is_default_template = is_default_template
        if is_system_template is not None:
            self.is_system_template = is_system_template
        if live_layout is not None:
            self.live_layout = live_layout
        if modify_time is not None:
            self.modify_time = modify_time
        if status is not None:
            self.status = status
        if template_id is not None:
            self.template_id = template_id
        if template_name is not None:
            self.template_name = template_name

    @property
    def cover_image(self):
        """Gets the cover_image of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The cover_image of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this TemplateListForGetAccountTemplateAPIOutput.


        :param cover_image: The cover_image of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def describe(self):
        """Gets the describe of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The describe of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._describe

    @describe.setter
    def describe(self, describe):
        """Sets the describe of this TemplateListForGetAccountTemplateAPIOutput.


        :param describe: The describe of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: str
        """

        self._describe = describe

    @property
    def is_default_template(self):
        """Gets the is_default_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The is_default_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_default_template

    @is_default_template.setter
    def is_default_template(self, is_default_template):
        """Sets the is_default_template of this TemplateListForGetAccountTemplateAPIOutput.


        :param is_default_template: The is_default_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_default_template = is_default_template

    @property
    def is_system_template(self):
        """Gets the is_system_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The is_system_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_system_template

    @is_system_template.setter
    def is_system_template(self, is_system_template):
        """Sets the is_system_template of this TemplateListForGetAccountTemplateAPIOutput.


        :param is_system_template: The is_system_template of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_system_template = is_system_template

    @property
    def live_layout(self):
        """Gets the live_layout of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The live_layout of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_layout

    @live_layout.setter
    def live_layout(self, live_layout):
        """Sets the live_layout of this TemplateListForGetAccountTemplateAPIOutput.


        :param live_layout: The live_layout of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_layout = live_layout

    @property
    def modify_time(self):
        """Gets the modify_time of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The modify_time of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._modify_time

    @modify_time.setter
    def modify_time(self, modify_time):
        """Sets the modify_time of this TemplateListForGetAccountTemplateAPIOutput.


        :param modify_time: The modify_time of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._modify_time = modify_time

    @property
    def status(self):
        """Gets the status of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The status of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TemplateListForGetAccountTemplateAPIOutput.


        :param status: The status of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def template_id(self):
        """Gets the template_id of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The template_id of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._template_id

    @template_id.setter
    def template_id(self, template_id):
        """Sets the template_id of this TemplateListForGetAccountTemplateAPIOutput.


        :param template_id: The template_id of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: int
        """

        self._template_id = template_id

    @property
    def template_name(self):
        """Gets the template_name of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501


        :return: The template_name of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._template_name

    @template_name.setter
    def template_name(self, template_name):
        """Sets the template_name of this TemplateListForGetAccountTemplateAPIOutput.


        :param template_name: The template_name of this TemplateListForGetAccountTemplateAPIOutput.  # noqa: E501
        :type: str
        """

        self._template_name = template_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TemplateListForGetAccountTemplateAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TemplateListForGetAccountTemplateAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TemplateListForGetAccountTemplateAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
