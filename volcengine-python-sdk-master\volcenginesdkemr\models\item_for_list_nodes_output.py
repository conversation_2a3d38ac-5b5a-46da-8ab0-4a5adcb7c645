# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListNodesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'create_time': 'int',
        'ecs_instance_type': 'str',
        'node_fqdn': 'str',
        'node_group_id': 'str',
        'node_id': 'str',
        'node_name': 'str',
        'node_state': 'str',
        'private_ip': 'str',
        'public_ip': 'str',
        'ready_time': 'int',
        'terminate_time': 'int'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'create_time': 'CreateTime',
        'ecs_instance_type': 'EcsInstanceType',
        'node_fqdn': 'NodeFqdn',
        'node_group_id': 'NodeGroupId',
        'node_id': 'NodeId',
        'node_name': 'NodeName',
        'node_state': 'NodeState',
        'private_ip': 'PrivateIp',
        'public_ip': 'PublicIp',
        'ready_time': 'ReadyTime',
        'terminate_time': 'TerminateTime'
    }

    def __init__(self, cluster_id=None, create_time=None, ecs_instance_type=None, node_fqdn=None, node_group_id=None, node_id=None, node_name=None, node_state=None, private_ip=None, public_ip=None, ready_time=None, terminate_time=None, _configuration=None):  # noqa: E501
        """ItemForListNodesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._create_time = None
        self._ecs_instance_type = None
        self._node_fqdn = None
        self._node_group_id = None
        self._node_id = None
        self._node_name = None
        self._node_state = None
        self._private_ip = None
        self._public_ip = None
        self._ready_time = None
        self._terminate_time = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if create_time is not None:
            self.create_time = create_time
        if ecs_instance_type is not None:
            self.ecs_instance_type = ecs_instance_type
        if node_fqdn is not None:
            self.node_fqdn = node_fqdn
        if node_group_id is not None:
            self.node_group_id = node_group_id
        if node_id is not None:
            self.node_id = node_id
        if node_name is not None:
            self.node_name = node_name
        if node_state is not None:
            self.node_state = node_state
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if ready_time is not None:
            self.ready_time = ready_time
        if terminate_time is not None:
            self.terminate_time = terminate_time

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListNodesOutput.


        :param cluster_id: The cluster_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListNodesOutput.  # noqa: E501


        :return: The create_time of this ItemForListNodesOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListNodesOutput.


        :param create_time: The create_time of this ItemForListNodesOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def ecs_instance_type(self):
        """Gets the ecs_instance_type of this ItemForListNodesOutput.  # noqa: E501


        :return: The ecs_instance_type of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_instance_type

    @ecs_instance_type.setter
    def ecs_instance_type(self, ecs_instance_type):
        """Sets the ecs_instance_type of this ItemForListNodesOutput.


        :param ecs_instance_type: The ecs_instance_type of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._ecs_instance_type = ecs_instance_type

    @property
    def node_fqdn(self):
        """Gets the node_fqdn of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_fqdn of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_fqdn

    @node_fqdn.setter
    def node_fqdn(self, node_fqdn):
        """Sets the node_fqdn of this ItemForListNodesOutput.


        :param node_fqdn: The node_fqdn of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_fqdn = node_fqdn

    @property
    def node_group_id(self):
        """Gets the node_group_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_group_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_id

    @node_group_id.setter
    def node_group_id(self, node_group_id):
        """Sets the node_group_id of this ItemForListNodesOutput.


        :param node_group_id: The node_group_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_group_id = node_group_id

    @property
    def node_id(self):
        """Gets the node_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this ItemForListNodesOutput.


        :param node_id: The node_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def node_name(self):
        """Gets the node_name of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_name of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this ItemForListNodesOutput.


        :param node_name: The node_name of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def node_state(self):
        """Gets the node_state of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_state of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_state

    @node_state.setter
    def node_state(self, node_state):
        """Sets the node_state of this ItemForListNodesOutput.


        :param node_state: The node_state of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_state = node_state

    @property
    def private_ip(self):
        """Gets the private_ip of this ItemForListNodesOutput.  # noqa: E501


        :return: The private_ip of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this ItemForListNodesOutput.


        :param private_ip: The private_ip of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this ItemForListNodesOutput.  # noqa: E501


        :return: The public_ip of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this ItemForListNodesOutput.


        :param public_ip: The public_ip of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def ready_time(self):
        """Gets the ready_time of this ItemForListNodesOutput.  # noqa: E501


        :return: The ready_time of this ItemForListNodesOutput.  # noqa: E501
        :rtype: int
        """
        return self._ready_time

    @ready_time.setter
    def ready_time(self, ready_time):
        """Sets the ready_time of this ItemForListNodesOutput.


        :param ready_time: The ready_time of this ItemForListNodesOutput.  # noqa: E501
        :type: int
        """

        self._ready_time = ready_time

    @property
    def terminate_time(self):
        """Gets the terminate_time of this ItemForListNodesOutput.  # noqa: E501


        :return: The terminate_time of this ItemForListNodesOutput.  # noqa: E501
        :rtype: int
        """
        return self._terminate_time

    @terminate_time.setter
    def terminate_time(self, terminate_time):
        """Sets the terminate_time of this ItemForListNodesOutput.


        :param terminate_time: The terminate_time of this ItemForListNodesOutput.  # noqa: E501
        :type: int
        """

        self._terminate_time = terminate_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListNodesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListNodesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListNodesOutput):
            return True

        return self.to_dict() != other.to_dict()
