# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RscInfoForDescribeReservedStorageCapacityOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'expired_time': 'str',
        'id': 'str',
        'name': 'str',
        'rsc_auto_renew_period': 'int',
        'remaining_size': 'int',
        'renewal_status': 'str',
        'size': 'int',
        'status': 'str',
        'take_effect_time': 'str',
        'type': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'expired_time': 'ExpiredTime',
        'id': 'Id',
        'name': 'Name',
        'rsc_auto_renew_period': 'RSCAutoRenewPeriod',
        'remaining_size': 'RemainingSize',
        'renewal_status': 'RenewalStatus',
        'size': 'Size',
        'status': 'Status',
        'take_effect_time': 'TakeEffectTime',
        'type': 'Type',
        'zone_id': 'ZoneId'
    }

    def __init__(self, account_id=None, expired_time=None, id=None, name=None, rsc_auto_renew_period=None, remaining_size=None, renewal_status=None, size=None, status=None, take_effect_time=None, type=None, zone_id=None, _configuration=None):  # noqa: E501
        """RscInfoForDescribeReservedStorageCapacityOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._expired_time = None
        self._id = None
        self._name = None
        self._rsc_auto_renew_period = None
        self._remaining_size = None
        self._renewal_status = None
        self._size = None
        self._status = None
        self._take_effect_time = None
        self._type = None
        self._zone_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if expired_time is not None:
            self.expired_time = expired_time
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if rsc_auto_renew_period is not None:
            self.rsc_auto_renew_period = rsc_auto_renew_period
        if remaining_size is not None:
            self.remaining_size = remaining_size
        if renewal_status is not None:
            self.renewal_status = renewal_status
        if size is not None:
            self.size = size
        if status is not None:
            self.status = status
        if take_effect_time is not None:
            self.take_effect_time = take_effect_time
        if type is not None:
            self.type = type
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def account_id(self):
        """Gets the account_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The account_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param account_id: The account_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def expired_time(self):
        """Gets the expired_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The expired_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param expired_time: The expired_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def id(self):
        """Gets the id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param id: The id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The name of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param name: The name of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def rsc_auto_renew_period(self):
        """Gets the rsc_auto_renew_period of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The rsc_auto_renew_period of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: int
        """
        return self._rsc_auto_renew_period

    @rsc_auto_renew_period.setter
    def rsc_auto_renew_period(self, rsc_auto_renew_period):
        """Sets the rsc_auto_renew_period of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param rsc_auto_renew_period: The rsc_auto_renew_period of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: int
        """

        self._rsc_auto_renew_period = rsc_auto_renew_period

    @property
    def remaining_size(self):
        """Gets the remaining_size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The remaining_size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: int
        """
        return self._remaining_size

    @remaining_size.setter
    def remaining_size(self, remaining_size):
        """Sets the remaining_size of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param remaining_size: The remaining_size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: int
        """

        self._remaining_size = remaining_size

    @property
    def renewal_status(self):
        """Gets the renewal_status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The renewal_status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._renewal_status

    @renewal_status.setter
    def renewal_status(self, renewal_status):
        """Sets the renewal_status of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param renewal_status: The renewal_status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._renewal_status = renewal_status

    @property
    def size(self):
        """Gets the size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param size: The size of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def status(self):
        """Gets the status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param status: The status of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def take_effect_time(self):
        """Gets the take_effect_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The take_effect_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._take_effect_time

    @take_effect_time.setter
    def take_effect_time(self, take_effect_time):
        """Sets the take_effect_time of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param take_effect_time: The take_effect_time of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._take_effect_time = take_effect_time

    @property
    def type(self):
        """Gets the type of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The type of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param type: The type of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def zone_id(self):
        """Gets the zone_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501


        :return: The zone_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this RscInfoForDescribeReservedStorageCapacityOutput.


        :param zone_id: The zone_id of this RscInfoForDescribeReservedStorageCapacityOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RscInfoForDescribeReservedStorageCapacityOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RscInfoForDescribeReservedStorageCapacityOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RscInfoForDescribeReservedStorageCapacityOutput):
            return True

        return self.to_dict() != other.to_dict()
