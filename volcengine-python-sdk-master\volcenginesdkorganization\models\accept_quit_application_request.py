# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AcceptQuitApplicationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'application_id': 'str',
        'reason': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'application_id': 'ApplicationId',
        'reason': 'Reason'
    }

    def __init__(self, account_id=None, application_id=None, reason=None, _configuration=None):  # noqa: E501
        """AcceptQuitApplicationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._application_id = None
        self._reason = None
        self.discriminator = None

        self.account_id = account_id
        self.application_id = application_id
        if reason is not None:
            self.reason = reason

    @property
    def account_id(self):
        """Gets the account_id of this AcceptQuitApplicationRequest.  # noqa: E501


        :return: The account_id of this AcceptQuitApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this AcceptQuitApplicationRequest.


        :param account_id: The account_id of this AcceptQuitApplicationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_id is None:
            raise ValueError("Invalid value for `account_id`, must not be `None`")  # noqa: E501

        self._account_id = account_id

    @property
    def application_id(self):
        """Gets the application_id of this AcceptQuitApplicationRequest.  # noqa: E501


        :return: The application_id of this AcceptQuitApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._application_id

    @application_id.setter
    def application_id(self, application_id):
        """Sets the application_id of this AcceptQuitApplicationRequest.


        :param application_id: The application_id of this AcceptQuitApplicationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and application_id is None:
            raise ValueError("Invalid value for `application_id`, must not be `None`")  # noqa: E501

        self._application_id = application_id

    @property
    def reason(self):
        """Gets the reason of this AcceptQuitApplicationRequest.  # noqa: E501


        :return: The reason of this AcceptQuitApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this AcceptQuitApplicationRequest.


        :param reason: The reason of this AcceptQuitApplicationRequest.  # noqa: E501
        :type: str
        """

        self._reason = reason

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AcceptQuitApplicationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AcceptQuitApplicationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AcceptQuitApplicationRequest):
            return True

        return self.to_dict() != other.to_dict()
