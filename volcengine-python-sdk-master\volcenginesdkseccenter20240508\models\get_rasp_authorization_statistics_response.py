# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRaspAuthorizationStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authorization_count': 'int',
        'expire_time': 'int',
        'left_authorization_count': 'int'
    }

    attribute_map = {
        'authorization_count': 'AuthorizationCount',
        'expire_time': 'ExpireTime',
        'left_authorization_count': 'LeftAuthorizationCount'
    }

    def __init__(self, authorization_count=None, expire_time=None, left_authorization_count=None, _configuration=None):  # noqa: E501
        """GetRaspAuthorizationStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authorization_count = None
        self._expire_time = None
        self._left_authorization_count = None
        self.discriminator = None

        if authorization_count is not None:
            self.authorization_count = authorization_count
        if expire_time is not None:
            self.expire_time = expire_time
        if left_authorization_count is not None:
            self.left_authorization_count = left_authorization_count

    @property
    def authorization_count(self):
        """Gets the authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501


        :return: The authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._authorization_count

    @authorization_count.setter
    def authorization_count(self, authorization_count):
        """Sets the authorization_count of this GetRaspAuthorizationStatisticsResponse.


        :param authorization_count: The authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._authorization_count = authorization_count

    @property
    def expire_time(self):
        """Gets the expire_time of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501


        :return: The expire_time of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this GetRaspAuthorizationStatisticsResponse.


        :param expire_time: The expire_time of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def left_authorization_count(self):
        """Gets the left_authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501


        :return: The left_authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._left_authorization_count

    @left_authorization_count.setter
    def left_authorization_count(self, left_authorization_count):
        """Sets the left_authorization_count of this GetRaspAuthorizationStatisticsResponse.


        :param left_authorization_count: The left_authorization_count of this GetRaspAuthorizationStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._left_authorization_count = left_authorization_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRaspAuthorizationStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRaspAuthorizationStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRaspAuthorizationStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
