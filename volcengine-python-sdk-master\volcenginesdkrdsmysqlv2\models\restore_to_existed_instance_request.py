# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreToExistedInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_id': 'str',
        'fast_mode': 'bool',
        'restore_time': 'str',
        'restore_type': 'str',
        'source_db_instance_id': 'str',
        'table_meta': 'list[TableMetaForRestoreToExistedInstanceInput]'
    }

    attribute_map = {
        'backup_id': 'BackupId',
        'fast_mode': 'FastMode',
        'restore_time': 'RestoreTime',
        'restore_type': 'RestoreType',
        'source_db_instance_id': 'SourceDBInstanceId',
        'table_meta': 'TableMeta'
    }

    def __init__(self, backup_id=None, fast_mode=None, restore_time=None, restore_type=None, source_db_instance_id=None, table_meta=None, _configuration=None):  # noqa: E501
        """RestoreToExistedInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_id = None
        self._fast_mode = None
        self._restore_time = None
        self._restore_type = None
        self._source_db_instance_id = None
        self._table_meta = None
        self.discriminator = None

        if backup_id is not None:
            self.backup_id = backup_id
        if fast_mode is not None:
            self.fast_mode = fast_mode
        if restore_time is not None:
            self.restore_time = restore_time
        if restore_type is not None:
            self.restore_type = restore_type
        self.source_db_instance_id = source_db_instance_id
        if table_meta is not None:
            self.table_meta = table_meta

    @property
    def backup_id(self):
        """Gets the backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this RestoreToExistedInstanceRequest.


        :param backup_id: The backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def fast_mode(self):
        """Gets the fast_mode of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The fast_mode of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._fast_mode

    @fast_mode.setter
    def fast_mode(self, fast_mode):
        """Sets the fast_mode of this RestoreToExistedInstanceRequest.


        :param fast_mode: The fast_mode of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: bool
        """

        self._fast_mode = fast_mode

    @property
    def restore_time(self):
        """Gets the restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_time

    @restore_time.setter
    def restore_time(self, restore_time):
        """Sets the restore_time of this RestoreToExistedInstanceRequest.


        :param restore_time: The restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """

        self._restore_time = restore_time

    @property
    def restore_type(self):
        """Gets the restore_type of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The restore_type of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_type

    @restore_type.setter
    def restore_type(self, restore_type):
        """Sets the restore_type of this RestoreToExistedInstanceRequest.


        :param restore_type: The restore_type of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """

        self._restore_type = restore_type

    @property
    def source_db_instance_id(self):
        """Gets the source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_db_instance_id

    @source_db_instance_id.setter
    def source_db_instance_id(self, source_db_instance_id):
        """Sets the source_db_instance_id of this RestoreToExistedInstanceRequest.


        :param source_db_instance_id: The source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_db_instance_id is None:
            raise ValueError("Invalid value for `source_db_instance_id`, must not be `None`")  # noqa: E501

        self._source_db_instance_id = source_db_instance_id

    @property
    def table_meta(self):
        """Gets the table_meta of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The table_meta of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: list[TableMetaForRestoreToExistedInstanceInput]
        """
        return self._table_meta

    @table_meta.setter
    def table_meta(self, table_meta):
        """Sets the table_meta of this RestoreToExistedInstanceRequest.


        :param table_meta: The table_meta of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: list[TableMetaForRestoreToExistedInstanceInput]
        """

        self._table_meta = table_meta

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreToExistedInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreToExistedInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreToExistedInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
