# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BodyForGetAwardConfigListAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_condition': 'AwardConditionForGetAwardConfigListAPIOutput',
        'award_count': 'int',
        'award_item_amounts': 'int',
        'award_item_num': 'str',
        'award_item_type': 'int',
        'award_lottery_ticket_addr': 'str',
        'award_num': 'int',
        'award_theme': 'str',
        'award_type': 'int',
        'barrage_pwd': 'str',
        'condition_type': 'int',
        'condition_variable': 'int',
        'dead_line': 'int',
        'id': 'int',
        'msg_ids': 'str',
        'name': 'str',
        'open_award_time': 'int',
        'range_status': 'int',
        'send_time': 'int',
        'show_people_number': 'int',
        'show_winner_comment': 'int',
        'status': 'int',
        'task_award_icon': 'str',
        'total_count': 'int',
        'vip_user_list': 'str',
        'winner_info_type': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_condition': 'AwardCondition',
        'award_count': 'AwardCount',
        'award_item_amounts': 'AwardItemAmounts',
        'award_item_num': 'AwardItemNum',
        'award_item_type': 'AwardItemType',
        'award_lottery_ticket_addr': 'AwardLotteryTicketAddr',
        'award_num': 'AwardNum',
        'award_theme': 'AwardTheme',
        'award_type': 'AwardType',
        'barrage_pwd': 'BarragePwd',
        'condition_type': 'ConditionType',
        'condition_variable': 'ConditionVariable',
        'dead_line': 'DeadLine',
        'id': 'ID',
        'msg_ids': 'MsgIds',
        'name': 'Name',
        'open_award_time': 'OpenAwardTime',
        'range_status': 'RangeStatus',
        'send_time': 'SendTime',
        'show_people_number': 'ShowPeopleNumber',
        'show_winner_comment': 'ShowWinnerComment',
        'status': 'Status',
        'task_award_icon': 'TaskAwardIcon',
        'total_count': 'TotalCount',
        'vip_user_list': 'VIPUserList',
        'winner_info_type': 'WinnerInfoType'
    }

    def __init__(self, activity_id=None, award_condition=None, award_count=None, award_item_amounts=None, award_item_num=None, award_item_type=None, award_lottery_ticket_addr=None, award_num=None, award_theme=None, award_type=None, barrage_pwd=None, condition_type=None, condition_variable=None, dead_line=None, id=None, msg_ids=None, name=None, open_award_time=None, range_status=None, send_time=None, show_people_number=None, show_winner_comment=None, status=None, task_award_icon=None, total_count=None, vip_user_list=None, winner_info_type=None, _configuration=None):  # noqa: E501
        """BodyForGetAwardConfigListAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_condition = None
        self._award_count = None
        self._award_item_amounts = None
        self._award_item_num = None
        self._award_item_type = None
        self._award_lottery_ticket_addr = None
        self._award_num = None
        self._award_theme = None
        self._award_type = None
        self._barrage_pwd = None
        self._condition_type = None
        self._condition_variable = None
        self._dead_line = None
        self._id = None
        self._msg_ids = None
        self._name = None
        self._open_award_time = None
        self._range_status = None
        self._send_time = None
        self._show_people_number = None
        self._show_winner_comment = None
        self._status = None
        self._task_award_icon = None
        self._total_count = None
        self._vip_user_list = None
        self._winner_info_type = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if award_condition is not None:
            self.award_condition = award_condition
        if award_count is not None:
            self.award_count = award_count
        if award_item_amounts is not None:
            self.award_item_amounts = award_item_amounts
        if award_item_num is not None:
            self.award_item_num = award_item_num
        if award_item_type is not None:
            self.award_item_type = award_item_type
        if award_lottery_ticket_addr is not None:
            self.award_lottery_ticket_addr = award_lottery_ticket_addr
        if award_num is not None:
            self.award_num = award_num
        if award_theme is not None:
            self.award_theme = award_theme
        if award_type is not None:
            self.award_type = award_type
        if barrage_pwd is not None:
            self.barrage_pwd = barrage_pwd
        if condition_type is not None:
            self.condition_type = condition_type
        if condition_variable is not None:
            self.condition_variable = condition_variable
        if dead_line is not None:
            self.dead_line = dead_line
        if id is not None:
            self.id = id
        if msg_ids is not None:
            self.msg_ids = msg_ids
        if name is not None:
            self.name = name
        if open_award_time is not None:
            self.open_award_time = open_award_time
        if range_status is not None:
            self.range_status = range_status
        if send_time is not None:
            self.send_time = send_time
        if show_people_number is not None:
            self.show_people_number = show_people_number
        if show_winner_comment is not None:
            self.show_winner_comment = show_winner_comment
        if status is not None:
            self.status = status
        if task_award_icon is not None:
            self.task_award_icon = task_award_icon
        if total_count is not None:
            self.total_count = total_count
        if vip_user_list is not None:
            self.vip_user_list = vip_user_list
        if winner_info_type is not None:
            self.winner_info_type = winner_info_type

    @property
    def activity_id(self):
        """Gets the activity_id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The activity_id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this BodyForGetAwardConfigListAPIOutput.


        :param activity_id: The activity_id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def award_condition(self):
        """Gets the award_condition of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_condition of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: AwardConditionForGetAwardConfigListAPIOutput
        """
        return self._award_condition

    @award_condition.setter
    def award_condition(self, award_condition):
        """Sets the award_condition of this BodyForGetAwardConfigListAPIOutput.


        :param award_condition: The award_condition of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: AwardConditionForGetAwardConfigListAPIOutput
        """

        self._award_condition = award_condition

    @property
    def award_count(self):
        """Gets the award_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_count

    @award_count.setter
    def award_count(self, award_count):
        """Sets the award_count of this BodyForGetAwardConfigListAPIOutput.


        :param award_count: The award_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_count = award_count

    @property
    def award_item_amounts(self):
        """Gets the award_item_amounts of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_item_amounts of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_amounts

    @award_item_amounts.setter
    def award_item_amounts(self, award_item_amounts):
        """Sets the award_item_amounts of this BodyForGetAwardConfigListAPIOutput.


        :param award_item_amounts: The award_item_amounts of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_item_amounts = award_item_amounts

    @property
    def award_item_num(self):
        """Gets the award_item_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_item_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_num

    @award_item_num.setter
    def award_item_num(self, award_item_num):
        """Sets the award_item_num of this BodyForGetAwardConfigListAPIOutput.


        :param award_item_num: The award_item_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_item_num = award_item_num

    @property
    def award_item_type(self):
        """Gets the award_item_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_item_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_type

    @award_item_type.setter
    def award_item_type(self, award_item_type):
        """Sets the award_item_type of this BodyForGetAwardConfigListAPIOutput.


        :param award_item_type: The award_item_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_item_type = award_item_type

    @property
    def award_lottery_ticket_addr(self):
        """Gets the award_lottery_ticket_addr of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_lottery_ticket_addr of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_lottery_ticket_addr

    @award_lottery_ticket_addr.setter
    def award_lottery_ticket_addr(self, award_lottery_ticket_addr):
        """Sets the award_lottery_ticket_addr of this BodyForGetAwardConfigListAPIOutput.


        :param award_lottery_ticket_addr: The award_lottery_ticket_addr of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_lottery_ticket_addr = award_lottery_ticket_addr

    @property
    def award_num(self):
        """Gets the award_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_num

    @award_num.setter
    def award_num(self, award_num):
        """Sets the award_num of this BodyForGetAwardConfigListAPIOutput.


        :param award_num: The award_num of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_num = award_num

    @property
    def award_theme(self):
        """Gets the award_theme of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_theme of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_theme

    @award_theme.setter
    def award_theme(self, award_theme):
        """Sets the award_theme of this BodyForGetAwardConfigListAPIOutput.


        :param award_theme: The award_theme of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_theme = award_theme

    @property
    def award_type(self):
        """Gets the award_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The award_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_type

    @award_type.setter
    def award_type(self, award_type):
        """Sets the award_type of this BodyForGetAwardConfigListAPIOutput.


        :param award_type: The award_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_type = award_type

    @property
    def barrage_pwd(self):
        """Gets the barrage_pwd of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The barrage_pwd of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._barrage_pwd

    @barrage_pwd.setter
    def barrage_pwd(self, barrage_pwd):
        """Sets the barrage_pwd of this BodyForGetAwardConfigListAPIOutput.


        :param barrage_pwd: The barrage_pwd of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._barrage_pwd = barrage_pwd

    @property
    def condition_type(self):
        """Gets the condition_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The condition_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._condition_type

    @condition_type.setter
    def condition_type(self, condition_type):
        """Sets the condition_type of this BodyForGetAwardConfigListAPIOutput.


        :param condition_type: The condition_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._condition_type = condition_type

    @property
    def condition_variable(self):
        """Gets the condition_variable of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The condition_variable of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._condition_variable

    @condition_variable.setter
    def condition_variable(self, condition_variable):
        """Sets the condition_variable of this BodyForGetAwardConfigListAPIOutput.


        :param condition_variable: The condition_variable of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._condition_variable = condition_variable

    @property
    def dead_line(self):
        """Gets the dead_line of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The dead_line of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._dead_line

    @dead_line.setter
    def dead_line(self, dead_line):
        """Sets the dead_line of this BodyForGetAwardConfigListAPIOutput.


        :param dead_line: The dead_line of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._dead_line = dead_line

    @property
    def id(self):
        """Gets the id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BodyForGetAwardConfigListAPIOutput.


        :param id: The id of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def msg_ids(self):
        """Gets the msg_ids of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The msg_ids of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._msg_ids

    @msg_ids.setter
    def msg_ids(self, msg_ids):
        """Sets the msg_ids of this BodyForGetAwardConfigListAPIOutput.


        :param msg_ids: The msg_ids of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._msg_ids = msg_ids

    @property
    def name(self):
        """Gets the name of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The name of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BodyForGetAwardConfigListAPIOutput.


        :param name: The name of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def open_award_time(self):
        """Gets the open_award_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The open_award_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._open_award_time

    @open_award_time.setter
    def open_award_time(self, open_award_time):
        """Sets the open_award_time of this BodyForGetAwardConfigListAPIOutput.


        :param open_award_time: The open_award_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._open_award_time = open_award_time

    @property
    def range_status(self):
        """Gets the range_status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The range_status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._range_status

    @range_status.setter
    def range_status(self, range_status):
        """Sets the range_status of this BodyForGetAwardConfigListAPIOutput.


        :param range_status: The range_status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._range_status = range_status

    @property
    def send_time(self):
        """Gets the send_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The send_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this BodyForGetAwardConfigListAPIOutput.


        :param send_time: The send_time of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def show_people_number(self):
        """Gets the show_people_number of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The show_people_number of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._show_people_number

    @show_people_number.setter
    def show_people_number(self, show_people_number):
        """Sets the show_people_number of this BodyForGetAwardConfigListAPIOutput.


        :param show_people_number: The show_people_number of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._show_people_number = show_people_number

    @property
    def show_winner_comment(self):
        """Gets the show_winner_comment of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The show_winner_comment of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._show_winner_comment

    @show_winner_comment.setter
    def show_winner_comment(self, show_winner_comment):
        """Sets the show_winner_comment of this BodyForGetAwardConfigListAPIOutput.


        :param show_winner_comment: The show_winner_comment of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._show_winner_comment = show_winner_comment

    @property
    def status(self):
        """Gets the status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this BodyForGetAwardConfigListAPIOutput.


        :param status: The status of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def task_award_icon(self):
        """Gets the task_award_icon of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The task_award_icon of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_award_icon

    @task_award_icon.setter
    def task_award_icon(self, task_award_icon):
        """Sets the task_award_icon of this BodyForGetAwardConfigListAPIOutput.


        :param task_award_icon: The task_award_icon of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._task_award_icon = task_award_icon

    @property
    def total_count(self):
        """Gets the total_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The total_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this BodyForGetAwardConfigListAPIOutput.


        :param total_count: The total_count of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def vip_user_list(self):
        """Gets the vip_user_list of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The vip_user_list of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._vip_user_list

    @vip_user_list.setter
    def vip_user_list(self, vip_user_list):
        """Sets the vip_user_list of this BodyForGetAwardConfigListAPIOutput.


        :param vip_user_list: The vip_user_list of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._vip_user_list = vip_user_list

    @property
    def winner_info_type(self):
        """Gets the winner_info_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501


        :return: The winner_info_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._winner_info_type

    @winner_info_type.setter
    def winner_info_type(self, winner_info_type):
        """Sets the winner_info_type of this BodyForGetAwardConfigListAPIOutput.


        :param winner_info_type: The winner_info_type of this BodyForGetAwardConfigListAPIOutput.  # noqa: E501
        :type: str
        """

        self._winner_info_type = winner_info_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BodyForGetAwardConfigListAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BodyForGetAwardConfigListAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BodyForGetAwardConfigListAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
