# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserTrackForGetAccountUserTrackDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'activity_name': 'str',
        'comment_count': 'int',
        'join_time': 'int',
        'lottery_awards': 'list[str]',
        'pay_amount': 'str',
        'watch_duration_live': 'int',
        'watch_duration_replay': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'activity_name': 'ActivityName',
        'comment_count': 'CommentCount',
        'join_time': 'JoinTime',
        'lottery_awards': 'LotteryAwards',
        'pay_amount': 'PayAmount',
        'watch_duration_live': 'WatchDurationLive',
        'watch_duration_replay': 'WatchDurationReplay'
    }

    def __init__(self, activity_id=None, activity_name=None, comment_count=None, join_time=None, lottery_awards=None, pay_amount=None, watch_duration_live=None, watch_duration_replay=None, _configuration=None):  # noqa: E501
        """UserTrackForGetAccountUserTrackDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._activity_name = None
        self._comment_count = None
        self._join_time = None
        self._lottery_awards = None
        self._pay_amount = None
        self._watch_duration_live = None
        self._watch_duration_replay = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if activity_name is not None:
            self.activity_name = activity_name
        if comment_count is not None:
            self.comment_count = comment_count
        if join_time is not None:
            self.join_time = join_time
        if lottery_awards is not None:
            self.lottery_awards = lottery_awards
        if pay_amount is not None:
            self.pay_amount = pay_amount
        if watch_duration_live is not None:
            self.watch_duration_live = watch_duration_live
        if watch_duration_replay is not None:
            self.watch_duration_replay = watch_duration_replay

    @property
    def activity_id(self):
        """Gets the activity_id of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The activity_id of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UserTrackForGetAccountUserTrackDataOutput.


        :param activity_id: The activity_id of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def activity_name(self):
        """Gets the activity_name of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The activity_name of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._activity_name

    @activity_name.setter
    def activity_name(self, activity_name):
        """Sets the activity_name of this UserTrackForGetAccountUserTrackDataOutput.


        :param activity_name: The activity_name of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: str
        """

        self._activity_name = activity_name

    @property
    def comment_count(self):
        """Gets the comment_count of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The comment_count of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._comment_count

    @comment_count.setter
    def comment_count(self, comment_count):
        """Sets the comment_count of this UserTrackForGetAccountUserTrackDataOutput.


        :param comment_count: The comment_count of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: int
        """

        self._comment_count = comment_count

    @property
    def join_time(self):
        """Gets the join_time of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The join_time of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._join_time

    @join_time.setter
    def join_time(self, join_time):
        """Sets the join_time of this UserTrackForGetAccountUserTrackDataOutput.


        :param join_time: The join_time of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: int
        """

        self._join_time = join_time

    @property
    def lottery_awards(self):
        """Gets the lottery_awards of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The lottery_awards of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._lottery_awards

    @lottery_awards.setter
    def lottery_awards(self, lottery_awards):
        """Sets the lottery_awards of this UserTrackForGetAccountUserTrackDataOutput.


        :param lottery_awards: The lottery_awards of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: list[str]
        """

        self._lottery_awards = lottery_awards

    @property
    def pay_amount(self):
        """Gets the pay_amount of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The pay_amount of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._pay_amount

    @pay_amount.setter
    def pay_amount(self, pay_amount):
        """Sets the pay_amount of this UserTrackForGetAccountUserTrackDataOutput.


        :param pay_amount: The pay_amount of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: str
        """

        self._pay_amount = pay_amount

    @property
    def watch_duration_live(self):
        """Gets the watch_duration_live of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The watch_duration_live of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_duration_live

    @watch_duration_live.setter
    def watch_duration_live(self, watch_duration_live):
        """Sets the watch_duration_live of this UserTrackForGetAccountUserTrackDataOutput.


        :param watch_duration_live: The watch_duration_live of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_duration_live = watch_duration_live

    @property
    def watch_duration_replay(self):
        """Gets the watch_duration_replay of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The watch_duration_replay of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_duration_replay

    @watch_duration_replay.setter
    def watch_duration_replay(self, watch_duration_replay):
        """Sets the watch_duration_replay of this UserTrackForGetAccountUserTrackDataOutput.


        :param watch_duration_replay: The watch_duration_replay of this UserTrackForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_duration_replay = watch_duration_replay

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserTrackForGetAccountUserTrackDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserTrackForGetAccountUserTrackDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserTrackForGetAccountUserTrackDataOutput):
            return True

        return self.to_dict() != other.to_dict()
