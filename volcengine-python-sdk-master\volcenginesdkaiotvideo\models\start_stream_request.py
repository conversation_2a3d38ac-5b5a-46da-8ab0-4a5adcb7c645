# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartStreamRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_audio_transcode': 'str',
        'fresh_expired_pull': 'str',
        'ignore_audio': 'str',
        'resolution': 'str',
        'space_id': 'str',
        'stream_id': 'str',
        'streaming_index': 'int'
    }

    attribute_map = {
        'enable_audio_transcode': 'EnableAudioTranscode',
        'fresh_expired_pull': 'FreshExpiredPull',
        'ignore_audio': 'IgnoreAudio',
        'resolution': 'Resolution',
        'space_id': 'SpaceID',
        'stream_id': 'StreamID',
        'streaming_index': 'StreamingIndex'
    }

    def __init__(self, enable_audio_transcode=None, fresh_expired_pull=None, ignore_audio=None, resolution=None, space_id=None, stream_id=None, streaming_index=None, _configuration=None):  # noqa: E501
        """StartStreamRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_audio_transcode = None
        self._fresh_expired_pull = None
        self._ignore_audio = None
        self._resolution = None
        self._space_id = None
        self._stream_id = None
        self._streaming_index = None
        self.discriminator = None

        if enable_audio_transcode is not None:
            self.enable_audio_transcode = enable_audio_transcode
        if fresh_expired_pull is not None:
            self.fresh_expired_pull = fresh_expired_pull
        if ignore_audio is not None:
            self.ignore_audio = ignore_audio
        if resolution is not None:
            self.resolution = resolution
        if space_id is not None:
            self.space_id = space_id
        self.stream_id = stream_id
        if streaming_index is not None:
            self.streaming_index = streaming_index

    @property
    def enable_audio_transcode(self):
        """Gets the enable_audio_transcode of this StartStreamRequest.  # noqa: E501


        :return: The enable_audio_transcode of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._enable_audio_transcode

    @enable_audio_transcode.setter
    def enable_audio_transcode(self, enable_audio_transcode):
        """Sets the enable_audio_transcode of this StartStreamRequest.


        :param enable_audio_transcode: The enable_audio_transcode of this StartStreamRequest.  # noqa: E501
        :type: str
        """

        self._enable_audio_transcode = enable_audio_transcode

    @property
    def fresh_expired_pull(self):
        """Gets the fresh_expired_pull of this StartStreamRequest.  # noqa: E501


        :return: The fresh_expired_pull of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._fresh_expired_pull

    @fresh_expired_pull.setter
    def fresh_expired_pull(self, fresh_expired_pull):
        """Sets the fresh_expired_pull of this StartStreamRequest.


        :param fresh_expired_pull: The fresh_expired_pull of this StartStreamRequest.  # noqa: E501
        :type: str
        """

        self._fresh_expired_pull = fresh_expired_pull

    @property
    def ignore_audio(self):
        """Gets the ignore_audio of this StartStreamRequest.  # noqa: E501


        :return: The ignore_audio of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._ignore_audio

    @ignore_audio.setter
    def ignore_audio(self, ignore_audio):
        """Sets the ignore_audio of this StartStreamRequest.


        :param ignore_audio: The ignore_audio of this StartStreamRequest.  # noqa: E501
        :type: str
        """

        self._ignore_audio = ignore_audio

    @property
    def resolution(self):
        """Gets the resolution of this StartStreamRequest.  # noqa: E501


        :return: The resolution of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this StartStreamRequest.


        :param resolution: The resolution of this StartStreamRequest.  # noqa: E501
        :type: str
        """

        self._resolution = resolution

    @property
    def space_id(self):
        """Gets the space_id of this StartStreamRequest.  # noqa: E501


        :return: The space_id of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this StartStreamRequest.


        :param space_id: The space_id of this StartStreamRequest.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def stream_id(self):
        """Gets the stream_id of this StartStreamRequest.  # noqa: E501


        :return: The stream_id of this StartStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this StartStreamRequest.


        :param stream_id: The stream_id of this StartStreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_id is None:
            raise ValueError("Invalid value for `stream_id`, must not be `None`")  # noqa: E501

        self._stream_id = stream_id

    @property
    def streaming_index(self):
        """Gets the streaming_index of this StartStreamRequest.  # noqa: E501


        :return: The streaming_index of this StartStreamRequest.  # noqa: E501
        :rtype: int
        """
        return self._streaming_index

    @streaming_index.setter
    def streaming_index(self, streaming_index):
        """Sets the streaming_index of this StartStreamRequest.


        :param streaming_index: The streaming_index of this StartStreamRequest.  # noqa: E501
        :type: int
        """

        self._streaming_index = streaming_index

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartStreamRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartStreamRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartStreamRequest):
            return True

        return self.to_dict() != other.to_dict()
