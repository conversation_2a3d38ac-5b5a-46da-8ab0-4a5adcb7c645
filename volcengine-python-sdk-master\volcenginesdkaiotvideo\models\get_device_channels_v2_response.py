# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDeviceChannelsV2Response(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channels': 'list[ChannelForGetDeviceChannelsV2Output]',
        'device_id': 'str',
        'device_nsid': 'str',
        'device_name': 'str'
    }

    attribute_map = {
        'channels': 'Channels',
        'device_id': 'DeviceID',
        'device_nsid': 'DeviceNSID',
        'device_name': 'DeviceName'
    }

    def __init__(self, channels=None, device_id=None, device_nsid=None, device_name=None, _configuration=None):  # noqa: E501
        """GetDeviceChannelsV2Response - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channels = None
        self._device_id = None
        self._device_nsid = None
        self._device_name = None
        self.discriminator = None

        if channels is not None:
            self.channels = channels
        if device_id is not None:
            self.device_id = device_id
        if device_nsid is not None:
            self.device_nsid = device_nsid
        if device_name is not None:
            self.device_name = device_name

    @property
    def channels(self):
        """Gets the channels of this GetDeviceChannelsV2Response.  # noqa: E501


        :return: The channels of this GetDeviceChannelsV2Response.  # noqa: E501
        :rtype: list[ChannelForGetDeviceChannelsV2Output]
        """
        return self._channels

    @channels.setter
    def channels(self, channels):
        """Sets the channels of this GetDeviceChannelsV2Response.


        :param channels: The channels of this GetDeviceChannelsV2Response.  # noqa: E501
        :type: list[ChannelForGetDeviceChannelsV2Output]
        """

        self._channels = channels

    @property
    def device_id(self):
        """Gets the device_id of this GetDeviceChannelsV2Response.  # noqa: E501


        :return: The device_id of this GetDeviceChannelsV2Response.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this GetDeviceChannelsV2Response.


        :param device_id: The device_id of this GetDeviceChannelsV2Response.  # noqa: E501
        :type: str
        """

        self._device_id = device_id

    @property
    def device_nsid(self):
        """Gets the device_nsid of this GetDeviceChannelsV2Response.  # noqa: E501


        :return: The device_nsid of this GetDeviceChannelsV2Response.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this GetDeviceChannelsV2Response.


        :param device_nsid: The device_nsid of this GetDeviceChannelsV2Response.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def device_name(self):
        """Gets the device_name of this GetDeviceChannelsV2Response.  # noqa: E501


        :return: The device_name of this GetDeviceChannelsV2Response.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this GetDeviceChannelsV2Response.


        :param device_name: The device_name of this GetDeviceChannelsV2Response.  # noqa: E501
        :type: str
        """

        self._device_name = device_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDeviceChannelsV2Response, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDeviceChannelsV2Response):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDeviceChannelsV2Response):
            return True

        return self.to_dict() != other.to_dict()
