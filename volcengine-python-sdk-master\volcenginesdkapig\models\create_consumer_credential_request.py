# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateConsumerCredentialRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'consumer_id': 'str',
        'credential_type': 'str',
        'hmac_auth_credential': 'HmacAuthCredentialForCreateConsumerCredentialInput',
        'key_auth_credential': 'KeyAuthCredentialForCreateConsumerCredentialInput'
    }

    attribute_map = {
        'consumer_id': 'ConsumerId',
        'credential_type': 'CredentialType',
        'hmac_auth_credential': 'HmacAuthCredential',
        'key_auth_credential': 'KeyAuthCredential'
    }

    def __init__(self, consumer_id=None, credential_type=None, hmac_auth_credential=None, key_auth_credential=None, _configuration=None):  # noqa: E501
        """CreateConsumerCredentialRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._consumer_id = None
        self._credential_type = None
        self._hmac_auth_credential = None
        self._key_auth_credential = None
        self.discriminator = None

        self.consumer_id = consumer_id
        self.credential_type = credential_type
        if hmac_auth_credential is not None:
            self.hmac_auth_credential = hmac_auth_credential
        if key_auth_credential is not None:
            self.key_auth_credential = key_auth_credential

    @property
    def consumer_id(self):
        """Gets the consumer_id of this CreateConsumerCredentialRequest.  # noqa: E501


        :return: The consumer_id of this CreateConsumerCredentialRequest.  # noqa: E501
        :rtype: str
        """
        return self._consumer_id

    @consumer_id.setter
    def consumer_id(self, consumer_id):
        """Sets the consumer_id of this CreateConsumerCredentialRequest.


        :param consumer_id: The consumer_id of this CreateConsumerCredentialRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and consumer_id is None:
            raise ValueError("Invalid value for `consumer_id`, must not be `None`")  # noqa: E501

        self._consumer_id = consumer_id

    @property
    def credential_type(self):
        """Gets the credential_type of this CreateConsumerCredentialRequest.  # noqa: E501


        :return: The credential_type of this CreateConsumerCredentialRequest.  # noqa: E501
        :rtype: str
        """
        return self._credential_type

    @credential_type.setter
    def credential_type(self, credential_type):
        """Sets the credential_type of this CreateConsumerCredentialRequest.


        :param credential_type: The credential_type of this CreateConsumerCredentialRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and credential_type is None:
            raise ValueError("Invalid value for `credential_type`, must not be `None`")  # noqa: E501

        self._credential_type = credential_type

    @property
    def hmac_auth_credential(self):
        """Gets the hmac_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501


        :return: The hmac_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501
        :rtype: HmacAuthCredentialForCreateConsumerCredentialInput
        """
        return self._hmac_auth_credential

    @hmac_auth_credential.setter
    def hmac_auth_credential(self, hmac_auth_credential):
        """Sets the hmac_auth_credential of this CreateConsumerCredentialRequest.


        :param hmac_auth_credential: The hmac_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501
        :type: HmacAuthCredentialForCreateConsumerCredentialInput
        """

        self._hmac_auth_credential = hmac_auth_credential

    @property
    def key_auth_credential(self):
        """Gets the key_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501


        :return: The key_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501
        :rtype: KeyAuthCredentialForCreateConsumerCredentialInput
        """
        return self._key_auth_credential

    @key_auth_credential.setter
    def key_auth_credential(self, key_auth_credential):
        """Sets the key_auth_credential of this CreateConsumerCredentialRequest.


        :param key_auth_credential: The key_auth_credential of this CreateConsumerCredentialRequest.  # noqa: E501
        :type: KeyAuthCredentialForCreateConsumerCredentialInput
        """

        self._key_auth_credential = key_auth_credential

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateConsumerCredentialRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateConsumerCredentialRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateConsumerCredentialRequest):
            return True

        return self.to_dict() != other.to_dict()
