# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatFileScanTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'agent_id_list': 'list[str]',
        'all_host': 'bool',
        'cloud_providers': 'list[str]',
        'cpu_i_dle': 'str',
        'file_path': 'str',
        'leaf_group_ids': 'list[str]',
        'task_name': 'str',
        'timeout': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'agent_id_list': 'AgentIDList',
        'all_host': 'AllHost',
        'cloud_providers': 'CloudProviders',
        'cpu_i_dle': 'CpuIDle',
        'file_path': 'FilePath',
        'leaf_group_ids': 'LeafGroupIDs',
        'task_name': 'TaskName',
        'timeout': 'Timeout',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, action=None, agent_id_list=None, all_host=None, cloud_providers=None, cpu_i_dle=None, file_path=None, leaf_group_ids=None, task_name=None, timeout=None, top_group_id=None, _configuration=None):  # noqa: E501
        """CreatFileScanTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._agent_id_list = None
        self._all_host = None
        self._cloud_providers = None
        self._cpu_i_dle = None
        self._file_path = None
        self._leaf_group_ids = None
        self._task_name = None
        self._timeout = None
        self._top_group_id = None
        self.discriminator = None

        self.action = action
        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if all_host is not None:
            self.all_host = all_host
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cpu_i_dle is not None:
            self.cpu_i_dle = cpu_i_dle
        if file_path is not None:
            self.file_path = file_path
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if task_name is not None:
            self.task_name = task_name
        if timeout is not None:
            self.timeout = timeout
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def action(self):
        """Gets the action of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The action of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this CreatFileScanTaskRequest.


        :param action: The action of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and action is None:
            raise ValueError("Invalid value for `action`, must not be `None`")  # noqa: E501

        self._action = action

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The agent_id_list of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this CreatFileScanTaskRequest.


        :param agent_id_list: The agent_id_list of this CreatFileScanTaskRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def all_host(self):
        """Gets the all_host of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The all_host of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_host

    @all_host.setter
    def all_host(self, all_host):
        """Sets the all_host of this CreatFileScanTaskRequest.


        :param all_host: The all_host of this CreatFileScanTaskRequest.  # noqa: E501
        :type: bool
        """

        self._all_host = all_host

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The cloud_providers of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this CreatFileScanTaskRequest.


        :param cloud_providers: The cloud_providers of this CreatFileScanTaskRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cpu_i_dle(self):
        """Gets the cpu_i_dle of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The cpu_i_dle of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._cpu_i_dle

    @cpu_i_dle.setter
    def cpu_i_dle(self, cpu_i_dle):
        """Sets the cpu_i_dle of this CreatFileScanTaskRequest.


        :param cpu_i_dle: The cpu_i_dle of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """

        self._cpu_i_dle = cpu_i_dle

    @property
    def file_path(self):
        """Gets the file_path of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The file_path of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this CreatFileScanTaskRequest.


        :param file_path: The file_path of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The leaf_group_ids of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this CreatFileScanTaskRequest.


        :param leaf_group_ids: The leaf_group_ids of this CreatFileScanTaskRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def task_name(self):
        """Gets the task_name of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The task_name of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this CreatFileScanTaskRequest.


        :param task_name: The task_name of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    @property
    def timeout(self):
        """Gets the timeout of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The timeout of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this CreatFileScanTaskRequest.


        :param timeout: The timeout of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """

        self._timeout = timeout

    @property
    def top_group_id(self):
        """Gets the top_group_id of this CreatFileScanTaskRequest.  # noqa: E501


        :return: The top_group_id of this CreatFileScanTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this CreatFileScanTaskRequest.


        :param top_group_id: The top_group_id of this CreatFileScanTaskRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatFileScanTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatFileScanTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatFileScanTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
