# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeMountServiceNodeTypesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'node_type_infos': 'list[NodeTypeInfoForDescribeMountServiceNodeTypesOutput]'
    }

    attribute_map = {
        'node_type_infos': 'NodeTypeInfos'
    }

    def __init__(self, node_type_infos=None, _configuration=None):  # noqa: E501
        """DescribeMountServiceNodeTypesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._node_type_infos = None
        self.discriminator = None

        if node_type_infos is not None:
            self.node_type_infos = node_type_infos

    @property
    def node_type_infos(self):
        """Gets the node_type_infos of this DescribeMountServiceNodeTypesResponse.  # noqa: E501


        :return: The node_type_infos of this DescribeMountServiceNodeTypesResponse.  # noqa: E501
        :rtype: list[NodeTypeInfoForDescribeMountServiceNodeTypesOutput]
        """
        return self._node_type_infos

    @node_type_infos.setter
    def node_type_infos(self, node_type_infos):
        """Sets the node_type_infos of this DescribeMountServiceNodeTypesResponse.


        :param node_type_infos: The node_type_infos of this DescribeMountServiceNodeTypesResponse.  # noqa: E501
        :type: list[NodeTypeInfoForDescribeMountServiceNodeTypesOutput]
        """

        self._node_type_infos = node_type_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeMountServiceNodeTypesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeMountServiceNodeTypesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeMountServiceNodeTypesResponse):
            return True

        return self.to_dict() != other.to_dict()
