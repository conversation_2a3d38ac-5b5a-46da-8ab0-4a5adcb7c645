# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'vgw_id': 'str',
        'x_coordinate': 'int',
        'y_coordinate': 'int'
    }

    attribute_map = {
        'vgw_id': 'VgwID',
        'x_coordinate': 'XCoordinate',
        'y_coordinate': 'YCoordinate'
    }

    def __init__(self, vgw_id=None, x_coordinate=None, y_coordinate=None, _configuration=None):  # noqa: E501
        """TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._vgw_id = None
        self._x_coordinate = None
        self._y_coordinate = None
        self.discriminator = None

        if vgw_id is not None:
            self.vgw_id = vgw_id
        if x_coordinate is not None:
            self.x_coordinate = x_coordinate
        if y_coordinate is not None:
            self.y_coordinate = y_coordinate

    @property
    def vgw_id(self):
        """Gets the vgw_id of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501


        :return: The vgw_id of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :rtype: str
        """
        return self._vgw_id

    @vgw_id.setter
    def vgw_id(self, vgw_id):
        """Sets the vgw_id of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.


        :param vgw_id: The vgw_id of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :type: str
        """

        self._vgw_id = vgw_id

    @property
    def x_coordinate(self):
        """Gets the x_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501


        :return: The x_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :rtype: int
        """
        return self._x_coordinate

    @x_coordinate.setter
    def x_coordinate(self, x_coordinate):
        """Sets the x_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.


        :param x_coordinate: The x_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :type: int
        """

        self._x_coordinate = x_coordinate

    @property
    def y_coordinate(self):
        """Gets the y_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501


        :return: The y_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :rtype: int
        """
        return self._y_coordinate

    @y_coordinate.setter
    def y_coordinate(self, y_coordinate):
        """Sets the y_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.


        :param y_coordinate: The y_coordinate of this TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput.  # noqa: E501
        :type: int
        """

        self._y_coordinate = y_coordinate

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopoCoordinateListForUpdateEDXVGWTopoCoordinateInput):
            return True

        return self.to_dict() != other.to_dict()
