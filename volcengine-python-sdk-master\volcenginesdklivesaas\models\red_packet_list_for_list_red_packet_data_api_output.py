# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RedPacketListForListRedPacketDataAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_delete': 'bool',
        'join_user_number': 'int',
        'received_amount': 'int',
        'red_packet_id': 'int',
        'red_packet_number': 'str',
        'red_packet_type': 'int',
        'rewards_points_unit': 'str',
        'send_time': 'int',
        'total_amount': 'int',
        'win_user_number': 'int',
        'withdrawal_amount': 'int'
    }

    attribute_map = {
        'is_delete': 'IsDelete',
        'join_user_number': 'JoinUserNumber',
        'received_amount': 'ReceivedAmount',
        'red_packet_id': 'RedPacketId',
        'red_packet_number': 'RedPacketNumber',
        'red_packet_type': 'RedPacketType',
        'rewards_points_unit': 'RewardsPointsUnit',
        'send_time': 'SendTime',
        'total_amount': 'TotalAmount',
        'win_user_number': 'WinUserNumber',
        'withdrawal_amount': 'WithdrawalAmount'
    }

    def __init__(self, is_delete=None, join_user_number=None, received_amount=None, red_packet_id=None, red_packet_number=None, red_packet_type=None, rewards_points_unit=None, send_time=None, total_amount=None, win_user_number=None, withdrawal_amount=None, _configuration=None):  # noqa: E501
        """RedPacketListForListRedPacketDataAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_delete = None
        self._join_user_number = None
        self._received_amount = None
        self._red_packet_id = None
        self._red_packet_number = None
        self._red_packet_type = None
        self._rewards_points_unit = None
        self._send_time = None
        self._total_amount = None
        self._win_user_number = None
        self._withdrawal_amount = None
        self.discriminator = None

        if is_delete is not None:
            self.is_delete = is_delete
        if join_user_number is not None:
            self.join_user_number = join_user_number
        if received_amount is not None:
            self.received_amount = received_amount
        if red_packet_id is not None:
            self.red_packet_id = red_packet_id
        if red_packet_number is not None:
            self.red_packet_number = red_packet_number
        if red_packet_type is not None:
            self.red_packet_type = red_packet_type
        if rewards_points_unit is not None:
            self.rewards_points_unit = rewards_points_unit
        if send_time is not None:
            self.send_time = send_time
        if total_amount is not None:
            self.total_amount = total_amount
        if win_user_number is not None:
            self.win_user_number = win_user_number
        if withdrawal_amount is not None:
            self.withdrawal_amount = withdrawal_amount

    @property
    def is_delete(self):
        """Gets the is_delete of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The is_delete of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_delete

    @is_delete.setter
    def is_delete(self, is_delete):
        """Sets the is_delete of this RedPacketListForListRedPacketDataAPIOutput.


        :param is_delete: The is_delete of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_delete = is_delete

    @property
    def join_user_number(self):
        """Gets the join_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The join_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._join_user_number

    @join_user_number.setter
    def join_user_number(self, join_user_number):
        """Sets the join_user_number of this RedPacketListForListRedPacketDataAPIOutput.


        :param join_user_number: The join_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._join_user_number = join_user_number

    @property
    def received_amount(self):
        """Gets the received_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The received_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._received_amount

    @received_amount.setter
    def received_amount(self, received_amount):
        """Sets the received_amount of this RedPacketListForListRedPacketDataAPIOutput.


        :param received_amount: The received_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._received_amount = received_amount

    @property
    def red_packet_id(self):
        """Gets the red_packet_id of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The red_packet_id of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_id

    @red_packet_id.setter
    def red_packet_id(self, red_packet_id):
        """Sets the red_packet_id of this RedPacketListForListRedPacketDataAPIOutput.


        :param red_packet_id: The red_packet_id of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_id = red_packet_id

    @property
    def red_packet_number(self):
        """Gets the red_packet_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The red_packet_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._red_packet_number

    @red_packet_number.setter
    def red_packet_number(self, red_packet_number):
        """Sets the red_packet_number of this RedPacketListForListRedPacketDataAPIOutput.


        :param red_packet_number: The red_packet_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._red_packet_number = red_packet_number

    @property
    def red_packet_type(self):
        """Gets the red_packet_type of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The red_packet_type of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_type

    @red_packet_type.setter
    def red_packet_type(self, red_packet_type):
        """Sets the red_packet_type of this RedPacketListForListRedPacketDataAPIOutput.


        :param red_packet_type: The red_packet_type of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_type = red_packet_type

    @property
    def rewards_points_unit(self):
        """Gets the rewards_points_unit of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The rewards_points_unit of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._rewards_points_unit

    @rewards_points_unit.setter
    def rewards_points_unit(self, rewards_points_unit):
        """Sets the rewards_points_unit of this RedPacketListForListRedPacketDataAPIOutput.


        :param rewards_points_unit: The rewards_points_unit of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._rewards_points_unit = rewards_points_unit

    @property
    def send_time(self):
        """Gets the send_time of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The send_time of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this RedPacketListForListRedPacketDataAPIOutput.


        :param send_time: The send_time of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def total_amount(self):
        """Gets the total_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The total_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_amount

    @total_amount.setter
    def total_amount(self, total_amount):
        """Sets the total_amount of this RedPacketListForListRedPacketDataAPIOutput.


        :param total_amount: The total_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._total_amount = total_amount

    @property
    def win_user_number(self):
        """Gets the win_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The win_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._win_user_number

    @win_user_number.setter
    def win_user_number(self, win_user_number):
        """Sets the win_user_number of this RedPacketListForListRedPacketDataAPIOutput.


        :param win_user_number: The win_user_number of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._win_user_number = win_user_number

    @property
    def withdrawal_amount(self):
        """Gets the withdrawal_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501


        :return: The withdrawal_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._withdrawal_amount

    @withdrawal_amount.setter
    def withdrawal_amount(self, withdrawal_amount):
        """Sets the withdrawal_amount of this RedPacketListForListRedPacketDataAPIOutput.


        :param withdrawal_amount: The withdrawal_amount of this RedPacketListForListRedPacketDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._withdrawal_amount = withdrawal_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RedPacketListForListRedPacketDataAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RedPacketListForListRedPacketDataAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RedPacketListForListRedPacketDataAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
