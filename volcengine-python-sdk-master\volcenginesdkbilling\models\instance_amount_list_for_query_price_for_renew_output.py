# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceAmountListForQueryPriceForRenewOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'configuration_code': 'str',
        'discount_amount': 'str',
        'instance_id': 'str',
        'original_amount': 'str',
        'product': 'str'
    }

    attribute_map = {
        'configuration_code': 'ConfigurationCode',
        'discount_amount': 'DiscountAmount',
        'instance_id': 'InstanceID',
        'original_amount': 'OriginalAmount',
        'product': 'Product'
    }

    def __init__(self, configuration_code=None, discount_amount=None, instance_id=None, original_amount=None, product=None, _configuration=None):  # noqa: E501
        """InstanceAmountListForQueryPriceForRenewOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._configuration_code = None
        self._discount_amount = None
        self._instance_id = None
        self._original_amount = None
        self._product = None
        self.discriminator = None

        if configuration_code is not None:
            self.configuration_code = configuration_code
        if discount_amount is not None:
            self.discount_amount = discount_amount
        if instance_id is not None:
            self.instance_id = instance_id
        if original_amount is not None:
            self.original_amount = original_amount
        if product is not None:
            self.product = product

    @property
    def configuration_code(self):
        """Gets the configuration_code of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501


        :return: The configuration_code of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this InstanceAmountListForQueryPriceForRenewOutput.


        :param configuration_code: The configuration_code of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def discount_amount(self):
        """Gets the discount_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501


        :return: The discount_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_amount

    @discount_amount.setter
    def discount_amount(self, discount_amount):
        """Sets the discount_amount of this InstanceAmountListForQueryPriceForRenewOutput.


        :param discount_amount: The discount_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :type: str
        """

        self._discount_amount = discount_amount

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501


        :return: The instance_id of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceAmountListForQueryPriceForRenewOutput.


        :param instance_id: The instance_id of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def original_amount(self):
        """Gets the original_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501


        :return: The original_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_amount

    @original_amount.setter
    def original_amount(self, original_amount):
        """Sets the original_amount of this InstanceAmountListForQueryPriceForRenewOutput.


        :param original_amount: The original_amount of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :type: str
        """

        self._original_amount = original_amount

    @property
    def product(self):
        """Gets the product of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501


        :return: The product of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this InstanceAmountListForQueryPriceForRenewOutput.


        :param product: The product of this InstanceAmountListForQueryPriceForRenewOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceAmountListForQueryPriceForRenewOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceAmountListForQueryPriceForRenewOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceAmountListForQueryPriceForRenewOutput):
            return True

        return self.to_dict() != other.to_dict()
