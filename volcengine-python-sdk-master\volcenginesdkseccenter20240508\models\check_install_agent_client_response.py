# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckInstallAgentClientResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_agent_ids': 'list[str]',
        'auto_count': 'int',
        'current_quota': 'CurrentQuotaForCheckInstallAgentClientOutput',
        'hit_limit': 'bool',
        'manual_count': 'int',
        'request_quota': 'RequestQuotaForCheckInstallAgentClientOutput'
    }

    attribute_map = {
        'auto_agent_ids': 'AutoAgentIDs',
        'auto_count': 'AutoCount',
        'current_quota': 'CurrentQuota',
        'hit_limit': 'HitLimit',
        'manual_count': 'ManualCount',
        'request_quota': 'RequestQuota'
    }

    def __init__(self, auto_agent_ids=None, auto_count=None, current_quota=None, hit_limit=None, manual_count=None, request_quota=None, _configuration=None):  # noqa: E501
        """CheckInstallAgentClientResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_agent_ids = None
        self._auto_count = None
        self._current_quota = None
        self._hit_limit = None
        self._manual_count = None
        self._request_quota = None
        self.discriminator = None

        if auto_agent_ids is not None:
            self.auto_agent_ids = auto_agent_ids
        if auto_count is not None:
            self.auto_count = auto_count
        if current_quota is not None:
            self.current_quota = current_quota
        if hit_limit is not None:
            self.hit_limit = hit_limit
        if manual_count is not None:
            self.manual_count = manual_count
        if request_quota is not None:
            self.request_quota = request_quota

    @property
    def auto_agent_ids(self):
        """Gets the auto_agent_ids of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The auto_agent_ids of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._auto_agent_ids

    @auto_agent_ids.setter
    def auto_agent_ids(self, auto_agent_ids):
        """Sets the auto_agent_ids of this CheckInstallAgentClientResponse.


        :param auto_agent_ids: The auto_agent_ids of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: list[str]
        """

        self._auto_agent_ids = auto_agent_ids

    @property
    def auto_count(self):
        """Gets the auto_count of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The auto_count of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: int
        """
        return self._auto_count

    @auto_count.setter
    def auto_count(self, auto_count):
        """Sets the auto_count of this CheckInstallAgentClientResponse.


        :param auto_count: The auto_count of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: int
        """

        self._auto_count = auto_count

    @property
    def current_quota(self):
        """Gets the current_quota of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The current_quota of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: CurrentQuotaForCheckInstallAgentClientOutput
        """
        return self._current_quota

    @current_quota.setter
    def current_quota(self, current_quota):
        """Sets the current_quota of this CheckInstallAgentClientResponse.


        :param current_quota: The current_quota of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: CurrentQuotaForCheckInstallAgentClientOutput
        """

        self._current_quota = current_quota

    @property
    def hit_limit(self):
        """Gets the hit_limit of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The hit_limit of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: bool
        """
        return self._hit_limit

    @hit_limit.setter
    def hit_limit(self, hit_limit):
        """Sets the hit_limit of this CheckInstallAgentClientResponse.


        :param hit_limit: The hit_limit of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: bool
        """

        self._hit_limit = hit_limit

    @property
    def manual_count(self):
        """Gets the manual_count of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The manual_count of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: int
        """
        return self._manual_count

    @manual_count.setter
    def manual_count(self, manual_count):
        """Sets the manual_count of this CheckInstallAgentClientResponse.


        :param manual_count: The manual_count of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: int
        """

        self._manual_count = manual_count

    @property
    def request_quota(self):
        """Gets the request_quota of this CheckInstallAgentClientResponse.  # noqa: E501


        :return: The request_quota of this CheckInstallAgentClientResponse.  # noqa: E501
        :rtype: RequestQuotaForCheckInstallAgentClientOutput
        """
        return self._request_quota

    @request_quota.setter
    def request_quota(self, request_quota):
        """Sets the request_quota of this CheckInstallAgentClientResponse.


        :param request_quota: The request_quota of this CheckInstallAgentClientResponse.  # noqa: E501
        :type: RequestQuotaForCheckInstallAgentClientOutput
        """

        self._request_quota = request_quota

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckInstallAgentClientResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckInstallAgentClientResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckInstallAgentClientResponse):
            return True

        return self.to_dict() != other.to_dict()
