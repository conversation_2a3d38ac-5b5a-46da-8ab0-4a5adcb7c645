# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListQuotaAlarmRulesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'next_token': 'str',
        'quota_alarm_rule_list': 'list[QuotaAlarmRuleListForListQuotaAlarmRulesOutput]'
    }

    attribute_map = {
        'next_token': 'NextToken',
        'quota_alarm_rule_list': 'QuotaAlarmRuleList'
    }

    def __init__(self, next_token=None, quota_alarm_rule_list=None, _configuration=None):  # noqa: E501
        """ListQuotaAlarmRulesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._next_token = None
        self._quota_alarm_rule_list = None
        self.discriminator = None

        if next_token is not None:
            self.next_token = next_token
        if quota_alarm_rule_list is not None:
            self.quota_alarm_rule_list = quota_alarm_rule_list

    @property
    def next_token(self):
        """Gets the next_token of this ListQuotaAlarmRulesResponse.  # noqa: E501


        :return: The next_token of this ListQuotaAlarmRulesResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListQuotaAlarmRulesResponse.


        :param next_token: The next_token of this ListQuotaAlarmRulesResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def quota_alarm_rule_list(self):
        """Gets the quota_alarm_rule_list of this ListQuotaAlarmRulesResponse.  # noqa: E501


        :return: The quota_alarm_rule_list of this ListQuotaAlarmRulesResponse.  # noqa: E501
        :rtype: list[QuotaAlarmRuleListForListQuotaAlarmRulesOutput]
        """
        return self._quota_alarm_rule_list

    @quota_alarm_rule_list.setter
    def quota_alarm_rule_list(self, quota_alarm_rule_list):
        """Sets the quota_alarm_rule_list of this ListQuotaAlarmRulesResponse.


        :param quota_alarm_rule_list: The quota_alarm_rule_list of this ListQuotaAlarmRulesResponse.  # noqa: E501
        :type: list[QuotaAlarmRuleListForListQuotaAlarmRulesOutput]
        """

        self._quota_alarm_rule_list = quota_alarm_rule_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListQuotaAlarmRulesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListQuotaAlarmRulesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListQuotaAlarmRulesResponse):
            return True

        return self.to_dict() != other.to_dict()
