# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SecurityForCreateDefaultNodePoolInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'login': 'LoginForCreateDefaultNodePoolInput',
        'security_group_ids': 'list[str]',
        'security_strategies': 'list[str]'
    }

    attribute_map = {
        'login': 'Login',
        'security_group_ids': 'SecurityGroupIds',
        'security_strategies': 'SecurityStrategies'
    }

    def __init__(self, login=None, security_group_ids=None, security_strategies=None, _configuration=None):  # noqa: E501
        """SecurityForCreateDefaultNodePoolInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._login = None
        self._security_group_ids = None
        self._security_strategies = None
        self.discriminator = None

        if login is not None:
            self.login = login
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if security_strategies is not None:
            self.security_strategies = security_strategies

    @property
    def login(self):
        """Gets the login of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The login of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: LoginForCreateDefaultNodePoolInput
        """
        return self._login

    @login.setter
    def login(self, login):
        """Sets the login of this SecurityForCreateDefaultNodePoolInput.


        :param login: The login of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :type: LoginForCreateDefaultNodePoolInput
        """

        self._login = login

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The security_group_ids of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this SecurityForCreateDefaultNodePoolInput.


        :param security_group_ids: The security_group_ids of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def security_strategies(self):
        """Gets the security_strategies of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The security_strategies of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_strategies

    @security_strategies.setter
    def security_strategies(self, security_strategies):
        """Sets the security_strategies of this SecurityForCreateDefaultNodePoolInput.


        :param security_strategies: The security_strategies of this SecurityForCreateDefaultNodePoolInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Hids"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(security_strategies).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `security_strategies` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(security_strategies) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._security_strategies = security_strategies

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SecurityForCreateDefaultNodePoolInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SecurityForCreateDefaultNodePoolInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SecurityForCreateDefaultNodePoolInput):
            return True

        return self.to_dict() != other.to_dict()
