# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NacosSourceForGetUpstreamSourceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_config': 'AuthConfigForGetUpstreamSourceOutput',
        'nacos_id': 'str',
        'nacos_name': 'str'
    }

    attribute_map = {
        'auth_config': 'AuthConfig',
        'nacos_id': 'NacosId',
        'nacos_name': 'NacosName'
    }

    def __init__(self, auth_config=None, nacos_id=None, nacos_name=None, _configuration=None):  # noqa: E501
        """NacosSourceForGetUpstreamSourceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_config = None
        self._nacos_id = None
        self._nacos_name = None
        self.discriminator = None

        if auth_config is not None:
            self.auth_config = auth_config
        if nacos_id is not None:
            self.nacos_id = nacos_id
        if nacos_name is not None:
            self.nacos_name = nacos_name

    @property
    def auth_config(self):
        """Gets the auth_config of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501


        :return: The auth_config of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :rtype: AuthConfigForGetUpstreamSourceOutput
        """
        return self._auth_config

    @auth_config.setter
    def auth_config(self, auth_config):
        """Sets the auth_config of this NacosSourceForGetUpstreamSourceOutput.


        :param auth_config: The auth_config of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :type: AuthConfigForGetUpstreamSourceOutput
        """

        self._auth_config = auth_config

    @property
    def nacos_id(self):
        """Gets the nacos_id of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501


        :return: The nacos_id of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :rtype: str
        """
        return self._nacos_id

    @nacos_id.setter
    def nacos_id(self, nacos_id):
        """Sets the nacos_id of this NacosSourceForGetUpstreamSourceOutput.


        :param nacos_id: The nacos_id of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :type: str
        """

        self._nacos_id = nacos_id

    @property
    def nacos_name(self):
        """Gets the nacos_name of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501


        :return: The nacos_name of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :rtype: str
        """
        return self._nacos_name

    @nacos_name.setter
    def nacos_name(self, nacos_name):
        """Sets the nacos_name of this NacosSourceForGetUpstreamSourceOutput.


        :param nacos_name: The nacos_name of this NacosSourceForGetUpstreamSourceOutput.  # noqa: E501
        :type: str
        """

        self._nacos_name = nacos_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NacosSourceForGetUpstreamSourceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NacosSourceForGetUpstreamSourceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NacosSourceForGetUpstreamSourceOutput):
            return True

        return self.to_dict() != other.to_dict()
