# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CopyImageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'copy_image_tags': 'bool',
        'description': 'str',
        'destination_region': 'str',
        'image_id': 'str',
        'image_name': 'str',
        'project_name': 'str'
    }

    attribute_map = {
        'copy_image_tags': 'CopyImageTags',
        'description': 'Description',
        'destination_region': 'DestinationRegion',
        'image_id': 'ImageId',
        'image_name': 'ImageName',
        'project_name': 'ProjectName'
    }

    def __init__(self, copy_image_tags=None, description=None, destination_region=None, image_id=None, image_name=None, project_name=None, _configuration=None):  # noqa: E501
        """CopyImageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._copy_image_tags = None
        self._description = None
        self._destination_region = None
        self._image_id = None
        self._image_name = None
        self._project_name = None
        self.discriminator = None

        if copy_image_tags is not None:
            self.copy_image_tags = copy_image_tags
        if description is not None:
            self.description = description
        self.destination_region = destination_region
        self.image_id = image_id
        self.image_name = image_name
        if project_name is not None:
            self.project_name = project_name

    @property
    def copy_image_tags(self):
        """Gets the copy_image_tags of this CopyImageRequest.  # noqa: E501


        :return: The copy_image_tags of this CopyImageRequest.  # noqa: E501
        :rtype: bool
        """
        return self._copy_image_tags

    @copy_image_tags.setter
    def copy_image_tags(self, copy_image_tags):
        """Sets the copy_image_tags of this CopyImageRequest.


        :param copy_image_tags: The copy_image_tags of this CopyImageRequest.  # noqa: E501
        :type: bool
        """

        self._copy_image_tags = copy_image_tags

    @property
    def description(self):
        """Gets the description of this CopyImageRequest.  # noqa: E501


        :return: The description of this CopyImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CopyImageRequest.


        :param description: The description of this CopyImageRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_region(self):
        """Gets the destination_region of this CopyImageRequest.  # noqa: E501


        :return: The destination_region of this CopyImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_region

    @destination_region.setter
    def destination_region(self, destination_region):
        """Sets the destination_region of this CopyImageRequest.


        :param destination_region: The destination_region of this CopyImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and destination_region is None:
            raise ValueError("Invalid value for `destination_region`, must not be `None`")  # noqa: E501

        self._destination_region = destination_region

    @property
    def image_id(self):
        """Gets the image_id of this CopyImageRequest.  # noqa: E501


        :return: The image_id of this CopyImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this CopyImageRequest.


        :param image_id: The image_id of this CopyImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_id is None:
            raise ValueError("Invalid value for `image_id`, must not be `None`")  # noqa: E501

        self._image_id = image_id

    @property
    def image_name(self):
        """Gets the image_name of this CopyImageRequest.  # noqa: E501


        :return: The image_name of this CopyImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this CopyImageRequest.


        :param image_name: The image_name of this CopyImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_name is None:
            raise ValueError("Invalid value for `image_name`, must not be `None`")  # noqa: E501

        self._image_name = image_name

    @property
    def project_name(self):
        """Gets the project_name of this CopyImageRequest.  # noqa: E501


        :return: The project_name of this CopyImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CopyImageRequest.


        :param project_name: The project_name of this CopyImageRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CopyImageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CopyImageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CopyImageRequest):
            return True

        return self.to_dict() != other.to_dict()
