# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListVendorContentTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_account_id': 'str',
        'end_time': 'int',
        'pagination': 'PaginationForListVendorContentTaskInput',
        'product_type': 'str',
        'start_time': 'int',
        'sub_product': 'str',
        'task_type': 'str',
        'vendor_task_id': 'str'
    }

    attribute_map = {
        'cloud_account_id': 'CloudAccountId',
        'end_time': 'EndTime',
        'pagination': 'Pagination',
        'product_type': 'ProductType',
        'start_time': 'StartTime',
        'sub_product': 'SubProduct',
        'task_type': 'TaskType',
        'vendor_task_id': 'VendorTaskId'
    }

    def __init__(self, cloud_account_id=None, end_time=None, pagination=None, product_type=None, start_time=None, sub_product=None, task_type=None, vendor_task_id=None, _configuration=None):  # noqa: E501
        """ListVendorContentTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_account_id = None
        self._end_time = None
        self._pagination = None
        self._product_type = None
        self._start_time = None
        self._sub_product = None
        self._task_type = None
        self._vendor_task_id = None
        self.discriminator = None

        self.cloud_account_id = cloud_account_id
        if end_time is not None:
            self.end_time = end_time
        if pagination is not None:
            self.pagination = pagination
        if product_type is not None:
            self.product_type = product_type
        if start_time is not None:
            self.start_time = start_time
        if sub_product is not None:
            self.sub_product = sub_product
        self.task_type = task_type
        if vendor_task_id is not None:
            self.vendor_task_id = vendor_task_id

    @property
    def cloud_account_id(self):
        """Gets the cloud_account_id of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The cloud_account_id of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_id

    @cloud_account_id.setter
    def cloud_account_id(self, cloud_account_id):
        """Sets the cloud_account_id of this ListVendorContentTaskRequest.


        :param cloud_account_id: The cloud_account_id of this ListVendorContentTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cloud_account_id is None:
            raise ValueError("Invalid value for `cloud_account_id`, must not be `None`")  # noqa: E501

        self._cloud_account_id = cloud_account_id

    @property
    def end_time(self):
        """Gets the end_time of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The end_time of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ListVendorContentTaskRequest.


        :param end_time: The end_time of this ListVendorContentTaskRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def pagination(self):
        """Gets the pagination of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The pagination of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: PaginationForListVendorContentTaskInput
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this ListVendorContentTaskRequest.


        :param pagination: The pagination of this ListVendorContentTaskRequest.  # noqa: E501
        :type: PaginationForListVendorContentTaskInput
        """

        self._pagination = pagination

    @property
    def product_type(self):
        """Gets the product_type of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The product_type of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_type

    @product_type.setter
    def product_type(self, product_type):
        """Sets the product_type of this ListVendorContentTaskRequest.


        :param product_type: The product_type of this ListVendorContentTaskRequest.  # noqa: E501
        :type: str
        """

        self._product_type = product_type

    @property
    def start_time(self):
        """Gets the start_time of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The start_time of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ListVendorContentTaskRequest.


        :param start_time: The start_time of this ListVendorContentTaskRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def sub_product(self):
        """Gets the sub_product of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The sub_product of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._sub_product

    @sub_product.setter
    def sub_product(self, sub_product):
        """Sets the sub_product of this ListVendorContentTaskRequest.


        :param sub_product: The sub_product of this ListVendorContentTaskRequest.  # noqa: E501
        :type: str
        """

        self._sub_product = sub_product

    @property
    def task_type(self):
        """Gets the task_type of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The task_type of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this ListVendorContentTaskRequest.


        :param task_type: The task_type of this ListVendorContentTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_type is None:
            raise ValueError("Invalid value for `task_type`, must not be `None`")  # noqa: E501

        self._task_type = task_type

    @property
    def vendor_task_id(self):
        """Gets the vendor_task_id of this ListVendorContentTaskRequest.  # noqa: E501


        :return: The vendor_task_id of this ListVendorContentTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._vendor_task_id

    @vendor_task_id.setter
    def vendor_task_id(self, vendor_task_id):
        """Sets the vendor_task_id of this ListVendorContentTaskRequest.


        :param vendor_task_id: The vendor_task_id of this ListVendorContentTaskRequest.  # noqa: E501
        :type: str
        """

        self._vendor_task_id = vendor_task_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListVendorContentTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListVendorContentTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListVendorContentTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
