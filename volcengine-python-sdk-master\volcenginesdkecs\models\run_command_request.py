# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RunCommandRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'command_content': 'str',
        'content_encoding': 'str',
        'enable_parameter': 'bool',
        'frequency': 'str',
        'instance_ids': 'list[str]',
        'invocation_description': 'str',
        'invocation_name': 'str',
        'launch_time': 'str',
        'parameter_definitions': 'list[ParameterDefinitionForRunCommandInput]',
        'parameters': 'str',
        'project_name': 'str',
        'recurrence_end_time': 'str',
        'repeat_mode': 'str',
        'tags': 'list[TagForRunCommandInput]',
        'timeout': 'int',
        'type': 'str',
        'username': 'str',
        'windows_password': 'str',
        'working_dir': 'str'
    }

    attribute_map = {
        'command_content': 'CommandContent',
        'content_encoding': 'ContentEncoding',
        'enable_parameter': 'EnableParameter',
        'frequency': 'Frequency',
        'instance_ids': 'InstanceIds',
        'invocation_description': 'InvocationDescription',
        'invocation_name': 'InvocationName',
        'launch_time': 'LaunchTime',
        'parameter_definitions': 'ParameterDefinitions',
        'parameters': 'Parameters',
        'project_name': 'ProjectName',
        'recurrence_end_time': 'RecurrenceEndTime',
        'repeat_mode': 'RepeatMode',
        'tags': 'Tags',
        'timeout': 'Timeout',
        'type': 'Type',
        'username': 'Username',
        'windows_password': 'WindowsPassword',
        'working_dir': 'WorkingDir'
    }

    def __init__(self, command_content=None, content_encoding=None, enable_parameter=None, frequency=None, instance_ids=None, invocation_description=None, invocation_name=None, launch_time=None, parameter_definitions=None, parameters=None, project_name=None, recurrence_end_time=None, repeat_mode=None, tags=None, timeout=None, type=None, username=None, windows_password=None, working_dir=None, _configuration=None):  # noqa: E501
        """RunCommandRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._command_content = None
        self._content_encoding = None
        self._enable_parameter = None
        self._frequency = None
        self._instance_ids = None
        self._invocation_description = None
        self._invocation_name = None
        self._launch_time = None
        self._parameter_definitions = None
        self._parameters = None
        self._project_name = None
        self._recurrence_end_time = None
        self._repeat_mode = None
        self._tags = None
        self._timeout = None
        self._type = None
        self._username = None
        self._windows_password = None
        self._working_dir = None
        self.discriminator = None

        self.command_content = command_content
        if content_encoding is not None:
            self.content_encoding = content_encoding
        if enable_parameter is not None:
            self.enable_parameter = enable_parameter
        if frequency is not None:
            self.frequency = frequency
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if invocation_description is not None:
            self.invocation_description = invocation_description
        self.invocation_name = invocation_name
        if launch_time is not None:
            self.launch_time = launch_time
        if parameter_definitions is not None:
            self.parameter_definitions = parameter_definitions
        if parameters is not None:
            self.parameters = parameters
        if project_name is not None:
            self.project_name = project_name
        if recurrence_end_time is not None:
            self.recurrence_end_time = recurrence_end_time
        if repeat_mode is not None:
            self.repeat_mode = repeat_mode
        if tags is not None:
            self.tags = tags
        if timeout is not None:
            self.timeout = timeout
        self.type = type
        if username is not None:
            self.username = username
        if windows_password is not None:
            self.windows_password = windows_password
        if working_dir is not None:
            self.working_dir = working_dir

    @property
    def command_content(self):
        """Gets the command_content of this RunCommandRequest.  # noqa: E501


        :return: The command_content of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._command_content

    @command_content.setter
    def command_content(self, command_content):
        """Sets the command_content of this RunCommandRequest.


        :param command_content: The command_content of this RunCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and command_content is None:
            raise ValueError("Invalid value for `command_content`, must not be `None`")  # noqa: E501

        self._command_content = command_content

    @property
    def content_encoding(self):
        """Gets the content_encoding of this RunCommandRequest.  # noqa: E501


        :return: The content_encoding of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._content_encoding

    @content_encoding.setter
    def content_encoding(self, content_encoding):
        """Sets the content_encoding of this RunCommandRequest.


        :param content_encoding: The content_encoding of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._content_encoding = content_encoding

    @property
    def enable_parameter(self):
        """Gets the enable_parameter of this RunCommandRequest.  # noqa: E501


        :return: The enable_parameter of this RunCommandRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_parameter

    @enable_parameter.setter
    def enable_parameter(self, enable_parameter):
        """Sets the enable_parameter of this RunCommandRequest.


        :param enable_parameter: The enable_parameter of this RunCommandRequest.  # noqa: E501
        :type: bool
        """

        self._enable_parameter = enable_parameter

    @property
    def frequency(self):
        """Gets the frequency of this RunCommandRequest.  # noqa: E501


        :return: The frequency of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._frequency

    @frequency.setter
    def frequency(self, frequency):
        """Sets the frequency of this RunCommandRequest.


        :param frequency: The frequency of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._frequency = frequency

    @property
    def instance_ids(self):
        """Gets the instance_ids of this RunCommandRequest.  # noqa: E501


        :return: The instance_ids of this RunCommandRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this RunCommandRequest.


        :param instance_ids: The instance_ids of this RunCommandRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def invocation_description(self):
        """Gets the invocation_description of this RunCommandRequest.  # noqa: E501


        :return: The invocation_description of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._invocation_description

    @invocation_description.setter
    def invocation_description(self, invocation_description):
        """Sets the invocation_description of this RunCommandRequest.


        :param invocation_description: The invocation_description of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._invocation_description = invocation_description

    @property
    def invocation_name(self):
        """Gets the invocation_name of this RunCommandRequest.  # noqa: E501


        :return: The invocation_name of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._invocation_name

    @invocation_name.setter
    def invocation_name(self, invocation_name):
        """Sets the invocation_name of this RunCommandRequest.


        :param invocation_name: The invocation_name of this RunCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and invocation_name is None:
            raise ValueError("Invalid value for `invocation_name`, must not be `None`")  # noqa: E501

        self._invocation_name = invocation_name

    @property
    def launch_time(self):
        """Gets the launch_time of this RunCommandRequest.  # noqa: E501


        :return: The launch_time of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._launch_time

    @launch_time.setter
    def launch_time(self, launch_time):
        """Sets the launch_time of this RunCommandRequest.


        :param launch_time: The launch_time of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._launch_time = launch_time

    @property
    def parameter_definitions(self):
        """Gets the parameter_definitions of this RunCommandRequest.  # noqa: E501


        :return: The parameter_definitions of this RunCommandRequest.  # noqa: E501
        :rtype: list[ParameterDefinitionForRunCommandInput]
        """
        return self._parameter_definitions

    @parameter_definitions.setter
    def parameter_definitions(self, parameter_definitions):
        """Sets the parameter_definitions of this RunCommandRequest.


        :param parameter_definitions: The parameter_definitions of this RunCommandRequest.  # noqa: E501
        :type: list[ParameterDefinitionForRunCommandInput]
        """

        self._parameter_definitions = parameter_definitions

    @property
    def parameters(self):
        """Gets the parameters of this RunCommandRequest.  # noqa: E501


        :return: The parameters of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._parameters

    @parameters.setter
    def parameters(self, parameters):
        """Sets the parameters of this RunCommandRequest.


        :param parameters: The parameters of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._parameters = parameters

    @property
    def project_name(self):
        """Gets the project_name of this RunCommandRequest.  # noqa: E501


        :return: The project_name of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this RunCommandRequest.


        :param project_name: The project_name of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def recurrence_end_time(self):
        """Gets the recurrence_end_time of this RunCommandRequest.  # noqa: E501


        :return: The recurrence_end_time of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._recurrence_end_time

    @recurrence_end_time.setter
    def recurrence_end_time(self, recurrence_end_time):
        """Sets the recurrence_end_time of this RunCommandRequest.


        :param recurrence_end_time: The recurrence_end_time of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._recurrence_end_time = recurrence_end_time

    @property
    def repeat_mode(self):
        """Gets the repeat_mode of this RunCommandRequest.  # noqa: E501


        :return: The repeat_mode of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._repeat_mode

    @repeat_mode.setter
    def repeat_mode(self, repeat_mode):
        """Sets the repeat_mode of this RunCommandRequest.


        :param repeat_mode: The repeat_mode of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._repeat_mode = repeat_mode

    @property
    def tags(self):
        """Gets the tags of this RunCommandRequest.  # noqa: E501


        :return: The tags of this RunCommandRequest.  # noqa: E501
        :rtype: list[TagForRunCommandInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this RunCommandRequest.


        :param tags: The tags of this RunCommandRequest.  # noqa: E501
        :type: list[TagForRunCommandInput]
        """

        self._tags = tags

    @property
    def timeout(self):
        """Gets the timeout of this RunCommandRequest.  # noqa: E501


        :return: The timeout of this RunCommandRequest.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this RunCommandRequest.


        :param timeout: The timeout of this RunCommandRequest.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    @property
    def type(self):
        """Gets the type of this RunCommandRequest.  # noqa: E501


        :return: The type of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this RunCommandRequest.


        :param type: The type of this RunCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def username(self):
        """Gets the username of this RunCommandRequest.  # noqa: E501


        :return: The username of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this RunCommandRequest.


        :param username: The username of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    @property
    def windows_password(self):
        """Gets the windows_password of this RunCommandRequest.  # noqa: E501


        :return: The windows_password of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._windows_password

    @windows_password.setter
    def windows_password(self, windows_password):
        """Sets the windows_password of this RunCommandRequest.


        :param windows_password: The windows_password of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._windows_password = windows_password

    @property
    def working_dir(self):
        """Gets the working_dir of this RunCommandRequest.  # noqa: E501


        :return: The working_dir of this RunCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._working_dir

    @working_dir.setter
    def working_dir(self, working_dir):
        """Sets the working_dir of this RunCommandRequest.


        :param working_dir: The working_dir of this RunCommandRequest.  # noqa: E501
        :type: str
        """

        self._working_dir = working_dir

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RunCommandRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RunCommandRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RunCommandRequest):
            return True

        return self.to_dict() != other.to_dict()
