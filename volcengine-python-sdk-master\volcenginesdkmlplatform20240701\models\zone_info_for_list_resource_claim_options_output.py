# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ZoneInfoForListResourceClaimOptionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'price_by_hour': 'float',
        'stock_status': 'str',
        'support_status': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'price_by_hour': 'PriceByHour',
        'stock_status': 'StockStatus',
        'support_status': 'SupportStatus',
        'zone_id': 'ZoneId'
    }

    def __init__(self, price_by_hour=None, stock_status=None, support_status=None, zone_id=None, _configuration=None):  # noqa: E501
        """ZoneInfoForListResourceClaimOptionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._price_by_hour = None
        self._stock_status = None
        self._support_status = None
        self._zone_id = None
        self.discriminator = None

        if price_by_hour is not None:
            self.price_by_hour = price_by_hour
        if stock_status is not None:
            self.stock_status = stock_status
        if support_status is not None:
            self.support_status = support_status
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def price_by_hour(self):
        """Gets the price_by_hour of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The price_by_hour of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: float
        """
        return self._price_by_hour

    @price_by_hour.setter
    def price_by_hour(self, price_by_hour):
        """Sets the price_by_hour of this ZoneInfoForListResourceClaimOptionsOutput.


        :param price_by_hour: The price_by_hour of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :type: float
        """

        self._price_by_hour = price_by_hour

    @property
    def stock_status(self):
        """Gets the stock_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The stock_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stock_status

    @stock_status.setter
    def stock_status(self, stock_status):
        """Sets the stock_status of this ZoneInfoForListResourceClaimOptionsOutput.


        :param stock_status: The stock_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._stock_status = stock_status

    @property
    def support_status(self):
        """Gets the support_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The support_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._support_status

    @support_status.setter
    def support_status(self, support_status):
        """Sets the support_status of this ZoneInfoForListResourceClaimOptionsOutput.


        :param support_status: The support_status of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._support_status = support_status

    @property
    def zone_id(self):
        """Gets the zone_id of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The zone_id of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ZoneInfoForListResourceClaimOptionsOutput.


        :param zone_id: The zone_id of this ZoneInfoForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ZoneInfoForListResourceClaimOptionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ZoneInfoForListResourceClaimOptionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ZoneInfoForListResourceClaimOptionsOutput):
            return True

        return self.to_dict() != other.to_dict()
