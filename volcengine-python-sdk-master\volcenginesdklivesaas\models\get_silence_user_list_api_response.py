# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetSilenceUserListAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'user_list': 'list[UserListForGetSilenceUserListAPIOutput]'
    }

    attribute_map = {
        'user_list': 'UserList'
    }

    def __init__(self, user_list=None, _configuration=None):  # noqa: E501
        """GetSilenceUserListAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._user_list = None
        self.discriminator = None

        if user_list is not None:
            self.user_list = user_list

    @property
    def user_list(self):
        """Gets the user_list of this GetSilenceUserListAPIResponse.  # noqa: E501


        :return: The user_list of this GetSilenceUserListAPIResponse.  # noqa: E501
        :rtype: list[UserListForGetSilenceUserListAPIOutput]
        """
        return self._user_list

    @user_list.setter
    def user_list(self, user_list):
        """Sets the user_list of this GetSilenceUserListAPIResponse.


        :param user_list: The user_list of this GetSilenceUserListAPIResponse.  # noqa: E501
        :type: list[UserListForGetSilenceUserListAPIOutput]
        """

        self._user_list = user_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetSilenceUserListAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetSilenceUserListAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetSilenceUserListAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
