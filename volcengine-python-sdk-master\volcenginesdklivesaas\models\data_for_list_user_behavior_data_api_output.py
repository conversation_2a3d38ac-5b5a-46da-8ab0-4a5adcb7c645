# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListUserBehaviorDataAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_time': 'str',
        'access_time_arr': 'list[str]',
        'channel_name': 'str',
        'check_in_number': 'int',
        'comment_counts': 'int',
        'cookie': 'int',
        'department': 'str',
        'enter_review_age': 'str',
        'enter_review_birthday': 'str',
        'enter_review_company': 'str',
        'enter_review_education': 'str',
        'enter_review_email': 'str',
        'enter_review_industry': 'str',
        'enter_review_name': 'str',
        'enter_review_position': 'str',
        'enter_review_sex': 'str',
        'enter_review_tel': 'str',
        'external_user_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'invite_code': 'str',
        'inviter_external_id': 'str',
        'inviter_id': 'int',
        'inviter_name': 'str',
        'is_banned_ip': 'int',
        'is_banned_user': 'int',
        'join_at': 'list[int]',
        'mute_number': 'int',
        'no_interact_number': 'int',
        'os': 'str',
        'play_status': 'int',
        'region': 'str',
        'shift_screen_number': 'int',
        'silence_status': 'int',
        'thumb_up_number': 'int',
        'trial_time': 'int',
        'user_credit': 'int',
        'user_device': 'list[str]',
        'user_id': 'int',
        'user_name': 'str',
        'user_tel': 'str',
        'view_duration': 'int',
        'watch_time': 'int',
        'leave_at': 'list[int]'
    }

    attribute_map = {
        'access_time': 'AccessTime',
        'access_time_arr': 'AccessTimeArr',
        'channel_name': 'ChannelName',
        'check_in_number': 'CheckInNumber',
        'comment_counts': 'CommentCounts',
        'cookie': 'Cookie',
        'department': 'Department',
        'enter_review_age': 'EnterReviewAge',
        'enter_review_birthday': 'EnterReviewBirthday',
        'enter_review_company': 'EnterReviewCompany',
        'enter_review_education': 'EnterReviewEducation',
        'enter_review_email': 'EnterReviewEmail',
        'enter_review_industry': 'EnterReviewIndustry',
        'enter_review_name': 'EnterReviewName',
        'enter_review_position': 'EnterReviewPosition',
        'enter_review_sex': 'EnterReviewSex',
        'enter_review_tel': 'EnterReviewTel',
        'external_user_id': 'ExternalUserId',
        'extra': 'Extra',
        'ip': 'IP',
        'invite_code': 'InviteCode',
        'inviter_external_id': 'InviterExternalId',
        'inviter_id': 'InviterId',
        'inviter_name': 'InviterName',
        'is_banned_ip': 'IsBannedIp',
        'is_banned_user': 'IsBannedUser',
        'join_at': 'JoinAt',
        'mute_number': 'MuteNumber',
        'no_interact_number': 'NoInteractNumber',
        'os': 'Os',
        'play_status': 'PlayStatus',
        'region': 'Region',
        'shift_screen_number': 'ShiftScreenNumber',
        'silence_status': 'SilenceStatus',
        'thumb_up_number': 'ThumbUpNumber',
        'trial_time': 'TrialTime',
        'user_credit': 'UserCredit',
        'user_device': 'UserDevice',
        'user_id': 'UserId',
        'user_name': 'UserName',
        'user_tel': 'UserTel',
        'view_duration': 'ViewDuration',
        'watch_time': 'WatchTime',
        'leave_at': 'leaveAt'
    }

    def __init__(self, access_time=None, access_time_arr=None, channel_name=None, check_in_number=None, comment_counts=None, cookie=None, department=None, enter_review_age=None, enter_review_birthday=None, enter_review_company=None, enter_review_education=None, enter_review_email=None, enter_review_industry=None, enter_review_name=None, enter_review_position=None, enter_review_sex=None, enter_review_tel=None, external_user_id=None, extra=None, ip=None, invite_code=None, inviter_external_id=None, inviter_id=None, inviter_name=None, is_banned_ip=None, is_banned_user=None, join_at=None, mute_number=None, no_interact_number=None, os=None, play_status=None, region=None, shift_screen_number=None, silence_status=None, thumb_up_number=None, trial_time=None, user_credit=None, user_device=None, user_id=None, user_name=None, user_tel=None, view_duration=None, watch_time=None, leave_at=None, _configuration=None):  # noqa: E501
        """DataForListUserBehaviorDataAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_time = None
        self._access_time_arr = None
        self._channel_name = None
        self._check_in_number = None
        self._comment_counts = None
        self._cookie = None
        self._department = None
        self._enter_review_age = None
        self._enter_review_birthday = None
        self._enter_review_company = None
        self._enter_review_education = None
        self._enter_review_email = None
        self._enter_review_industry = None
        self._enter_review_name = None
        self._enter_review_position = None
        self._enter_review_sex = None
        self._enter_review_tel = None
        self._external_user_id = None
        self._extra = None
        self._ip = None
        self._invite_code = None
        self._inviter_external_id = None
        self._inviter_id = None
        self._inviter_name = None
        self._is_banned_ip = None
        self._is_banned_user = None
        self._join_at = None
        self._mute_number = None
        self._no_interact_number = None
        self._os = None
        self._play_status = None
        self._region = None
        self._shift_screen_number = None
        self._silence_status = None
        self._thumb_up_number = None
        self._trial_time = None
        self._user_credit = None
        self._user_device = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self._view_duration = None
        self._watch_time = None
        self._leave_at = None
        self.discriminator = None

        if access_time is not None:
            self.access_time = access_time
        if access_time_arr is not None:
            self.access_time_arr = access_time_arr
        if channel_name is not None:
            self.channel_name = channel_name
        if check_in_number is not None:
            self.check_in_number = check_in_number
        if comment_counts is not None:
            self.comment_counts = comment_counts
        if cookie is not None:
            self.cookie = cookie
        if department is not None:
            self.department = department
        if enter_review_age is not None:
            self.enter_review_age = enter_review_age
        if enter_review_birthday is not None:
            self.enter_review_birthday = enter_review_birthday
        if enter_review_company is not None:
            self.enter_review_company = enter_review_company
        if enter_review_education is not None:
            self.enter_review_education = enter_review_education
        if enter_review_email is not None:
            self.enter_review_email = enter_review_email
        if enter_review_industry is not None:
            self.enter_review_industry = enter_review_industry
        if enter_review_name is not None:
            self.enter_review_name = enter_review_name
        if enter_review_position is not None:
            self.enter_review_position = enter_review_position
        if enter_review_sex is not None:
            self.enter_review_sex = enter_review_sex
        if enter_review_tel is not None:
            self.enter_review_tel = enter_review_tel
        if external_user_id is not None:
            self.external_user_id = external_user_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if invite_code is not None:
            self.invite_code = invite_code
        if inviter_external_id is not None:
            self.inviter_external_id = inviter_external_id
        if inviter_id is not None:
            self.inviter_id = inviter_id
        if inviter_name is not None:
            self.inviter_name = inviter_name
        if is_banned_ip is not None:
            self.is_banned_ip = is_banned_ip
        if is_banned_user is not None:
            self.is_banned_user = is_banned_user
        if join_at is not None:
            self.join_at = join_at
        if mute_number is not None:
            self.mute_number = mute_number
        if no_interact_number is not None:
            self.no_interact_number = no_interact_number
        if os is not None:
            self.os = os
        if play_status is not None:
            self.play_status = play_status
        if region is not None:
            self.region = region
        if shift_screen_number is not None:
            self.shift_screen_number = shift_screen_number
        if silence_status is not None:
            self.silence_status = silence_status
        if thumb_up_number is not None:
            self.thumb_up_number = thumb_up_number
        if trial_time is not None:
            self.trial_time = trial_time
        if user_credit is not None:
            self.user_credit = user_credit
        if user_device is not None:
            self.user_device = user_device
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel
        if view_duration is not None:
            self.view_duration = view_duration
        if watch_time is not None:
            self.watch_time = watch_time
        if leave_at is not None:
            self.leave_at = leave_at

    @property
    def access_time(self):
        """Gets the access_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The access_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_time

    @access_time.setter
    def access_time(self, access_time):
        """Sets the access_time of this DataForListUserBehaviorDataAPIOutput.


        :param access_time: The access_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._access_time = access_time

    @property
    def access_time_arr(self):
        """Gets the access_time_arr of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The access_time_arr of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._access_time_arr

    @access_time_arr.setter
    def access_time_arr(self, access_time_arr):
        """Sets the access_time_arr of this DataForListUserBehaviorDataAPIOutput.


        :param access_time_arr: The access_time_arr of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: list[str]
        """

        self._access_time_arr = access_time_arr

    @property
    def channel_name(self):
        """Gets the channel_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The channel_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_name

    @channel_name.setter
    def channel_name(self, channel_name):
        """Sets the channel_name of this DataForListUserBehaviorDataAPIOutput.


        :param channel_name: The channel_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._channel_name = channel_name

    @property
    def check_in_number(self):
        """Gets the check_in_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The check_in_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_in_number

    @check_in_number.setter
    def check_in_number(self, check_in_number):
        """Sets the check_in_number of this DataForListUserBehaviorDataAPIOutput.


        :param check_in_number: The check_in_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._check_in_number = check_in_number

    @property
    def comment_counts(self):
        """Gets the comment_counts of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The comment_counts of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._comment_counts

    @comment_counts.setter
    def comment_counts(self, comment_counts):
        """Sets the comment_counts of this DataForListUserBehaviorDataAPIOutput.


        :param comment_counts: The comment_counts of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._comment_counts = comment_counts

    @property
    def cookie(self):
        """Gets the cookie of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The cookie of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._cookie

    @cookie.setter
    def cookie(self, cookie):
        """Sets the cookie of this DataForListUserBehaviorDataAPIOutput.


        :param cookie: The cookie of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._cookie = cookie

    @property
    def department(self):
        """Gets the department of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The department of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._department

    @department.setter
    def department(self, department):
        """Sets the department of this DataForListUserBehaviorDataAPIOutput.


        :param department: The department of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._department = department

    @property
    def enter_review_age(self):
        """Gets the enter_review_age of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_age of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_age

    @enter_review_age.setter
    def enter_review_age(self, enter_review_age):
        """Sets the enter_review_age of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_age: The enter_review_age of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_age = enter_review_age

    @property
    def enter_review_birthday(self):
        """Gets the enter_review_birthday of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_birthday of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_birthday

    @enter_review_birthday.setter
    def enter_review_birthday(self, enter_review_birthday):
        """Sets the enter_review_birthday of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_birthday: The enter_review_birthday of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_birthday = enter_review_birthday

    @property
    def enter_review_company(self):
        """Gets the enter_review_company of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_company of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_company

    @enter_review_company.setter
    def enter_review_company(self, enter_review_company):
        """Sets the enter_review_company of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_company: The enter_review_company of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_company = enter_review_company

    @property
    def enter_review_education(self):
        """Gets the enter_review_education of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_education of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_education

    @enter_review_education.setter
    def enter_review_education(self, enter_review_education):
        """Sets the enter_review_education of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_education: The enter_review_education of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_education = enter_review_education

    @property
    def enter_review_email(self):
        """Gets the enter_review_email of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_email of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_email

    @enter_review_email.setter
    def enter_review_email(self, enter_review_email):
        """Sets the enter_review_email of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_email: The enter_review_email of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_email = enter_review_email

    @property
    def enter_review_industry(self):
        """Gets the enter_review_industry of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_industry of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_industry

    @enter_review_industry.setter
    def enter_review_industry(self, enter_review_industry):
        """Sets the enter_review_industry of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_industry: The enter_review_industry of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_industry = enter_review_industry

    @property
    def enter_review_name(self):
        """Gets the enter_review_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_name

    @enter_review_name.setter
    def enter_review_name(self, enter_review_name):
        """Sets the enter_review_name of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_name: The enter_review_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_name = enter_review_name

    @property
    def enter_review_position(self):
        """Gets the enter_review_position of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_position of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_position

    @enter_review_position.setter
    def enter_review_position(self, enter_review_position):
        """Sets the enter_review_position of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_position: The enter_review_position of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_position = enter_review_position

    @property
    def enter_review_sex(self):
        """Gets the enter_review_sex of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_sex of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_sex

    @enter_review_sex.setter
    def enter_review_sex(self, enter_review_sex):
        """Sets the enter_review_sex of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_sex: The enter_review_sex of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_sex = enter_review_sex

    @property
    def enter_review_tel(self):
        """Gets the enter_review_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The enter_review_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_tel

    @enter_review_tel.setter
    def enter_review_tel(self, enter_review_tel):
        """Sets the enter_review_tel of this DataForListUserBehaviorDataAPIOutput.


        :param enter_review_tel: The enter_review_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_tel = enter_review_tel

    @property
    def external_user_id(self):
        """Gets the external_user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The external_user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this DataForListUserBehaviorDataAPIOutput.


        :param external_user_id: The external_user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def extra(self):
        """Gets the extra of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The extra of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this DataForListUserBehaviorDataAPIOutput.


        :param extra: The extra of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this DataForListUserBehaviorDataAPIOutput.


        :param ip: The ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def invite_code(self):
        """Gets the invite_code of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The invite_code of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._invite_code

    @invite_code.setter
    def invite_code(self, invite_code):
        """Sets the invite_code of this DataForListUserBehaviorDataAPIOutput.


        :param invite_code: The invite_code of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._invite_code = invite_code

    @property
    def inviter_external_id(self):
        """Gets the inviter_external_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The inviter_external_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._inviter_external_id

    @inviter_external_id.setter
    def inviter_external_id(self, inviter_external_id):
        """Sets the inviter_external_id of this DataForListUserBehaviorDataAPIOutput.


        :param inviter_external_id: The inviter_external_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._inviter_external_id = inviter_external_id

    @property
    def inviter_id(self):
        """Gets the inviter_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The inviter_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._inviter_id

    @inviter_id.setter
    def inviter_id(self, inviter_id):
        """Sets the inviter_id of this DataForListUserBehaviorDataAPIOutput.


        :param inviter_id: The inviter_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._inviter_id = inviter_id

    @property
    def inviter_name(self):
        """Gets the inviter_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The inviter_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._inviter_name

    @inviter_name.setter
    def inviter_name(self, inviter_name):
        """Sets the inviter_name of this DataForListUserBehaviorDataAPIOutput.


        :param inviter_name: The inviter_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._inviter_name = inviter_name

    @property
    def is_banned_ip(self):
        """Gets the is_banned_ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The is_banned_ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_banned_ip

    @is_banned_ip.setter
    def is_banned_ip(self, is_banned_ip):
        """Sets the is_banned_ip of this DataForListUserBehaviorDataAPIOutput.


        :param is_banned_ip: The is_banned_ip of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_banned_ip = is_banned_ip

    @property
    def is_banned_user(self):
        """Gets the is_banned_user of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The is_banned_user of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_banned_user

    @is_banned_user.setter
    def is_banned_user(self, is_banned_user):
        """Sets the is_banned_user of this DataForListUserBehaviorDataAPIOutput.


        :param is_banned_user: The is_banned_user of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_banned_user = is_banned_user

    @property
    def join_at(self):
        """Gets the join_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The join_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._join_at

    @join_at.setter
    def join_at(self, join_at):
        """Sets the join_at of this DataForListUserBehaviorDataAPIOutput.


        :param join_at: The join_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: list[int]
        """

        self._join_at = join_at

    @property
    def mute_number(self):
        """Gets the mute_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The mute_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._mute_number

    @mute_number.setter
    def mute_number(self, mute_number):
        """Sets the mute_number of this DataForListUserBehaviorDataAPIOutput.


        :param mute_number: The mute_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._mute_number = mute_number

    @property
    def no_interact_number(self):
        """Gets the no_interact_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The no_interact_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._no_interact_number

    @no_interact_number.setter
    def no_interact_number(self, no_interact_number):
        """Sets the no_interact_number of this DataForListUserBehaviorDataAPIOutput.


        :param no_interact_number: The no_interact_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._no_interact_number = no_interact_number

    @property
    def os(self):
        """Gets the os of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The os of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this DataForListUserBehaviorDataAPIOutput.


        :param os: The os of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def play_status(self):
        """Gets the play_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The play_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._play_status

    @play_status.setter
    def play_status(self, play_status):
        """Sets the play_status of this DataForListUserBehaviorDataAPIOutput.


        :param play_status: The play_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._play_status = play_status

    @property
    def region(self):
        """Gets the region of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The region of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListUserBehaviorDataAPIOutput.


        :param region: The region of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def shift_screen_number(self):
        """Gets the shift_screen_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The shift_screen_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._shift_screen_number

    @shift_screen_number.setter
    def shift_screen_number(self, shift_screen_number):
        """Sets the shift_screen_number of this DataForListUserBehaviorDataAPIOutput.


        :param shift_screen_number: The shift_screen_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._shift_screen_number = shift_screen_number

    @property
    def silence_status(self):
        """Gets the silence_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The silence_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._silence_status

    @silence_status.setter
    def silence_status(self, silence_status):
        """Sets the silence_status of this DataForListUserBehaviorDataAPIOutput.


        :param silence_status: The silence_status of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._silence_status = silence_status

    @property
    def thumb_up_number(self):
        """Gets the thumb_up_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The thumb_up_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._thumb_up_number

    @thumb_up_number.setter
    def thumb_up_number(self, thumb_up_number):
        """Sets the thumb_up_number of this DataForListUserBehaviorDataAPIOutput.


        :param thumb_up_number: The thumb_up_number of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._thumb_up_number = thumb_up_number

    @property
    def trial_time(self):
        """Gets the trial_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The trial_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._trial_time

    @trial_time.setter
    def trial_time(self, trial_time):
        """Sets the trial_time of this DataForListUserBehaviorDataAPIOutput.


        :param trial_time: The trial_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._trial_time = trial_time

    @property
    def user_credit(self):
        """Gets the user_credit of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The user_credit of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_credit

    @user_credit.setter
    def user_credit(self, user_credit):
        """Sets the user_credit of this DataForListUserBehaviorDataAPIOutput.


        :param user_credit: The user_credit of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_credit = user_credit

    @property
    def user_device(self):
        """Gets the user_device of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The user_device of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_device

    @user_device.setter
    def user_device(self, user_device):
        """Sets the user_device of this DataForListUserBehaviorDataAPIOutput.


        :param user_device: The user_device of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: list[str]
        """

        self._user_device = user_device

    @property
    def user_id(self):
        """Gets the user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this DataForListUserBehaviorDataAPIOutput.


        :param user_id: The user_id of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The user_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this DataForListUserBehaviorDataAPIOutput.


        :param user_name: The user_name of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The user_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this DataForListUserBehaviorDataAPIOutput.


        :param user_tel: The user_tel of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    @property
    def view_duration(self):
        """Gets the view_duration of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The view_duration of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._view_duration

    @view_duration.setter
    def view_duration(self, view_duration):
        """Sets the view_duration of this DataForListUserBehaviorDataAPIOutput.


        :param view_duration: The view_duration of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._view_duration = view_duration

    @property
    def watch_time(self):
        """Gets the watch_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The watch_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_time

    @watch_time.setter
    def watch_time(self, watch_time):
        """Sets the watch_time of this DataForListUserBehaviorDataAPIOutput.


        :param watch_time: The watch_time of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._watch_time = watch_time

    @property
    def leave_at(self):
        """Gets the leave_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501


        :return: The leave_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._leave_at

    @leave_at.setter
    def leave_at(self, leave_at):
        """Sets the leave_at of this DataForListUserBehaviorDataAPIOutput.


        :param leave_at: The leave_at of this DataForListUserBehaviorDataAPIOutput.  # noqa: E501
        :type: list[int]
        """

        self._leave_at = leave_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListUserBehaviorDataAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListUserBehaviorDataAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListUserBehaviorDataAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
