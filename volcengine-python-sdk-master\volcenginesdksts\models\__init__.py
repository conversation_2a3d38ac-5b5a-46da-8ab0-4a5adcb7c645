# coding: utf-8

# flake8: noqa
"""
    sts

    No description provided (generated by <PERSON>wagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdksts.models.assume_role_request import AssumeRoleRequest
from volcenginesdksts.models.assume_role_response import AssumeRoleResponse
from volcenginesdksts.models.assumed_role_user_for_assume_role_output import AssumedRoleUserForAssumeRoleOutput
from volcenginesdksts.models.credentials_for_assume_role_output import CredentialsForAssumeRoleOutput
from volcenginesdksts.models.tag_for_assume_role_input import TagForAssumeRoleInput
