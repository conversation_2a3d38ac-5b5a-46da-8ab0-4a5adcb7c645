# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotaForCreateResourceQueueInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu': 'float',
        'gpu': 'float',
        'volume_size_gi_b': 'float'
    }

    attribute_map = {
        'cpu': 'Cpu',
        'gpu': 'Gpu',
        'volume_size_gi_b': 'VolumeSizeGiB'
    }

    def __init__(self, cpu=None, gpu=None, volume_size_gi_b=None, _configuration=None):  # noqa: E501
        """QuotaForCreateResourceQueueInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu = None
        self._gpu = None
        self._volume_size_gi_b = None
        self.discriminator = None

        if cpu is not None:
            self.cpu = cpu
        if gpu is not None:
            self.gpu = gpu
        if volume_size_gi_b is not None:
            self.volume_size_gi_b = volume_size_gi_b

    @property
    def cpu(self):
        """Gets the cpu of this QuotaForCreateResourceQueueInput.  # noqa: E501


        :return: The cpu of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :rtype: float
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this QuotaForCreateResourceQueueInput.


        :param cpu: The cpu of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :type: float
        """

        self._cpu = cpu

    @property
    def gpu(self):
        """Gets the gpu of this QuotaForCreateResourceQueueInput.  # noqa: E501


        :return: The gpu of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :rtype: float
        """
        return self._gpu

    @gpu.setter
    def gpu(self, gpu):
        """Sets the gpu of this QuotaForCreateResourceQueueInput.


        :param gpu: The gpu of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :type: float
        """

        self._gpu = gpu

    @property
    def volume_size_gi_b(self):
        """Gets the volume_size_gi_b of this QuotaForCreateResourceQueueInput.  # noqa: E501


        :return: The volume_size_gi_b of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :rtype: float
        """
        return self._volume_size_gi_b

    @volume_size_gi_b.setter
    def volume_size_gi_b(self, volume_size_gi_b):
        """Sets the volume_size_gi_b of this QuotaForCreateResourceQueueInput.


        :param volume_size_gi_b: The volume_size_gi_b of this QuotaForCreateResourceQueueInput.  # noqa: E501
        :type: float
        """

        self._volume_size_gi_b = volume_size_gi_b

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotaForCreateResourceQueueInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotaForCreateResourceQueueInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotaForCreateResourceQueueInput):
            return True

        return self.to_dict() != other.to_dict()
