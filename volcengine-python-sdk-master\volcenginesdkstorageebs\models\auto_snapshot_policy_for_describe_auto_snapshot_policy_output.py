# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_snapshot_policy_id': 'str',
        'auto_snapshot_policy_name': 'str',
        'created_at': 'str',
        'destination_region': 'str',
        'destination_retention_days': 'int',
        'enable_copy': 'bool',
        'project_name': 'str',
        'repeat_days': 'int',
        'repeat_weekdays': 'list[str]',
        'retention_days': 'int',
        'status': 'str',
        'tags': 'list[TagForDescribeAutoSnapshotPolicyOutput]',
        'time_points': 'list[str]',
        'updated_at': 'str',
        'volume_nums': 'int'
    }

    attribute_map = {
        'auto_snapshot_policy_id': 'AutoSnapshotPolicyId',
        'auto_snapshot_policy_name': 'AutoSnapshotPolicyName',
        'created_at': 'CreatedAt',
        'destination_region': 'DestinationRegion',
        'destination_retention_days': 'DestinationRetentionDays',
        'enable_copy': 'EnableCopy',
        'project_name': 'ProjectName',
        'repeat_days': 'RepeatDays',
        'repeat_weekdays': 'RepeatWeekdays',
        'retention_days': 'RetentionDays',
        'status': 'Status',
        'tags': 'Tags',
        'time_points': 'TimePoints',
        'updated_at': 'UpdatedAt',
        'volume_nums': 'VolumeNums'
    }

    def __init__(self, auto_snapshot_policy_id=None, auto_snapshot_policy_name=None, created_at=None, destination_region=None, destination_retention_days=None, enable_copy=None, project_name=None, repeat_days=None, repeat_weekdays=None, retention_days=None, status=None, tags=None, time_points=None, updated_at=None, volume_nums=None, _configuration=None):  # noqa: E501
        """AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_snapshot_policy_id = None
        self._auto_snapshot_policy_name = None
        self._created_at = None
        self._destination_region = None
        self._destination_retention_days = None
        self._enable_copy = None
        self._project_name = None
        self._repeat_days = None
        self._repeat_weekdays = None
        self._retention_days = None
        self._status = None
        self._tags = None
        self._time_points = None
        self._updated_at = None
        self._volume_nums = None
        self.discriminator = None

        if auto_snapshot_policy_id is not None:
            self.auto_snapshot_policy_id = auto_snapshot_policy_id
        if auto_snapshot_policy_name is not None:
            self.auto_snapshot_policy_name = auto_snapshot_policy_name
        if created_at is not None:
            self.created_at = created_at
        if destination_region is not None:
            self.destination_region = destination_region
        if destination_retention_days is not None:
            self.destination_retention_days = destination_retention_days
        if enable_copy is not None:
            self.enable_copy = enable_copy
        if project_name is not None:
            self.project_name = project_name
        if repeat_days is not None:
            self.repeat_days = repeat_days
        if repeat_weekdays is not None:
            self.repeat_weekdays = repeat_weekdays
        if retention_days is not None:
            self.retention_days = retention_days
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if time_points is not None:
            self.time_points = time_points
        if updated_at is not None:
            self.updated_at = updated_at
        if volume_nums is not None:
            self.volume_nums = volume_nums

    @property
    def auto_snapshot_policy_id(self):
        """Gets the auto_snapshot_policy_id of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The auto_snapshot_policy_id of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_snapshot_policy_id

    @auto_snapshot_policy_id.setter
    def auto_snapshot_policy_id(self, auto_snapshot_policy_id):
        """Sets the auto_snapshot_policy_id of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param auto_snapshot_policy_id: The auto_snapshot_policy_id of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._auto_snapshot_policy_id = auto_snapshot_policy_id

    @property
    def auto_snapshot_policy_name(self):
        """Gets the auto_snapshot_policy_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The auto_snapshot_policy_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_snapshot_policy_name

    @auto_snapshot_policy_name.setter
    def auto_snapshot_policy_name(self, auto_snapshot_policy_name):
        """Sets the auto_snapshot_policy_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param auto_snapshot_policy_name: The auto_snapshot_policy_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._auto_snapshot_policy_name = auto_snapshot_policy_name

    @property
    def created_at(self):
        """Gets the created_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The created_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param created_at: The created_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def destination_region(self):
        """Gets the destination_region of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The destination_region of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_region

    @destination_region.setter
    def destination_region(self, destination_region):
        """Sets the destination_region of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param destination_region: The destination_region of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._destination_region = destination_region

    @property
    def destination_retention_days(self):
        """Gets the destination_retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The destination_retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._destination_retention_days

    @destination_retention_days.setter
    def destination_retention_days(self, destination_retention_days):
        """Sets the destination_retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param destination_retention_days: The destination_retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: int
        """

        self._destination_retention_days = destination_retention_days

    @property
    def enable_copy(self):
        """Gets the enable_copy of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The enable_copy of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_copy

    @enable_copy.setter
    def enable_copy(self, enable_copy):
        """Sets the enable_copy of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param enable_copy: The enable_copy of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: bool
        """

        self._enable_copy = enable_copy

    @property
    def project_name(self):
        """Gets the project_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The project_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param project_name: The project_name of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def repeat_days(self):
        """Gets the repeat_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The repeat_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._repeat_days

    @repeat_days.setter
    def repeat_days(self, repeat_days):
        """Sets the repeat_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param repeat_days: The repeat_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: int
        """

        self._repeat_days = repeat_days

    @property
    def repeat_weekdays(self):
        """Gets the repeat_weekdays of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The repeat_weekdays of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._repeat_weekdays

    @repeat_weekdays.setter
    def repeat_weekdays(self, repeat_weekdays):
        """Sets the repeat_weekdays of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param repeat_weekdays: The repeat_weekdays of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: list[str]
        """

        self._repeat_weekdays = repeat_weekdays

    @property
    def retention_days(self):
        """Gets the retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._retention_days

    @retention_days.setter
    def retention_days(self, retention_days):
        """Sets the retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param retention_days: The retention_days of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: int
        """

        self._retention_days = retention_days

    @property
    def status(self):
        """Gets the status of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The status of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param status: The status of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The tags of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: list[TagForDescribeAutoSnapshotPolicyOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param tags: The tags of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: list[TagForDescribeAutoSnapshotPolicyOutput]
        """

        self._tags = tags

    @property
    def time_points(self):
        """Gets the time_points of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The time_points of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._time_points

    @time_points.setter
    def time_points(self, time_points):
        """Sets the time_points of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param time_points: The time_points of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: list[str]
        """

        self._time_points = time_points

    @property
    def updated_at(self):
        """Gets the updated_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The updated_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param updated_at: The updated_at of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def volume_nums(self):
        """Gets the volume_nums of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501


        :return: The volume_nums of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._volume_nums

    @volume_nums.setter
    def volume_nums(self, volume_nums):
        """Sets the volume_nums of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.


        :param volume_nums: The volume_nums of this AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput.  # noqa: E501
        :type: int
        """

        self._volume_nums = volume_nums

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AutoSnapshotPolicyForDescribeAutoSnapshotPolicyOutput):
            return True

        return self.to_dict() != other.to_dict()
