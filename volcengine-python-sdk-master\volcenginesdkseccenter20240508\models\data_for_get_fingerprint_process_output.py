# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetFingerprintProcessOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'checksum': 'str',
        'cmdline': 'str',
        'comm': 'str',
        'common': 'bool',
        'container_id': 'str',
        'container_name': 'str',
        'eip_address': 'str',
        'euid': 'str',
        'eusername': 'str',
        'exe': 'str',
        'hostname': 'str',
        'id': 'str',
        'integrity': 'bool',
        'pid': 'str',
        'ppid': 'str',
        'primary_ip_address': 'str',
        'start_time': 'int',
        'state': 'str',
        'uid': 'str',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'checksum': 'Checksum',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'common': 'Common',
        'container_id': 'ContainerID',
        'container_name': 'ContainerName',
        'eip_address': 'EipAddress',
        'euid': 'Euid',
        'eusername': 'Eusername',
        'exe': 'Exe',
        'hostname': 'Hostname',
        'id': 'ID',
        'integrity': 'Integrity',
        'pid': 'Pid',
        'ppid': 'Ppid',
        'primary_ip_address': 'PrimaryIpAddress',
        'start_time': 'StartTime',
        'state': 'State',
        'uid': 'Uid',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, agent_id=None, agent_tags=None, checksum=None, cmdline=None, comm=None, common=None, container_id=None, container_name=None, eip_address=None, euid=None, eusername=None, exe=None, hostname=None, id=None, integrity=None, pid=None, ppid=None, primary_ip_address=None, start_time=None, state=None, uid=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetFingerprintProcessOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._checksum = None
        self._cmdline = None
        self._comm = None
        self._common = None
        self._container_id = None
        self._container_name = None
        self._eip_address = None
        self._euid = None
        self._eusername = None
        self._exe = None
        self._hostname = None
        self._id = None
        self._integrity = None
        self._pid = None
        self._ppid = None
        self._primary_ip_address = None
        self._start_time = None
        self._state = None
        self._uid = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if checksum is not None:
            self.checksum = checksum
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if common is not None:
            self.common = common
        if container_id is not None:
            self.container_id = container_id
        if container_name is not None:
            self.container_name = container_name
        if eip_address is not None:
            self.eip_address = eip_address
        if euid is not None:
            self.euid = euid
        if eusername is not None:
            self.eusername = eusername
        if exe is not None:
            self.exe = exe
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if integrity is not None:
            self.integrity = integrity
        if pid is not None:
            self.pid = pid
        if ppid is not None:
            self.ppid = ppid
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state
        if uid is not None:
            self.uid = uid
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The agent_id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetFingerprintProcessOutput.


        :param agent_id: The agent_id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The agent_tags of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForGetFingerprintProcessOutput.


        :param agent_tags: The agent_tags of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def checksum(self):
        """Gets the checksum of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The checksum of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._checksum

    @checksum.setter
    def checksum(self, checksum):
        """Sets the checksum of this DataForGetFingerprintProcessOutput.


        :param checksum: The checksum of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._checksum = checksum

    @property
    def cmdline(self):
        """Gets the cmdline of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The cmdline of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this DataForGetFingerprintProcessOutput.


        :param cmdline: The cmdline of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The comm of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this DataForGetFingerprintProcessOutput.


        :param comm: The comm of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def common(self):
        """Gets the common of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The common of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: bool
        """
        return self._common

    @common.setter
    def common(self, common):
        """Sets the common of this DataForGetFingerprintProcessOutput.


        :param common: The common of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: bool
        """

        self._common = common

    @property
    def container_id(self):
        """Gets the container_id of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The container_id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this DataForGetFingerprintProcessOutput.


        :param container_id: The container_id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def container_name(self):
        """Gets the container_name of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The container_name of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this DataForGetFingerprintProcessOutput.


        :param container_name: The container_name of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The eip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForGetFingerprintProcessOutput.


        :param eip_address: The eip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def euid(self):
        """Gets the euid of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The euid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._euid

    @euid.setter
    def euid(self, euid):
        """Sets the euid of this DataForGetFingerprintProcessOutput.


        :param euid: The euid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._euid = euid

    @property
    def eusername(self):
        """Gets the eusername of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The eusername of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._eusername

    @eusername.setter
    def eusername(self, eusername):
        """Sets the eusername of this DataForGetFingerprintProcessOutput.


        :param eusername: The eusername of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._eusername = eusername

    @property
    def exe(self):
        """Gets the exe of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The exe of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this DataForGetFingerprintProcessOutput.


        :param exe: The exe of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The hostname of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetFingerprintProcessOutput.


        :param hostname: The hostname of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetFingerprintProcessOutput.


        :param id: The id of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def integrity(self):
        """Gets the integrity of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The integrity of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: bool
        """
        return self._integrity

    @integrity.setter
    def integrity(self, integrity):
        """Sets the integrity of this DataForGetFingerprintProcessOutput.


        :param integrity: The integrity of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: bool
        """

        self._integrity = integrity

    @property
    def pid(self):
        """Gets the pid of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The pid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForGetFingerprintProcessOutput.


        :param pid: The pid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def ppid(self):
        """Gets the ppid of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The ppid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._ppid

    @ppid.setter
    def ppid(self, ppid):
        """Sets the ppid of this DataForGetFingerprintProcessOutput.


        :param ppid: The ppid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._ppid = ppid

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForGetFingerprintProcessOutput.


        :param primary_ip_address: The primary_ip_address of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The start_time of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetFingerprintProcessOutput.


        :param start_time: The start_time of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The state of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DataForGetFingerprintProcessOutput.


        :param state: The state of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def uid(self):
        """Gets the uid of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The uid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this DataForGetFingerprintProcessOutput.


        :param uid: The uid of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The update_time of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetFingerprintProcessOutput.


        :param update_time: The update_time of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetFingerprintProcessOutput.  # noqa: E501


        :return: The username of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetFingerprintProcessOutput.


        :param username: The username of this DataForGetFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetFingerprintProcessOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetFingerprintProcessOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetFingerprintProcessOutput):
            return True

        return self.to_dict() != other.to_dict()
