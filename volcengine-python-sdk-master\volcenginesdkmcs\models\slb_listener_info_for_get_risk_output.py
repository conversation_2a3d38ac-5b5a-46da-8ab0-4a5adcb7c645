# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SLBListenerInfoForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'acl_group_uids': 'list[str]',
        'name': 'str',
        'port': 'int',
        'protocol_type': 'str',
        'uid': 'str'
    }

    attribute_map = {
        'acl_group_uids': 'AclGroupUIDs',
        'name': 'Name',
        'port': 'Port',
        'protocol_type': 'ProtocolType',
        'uid': 'UID'
    }

    def __init__(self, acl_group_uids=None, name=None, port=None, protocol_type=None, uid=None, _configuration=None):  # noqa: E501
        """SLBListenerInfoForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._acl_group_uids = None
        self._name = None
        self._port = None
        self._protocol_type = None
        self._uid = None
        self.discriminator = None

        if acl_group_uids is not None:
            self.acl_group_uids = acl_group_uids
        if name is not None:
            self.name = name
        if port is not None:
            self.port = port
        if protocol_type is not None:
            self.protocol_type = protocol_type
        if uid is not None:
            self.uid = uid

    @property
    def acl_group_uids(self):
        """Gets the acl_group_uids of this SLBListenerInfoForGetRiskOutput.  # noqa: E501


        :return: The acl_group_uids of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._acl_group_uids

    @acl_group_uids.setter
    def acl_group_uids(self, acl_group_uids):
        """Sets the acl_group_uids of this SLBListenerInfoForGetRiskOutput.


        :param acl_group_uids: The acl_group_uids of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :type: list[str]
        """

        self._acl_group_uids = acl_group_uids

    @property
    def name(self):
        """Gets the name of this SLBListenerInfoForGetRiskOutput.  # noqa: E501


        :return: The name of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SLBListenerInfoForGetRiskOutput.


        :param name: The name of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def port(self):
        """Gets the port of this SLBListenerInfoForGetRiskOutput.  # noqa: E501


        :return: The port of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this SLBListenerInfoForGetRiskOutput.


        :param port: The port of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def protocol_type(self):
        """Gets the protocol_type of this SLBListenerInfoForGetRiskOutput.  # noqa: E501


        :return: The protocol_type of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this SLBListenerInfoForGetRiskOutput.


        :param protocol_type: The protocol_type of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._protocol_type = protocol_type

    @property
    def uid(self):
        """Gets the uid of this SLBListenerInfoForGetRiskOutput.  # noqa: E501


        :return: The uid of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this SLBListenerInfoForGetRiskOutput.


        :param uid: The uid of this SLBListenerInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SLBListenerInfoForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SLBListenerInfoForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SLBListenerInfoForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
