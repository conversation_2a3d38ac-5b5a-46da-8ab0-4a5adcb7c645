# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyOrderForModifyHostAcceleratePackOrderInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'execute_time': 'str',
        'new_douyin_id': 'str',
        'new_nickname': 'str',
        'new_uid': 'str',
        'old_uid': 'str',
        'order_id': 'str'
    }

    attribute_map = {
        'execute_time': 'ExecuteTime',
        'new_douyin_id': 'NewDouyinId',
        'new_nickname': 'NewNickname',
        'new_uid': 'NewUid',
        'old_uid': 'OldUid',
        'order_id': 'OrderId'
    }

    def __init__(self, execute_time=None, new_douyin_id=None, new_nickname=None, new_uid=None, old_uid=None, order_id=None, _configuration=None):  # noqa: E501
        """ModifyOrderForModifyHostAcceleratePackOrderInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._execute_time = None
        self._new_douyin_id = None
        self._new_nickname = None
        self._new_uid = None
        self._old_uid = None
        self._order_id = None
        self.discriminator = None

        if execute_time is not None:
            self.execute_time = execute_time
        if new_douyin_id is not None:
            self.new_douyin_id = new_douyin_id
        if new_nickname is not None:
            self.new_nickname = new_nickname
        if new_uid is not None:
            self.new_uid = new_uid
        if old_uid is not None:
            self.old_uid = old_uid
        if order_id is not None:
            self.order_id = order_id

    @property
    def execute_time(self):
        """Gets the execute_time of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The execute_time of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._execute_time

    @execute_time.setter
    def execute_time(self, execute_time):
        """Sets the execute_time of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param execute_time: The execute_time of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._execute_time = execute_time

    @property
    def new_douyin_id(self):
        """Gets the new_douyin_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The new_douyin_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._new_douyin_id

    @new_douyin_id.setter
    def new_douyin_id(self, new_douyin_id):
        """Sets the new_douyin_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param new_douyin_id: The new_douyin_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._new_douyin_id = new_douyin_id

    @property
    def new_nickname(self):
        """Gets the new_nickname of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The new_nickname of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._new_nickname

    @new_nickname.setter
    def new_nickname(self, new_nickname):
        """Sets the new_nickname of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param new_nickname: The new_nickname of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._new_nickname = new_nickname

    @property
    def new_uid(self):
        """Gets the new_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The new_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._new_uid

    @new_uid.setter
    def new_uid(self, new_uid):
        """Sets the new_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param new_uid: The new_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._new_uid = new_uid

    @property
    def old_uid(self):
        """Gets the old_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The old_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._old_uid

    @old_uid.setter
    def old_uid(self, old_uid):
        """Sets the old_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param old_uid: The old_uid of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._old_uid = old_uid

    @property
    def order_id(self):
        """Gets the order_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501


        :return: The order_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.


        :param order_id: The order_id of this ModifyOrderForModifyHostAcceleratePackOrderInput.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyOrderForModifyHostAcceleratePackOrderInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyOrderForModifyHostAcceleratePackOrderInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyOrderForModifyHostAcceleratePackOrderInput):
            return True

        return self.to_dict() != other.to_dict()
