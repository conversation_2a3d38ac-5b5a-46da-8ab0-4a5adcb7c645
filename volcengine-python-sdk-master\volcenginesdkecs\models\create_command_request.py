# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateCommandRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'command_content': 'str',
        'content_encoding': 'str',
        'description': 'str',
        'enable_parameter': 'bool',
        'name': 'str',
        'parameter_definitions': 'list[ParameterDefinitionForCreateCommandInput]',
        'project_name': 'str',
        'tags': 'list[TagForCreateCommandInput]',
        'timeout': 'int',
        'type': 'str',
        'username': 'str',
        'working_dir': 'str'
    }

    attribute_map = {
        'command_content': 'CommandContent',
        'content_encoding': 'ContentEncoding',
        'description': 'Description',
        'enable_parameter': 'EnableParameter',
        'name': 'Name',
        'parameter_definitions': 'ParameterDefinitions',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'timeout': 'Timeout',
        'type': 'Type',
        'username': 'Username',
        'working_dir': 'WorkingDir'
    }

    def __init__(self, command_content=None, content_encoding=None, description=None, enable_parameter=None, name=None, parameter_definitions=None, project_name=None, tags=None, timeout=None, type=None, username=None, working_dir=None, _configuration=None):  # noqa: E501
        """CreateCommandRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._command_content = None
        self._content_encoding = None
        self._description = None
        self._enable_parameter = None
        self._name = None
        self._parameter_definitions = None
        self._project_name = None
        self._tags = None
        self._timeout = None
        self._type = None
        self._username = None
        self._working_dir = None
        self.discriminator = None

        self.command_content = command_content
        if content_encoding is not None:
            self.content_encoding = content_encoding
        if description is not None:
            self.description = description
        if enable_parameter is not None:
            self.enable_parameter = enable_parameter
        self.name = name
        if parameter_definitions is not None:
            self.parameter_definitions = parameter_definitions
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if timeout is not None:
            self.timeout = timeout
        self.type = type
        if username is not None:
            self.username = username
        if working_dir is not None:
            self.working_dir = working_dir

    @property
    def command_content(self):
        """Gets the command_content of this CreateCommandRequest.  # noqa: E501


        :return: The command_content of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._command_content

    @command_content.setter
    def command_content(self, command_content):
        """Sets the command_content of this CreateCommandRequest.


        :param command_content: The command_content of this CreateCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and command_content is None:
            raise ValueError("Invalid value for `command_content`, must not be `None`")  # noqa: E501

        self._command_content = command_content

    @property
    def content_encoding(self):
        """Gets the content_encoding of this CreateCommandRequest.  # noqa: E501


        :return: The content_encoding of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._content_encoding

    @content_encoding.setter
    def content_encoding(self, content_encoding):
        """Sets the content_encoding of this CreateCommandRequest.


        :param content_encoding: The content_encoding of this CreateCommandRequest.  # noqa: E501
        :type: str
        """

        self._content_encoding = content_encoding

    @property
    def description(self):
        """Gets the description of this CreateCommandRequest.  # noqa: E501


        :return: The description of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateCommandRequest.


        :param description: The description of this CreateCommandRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable_parameter(self):
        """Gets the enable_parameter of this CreateCommandRequest.  # noqa: E501


        :return: The enable_parameter of this CreateCommandRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_parameter

    @enable_parameter.setter
    def enable_parameter(self, enable_parameter):
        """Sets the enable_parameter of this CreateCommandRequest.


        :param enable_parameter: The enable_parameter of this CreateCommandRequest.  # noqa: E501
        :type: bool
        """

        self._enable_parameter = enable_parameter

    @property
    def name(self):
        """Gets the name of this CreateCommandRequest.  # noqa: E501


        :return: The name of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateCommandRequest.


        :param name: The name of this CreateCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def parameter_definitions(self):
        """Gets the parameter_definitions of this CreateCommandRequest.  # noqa: E501


        :return: The parameter_definitions of this CreateCommandRequest.  # noqa: E501
        :rtype: list[ParameterDefinitionForCreateCommandInput]
        """
        return self._parameter_definitions

    @parameter_definitions.setter
    def parameter_definitions(self, parameter_definitions):
        """Sets the parameter_definitions of this CreateCommandRequest.


        :param parameter_definitions: The parameter_definitions of this CreateCommandRequest.  # noqa: E501
        :type: list[ParameterDefinitionForCreateCommandInput]
        """

        self._parameter_definitions = parameter_definitions

    @property
    def project_name(self):
        """Gets the project_name of this CreateCommandRequest.  # noqa: E501


        :return: The project_name of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateCommandRequest.


        :param project_name: The project_name of this CreateCommandRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateCommandRequest.  # noqa: E501


        :return: The tags of this CreateCommandRequest.  # noqa: E501
        :rtype: list[TagForCreateCommandInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateCommandRequest.


        :param tags: The tags of this CreateCommandRequest.  # noqa: E501
        :type: list[TagForCreateCommandInput]
        """

        self._tags = tags

    @property
    def timeout(self):
        """Gets the timeout of this CreateCommandRequest.  # noqa: E501


        :return: The timeout of this CreateCommandRequest.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this CreateCommandRequest.


        :param timeout: The timeout of this CreateCommandRequest.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    @property
    def type(self):
        """Gets the type of this CreateCommandRequest.  # noqa: E501


        :return: The type of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateCommandRequest.


        :param type: The type of this CreateCommandRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def username(self):
        """Gets the username of this CreateCommandRequest.  # noqa: E501


        :return: The username of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this CreateCommandRequest.


        :param username: The username of this CreateCommandRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    @property
    def working_dir(self):
        """Gets the working_dir of this CreateCommandRequest.  # noqa: E501


        :return: The working_dir of this CreateCommandRequest.  # noqa: E501
        :rtype: str
        """
        return self._working_dir

    @working_dir.setter
    def working_dir(self, working_dir):
        """Sets the working_dir of this CreateCommandRequest.


        :param working_dir: The working_dir of this CreateCommandRequest.  # noqa: E501
        :type: str
        """

        self._working_dir = working_dir

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateCommandRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateCommandRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateCommandRequest):
            return True

        return self.to_dict() != other.to_dict()
