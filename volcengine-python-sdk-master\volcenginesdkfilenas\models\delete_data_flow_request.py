# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteDataFlowRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_delete_tls_log_topic': 'bool',
        'id': 'str',
        'policy': 'PolicyForDeleteDataFlowInput'
    }

    attribute_map = {
        'enable_delete_tls_log_topic': 'EnableDeleteTlsLogTopic',
        'id': 'Id',
        'policy': 'Policy'
    }

    def __init__(self, enable_delete_tls_log_topic=None, id=None, policy=None, _configuration=None):  # noqa: E501
        """DeleteDataFlowRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_delete_tls_log_topic = None
        self._id = None
        self._policy = None
        self.discriminator = None

        if enable_delete_tls_log_topic is not None:
            self.enable_delete_tls_log_topic = enable_delete_tls_log_topic
        self.id = id
        if policy is not None:
            self.policy = policy

    @property
    def enable_delete_tls_log_topic(self):
        """Gets the enable_delete_tls_log_topic of this DeleteDataFlowRequest.  # noqa: E501


        :return: The enable_delete_tls_log_topic of this DeleteDataFlowRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_delete_tls_log_topic

    @enable_delete_tls_log_topic.setter
    def enable_delete_tls_log_topic(self, enable_delete_tls_log_topic):
        """Sets the enable_delete_tls_log_topic of this DeleteDataFlowRequest.


        :param enable_delete_tls_log_topic: The enable_delete_tls_log_topic of this DeleteDataFlowRequest.  # noqa: E501
        :type: bool
        """

        self._enable_delete_tls_log_topic = enable_delete_tls_log_topic

    @property
    def id(self):
        """Gets the id of this DeleteDataFlowRequest.  # noqa: E501


        :return: The id of this DeleteDataFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DeleteDataFlowRequest.


        :param id: The id of this DeleteDataFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def policy(self):
        """Gets the policy of this DeleteDataFlowRequest.  # noqa: E501


        :return: The policy of this DeleteDataFlowRequest.  # noqa: E501
        :rtype: PolicyForDeleteDataFlowInput
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this DeleteDataFlowRequest.


        :param policy: The policy of this DeleteDataFlowRequest.  # noqa: E501
        :type: PolicyForDeleteDataFlowInput
        """

        self._policy = policy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteDataFlowRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteDataFlowRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteDataFlowRequest):
            return True

        return self.to_dict() != other.to_dict()
