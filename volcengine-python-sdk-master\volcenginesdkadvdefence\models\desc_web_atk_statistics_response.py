# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebAtkStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attack_flow': 'list[AttackFlowForDescWebAtkStatisticsOutput]',
        'back_src_flow': 'list[BackSrcFlowForDescWebAtkStatisticsOutput]',
        'in_query_flow': 'list[InQueryFlowForDescWebAtkStatisticsOutput]'
    }

    attribute_map = {
        'attack_flow': 'AttackFlow',
        'back_src_flow': 'BackSrcFlow',
        'in_query_flow': 'InQueryFlow'
    }

    def __init__(self, attack_flow=None, back_src_flow=None, in_query_flow=None, _configuration=None):  # noqa: E501
        """DescWebAtkStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attack_flow = None
        self._back_src_flow = None
        self._in_query_flow = None
        self.discriminator = None

        if attack_flow is not None:
            self.attack_flow = attack_flow
        if back_src_flow is not None:
            self.back_src_flow = back_src_flow
        if in_query_flow is not None:
            self.in_query_flow = in_query_flow

    @property
    def attack_flow(self):
        """Gets the attack_flow of this DescWebAtkStatisticsResponse.  # noqa: E501


        :return: The attack_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :rtype: list[AttackFlowForDescWebAtkStatisticsOutput]
        """
        return self._attack_flow

    @attack_flow.setter
    def attack_flow(self, attack_flow):
        """Sets the attack_flow of this DescWebAtkStatisticsResponse.


        :param attack_flow: The attack_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :type: list[AttackFlowForDescWebAtkStatisticsOutput]
        """

        self._attack_flow = attack_flow

    @property
    def back_src_flow(self):
        """Gets the back_src_flow of this DescWebAtkStatisticsResponse.  # noqa: E501


        :return: The back_src_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :rtype: list[BackSrcFlowForDescWebAtkStatisticsOutput]
        """
        return self._back_src_flow

    @back_src_flow.setter
    def back_src_flow(self, back_src_flow):
        """Sets the back_src_flow of this DescWebAtkStatisticsResponse.


        :param back_src_flow: The back_src_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :type: list[BackSrcFlowForDescWebAtkStatisticsOutput]
        """

        self._back_src_flow = back_src_flow

    @property
    def in_query_flow(self):
        """Gets the in_query_flow of this DescWebAtkStatisticsResponse.  # noqa: E501


        :return: The in_query_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :rtype: list[InQueryFlowForDescWebAtkStatisticsOutput]
        """
        return self._in_query_flow

    @in_query_flow.setter
    def in_query_flow(self, in_query_flow):
        """Sets the in_query_flow of this DescWebAtkStatisticsResponse.


        :param in_query_flow: The in_query_flow of this DescWebAtkStatisticsResponse.  # noqa: E501
        :type: list[InQueryFlowForDescWebAtkStatisticsOutput]
        """

        self._in_query_flow = in_query_flow

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebAtkStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebAtkStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebAtkStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
