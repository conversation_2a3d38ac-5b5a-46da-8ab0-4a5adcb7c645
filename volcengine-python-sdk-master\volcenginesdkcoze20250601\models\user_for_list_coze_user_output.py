# coding: utf-8

"""
    coze20250601

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserForListCozeUserOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'coze_user_id': 'str',
        'coze_user_in_enterprise': 'str',
        'coze_user_name': 'str',
        'created_time': 'str',
        'updated_time': 'str',
        'user_id': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'coze_user_id': 'CozeUserId',
        'coze_user_in_enterprise': 'CozeUserInEnterprise',
        'coze_user_name': 'CozeUserName',
        'created_time': 'CreatedTime',
        'updated_time': 'UpdatedTime',
        'user_id': 'UserId',
        'user_name': 'UserName'
    }

    def __init__(self, coze_user_id=None, coze_user_in_enterprise=None, coze_user_name=None, created_time=None, updated_time=None, user_id=None, user_name=None, _configuration=None):  # noqa: E501
        """UserForListCozeUserOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._coze_user_id = None
        self._coze_user_in_enterprise = None
        self._coze_user_name = None
        self._created_time = None
        self._updated_time = None
        self._user_id = None
        self._user_name = None
        self.discriminator = None

        if coze_user_id is not None:
            self.coze_user_id = coze_user_id
        if coze_user_in_enterprise is not None:
            self.coze_user_in_enterprise = coze_user_in_enterprise
        if coze_user_name is not None:
            self.coze_user_name = coze_user_name
        if created_time is not None:
            self.created_time = created_time
        if updated_time is not None:
            self.updated_time = updated_time
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name

    @property
    def coze_user_id(self):
        """Gets the coze_user_id of this UserForListCozeUserOutput.  # noqa: E501


        :return: The coze_user_id of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._coze_user_id

    @coze_user_id.setter
    def coze_user_id(self, coze_user_id):
        """Sets the coze_user_id of this UserForListCozeUserOutput.


        :param coze_user_id: The coze_user_id of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._coze_user_id = coze_user_id

    @property
    def coze_user_in_enterprise(self):
        """Gets the coze_user_in_enterprise of this UserForListCozeUserOutput.  # noqa: E501


        :return: The coze_user_in_enterprise of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._coze_user_in_enterprise

    @coze_user_in_enterprise.setter
    def coze_user_in_enterprise(self, coze_user_in_enterprise):
        """Sets the coze_user_in_enterprise of this UserForListCozeUserOutput.


        :param coze_user_in_enterprise: The coze_user_in_enterprise of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._coze_user_in_enterprise = coze_user_in_enterprise

    @property
    def coze_user_name(self):
        """Gets the coze_user_name of this UserForListCozeUserOutput.  # noqa: E501


        :return: The coze_user_name of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._coze_user_name

    @coze_user_name.setter
    def coze_user_name(self, coze_user_name):
        """Sets the coze_user_name of this UserForListCozeUserOutput.


        :param coze_user_name: The coze_user_name of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._coze_user_name = coze_user_name

    @property
    def created_time(self):
        """Gets the created_time of this UserForListCozeUserOutput.  # noqa: E501


        :return: The created_time of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this UserForListCozeUserOutput.


        :param created_time: The created_time of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def updated_time(self):
        """Gets the updated_time of this UserForListCozeUserOutput.  # noqa: E501


        :return: The updated_time of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this UserForListCozeUserOutput.


        :param updated_time: The updated_time of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    @property
    def user_id(self):
        """Gets the user_id of this UserForListCozeUserOutput.  # noqa: E501


        :return: The user_id of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UserForListCozeUserOutput.


        :param user_id: The user_id of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this UserForListCozeUserOutput.  # noqa: E501


        :return: The user_name of this UserForListCozeUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this UserForListCozeUserOutput.


        :param user_name: The user_name of this UserForListCozeUserOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserForListCozeUserOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserForListCozeUserOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserForListCozeUserOutput):
            return True

        return self.to_dict() != other.to_dict()
