# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListSpacesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'order': 'str',
        'page_number': 'str',
        'page_size': 'str',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForListSpacesInput]'
    }

    attribute_map = {
        'order': 'Order',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, order=None, page_number=None, page_size=None, project_name=None, tag_filters=None, _configuration=None):  # noqa: E501
        """ListSpacesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._order = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self.discriminator = None

        if order is not None:
            self.order = order
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def order(self):
        """Gets the order of this ListSpacesRequest.  # noqa: E501


        :return: The order of this ListSpacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._order

    @order.setter
    def order(self, order):
        """Sets the order of this ListSpacesRequest.


        :param order: The order of this ListSpacesRequest.  # noqa: E501
        :type: str
        """

        self._order = order

    @property
    def page_number(self):
        """Gets the page_number of this ListSpacesRequest.  # noqa: E501


        :return: The page_number of this ListSpacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListSpacesRequest.


        :param page_number: The page_number of this ListSpacesRequest.  # noqa: E501
        :type: str
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListSpacesRequest.  # noqa: E501


        :return: The page_size of this ListSpacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListSpacesRequest.


        :param page_size: The page_size of this ListSpacesRequest.  # noqa: E501
        :type: str
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListSpacesRequest.  # noqa: E501


        :return: The project_name of this ListSpacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListSpacesRequest.


        :param project_name: The project_name of this ListSpacesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this ListSpacesRequest.  # noqa: E501


        :return: The tag_filters of this ListSpacesRequest.  # noqa: E501
        :rtype: list[TagFilterForListSpacesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this ListSpacesRequest.


        :param tag_filters: The tag_filters of this ListSpacesRequest.  # noqa: E501
        :type: list[TagFilterForListSpacesInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListSpacesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListSpacesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListSpacesRequest):
            return True

        return self.to_dict() != other.to_dict()
