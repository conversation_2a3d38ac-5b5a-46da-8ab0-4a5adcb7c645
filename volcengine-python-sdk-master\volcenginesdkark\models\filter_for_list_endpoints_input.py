# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListEndpointsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_model_ids': 'list[str]',
        'endpoint_model_types': 'list[str]',
        'foundation_model_name': 'str',
        'ids': 'list[str]',
        'model_versions': 'list[str]',
        'name': 'str',
        'statuses': 'list[str]'
    }

    attribute_map = {
        'custom_model_ids': 'CustomModelIds',
        'endpoint_model_types': 'EndpointModelTypes',
        'foundation_model_name': 'FoundationModelName',
        'ids': 'Ids',
        'model_versions': 'ModelVersions',
        'name': 'Name',
        'statuses': 'Statuses'
    }

    def __init__(self, custom_model_ids=None, endpoint_model_types=None, foundation_model_name=None, ids=None, model_versions=None, name=None, statuses=None, _configuration=None):  # noqa: E501
        """FilterForListEndpointsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_model_ids = None
        self._endpoint_model_types = None
        self._foundation_model_name = None
        self._ids = None
        self._model_versions = None
        self._name = None
        self._statuses = None
        self.discriminator = None

        if custom_model_ids is not None:
            self.custom_model_ids = custom_model_ids
        if endpoint_model_types is not None:
            self.endpoint_model_types = endpoint_model_types
        if foundation_model_name is not None:
            self.foundation_model_name = foundation_model_name
        if ids is not None:
            self.ids = ids
        if model_versions is not None:
            self.model_versions = model_versions
        if name is not None:
            self.name = name
        if statuses is not None:
            self.statuses = statuses

    @property
    def custom_model_ids(self):
        """Gets the custom_model_ids of this FilterForListEndpointsInput.  # noqa: E501


        :return: The custom_model_ids of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._custom_model_ids

    @custom_model_ids.setter
    def custom_model_ids(self, custom_model_ids):
        """Sets the custom_model_ids of this FilterForListEndpointsInput.


        :param custom_model_ids: The custom_model_ids of this FilterForListEndpointsInput.  # noqa: E501
        :type: list[str]
        """

        self._custom_model_ids = custom_model_ids

    @property
    def endpoint_model_types(self):
        """Gets the endpoint_model_types of this FilterForListEndpointsInput.  # noqa: E501


        :return: The endpoint_model_types of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_model_types

    @endpoint_model_types.setter
    def endpoint_model_types(self, endpoint_model_types):
        """Sets the endpoint_model_types of this FilterForListEndpointsInput.


        :param endpoint_model_types: The endpoint_model_types of this FilterForListEndpointsInput.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_model_types = endpoint_model_types

    @property
    def foundation_model_name(self):
        """Gets the foundation_model_name of this FilterForListEndpointsInput.  # noqa: E501


        :return: The foundation_model_name of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: str
        """
        return self._foundation_model_name

    @foundation_model_name.setter
    def foundation_model_name(self, foundation_model_name):
        """Sets the foundation_model_name of this FilterForListEndpointsInput.


        :param foundation_model_name: The foundation_model_name of this FilterForListEndpointsInput.  # noqa: E501
        :type: str
        """

        self._foundation_model_name = foundation_model_name

    @property
    def ids(self):
        """Gets the ids of this FilterForListEndpointsInput.  # noqa: E501


        :return: The ids of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListEndpointsInput.


        :param ids: The ids of this FilterForListEndpointsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def model_versions(self):
        """Gets the model_versions of this FilterForListEndpointsInput.  # noqa: E501


        :return: The model_versions of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._model_versions

    @model_versions.setter
    def model_versions(self, model_versions):
        """Sets the model_versions of this FilterForListEndpointsInput.


        :param model_versions: The model_versions of this FilterForListEndpointsInput.  # noqa: E501
        :type: list[str]
        """

        self._model_versions = model_versions

    @property
    def name(self):
        """Gets the name of this FilterForListEndpointsInput.  # noqa: E501


        :return: The name of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListEndpointsInput.


        :param name: The name of this FilterForListEndpointsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def statuses(self):
        """Gets the statuses of this FilterForListEndpointsInput.  # noqa: E501


        :return: The statuses of this FilterForListEndpointsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._statuses

    @statuses.setter
    def statuses(self, statuses):
        """Sets the statuses of this FilterForListEndpointsInput.


        :param statuses: The statuses of this FilterForListEndpointsInput.  # noqa: E501
        :type: list[str]
        """

        self._statuses = statuses

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListEndpointsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListEndpointsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListEndpointsInput):
            return True

        return self.to_dict() != other.to_dict()
