# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryPriceForRenewResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'currency': 'str',
        'instance_amount_list': 'list[InstanceAmountListForQueryPriceForRenewOutput]',
        'total_discount_amount': 'str',
        'total_original_amount': 'str'
    }

    attribute_map = {
        'currency': 'Currency',
        'instance_amount_list': 'InstanceAmountList',
        'total_discount_amount': 'TotalDiscountAmount',
        'total_original_amount': 'TotalOriginalAmount'
    }

    def __init__(self, currency=None, instance_amount_list=None, total_discount_amount=None, total_original_amount=None, _configuration=None):  # noqa: E501
        """QueryPriceForRenewResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._currency = None
        self._instance_amount_list = None
        self._total_discount_amount = None
        self._total_original_amount = None
        self.discriminator = None

        if currency is not None:
            self.currency = currency
        if instance_amount_list is not None:
            self.instance_amount_list = instance_amount_list
        if total_discount_amount is not None:
            self.total_discount_amount = total_discount_amount
        if total_original_amount is not None:
            self.total_original_amount = total_original_amount

    @property
    def currency(self):
        """Gets the currency of this QueryPriceForRenewResponse.  # noqa: E501


        :return: The currency of this QueryPriceForRenewResponse.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this QueryPriceForRenewResponse.


        :param currency: The currency of this QueryPriceForRenewResponse.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def instance_amount_list(self):
        """Gets the instance_amount_list of this QueryPriceForRenewResponse.  # noqa: E501


        :return: The instance_amount_list of this QueryPriceForRenewResponse.  # noqa: E501
        :rtype: list[InstanceAmountListForQueryPriceForRenewOutput]
        """
        return self._instance_amount_list

    @instance_amount_list.setter
    def instance_amount_list(self, instance_amount_list):
        """Sets the instance_amount_list of this QueryPriceForRenewResponse.


        :param instance_amount_list: The instance_amount_list of this QueryPriceForRenewResponse.  # noqa: E501
        :type: list[InstanceAmountListForQueryPriceForRenewOutput]
        """

        self._instance_amount_list = instance_amount_list

    @property
    def total_discount_amount(self):
        """Gets the total_discount_amount of this QueryPriceForRenewResponse.  # noqa: E501


        :return: The total_discount_amount of this QueryPriceForRenewResponse.  # noqa: E501
        :rtype: str
        """
        return self._total_discount_amount

    @total_discount_amount.setter
    def total_discount_amount(self, total_discount_amount):
        """Sets the total_discount_amount of this QueryPriceForRenewResponse.


        :param total_discount_amount: The total_discount_amount of this QueryPriceForRenewResponse.  # noqa: E501
        :type: str
        """

        self._total_discount_amount = total_discount_amount

    @property
    def total_original_amount(self):
        """Gets the total_original_amount of this QueryPriceForRenewResponse.  # noqa: E501


        :return: The total_original_amount of this QueryPriceForRenewResponse.  # noqa: E501
        :rtype: str
        """
        return self._total_original_amount

    @total_original_amount.setter
    def total_original_amount(self, total_original_amount):
        """Sets the total_original_amount of this QueryPriceForRenewResponse.


        :param total_original_amount: The total_original_amount of this QueryPriceForRenewResponse.  # noqa: E501
        :type: str
        """

        self._total_original_amount = total_original_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryPriceForRenewResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryPriceForRenewResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryPriceForRenewResponse):
            return True

        return self.to_dict() != other.to_dict()
