# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RuntimeConfigForGetJobOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'advance_args': 'str',
        'code': 'CodeForGetJobOutput',
        'command': 'str',
        'envs': 'list[EnvForGetJobOutput]',
        'framework': 'str',
        'image': 'ImageForGetJobOutput'
    }

    attribute_map = {
        'advance_args': 'AdvanceArgs',
        'code': 'Code',
        'command': 'Command',
        'envs': 'Envs',
        'framework': 'Framework',
        'image': 'Image'
    }

    def __init__(self, advance_args=None, code=None, command=None, envs=None, framework=None, image=None, _configuration=None):  # noqa: E501
        """RuntimeConfigForGetJobOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._advance_args = None
        self._code = None
        self._command = None
        self._envs = None
        self._framework = None
        self._image = None
        self.discriminator = None

        if advance_args is not None:
            self.advance_args = advance_args
        if code is not None:
            self.code = code
        if command is not None:
            self.command = command
        if envs is not None:
            self.envs = envs
        if framework is not None:
            self.framework = framework
        if image is not None:
            self.image = image

    @property
    def advance_args(self):
        """Gets the advance_args of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The advance_args of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._advance_args

    @advance_args.setter
    def advance_args(self, advance_args):
        """Sets the advance_args of this RuntimeConfigForGetJobOutput.


        :param advance_args: The advance_args of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._advance_args = advance_args

    @property
    def code(self):
        """Gets the code of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The code of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: CodeForGetJobOutput
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this RuntimeConfigForGetJobOutput.


        :param code: The code of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: CodeForGetJobOutput
        """

        self._code = code

    @property
    def command(self):
        """Gets the command of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The command of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._command

    @command.setter
    def command(self, command):
        """Sets the command of this RuntimeConfigForGetJobOutput.


        :param command: The command of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._command = command

    @property
    def envs(self):
        """Gets the envs of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The envs of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: list[EnvForGetJobOutput]
        """
        return self._envs

    @envs.setter
    def envs(self, envs):
        """Sets the envs of this RuntimeConfigForGetJobOutput.


        :param envs: The envs of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: list[EnvForGetJobOutput]
        """

        self._envs = envs

    @property
    def framework(self):
        """Gets the framework of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The framework of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._framework

    @framework.setter
    def framework(self, framework):
        """Sets the framework of this RuntimeConfigForGetJobOutput.


        :param framework: The framework of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._framework = framework

    @property
    def image(self):
        """Gets the image of this RuntimeConfigForGetJobOutput.  # noqa: E501


        :return: The image of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :rtype: ImageForGetJobOutput
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this RuntimeConfigForGetJobOutput.


        :param image: The image of this RuntimeConfigForGetJobOutput.  # noqa: E501
        :type: ImageForGetJobOutput
        """

        self._image = image

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RuntimeConfigForGetJobOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RuntimeConfigForGetJobOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RuntimeConfigForGetJobOutput):
            return True

        return self.to_dict() != other.to_dict()
