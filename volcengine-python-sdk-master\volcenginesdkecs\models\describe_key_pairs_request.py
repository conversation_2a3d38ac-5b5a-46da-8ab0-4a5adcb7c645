# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeKeyPairsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'finger_print': 'str',
        'key_pair_ids': 'list[str]',
        'key_pair_name': 'str',
        'key_pair_names': 'list[str]',
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeKeyPairsInput]'
    }

    attribute_map = {
        'finger_print': 'FingerPrint',
        'key_pair_ids': 'KeyPairIds',
        'key_pair_name': 'KeyPairName',
        'key_pair_names': 'KeyPairNames',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, finger_print=None, key_pair_ids=None, key_pair_name=None, key_pair_names=None, max_results=None, next_token=None, project_name=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeKeyPairsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._finger_print = None
        self._key_pair_ids = None
        self._key_pair_name = None
        self._key_pair_names = None
        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._tag_filters = None
        self.discriminator = None

        if finger_print is not None:
            self.finger_print = finger_print
        if key_pair_ids is not None:
            self.key_pair_ids = key_pair_ids
        if key_pair_name is not None:
            self.key_pair_name = key_pair_name
        if key_pair_names is not None:
            self.key_pair_names = key_pair_names
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def finger_print(self):
        """Gets the finger_print of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The finger_print of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: str
        """
        return self._finger_print

    @finger_print.setter
    def finger_print(self, finger_print):
        """Sets the finger_print of this DescribeKeyPairsRequest.


        :param finger_print: The finger_print of this DescribeKeyPairsRequest.  # noqa: E501
        :type: str
        """

        self._finger_print = finger_print

    @property
    def key_pair_ids(self):
        """Gets the key_pair_ids of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The key_pair_ids of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._key_pair_ids

    @key_pair_ids.setter
    def key_pair_ids(self, key_pair_ids):
        """Sets the key_pair_ids of this DescribeKeyPairsRequest.


        :param key_pair_ids: The key_pair_ids of this DescribeKeyPairsRequest.  # noqa: E501
        :type: list[str]
        """

        self._key_pair_ids = key_pair_ids

    @property
    def key_pair_name(self):
        """Gets the key_pair_name of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The key_pair_name of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_name

    @key_pair_name.setter
    def key_pair_name(self, key_pair_name):
        """Sets the key_pair_name of this DescribeKeyPairsRequest.


        :param key_pair_name: The key_pair_name of this DescribeKeyPairsRequest.  # noqa: E501
        :type: str
        """

        self._key_pair_name = key_pair_name

    @property
    def key_pair_names(self):
        """Gets the key_pair_names of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The key_pair_names of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._key_pair_names

    @key_pair_names.setter
    def key_pair_names(self, key_pair_names):
        """Sets the key_pair_names of this DescribeKeyPairsRequest.


        :param key_pair_names: The key_pair_names of this DescribeKeyPairsRequest.  # noqa: E501
        :type: list[str]
        """

        self._key_pair_names = key_pair_names

    @property
    def max_results(self):
        """Gets the max_results of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The max_results of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeKeyPairsRequest.


        :param max_results: The max_results of this DescribeKeyPairsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The next_token of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeKeyPairsRequest.


        :param next_token: The next_token of this DescribeKeyPairsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The project_name of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeKeyPairsRequest.


        :param project_name: The project_name of this DescribeKeyPairsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeKeyPairsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeKeyPairsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeKeyPairsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeKeyPairsRequest.


        :param tag_filters: The tag_filters of this DescribeKeyPairsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeKeyPairsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeKeyPairsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeKeyPairsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeKeyPairsRequest):
            return True

        return self.to_dict() != other.to_dict()
