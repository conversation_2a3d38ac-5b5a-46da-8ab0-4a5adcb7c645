# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateClusterRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'cluster_config': 'ClusterConfigForCreateClusterInput',
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'kubernetes_version': 'str',
        'logging_config': 'LoggingConfigForCreateClusterInput',
        'name': 'str',
        'pods_config': 'PodsConfigForCreateClusterInput',
        'project_name': 'str',
        'services_config': 'ServicesConfigForCreateClusterInput',
        'tags': 'list[TagForCreateClusterInput]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'cluster_config': 'ClusterConfig',
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'kubernetes_version': 'KubernetesVersion',
        'logging_config': 'LoggingConfig',
        'name': 'Name',
        'pods_config': 'PodsConfig',
        'project_name': 'ProjectName',
        'services_config': 'ServicesConfig',
        'tags': 'Tags'
    }

    def __init__(self, client_token=None, cluster_config=None, delete_protection_enabled=None, description=None, kubernetes_version=None, logging_config=None, name=None, pods_config=None, project_name=None, services_config=None, tags=None, _configuration=None):  # noqa: E501
        """CreateClusterRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._cluster_config = None
        self._delete_protection_enabled = None
        self._description = None
        self._kubernetes_version = None
        self._logging_config = None
        self._name = None
        self._pods_config = None
        self._project_name = None
        self._services_config = None
        self._tags = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if cluster_config is not None:
            self.cluster_config = cluster_config
        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        if kubernetes_version is not None:
            self.kubernetes_version = kubernetes_version
        if logging_config is not None:
            self.logging_config = logging_config
        self.name = name
        if pods_config is not None:
            self.pods_config = pods_config
        if project_name is not None:
            self.project_name = project_name
        if services_config is not None:
            self.services_config = services_config
        if tags is not None:
            self.tags = tags

    @property
    def client_token(self):
        """Gets the client_token of this CreateClusterRequest.  # noqa: E501


        :return: The client_token of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateClusterRequest.


        :param client_token: The client_token of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_config(self):
        """Gets the cluster_config of this CreateClusterRequest.  # noqa: E501


        :return: The cluster_config of this CreateClusterRequest.  # noqa: E501
        :rtype: ClusterConfigForCreateClusterInput
        """
        return self._cluster_config

    @cluster_config.setter
    def cluster_config(self, cluster_config):
        """Sets the cluster_config of this CreateClusterRequest.


        :param cluster_config: The cluster_config of this CreateClusterRequest.  # noqa: E501
        :type: ClusterConfigForCreateClusterInput
        """

        self._cluster_config = cluster_config

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this CreateClusterRequest.  # noqa: E501


        :return: The delete_protection_enabled of this CreateClusterRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this CreateClusterRequest.


        :param delete_protection_enabled: The delete_protection_enabled of this CreateClusterRequest.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this CreateClusterRequest.  # noqa: E501


        :return: The description of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateClusterRequest.


        :param description: The description of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def kubernetes_version(self):
        """Gets the kubernetes_version of this CreateClusterRequest.  # noqa: E501


        :return: The kubernetes_version of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._kubernetes_version

    @kubernetes_version.setter
    def kubernetes_version(self, kubernetes_version):
        """Sets the kubernetes_version of this CreateClusterRequest.


        :param kubernetes_version: The kubernetes_version of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._kubernetes_version = kubernetes_version

    @property
    def logging_config(self):
        """Gets the logging_config of this CreateClusterRequest.  # noqa: E501


        :return: The logging_config of this CreateClusterRequest.  # noqa: E501
        :rtype: LoggingConfigForCreateClusterInput
        """
        return self._logging_config

    @logging_config.setter
    def logging_config(self, logging_config):
        """Sets the logging_config of this CreateClusterRequest.


        :param logging_config: The logging_config of this CreateClusterRequest.  # noqa: E501
        :type: LoggingConfigForCreateClusterInput
        """

        self._logging_config = logging_config

    @property
    def name(self):
        """Gets the name of this CreateClusterRequest.  # noqa: E501


        :return: The name of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateClusterRequest.


        :param name: The name of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def pods_config(self):
        """Gets the pods_config of this CreateClusterRequest.  # noqa: E501


        :return: The pods_config of this CreateClusterRequest.  # noqa: E501
        :rtype: PodsConfigForCreateClusterInput
        """
        return self._pods_config

    @pods_config.setter
    def pods_config(self, pods_config):
        """Sets the pods_config of this CreateClusterRequest.


        :param pods_config: The pods_config of this CreateClusterRequest.  # noqa: E501
        :type: PodsConfigForCreateClusterInput
        """

        self._pods_config = pods_config

    @property
    def project_name(self):
        """Gets the project_name of this CreateClusterRequest.  # noqa: E501


        :return: The project_name of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateClusterRequest.


        :param project_name: The project_name of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def services_config(self):
        """Gets the services_config of this CreateClusterRequest.  # noqa: E501


        :return: The services_config of this CreateClusterRequest.  # noqa: E501
        :rtype: ServicesConfigForCreateClusterInput
        """
        return self._services_config

    @services_config.setter
    def services_config(self, services_config):
        """Sets the services_config of this CreateClusterRequest.


        :param services_config: The services_config of this CreateClusterRequest.  # noqa: E501
        :type: ServicesConfigForCreateClusterInput
        """

        self._services_config = services_config

    @property
    def tags(self):
        """Gets the tags of this CreateClusterRequest.  # noqa: E501


        :return: The tags of this CreateClusterRequest.  # noqa: E501
        :rtype: list[TagForCreateClusterInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateClusterRequest.


        :param tags: The tags of this CreateClusterRequest.  # noqa: E501
        :type: list[TagForCreateClusterInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateClusterRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateClusterRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateClusterRequest):
            return True

        return self.to_dict() != other.to_dict()
