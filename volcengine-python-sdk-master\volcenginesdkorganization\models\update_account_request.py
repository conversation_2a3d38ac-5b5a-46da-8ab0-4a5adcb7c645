# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateAccountRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'account_name': 'str',
        'description': 'str',
        'show_name': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'account_name': 'AccountName',
        'description': 'Description',
        'show_name': 'ShowName'
    }

    def __init__(self, account_id=None, account_name=None, description=None, show_name=None, _configuration=None):  # noqa: E501
        """UpdateAccountRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._account_name = None
        self._description = None
        self._show_name = None
        self.discriminator = None

        self.account_id = account_id
        self.account_name = account_name
        if description is not None:
            self.description = description
        self.show_name = show_name

    @property
    def account_id(self):
        """Gets the account_id of this UpdateAccountRequest.  # noqa: E501


        :return: The account_id of this UpdateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this UpdateAccountRequest.


        :param account_id: The account_id of this UpdateAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_id is None:
            raise ValueError("Invalid value for `account_id`, must not be `None`")  # noqa: E501

        self._account_id = account_id

    @property
    def account_name(self):
        """Gets the account_name of this UpdateAccountRequest.  # noqa: E501


        :return: The account_name of this UpdateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this UpdateAccountRequest.


        :param account_name: The account_name of this UpdateAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_name is None:
            raise ValueError("Invalid value for `account_name`, must not be `None`")  # noqa: E501

        self._account_name = account_name

    @property
    def description(self):
        """Gets the description of this UpdateAccountRequest.  # noqa: E501


        :return: The description of this UpdateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateAccountRequest.


        :param description: The description of this UpdateAccountRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def show_name(self):
        """Gets the show_name of this UpdateAccountRequest.  # noqa: E501


        :return: The show_name of this UpdateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._show_name

    @show_name.setter
    def show_name(self, show_name):
        """Sets the show_name of this UpdateAccountRequest.


        :param show_name: The show_name of this UpdateAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and show_name is None:
            raise ValueError("Invalid value for `show_name`, must not be `None`")  # noqa: E501

        self._show_name = show_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateAccountRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateAccountRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateAccountRequest):
            return True

        return self.to_dict() != other.to_dict()
