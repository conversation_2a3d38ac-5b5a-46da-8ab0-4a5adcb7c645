# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeOrganizationResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'organization': 'OrganizationForDescribeOrganizationOutput',
        'owner': 'OwnerForDescribeOrganizationOutput'
    }

    attribute_map = {
        'organization': 'Organization',
        'owner': 'Owner'
    }

    def __init__(self, organization=None, owner=None, _configuration=None):  # noqa: E501
        """DescribeOrganizationResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._organization = None
        self._owner = None
        self.discriminator = None

        if organization is not None:
            self.organization = organization
        if owner is not None:
            self.owner = owner

    @property
    def organization(self):
        """Gets the organization of this DescribeOrganizationResponse.  # noqa: E501


        :return: The organization of this DescribeOrganizationResponse.  # noqa: E501
        :rtype: OrganizationForDescribeOrganizationOutput
        """
        return self._organization

    @organization.setter
    def organization(self, organization):
        """Sets the organization of this DescribeOrganizationResponse.


        :param organization: The organization of this DescribeOrganizationResponse.  # noqa: E501
        :type: OrganizationForDescribeOrganizationOutput
        """

        self._organization = organization

    @property
    def owner(self):
        """Gets the owner of this DescribeOrganizationResponse.  # noqa: E501


        :return: The owner of this DescribeOrganizationResponse.  # noqa: E501
        :rtype: OwnerForDescribeOrganizationOutput
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this DescribeOrganizationResponse.


        :param owner: The owner of this DescribeOrganizationResponse.  # noqa: E501
        :type: OwnerForDescribeOrganizationOutput
        """

        self._owner = owner

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeOrganizationResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeOrganizationResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeOrganizationResponse):
            return True

        return self.to_dict() != other.to_dict()
