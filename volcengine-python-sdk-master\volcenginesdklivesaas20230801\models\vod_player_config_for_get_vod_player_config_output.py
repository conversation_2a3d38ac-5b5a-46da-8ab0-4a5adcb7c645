# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VodPlayerConfigForGetVodPlayerConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'basic_config': 'BasicConfigForGetVodPlayerConfigOutput',
        'is_default_vod_player_config': 'int',
        'logo_config': 'LogoConfigForGetVodPlayerConfigOutput',
        'speed_config': 'SpeedConfigForGetVodPlayerConfigOutput',
        'ticker_config': 'TickerConfigForGetVodPlayerConfigOutput',
        'vod_player_config_id': 'str',
        'watermark_config': 'WatermarkConfigForGetVodPlayerConfigOutput',
        'zoom_config': 'ZoomConfigForGetVodPlayerConfigOutput'
    }

    attribute_map = {
        'basic_config': 'BasicConfig',
        'is_default_vod_player_config': 'IsDefaultVodPlayerConfig',
        'logo_config': 'LogoConfig',
        'speed_config': 'SpeedConfig',
        'ticker_config': 'TickerConfig',
        'vod_player_config_id': 'VodPlayerConfigId',
        'watermark_config': 'WatermarkConfig',
        'zoom_config': 'ZoomConfig'
    }

    def __init__(self, basic_config=None, is_default_vod_player_config=None, logo_config=None, speed_config=None, ticker_config=None, vod_player_config_id=None, watermark_config=None, zoom_config=None, _configuration=None):  # noqa: E501
        """VodPlayerConfigForGetVodPlayerConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._basic_config = None
        self._is_default_vod_player_config = None
        self._logo_config = None
        self._speed_config = None
        self._ticker_config = None
        self._vod_player_config_id = None
        self._watermark_config = None
        self._zoom_config = None
        self.discriminator = None

        if basic_config is not None:
            self.basic_config = basic_config
        if is_default_vod_player_config is not None:
            self.is_default_vod_player_config = is_default_vod_player_config
        if logo_config is not None:
            self.logo_config = logo_config
        if speed_config is not None:
            self.speed_config = speed_config
        if ticker_config is not None:
            self.ticker_config = ticker_config
        if vod_player_config_id is not None:
            self.vod_player_config_id = vod_player_config_id
        if watermark_config is not None:
            self.watermark_config = watermark_config
        if zoom_config is not None:
            self.zoom_config = zoom_config

    @property
    def basic_config(self):
        """Gets the basic_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The basic_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: BasicConfigForGetVodPlayerConfigOutput
        """
        return self._basic_config

    @basic_config.setter
    def basic_config(self, basic_config):
        """Sets the basic_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param basic_config: The basic_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: BasicConfigForGetVodPlayerConfigOutput
        """

        self._basic_config = basic_config

    @property
    def is_default_vod_player_config(self):
        """Gets the is_default_vod_player_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The is_default_vod_player_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_default_vod_player_config

    @is_default_vod_player_config.setter
    def is_default_vod_player_config(self, is_default_vod_player_config):
        """Sets the is_default_vod_player_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param is_default_vod_player_config: The is_default_vod_player_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: int
        """

        self._is_default_vod_player_config = is_default_vod_player_config

    @property
    def logo_config(self):
        """Gets the logo_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The logo_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: LogoConfigForGetVodPlayerConfigOutput
        """
        return self._logo_config

    @logo_config.setter
    def logo_config(self, logo_config):
        """Sets the logo_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param logo_config: The logo_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: LogoConfigForGetVodPlayerConfigOutput
        """

        self._logo_config = logo_config

    @property
    def speed_config(self):
        """Gets the speed_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The speed_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: SpeedConfigForGetVodPlayerConfigOutput
        """
        return self._speed_config

    @speed_config.setter
    def speed_config(self, speed_config):
        """Sets the speed_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param speed_config: The speed_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: SpeedConfigForGetVodPlayerConfigOutput
        """

        self._speed_config = speed_config

    @property
    def ticker_config(self):
        """Gets the ticker_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The ticker_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: TickerConfigForGetVodPlayerConfigOutput
        """
        return self._ticker_config

    @ticker_config.setter
    def ticker_config(self, ticker_config):
        """Sets the ticker_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param ticker_config: The ticker_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: TickerConfigForGetVodPlayerConfigOutput
        """

        self._ticker_config = ticker_config

    @property
    def vod_player_config_id(self):
        """Gets the vod_player_config_id of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The vod_player_config_id of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._vod_player_config_id

    @vod_player_config_id.setter
    def vod_player_config_id(self, vod_player_config_id):
        """Sets the vod_player_config_id of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param vod_player_config_id: The vod_player_config_id of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: str
        """

        self._vod_player_config_id = vod_player_config_id

    @property
    def watermark_config(self):
        """Gets the watermark_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The watermark_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: WatermarkConfigForGetVodPlayerConfigOutput
        """
        return self._watermark_config

    @watermark_config.setter
    def watermark_config(self, watermark_config):
        """Sets the watermark_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param watermark_config: The watermark_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: WatermarkConfigForGetVodPlayerConfigOutput
        """

        self._watermark_config = watermark_config

    @property
    def zoom_config(self):
        """Gets the zoom_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501


        :return: The zoom_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :rtype: ZoomConfigForGetVodPlayerConfigOutput
        """
        return self._zoom_config

    @zoom_config.setter
    def zoom_config(self, zoom_config):
        """Sets the zoom_config of this VodPlayerConfigForGetVodPlayerConfigOutput.


        :param zoom_config: The zoom_config of this VodPlayerConfigForGetVodPlayerConfigOutput.  # noqa: E501
        :type: ZoomConfigForGetVodPlayerConfigOutput
        """

        self._zoom_config = zoom_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VodPlayerConfigForGetVodPlayerConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VodPlayerConfigForGetVodPlayerConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VodPlayerConfigForGetVodPlayerConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
