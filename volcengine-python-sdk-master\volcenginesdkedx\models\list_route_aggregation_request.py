# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRouteAggregationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_cidr': 'str',
        'enabled': 'str',
        'page_number': 'str',
        'page_size': 'int',
        'vif_instance_id': 'str'
    }

    attribute_map = {
        'aggregation_cidr': 'AggregationCidr',
        'enabled': 'Enabled',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'vif_instance_id': 'VIFInstanceId'
    }

    def __init__(self, aggregation_cidr=None, enabled=None, page_number=None, page_size=None, vif_instance_id=None, _configuration=None):  # noqa: E501
        """ListRouteAggregationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_cidr = None
        self._enabled = None
        self._page_number = None
        self._page_size = None
        self._vif_instance_id = None
        self.discriminator = None

        if aggregation_cidr is not None:
            self.aggregation_cidr = aggregation_cidr
        if enabled is not None:
            self.enabled = enabled
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if vif_instance_id is not None:
            self.vif_instance_id = vif_instance_id

    @property
    def aggregation_cidr(self):
        """Gets the aggregation_cidr of this ListRouteAggregationRequest.  # noqa: E501


        :return: The aggregation_cidr of this ListRouteAggregationRequest.  # noqa: E501
        :rtype: str
        """
        return self._aggregation_cidr

    @aggregation_cidr.setter
    def aggregation_cidr(self, aggregation_cidr):
        """Sets the aggregation_cidr of this ListRouteAggregationRequest.


        :param aggregation_cidr: The aggregation_cidr of this ListRouteAggregationRequest.  # noqa: E501
        :type: str
        """

        self._aggregation_cidr = aggregation_cidr

    @property
    def enabled(self):
        """Gets the enabled of this ListRouteAggregationRequest.  # noqa: E501


        :return: The enabled of this ListRouteAggregationRequest.  # noqa: E501
        :rtype: str
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this ListRouteAggregationRequest.


        :param enabled: The enabled of this ListRouteAggregationRequest.  # noqa: E501
        :type: str
        """

        self._enabled = enabled

    @property
    def page_number(self):
        """Gets the page_number of this ListRouteAggregationRequest.  # noqa: E501


        :return: The page_number of this ListRouteAggregationRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRouteAggregationRequest.


        :param page_number: The page_number of this ListRouteAggregationRequest.  # noqa: E501
        :type: str
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRouteAggregationRequest.  # noqa: E501


        :return: The page_size of this ListRouteAggregationRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRouteAggregationRequest.


        :param page_size: The page_size of this ListRouteAggregationRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this ListRouteAggregationRequest.  # noqa: E501


        :return: The vif_instance_id of this ListRouteAggregationRequest.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this ListRouteAggregationRequest.


        :param vif_instance_id: The vif_instance_id of this ListRouteAggregationRequest.  # noqa: E501
        :type: str
        """

        self._vif_instance_id = vif_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRouteAggregationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRouteAggregationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRouteAggregationRequest):
            return True

        return self.to_dict() != other.to_dict()
