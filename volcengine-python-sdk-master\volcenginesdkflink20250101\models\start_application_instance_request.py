# coding: utf-8

"""
    flink20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartApplicationInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'app': 'AppForStartApplicationInstanceInput',
        'id': 'str',
        'restore_strategy': 'RestoreStrategyForStartApplicationInstanceInput'
    }

    attribute_map = {
        'app': 'App',
        'id': 'Id',
        'restore_strategy': 'RestoreStrategy'
    }

    def __init__(self, app=None, id=None, restore_strategy=None, _configuration=None):  # noqa: E501
        """StartApplicationInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._app = None
        self._id = None
        self._restore_strategy = None
        self.discriminator = None

        if app is not None:
            self.app = app
        if id is not None:
            self.id = id
        if restore_strategy is not None:
            self.restore_strategy = restore_strategy

    @property
    def app(self):
        """Gets the app of this StartApplicationInstanceRequest.  # noqa: E501


        :return: The app of this StartApplicationInstanceRequest.  # noqa: E501
        :rtype: AppForStartApplicationInstanceInput
        """
        return self._app

    @app.setter
    def app(self, app):
        """Sets the app of this StartApplicationInstanceRequest.


        :param app: The app of this StartApplicationInstanceRequest.  # noqa: E501
        :type: AppForStartApplicationInstanceInput
        """

        self._app = app

    @property
    def id(self):
        """Gets the id of this StartApplicationInstanceRequest.  # noqa: E501


        :return: The id of this StartApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this StartApplicationInstanceRequest.


        :param id: The id of this StartApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def restore_strategy(self):
        """Gets the restore_strategy of this StartApplicationInstanceRequest.  # noqa: E501


        :return: The restore_strategy of this StartApplicationInstanceRequest.  # noqa: E501
        :rtype: RestoreStrategyForStartApplicationInstanceInput
        """
        return self._restore_strategy

    @restore_strategy.setter
    def restore_strategy(self, restore_strategy):
        """Sets the restore_strategy of this StartApplicationInstanceRequest.


        :param restore_strategy: The restore_strategy of this StartApplicationInstanceRequest.  # noqa: E501
        :type: RestoreStrategyForStartApplicationInstanceInput
        """

        self._restore_strategy = restore_strategy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartApplicationInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartApplicationInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartApplicationInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
