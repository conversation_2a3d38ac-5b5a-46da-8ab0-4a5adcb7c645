# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDataProjectWithBindWidthAndFlowResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_down': 'list[DataDownForGetDataProjectWithBindWidthAndFlowOutput]',
        'data_up': 'list[DataUpForGetDataProjectWithBindWidthAndFlowOutput]',
        'total_down': 'float',
        'total_up': 'float'
    }

    attribute_map = {
        'data_down': 'DataDown',
        'data_up': 'DataUp',
        'total_down': 'TotalDown',
        'total_up': 'TotalUp'
    }

    def __init__(self, data_down=None, data_up=None, total_down=None, total_up=None, _configuration=None):  # noqa: E501
        """GetDataProjectWithBindWidthAndFlowResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_down = None
        self._data_up = None
        self._total_down = None
        self._total_up = None
        self.discriminator = None

        if data_down is not None:
            self.data_down = data_down
        if data_up is not None:
            self.data_up = data_up
        if total_down is not None:
            self.total_down = total_down
        if total_up is not None:
            self.total_up = total_up

    @property
    def data_down(self):
        """Gets the data_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501


        :return: The data_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :rtype: list[DataDownForGetDataProjectWithBindWidthAndFlowOutput]
        """
        return self._data_down

    @data_down.setter
    def data_down(self, data_down):
        """Sets the data_down of this GetDataProjectWithBindWidthAndFlowResponse.


        :param data_down: The data_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :type: list[DataDownForGetDataProjectWithBindWidthAndFlowOutput]
        """

        self._data_down = data_down

    @property
    def data_up(self):
        """Gets the data_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501


        :return: The data_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :rtype: list[DataUpForGetDataProjectWithBindWidthAndFlowOutput]
        """
        return self._data_up

    @data_up.setter
    def data_up(self, data_up):
        """Sets the data_up of this GetDataProjectWithBindWidthAndFlowResponse.


        :param data_up: The data_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :type: list[DataUpForGetDataProjectWithBindWidthAndFlowOutput]
        """

        self._data_up = data_up

    @property
    def total_down(self):
        """Gets the total_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501


        :return: The total_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :rtype: float
        """
        return self._total_down

    @total_down.setter
    def total_down(self, total_down):
        """Sets the total_down of this GetDataProjectWithBindWidthAndFlowResponse.


        :param total_down: The total_down of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :type: float
        """

        self._total_down = total_down

    @property
    def total_up(self):
        """Gets the total_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501


        :return: The total_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :rtype: float
        """
        return self._total_up

    @total_up.setter
    def total_up(self, total_up):
        """Sets the total_up of this GetDataProjectWithBindWidthAndFlowResponse.


        :param total_up: The total_up of this GetDataProjectWithBindWidthAndFlowResponse.  # noqa: E501
        :type: float
        """

        self._total_up = total_up

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDataProjectWithBindWidthAndFlowResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDataProjectWithBindWidthAndFlowResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDataProjectWithBindWidthAndFlowResponse):
            return True

        return self.to_dict() != other.to_dict()
