# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProxyDetailForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_proxy_status': 'str',
        'proxy_resource_info': 'ProxyResourceInfoForDescribeDBInstanceDetailOutput'
    }

    attribute_map = {
        'db_proxy_status': 'DBProxyStatus',
        'proxy_resource_info': 'ProxyResourceInfo'
    }

    def __init__(self, db_proxy_status=None, proxy_resource_info=None, _configuration=None):  # noqa: E501
        """ProxyDetailForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_proxy_status = None
        self._proxy_resource_info = None
        self.discriminator = None

        if db_proxy_status is not None:
            self.db_proxy_status = db_proxy_status
        if proxy_resource_info is not None:
            self.proxy_resource_info = proxy_resource_info

    @property
    def db_proxy_status(self):
        """Gets the db_proxy_status of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_proxy_status of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_proxy_status

    @db_proxy_status.setter
    def db_proxy_status(self, db_proxy_status):
        """Sets the db_proxy_status of this ProxyDetailForDescribeDBInstanceDetailOutput.


        :param db_proxy_status: The db_proxy_status of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_proxy_status = db_proxy_status

    @property
    def proxy_resource_info(self):
        """Gets the proxy_resource_info of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The proxy_resource_info of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: ProxyResourceInfoForDescribeDBInstanceDetailOutput
        """
        return self._proxy_resource_info

    @proxy_resource_info.setter
    def proxy_resource_info(self, proxy_resource_info):
        """Sets the proxy_resource_info of this ProxyDetailForDescribeDBInstanceDetailOutput.


        :param proxy_resource_info: The proxy_resource_info of this ProxyDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: ProxyResourceInfoForDescribeDBInstanceDetailOutput
        """

        self._proxy_resource_info = proxy_resource_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProxyDetailForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProxyDetailForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProxyDetailForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
