# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceSpecsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'arch_type': 'str',
        'instance_class': 'str'
    }

    attribute_map = {
        'arch_type': 'ArchType',
        'instance_class': 'InstanceClass'
    }

    def __init__(self, arch_type=None, instance_class=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceSpecsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._arch_type = None
        self._instance_class = None
        self.discriminator = None

        if arch_type is not None:
            self.arch_type = arch_type
        if instance_class is not None:
            self.instance_class = instance_class

    @property
    def arch_type(self):
        """Gets the arch_type of this DescribeDBInstanceSpecsRequest.  # noqa: E501


        :return: The arch_type of this DescribeDBInstanceSpecsRequest.  # noqa: E501
        :rtype: str
        """
        return self._arch_type

    @arch_type.setter
    def arch_type(self, arch_type):
        """Sets the arch_type of this DescribeDBInstanceSpecsRequest.


        :param arch_type: The arch_type of this DescribeDBInstanceSpecsRequest.  # noqa: E501
        :type: str
        """

        self._arch_type = arch_type

    @property
    def instance_class(self):
        """Gets the instance_class of this DescribeDBInstanceSpecsRequest.  # noqa: E501


        :return: The instance_class of this DescribeDBInstanceSpecsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_class

    @instance_class.setter
    def instance_class(self, instance_class):
        """Sets the instance_class of this DescribeDBInstanceSpecsRequest.


        :param instance_class: The instance_class of this DescribeDBInstanceSpecsRequest.  # noqa: E501
        :type: str
        """

        self._instance_class = instance_class

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceSpecsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceSpecsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceSpecsRequest):
            return True

        return self.to_dict() != other.to_dict()
