# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IncreaseDBInstanceNodeNumberRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apply_immediately': 'bool',
        'backup_point_name': 'str',
        'client_token': 'str',
        'configure_new_nodes': 'list[ConfigureNewNodeForIncreaseDBInstanceNodeNumberInput]',
        'create_backup': 'bool',
        'instance_id': 'str',
        'nodes_number_to_increase': 'int'
    }

    attribute_map = {
        'apply_immediately': 'ApplyImmediately',
        'backup_point_name': 'BackupPointName',
        'client_token': 'ClientToken',
        'configure_new_nodes': 'ConfigureNewNodes',
        'create_backup': 'CreateBackup',
        'instance_id': 'InstanceId',
        'nodes_number_to_increase': 'NodesNumberToIncrease'
    }

    def __init__(self, apply_immediately=None, backup_point_name=None, client_token=None, configure_new_nodes=None, create_backup=None, instance_id=None, nodes_number_to_increase=None, _configuration=None):  # noqa: E501
        """IncreaseDBInstanceNodeNumberRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apply_immediately = None
        self._backup_point_name = None
        self._client_token = None
        self._configure_new_nodes = None
        self._create_backup = None
        self._instance_id = None
        self._nodes_number_to_increase = None
        self.discriminator = None

        self.apply_immediately = apply_immediately
        if backup_point_name is not None:
            self.backup_point_name = backup_point_name
        if client_token is not None:
            self.client_token = client_token
        if configure_new_nodes is not None:
            self.configure_new_nodes = configure_new_nodes
        if create_backup is not None:
            self.create_backup = create_backup
        self.instance_id = instance_id
        self.nodes_number_to_increase = nodes_number_to_increase

    @property
    def apply_immediately(self):
        """Gets the apply_immediately of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The apply_immediately of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: bool
        """
        return self._apply_immediately

    @apply_immediately.setter
    def apply_immediately(self, apply_immediately):
        """Sets the apply_immediately of this IncreaseDBInstanceNodeNumberRequest.


        :param apply_immediately: The apply_immediately of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and apply_immediately is None:
            raise ValueError("Invalid value for `apply_immediately`, must not be `None`")  # noqa: E501

        self._apply_immediately = apply_immediately

    @property
    def backup_point_name(self):
        """Gets the backup_point_name of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The backup_point_name of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_name

    @backup_point_name.setter
    def backup_point_name(self, backup_point_name):
        """Sets the backup_point_name of this IncreaseDBInstanceNodeNumberRequest.


        :param backup_point_name: The backup_point_name of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_name = backup_point_name

    @property
    def client_token(self):
        """Gets the client_token of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The client_token of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this IncreaseDBInstanceNodeNumberRequest.


        :param client_token: The client_token of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def configure_new_nodes(self):
        """Gets the configure_new_nodes of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The configure_new_nodes of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: list[ConfigureNewNodeForIncreaseDBInstanceNodeNumberInput]
        """
        return self._configure_new_nodes

    @configure_new_nodes.setter
    def configure_new_nodes(self, configure_new_nodes):
        """Sets the configure_new_nodes of this IncreaseDBInstanceNodeNumberRequest.


        :param configure_new_nodes: The configure_new_nodes of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: list[ConfigureNewNodeForIncreaseDBInstanceNodeNumberInput]
        """

        self._configure_new_nodes = configure_new_nodes

    @property
    def create_backup(self):
        """Gets the create_backup of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The create_backup of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: bool
        """
        return self._create_backup

    @create_backup.setter
    def create_backup(self, create_backup):
        """Sets the create_backup of this IncreaseDBInstanceNodeNumberRequest.


        :param create_backup: The create_backup of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: bool
        """

        self._create_backup = create_backup

    @property
    def instance_id(self):
        """Gets the instance_id of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The instance_id of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this IncreaseDBInstanceNodeNumberRequest.


        :param instance_id: The instance_id of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def nodes_number_to_increase(self):
        """Gets the nodes_number_to_increase of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501


        :return: The nodes_number_to_increase of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :rtype: int
        """
        return self._nodes_number_to_increase

    @nodes_number_to_increase.setter
    def nodes_number_to_increase(self, nodes_number_to_increase):
        """Sets the nodes_number_to_increase of this IncreaseDBInstanceNodeNumberRequest.


        :param nodes_number_to_increase: The nodes_number_to_increase of this IncreaseDBInstanceNodeNumberRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and nodes_number_to_increase is None:
            raise ValueError("Invalid value for `nodes_number_to_increase`, must not be `None`")  # noqa: E501

        self._nodes_number_to_increase = nodes_number_to_increase

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IncreaseDBInstanceNodeNumberRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IncreaseDBInstanceNodeNumberRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IncreaseDBInstanceNodeNumberRequest):
            return True

        return self.to_dict() != other.to_dict()
