# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attention_detection_type': 'int',
        'detection_rule': 'DetectionRuleForUpdateAttentionDetectionConfigOutput',
        'is_open': 'int',
        'reminder_copy': 'str'
    }

    attribute_map = {
        'attention_detection_type': 'AttentionDetectionType',
        'detection_rule': 'DetectionRule',
        'is_open': 'IsOpen',
        'reminder_copy': 'ReminderCopy'
    }

    def __init__(self, attention_detection_type=None, detection_rule=None, is_open=None, reminder_copy=None, _configuration=None):  # noqa: E501
        """AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attention_detection_type = None
        self._detection_rule = None
        self._is_open = None
        self._reminder_copy = None
        self.discriminator = None

        if attention_detection_type is not None:
            self.attention_detection_type = attention_detection_type
        if detection_rule is not None:
            self.detection_rule = detection_rule
        if is_open is not None:
            self.is_open = is_open
        if reminder_copy is not None:
            self.reminder_copy = reminder_copy

    @property
    def attention_detection_type(self):
        """Gets the attention_detection_type of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501


        :return: The attention_detection_type of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._attention_detection_type

    @attention_detection_type.setter
    def attention_detection_type(self, attention_detection_type):
        """Sets the attention_detection_type of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.


        :param attention_detection_type: The attention_detection_type of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :type: int
        """

        self._attention_detection_type = attention_detection_type

    @property
    def detection_rule(self):
        """Gets the detection_rule of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501


        :return: The detection_rule of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :rtype: DetectionRuleForUpdateAttentionDetectionConfigOutput
        """
        return self._detection_rule

    @detection_rule.setter
    def detection_rule(self, detection_rule):
        """Sets the detection_rule of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.


        :param detection_rule: The detection_rule of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :type: DetectionRuleForUpdateAttentionDetectionConfigOutput
        """

        self._detection_rule = detection_rule

    @property
    def is_open(self):
        """Gets the is_open of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501


        :return: The is_open of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_open

    @is_open.setter
    def is_open(self, is_open):
        """Sets the is_open of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.


        :param is_open: The is_open of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :type: int
        """

        self._is_open = is_open

    @property
    def reminder_copy(self):
        """Gets the reminder_copy of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501


        :return: The reminder_copy of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._reminder_copy

    @reminder_copy.setter
    def reminder_copy(self, reminder_copy):
        """Sets the reminder_copy of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.


        :param reminder_copy: The reminder_copy of this AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput.  # noqa: E501
        :type: str
        """

        self._reminder_copy = reminder_copy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AttentionDetectionConfigArrayForUpdateAttentionDetectionConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
