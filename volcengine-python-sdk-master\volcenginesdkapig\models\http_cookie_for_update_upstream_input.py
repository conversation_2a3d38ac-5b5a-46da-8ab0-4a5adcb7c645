# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HTTPCookieForUpdateUpstreamInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'path': 'str',
        'ttl': 'int'
    }

    attribute_map = {
        'name': 'Name',
        'path': 'Path',
        'ttl': 'Ttl'
    }

    def __init__(self, name=None, path=None, ttl=None, _configuration=None):  # noqa: E501
        """HTTPCookieForUpdateUpstreamInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._path = None
        self._ttl = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if path is not None:
            self.path = path
        if ttl is not None:
            self.ttl = ttl

    @property
    def name(self):
        """Gets the name of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501


        :return: The name of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this HTTPCookieForUpdateUpstreamInput.


        :param name: The name of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def path(self):
        """Gets the path of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501


        :return: The path of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this HTTPCookieForUpdateUpstreamInput.


        :param path: The path of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def ttl(self):
        """Gets the ttl of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501


        :return: The ttl of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :rtype: int
        """
        return self._ttl

    @ttl.setter
    def ttl(self, ttl):
        """Sets the ttl of this HTTPCookieForUpdateUpstreamInput.


        :param ttl: The ttl of this HTTPCookieForUpdateUpstreamInput.  # noqa: E501
        :type: int
        """

        self._ttl = ttl

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HTTPCookieForUpdateUpstreamInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HTTPCookieForUpdateUpstreamInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HTTPCookieForUpdateUpstreamInput):
            return True

        return self.to_dict() != other.to_dict()
