# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SpeedLimitTimeForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'begin_time': 'str',
        'day_week': 'str',
        'end_time': 'str'
    }

    attribute_map = {
        'begin_time': 'BeginTime',
        'day_week': 'DayWeek',
        'end_time': 'EndTime'
    }

    def __init__(self, begin_time=None, day_week=None, end_time=None, _configuration=None):  # noqa: E501
        """SpeedLimitTimeForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._begin_time = None
        self._day_week = None
        self._end_time = None
        self.discriminator = None

        if begin_time is not None:
            self.begin_time = begin_time
        if day_week is not None:
            self.day_week = day_week
        if end_time is not None:
            self.end_time = end_time

    @property
    def begin_time(self):
        """Gets the begin_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501


        :return: The begin_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this SpeedLimitTimeForAddCdnDomainInput.


        :param begin_time: The begin_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._begin_time = begin_time

    @property
    def day_week(self):
        """Gets the day_week of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501


        :return: The day_week of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._day_week

    @day_week.setter
    def day_week(self, day_week):
        """Sets the day_week of this SpeedLimitTimeForAddCdnDomainInput.


        :param day_week: The day_week of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._day_week = day_week

    @property
    def end_time(self):
        """Gets the end_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501


        :return: The end_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this SpeedLimitTimeForAddCdnDomainInput.


        :param end_time: The end_time of this SpeedLimitTimeForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SpeedLimitTimeForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SpeedLimitTimeForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SpeedLimitTimeForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
