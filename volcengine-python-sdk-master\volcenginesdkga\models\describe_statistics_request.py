# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'str',
        'group_by_accelerator': 'bool',
        'group_by_listener': 'bool',
        'group_by_region': 'bool',
        'input_id': 'list[str]',
        'input_id_type': 'str',
        'interval': 'int',
        'metrics': 'list[str]',
        'region': 'list[str]',
        'region_type': 'str',
        'start_time': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'group_by_accelerator': 'GroupByAccelerator',
        'group_by_listener': 'GroupByListener',
        'group_by_region': 'GroupByRegion',
        'input_id': 'InputId',
        'input_id_type': 'InputIdType',
        'interval': 'Interval',
        'metrics': 'Metrics',
        'region': 'Region',
        'region_type': 'RegionType',
        'start_time': 'StartTime'
    }

    def __init__(self, end_time=None, group_by_accelerator=None, group_by_listener=None, group_by_region=None, input_id=None, input_id_type=None, interval=None, metrics=None, region=None, region_type=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._group_by_accelerator = None
        self._group_by_listener = None
        self._group_by_region = None
        self._input_id = None
        self._input_id_type = None
        self._interval = None
        self._metrics = None
        self._region = None
        self._region_type = None
        self._start_time = None
        self.discriminator = None

        self.end_time = end_time
        if group_by_accelerator is not None:
            self.group_by_accelerator = group_by_accelerator
        if group_by_listener is not None:
            self.group_by_listener = group_by_listener
        if group_by_region is not None:
            self.group_by_region = group_by_region
        if input_id is not None:
            self.input_id = input_id
        self.input_id_type = input_id_type
        self.interval = interval
        if metrics is not None:
            self.metrics = metrics
        if region is not None:
            self.region = region
        if region_type is not None:
            self.region_type = region_type
        self.start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DescribeStatisticsRequest.  # noqa: E501


        :return: The end_time of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeStatisticsRequest.


        :param end_time: The end_time of this DescribeStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def group_by_accelerator(self):
        """Gets the group_by_accelerator of this DescribeStatisticsRequest.  # noqa: E501


        :return: The group_by_accelerator of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._group_by_accelerator

    @group_by_accelerator.setter
    def group_by_accelerator(self, group_by_accelerator):
        """Sets the group_by_accelerator of this DescribeStatisticsRequest.


        :param group_by_accelerator: The group_by_accelerator of this DescribeStatisticsRequest.  # noqa: E501
        :type: bool
        """

        self._group_by_accelerator = group_by_accelerator

    @property
    def group_by_listener(self):
        """Gets the group_by_listener of this DescribeStatisticsRequest.  # noqa: E501


        :return: The group_by_listener of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._group_by_listener

    @group_by_listener.setter
    def group_by_listener(self, group_by_listener):
        """Sets the group_by_listener of this DescribeStatisticsRequest.


        :param group_by_listener: The group_by_listener of this DescribeStatisticsRequest.  # noqa: E501
        :type: bool
        """

        self._group_by_listener = group_by_listener

    @property
    def group_by_region(self):
        """Gets the group_by_region of this DescribeStatisticsRequest.  # noqa: E501


        :return: The group_by_region of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._group_by_region

    @group_by_region.setter
    def group_by_region(self, group_by_region):
        """Sets the group_by_region of this DescribeStatisticsRequest.


        :param group_by_region: The group_by_region of this DescribeStatisticsRequest.  # noqa: E501
        :type: bool
        """

        self._group_by_region = group_by_region

    @property
    def input_id(self):
        """Gets the input_id of this DescribeStatisticsRequest.  # noqa: E501


        :return: The input_id of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._input_id

    @input_id.setter
    def input_id(self, input_id):
        """Sets the input_id of this DescribeStatisticsRequest.


        :param input_id: The input_id of this DescribeStatisticsRequest.  # noqa: E501
        :type: list[str]
        """

        self._input_id = input_id

    @property
    def input_id_type(self):
        """Gets the input_id_type of this DescribeStatisticsRequest.  # noqa: E501


        :return: The input_id_type of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._input_id_type

    @input_id_type.setter
    def input_id_type(self, input_id_type):
        """Sets the input_id_type of this DescribeStatisticsRequest.


        :param input_id_type: The input_id_type of this DescribeStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and input_id_type is None:
            raise ValueError("Invalid value for `input_id_type`, must not be `None`")  # noqa: E501

        self._input_id_type = input_id_type

    @property
    def interval(self):
        """Gets the interval of this DescribeStatisticsRequest.  # noqa: E501


        :return: The interval of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this DescribeStatisticsRequest.


        :param interval: The interval of this DescribeStatisticsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and interval is None:
            raise ValueError("Invalid value for `interval`, must not be `None`")  # noqa: E501

        self._interval = interval

    @property
    def metrics(self):
        """Gets the metrics of this DescribeStatisticsRequest.  # noqa: E501


        :return: The metrics of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._metrics

    @metrics.setter
    def metrics(self, metrics):
        """Sets the metrics of this DescribeStatisticsRequest.


        :param metrics: The metrics of this DescribeStatisticsRequest.  # noqa: E501
        :type: list[str]
        """

        self._metrics = metrics

    @property
    def region(self):
        """Gets the region of this DescribeStatisticsRequest.  # noqa: E501


        :return: The region of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DescribeStatisticsRequest.


        :param region: The region of this DescribeStatisticsRequest.  # noqa: E501
        :type: list[str]
        """

        self._region = region

    @property
    def region_type(self):
        """Gets the region_type of this DescribeStatisticsRequest.  # noqa: E501


        :return: The region_type of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._region_type

    @region_type.setter
    def region_type(self, region_type):
        """Sets the region_type of this DescribeStatisticsRequest.


        :param region_type: The region_type of this DescribeStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._region_type = region_type

    @property
    def start_time(self):
        """Gets the start_time of this DescribeStatisticsRequest.  # noqa: E501


        :return: The start_time of this DescribeStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeStatisticsRequest.


        :param start_time: The start_time of this DescribeStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
