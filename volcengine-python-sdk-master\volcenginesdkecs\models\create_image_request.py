# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateImageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_whole_image': 'bool',
        'description': 'str',
        'image_name': 'str',
        'instance_id': 'str',
        'need_detection': 'bool',
        'project_name': 'str',
        'snapshot_group_id': 'str',
        'snapshot_id': 'str',
        'tags': 'list[TagForCreateImageInput]'
    }

    attribute_map = {
        'create_whole_image': 'CreateWholeImage',
        'description': 'Description',
        'image_name': 'ImageName',
        'instance_id': 'InstanceId',
        'need_detection': 'NeedDetection',
        'project_name': 'ProjectName',
        'snapshot_group_id': 'SnapshotGroupId',
        'snapshot_id': 'SnapshotId',
        'tags': 'Tags'
    }

    def __init__(self, create_whole_image=None, description=None, image_name=None, instance_id=None, need_detection=None, project_name=None, snapshot_group_id=None, snapshot_id=None, tags=None, _configuration=None):  # noqa: E501
        """CreateImageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_whole_image = None
        self._description = None
        self._image_name = None
        self._instance_id = None
        self._need_detection = None
        self._project_name = None
        self._snapshot_group_id = None
        self._snapshot_id = None
        self._tags = None
        self.discriminator = None

        if create_whole_image is not None:
            self.create_whole_image = create_whole_image
        if description is not None:
            self.description = description
        self.image_name = image_name
        if instance_id is not None:
            self.instance_id = instance_id
        if need_detection is not None:
            self.need_detection = need_detection
        if project_name is not None:
            self.project_name = project_name
        if snapshot_group_id is not None:
            self.snapshot_group_id = snapshot_group_id
        if snapshot_id is not None:
            self.snapshot_id = snapshot_id
        if tags is not None:
            self.tags = tags

    @property
    def create_whole_image(self):
        """Gets the create_whole_image of this CreateImageRequest.  # noqa: E501


        :return: The create_whole_image of this CreateImageRequest.  # noqa: E501
        :rtype: bool
        """
        return self._create_whole_image

    @create_whole_image.setter
    def create_whole_image(self, create_whole_image):
        """Sets the create_whole_image of this CreateImageRequest.


        :param create_whole_image: The create_whole_image of this CreateImageRequest.  # noqa: E501
        :type: bool
        """

        self._create_whole_image = create_whole_image

    @property
    def description(self):
        """Gets the description of this CreateImageRequest.  # noqa: E501


        :return: The description of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateImageRequest.


        :param description: The description of this CreateImageRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def image_name(self):
        """Gets the image_name of this CreateImageRequest.  # noqa: E501


        :return: The image_name of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this CreateImageRequest.


        :param image_name: The image_name of this CreateImageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_name is None:
            raise ValueError("Invalid value for `image_name`, must not be `None`")  # noqa: E501

        self._image_name = image_name

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateImageRequest.  # noqa: E501


        :return: The instance_id of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateImageRequest.


        :param instance_id: The instance_id of this CreateImageRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def need_detection(self):
        """Gets the need_detection of this CreateImageRequest.  # noqa: E501


        :return: The need_detection of this CreateImageRequest.  # noqa: E501
        :rtype: bool
        """
        return self._need_detection

    @need_detection.setter
    def need_detection(self, need_detection):
        """Sets the need_detection of this CreateImageRequest.


        :param need_detection: The need_detection of this CreateImageRequest.  # noqa: E501
        :type: bool
        """

        self._need_detection = need_detection

    @property
    def project_name(self):
        """Gets the project_name of this CreateImageRequest.  # noqa: E501


        :return: The project_name of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateImageRequest.


        :param project_name: The project_name of this CreateImageRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def snapshot_group_id(self):
        """Gets the snapshot_group_id of this CreateImageRequest.  # noqa: E501


        :return: The snapshot_group_id of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_group_id

    @snapshot_group_id.setter
    def snapshot_group_id(self, snapshot_group_id):
        """Sets the snapshot_group_id of this CreateImageRequest.


        :param snapshot_group_id: The snapshot_group_id of this CreateImageRequest.  # noqa: E501
        :type: str
        """

        self._snapshot_group_id = snapshot_group_id

    @property
    def snapshot_id(self):
        """Gets the snapshot_id of this CreateImageRequest.  # noqa: E501


        :return: The snapshot_id of this CreateImageRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_id

    @snapshot_id.setter
    def snapshot_id(self, snapshot_id):
        """Sets the snapshot_id of this CreateImageRequest.


        :param snapshot_id: The snapshot_id of this CreateImageRequest.  # noqa: E501
        :type: str
        """

        self._snapshot_id = snapshot_id

    @property
    def tags(self):
        """Gets the tags of this CreateImageRequest.  # noqa: E501


        :return: The tags of this CreateImageRequest.  # noqa: E501
        :rtype: list[TagForCreateImageInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateImageRequest.


        :param tags: The tags of this CreateImageRequest.  # noqa: E501
        :type: list[TagForCreateImageInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateImageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateImageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateImageRequest):
            return True

        return self.to_dict() != other.to_dict()
