# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAccountAggregatedStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'live_count': 'int',
        'watch_count_per_people': 'float',
        'watch_live_per_people': 'float',
        'watch_people': 'int',
        'watch_time': 'int',
        'watch_time_per_people': 'float'
    }

    attribute_map = {
        'live_count': 'LiveCount',
        'watch_count_per_people': 'WatchCountPerPeople',
        'watch_live_per_people': 'WatchLivePerPeople',
        'watch_people': 'WatchPeople',
        'watch_time': 'WatchTime',
        'watch_time_per_people': 'WatchTimePerPeople'
    }

    def __init__(self, live_count=None, watch_count_per_people=None, watch_live_per_people=None, watch_people=None, watch_time=None, watch_time_per_people=None, _configuration=None):  # noqa: E501
        """GetAccountAggregatedStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._live_count = None
        self._watch_count_per_people = None
        self._watch_live_per_people = None
        self._watch_people = None
        self._watch_time = None
        self._watch_time_per_people = None
        self.discriminator = None

        if live_count is not None:
            self.live_count = live_count
        if watch_count_per_people is not None:
            self.watch_count_per_people = watch_count_per_people
        if watch_live_per_people is not None:
            self.watch_live_per_people = watch_live_per_people
        if watch_people is not None:
            self.watch_people = watch_people
        if watch_time is not None:
            self.watch_time = watch_time
        if watch_time_per_people is not None:
            self.watch_time_per_people = watch_time_per_people

    @property
    def live_count(self):
        """Gets the live_count of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The live_count of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_count

    @live_count.setter
    def live_count(self, live_count):
        """Sets the live_count of this GetAccountAggregatedStatisticsResponse.


        :param live_count: The live_count of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._live_count = live_count

    @property
    def watch_count_per_people(self):
        """Gets the watch_count_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The watch_count_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: float
        """
        return self._watch_count_per_people

    @watch_count_per_people.setter
    def watch_count_per_people(self, watch_count_per_people):
        """Sets the watch_count_per_people of this GetAccountAggregatedStatisticsResponse.


        :param watch_count_per_people: The watch_count_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: float
        """

        self._watch_count_per_people = watch_count_per_people

    @property
    def watch_live_per_people(self):
        """Gets the watch_live_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The watch_live_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: float
        """
        return self._watch_live_per_people

    @watch_live_per_people.setter
    def watch_live_per_people(self, watch_live_per_people):
        """Sets the watch_live_per_people of this GetAccountAggregatedStatisticsResponse.


        :param watch_live_per_people: The watch_live_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: float
        """

        self._watch_live_per_people = watch_live_per_people

    @property
    def watch_people(self):
        """Gets the watch_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The watch_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._watch_people

    @watch_people.setter
    def watch_people(self, watch_people):
        """Sets the watch_people of this GetAccountAggregatedStatisticsResponse.


        :param watch_people: The watch_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._watch_people = watch_people

    @property
    def watch_time(self):
        """Gets the watch_time of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The watch_time of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._watch_time

    @watch_time.setter
    def watch_time(self, watch_time):
        """Sets the watch_time of this GetAccountAggregatedStatisticsResponse.


        :param watch_time: The watch_time of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._watch_time = watch_time

    @property
    def watch_time_per_people(self):
        """Gets the watch_time_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501


        :return: The watch_time_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :rtype: float
        """
        return self._watch_time_per_people

    @watch_time_per_people.setter
    def watch_time_per_people(self, watch_time_per_people):
        """Sets the watch_time_per_people of this GetAccountAggregatedStatisticsResponse.


        :param watch_time_per_people: The watch_time_per_people of this GetAccountAggregatedStatisticsResponse.  # noqa: E501
        :type: float
        """

        self._watch_time_per_people = watch_time_per_people

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAccountAggregatedStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAccountAggregatedStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAccountAggregatedStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
