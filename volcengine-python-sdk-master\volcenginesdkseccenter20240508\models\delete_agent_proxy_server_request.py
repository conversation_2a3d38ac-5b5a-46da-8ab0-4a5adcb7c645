# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteAgentProxyServerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'proxy_name': 'str',
        'proxy_server_name': 'str'
    }

    attribute_map = {
        'proxy_name': 'ProxyName',
        'proxy_server_name': 'ProxyServerName'
    }

    def __init__(self, proxy_name=None, proxy_server_name=None, _configuration=None):  # noqa: E501
        """DeleteAgentProxyServerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._proxy_name = None
        self._proxy_server_name = None
        self.discriminator = None

        if proxy_name is not None:
            self.proxy_name = proxy_name
        if proxy_server_name is not None:
            self.proxy_server_name = proxy_server_name

    @property
    def proxy_name(self):
        """Gets the proxy_name of this DeleteAgentProxyServerRequest.  # noqa: E501


        :return: The proxy_name of this DeleteAgentProxyServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_name

    @proxy_name.setter
    def proxy_name(self, proxy_name):
        """Sets the proxy_name of this DeleteAgentProxyServerRequest.


        :param proxy_name: The proxy_name of this DeleteAgentProxyServerRequest.  # noqa: E501
        :type: str
        """

        self._proxy_name = proxy_name

    @property
    def proxy_server_name(self):
        """Gets the proxy_server_name of this DeleteAgentProxyServerRequest.  # noqa: E501


        :return: The proxy_server_name of this DeleteAgentProxyServerRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_server_name

    @proxy_server_name.setter
    def proxy_server_name(self, proxy_server_name):
        """Sets the proxy_server_name of this DeleteAgentProxyServerRequest.


        :param proxy_server_name: The proxy_server_name of this DeleteAgentProxyServerRequest.  # noqa: E501
        :type: str
        """

        self._proxy_server_name = proxy_server_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteAgentProxyServerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteAgentProxyServerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteAgentProxyServerRequest):
            return True

        return self.to_dict() != other.to_dict()
