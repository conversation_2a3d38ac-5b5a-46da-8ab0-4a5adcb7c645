# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeListenerResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'account_id': 'str',
        'backup_endpoint_group_ids': 'list[str]',
        'create_time': 'int',
        'disable_isolate_tcp_null_conn': 'bool',
        'disable_pre_connect': 'bool',
        'enable_affinity': 'bool',
        'endpoint_group_ids': 'list[str]',
        'fixed_source_return': 'FixedSourceReturnForDescribeListenerOutput',
        'ip_access': 'IPAccessForDescribeListenerOutput',
        'listener_id': 'str',
        'name': 'str',
        'port_ranges': 'list[PortRangeForDescribeListenerOutput]',
        'protocol': 'str',
        'state': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'account_id': 'AccountID',
        'backup_endpoint_group_ids': 'BackupEndpointGroupIds',
        'create_time': 'CreateTime',
        'disable_isolate_tcp_null_conn': 'DisableIsolateTCPNullConn',
        'disable_pre_connect': 'DisablePreConnect',
        'enable_affinity': 'EnableAffinity',
        'endpoint_group_ids': 'EndpointGroupIds',
        'fixed_source_return': 'FixedSourceReturn',
        'ip_access': 'IPAccess',
        'listener_id': 'ListenerId',
        'name': 'Name',
        'port_ranges': 'PortRanges',
        'protocol': 'Protocol',
        'state': 'State'
    }

    def __init__(self, accelerator_id=None, account_id=None, backup_endpoint_group_ids=None, create_time=None, disable_isolate_tcp_null_conn=None, disable_pre_connect=None, enable_affinity=None, endpoint_group_ids=None, fixed_source_return=None, ip_access=None, listener_id=None, name=None, port_ranges=None, protocol=None, state=None, _configuration=None):  # noqa: E501
        """DescribeListenerResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._account_id = None
        self._backup_endpoint_group_ids = None
        self._create_time = None
        self._disable_isolate_tcp_null_conn = None
        self._disable_pre_connect = None
        self._enable_affinity = None
        self._endpoint_group_ids = None
        self._fixed_source_return = None
        self._ip_access = None
        self._listener_id = None
        self._name = None
        self._port_ranges = None
        self._protocol = None
        self._state = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if account_id is not None:
            self.account_id = account_id
        if backup_endpoint_group_ids is not None:
            self.backup_endpoint_group_ids = backup_endpoint_group_ids
        if create_time is not None:
            self.create_time = create_time
        if disable_isolate_tcp_null_conn is not None:
            self.disable_isolate_tcp_null_conn = disable_isolate_tcp_null_conn
        if disable_pre_connect is not None:
            self.disable_pre_connect = disable_pre_connect
        if enable_affinity is not None:
            self.enable_affinity = enable_affinity
        if endpoint_group_ids is not None:
            self.endpoint_group_ids = endpoint_group_ids
        if fixed_source_return is not None:
            self.fixed_source_return = fixed_source_return
        if ip_access is not None:
            self.ip_access = ip_access
        if listener_id is not None:
            self.listener_id = listener_id
        if name is not None:
            self.name = name
        if port_ranges is not None:
            self.port_ranges = port_ranges
        if protocol is not None:
            self.protocol = protocol
        if state is not None:
            self.state = state

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this DescribeListenerResponse.  # noqa: E501


        :return: The accelerator_id of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this DescribeListenerResponse.


        :param accelerator_id: The accelerator_id of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def account_id(self):
        """Gets the account_id of this DescribeListenerResponse.  # noqa: E501


        :return: The account_id of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeListenerResponse.


        :param account_id: The account_id of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def backup_endpoint_group_ids(self):
        """Gets the backup_endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501


        :return: The backup_endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._backup_endpoint_group_ids

    @backup_endpoint_group_ids.setter
    def backup_endpoint_group_ids(self, backup_endpoint_group_ids):
        """Sets the backup_endpoint_group_ids of this DescribeListenerResponse.


        :param backup_endpoint_group_ids: The backup_endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501
        :type: list[str]
        """

        self._backup_endpoint_group_ids = backup_endpoint_group_ids

    @property
    def create_time(self):
        """Gets the create_time of this DescribeListenerResponse.  # noqa: E501


        :return: The create_time of this DescribeListenerResponse.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeListenerResponse.


        :param create_time: The create_time of this DescribeListenerResponse.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def disable_isolate_tcp_null_conn(self):
        """Gets the disable_isolate_tcp_null_conn of this DescribeListenerResponse.  # noqa: E501


        :return: The disable_isolate_tcp_null_conn of this DescribeListenerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._disable_isolate_tcp_null_conn

    @disable_isolate_tcp_null_conn.setter
    def disable_isolate_tcp_null_conn(self, disable_isolate_tcp_null_conn):
        """Sets the disable_isolate_tcp_null_conn of this DescribeListenerResponse.


        :param disable_isolate_tcp_null_conn: The disable_isolate_tcp_null_conn of this DescribeListenerResponse.  # noqa: E501
        :type: bool
        """

        self._disable_isolate_tcp_null_conn = disable_isolate_tcp_null_conn

    @property
    def disable_pre_connect(self):
        """Gets the disable_pre_connect of this DescribeListenerResponse.  # noqa: E501


        :return: The disable_pre_connect of this DescribeListenerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._disable_pre_connect

    @disable_pre_connect.setter
    def disable_pre_connect(self, disable_pre_connect):
        """Sets the disable_pre_connect of this DescribeListenerResponse.


        :param disable_pre_connect: The disable_pre_connect of this DescribeListenerResponse.  # noqa: E501
        :type: bool
        """

        self._disable_pre_connect = disable_pre_connect

    @property
    def enable_affinity(self):
        """Gets the enable_affinity of this DescribeListenerResponse.  # noqa: E501


        :return: The enable_affinity of this DescribeListenerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enable_affinity

    @enable_affinity.setter
    def enable_affinity(self, enable_affinity):
        """Sets the enable_affinity of this DescribeListenerResponse.


        :param enable_affinity: The enable_affinity of this DescribeListenerResponse.  # noqa: E501
        :type: bool
        """

        self._enable_affinity = enable_affinity

    @property
    def endpoint_group_ids(self):
        """Gets the endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501


        :return: The endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_group_ids

    @endpoint_group_ids.setter
    def endpoint_group_ids(self, endpoint_group_ids):
        """Sets the endpoint_group_ids of this DescribeListenerResponse.


        :param endpoint_group_ids: The endpoint_group_ids of this DescribeListenerResponse.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_group_ids = endpoint_group_ids

    @property
    def fixed_source_return(self):
        """Gets the fixed_source_return of this DescribeListenerResponse.  # noqa: E501


        :return: The fixed_source_return of this DescribeListenerResponse.  # noqa: E501
        :rtype: FixedSourceReturnForDescribeListenerOutput
        """
        return self._fixed_source_return

    @fixed_source_return.setter
    def fixed_source_return(self, fixed_source_return):
        """Sets the fixed_source_return of this DescribeListenerResponse.


        :param fixed_source_return: The fixed_source_return of this DescribeListenerResponse.  # noqa: E501
        :type: FixedSourceReturnForDescribeListenerOutput
        """

        self._fixed_source_return = fixed_source_return

    @property
    def ip_access(self):
        """Gets the ip_access of this DescribeListenerResponse.  # noqa: E501


        :return: The ip_access of this DescribeListenerResponse.  # noqa: E501
        :rtype: IPAccessForDescribeListenerOutput
        """
        return self._ip_access

    @ip_access.setter
    def ip_access(self, ip_access):
        """Sets the ip_access of this DescribeListenerResponse.


        :param ip_access: The ip_access of this DescribeListenerResponse.  # noqa: E501
        :type: IPAccessForDescribeListenerOutput
        """

        self._ip_access = ip_access

    @property
    def listener_id(self):
        """Gets the listener_id of this DescribeListenerResponse.  # noqa: E501


        :return: The listener_id of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this DescribeListenerResponse.


        :param listener_id: The listener_id of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def name(self):
        """Gets the name of this DescribeListenerResponse.  # noqa: E501


        :return: The name of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeListenerResponse.


        :param name: The name of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def port_ranges(self):
        """Gets the port_ranges of this DescribeListenerResponse.  # noqa: E501


        :return: The port_ranges of this DescribeListenerResponse.  # noqa: E501
        :rtype: list[PortRangeForDescribeListenerOutput]
        """
        return self._port_ranges

    @port_ranges.setter
    def port_ranges(self, port_ranges):
        """Sets the port_ranges of this DescribeListenerResponse.


        :param port_ranges: The port_ranges of this DescribeListenerResponse.  # noqa: E501
        :type: list[PortRangeForDescribeListenerOutput]
        """

        self._port_ranges = port_ranges

    @property
    def protocol(self):
        """Gets the protocol of this DescribeListenerResponse.  # noqa: E501


        :return: The protocol of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DescribeListenerResponse.


        :param protocol: The protocol of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def state(self):
        """Gets the state of this DescribeListenerResponse.  # noqa: E501


        :return: The state of this DescribeListenerResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DescribeListenerResponse.


        :param state: The state of this DescribeListenerResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeListenerResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeListenerResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeListenerResponse):
            return True

        return self.to_dict() != other.to_dict()
