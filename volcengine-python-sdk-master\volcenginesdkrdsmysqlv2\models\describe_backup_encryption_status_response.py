# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBackupEncryptionStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_encryption_status': 'str',
        'log_encryption_status': 'str'
    }

    attribute_map = {
        'data_encryption_status': 'DataEncryptionStatus',
        'log_encryption_status': 'LogEncryptionStatus'
    }

    def __init__(self, data_encryption_status=None, log_encryption_status=None, _configuration=None):  # noqa: E501
        """DescribeBackupEncryptionStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_encryption_status = None
        self._log_encryption_status = None
        self.discriminator = None

        if data_encryption_status is not None:
            self.data_encryption_status = data_encryption_status
        if log_encryption_status is not None:
            self.log_encryption_status = log_encryption_status

    @property
    def data_encryption_status(self):
        """Gets the data_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501


        :return: The data_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_encryption_status

    @data_encryption_status.setter
    def data_encryption_status(self, data_encryption_status):
        """Sets the data_encryption_status of this DescribeBackupEncryptionStatusResponse.


        :param data_encryption_status: The data_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501
        :type: str
        """

        self._data_encryption_status = data_encryption_status

    @property
    def log_encryption_status(self):
        """Gets the log_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501


        :return: The log_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._log_encryption_status

    @log_encryption_status.setter
    def log_encryption_status(self, log_encryption_status):
        """Sets the log_encryption_status of this DescribeBackupEncryptionStatusResponse.


        :param log_encryption_status: The log_encryption_status of this DescribeBackupEncryptionStatusResponse.  # noqa: E501
        :type: str
        """

        self._log_encryption_status = log_encryption_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBackupEncryptionStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBackupEncryptionStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBackupEncryptionStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
