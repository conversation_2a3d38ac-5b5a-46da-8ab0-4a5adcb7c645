# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TotalStatisticResultForDescribeStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'listener_id': 'str',
        'max_bandwidth': 'float',
        'max_bandwidth95': 'float',
        'max_connection_num': 'float',
        'region': 'str',
        'total_traffic': 'float'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'listener_id': 'ListenerId',
        'max_bandwidth': 'MaxBandwidth',
        'max_bandwidth95': 'MaxBandwidth95',
        'max_connection_num': 'MaxConnectionNum',
        'region': 'Region',
        'total_traffic': 'TotalTraffic'
    }

    def __init__(self, accelerator_id=None, listener_id=None, max_bandwidth=None, max_bandwidth95=None, max_connection_num=None, region=None, total_traffic=None, _configuration=None):  # noqa: E501
        """TotalStatisticResultForDescribeStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._listener_id = None
        self._max_bandwidth = None
        self._max_bandwidth95 = None
        self._max_connection_num = None
        self._region = None
        self._total_traffic = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if listener_id is not None:
            self.listener_id = listener_id
        if max_bandwidth is not None:
            self.max_bandwidth = max_bandwidth
        if max_bandwidth95 is not None:
            self.max_bandwidth95 = max_bandwidth95
        if max_connection_num is not None:
            self.max_connection_num = max_connection_num
        if region is not None:
            self.region = region
        if total_traffic is not None:
            self.total_traffic = total_traffic

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The accelerator_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this TotalStatisticResultForDescribeStatisticsOutput.


        :param accelerator_id: The accelerator_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def listener_id(self):
        """Gets the listener_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The listener_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this TotalStatisticResultForDescribeStatisticsOutput.


        :param listener_id: The listener_id of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def max_bandwidth(self):
        """Gets the max_bandwidth of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The max_bandwidth of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_bandwidth

    @max_bandwidth.setter
    def max_bandwidth(self, max_bandwidth):
        """Sets the max_bandwidth of this TotalStatisticResultForDescribeStatisticsOutput.


        :param max_bandwidth: The max_bandwidth of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_bandwidth = max_bandwidth

    @property
    def max_bandwidth95(self):
        """Gets the max_bandwidth95 of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The max_bandwidth95 of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_bandwidth95

    @max_bandwidth95.setter
    def max_bandwidth95(self, max_bandwidth95):
        """Sets the max_bandwidth95 of this TotalStatisticResultForDescribeStatisticsOutput.


        :param max_bandwidth95: The max_bandwidth95 of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_bandwidth95 = max_bandwidth95

    @property
    def max_connection_num(self):
        """Gets the max_connection_num of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The max_connection_num of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_connection_num

    @max_connection_num.setter
    def max_connection_num(self, max_connection_num):
        """Sets the max_connection_num of this TotalStatisticResultForDescribeStatisticsOutput.


        :param max_connection_num: The max_connection_num of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_connection_num = max_connection_num

    @property
    def region(self):
        """Gets the region of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The region of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this TotalStatisticResultForDescribeStatisticsOutput.


        :param region: The region of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def total_traffic(self):
        """Gets the total_traffic of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The total_traffic of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._total_traffic

    @total_traffic.setter
    def total_traffic(self, total_traffic):
        """Sets the total_traffic of this TotalStatisticResultForDescribeStatisticsOutput.


        :param total_traffic: The total_traffic of this TotalStatisticResultForDescribeStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._total_traffic = total_traffic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TotalStatisticResultForDescribeStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TotalStatisticResultForDescribeStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TotalStatisticResultForDescribeStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
