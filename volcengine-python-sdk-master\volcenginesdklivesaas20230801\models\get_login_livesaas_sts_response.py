# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetLoginLivesaasStsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'login_livesaas_token': 'str',
        'sts_msg': 'StsMsgForGetLoginLivesaasStsOutput'
    }

    attribute_map = {
        'login_livesaas_token': 'LoginLivesaasToken',
        'sts_msg': 'StsMsg'
    }

    def __init__(self, login_livesaas_token=None, sts_msg=None, _configuration=None):  # noqa: E501
        """GetLoginLivesaasStsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._login_livesaas_token = None
        self._sts_msg = None
        self.discriminator = None

        if login_livesaas_token is not None:
            self.login_livesaas_token = login_livesaas_token
        if sts_msg is not None:
            self.sts_msg = sts_msg

    @property
    def login_livesaas_token(self):
        """Gets the login_livesaas_token of this GetLoginLivesaasStsResponse.  # noqa: E501


        :return: The login_livesaas_token of this GetLoginLivesaasStsResponse.  # noqa: E501
        :rtype: str
        """
        return self._login_livesaas_token

    @login_livesaas_token.setter
    def login_livesaas_token(self, login_livesaas_token):
        """Sets the login_livesaas_token of this GetLoginLivesaasStsResponse.


        :param login_livesaas_token: The login_livesaas_token of this GetLoginLivesaasStsResponse.  # noqa: E501
        :type: str
        """

        self._login_livesaas_token = login_livesaas_token

    @property
    def sts_msg(self):
        """Gets the sts_msg of this GetLoginLivesaasStsResponse.  # noqa: E501


        :return: The sts_msg of this GetLoginLivesaasStsResponse.  # noqa: E501
        :rtype: StsMsgForGetLoginLivesaasStsOutput
        """
        return self._sts_msg

    @sts_msg.setter
    def sts_msg(self, sts_msg):
        """Sets the sts_msg of this GetLoginLivesaasStsResponse.


        :param sts_msg: The sts_msg of this GetLoginLivesaasStsResponse.  # noqa: E501
        :type: StsMsgForGetLoginLivesaasStsOutput
        """

        self._sts_msg = sts_msg

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetLoginLivesaasStsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetLoginLivesaasStsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetLoginLivesaasStsResponse):
            return True

        return self.to_dict() != other.to_dict()
