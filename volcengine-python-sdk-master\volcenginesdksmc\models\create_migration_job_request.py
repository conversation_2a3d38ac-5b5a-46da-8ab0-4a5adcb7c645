# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateMigrationJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'destination_config': 'DestinationConfigForCreateMigrationJobInput',
        'disk_configs': 'list[DiskConfigForCreateMigrationJobInput]',
        'migration_job_description': 'str',
        'migration_job_name': 'str',
        'migration_source_id': 'str',
        'network_config': 'NetworkConfigForCreateMigrationJobInput',
        'project_name': 'str',
        'sync_config': 'SyncConfigForCreateMigrationJobInput',
        'tags': 'list[TagForCreateMigrationJobInput]'
    }

    attribute_map = {
        'destination_config': 'DestinationConfig',
        'disk_configs': 'DiskConfigs',
        'migration_job_description': 'MigrationJobDescription',
        'migration_job_name': 'MigrationJobName',
        'migration_source_id': 'MigrationSourceId',
        'network_config': 'NetworkConfig',
        'project_name': 'ProjectName',
        'sync_config': 'SyncConfig',
        'tags': 'Tags'
    }

    def __init__(self, destination_config=None, disk_configs=None, migration_job_description=None, migration_job_name=None, migration_source_id=None, network_config=None, project_name=None, sync_config=None, tags=None, _configuration=None):  # noqa: E501
        """CreateMigrationJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._destination_config = None
        self._disk_configs = None
        self._migration_job_description = None
        self._migration_job_name = None
        self._migration_source_id = None
        self._network_config = None
        self._project_name = None
        self._sync_config = None
        self._tags = None
        self.discriminator = None

        if destination_config is not None:
            self.destination_config = destination_config
        if disk_configs is not None:
            self.disk_configs = disk_configs
        if migration_job_description is not None:
            self.migration_job_description = migration_job_description
        self.migration_job_name = migration_job_name
        self.migration_source_id = migration_source_id
        if network_config is not None:
            self.network_config = network_config
        if project_name is not None:
            self.project_name = project_name
        if sync_config is not None:
            self.sync_config = sync_config
        if tags is not None:
            self.tags = tags

    @property
    def destination_config(self):
        """Gets the destination_config of this CreateMigrationJobRequest.  # noqa: E501


        :return: The destination_config of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: DestinationConfigForCreateMigrationJobInput
        """
        return self._destination_config

    @destination_config.setter
    def destination_config(self, destination_config):
        """Sets the destination_config of this CreateMigrationJobRequest.


        :param destination_config: The destination_config of this CreateMigrationJobRequest.  # noqa: E501
        :type: DestinationConfigForCreateMigrationJobInput
        """

        self._destination_config = destination_config

    @property
    def disk_configs(self):
        """Gets the disk_configs of this CreateMigrationJobRequest.  # noqa: E501


        :return: The disk_configs of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: list[DiskConfigForCreateMigrationJobInput]
        """
        return self._disk_configs

    @disk_configs.setter
    def disk_configs(self, disk_configs):
        """Sets the disk_configs of this CreateMigrationJobRequest.


        :param disk_configs: The disk_configs of this CreateMigrationJobRequest.  # noqa: E501
        :type: list[DiskConfigForCreateMigrationJobInput]
        """

        self._disk_configs = disk_configs

    @property
    def migration_job_description(self):
        """Gets the migration_job_description of this CreateMigrationJobRequest.  # noqa: E501


        :return: The migration_job_description of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_description

    @migration_job_description.setter
    def migration_job_description(self, migration_job_description):
        """Sets the migration_job_description of this CreateMigrationJobRequest.


        :param migration_job_description: The migration_job_description of this CreateMigrationJobRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_description = migration_job_description

    @property
    def migration_job_name(self):
        """Gets the migration_job_name of this CreateMigrationJobRequest.  # noqa: E501


        :return: The migration_job_name of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_name

    @migration_job_name.setter
    def migration_job_name(self, migration_job_name):
        """Sets the migration_job_name of this CreateMigrationJobRequest.


        :param migration_job_name: The migration_job_name of this CreateMigrationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and migration_job_name is None:
            raise ValueError("Invalid value for `migration_job_name`, must not be `None`")  # noqa: E501

        self._migration_job_name = migration_job_name

    @property
    def migration_source_id(self):
        """Gets the migration_source_id of this CreateMigrationJobRequest.  # noqa: E501


        :return: The migration_source_id of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_source_id

    @migration_source_id.setter
    def migration_source_id(self, migration_source_id):
        """Sets the migration_source_id of this CreateMigrationJobRequest.


        :param migration_source_id: The migration_source_id of this CreateMigrationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and migration_source_id is None:
            raise ValueError("Invalid value for `migration_source_id`, must not be `None`")  # noqa: E501

        self._migration_source_id = migration_source_id

    @property
    def network_config(self):
        """Gets the network_config of this CreateMigrationJobRequest.  # noqa: E501


        :return: The network_config of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: NetworkConfigForCreateMigrationJobInput
        """
        return self._network_config

    @network_config.setter
    def network_config(self, network_config):
        """Sets the network_config of this CreateMigrationJobRequest.


        :param network_config: The network_config of this CreateMigrationJobRequest.  # noqa: E501
        :type: NetworkConfigForCreateMigrationJobInput
        """

        self._network_config = network_config

    @property
    def project_name(self):
        """Gets the project_name of this CreateMigrationJobRequest.  # noqa: E501


        :return: The project_name of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateMigrationJobRequest.


        :param project_name: The project_name of this CreateMigrationJobRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def sync_config(self):
        """Gets the sync_config of this CreateMigrationJobRequest.  # noqa: E501


        :return: The sync_config of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: SyncConfigForCreateMigrationJobInput
        """
        return self._sync_config

    @sync_config.setter
    def sync_config(self, sync_config):
        """Sets the sync_config of this CreateMigrationJobRequest.


        :param sync_config: The sync_config of this CreateMigrationJobRequest.  # noqa: E501
        :type: SyncConfigForCreateMigrationJobInput
        """

        self._sync_config = sync_config

    @property
    def tags(self):
        """Gets the tags of this CreateMigrationJobRequest.  # noqa: E501


        :return: The tags of this CreateMigrationJobRequest.  # noqa: E501
        :rtype: list[TagForCreateMigrationJobInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateMigrationJobRequest.


        :param tags: The tags of this CreateMigrationJobRequest.  # noqa: E501
        :type: list[TagForCreateMigrationJobInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateMigrationJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateMigrationJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateMigrationJobRequest):
            return True

        return self.to_dict() != other.to_dict()
