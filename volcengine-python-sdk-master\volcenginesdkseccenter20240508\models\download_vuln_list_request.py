# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DownloadVulnListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'conditions': 'ConditionsForDownloadVulnListInput',
        'id_list': 'list[str]',
        'status': 'list[str]',
        'vuln_type_list': 'list[str]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'conditions': 'Conditions',
        'id_list': 'IDList',
        'status': 'Status',
        'vuln_type_list': 'VulnTypeList'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, conditions=None, id_list=None, status=None, vuln_type_list=None, _configuration=None):  # noqa: E501
        """DownloadVulnListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._conditions = None
        self._id_list = None
        self._status = None
        self._vuln_type_list = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        if conditions is not None:
            self.conditions = conditions
        if id_list is not None:
            self.id_list = id_list
        if status is not None:
            self.status = status
        if vuln_type_list is not None:
            self.vuln_type_list = vuln_type_list

    @property
    def agent_id(self):
        """Gets the agent_id of this DownloadVulnListRequest.  # noqa: E501


        :return: The agent_id of this DownloadVulnListRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DownloadVulnListRequest.


        :param agent_id: The agent_id of this DownloadVulnListRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DownloadVulnListRequest.  # noqa: E501


        :return: The asset_id of this DownloadVulnListRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DownloadVulnListRequest.


        :param asset_id: The asset_id of this DownloadVulnListRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this DownloadVulnListRequest.  # noqa: E501


        :return: The asset_type of this DownloadVulnListRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this DownloadVulnListRequest.


        :param asset_type: The asset_type of this DownloadVulnListRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def conditions(self):
        """Gets the conditions of this DownloadVulnListRequest.  # noqa: E501


        :return: The conditions of this DownloadVulnListRequest.  # noqa: E501
        :rtype: ConditionsForDownloadVulnListInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this DownloadVulnListRequest.


        :param conditions: The conditions of this DownloadVulnListRequest.  # noqa: E501
        :type: ConditionsForDownloadVulnListInput
        """

        self._conditions = conditions

    @property
    def id_list(self):
        """Gets the id_list of this DownloadVulnListRequest.  # noqa: E501


        :return: The id_list of this DownloadVulnListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._id_list

    @id_list.setter
    def id_list(self, id_list):
        """Sets the id_list of this DownloadVulnListRequest.


        :param id_list: The id_list of this DownloadVulnListRequest.  # noqa: E501
        :type: list[str]
        """

        self._id_list = id_list

    @property
    def status(self):
        """Gets the status of this DownloadVulnListRequest.  # noqa: E501


        :return: The status of this DownloadVulnListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DownloadVulnListRequest.


        :param status: The status of this DownloadVulnListRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def vuln_type_list(self):
        """Gets the vuln_type_list of this DownloadVulnListRequest.  # noqa: E501


        :return: The vuln_type_list of this DownloadVulnListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_type_list

    @vuln_type_list.setter
    def vuln_type_list(self, vuln_type_list):
        """Sets the vuln_type_list of this DownloadVulnListRequest.


        :param vuln_type_list: The vuln_type_list of this DownloadVulnListRequest.  # noqa: E501
        :type: list[str]
        """

        self._vuln_type_list = vuln_type_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DownloadVulnListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DownloadVulnListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DownloadVulnListRequest):
            return True

        return self.to_dict() != other.to_dict()
