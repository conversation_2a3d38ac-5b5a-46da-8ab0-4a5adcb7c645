# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdatePublicBandwidthPackageResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_need_pay': 'bool',
        'order_id': 'str'
    }

    attribute_map = {
        'is_need_pay': 'IsNeedPay',
        'order_id': 'OrderId'
    }

    def __init__(self, is_need_pay=None, order_id=None, _configuration=None):  # noqa: E501
        """UpdatePublicBandwidthPackageResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_need_pay = None
        self._order_id = None
        self.discriminator = None

        if is_need_pay is not None:
            self.is_need_pay = is_need_pay
        if order_id is not None:
            self.order_id = order_id

    @property
    def is_need_pay(self):
        """Gets the is_need_pay of this UpdatePublicBandwidthPackageResponse.  # noqa: E501


        :return: The is_need_pay of this UpdatePublicBandwidthPackageResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_need_pay

    @is_need_pay.setter
    def is_need_pay(self, is_need_pay):
        """Sets the is_need_pay of this UpdatePublicBandwidthPackageResponse.


        :param is_need_pay: The is_need_pay of this UpdatePublicBandwidthPackageResponse.  # noqa: E501
        :type: bool
        """

        self._is_need_pay = is_need_pay

    @property
    def order_id(self):
        """Gets the order_id of this UpdatePublicBandwidthPackageResponse.  # noqa: E501


        :return: The order_id of this UpdatePublicBandwidthPackageResponse.  # noqa: E501
        :rtype: str
        """
        return self._order_id

    @order_id.setter
    def order_id(self, order_id):
        """Sets the order_id of this UpdatePublicBandwidthPackageResponse.


        :param order_id: The order_id of this UpdatePublicBandwidthPackageResponse.  # noqa: E501
        :type: str
        """

        self._order_id = order_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdatePublicBandwidthPackageResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdatePublicBandwidthPackageResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdatePublicBandwidthPackageResponse):
            return True

        return self.to_dict() != other.to_dict()
