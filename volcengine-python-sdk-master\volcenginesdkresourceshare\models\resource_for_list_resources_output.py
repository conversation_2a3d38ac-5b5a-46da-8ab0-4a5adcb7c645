# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceForListResourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'region_scope': 'str',
        'resource_shares': 'list[ResourceShareForListResourcesOutput]',
        'resource_type': 'str',
        'status': 'str',
        'trn': 'str'
    }

    attribute_map = {
        'region_scope': 'RegionScope',
        'resource_shares': 'ResourceShares',
        'resource_type': 'ResourceType',
        'status': 'Status',
        'trn': 'Trn'
    }

    def __init__(self, region_scope=None, resource_shares=None, resource_type=None, status=None, trn=None, _configuration=None):  # noqa: E501
        """ResourceForListResourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._region_scope = None
        self._resource_shares = None
        self._resource_type = None
        self._status = None
        self._trn = None
        self.discriminator = None

        if region_scope is not None:
            self.region_scope = region_scope
        if resource_shares is not None:
            self.resource_shares = resource_shares
        if resource_type is not None:
            self.resource_type = resource_type
        if status is not None:
            self.status = status
        if trn is not None:
            self.trn = trn

    @property
    def region_scope(self):
        """Gets the region_scope of this ResourceForListResourcesOutput.  # noqa: E501


        :return: The region_scope of this ResourceForListResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_scope

    @region_scope.setter
    def region_scope(self, region_scope):
        """Sets the region_scope of this ResourceForListResourcesOutput.


        :param region_scope: The region_scope of this ResourceForListResourcesOutput.  # noqa: E501
        :type: str
        """

        self._region_scope = region_scope

    @property
    def resource_shares(self):
        """Gets the resource_shares of this ResourceForListResourcesOutput.  # noqa: E501


        :return: The resource_shares of this ResourceForListResourcesOutput.  # noqa: E501
        :rtype: list[ResourceShareForListResourcesOutput]
        """
        return self._resource_shares

    @resource_shares.setter
    def resource_shares(self, resource_shares):
        """Sets the resource_shares of this ResourceForListResourcesOutput.


        :param resource_shares: The resource_shares of this ResourceForListResourcesOutput.  # noqa: E501
        :type: list[ResourceShareForListResourcesOutput]
        """

        self._resource_shares = resource_shares

    @property
    def resource_type(self):
        """Gets the resource_type of this ResourceForListResourcesOutput.  # noqa: E501


        :return: The resource_type of this ResourceForListResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ResourceForListResourcesOutput.


        :param resource_type: The resource_type of this ResourceForListResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def status(self):
        """Gets the status of this ResourceForListResourcesOutput.  # noqa: E501


        :return: The status of this ResourceForListResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResourceForListResourcesOutput.


        :param status: The status of this ResourceForListResourcesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def trn(self):
        """Gets the trn of this ResourceForListResourcesOutput.  # noqa: E501


        :return: The trn of this ResourceForListResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._trn

    @trn.setter
    def trn(self, trn):
        """Sets the trn of this ResourceForListResourcesOutput.


        :param trn: The trn of this ResourceForListResourcesOutput.  # noqa: E501
        :type: str
        """

        self._trn = trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceForListResourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceForListResourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceForListResourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
