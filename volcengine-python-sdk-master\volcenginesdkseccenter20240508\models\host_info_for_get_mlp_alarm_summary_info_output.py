# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HostInfoForGetMlpAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cloud_providers': 'list[str]',
        'container_image': 'str',
        'container_name': 'str',
        'hostname': 'str',
        'in_ip_list': 'list[str]',
        'leaf_group_ids': 'list[str]',
        'mlp_instance_id': 'str',
        'os': 'str',
        'os_platform': 'str',
        'out_ip_list': 'list[str]',
        'region': 'str',
        'tag_list': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cloud_providers': 'CloudProviders',
        'container_image': 'ContainerImage',
        'container_name': 'ContainerName',
        'hostname': 'Hostname',
        'in_ip_list': 'InIPList',
        'leaf_group_ids': 'LeafGroupIDs',
        'mlp_instance_id': 'MlpInstanceID',
        'os': 'Os',
        'os_platform': 'OsPlatform',
        'out_ip_list': 'OutIPList',
        'region': 'Region',
        'tag_list': 'TagList',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, cloud_providers=None, container_image=None, container_name=None, hostname=None, in_ip_list=None, leaf_group_ids=None, mlp_instance_id=None, os=None, os_platform=None, out_ip_list=None, region=None, tag_list=None, top_group_id=None, _configuration=None):  # noqa: E501
        """HostInfoForGetMlpAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cloud_providers = None
        self._container_image = None
        self._container_name = None
        self._hostname = None
        self._in_ip_list = None
        self._leaf_group_ids = None
        self._mlp_instance_id = None
        self._os = None
        self._os_platform = None
        self._out_ip_list = None
        self._region = None
        self._tag_list = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if container_image is not None:
            self.container_image = container_image
        if container_name is not None:
            self.container_name = container_name
        if hostname is not None:
            self.hostname = hostname
        if in_ip_list is not None:
            self.in_ip_list = in_ip_list
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if mlp_instance_id is not None:
            self.mlp_instance_id = mlp_instance_id
        if os is not None:
            self.os = os
        if os_platform is not None:
            self.os_platform = os_platform
        if out_ip_list is not None:
            self.out_ip_list = out_ip_list
        if region is not None:
            self.region = region
        if tag_list is not None:
            self.tag_list = tag_list
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The agent_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param agent_id: The agent_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cloud_providers of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param cloud_providers: The cloud_providers of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def container_image(self):
        """Gets the container_image of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_image of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_image

    @container_image.setter
    def container_image(self, container_image):
        """Sets the container_image of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_image: The container_image of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_image = container_image

    @property
    def container_name(self):
        """Gets the container_name of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_name of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_name: The container_name of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def hostname(self):
        """Gets the hostname of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hostname of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param hostname: The hostname of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def in_ip_list(self):
        """Gets the in_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The in_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._in_ip_list

    @in_ip_list.setter
    def in_ip_list(self, in_ip_list):
        """Sets the in_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param in_ip_list: The in_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._in_ip_list = in_ip_list

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The leaf_group_ids of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param leaf_group_ids: The leaf_group_ids of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def mlp_instance_id(self):
        """Gets the mlp_instance_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The mlp_instance_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._mlp_instance_id

    @mlp_instance_id.setter
    def mlp_instance_id(self, mlp_instance_id):
        """Sets the mlp_instance_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param mlp_instance_id: The mlp_instance_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._mlp_instance_id = mlp_instance_id

    @property
    def os(self):
        """Gets the os of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The os of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param os: The os of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def os_platform(self):
        """Gets the os_platform of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The os_platform of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_platform

    @os_platform.setter
    def os_platform(self, os_platform):
        """Sets the os_platform of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param os_platform: The os_platform of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._os_platform = os_platform

    @property
    def out_ip_list(self):
        """Gets the out_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The out_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._out_ip_list

    @out_ip_list.setter
    def out_ip_list(self, out_ip_list):
        """Sets the out_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param out_ip_list: The out_ip_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._out_ip_list = out_ip_list

    @property
    def region(self):
        """Gets the region of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The region of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param region: The region of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def tag_list(self):
        """Gets the tag_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The tag_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag_list

    @tag_list.setter
    def tag_list(self, tag_list):
        """Sets the tag_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param tag_list: The tag_list of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag_list = tag_list

    @property
    def top_group_id(self):
        """Gets the top_group_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The top_group_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.


        :param top_group_id: The top_group_id of this HostInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HostInfoForGetMlpAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HostInfoForGetMlpAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HostInfoForGetMlpAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
