# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExportAssetCenterDevsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'asset_ids': 'list[str]',
        'asset_name': 'str',
        'ip': 'str',
        'is_all': 'bool',
        'platforms': 'list[str]',
        'regions': 'list[str]',
        'risk': 'RiskForExportAssetCenterDevsInput',
        'status': 'list[str]'
    }

    attribute_map = {
        'asset_id': 'AssetId',
        'asset_ids': 'AssetIds',
        'asset_name': 'AssetName',
        'ip': 'IP',
        'is_all': 'IsAll',
        'platforms': 'Platforms',
        'regions': 'Regions',
        'risk': 'Risk',
        'status': 'Status'
    }

    def __init__(self, asset_id=None, asset_ids=None, asset_name=None, ip=None, is_all=None, platforms=None, regions=None, risk=None, status=None, _configuration=None):  # noqa: E501
        """ExportAssetCenterDevsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._asset_ids = None
        self._asset_name = None
        self._ip = None
        self._is_all = None
        self._platforms = None
        self._regions = None
        self._risk = None
        self._status = None
        self.discriminator = None

        if asset_id is not None:
            self.asset_id = asset_id
        if asset_ids is not None:
            self.asset_ids = asset_ids
        if asset_name is not None:
            self.asset_name = asset_name
        if ip is not None:
            self.ip = ip
        if is_all is not None:
            self.is_all = is_all
        if platforms is not None:
            self.platforms = platforms
        if regions is not None:
            self.regions = regions
        if risk is not None:
            self.risk = risk
        if status is not None:
            self.status = status

    @property
    def asset_id(self):
        """Gets the asset_id of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The asset_id of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ExportAssetCenterDevsRequest.


        :param asset_id: The asset_id of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_ids(self):
        """Gets the asset_ids of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The asset_ids of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_ids

    @asset_ids.setter
    def asset_ids(self, asset_ids):
        """Sets the asset_ids of this ExportAssetCenterDevsRequest.


        :param asset_ids: The asset_ids of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_ids = asset_ids

    @property
    def asset_name(self):
        """Gets the asset_name of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The asset_name of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this ExportAssetCenterDevsRequest.


        :param asset_name: The asset_name of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def ip(self):
        """Gets the ip of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The ip of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ExportAssetCenterDevsRequest.


        :param ip: The ip of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_all(self):
        """Gets the is_all of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The is_all of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_all

    @is_all.setter
    def is_all(self, is_all):
        """Sets the is_all of this ExportAssetCenterDevsRequest.


        :param is_all: The is_all of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: bool
        """

        self._is_all = is_all

    @property
    def platforms(self):
        """Gets the platforms of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The platforms of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._platforms

    @platforms.setter
    def platforms(self, platforms):
        """Sets the platforms of this ExportAssetCenterDevsRequest.


        :param platforms: The platforms of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: list[str]
        """

        self._platforms = platforms

    @property
    def regions(self):
        """Gets the regions of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The regions of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this ExportAssetCenterDevsRequest.


        :param regions: The regions of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def risk(self):
        """Gets the risk of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The risk of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: RiskForExportAssetCenterDevsInput
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this ExportAssetCenterDevsRequest.


        :param risk: The risk of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: RiskForExportAssetCenterDevsInput
        """

        self._risk = risk

    @property
    def status(self):
        """Gets the status of this ExportAssetCenterDevsRequest.  # noqa: E501


        :return: The status of this ExportAssetCenterDevsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ExportAssetCenterDevsRequest.


        :param status: The status of this ExportAssetCenterDevsRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExportAssetCenterDevsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExportAssetCenterDevsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExportAssetCenterDevsRequest):
            return True

        return self.to_dict() != other.to_dict()
