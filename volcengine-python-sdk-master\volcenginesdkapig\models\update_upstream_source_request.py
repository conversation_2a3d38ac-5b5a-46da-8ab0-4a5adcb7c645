# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateUpstreamSourceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comments': 'str',
        'id': 'str',
        'ingress_settings': 'IngressSettingsForUpdateUpstreamSourceInput',
        'watch_namespaces': 'list[str]'
    }

    attribute_map = {
        'comments': 'Comments',
        'id': 'Id',
        'ingress_settings': 'IngressSettings',
        'watch_namespaces': 'WatchNamespaces'
    }

    def __init__(self, comments=None, id=None, ingress_settings=None, watch_namespaces=None, _configuration=None):  # noqa: E501
        """UpdateUpstreamSourceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comments = None
        self._id = None
        self._ingress_settings = None
        self._watch_namespaces = None
        self.discriminator = None

        if comments is not None:
            self.comments = comments
        self.id = id
        if ingress_settings is not None:
            self.ingress_settings = ingress_settings
        if watch_namespaces is not None:
            self.watch_namespaces = watch_namespaces

    @property
    def comments(self):
        """Gets the comments of this UpdateUpstreamSourceRequest.  # noqa: E501


        :return: The comments of this UpdateUpstreamSourceRequest.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this UpdateUpstreamSourceRequest.


        :param comments: The comments of this UpdateUpstreamSourceRequest.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def id(self):
        """Gets the id of this UpdateUpstreamSourceRequest.  # noqa: E501


        :return: The id of this UpdateUpstreamSourceRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateUpstreamSourceRequest.


        :param id: The id of this UpdateUpstreamSourceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def ingress_settings(self):
        """Gets the ingress_settings of this UpdateUpstreamSourceRequest.  # noqa: E501


        :return: The ingress_settings of this UpdateUpstreamSourceRequest.  # noqa: E501
        :rtype: IngressSettingsForUpdateUpstreamSourceInput
        """
        return self._ingress_settings

    @ingress_settings.setter
    def ingress_settings(self, ingress_settings):
        """Sets the ingress_settings of this UpdateUpstreamSourceRequest.


        :param ingress_settings: The ingress_settings of this UpdateUpstreamSourceRequest.  # noqa: E501
        :type: IngressSettingsForUpdateUpstreamSourceInput
        """

        self._ingress_settings = ingress_settings

    @property
    def watch_namespaces(self):
        """Gets the watch_namespaces of this UpdateUpstreamSourceRequest.  # noqa: E501


        :return: The watch_namespaces of this UpdateUpstreamSourceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._watch_namespaces

    @watch_namespaces.setter
    def watch_namespaces(self, watch_namespaces):
        """Sets the watch_namespaces of this UpdateUpstreamSourceRequest.


        :param watch_namespaces: The watch_namespaces of this UpdateUpstreamSourceRequest.  # noqa: E501
        :type: list[str]
        """

        self._watch_namespaces = watch_namespaces

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateUpstreamSourceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateUpstreamSourceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateUpstreamSourceRequest):
            return True

        return self.to_dict() != other.to_dict()
