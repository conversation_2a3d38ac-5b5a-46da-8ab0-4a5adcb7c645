# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SyncStatusStateForListCloudAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain_sync_status_state': 'DomainSyncStatusStateForListCloudAccountsOutput',
        'stat_sync_status_state': 'StatSyncStatusStateForListCloudAccountsOutput'
    }

    attribute_map = {
        'domain_sync_status_state': 'DomainSyncStatusState',
        'stat_sync_status_state': 'StatSyncStatusState'
    }

    def __init__(self, domain_sync_status_state=None, stat_sync_status_state=None, _configuration=None):  # noqa: E501
        """SyncStatusStateForListCloudAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain_sync_status_state = None
        self._stat_sync_status_state = None
        self.discriminator = None

        if domain_sync_status_state is not None:
            self.domain_sync_status_state = domain_sync_status_state
        if stat_sync_status_state is not None:
            self.stat_sync_status_state = stat_sync_status_state

    @property
    def domain_sync_status_state(self):
        """Gets the domain_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501


        :return: The domain_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501
        :rtype: DomainSyncStatusStateForListCloudAccountsOutput
        """
        return self._domain_sync_status_state

    @domain_sync_status_state.setter
    def domain_sync_status_state(self, domain_sync_status_state):
        """Sets the domain_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.


        :param domain_sync_status_state: The domain_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501
        :type: DomainSyncStatusStateForListCloudAccountsOutput
        """

        self._domain_sync_status_state = domain_sync_status_state

    @property
    def stat_sync_status_state(self):
        """Gets the stat_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501


        :return: The stat_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501
        :rtype: StatSyncStatusStateForListCloudAccountsOutput
        """
        return self._stat_sync_status_state

    @stat_sync_status_state.setter
    def stat_sync_status_state(self, stat_sync_status_state):
        """Sets the stat_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.


        :param stat_sync_status_state: The stat_sync_status_state of this SyncStatusStateForListCloudAccountsOutput.  # noqa: E501
        :type: StatSyncStatusStateForListCloudAccountsOutput
        """

        self._stat_sync_status_state = stat_sync_status_state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SyncStatusStateForListCloudAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SyncStatusStateForListCloudAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SyncStatusStateForListCloudAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
