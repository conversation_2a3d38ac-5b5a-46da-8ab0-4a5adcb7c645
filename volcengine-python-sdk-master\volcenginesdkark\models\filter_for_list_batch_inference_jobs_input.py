# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListBatchInferenceJobsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_model_ids': 'list[str]',
        'foundation_models': 'list[FoundationModelForListBatchInferenceJobsInput]',
        'ids': 'list[str]',
        'name': 'str',
        'phases': 'list[str]'
    }

    attribute_map = {
        'custom_model_ids': 'CustomModelIds',
        'foundation_models': 'FoundationModels',
        'ids': 'Ids',
        'name': 'Name',
        'phases': 'Phases'
    }

    def __init__(self, custom_model_ids=None, foundation_models=None, ids=None, name=None, phases=None, _configuration=None):  # noqa: E501
        """FilterForListBatchInferenceJobsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_model_ids = None
        self._foundation_models = None
        self._ids = None
        self._name = None
        self._phases = None
        self.discriminator = None

        if custom_model_ids is not None:
            self.custom_model_ids = custom_model_ids
        if foundation_models is not None:
            self.foundation_models = foundation_models
        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name
        if phases is not None:
            self.phases = phases

    @property
    def custom_model_ids(self):
        """Gets the custom_model_ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501


        :return: The custom_model_ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._custom_model_ids

    @custom_model_ids.setter
    def custom_model_ids(self, custom_model_ids):
        """Sets the custom_model_ids of this FilterForListBatchInferenceJobsInput.


        :param custom_model_ids: The custom_model_ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :type: list[str]
        """

        self._custom_model_ids = custom_model_ids

    @property
    def foundation_models(self):
        """Gets the foundation_models of this FilterForListBatchInferenceJobsInput.  # noqa: E501


        :return: The foundation_models of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :rtype: list[FoundationModelForListBatchInferenceJobsInput]
        """
        return self._foundation_models

    @foundation_models.setter
    def foundation_models(self, foundation_models):
        """Sets the foundation_models of this FilterForListBatchInferenceJobsInput.


        :param foundation_models: The foundation_models of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :type: list[FoundationModelForListBatchInferenceJobsInput]
        """

        self._foundation_models = foundation_models

    @property
    def ids(self):
        """Gets the ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501


        :return: The ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListBatchInferenceJobsInput.


        :param ids: The ids of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListBatchInferenceJobsInput.  # noqa: E501


        :return: The name of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListBatchInferenceJobsInput.


        :param name: The name of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def phases(self):
        """Gets the phases of this FilterForListBatchInferenceJobsInput.  # noqa: E501


        :return: The phases of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._phases

    @phases.setter
    def phases(self, phases):
        """Sets the phases of this FilterForListBatchInferenceJobsInput.


        :param phases: The phases of this FilterForListBatchInferenceJobsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Queued", "Running", "Completed", "Terminating", "Terminated", "Failed"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(phases).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `phases` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(phases) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._phases = phases

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListBatchInferenceJobsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListBatchInferenceJobsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListBatchInferenceJobsInput):
            return True

        return self.to_dict() != other.to_dict()
