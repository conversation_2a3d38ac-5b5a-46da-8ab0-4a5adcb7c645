# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListAmortizedCostBillDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amortized_begin_time': 'str',
        'amortized_day': 'str',
        'amortized_end_time': 'str',
        'amortized_month': 'str',
        'amortized_type': 'str',
        'bill_category': 'str',
        'bill_id': 'str',
        'bill_period': 'str',
        'billing_function': 'str',
        'billing_method_code': 'str',
        'billing_mode': 'str',
        'busi_period': 'str',
        'business_mode': 'str',
        'config_name': 'str',
        'cost_id': 'str',
        'count': 'str',
        'country_area': 'str',
        'coupon_amount': 'str',
        'currency': 'str',
        'daily_amortized_coupon_amount': 'str',
        'daily_amortized_discount_bill_amount': 'str',
        'daily_amortized_original_bill_amount': 'str',
        'daily_amortized_paid_amount': 'str',
        'daily_amortized_payable_amount': 'str',
        'daily_amortized_pre_tax_payable_amount': 'str',
        'daily_amortized_pre_tax_real_value': 'str',
        'daily_amortized_preferential_bill_amount': 'str',
        'daily_amortized_real_value': 'str',
        'daily_amortized_round_amount': 'str',
        'daily_amortized_settle_payable_amount': 'str',
        'daily_amortized_settle_pre_tax_payable_amount': 'str',
        'daily_amortized_settle_pre_tax_real_value': 'str',
        'daily_amortized_settle_real_value': 'str',
        'daily_amortized_settle_tax_amount': 'str',
        'daily_amortized_tax_amount': 'str',
        'deduction_use_duration': 'str',
        'discount_bill_amount': 'str',
        'discount_biz_billing_function': 'str',
        'discount_biz_measure_interval': 'str',
        'discount_biz_unit_price': 'str',
        'discount_biz_unit_price_interval': 'str',
        'effective_factor': 'str',
        'element': 'str',
        'exchange_rate': 'str',
        'expand_field': 'str',
        'expense_begin_time': 'str',
        'expense_end_time': 'str',
        'factor': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'market_price': 'str',
        'measure_interval': 'str',
        'original_bill_amount': 'str',
        'owner_customer_name': 'str',
        'owner_id': 'str',
        'owner_user_name': 'str',
        'paid_amount': 'str',
        'payable_amount': 'str',
        'payer_customer_name': 'str',
        'payer_id': 'str',
        'payer_user_name': 'str',
        'pre_tax_payable_amount': 'str',
        'pre_tax_real_value': 'str',
        'preferential_bill_amount': 'str',
        'price': 'str',
        'price_interval': 'str',
        'price_unit': 'str',
        'product': 'str',
        'product_zh': 'str',
        'project': 'str',
        'project_display_name': 'str',
        'real_value': 'str',
        'region': 'str',
        'round_amount': 'str',
        'seller_customer_name': 'str',
        'seller_id': 'str',
        'seller_user_name': 'str',
        'settle_payable_amount': 'str',
        'settle_pre_tax_payable_amount': 'str',
        'settle_pre_tax_real_value': 'str',
        'settle_real_value': 'str',
        'settle_tax_amount': 'str',
        'split_item_id': 'str',
        'split_item_name': 'str',
        'subject_name': 'str',
        'tag': 'str',
        'tax_amount': 'str',
        'tax_rate': 'str',
        'trade_time': 'str',
        'unit': 'str',
        'use_duration': 'str',
        'use_duration_unit': 'str',
        'zone': 'str'
    }

    attribute_map = {
        'amortized_begin_time': 'AmortizedBeginTime',
        'amortized_day': 'AmortizedDay',
        'amortized_end_time': 'AmortizedEndTime',
        'amortized_month': 'AmortizedMonth',
        'amortized_type': 'AmortizedType',
        'bill_category': 'BillCategory',
        'bill_id': 'BillID',
        'bill_period': 'BillPeriod',
        'billing_function': 'BillingFunction',
        'billing_method_code': 'BillingMethodCode',
        'billing_mode': 'BillingMode',
        'busi_period': 'BusiPeriod',
        'business_mode': 'BusinessMode',
        'config_name': 'ConfigName',
        'cost_id': 'CostID',
        'count': 'Count',
        'country_area': 'CountryArea',
        'coupon_amount': 'CouponAmount',
        'currency': 'Currency',
        'daily_amortized_coupon_amount': 'DailyAmortizedCouponAmount',
        'daily_amortized_discount_bill_amount': 'DailyAmortizedDiscountBillAmount',
        'daily_amortized_original_bill_amount': 'DailyAmortizedOriginalBillAmount',
        'daily_amortized_paid_amount': 'DailyAmortizedPaidAmount',
        'daily_amortized_payable_amount': 'DailyAmortizedPayableAmount',
        'daily_amortized_pre_tax_payable_amount': 'DailyAmortizedPreTaxPayableAmount',
        'daily_amortized_pre_tax_real_value': 'DailyAmortizedPreTaxRealValue',
        'daily_amortized_preferential_bill_amount': 'DailyAmortizedPreferentialBillAmount',
        'daily_amortized_real_value': 'DailyAmortizedRealValue',
        'daily_amortized_round_amount': 'DailyAmortizedRoundAmount',
        'daily_amortized_settle_payable_amount': 'DailyAmortizedSettlePayableAmount',
        'daily_amortized_settle_pre_tax_payable_amount': 'DailyAmortizedSettlePreTaxPayableAmount',
        'daily_amortized_settle_pre_tax_real_value': 'DailyAmortizedSettlePreTaxRealValue',
        'daily_amortized_settle_real_value': 'DailyAmortizedSettleRealValue',
        'daily_amortized_settle_tax_amount': 'DailyAmortizedSettleTaxAmount',
        'daily_amortized_tax_amount': 'DailyAmortizedTaxAmount',
        'deduction_use_duration': 'DeductionUseDuration',
        'discount_bill_amount': 'DiscountBillAmount',
        'discount_biz_billing_function': 'DiscountBizBillingFunction',
        'discount_biz_measure_interval': 'DiscountBizMeasureInterval',
        'discount_biz_unit_price': 'DiscountBizUnitPrice',
        'discount_biz_unit_price_interval': 'DiscountBizUnitPriceInterval',
        'effective_factor': 'EffectiveFactor',
        'element': 'Element',
        'exchange_rate': 'ExchangeRate',
        'expand_field': 'ExpandField',
        'expense_begin_time': 'ExpenseBeginTime',
        'expense_end_time': 'ExpenseEndTime',
        'factor': 'Factor',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'market_price': 'MarketPrice',
        'measure_interval': 'MeasureInterval',
        'original_bill_amount': 'OriginalBillAmount',
        'owner_customer_name': 'OwnerCustomerName',
        'owner_id': 'OwnerID',
        'owner_user_name': 'OwnerUserName',
        'paid_amount': 'PaidAmount',
        'payable_amount': 'PayableAmount',
        'payer_customer_name': 'PayerCustomerName',
        'payer_id': 'PayerID',
        'payer_user_name': 'PayerUserName',
        'pre_tax_payable_amount': 'PreTaxPayableAmount',
        'pre_tax_real_value': 'PreTaxRealValue',
        'preferential_bill_amount': 'PreferentialBillAmount',
        'price': 'Price',
        'price_interval': 'PriceInterval',
        'price_unit': 'PriceUnit',
        'product': 'Product',
        'product_zh': 'ProductZh',
        'project': 'Project',
        'project_display_name': 'ProjectDisplayName',
        'real_value': 'RealValue',
        'region': 'Region',
        'round_amount': 'RoundAmount',
        'seller_customer_name': 'SellerCustomerName',
        'seller_id': 'SellerID',
        'seller_user_name': 'SellerUserName',
        'settle_payable_amount': 'SettlePayableAmount',
        'settle_pre_tax_payable_amount': 'SettlePreTaxPayableAmount',
        'settle_pre_tax_real_value': 'SettlePreTaxRealValue',
        'settle_real_value': 'SettleRealValue',
        'settle_tax_amount': 'SettleTaxAmount',
        'split_item_id': 'SplitItemID',
        'split_item_name': 'SplitItemName',
        'subject_name': 'SubjectName',
        'tag': 'Tag',
        'tax_amount': 'TaxAmount',
        'tax_rate': 'TaxRate',
        'trade_time': 'TradeTime',
        'unit': 'Unit',
        'use_duration': 'UseDuration',
        'use_duration_unit': 'UseDurationUnit',
        'zone': 'Zone'
    }

    def __init__(self, amortized_begin_time=None, amortized_day=None, amortized_end_time=None, amortized_month=None, amortized_type=None, bill_category=None, bill_id=None, bill_period=None, billing_function=None, billing_method_code=None, billing_mode=None, busi_period=None, business_mode=None, config_name=None, cost_id=None, count=None, country_area=None, coupon_amount=None, currency=None, daily_amortized_coupon_amount=None, daily_amortized_discount_bill_amount=None, daily_amortized_original_bill_amount=None, daily_amortized_paid_amount=None, daily_amortized_payable_amount=None, daily_amortized_pre_tax_payable_amount=None, daily_amortized_pre_tax_real_value=None, daily_amortized_preferential_bill_amount=None, daily_amortized_real_value=None, daily_amortized_round_amount=None, daily_amortized_settle_payable_amount=None, daily_amortized_settle_pre_tax_payable_amount=None, daily_amortized_settle_pre_tax_real_value=None, daily_amortized_settle_real_value=None, daily_amortized_settle_tax_amount=None, daily_amortized_tax_amount=None, deduction_use_duration=None, discount_bill_amount=None, discount_biz_billing_function=None, discount_biz_measure_interval=None, discount_biz_unit_price=None, discount_biz_unit_price_interval=None, effective_factor=None, element=None, exchange_rate=None, expand_field=None, expense_begin_time=None, expense_end_time=None, factor=None, instance_name=None, instance_no=None, market_price=None, measure_interval=None, original_bill_amount=None, owner_customer_name=None, owner_id=None, owner_user_name=None, paid_amount=None, payable_amount=None, payer_customer_name=None, payer_id=None, payer_user_name=None, pre_tax_payable_amount=None, pre_tax_real_value=None, preferential_bill_amount=None, price=None, price_interval=None, price_unit=None, product=None, product_zh=None, project=None, project_display_name=None, real_value=None, region=None, round_amount=None, seller_customer_name=None, seller_id=None, seller_user_name=None, settle_payable_amount=None, settle_pre_tax_payable_amount=None, settle_pre_tax_real_value=None, settle_real_value=None, settle_tax_amount=None, split_item_id=None, split_item_name=None, subject_name=None, tag=None, tax_amount=None, tax_rate=None, trade_time=None, unit=None, use_duration=None, use_duration_unit=None, zone=None, _configuration=None):  # noqa: E501
        """ListForListAmortizedCostBillDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._amortized_begin_time = None
        self._amortized_day = None
        self._amortized_end_time = None
        self._amortized_month = None
        self._amortized_type = None
        self._bill_category = None
        self._bill_id = None
        self._bill_period = None
        self._billing_function = None
        self._billing_method_code = None
        self._billing_mode = None
        self._busi_period = None
        self._business_mode = None
        self._config_name = None
        self._cost_id = None
        self._count = None
        self._country_area = None
        self._coupon_amount = None
        self._currency = None
        self._daily_amortized_coupon_amount = None
        self._daily_amortized_discount_bill_amount = None
        self._daily_amortized_original_bill_amount = None
        self._daily_amortized_paid_amount = None
        self._daily_amortized_payable_amount = None
        self._daily_amortized_pre_tax_payable_amount = None
        self._daily_amortized_pre_tax_real_value = None
        self._daily_amortized_preferential_bill_amount = None
        self._daily_amortized_real_value = None
        self._daily_amortized_round_amount = None
        self._daily_amortized_settle_payable_amount = None
        self._daily_amortized_settle_pre_tax_payable_amount = None
        self._daily_amortized_settle_pre_tax_real_value = None
        self._daily_amortized_settle_real_value = None
        self._daily_amortized_settle_tax_amount = None
        self._daily_amortized_tax_amount = None
        self._deduction_use_duration = None
        self._discount_bill_amount = None
        self._discount_biz_billing_function = None
        self._discount_biz_measure_interval = None
        self._discount_biz_unit_price = None
        self._discount_biz_unit_price_interval = None
        self._effective_factor = None
        self._element = None
        self._exchange_rate = None
        self._expand_field = None
        self._expense_begin_time = None
        self._expense_end_time = None
        self._factor = None
        self._instance_name = None
        self._instance_no = None
        self._market_price = None
        self._measure_interval = None
        self._original_bill_amount = None
        self._owner_customer_name = None
        self._owner_id = None
        self._owner_user_name = None
        self._paid_amount = None
        self._payable_amount = None
        self._payer_customer_name = None
        self._payer_id = None
        self._payer_user_name = None
        self._pre_tax_payable_amount = None
        self._pre_tax_real_value = None
        self._preferential_bill_amount = None
        self._price = None
        self._price_interval = None
        self._price_unit = None
        self._product = None
        self._product_zh = None
        self._project = None
        self._project_display_name = None
        self._real_value = None
        self._region = None
        self._round_amount = None
        self._seller_customer_name = None
        self._seller_id = None
        self._seller_user_name = None
        self._settle_payable_amount = None
        self._settle_pre_tax_payable_amount = None
        self._settle_pre_tax_real_value = None
        self._settle_real_value = None
        self._settle_tax_amount = None
        self._split_item_id = None
        self._split_item_name = None
        self._subject_name = None
        self._tag = None
        self._tax_amount = None
        self._tax_rate = None
        self._trade_time = None
        self._unit = None
        self._use_duration = None
        self._use_duration_unit = None
        self._zone = None
        self.discriminator = None

        if amortized_begin_time is not None:
            self.amortized_begin_time = amortized_begin_time
        if amortized_day is not None:
            self.amortized_day = amortized_day
        if amortized_end_time is not None:
            self.amortized_end_time = amortized_end_time
        if amortized_month is not None:
            self.amortized_month = amortized_month
        if amortized_type is not None:
            self.amortized_type = amortized_type
        if bill_category is not None:
            self.bill_category = bill_category
        if bill_id is not None:
            self.bill_id = bill_id
        if bill_period is not None:
            self.bill_period = bill_period
        if billing_function is not None:
            self.billing_function = billing_function
        if billing_method_code is not None:
            self.billing_method_code = billing_method_code
        if billing_mode is not None:
            self.billing_mode = billing_mode
        if busi_period is not None:
            self.busi_period = busi_period
        if business_mode is not None:
            self.business_mode = business_mode
        if config_name is not None:
            self.config_name = config_name
        if cost_id is not None:
            self.cost_id = cost_id
        if count is not None:
            self.count = count
        if country_area is not None:
            self.country_area = country_area
        if coupon_amount is not None:
            self.coupon_amount = coupon_amount
        if currency is not None:
            self.currency = currency
        if daily_amortized_coupon_amount is not None:
            self.daily_amortized_coupon_amount = daily_amortized_coupon_amount
        if daily_amortized_discount_bill_amount is not None:
            self.daily_amortized_discount_bill_amount = daily_amortized_discount_bill_amount
        if daily_amortized_original_bill_amount is not None:
            self.daily_amortized_original_bill_amount = daily_amortized_original_bill_amount
        if daily_amortized_paid_amount is not None:
            self.daily_amortized_paid_amount = daily_amortized_paid_amount
        if daily_amortized_payable_amount is not None:
            self.daily_amortized_payable_amount = daily_amortized_payable_amount
        if daily_amortized_pre_tax_payable_amount is not None:
            self.daily_amortized_pre_tax_payable_amount = daily_amortized_pre_tax_payable_amount
        if daily_amortized_pre_tax_real_value is not None:
            self.daily_amortized_pre_tax_real_value = daily_amortized_pre_tax_real_value
        if daily_amortized_preferential_bill_amount is not None:
            self.daily_amortized_preferential_bill_amount = daily_amortized_preferential_bill_amount
        if daily_amortized_real_value is not None:
            self.daily_amortized_real_value = daily_amortized_real_value
        if daily_amortized_round_amount is not None:
            self.daily_amortized_round_amount = daily_amortized_round_amount
        if daily_amortized_settle_payable_amount is not None:
            self.daily_amortized_settle_payable_amount = daily_amortized_settle_payable_amount
        if daily_amortized_settle_pre_tax_payable_amount is not None:
            self.daily_amortized_settle_pre_tax_payable_amount = daily_amortized_settle_pre_tax_payable_amount
        if daily_amortized_settle_pre_tax_real_value is not None:
            self.daily_amortized_settle_pre_tax_real_value = daily_amortized_settle_pre_tax_real_value
        if daily_amortized_settle_real_value is not None:
            self.daily_amortized_settle_real_value = daily_amortized_settle_real_value
        if daily_amortized_settle_tax_amount is not None:
            self.daily_amortized_settle_tax_amount = daily_amortized_settle_tax_amount
        if daily_amortized_tax_amount is not None:
            self.daily_amortized_tax_amount = daily_amortized_tax_amount
        if deduction_use_duration is not None:
            self.deduction_use_duration = deduction_use_duration
        if discount_bill_amount is not None:
            self.discount_bill_amount = discount_bill_amount
        if discount_biz_billing_function is not None:
            self.discount_biz_billing_function = discount_biz_billing_function
        if discount_biz_measure_interval is not None:
            self.discount_biz_measure_interval = discount_biz_measure_interval
        if discount_biz_unit_price is not None:
            self.discount_biz_unit_price = discount_biz_unit_price
        if discount_biz_unit_price_interval is not None:
            self.discount_biz_unit_price_interval = discount_biz_unit_price_interval
        if effective_factor is not None:
            self.effective_factor = effective_factor
        if element is not None:
            self.element = element
        if exchange_rate is not None:
            self.exchange_rate = exchange_rate
        if expand_field is not None:
            self.expand_field = expand_field
        if expense_begin_time is not None:
            self.expense_begin_time = expense_begin_time
        if expense_end_time is not None:
            self.expense_end_time = expense_end_time
        if factor is not None:
            self.factor = factor
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if market_price is not None:
            self.market_price = market_price
        if measure_interval is not None:
            self.measure_interval = measure_interval
        if original_bill_amount is not None:
            self.original_bill_amount = original_bill_amount
        if owner_customer_name is not None:
            self.owner_customer_name = owner_customer_name
        if owner_id is not None:
            self.owner_id = owner_id
        if owner_user_name is not None:
            self.owner_user_name = owner_user_name
        if paid_amount is not None:
            self.paid_amount = paid_amount
        if payable_amount is not None:
            self.payable_amount = payable_amount
        if payer_customer_name is not None:
            self.payer_customer_name = payer_customer_name
        if payer_id is not None:
            self.payer_id = payer_id
        if payer_user_name is not None:
            self.payer_user_name = payer_user_name
        if pre_tax_payable_amount is not None:
            self.pre_tax_payable_amount = pre_tax_payable_amount
        if pre_tax_real_value is not None:
            self.pre_tax_real_value = pre_tax_real_value
        if preferential_bill_amount is not None:
            self.preferential_bill_amount = preferential_bill_amount
        if price is not None:
            self.price = price
        if price_interval is not None:
            self.price_interval = price_interval
        if price_unit is not None:
            self.price_unit = price_unit
        if product is not None:
            self.product = product
        if product_zh is not None:
            self.product_zh = product_zh
        if project is not None:
            self.project = project
        if project_display_name is not None:
            self.project_display_name = project_display_name
        if real_value is not None:
            self.real_value = real_value
        if region is not None:
            self.region = region
        if round_amount is not None:
            self.round_amount = round_amount
        if seller_customer_name is not None:
            self.seller_customer_name = seller_customer_name
        if seller_id is not None:
            self.seller_id = seller_id
        if seller_user_name is not None:
            self.seller_user_name = seller_user_name
        if settle_payable_amount is not None:
            self.settle_payable_amount = settle_payable_amount
        if settle_pre_tax_payable_amount is not None:
            self.settle_pre_tax_payable_amount = settle_pre_tax_payable_amount
        if settle_pre_tax_real_value is not None:
            self.settle_pre_tax_real_value = settle_pre_tax_real_value
        if settle_real_value is not None:
            self.settle_real_value = settle_real_value
        if settle_tax_amount is not None:
            self.settle_tax_amount = settle_tax_amount
        if split_item_id is not None:
            self.split_item_id = split_item_id
        if split_item_name is not None:
            self.split_item_name = split_item_name
        if subject_name is not None:
            self.subject_name = subject_name
        if tag is not None:
            self.tag = tag
        if tax_amount is not None:
            self.tax_amount = tax_amount
        if tax_rate is not None:
            self.tax_rate = tax_rate
        if trade_time is not None:
            self.trade_time = trade_time
        if unit is not None:
            self.unit = unit
        if use_duration is not None:
            self.use_duration = use_duration
        if use_duration_unit is not None:
            self.use_duration_unit = use_duration_unit
        if zone is not None:
            self.zone = zone

    @property
    def amortized_begin_time(self):
        """Gets the amortized_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The amortized_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_begin_time

    @amortized_begin_time.setter
    def amortized_begin_time(self, amortized_begin_time):
        """Sets the amortized_begin_time of this ListForListAmortizedCostBillDetailOutput.


        :param amortized_begin_time: The amortized_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._amortized_begin_time = amortized_begin_time

    @property
    def amortized_day(self):
        """Gets the amortized_day of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The amortized_day of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_day

    @amortized_day.setter
    def amortized_day(self, amortized_day):
        """Sets the amortized_day of this ListForListAmortizedCostBillDetailOutput.


        :param amortized_day: The amortized_day of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._amortized_day = amortized_day

    @property
    def amortized_end_time(self):
        """Gets the amortized_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The amortized_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_end_time

    @amortized_end_time.setter
    def amortized_end_time(self, amortized_end_time):
        """Sets the amortized_end_time of this ListForListAmortizedCostBillDetailOutput.


        :param amortized_end_time: The amortized_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._amortized_end_time = amortized_end_time

    @property
    def amortized_month(self):
        """Gets the amortized_month of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The amortized_month of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_month

    @amortized_month.setter
    def amortized_month(self, amortized_month):
        """Sets the amortized_month of this ListForListAmortizedCostBillDetailOutput.


        :param amortized_month: The amortized_month of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._amortized_month = amortized_month

    @property
    def amortized_type(self):
        """Gets the amortized_type of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The amortized_type of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._amortized_type

    @amortized_type.setter
    def amortized_type(self, amortized_type):
        """Sets the amortized_type of this ListForListAmortizedCostBillDetailOutput.


        :param amortized_type: The amortized_type of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._amortized_type = amortized_type

    @property
    def bill_category(self):
        """Gets the bill_category of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The bill_category of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_category

    @bill_category.setter
    def bill_category(self, bill_category):
        """Sets the bill_category of this ListForListAmortizedCostBillDetailOutput.


        :param bill_category: The bill_category of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_category = bill_category

    @property
    def bill_id(self):
        """Gets the bill_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The bill_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_id

    @bill_id.setter
    def bill_id(self, bill_id):
        """Sets the bill_id of this ListForListAmortizedCostBillDetailOutput.


        :param bill_id: The bill_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_id = bill_id

    @property
    def bill_period(self):
        """Gets the bill_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The bill_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_period

    @bill_period.setter
    def bill_period(self, bill_period):
        """Sets the bill_period of this ListForListAmortizedCostBillDetailOutput.


        :param bill_period: The bill_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._bill_period = bill_period

    @property
    def billing_function(self):
        """Gets the billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_function

    @billing_function.setter
    def billing_function(self, billing_function):
        """Sets the billing_function of this ListForListAmortizedCostBillDetailOutput.


        :param billing_function: The billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_function = billing_function

    @property
    def billing_method_code(self):
        """Gets the billing_method_code of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The billing_method_code of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_method_code

    @billing_method_code.setter
    def billing_method_code(self, billing_method_code):
        """Sets the billing_method_code of this ListForListAmortizedCostBillDetailOutput.


        :param billing_method_code: The billing_method_code of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_method_code = billing_method_code

    @property
    def billing_mode(self):
        """Gets the billing_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The billing_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this ListForListAmortizedCostBillDetailOutput.


        :param billing_mode: The billing_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._billing_mode = billing_mode

    @property
    def busi_period(self):
        """Gets the busi_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The busi_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._busi_period

    @busi_period.setter
    def busi_period(self, busi_period):
        """Sets the busi_period of this ListForListAmortizedCostBillDetailOutput.


        :param busi_period: The busi_period of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._busi_period = busi_period

    @property
    def business_mode(self):
        """Gets the business_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The business_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_mode

    @business_mode.setter
    def business_mode(self, business_mode):
        """Sets the business_mode of this ListForListAmortizedCostBillDetailOutput.


        :param business_mode: The business_mode of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._business_mode = business_mode

    @property
    def config_name(self):
        """Gets the config_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The config_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this ListForListAmortizedCostBillDetailOutput.


        :param config_name: The config_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def cost_id(self):
        """Gets the cost_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The cost_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._cost_id

    @cost_id.setter
    def cost_id(self, cost_id):
        """Sets the cost_id of this ListForListAmortizedCostBillDetailOutput.


        :param cost_id: The cost_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._cost_id = cost_id

    @property
    def count(self):
        """Gets the count of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The count of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ListForListAmortizedCostBillDetailOutput.


        :param count: The count of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._count = count

    @property
    def country_area(self):
        """Gets the country_area of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The country_area of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._country_area

    @country_area.setter
    def country_area(self, country_area):
        """Sets the country_area of this ListForListAmortizedCostBillDetailOutput.


        :param country_area: The country_area of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._country_area = country_area

    @property
    def coupon_amount(self):
        """Gets the coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._coupon_amount

    @coupon_amount.setter
    def coupon_amount(self, coupon_amount):
        """Sets the coupon_amount of this ListForListAmortizedCostBillDetailOutput.


        :param coupon_amount: The coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._coupon_amount = coupon_amount

    @property
    def currency(self):
        """Gets the currency of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The currency of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this ListForListAmortizedCostBillDetailOutput.


        :param currency: The currency of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def daily_amortized_coupon_amount(self):
        """Gets the daily_amortized_coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_coupon_amount

    @daily_amortized_coupon_amount.setter
    def daily_amortized_coupon_amount(self, daily_amortized_coupon_amount):
        """Sets the daily_amortized_coupon_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_coupon_amount: The daily_amortized_coupon_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_coupon_amount = daily_amortized_coupon_amount

    @property
    def daily_amortized_discount_bill_amount(self):
        """Gets the daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_discount_bill_amount

    @daily_amortized_discount_bill_amount.setter
    def daily_amortized_discount_bill_amount(self, daily_amortized_discount_bill_amount):
        """Sets the daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_discount_bill_amount: The daily_amortized_discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_discount_bill_amount = daily_amortized_discount_bill_amount

    @property
    def daily_amortized_original_bill_amount(self):
        """Gets the daily_amortized_original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_original_bill_amount

    @daily_amortized_original_bill_amount.setter
    def daily_amortized_original_bill_amount(self, daily_amortized_original_bill_amount):
        """Sets the daily_amortized_original_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_original_bill_amount: The daily_amortized_original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_original_bill_amount = daily_amortized_original_bill_amount

    @property
    def daily_amortized_paid_amount(self):
        """Gets the daily_amortized_paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_paid_amount

    @daily_amortized_paid_amount.setter
    def daily_amortized_paid_amount(self, daily_amortized_paid_amount):
        """Sets the daily_amortized_paid_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_paid_amount: The daily_amortized_paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_paid_amount = daily_amortized_paid_amount

    @property
    def daily_amortized_payable_amount(self):
        """Gets the daily_amortized_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_payable_amount

    @daily_amortized_payable_amount.setter
    def daily_amortized_payable_amount(self, daily_amortized_payable_amount):
        """Sets the daily_amortized_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_payable_amount: The daily_amortized_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_payable_amount = daily_amortized_payable_amount

    @property
    def daily_amortized_pre_tax_payable_amount(self):
        """Gets the daily_amortized_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_pre_tax_payable_amount

    @daily_amortized_pre_tax_payable_amount.setter
    def daily_amortized_pre_tax_payable_amount(self, daily_amortized_pre_tax_payable_amount):
        """Sets the daily_amortized_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_pre_tax_payable_amount: The daily_amortized_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_pre_tax_payable_amount = daily_amortized_pre_tax_payable_amount

    @property
    def daily_amortized_pre_tax_real_value(self):
        """Gets the daily_amortized_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_pre_tax_real_value

    @daily_amortized_pre_tax_real_value.setter
    def daily_amortized_pre_tax_real_value(self, daily_amortized_pre_tax_real_value):
        """Sets the daily_amortized_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_pre_tax_real_value: The daily_amortized_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_pre_tax_real_value = daily_amortized_pre_tax_real_value

    @property
    def daily_amortized_preferential_bill_amount(self):
        """Gets the daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_preferential_bill_amount

    @daily_amortized_preferential_bill_amount.setter
    def daily_amortized_preferential_bill_amount(self, daily_amortized_preferential_bill_amount):
        """Sets the daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_preferential_bill_amount: The daily_amortized_preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_preferential_bill_amount = daily_amortized_preferential_bill_amount

    @property
    def daily_amortized_real_value(self):
        """Gets the daily_amortized_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_real_value

    @daily_amortized_real_value.setter
    def daily_amortized_real_value(self, daily_amortized_real_value):
        """Sets the daily_amortized_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_real_value: The daily_amortized_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_real_value = daily_amortized_real_value

    @property
    def daily_amortized_round_amount(self):
        """Gets the daily_amortized_round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_round_amount

    @daily_amortized_round_amount.setter
    def daily_amortized_round_amount(self, daily_amortized_round_amount):
        """Sets the daily_amortized_round_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_round_amount: The daily_amortized_round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_round_amount = daily_amortized_round_amount

    @property
    def daily_amortized_settle_payable_amount(self):
        """Gets the daily_amortized_settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_settle_payable_amount

    @daily_amortized_settle_payable_amount.setter
    def daily_amortized_settle_payable_amount(self, daily_amortized_settle_payable_amount):
        """Sets the daily_amortized_settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_settle_payable_amount: The daily_amortized_settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_settle_payable_amount = daily_amortized_settle_payable_amount

    @property
    def daily_amortized_settle_pre_tax_payable_amount(self):
        """Gets the daily_amortized_settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_settle_pre_tax_payable_amount

    @daily_amortized_settle_pre_tax_payable_amount.setter
    def daily_amortized_settle_pre_tax_payable_amount(self, daily_amortized_settle_pre_tax_payable_amount):
        """Sets the daily_amortized_settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_settle_pre_tax_payable_amount: The daily_amortized_settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_settle_pre_tax_payable_amount = daily_amortized_settle_pre_tax_payable_amount

    @property
    def daily_amortized_settle_pre_tax_real_value(self):
        """Gets the daily_amortized_settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_settle_pre_tax_real_value

    @daily_amortized_settle_pre_tax_real_value.setter
    def daily_amortized_settle_pre_tax_real_value(self, daily_amortized_settle_pre_tax_real_value):
        """Sets the daily_amortized_settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_settle_pre_tax_real_value: The daily_amortized_settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_settle_pre_tax_real_value = daily_amortized_settle_pre_tax_real_value

    @property
    def daily_amortized_settle_real_value(self):
        """Gets the daily_amortized_settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_settle_real_value

    @daily_amortized_settle_real_value.setter
    def daily_amortized_settle_real_value(self, daily_amortized_settle_real_value):
        """Sets the daily_amortized_settle_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_settle_real_value: The daily_amortized_settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_settle_real_value = daily_amortized_settle_real_value

    @property
    def daily_amortized_settle_tax_amount(self):
        """Gets the daily_amortized_settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_settle_tax_amount

    @daily_amortized_settle_tax_amount.setter
    def daily_amortized_settle_tax_amount(self, daily_amortized_settle_tax_amount):
        """Sets the daily_amortized_settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_settle_tax_amount: The daily_amortized_settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_settle_tax_amount = daily_amortized_settle_tax_amount

    @property
    def daily_amortized_tax_amount(self):
        """Gets the daily_amortized_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The daily_amortized_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._daily_amortized_tax_amount

    @daily_amortized_tax_amount.setter
    def daily_amortized_tax_amount(self, daily_amortized_tax_amount):
        """Sets the daily_amortized_tax_amount of this ListForListAmortizedCostBillDetailOutput.


        :param daily_amortized_tax_amount: The daily_amortized_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._daily_amortized_tax_amount = daily_amortized_tax_amount

    @property
    def deduction_use_duration(self):
        """Gets the deduction_use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The deduction_use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._deduction_use_duration

    @deduction_use_duration.setter
    def deduction_use_duration(self, deduction_use_duration):
        """Sets the deduction_use_duration of this ListForListAmortizedCostBillDetailOutput.


        :param deduction_use_duration: The deduction_use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._deduction_use_duration = deduction_use_duration

    @property
    def discount_bill_amount(self):
        """Gets the discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_bill_amount

    @discount_bill_amount.setter
    def discount_bill_amount(self, discount_bill_amount):
        """Sets the discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param discount_bill_amount: The discount_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_bill_amount = discount_bill_amount

    @property
    def discount_biz_billing_function(self):
        """Gets the discount_biz_billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The discount_biz_billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_billing_function

    @discount_biz_billing_function.setter
    def discount_biz_billing_function(self, discount_biz_billing_function):
        """Sets the discount_biz_billing_function of this ListForListAmortizedCostBillDetailOutput.


        :param discount_biz_billing_function: The discount_biz_billing_function of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_billing_function = discount_biz_billing_function

    @property
    def discount_biz_measure_interval(self):
        """Gets the discount_biz_measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The discount_biz_measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_measure_interval

    @discount_biz_measure_interval.setter
    def discount_biz_measure_interval(self, discount_biz_measure_interval):
        """Sets the discount_biz_measure_interval of this ListForListAmortizedCostBillDetailOutput.


        :param discount_biz_measure_interval: The discount_biz_measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_measure_interval = discount_biz_measure_interval

    @property
    def discount_biz_unit_price(self):
        """Gets the discount_biz_unit_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The discount_biz_unit_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price

    @discount_biz_unit_price.setter
    def discount_biz_unit_price(self, discount_biz_unit_price):
        """Sets the discount_biz_unit_price of this ListForListAmortizedCostBillDetailOutput.


        :param discount_biz_unit_price: The discount_biz_unit_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price = discount_biz_unit_price

    @property
    def discount_biz_unit_price_interval(self):
        """Gets the discount_biz_unit_price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The discount_biz_unit_price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_biz_unit_price_interval

    @discount_biz_unit_price_interval.setter
    def discount_biz_unit_price_interval(self, discount_biz_unit_price_interval):
        """Sets the discount_biz_unit_price_interval of this ListForListAmortizedCostBillDetailOutput.


        :param discount_biz_unit_price_interval: The discount_biz_unit_price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._discount_biz_unit_price_interval = discount_biz_unit_price_interval

    @property
    def effective_factor(self):
        """Gets the effective_factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The effective_factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._effective_factor

    @effective_factor.setter
    def effective_factor(self, effective_factor):
        """Sets the effective_factor of this ListForListAmortizedCostBillDetailOutput.


        :param effective_factor: The effective_factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._effective_factor = effective_factor

    @property
    def element(self):
        """Gets the element of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The element of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._element

    @element.setter
    def element(self, element):
        """Sets the element of this ListForListAmortizedCostBillDetailOutput.


        :param element: The element of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._element = element

    @property
    def exchange_rate(self):
        """Gets the exchange_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The exchange_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._exchange_rate

    @exchange_rate.setter
    def exchange_rate(self, exchange_rate):
        """Sets the exchange_rate of this ListForListAmortizedCostBillDetailOutput.


        :param exchange_rate: The exchange_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._exchange_rate = exchange_rate

    @property
    def expand_field(self):
        """Gets the expand_field of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The expand_field of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expand_field

    @expand_field.setter
    def expand_field(self, expand_field):
        """Sets the expand_field of this ListForListAmortizedCostBillDetailOutput.


        :param expand_field: The expand_field of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expand_field = expand_field

    @property
    def expense_begin_time(self):
        """Gets the expense_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The expense_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_begin_time

    @expense_begin_time.setter
    def expense_begin_time(self, expense_begin_time):
        """Sets the expense_begin_time of this ListForListAmortizedCostBillDetailOutput.


        :param expense_begin_time: The expense_begin_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expense_begin_time = expense_begin_time

    @property
    def expense_end_time(self):
        """Gets the expense_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The expense_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_end_time

    @expense_end_time.setter
    def expense_end_time(self, expense_end_time):
        """Sets the expense_end_time of this ListForListAmortizedCostBillDetailOutput.


        :param expense_end_time: The expense_end_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._expense_end_time = expense_end_time

    @property
    def factor(self):
        """Gets the factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._factor

    @factor.setter
    def factor(self, factor):
        """Sets the factor of this ListForListAmortizedCostBillDetailOutput.


        :param factor: The factor of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._factor = factor

    @property
    def instance_name(self):
        """Gets the instance_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The instance_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this ListForListAmortizedCostBillDetailOutput.


        :param instance_name: The instance_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The instance_no of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForListAmortizedCostBillDetailOutput.


        :param instance_no: The instance_no of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def market_price(self):
        """Gets the market_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The market_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._market_price

    @market_price.setter
    def market_price(self, market_price):
        """Sets the market_price of this ListForListAmortizedCostBillDetailOutput.


        :param market_price: The market_price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._market_price = market_price

    @property
    def measure_interval(self):
        """Gets the measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._measure_interval

    @measure_interval.setter
    def measure_interval(self, measure_interval):
        """Sets the measure_interval of this ListForListAmortizedCostBillDetailOutput.


        :param measure_interval: The measure_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._measure_interval = measure_interval

    @property
    def original_bill_amount(self):
        """Gets the original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_bill_amount

    @original_bill_amount.setter
    def original_bill_amount(self, original_bill_amount):
        """Sets the original_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param original_bill_amount: The original_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._original_bill_amount = original_bill_amount

    @property
    def owner_customer_name(self):
        """Gets the owner_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The owner_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_customer_name

    @owner_customer_name.setter
    def owner_customer_name(self, owner_customer_name):
        """Sets the owner_customer_name of this ListForListAmortizedCostBillDetailOutput.


        :param owner_customer_name: The owner_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_customer_name = owner_customer_name

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The owner_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListAmortizedCostBillDetailOutput.


        :param owner_id: The owner_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def owner_user_name(self):
        """Gets the owner_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The owner_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_user_name

    @owner_user_name.setter
    def owner_user_name(self, owner_user_name):
        """Sets the owner_user_name of this ListForListAmortizedCostBillDetailOutput.


        :param owner_user_name: The owner_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._owner_user_name = owner_user_name

    @property
    def paid_amount(self):
        """Gets the paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_amount

    @paid_amount.setter
    def paid_amount(self, paid_amount):
        """Sets the paid_amount of this ListForListAmortizedCostBillDetailOutput.


        :param paid_amount: The paid_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._paid_amount = paid_amount

    @property
    def payable_amount(self):
        """Gets the payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payable_amount

    @payable_amount.setter
    def payable_amount(self, payable_amount):
        """Sets the payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param payable_amount: The payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payable_amount = payable_amount

    @property
    def payer_customer_name(self):
        """Gets the payer_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The payer_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_customer_name

    @payer_customer_name.setter
    def payer_customer_name(self, payer_customer_name):
        """Sets the payer_customer_name of this ListForListAmortizedCostBillDetailOutput.


        :param payer_customer_name: The payer_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_customer_name = payer_customer_name

    @property
    def payer_id(self):
        """Gets the payer_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The payer_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_id

    @payer_id.setter
    def payer_id(self, payer_id):
        """Sets the payer_id of this ListForListAmortizedCostBillDetailOutput.


        :param payer_id: The payer_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_id = payer_id

    @property
    def payer_user_name(self):
        """Gets the payer_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The payer_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_user_name

    @payer_user_name.setter
    def payer_user_name(self, payer_user_name):
        """Sets the payer_user_name of this ListForListAmortizedCostBillDetailOutput.


        :param payer_user_name: The payer_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._payer_user_name = payer_user_name

    @property
    def pre_tax_payable_amount(self):
        """Gets the pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._pre_tax_payable_amount

    @pre_tax_payable_amount.setter
    def pre_tax_payable_amount(self, pre_tax_payable_amount):
        """Sets the pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param pre_tax_payable_amount: The pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._pre_tax_payable_amount = pre_tax_payable_amount

    @property
    def pre_tax_real_value(self):
        """Gets the pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._pre_tax_real_value

    @pre_tax_real_value.setter
    def pre_tax_real_value(self, pre_tax_real_value):
        """Sets the pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param pre_tax_real_value: The pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._pre_tax_real_value = pre_tax_real_value

    @property
    def preferential_bill_amount(self):
        """Gets the preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._preferential_bill_amount

    @preferential_bill_amount.setter
    def preferential_bill_amount(self, preferential_bill_amount):
        """Sets the preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.


        :param preferential_bill_amount: The preferential_bill_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._preferential_bill_amount = preferential_bill_amount

    @property
    def price(self):
        """Gets the price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price

    @price.setter
    def price(self, price):
        """Sets the price of this ListForListAmortizedCostBillDetailOutput.


        :param price: The price of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price = price

    @property
    def price_interval(self):
        """Gets the price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_interval

    @price_interval.setter
    def price_interval(self, price_interval):
        """Sets the price_interval of this ListForListAmortizedCostBillDetailOutput.


        :param price_interval: The price_interval of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price_interval = price_interval

    @property
    def price_unit(self):
        """Gets the price_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The price_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._price_unit

    @price_unit.setter
    def price_unit(self, price_unit):
        """Sets the price_unit of this ListForListAmortizedCostBillDetailOutput.


        :param price_unit: The price_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._price_unit = price_unit

    @property
    def product(self):
        """Gets the product of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The product of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListAmortizedCostBillDetailOutput.


        :param product: The product of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_zh(self):
        """Gets the product_zh of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The product_zh of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_zh

    @product_zh.setter
    def product_zh(self, product_zh):
        """Sets the product_zh of this ListForListAmortizedCostBillDetailOutput.


        :param product_zh: The product_zh of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._product_zh = product_zh

    @property
    def project(self):
        """Gets the project of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The project of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ListForListAmortizedCostBillDetailOutput.


        :param project: The project of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def project_display_name(self):
        """Gets the project_display_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The project_display_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_display_name

    @project_display_name.setter
    def project_display_name(self, project_display_name):
        """Sets the project_display_name of this ListForListAmortizedCostBillDetailOutput.


        :param project_display_name: The project_display_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_display_name = project_display_name

    @property
    def real_value(self):
        """Gets the real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._real_value

    @real_value.setter
    def real_value(self, real_value):
        """Sets the real_value of this ListForListAmortizedCostBillDetailOutput.


        :param real_value: The real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._real_value = real_value

    @property
    def region(self):
        """Gets the region of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The region of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListForListAmortizedCostBillDetailOutput.


        :param region: The region of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def round_amount(self):
        """Gets the round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._round_amount

    @round_amount.setter
    def round_amount(self, round_amount):
        """Sets the round_amount of this ListForListAmortizedCostBillDetailOutput.


        :param round_amount: The round_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._round_amount = round_amount

    @property
    def seller_customer_name(self):
        """Gets the seller_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The seller_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_customer_name

    @seller_customer_name.setter
    def seller_customer_name(self, seller_customer_name):
        """Sets the seller_customer_name of this ListForListAmortizedCostBillDetailOutput.


        :param seller_customer_name: The seller_customer_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_customer_name = seller_customer_name

    @property
    def seller_id(self):
        """Gets the seller_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The seller_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this ListForListAmortizedCostBillDetailOutput.


        :param seller_id: The seller_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def seller_user_name(self):
        """Gets the seller_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The seller_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_user_name

    @seller_user_name.setter
    def seller_user_name(self, seller_user_name):
        """Sets the seller_user_name of this ListForListAmortizedCostBillDetailOutput.


        :param seller_user_name: The seller_user_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._seller_user_name = seller_user_name

    @property
    def settle_payable_amount(self):
        """Gets the settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_payable_amount

    @settle_payable_amount.setter
    def settle_payable_amount(self, settle_payable_amount):
        """Sets the settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param settle_payable_amount: The settle_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_payable_amount = settle_payable_amount

    @property
    def settle_pre_tax_payable_amount(self):
        """Gets the settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pre_tax_payable_amount

    @settle_pre_tax_payable_amount.setter
    def settle_pre_tax_payable_amount(self, settle_pre_tax_payable_amount):
        """Sets the settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.


        :param settle_pre_tax_payable_amount: The settle_pre_tax_payable_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_pre_tax_payable_amount = settle_pre_tax_payable_amount

    @property
    def settle_pre_tax_real_value(self):
        """Gets the settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pre_tax_real_value

    @settle_pre_tax_real_value.setter
    def settle_pre_tax_real_value(self, settle_pre_tax_real_value):
        """Sets the settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param settle_pre_tax_real_value: The settle_pre_tax_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_pre_tax_real_value = settle_pre_tax_real_value

    @property
    def settle_real_value(self):
        """Gets the settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_real_value

    @settle_real_value.setter
    def settle_real_value(self, settle_real_value):
        """Sets the settle_real_value of this ListForListAmortizedCostBillDetailOutput.


        :param settle_real_value: The settle_real_value of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_real_value = settle_real_value

    @property
    def settle_tax_amount(self):
        """Gets the settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_tax_amount

    @settle_tax_amount.setter
    def settle_tax_amount(self, settle_tax_amount):
        """Sets the settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.


        :param settle_tax_amount: The settle_tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._settle_tax_amount = settle_tax_amount

    @property
    def split_item_id(self):
        """Gets the split_item_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The split_item_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._split_item_id

    @split_item_id.setter
    def split_item_id(self, split_item_id):
        """Sets the split_item_id of this ListForListAmortizedCostBillDetailOutput.


        :param split_item_id: The split_item_id of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._split_item_id = split_item_id

    @property
    def split_item_name(self):
        """Gets the split_item_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The split_item_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._split_item_name

    @split_item_name.setter
    def split_item_name(self, split_item_name):
        """Sets the split_item_name of this ListForListAmortizedCostBillDetailOutput.


        :param split_item_name: The split_item_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._split_item_name = split_item_name

    @property
    def subject_name(self):
        """Gets the subject_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The subject_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_name

    @subject_name.setter
    def subject_name(self, subject_name):
        """Sets the subject_name of this ListForListAmortizedCostBillDetailOutput.


        :param subject_name: The subject_name of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._subject_name = subject_name

    @property
    def tag(self):
        """Gets the tag of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The tag of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ListForListAmortizedCostBillDetailOutput.


        :param tag: The tag of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tax_amount(self):
        """Gets the tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tax_amount

    @tax_amount.setter
    def tax_amount(self, tax_amount):
        """Sets the tax_amount of this ListForListAmortizedCostBillDetailOutput.


        :param tax_amount: The tax_amount of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tax_amount = tax_amount

    @property
    def tax_rate(self):
        """Gets the tax_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The tax_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tax_rate

    @tax_rate.setter
    def tax_rate(self, tax_rate):
        """Sets the tax_rate of this ListForListAmortizedCostBillDetailOutput.


        :param tax_rate: The tax_rate of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._tax_rate = tax_rate

    @property
    def trade_time(self):
        """Gets the trade_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The trade_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._trade_time

    @trade_time.setter
    def trade_time(self, trade_time):
        """Sets the trade_time of this ListForListAmortizedCostBillDetailOutput.


        :param trade_time: The trade_time of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._trade_time = trade_time

    @property
    def unit(self):
        """Gets the unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ListForListAmortizedCostBillDetailOutput.


        :param unit: The unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    @property
    def use_duration(self):
        """Gets the use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration

    @use_duration.setter
    def use_duration(self, use_duration):
        """Sets the use_duration of this ListForListAmortizedCostBillDetailOutput.


        :param use_duration: The use_duration of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._use_duration = use_duration

    @property
    def use_duration_unit(self):
        """Gets the use_duration_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The use_duration_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_duration_unit

    @use_duration_unit.setter
    def use_duration_unit(self, use_duration_unit):
        """Sets the use_duration_unit of this ListForListAmortizedCostBillDetailOutput.


        :param use_duration_unit: The use_duration_unit of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._use_duration_unit = use_duration_unit

    @property
    def zone(self):
        """Gets the zone of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501


        :return: The zone of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this ListForListAmortizedCostBillDetailOutput.


        :param zone: The zone of this ListForListAmortizedCostBillDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListAmortizedCostBillDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListAmortizedCostBillDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListAmortizedCostBillDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
