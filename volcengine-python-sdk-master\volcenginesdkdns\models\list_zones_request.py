# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListZonesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'key': 'str',
        'order_key': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'search_mode': 'str',
        'search_order': 'str',
        'stage': 'str',
        'tag_filters': 'list[TagFilterForListZonesInput]',
        'trade_code': 'str'
    }

    attribute_map = {
        'key': 'Key',
        'order_key': 'OrderKey',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'search_mode': 'SearchMode',
        'search_order': 'SearchOrder',
        'stage': 'Stage',
        'tag_filters': 'TagFilters',
        'trade_code': 'TradeCode'
    }

    def __init__(self, key=None, order_key=None, page_number=None, page_size=None, project_name=None, search_mode=None, search_order=None, stage=None, tag_filters=None, trade_code=None, _configuration=None):  # noqa: E501
        """ListZonesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._key = None
        self._order_key = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._search_mode = None
        self._search_order = None
        self._stage = None
        self._tag_filters = None
        self._trade_code = None
        self.discriminator = None

        if key is not None:
            self.key = key
        if order_key is not None:
            self.order_key = order_key
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if search_mode is not None:
            self.search_mode = search_mode
        if search_order is not None:
            self.search_order = search_order
        if stage is not None:
            self.stage = stage
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if trade_code is not None:
            self.trade_code = trade_code

    @property
    def key(self):
        """Gets the key of this ListZonesRequest.  # noqa: E501


        :return: The key of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this ListZonesRequest.


        :param key: The key of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def order_key(self):
        """Gets the order_key of this ListZonesRequest.  # noqa: E501


        :return: The order_key of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_key

    @order_key.setter
    def order_key(self, order_key):
        """Sets the order_key of this ListZonesRequest.


        :param order_key: The order_key of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._order_key = order_key

    @property
    def page_number(self):
        """Gets the page_number of this ListZonesRequest.  # noqa: E501


        :return: The page_number of this ListZonesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListZonesRequest.


        :param page_number: The page_number of this ListZonesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListZonesRequest.  # noqa: E501


        :return: The page_size of this ListZonesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListZonesRequest.


        :param page_size: The page_size of this ListZonesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListZonesRequest.  # noqa: E501


        :return: The project_name of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListZonesRequest.


        :param project_name: The project_name of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def search_mode(self):
        """Gets the search_mode of this ListZonesRequest.  # noqa: E501


        :return: The search_mode of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_mode

    @search_mode.setter
    def search_mode(self, search_mode):
        """Sets the search_mode of this ListZonesRequest.


        :param search_mode: The search_mode of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._search_mode = search_mode

    @property
    def search_order(self):
        """Gets the search_order of this ListZonesRequest.  # noqa: E501


        :return: The search_order of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_order

    @search_order.setter
    def search_order(self, search_order):
        """Sets the search_order of this ListZonesRequest.


        :param search_order: The search_order of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._search_order = search_order

    @property
    def stage(self):
        """Gets the stage of this ListZonesRequest.  # noqa: E501


        :return: The stage of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._stage

    @stage.setter
    def stage(self, stage):
        """Sets the stage of this ListZonesRequest.


        :param stage: The stage of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._stage = stage

    @property
    def tag_filters(self):
        """Gets the tag_filters of this ListZonesRequest.  # noqa: E501


        :return: The tag_filters of this ListZonesRequest.  # noqa: E501
        :rtype: list[TagFilterForListZonesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this ListZonesRequest.


        :param tag_filters: The tag_filters of this ListZonesRequest.  # noqa: E501
        :type: list[TagFilterForListZonesInput]
        """

        self._tag_filters = tag_filters

    @property
    def trade_code(self):
        """Gets the trade_code of this ListZonesRequest.  # noqa: E501


        :return: The trade_code of this ListZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._trade_code

    @trade_code.setter
    def trade_code(self, trade_code):
        """Sets the trade_code of this ListZonesRequest.


        :param trade_code: The trade_code of this ListZonesRequest.  # noqa: E501
        :type: str
        """

        self._trade_code = trade_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListZonesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListZonesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListZonesRequest):
            return True

        return self.to_dict() != other.to_dict()
