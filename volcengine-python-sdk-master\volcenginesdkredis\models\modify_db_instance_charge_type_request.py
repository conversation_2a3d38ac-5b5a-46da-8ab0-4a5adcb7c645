# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceChargeTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'charge_type': 'str',
        'client_token': 'str',
        'instance_ids': 'list[str]',
        'purchase_months': 'int'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'charge_type': 'ChargeType',
        'client_token': 'ClientToken',
        'instance_ids': 'InstanceIds',
        'purchase_months': 'PurchaseMonths'
    }

    def __init__(self, auto_renew=None, charge_type=None, client_token=None, instance_ids=None, purchase_months=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceChargeTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._charge_type = None
        self._client_token = None
        self._instance_ids = None
        self._purchase_months = None
        self.discriminator = None

        if auto_renew is not None:
            self.auto_renew = auto_renew
        self.charge_type = charge_type
        if client_token is not None:
            self.client_token = client_token
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if purchase_months is not None:
            self.purchase_months = purchase_months

    @property
    def auto_renew(self):
        """Gets the auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this ModifyDBInstanceChargeTypeRequest.


        :param auto_renew: The auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def charge_type(self):
        """Gets the charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ModifyDBInstanceChargeTypeRequest.


        :param charge_type: The charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def client_token(self):
        """Gets the client_token of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The client_token of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyDBInstanceChargeTypeRequest.


        :param client_token: The client_token of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_ids(self):
        """Gets the instance_ids of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The instance_ids of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this ModifyDBInstanceChargeTypeRequest.


        :param instance_ids: The instance_ids of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def purchase_months(self):
        """Gets the purchase_months of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The purchase_months of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._purchase_months

    @purchase_months.setter
    def purchase_months(self, purchase_months):
        """Sets the purchase_months of this ModifyDBInstanceChargeTypeRequest.


        :param purchase_months: The purchase_months of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: int
        """

        self._purchase_months = purchase_months

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceChargeTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceChargeTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceChargeTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
