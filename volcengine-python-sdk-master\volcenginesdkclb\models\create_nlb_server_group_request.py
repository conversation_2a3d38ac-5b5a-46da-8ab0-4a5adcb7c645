# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNLBServerGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'any_port_enabled': 'bool',
        'bypass_security_group_enabled': 'bool',
        'connection_drain_enabled': 'bool',
        'connection_drain_timeout': 'int',
        'description': 'str',
        'health_check': 'HealthCheckForCreateNLBServerGroupInput',
        'ip_address_version': 'str',
        'preserve_client_ip_enabled': 'bool',
        'project_name': 'str',
        'protocol': 'str',
        'proxy_protocol_type': 'str',
        'scheduler': 'str',
        'server_group_name': 'str',
        'servers': 'list[ServerForCreateNLBServerGroupInput]',
        'session_persistence_enabled': 'bool',
        'session_persistence_timeout': 'int',
        'tags': 'list[TagForCreateNLBServerGroupInput]',
        'timestamp_remove_enabled': 'bool',
        'type': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'any_port_enabled': 'AnyPortEnabled',
        'bypass_security_group_enabled': 'BypassSecurityGroupEnabled',
        'connection_drain_enabled': 'ConnectionDrainEnabled',
        'connection_drain_timeout': 'ConnectionDrainTimeout',
        'description': 'Description',
        'health_check': 'HealthCheck',
        'ip_address_version': 'IpAddressVersion',
        'preserve_client_ip_enabled': 'PreserveClientIpEnabled',
        'project_name': 'ProjectName',
        'protocol': 'Protocol',
        'proxy_protocol_type': 'ProxyProtocolType',
        'scheduler': 'Scheduler',
        'server_group_name': 'ServerGroupName',
        'servers': 'Servers',
        'session_persistence_enabled': 'SessionPersistenceEnabled',
        'session_persistence_timeout': 'SessionPersistenceTimeout',
        'tags': 'Tags',
        'timestamp_remove_enabled': 'TimestampRemoveEnabled',
        'type': 'Type',
        'vpc_id': 'VpcId'
    }

    def __init__(self, any_port_enabled=None, bypass_security_group_enabled=None, connection_drain_enabled=None, connection_drain_timeout=None, description=None, health_check=None, ip_address_version=None, preserve_client_ip_enabled=None, project_name=None, protocol=None, proxy_protocol_type=None, scheduler=None, server_group_name=None, servers=None, session_persistence_enabled=None, session_persistence_timeout=None, tags=None, timestamp_remove_enabled=None, type=None, vpc_id=None, _configuration=None):  # noqa: E501
        """CreateNLBServerGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._any_port_enabled = None
        self._bypass_security_group_enabled = None
        self._connection_drain_enabled = None
        self._connection_drain_timeout = None
        self._description = None
        self._health_check = None
        self._ip_address_version = None
        self._preserve_client_ip_enabled = None
        self._project_name = None
        self._protocol = None
        self._proxy_protocol_type = None
        self._scheduler = None
        self._server_group_name = None
        self._servers = None
        self._session_persistence_enabled = None
        self._session_persistence_timeout = None
        self._tags = None
        self._timestamp_remove_enabled = None
        self._type = None
        self._vpc_id = None
        self.discriminator = None

        if any_port_enabled is not None:
            self.any_port_enabled = any_port_enabled
        if bypass_security_group_enabled is not None:
            self.bypass_security_group_enabled = bypass_security_group_enabled
        if connection_drain_enabled is not None:
            self.connection_drain_enabled = connection_drain_enabled
        if connection_drain_timeout is not None:
            self.connection_drain_timeout = connection_drain_timeout
        if description is not None:
            self.description = description
        if health_check is not None:
            self.health_check = health_check
        if ip_address_version is not None:
            self.ip_address_version = ip_address_version
        if preserve_client_ip_enabled is not None:
            self.preserve_client_ip_enabled = preserve_client_ip_enabled
        if project_name is not None:
            self.project_name = project_name
        self.protocol = protocol
        if proxy_protocol_type is not None:
            self.proxy_protocol_type = proxy_protocol_type
        if scheduler is not None:
            self.scheduler = scheduler
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if servers is not None:
            self.servers = servers
        if session_persistence_enabled is not None:
            self.session_persistence_enabled = session_persistence_enabled
        if session_persistence_timeout is not None:
            self.session_persistence_timeout = session_persistence_timeout
        if tags is not None:
            self.tags = tags
        if timestamp_remove_enabled is not None:
            self.timestamp_remove_enabled = timestamp_remove_enabled
        if type is not None:
            self.type = type
        self.vpc_id = vpc_id

    @property
    def any_port_enabled(self):
        """Gets the any_port_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The any_port_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._any_port_enabled

    @any_port_enabled.setter
    def any_port_enabled(self, any_port_enabled):
        """Sets the any_port_enabled of this CreateNLBServerGroupRequest.


        :param any_port_enabled: The any_port_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._any_port_enabled = any_port_enabled

    @property
    def bypass_security_group_enabled(self):
        """Gets the bypass_security_group_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The bypass_security_group_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._bypass_security_group_enabled

    @bypass_security_group_enabled.setter
    def bypass_security_group_enabled(self, bypass_security_group_enabled):
        """Sets the bypass_security_group_enabled of this CreateNLBServerGroupRequest.


        :param bypass_security_group_enabled: The bypass_security_group_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._bypass_security_group_enabled = bypass_security_group_enabled

    @property
    def connection_drain_enabled(self):
        """Gets the connection_drain_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The connection_drain_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._connection_drain_enabled

    @connection_drain_enabled.setter
    def connection_drain_enabled(self, connection_drain_enabled):
        """Sets the connection_drain_enabled of this CreateNLBServerGroupRequest.


        :param connection_drain_enabled: The connection_drain_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._connection_drain_enabled = connection_drain_enabled

    @property
    def connection_drain_timeout(self):
        """Gets the connection_drain_timeout of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The connection_drain_timeout of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: int
        """
        return self._connection_drain_timeout

    @connection_drain_timeout.setter
    def connection_drain_timeout(self, connection_drain_timeout):
        """Sets the connection_drain_timeout of this CreateNLBServerGroupRequest.


        :param connection_drain_timeout: The connection_drain_timeout of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: int
        """

        self._connection_drain_timeout = connection_drain_timeout

    @property
    def description(self):
        """Gets the description of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The description of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNLBServerGroupRequest.


        :param description: The description of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def health_check(self):
        """Gets the health_check of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The health_check of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: HealthCheckForCreateNLBServerGroupInput
        """
        return self._health_check

    @health_check.setter
    def health_check(self, health_check):
        """Sets the health_check of this CreateNLBServerGroupRequest.


        :param health_check: The health_check of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: HealthCheckForCreateNLBServerGroupInput
        """

        self._health_check = health_check

    @property
    def ip_address_version(self):
        """Gets the ip_address_version of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The ip_address_version of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_version

    @ip_address_version.setter
    def ip_address_version(self, ip_address_version):
        """Sets the ip_address_version of this CreateNLBServerGroupRequest.


        :param ip_address_version: The ip_address_version of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._ip_address_version = ip_address_version

    @property
    def preserve_client_ip_enabled(self):
        """Gets the preserve_client_ip_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The preserve_client_ip_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._preserve_client_ip_enabled

    @preserve_client_ip_enabled.setter
    def preserve_client_ip_enabled(self, preserve_client_ip_enabled):
        """Sets the preserve_client_ip_enabled of this CreateNLBServerGroupRequest.


        :param preserve_client_ip_enabled: The preserve_client_ip_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._preserve_client_ip_enabled = preserve_client_ip_enabled

    @property
    def project_name(self):
        """Gets the project_name of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The project_name of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateNLBServerGroupRequest.


        :param project_name: The project_name of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def protocol(self):
        """Gets the protocol of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The protocol of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateNLBServerGroupRequest.


        :param protocol: The protocol of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protocol is None:
            raise ValueError("Invalid value for `protocol`, must not be `None`")  # noqa: E501

        self._protocol = protocol

    @property
    def proxy_protocol_type(self):
        """Gets the proxy_protocol_type of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The proxy_protocol_type of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_protocol_type

    @proxy_protocol_type.setter
    def proxy_protocol_type(self, proxy_protocol_type):
        """Sets the proxy_protocol_type of this CreateNLBServerGroupRequest.


        :param proxy_protocol_type: The proxy_protocol_type of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._proxy_protocol_type = proxy_protocol_type

    @property
    def scheduler(self):
        """Gets the scheduler of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The scheduler of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._scheduler

    @scheduler.setter
    def scheduler(self, scheduler):
        """Sets the scheduler of this CreateNLBServerGroupRequest.


        :param scheduler: The scheduler of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._scheduler = scheduler

    @property
    def server_group_name(self):
        """Gets the server_group_name of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The server_group_name of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this CreateNLBServerGroupRequest.


        :param server_group_name: The server_group_name of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def servers(self):
        """Gets the servers of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The servers of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: list[ServerForCreateNLBServerGroupInput]
        """
        return self._servers

    @servers.setter
    def servers(self, servers):
        """Sets the servers of this CreateNLBServerGroupRequest.


        :param servers: The servers of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: list[ServerForCreateNLBServerGroupInput]
        """

        self._servers = servers

    @property
    def session_persistence_enabled(self):
        """Gets the session_persistence_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The session_persistence_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._session_persistence_enabled

    @session_persistence_enabled.setter
    def session_persistence_enabled(self, session_persistence_enabled):
        """Sets the session_persistence_enabled of this CreateNLBServerGroupRequest.


        :param session_persistence_enabled: The session_persistence_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._session_persistence_enabled = session_persistence_enabled

    @property
    def session_persistence_timeout(self):
        """Gets the session_persistence_timeout of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The session_persistence_timeout of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: int
        """
        return self._session_persistence_timeout

    @session_persistence_timeout.setter
    def session_persistence_timeout(self, session_persistence_timeout):
        """Sets the session_persistence_timeout of this CreateNLBServerGroupRequest.


        :param session_persistence_timeout: The session_persistence_timeout of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: int
        """

        self._session_persistence_timeout = session_persistence_timeout

    @property
    def tags(self):
        """Gets the tags of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The tags of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: list[TagForCreateNLBServerGroupInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateNLBServerGroupRequest.


        :param tags: The tags of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: list[TagForCreateNLBServerGroupInput]
        """

        self._tags = tags

    @property
    def timestamp_remove_enabled(self):
        """Gets the timestamp_remove_enabled of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The timestamp_remove_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._timestamp_remove_enabled

    @timestamp_remove_enabled.setter
    def timestamp_remove_enabled(self, timestamp_remove_enabled):
        """Sets the timestamp_remove_enabled of this CreateNLBServerGroupRequest.


        :param timestamp_remove_enabled: The timestamp_remove_enabled of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: bool
        """

        self._timestamp_remove_enabled = timestamp_remove_enabled

    @property
    def type(self):
        """Gets the type of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The type of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateNLBServerGroupRequest.


        :param type: The type of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateNLBServerGroupRequest.  # noqa: E501


        :return: The vpc_id of this CreateNLBServerGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateNLBServerGroupRequest.


        :param vpc_id: The vpc_id of this CreateNLBServerGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNLBServerGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNLBServerGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNLBServerGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
