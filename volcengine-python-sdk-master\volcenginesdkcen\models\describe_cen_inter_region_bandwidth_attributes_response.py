# coding: utf-8

"""
    cen

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCenInterRegionBandwidthAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'cen_bandwidth_package_id': 'str',
        'cen_id': 'str',
        'creation_time': 'str',
        'inter_region_bandwidth_id': 'str',
        'local_region_id': 'str',
        'peer_region_id': 'str',
        'request_id': 'str',
        'status': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'cen_bandwidth_package_id': 'CenBandwidthPackageId',
        'cen_id': 'CenId',
        'creation_time': 'CreationTime',
        'inter_region_bandwidth_id': 'InterRegionBandwidthId',
        'local_region_id': 'LocalRegionId',
        'peer_region_id': 'PeerRegionId',
        'request_id': 'RequestId',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, bandwidth=None, cen_bandwidth_package_id=None, cen_id=None, creation_time=None, inter_region_bandwidth_id=None, local_region_id=None, peer_region_id=None, request_id=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeCenInterRegionBandwidthAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._cen_bandwidth_package_id = None
        self._cen_id = None
        self._creation_time = None
        self._inter_region_bandwidth_id = None
        self._local_region_id = None
        self._peer_region_id = None
        self._request_id = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if cen_bandwidth_package_id is not None:
            self.cen_bandwidth_package_id = cen_bandwidth_package_id
        if cen_id is not None:
            self.cen_id = cen_id
        if creation_time is not None:
            self.creation_time = creation_time
        if inter_region_bandwidth_id is not None:
            self.inter_region_bandwidth_id = inter_region_bandwidth_id
        if local_region_id is not None:
            self.local_region_id = local_region_id
        if peer_region_id is not None:
            self.peer_region_id = peer_region_id
        if request_id is not None:
            self.request_id = request_id
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The bandwidth of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param bandwidth: The bandwidth of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def cen_bandwidth_package_id(self):
        """Gets the cen_bandwidth_package_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The cen_bandwidth_package_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._cen_bandwidth_package_id

    @cen_bandwidth_package_id.setter
    def cen_bandwidth_package_id(self, cen_bandwidth_package_id):
        """Sets the cen_bandwidth_package_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param cen_bandwidth_package_id: The cen_bandwidth_package_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._cen_bandwidth_package_id = cen_bandwidth_package_id

    @property
    def cen_id(self):
        """Gets the cen_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The cen_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._cen_id

    @cen_id.setter
    def cen_id(self, cen_id):
        """Sets the cen_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param cen_id: The cen_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._cen_id = cen_id

    @property
    def creation_time(self):
        """Gets the creation_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The creation_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param creation_time: The creation_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def inter_region_bandwidth_id(self):
        """Gets the inter_region_bandwidth_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The inter_region_bandwidth_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._inter_region_bandwidth_id

    @inter_region_bandwidth_id.setter
    def inter_region_bandwidth_id(self, inter_region_bandwidth_id):
        """Sets the inter_region_bandwidth_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param inter_region_bandwidth_id: The inter_region_bandwidth_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._inter_region_bandwidth_id = inter_region_bandwidth_id

    @property
    def local_region_id(self):
        """Gets the local_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The local_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._local_region_id

    @local_region_id.setter
    def local_region_id(self, local_region_id):
        """Sets the local_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param local_region_id: The local_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._local_region_id = local_region_id

    @property
    def peer_region_id(self):
        """Gets the peer_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The peer_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._peer_region_id

    @peer_region_id.setter
    def peer_region_id(self, peer_region_id):
        """Sets the peer_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param peer_region_id: The peer_region_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._peer_region_id = peer_region_id

    @property
    def request_id(self):
        """Gets the request_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param request_id: The request_id of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def status(self):
        """Gets the status of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The status of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param status: The status of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeCenInterRegionBandwidthAttributesResponse.


        :param update_time: The update_time of this DescribeCenInterRegionBandwidthAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCenInterRegionBandwidthAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCenInterRegionBandwidthAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCenInterRegionBandwidthAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
