# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_name': 'str',
        'account_status': 'str',
        'account_type': 'str',
        'db_privileges': 'list[DBPrivilegeForListAccountsOutput]'
    }

    attribute_map = {
        'account_name': 'AccountName',
        'account_status': 'AccountStatus',
        'account_type': 'AccountType',
        'db_privileges': 'DBPrivileges'
    }

    def __init__(self, account_name=None, account_status=None, account_type=None, db_privileges=None, _configuration=None):  # noqa: E501
        """DataForListAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_name = None
        self._account_status = None
        self._account_type = None
        self._db_privileges = None
        self.discriminator = None

        if account_name is not None:
            self.account_name = account_name
        if account_status is not None:
            self.account_status = account_status
        if account_type is not None:
            self.account_type = account_type
        if db_privileges is not None:
            self.db_privileges = db_privileges

    @property
    def account_name(self):
        """Gets the account_name of this DataForListAccountsOutput.  # noqa: E501


        :return: The account_name of this DataForListAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this DataForListAccountsOutput.


        :param account_name: The account_name of this DataForListAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def account_status(self):
        """Gets the account_status of this DataForListAccountsOutput.  # noqa: E501


        :return: The account_status of this DataForListAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_status

    @account_status.setter
    def account_status(self, account_status):
        """Sets the account_status of this DataForListAccountsOutput.


        :param account_status: The account_status of this DataForListAccountsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Available", "Unavailable"]  # noqa: E501
        if (self._configuration.client_side_validation and
                account_status not in allowed_values):
            raise ValueError(
                "Invalid value for `account_status` ({0}), must be one of {1}"  # noqa: E501
                .format(account_status, allowed_values)
            )

        self._account_status = account_status

    @property
    def account_type(self):
        """Gets the account_type of this DataForListAccountsOutput.  # noqa: E501


        :return: The account_type of this DataForListAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_type

    @account_type.setter
    def account_type(self, account_type):
        """Sets the account_type of this DataForListAccountsOutput.


        :param account_type: The account_type of this DataForListAccountsOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Grant", "Normal", "Super"]  # noqa: E501
        if (self._configuration.client_side_validation and
                account_type not in allowed_values):
            raise ValueError(
                "Invalid value for `account_type` ({0}), must be one of {1}"  # noqa: E501
                .format(account_type, allowed_values)
            )

        self._account_type = account_type

    @property
    def db_privileges(self):
        """Gets the db_privileges of this DataForListAccountsOutput.  # noqa: E501


        :return: The db_privileges of this DataForListAccountsOutput.  # noqa: E501
        :rtype: list[DBPrivilegeForListAccountsOutput]
        """
        return self._db_privileges

    @db_privileges.setter
    def db_privileges(self, db_privileges):
        """Sets the db_privileges of this DataForListAccountsOutput.


        :param db_privileges: The db_privileges of this DataForListAccountsOutput.  # noqa: E501
        :type: list[DBPrivilegeForListAccountsOutput]
        """

        self._db_privileges = db_privileges

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
