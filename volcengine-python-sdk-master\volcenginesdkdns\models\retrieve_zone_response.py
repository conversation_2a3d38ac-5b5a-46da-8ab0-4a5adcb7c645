# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RetrieveZoneResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host': 'str',
        'is_owned_by_domain': 'bool',
        'txt': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'host': 'Host',
        'is_owned_by_domain': 'IsOwnedByDomain',
        'txt': 'Txt',
        'zone_name': 'ZoneName'
    }

    def __init__(self, host=None, is_owned_by_domain=None, txt=None, zone_name=None, _configuration=None):  # noqa: E501
        """RetrieveZoneResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host = None
        self._is_owned_by_domain = None
        self._txt = None
        self._zone_name = None
        self.discriminator = None

        if host is not None:
            self.host = host
        if is_owned_by_domain is not None:
            self.is_owned_by_domain = is_owned_by_domain
        if txt is not None:
            self.txt = txt
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def host(self):
        """Gets the host of this RetrieveZoneResponse.  # noqa: E501


        :return: The host of this RetrieveZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this RetrieveZoneResponse.


        :param host: The host of this RetrieveZoneResponse.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def is_owned_by_domain(self):
        """Gets the is_owned_by_domain of this RetrieveZoneResponse.  # noqa: E501


        :return: The is_owned_by_domain of this RetrieveZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_owned_by_domain

    @is_owned_by_domain.setter
    def is_owned_by_domain(self, is_owned_by_domain):
        """Sets the is_owned_by_domain of this RetrieveZoneResponse.


        :param is_owned_by_domain: The is_owned_by_domain of this RetrieveZoneResponse.  # noqa: E501
        :type: bool
        """

        self._is_owned_by_domain = is_owned_by_domain

    @property
    def txt(self):
        """Gets the txt of this RetrieveZoneResponse.  # noqa: E501


        :return: The txt of this RetrieveZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._txt

    @txt.setter
    def txt(self, txt):
        """Sets the txt of this RetrieveZoneResponse.


        :param txt: The txt of this RetrieveZoneResponse.  # noqa: E501
        :type: str
        """

        self._txt = txt

    @property
    def zone_name(self):
        """Gets the zone_name of this RetrieveZoneResponse.  # noqa: E501


        :return: The zone_name of this RetrieveZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this RetrieveZoneResponse.


        :param zone_name: The zone_name of this RetrieveZoneResponse.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RetrieveZoneResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RetrieveZoneResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RetrieveZoneResponse):
            return True

        return self.to_dict() != other.to_dict()
