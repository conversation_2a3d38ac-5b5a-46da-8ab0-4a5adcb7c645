# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DownloadLeafInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'download_type': 'str',
        'instance_id': 'str',
        'is_tarball': 'bool',
        'password': 'str'
    }

    attribute_map = {
        'download_type': 'download_type',
        'instance_id': 'instance_id',
        'is_tarball': 'is_tarball',
        'password': 'password'
    }

    def __init__(self, download_type=None, instance_id=None, is_tarball=None, password=None, _configuration=None):  # noqa: E501
        """DownloadLeafInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._download_type = None
        self._instance_id = None
        self._is_tarball = None
        self._password = None
        self.discriminator = None

        self.download_type = download_type
        self.instance_id = instance_id
        self.is_tarball = is_tarball
        if password is not None:
            self.password = password

    @property
    def download_type(self):
        """Gets the download_type of this DownloadLeafInstanceRequest.  # noqa: E501


        :return: The download_type of this DownloadLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._download_type

    @download_type.setter
    def download_type(self, download_type):
        """Sets the download_type of this DownloadLeafInstanceRequest.


        :param download_type: The download_type of this DownloadLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and download_type is None:
            raise ValueError("Invalid value for `download_type`, must not be `None`")  # noqa: E501

        self._download_type = download_type

    @property
    def instance_id(self):
        """Gets the instance_id of this DownloadLeafInstanceRequest.  # noqa: E501


        :return: The instance_id of this DownloadLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DownloadLeafInstanceRequest.


        :param instance_id: The instance_id of this DownloadLeafInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def is_tarball(self):
        """Gets the is_tarball of this DownloadLeafInstanceRequest.  # noqa: E501


        :return: The is_tarball of this DownloadLeafInstanceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_tarball

    @is_tarball.setter
    def is_tarball(self, is_tarball):
        """Sets the is_tarball of this DownloadLeafInstanceRequest.


        :param is_tarball: The is_tarball of this DownloadLeafInstanceRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and is_tarball is None:
            raise ValueError("Invalid value for `is_tarball`, must not be `None`")  # noqa: E501

        self._is_tarball = is_tarball

    @property
    def password(self):
        """Gets the password of this DownloadLeafInstanceRequest.  # noqa: E501


        :return: The password of this DownloadLeafInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this DownloadLeafInstanceRequest.


        :param password: The password of this DownloadLeafInstanceRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DownloadLeafInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DownloadLeafInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DownloadLeafInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
