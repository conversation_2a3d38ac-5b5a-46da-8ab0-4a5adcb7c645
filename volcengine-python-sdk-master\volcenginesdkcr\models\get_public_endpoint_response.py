# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetPublicEndpointResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'acl_policies': 'list[AclPolicyForGetPublicEndpointOutput]',
        'enabled': 'bool',
        'registry': 'str',
        'status': 'str'
    }

    attribute_map = {
        'acl_policies': 'AclPolicies',
        'enabled': 'Enabled',
        'registry': 'Registry',
        'status': 'Status'
    }

    def __init__(self, acl_policies=None, enabled=None, registry=None, status=None, _configuration=None):  # noqa: E501
        """GetPublicEndpointResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._acl_policies = None
        self._enabled = None
        self._registry = None
        self._status = None
        self.discriminator = None

        if acl_policies is not None:
            self.acl_policies = acl_policies
        if enabled is not None:
            self.enabled = enabled
        if registry is not None:
            self.registry = registry
        if status is not None:
            self.status = status

    @property
    def acl_policies(self):
        """Gets the acl_policies of this GetPublicEndpointResponse.  # noqa: E501


        :return: The acl_policies of this GetPublicEndpointResponse.  # noqa: E501
        :rtype: list[AclPolicyForGetPublicEndpointOutput]
        """
        return self._acl_policies

    @acl_policies.setter
    def acl_policies(self, acl_policies):
        """Sets the acl_policies of this GetPublicEndpointResponse.


        :param acl_policies: The acl_policies of this GetPublicEndpointResponse.  # noqa: E501
        :type: list[AclPolicyForGetPublicEndpointOutput]
        """

        self._acl_policies = acl_policies

    @property
    def enabled(self):
        """Gets the enabled of this GetPublicEndpointResponse.  # noqa: E501


        :return: The enabled of this GetPublicEndpointResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this GetPublicEndpointResponse.


        :param enabled: The enabled of this GetPublicEndpointResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def registry(self):
        """Gets the registry of this GetPublicEndpointResponse.  # noqa: E501


        :return: The registry of this GetPublicEndpointResponse.  # noqa: E501
        :rtype: str
        """
        return self._registry

    @registry.setter
    def registry(self, registry):
        """Sets the registry of this GetPublicEndpointResponse.


        :param registry: The registry of this GetPublicEndpointResponse.  # noqa: E501
        :type: str
        """

        self._registry = registry

    @property
    def status(self):
        """Gets the status of this GetPublicEndpointResponse.  # noqa: E501


        :return: The status of this GetPublicEndpointResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetPublicEndpointResponse.


        :param status: The status of this GetPublicEndpointResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetPublicEndpointResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetPublicEndpointResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetPublicEndpointResponse):
            return True

        return self.to_dict() != other.to_dict()
