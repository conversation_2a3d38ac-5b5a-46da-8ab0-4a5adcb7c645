# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuickApplyCertificateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'common_name': 'str',
        'csr': 'str',
        'key_alg': 'str',
        'organization_id': 'str',
        'plan': 'str',
        'private_key': 'str',
        'project_name': 'str',
        'san': 'list[str]',
        'tag': 'str',
        'tags': 'list[TagForQuickApplyCertificateInput]',
        'validation_type': 'str'
    }

    attribute_map = {
        'common_name': 'CommonName',
        'csr': 'Csr',
        'key_alg': 'KeyAlg',
        'organization_id': 'OrganizationId',
        'plan': 'Plan',
        'private_key': 'PrivateKey',
        'project_name': 'ProjectName',
        'san': 'San',
        'tag': 'Tag',
        'tags': 'Tags',
        'validation_type': 'ValidationType'
    }

    def __init__(self, common_name=None, csr=None, key_alg=None, organization_id=None, plan=None, private_key=None, project_name=None, san=None, tag=None, tags=None, validation_type=None, _configuration=None):  # noqa: E501
        """QuickApplyCertificateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._common_name = None
        self._csr = None
        self._key_alg = None
        self._organization_id = None
        self._plan = None
        self._private_key = None
        self._project_name = None
        self._san = None
        self._tag = None
        self._tags = None
        self._validation_type = None
        self.discriminator = None

        if common_name is not None:
            self.common_name = common_name
        if csr is not None:
            self.csr = csr
        if key_alg is not None:
            self.key_alg = key_alg
        self.organization_id = organization_id
        self.plan = plan
        if private_key is not None:
            self.private_key = private_key
        if project_name is not None:
            self.project_name = project_name
        if san is not None:
            self.san = san
        if tag is not None:
            self.tag = tag
        if tags is not None:
            self.tags = tags
        self.validation_type = validation_type

    @property
    def common_name(self):
        """Gets the common_name of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The common_name of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this QuickApplyCertificateRequest.


        :param common_name: The common_name of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def csr(self):
        """Gets the csr of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The csr of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._csr

    @csr.setter
    def csr(self, csr):
        """Sets the csr of this QuickApplyCertificateRequest.


        :param csr: The csr of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """

        self._csr = csr

    @property
    def key_alg(self):
        """Gets the key_alg of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The key_alg of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_alg

    @key_alg.setter
    def key_alg(self, key_alg):
        """Sets the key_alg of this QuickApplyCertificateRequest.


        :param key_alg: The key_alg of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["rsa", "ecc", "SM2"]  # noqa: E501
        if (self._configuration.client_side_validation and
                key_alg not in allowed_values):
            raise ValueError(
                "Invalid value for `key_alg` ({0}), must be one of {1}"  # noqa: E501
                .format(key_alg, allowed_values)
            )

        self._key_alg = key_alg

    @property
    def organization_id(self):
        """Gets the organization_id of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The organization_id of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._organization_id

    @organization_id.setter
    def organization_id(self, organization_id):
        """Sets the organization_id of this QuickApplyCertificateRequest.


        :param organization_id: The organization_id of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and organization_id is None:
            raise ValueError("Invalid value for `organization_id`, must not be `None`")  # noqa: E501

        self._organization_id = organization_id

    @property
    def plan(self):
        """Gets the plan of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The plan of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._plan

    @plan.setter
    def plan(self, plan):
        """Sets the plan of this QuickApplyCertificateRequest.


        :param plan: The plan of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plan is None:
            raise ValueError("Invalid value for `plan`, must not be `None`")  # noqa: E501

        self._plan = plan

    @property
    def private_key(self):
        """Gets the private_key of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The private_key of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this QuickApplyCertificateRequest.


        :param private_key: The private_key of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    @property
    def project_name(self):
        """Gets the project_name of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The project_name of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this QuickApplyCertificateRequest.


        :param project_name: The project_name of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def san(self):
        """Gets the san of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The san of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._san

    @san.setter
    def san(self, san):
        """Sets the san of this QuickApplyCertificateRequest.


        :param san: The san of this QuickApplyCertificateRequest.  # noqa: E501
        :type: list[str]
        """

        self._san = san

    @property
    def tag(self):
        """Gets the tag of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The tag of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this QuickApplyCertificateRequest.


        :param tag: The tag of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tags(self):
        """Gets the tags of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The tags of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: list[TagForQuickApplyCertificateInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this QuickApplyCertificateRequest.


        :param tags: The tags of this QuickApplyCertificateRequest.  # noqa: E501
        :type: list[TagForQuickApplyCertificateInput]
        """

        self._tags = tags

    @property
    def validation_type(self):
        """Gets the validation_type of this QuickApplyCertificateRequest.  # noqa: E501


        :return: The validation_type of this QuickApplyCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._validation_type

    @validation_type.setter
    def validation_type(self, validation_type):
        """Sets the validation_type of this QuickApplyCertificateRequest.


        :param validation_type: The validation_type of this QuickApplyCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and validation_type is None:
            raise ValueError("Invalid value for `validation_type`, must not be `None`")  # noqa: E501
        allowed_values = ["dns_cname", "dns_txt", "https", "http"]  # noqa: E501
        if (self._configuration.client_side_validation and
                validation_type not in allowed_values):
            raise ValueError(
                "Invalid value for `validation_type` ({0}), must be one of {1}"  # noqa: E501
                .format(validation_type, allowed_values)
            )

        self._validation_type = validation_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuickApplyCertificateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuickApplyCertificateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuickApplyCertificateRequest):
            return True

        return self.to_dict() != other.to_dict()
