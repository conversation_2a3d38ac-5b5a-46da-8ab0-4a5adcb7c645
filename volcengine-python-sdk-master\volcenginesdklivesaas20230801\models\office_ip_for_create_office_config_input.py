# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OfficeIpForCreateOfficeConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip': 'list[str]',
        'network_segment': 'list[str]'
    }

    attribute_map = {
        'ip': 'Ip',
        'network_segment': 'NetworkSegment'
    }

    def __init__(self, ip=None, network_segment=None, _configuration=None):  # noqa: E501
        """OfficeIpForCreateOfficeConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip = None
        self._network_segment = None
        self.discriminator = None

        if ip is not None:
            self.ip = ip
        if network_segment is not None:
            self.network_segment = network_segment

    @property
    def ip(self):
        """Gets the ip of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501


        :return: The ip of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this OfficeIpForCreateOfficeConfigInput.


        :param ip: The ip of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._ip = ip

    @property
    def network_segment(self):
        """Gets the network_segment of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501


        :return: The network_segment of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._network_segment

    @network_segment.setter
    def network_segment(self, network_segment):
        """Sets the network_segment of this OfficeIpForCreateOfficeConfigInput.


        :param network_segment: The network_segment of this OfficeIpForCreateOfficeConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._network_segment = network_segment

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OfficeIpForCreateOfficeConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OfficeIpForCreateOfficeConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OfficeIpForCreateOfficeConfigInput):
            return True

        return self.to_dict() != other.to_dict()
