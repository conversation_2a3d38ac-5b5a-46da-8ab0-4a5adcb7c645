# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateServiceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apig_config': 'ApigConfigForCreateServiceInput',
        'clb_config': 'CLBConfigForCreateServiceInput',
        'description': 'str',
        'dry_run': 'bool',
        'ports': 'list[PortForCreateServiceInput]',
        'resource_group_id': 'str',
        'service_name': 'str',
        'traffic_config': 'TrafficConfigForCreateServiceInput'
    }

    attribute_map = {
        'apig_config': 'ApigConfig',
        'clb_config': 'CLBConfig',
        'description': 'Description',
        'dry_run': 'DryRun',
        'ports': 'Ports',
        'resource_group_id': 'ResourceGroupId',
        'service_name': 'ServiceName',
        'traffic_config': 'TrafficConfig'
    }

    def __init__(self, apig_config=None, clb_config=None, description=None, dry_run=None, ports=None, resource_group_id=None, service_name=None, traffic_config=None, _configuration=None):  # noqa: E501
        """CreateServiceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apig_config = None
        self._clb_config = None
        self._description = None
        self._dry_run = None
        self._ports = None
        self._resource_group_id = None
        self._service_name = None
        self._traffic_config = None
        self.discriminator = None

        if apig_config is not None:
            self.apig_config = apig_config
        if clb_config is not None:
            self.clb_config = clb_config
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if ports is not None:
            self.ports = ports
        self.resource_group_id = resource_group_id
        self.service_name = service_name
        if traffic_config is not None:
            self.traffic_config = traffic_config

    @property
    def apig_config(self):
        """Gets the apig_config of this CreateServiceRequest.  # noqa: E501


        :return: The apig_config of this CreateServiceRequest.  # noqa: E501
        :rtype: ApigConfigForCreateServiceInput
        """
        return self._apig_config

    @apig_config.setter
    def apig_config(self, apig_config):
        """Sets the apig_config of this CreateServiceRequest.


        :param apig_config: The apig_config of this CreateServiceRequest.  # noqa: E501
        :type: ApigConfigForCreateServiceInput
        """

        self._apig_config = apig_config

    @property
    def clb_config(self):
        """Gets the clb_config of this CreateServiceRequest.  # noqa: E501


        :return: The clb_config of this CreateServiceRequest.  # noqa: E501
        :rtype: CLBConfigForCreateServiceInput
        """
        return self._clb_config

    @clb_config.setter
    def clb_config(self, clb_config):
        """Sets the clb_config of this CreateServiceRequest.


        :param clb_config: The clb_config of this CreateServiceRequest.  # noqa: E501
        :type: CLBConfigForCreateServiceInput
        """

        self._clb_config = clb_config

    @property
    def description(self):
        """Gets the description of this CreateServiceRequest.  # noqa: E501


        :return: The description of this CreateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateServiceRequest.


        :param description: The description of this CreateServiceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateServiceRequest.  # noqa: E501


        :return: The dry_run of this CreateServiceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateServiceRequest.


        :param dry_run: The dry_run of this CreateServiceRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def ports(self):
        """Gets the ports of this CreateServiceRequest.  # noqa: E501


        :return: The ports of this CreateServiceRequest.  # noqa: E501
        :rtype: list[PortForCreateServiceInput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this CreateServiceRequest.


        :param ports: The ports of this CreateServiceRequest.  # noqa: E501
        :type: list[PortForCreateServiceInput]
        """

        self._ports = ports

    @property
    def resource_group_id(self):
        """Gets the resource_group_id of this CreateServiceRequest.  # noqa: E501


        :return: The resource_group_id of this CreateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_group_id

    @resource_group_id.setter
    def resource_group_id(self, resource_group_id):
        """Sets the resource_group_id of this CreateServiceRequest.


        :param resource_group_id: The resource_group_id of this CreateServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_group_id is None:
            raise ValueError("Invalid value for `resource_group_id`, must not be `None`")  # noqa: E501

        self._resource_group_id = resource_group_id

    @property
    def service_name(self):
        """Gets the service_name of this CreateServiceRequest.  # noqa: E501


        :return: The service_name of this CreateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this CreateServiceRequest.


        :param service_name: The service_name of this CreateServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and service_name is None:
            raise ValueError("Invalid value for `service_name`, must not be `None`")  # noqa: E501

        self._service_name = service_name

    @property
    def traffic_config(self):
        """Gets the traffic_config of this CreateServiceRequest.  # noqa: E501


        :return: The traffic_config of this CreateServiceRequest.  # noqa: E501
        :rtype: TrafficConfigForCreateServiceInput
        """
        return self._traffic_config

    @traffic_config.setter
    def traffic_config(self, traffic_config):
        """Sets the traffic_config of this CreateServiceRequest.


        :param traffic_config: The traffic_config of this CreateServiceRequest.  # noqa: E501
        :type: TrafficConfigForCreateServiceInput
        """

        self._traffic_config = traffic_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateServiceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateServiceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateServiceRequest):
            return True

        return self.to_dict() != other.to_dict()
