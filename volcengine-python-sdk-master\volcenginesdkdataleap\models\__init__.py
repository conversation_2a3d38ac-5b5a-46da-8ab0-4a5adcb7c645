# coding: utf-8

# flake8: noqa
"""
    dataleap

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkdataleap.models.conf_for_dts_open_describe_resource_groups_output import ConfForDTSOpenDescribeResourceGroupsOutput
from volcenginesdkdataleap.models.dts_open_describe_resource_groups_request import DTSOpenDescribeResourceGroupsRequest
from volcenginesdkdataleap.models.dts_open_describe_resource_groups_response import DTSOpenDescribeResourceGroupsResponse
from volcenginesdkdataleap.models.dts_open_list_tag_resource_groups_request import DTSOpenListTagResourceGroupsRequest
from volcenginesdkdataleap.models.dts_open_list_tag_resource_groups_response import DTSOpenListTagResourceGroupsResponse
from volcenginesdkdataleap.models.dts_open_tag_resource_groups_request import DTSOpenTagResourceGroupsRequest
from volcenginesdkdataleap.models.dts_open_tag_resource_groups_response import DTSOpenTagResourceGroupsResponse
from volcenginesdkdataleap.models.dts_open_untag_resource_groups_request import DTSOpenUntagResourceGroupsRequest
from volcenginesdkdataleap.models.dts_open_untag_resource_groups_response import DTSOpenUntagResourceGroupsResponse
from volcenginesdkdataleap.models.list_for_dts_open_describe_resource_groups_output import ListForDTSOpenDescribeResourceGroupsOutput
from volcenginesdkdataleap.models.resource_tag_for_dts_open_list_tag_resource_groups_output import ResourceTagForDTSOpenListTagResourceGroupsOutput
from volcenginesdkdataleap.models.tag_filter_for_dts_open_describe_resource_groups_input import TagFilterForDTSOpenDescribeResourceGroupsInput
from volcenginesdkdataleap.models.tag_for_dts_open_describe_resource_groups_output import TagForDTSOpenDescribeResourceGroupsOutput
from volcenginesdkdataleap.models.tag_for_dts_open_list_tag_resource_groups_input import TagForDTSOpenListTagResourceGroupsInput
from volcenginesdkdataleap.models.tag_for_dts_open_tag_resource_groups_input import TagForDTSOpenTagResourceGroupsInput
