# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceVisitAddressRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'addr_type': 'str',
        'client_token': 'str',
        'instance_id': 'str',
        'new_address_prefix': 'str',
        'new_port': 'int',
        'upgrade_region_domain': 'bool'
    }

    attribute_map = {
        'addr_type': 'AddrType',
        'client_token': 'ClientToken',
        'instance_id': 'InstanceId',
        'new_address_prefix': 'NewAddressPrefix',
        'new_port': 'NewPort',
        'upgrade_region_domain': 'UpgradeRegionDomain'
    }

    def __init__(self, addr_type=None, client_token=None, instance_id=None, new_address_prefix=None, new_port=None, upgrade_region_domain=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceVisitAddressRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._addr_type = None
        self._client_token = None
        self._instance_id = None
        self._new_address_prefix = None
        self._new_port = None
        self._upgrade_region_domain = None
        self.discriminator = None

        self.addr_type = addr_type
        if client_token is not None:
            self.client_token = client_token
        self.instance_id = instance_id
        if new_address_prefix is not None:
            self.new_address_prefix = new_address_prefix
        if new_port is not None:
            self.new_port = new_port
        if upgrade_region_domain is not None:
            self.upgrade_region_domain = upgrade_region_domain

    @property
    def addr_type(self):
        """Gets the addr_type of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The addr_type of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._addr_type

    @addr_type.setter
    def addr_type(self, addr_type):
        """Sets the addr_type of this ModifyDBInstanceVisitAddressRequest.


        :param addr_type: The addr_type of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and addr_type is None:
            raise ValueError("Invalid value for `addr_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Private", "Public", "PublicZone", "StorageInner", "DirectLink", "PublicZoneAffinity"]  # noqa: E501
        if (self._configuration.client_side_validation and
                addr_type not in allowed_values):
            raise ValueError(
                "Invalid value for `addr_type` ({0}), must be one of {1}"  # noqa: E501
                .format(addr_type, allowed_values)
            )

        self._addr_type = addr_type

    @property
    def client_token(self):
        """Gets the client_token of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The client_token of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyDBInstanceVisitAddressRequest.


        :param client_token: The client_token of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceVisitAddressRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def new_address_prefix(self):
        """Gets the new_address_prefix of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The new_address_prefix of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._new_address_prefix

    @new_address_prefix.setter
    def new_address_prefix(self, new_address_prefix):
        """Sets the new_address_prefix of this ModifyDBInstanceVisitAddressRequest.


        :param new_address_prefix: The new_address_prefix of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: str
        """

        self._new_address_prefix = new_address_prefix

    @property
    def new_port(self):
        """Gets the new_port of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The new_port of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: int
        """
        return self._new_port

    @new_port.setter
    def new_port(self, new_port):
        """Sets the new_port of this ModifyDBInstanceVisitAddressRequest.


        :param new_port: The new_port of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: int
        """

        self._new_port = new_port

    @property
    def upgrade_region_domain(self):
        """Gets the upgrade_region_domain of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501


        :return: The upgrade_region_domain of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :rtype: bool
        """
        return self._upgrade_region_domain

    @upgrade_region_domain.setter
    def upgrade_region_domain(self, upgrade_region_domain):
        """Sets the upgrade_region_domain of this ModifyDBInstanceVisitAddressRequest.


        :param upgrade_region_domain: The upgrade_region_domain of this ModifyDBInstanceVisitAddressRequest.  # noqa: E501
        :type: bool
        """

        self._upgrade_region_domain = upgrade_region_domain

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceVisitAddressRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceVisitAddressRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceVisitAddressRequest):
            return True

        return self.to_dict() != other.to_dict()
