# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InKBpsFlowForDescribeAttackFlowOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip': 'str',
        'timestamp': 'int',
        'value': 'int'
    }

    attribute_map = {
        'ip': 'Ip',
        'timestamp': 'Timestamp',
        'value': 'Value'
    }

    def __init__(self, ip=None, timestamp=None, value=None, _configuration=None):  # noqa: E501
        """InKBpsFlowForDescribeAttackFlowOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip = None
        self._timestamp = None
        self._value = None
        self.discriminator = None

        if ip is not None:
            self.ip = ip
        if timestamp is not None:
            self.timestamp = timestamp
        if value is not None:
            self.value = value

    @property
    def ip(self):
        """Gets the ip of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501


        :return: The ip of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this InKBpsFlowForDescribeAttackFlowOutput.


        :param ip: The ip of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def timestamp(self):
        """Gets the timestamp of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501


        :return: The timestamp of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this InKBpsFlowForDescribeAttackFlowOutput.


        :param timestamp: The timestamp of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :type: int
        """

        self._timestamp = timestamp

    @property
    def value(self):
        """Gets the value of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501


        :return: The value of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :rtype: int
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this InKBpsFlowForDescribeAttackFlowOutput.


        :param value: The value of this InKBpsFlowForDescribeAttackFlowOutput.  # noqa: E501
        :type: int
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InKBpsFlowForDescribeAttackFlowOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InKBpsFlowForDescribeAttackFlowOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InKBpsFlowForDescribeAttackFlowOutput):
            return True

        return self.to_dict() != other.to_dict()
