# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LocationDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'remote_address': 'str',
        'request_headers': 'list[RequestHeaderForGetTaskResultOutput]',
        'response_headers': 'list[ResponseHeaderForGetTaskResultOutput]',
        'status_code': 'int',
        'time_dns': 'float',
        'time_receive': 'float',
        'time_ssl': 'float',
        'time_tcp': 'float',
        'time_total': 'float',
        'time_wait': 'float',
        'url': 'str'
    }

    attribute_map = {
        'remote_address': 'RemoteAddress',
        'request_headers': 'RequestHeaders',
        'response_headers': 'ResponseHeaders',
        'status_code': 'StatusCode',
        'time_dns': 'TimeDNS',
        'time_receive': 'TimeReceive',
        'time_ssl': 'TimeSsl',
        'time_tcp': 'TimeTCP',
        'time_total': 'TimeTotal',
        'time_wait': 'TimeWait',
        'url': 'URL'
    }

    def __init__(self, remote_address=None, request_headers=None, response_headers=None, status_code=None, time_dns=None, time_receive=None, time_ssl=None, time_tcp=None, time_total=None, time_wait=None, url=None, _configuration=None):  # noqa: E501
        """LocationDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._remote_address = None
        self._request_headers = None
        self._response_headers = None
        self._status_code = None
        self._time_dns = None
        self._time_receive = None
        self._time_ssl = None
        self._time_tcp = None
        self._time_total = None
        self._time_wait = None
        self._url = None
        self.discriminator = None

        if remote_address is not None:
            self.remote_address = remote_address
        if request_headers is not None:
            self.request_headers = request_headers
        if response_headers is not None:
            self.response_headers = response_headers
        if status_code is not None:
            self.status_code = status_code
        if time_dns is not None:
            self.time_dns = time_dns
        if time_receive is not None:
            self.time_receive = time_receive
        if time_ssl is not None:
            self.time_ssl = time_ssl
        if time_tcp is not None:
            self.time_tcp = time_tcp
        if time_total is not None:
            self.time_total = time_total
        if time_wait is not None:
            self.time_wait = time_wait
        if url is not None:
            self.url = url

    @property
    def remote_address(self):
        """Gets the remote_address of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The remote_address of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._remote_address

    @remote_address.setter
    def remote_address(self, remote_address):
        """Sets the remote_address of this LocationDetailForGetTaskResultOutput.


        :param remote_address: The remote_address of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._remote_address = remote_address

    @property
    def request_headers(self):
        """Gets the request_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The request_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[RequestHeaderForGetTaskResultOutput]
        """
        return self._request_headers

    @request_headers.setter
    def request_headers(self, request_headers):
        """Sets the request_headers of this LocationDetailForGetTaskResultOutput.


        :param request_headers: The request_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[RequestHeaderForGetTaskResultOutput]
        """

        self._request_headers = request_headers

    @property
    def response_headers(self):
        """Gets the response_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The response_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[ResponseHeaderForGetTaskResultOutput]
        """
        return self._response_headers

    @response_headers.setter
    def response_headers(self, response_headers):
        """Sets the response_headers of this LocationDetailForGetTaskResultOutput.


        :param response_headers: The response_headers of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[ResponseHeaderForGetTaskResultOutput]
        """

        self._response_headers = response_headers

    @property
    def status_code(self):
        """Gets the status_code of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The status_code of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this LocationDetailForGetTaskResultOutput.


        :param status_code: The status_code of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._status_code = status_code

    @property
    def time_dns(self):
        """Gets the time_dns of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_dns of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_dns

    @time_dns.setter
    def time_dns(self, time_dns):
        """Sets the time_dns of this LocationDetailForGetTaskResultOutput.


        :param time_dns: The time_dns of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_dns = time_dns

    @property
    def time_receive(self):
        """Gets the time_receive of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_receive of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_receive

    @time_receive.setter
    def time_receive(self, time_receive):
        """Sets the time_receive of this LocationDetailForGetTaskResultOutput.


        :param time_receive: The time_receive of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_receive = time_receive

    @property
    def time_ssl(self):
        """Gets the time_ssl of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_ssl of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_ssl

    @time_ssl.setter
    def time_ssl(self, time_ssl):
        """Sets the time_ssl of this LocationDetailForGetTaskResultOutput.


        :param time_ssl: The time_ssl of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_ssl = time_ssl

    @property
    def time_tcp(self):
        """Gets the time_tcp of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_tcp of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_tcp

    @time_tcp.setter
    def time_tcp(self, time_tcp):
        """Sets the time_tcp of this LocationDetailForGetTaskResultOutput.


        :param time_tcp: The time_tcp of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_tcp = time_tcp

    @property
    def time_total(self):
        """Gets the time_total of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_total of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_total

    @time_total.setter
    def time_total(self, time_total):
        """Sets the time_total of this LocationDetailForGetTaskResultOutput.


        :param time_total: The time_total of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_total = time_total

    @property
    def time_wait(self):
        """Gets the time_wait of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The time_wait of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._time_wait

    @time_wait.setter
    def time_wait(self, time_wait):
        """Sets the time_wait of this LocationDetailForGetTaskResultOutput.


        :param time_wait: The time_wait of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._time_wait = time_wait

    @property
    def url(self):
        """Gets the url of this LocationDetailForGetTaskResultOutput.  # noqa: E501


        :return: The url of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this LocationDetailForGetTaskResultOutput.


        :param url: The url of this LocationDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LocationDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LocationDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LocationDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
