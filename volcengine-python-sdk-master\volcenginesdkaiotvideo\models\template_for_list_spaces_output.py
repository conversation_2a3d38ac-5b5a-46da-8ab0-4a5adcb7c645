# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TemplateForListSpacesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ai': 'AIForListSpacesOutput',
        'record': 'RecordForListSpacesOutput',
        'screenshot': 'ScreenshotForListSpacesOutput'
    }

    attribute_map = {
        'ai': 'AI',
        'record': 'Record',
        'screenshot': 'Screenshot'
    }

    def __init__(self, ai=None, record=None, screenshot=None, _configuration=None):  # noqa: E501
        """TemplateForListSpacesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ai = None
        self._record = None
        self._screenshot = None
        self.discriminator = None

        if ai is not None:
            self.ai = ai
        if record is not None:
            self.record = record
        if screenshot is not None:
            self.screenshot = screenshot

    @property
    def ai(self):
        """Gets the ai of this TemplateForListSpacesOutput.  # noqa: E501


        :return: The ai of this TemplateForListSpacesOutput.  # noqa: E501
        :rtype: AIForListSpacesOutput
        """
        return self._ai

    @ai.setter
    def ai(self, ai):
        """Sets the ai of this TemplateForListSpacesOutput.


        :param ai: The ai of this TemplateForListSpacesOutput.  # noqa: E501
        :type: AIForListSpacesOutput
        """

        self._ai = ai

    @property
    def record(self):
        """Gets the record of this TemplateForListSpacesOutput.  # noqa: E501


        :return: The record of this TemplateForListSpacesOutput.  # noqa: E501
        :rtype: RecordForListSpacesOutput
        """
        return self._record

    @record.setter
    def record(self, record):
        """Sets the record of this TemplateForListSpacesOutput.


        :param record: The record of this TemplateForListSpacesOutput.  # noqa: E501
        :type: RecordForListSpacesOutput
        """

        self._record = record

    @property
    def screenshot(self):
        """Gets the screenshot of this TemplateForListSpacesOutput.  # noqa: E501


        :return: The screenshot of this TemplateForListSpacesOutput.  # noqa: E501
        :rtype: ScreenshotForListSpacesOutput
        """
        return self._screenshot

    @screenshot.setter
    def screenshot(self, screenshot):
        """Sets the screenshot of this TemplateForListSpacesOutput.


        :param screenshot: The screenshot of this TemplateForListSpacesOutput.  # noqa: E501
        :type: ScreenshotForListSpacesOutput
        """

        self._screenshot = screenshot

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TemplateForListSpacesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TemplateForListSpacesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TemplateForListSpacesOutput):
            return True

        return self.to_dict() != other.to_dict()
