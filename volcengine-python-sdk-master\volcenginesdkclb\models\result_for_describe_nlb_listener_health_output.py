# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResultForDescribeNLBListenerHealthOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'ip': 'str',
        'port': 'int',
        'server_id': 'str',
        'server_type': 'str',
        'status': 'str',
        'update_time': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'ip': 'Ip',
        'port': 'Port',
        'server_id': 'ServerId',
        'server_type': 'ServerType',
        'status': 'Status',
        'update_time': 'UpdateTime',
        'zone_id': 'ZoneId'
    }

    def __init__(self, instance_id=None, ip=None, port=None, server_id=None, server_type=None, status=None, update_time=None, zone_id=None, _configuration=None):  # noqa: E501
        """ResultForDescribeNLBListenerHealthOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._ip = None
        self._port = None
        self._server_id = None
        self._server_type = None
        self._status = None
        self._update_time = None
        self._zone_id = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if ip is not None:
            self.ip = ip
        if port is not None:
            self.port = port
        if server_id is not None:
            self.server_id = server_id
        if server_type is not None:
            self.server_type = server_type
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def instance_id(self):
        """Gets the instance_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The instance_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ResultForDescribeNLBListenerHealthOutput.


        :param instance_id: The instance_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def ip(self):
        """Gets the ip of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The ip of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ResultForDescribeNLBListenerHealthOutput.


        :param ip: The ip of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def port(self):
        """Gets the port of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The port of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this ResultForDescribeNLBListenerHealthOutput.


        :param port: The port of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def server_id(self):
        """Gets the server_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The server_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_id

    @server_id.setter
    def server_id(self, server_id):
        """Sets the server_id of this ResultForDescribeNLBListenerHealthOutput.


        :param server_id: The server_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._server_id = server_id

    @property
    def server_type(self):
        """Gets the server_type of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The server_type of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_type

    @server_type.setter
    def server_type(self, server_type):
        """Sets the server_type of this ResultForDescribeNLBListenerHealthOutput.


        :param server_type: The server_type of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._server_type = server_type

    @property
    def status(self):
        """Gets the status of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The status of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResultForDescribeNLBListenerHealthOutput.


        :param status: The status of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The update_time of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ResultForDescribeNLBListenerHealthOutput.


        :param update_time: The update_time of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def zone_id(self):
        """Gets the zone_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501


        :return: The zone_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ResultForDescribeNLBListenerHealthOutput.


        :param zone_id: The zone_id of this ResultForDescribeNLBListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResultForDescribeNLBListenerHealthOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResultForDescribeNLBListenerHealthOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResultForDescribeNLBListenerHealthOutput):
            return True

        return self.to_dict() != other.to_dict()
