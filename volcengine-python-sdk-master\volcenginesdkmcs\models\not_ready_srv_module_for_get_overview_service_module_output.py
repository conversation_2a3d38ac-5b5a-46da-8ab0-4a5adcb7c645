# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NotReadySrvModuleForGetOverviewServiceModuleOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'service_module_code': 'str',
        'service_module_name_cn': 'str',
        'status_statement': 'str'
    }

    attribute_map = {
        'service_module_code': 'ServiceModuleCode',
        'service_module_name_cn': 'ServiceModuleNameCN',
        'status_statement': 'StatusStatement'
    }

    def __init__(self, service_module_code=None, service_module_name_cn=None, status_statement=None, _configuration=None):  # noqa: E501
        """NotReadySrvModuleForGetOverviewServiceModuleOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._service_module_code = None
        self._service_module_name_cn = None
        self._status_statement = None
        self.discriminator = None

        if service_module_code is not None:
            self.service_module_code = service_module_code
        if service_module_name_cn is not None:
            self.service_module_name_cn = service_module_name_cn
        if status_statement is not None:
            self.status_statement = status_statement

    @property
    def service_module_code(self):
        """Gets the service_module_code of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501


        :return: The service_module_code of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_module_code

    @service_module_code.setter
    def service_module_code(self, service_module_code):
        """Sets the service_module_code of this NotReadySrvModuleForGetOverviewServiceModuleOutput.


        :param service_module_code: The service_module_code of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :type: str
        """

        self._service_module_code = service_module_code

    @property
    def service_module_name_cn(self):
        """Gets the service_module_name_cn of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501


        :return: The service_module_name_cn of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_module_name_cn

    @service_module_name_cn.setter
    def service_module_name_cn(self, service_module_name_cn):
        """Sets the service_module_name_cn of this NotReadySrvModuleForGetOverviewServiceModuleOutput.


        :param service_module_name_cn: The service_module_name_cn of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :type: str
        """

        self._service_module_name_cn = service_module_name_cn

    @property
    def status_statement(self):
        """Gets the status_statement of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501


        :return: The status_statement of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :rtype: str
        """
        return self._status_statement

    @status_statement.setter
    def status_statement(self, status_statement):
        """Sets the status_statement of this NotReadySrvModuleForGetOverviewServiceModuleOutput.


        :param status_statement: The status_statement of this NotReadySrvModuleForGetOverviewServiceModuleOutput.  # noqa: E501
        :type: str
        """

        self._status_statement = status_statement

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NotReadySrvModuleForGetOverviewServiceModuleOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NotReadySrvModuleForGetOverviewServiceModuleOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NotReadySrvModuleForGetOverviewServiceModuleOutput):
            return True

        return self.to_dict() != other.to_dict()
