# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyMultiLevelInstitutionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'app_sec': 'bool',
        'cluster_sec': 'bool',
        'expire_time': 'int',
        'id': 'str',
        'institution_id': 'str',
        'institution_name': 'str',
        'licenses': 'int',
        'remark': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'app_sec': 'AppSec',
        'cluster_sec': 'ClusterSec',
        'expire_time': 'ExpireTime',
        'id': 'ID',
        'institution_id': 'InstitutionID',
        'institution_name': 'InstitutionName',
        'licenses': 'Licenses',
        'remark': 'Remark'
    }

    def __init__(self, account_id=None, app_sec=None, cluster_sec=None, expire_time=None, id=None, institution_id=None, institution_name=None, licenses=None, remark=None, _configuration=None):  # noqa: E501
        """ModifyMultiLevelInstitutionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._app_sec = None
        self._cluster_sec = None
        self._expire_time = None
        self._id = None
        self._institution_id = None
        self._institution_name = None
        self._licenses = None
        self._remark = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if app_sec is not None:
            self.app_sec = app_sec
        if cluster_sec is not None:
            self.cluster_sec = cluster_sec
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if institution_id is not None:
            self.institution_id = institution_id
        if institution_name is not None:
            self.institution_name = institution_name
        if licenses is not None:
            self.licenses = licenses
        if remark is not None:
            self.remark = remark

    @property
    def account_id(self):
        """Gets the account_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The account_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ModifyMultiLevelInstitutionRequest.


        :param account_id: The account_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def app_sec(self):
        """Gets the app_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The app_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: bool
        """
        return self._app_sec

    @app_sec.setter
    def app_sec(self, app_sec):
        """Sets the app_sec of this ModifyMultiLevelInstitutionRequest.


        :param app_sec: The app_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: bool
        """

        self._app_sec = app_sec

    @property
    def cluster_sec(self):
        """Gets the cluster_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The cluster_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: bool
        """
        return self._cluster_sec

    @cluster_sec.setter
    def cluster_sec(self, cluster_sec):
        """Sets the cluster_sec of this ModifyMultiLevelInstitutionRequest.


        :param cluster_sec: The cluster_sec of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: bool
        """

        self._cluster_sec = cluster_sec

    @property
    def expire_time(self):
        """Gets the expire_time of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The expire_time of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ModifyMultiLevelInstitutionRequest.


        :param expire_time: The expire_time of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ModifyMultiLevelInstitutionRequest.


        :param id: The id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def institution_id(self):
        """Gets the institution_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The institution_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: str
        """
        return self._institution_id

    @institution_id.setter
    def institution_id(self, institution_id):
        """Sets the institution_id of this ModifyMultiLevelInstitutionRequest.


        :param institution_id: The institution_id of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: str
        """

        self._institution_id = institution_id

    @property
    def institution_name(self):
        """Gets the institution_name of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The institution_name of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: str
        """
        return self._institution_name

    @institution_name.setter
    def institution_name(self, institution_name):
        """Sets the institution_name of this ModifyMultiLevelInstitutionRequest.


        :param institution_name: The institution_name of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: str
        """

        self._institution_name = institution_name

    @property
    def licenses(self):
        """Gets the licenses of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The licenses of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: int
        """
        return self._licenses

    @licenses.setter
    def licenses(self, licenses):
        """Sets the licenses of this ModifyMultiLevelInstitutionRequest.


        :param licenses: The licenses of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: int
        """

        self._licenses = licenses

    @property
    def remark(self):
        """Gets the remark of this ModifyMultiLevelInstitutionRequest.  # noqa: E501


        :return: The remark of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this ModifyMultiLevelInstitutionRequest.


        :param remark: The remark of this ModifyMultiLevelInstitutionRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyMultiLevelInstitutionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyMultiLevelInstitutionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyMultiLevelInstitutionRequest):
            return True

        return self.to_dict() != other.to_dict()
