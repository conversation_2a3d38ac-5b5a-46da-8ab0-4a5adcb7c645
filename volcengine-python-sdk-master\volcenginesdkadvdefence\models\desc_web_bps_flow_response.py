# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebBpsFlowResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'back_src_bps_flow': 'list[BackSrcBpsFlowForDescWebBpsFlowOutput]'
    }

    attribute_map = {
        'back_src_bps_flow': 'BackSrcBpsFlow'
    }

    def __init__(self, back_src_bps_flow=None, _configuration=None):  # noqa: E501
        """DescWebBpsFlowResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._back_src_bps_flow = None
        self.discriminator = None

        if back_src_bps_flow is not None:
            self.back_src_bps_flow = back_src_bps_flow

    @property
    def back_src_bps_flow(self):
        """Gets the back_src_bps_flow of this DescWebBpsFlowResponse.  # noqa: E501


        :return: The back_src_bps_flow of this DescWebBpsFlowResponse.  # noqa: E501
        :rtype: list[BackSrcBpsFlowForDescWebBpsFlowOutput]
        """
        return self._back_src_bps_flow

    @back_src_bps_flow.setter
    def back_src_bps_flow(self, back_src_bps_flow):
        """Sets the back_src_bps_flow of this DescWebBpsFlowResponse.


        :param back_src_bps_flow: The back_src_bps_flow of this DescWebBpsFlowResponse.  # noqa: E501
        :type: list[BackSrcBpsFlowForDescWebBpsFlowOutput]
        """

        self._back_src_bps_flow = back_src_bps_flow

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebBpsFlowResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebBpsFlowResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebBpsFlowResponse):
            return True

        return self.to_dict() != other.to_dict()
