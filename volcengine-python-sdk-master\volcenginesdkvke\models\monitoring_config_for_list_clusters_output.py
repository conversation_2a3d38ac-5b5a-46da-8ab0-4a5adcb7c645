# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MonitoringConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'component_configs': 'list[ComponentConfigForListClustersOutput]',
        'workspace_id': 'str'
    }

    attribute_map = {
        'component_configs': 'ComponentConfigs',
        'workspace_id': 'WorkspaceId'
    }

    def __init__(self, component_configs=None, workspace_id=None, _configuration=None):  # noqa: E501
        """MonitoringConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._component_configs = None
        self._workspace_id = None
        self.discriminator = None

        if component_configs is not None:
            self.component_configs = component_configs
        if workspace_id is not None:
            self.workspace_id = workspace_id

    @property
    def component_configs(self):
        """Gets the component_configs of this MonitoringConfigForListClustersOutput.  # noqa: E501


        :return: The component_configs of this MonitoringConfigForListClustersOutput.  # noqa: E501
        :rtype: list[ComponentConfigForListClustersOutput]
        """
        return self._component_configs

    @component_configs.setter
    def component_configs(self, component_configs):
        """Sets the component_configs of this MonitoringConfigForListClustersOutput.


        :param component_configs: The component_configs of this MonitoringConfigForListClustersOutput.  # noqa: E501
        :type: list[ComponentConfigForListClustersOutput]
        """

        self._component_configs = component_configs

    @property
    def workspace_id(self):
        """Gets the workspace_id of this MonitoringConfigForListClustersOutput.  # noqa: E501


        :return: The workspace_id of this MonitoringConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this MonitoringConfigForListClustersOutput.


        :param workspace_id: The workspace_id of this MonitoringConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MonitoringConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MonitoringConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MonitoringConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
