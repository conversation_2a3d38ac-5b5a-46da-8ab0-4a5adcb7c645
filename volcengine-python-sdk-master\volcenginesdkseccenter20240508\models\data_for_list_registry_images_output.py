# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRegistryImagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'digest': 'str',
        'id': 'str',
        'image_id': 'str',
        'namespace': 'str',
        'push_time': 'int',
        'region': 'str',
        'registry_name': 'str',
        'registry_type': 'str',
        'repo': 'str',
        'scan': 'ScanForListRegistryImagesOutput',
        'scan_id': 'str',
        'scan_status': 'str',
        'scan_time': 'int',
        'size': 'int',
        'tag': 'str'
    }

    attribute_map = {
        'digest': 'Digest',
        'id': 'ID',
        'image_id': 'ImageID',
        'namespace': 'Namespace',
        'push_time': 'PushTime',
        'region': 'Region',
        'registry_name': 'RegistryName',
        'registry_type': 'RegistryType',
        'repo': 'Repo',
        'scan': 'Scan',
        'scan_id': 'ScanID',
        'scan_status': 'ScanStatus',
        'scan_time': 'ScanTime',
        'size': 'Size',
        'tag': 'Tag'
    }

    def __init__(self, digest=None, id=None, image_id=None, namespace=None, push_time=None, region=None, registry_name=None, registry_type=None, repo=None, scan=None, scan_id=None, scan_status=None, scan_time=None, size=None, tag=None, _configuration=None):  # noqa: E501
        """DataForListRegistryImagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._digest = None
        self._id = None
        self._image_id = None
        self._namespace = None
        self._push_time = None
        self._region = None
        self._registry_name = None
        self._registry_type = None
        self._repo = None
        self._scan = None
        self._scan_id = None
        self._scan_status = None
        self._scan_time = None
        self._size = None
        self._tag = None
        self.discriminator = None

        if digest is not None:
            self.digest = digest
        if id is not None:
            self.id = id
        if image_id is not None:
            self.image_id = image_id
        if namespace is not None:
            self.namespace = namespace
        if push_time is not None:
            self.push_time = push_time
        if region is not None:
            self.region = region
        if registry_name is not None:
            self.registry_name = registry_name
        if registry_type is not None:
            self.registry_type = registry_type
        if repo is not None:
            self.repo = repo
        if scan is not None:
            self.scan = scan
        if scan_id is not None:
            self.scan_id = scan_id
        if scan_status is not None:
            self.scan_status = scan_status
        if scan_time is not None:
            self.scan_time = scan_time
        if size is not None:
            self.size = size
        if tag is not None:
            self.tag = tag

    @property
    def digest(self):
        """Gets the digest of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The digest of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._digest

    @digest.setter
    def digest(self, digest):
        """Sets the digest of this DataForListRegistryImagesOutput.


        :param digest: The digest of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._digest = digest

    @property
    def id(self):
        """Gets the id of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The id of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRegistryImagesOutput.


        :param id: The id of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def image_id(self):
        """Gets the image_id of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The image_id of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this DataForListRegistryImagesOutput.


        :param image_id: The image_id of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def namespace(self):
        """Gets the namespace of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The namespace of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListRegistryImagesOutput.


        :param namespace: The namespace of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def push_time(self):
        """Gets the push_time of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The push_time of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._push_time

    @push_time.setter
    def push_time(self, push_time):
        """Sets the push_time of this DataForListRegistryImagesOutput.


        :param push_time: The push_time of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: int
        """

        self._push_time = push_time

    @property
    def region(self):
        """Gets the region of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The region of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListRegistryImagesOutput.


        :param region: The region of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def registry_name(self):
        """Gets the registry_name of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The registry_name of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_name

    @registry_name.setter
    def registry_name(self, registry_name):
        """Sets the registry_name of this DataForListRegistryImagesOutput.


        :param registry_name: The registry_name of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._registry_name = registry_name

    @property
    def registry_type(self):
        """Gets the registry_type of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The registry_type of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_type

    @registry_type.setter
    def registry_type(self, registry_type):
        """Sets the registry_type of this DataForListRegistryImagesOutput.


        :param registry_type: The registry_type of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._registry_type = registry_type

    @property
    def repo(self):
        """Gets the repo of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The repo of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._repo

    @repo.setter
    def repo(self, repo):
        """Sets the repo of this DataForListRegistryImagesOutput.


        :param repo: The repo of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._repo = repo

    @property
    def scan(self):
        """Gets the scan of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The scan of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: ScanForListRegistryImagesOutput
        """
        return self._scan

    @scan.setter
    def scan(self, scan):
        """Sets the scan of this DataForListRegistryImagesOutput.


        :param scan: The scan of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: ScanForListRegistryImagesOutput
        """

        self._scan = scan

    @property
    def scan_id(self):
        """Gets the scan_id of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The scan_id of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scan_id

    @scan_id.setter
    def scan_id(self, scan_id):
        """Sets the scan_id of this DataForListRegistryImagesOutput.


        :param scan_id: The scan_id of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._scan_id = scan_id

    @property
    def scan_status(self):
        """Gets the scan_status of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The scan_status of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scan_status

    @scan_status.setter
    def scan_status(self, scan_status):
        """Sets the scan_status of this DataForListRegistryImagesOutput.


        :param scan_status: The scan_status of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._scan_status = scan_status

    @property
    def scan_time(self):
        """Gets the scan_time of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The scan_time of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._scan_time

    @scan_time.setter
    def scan_time(self, scan_time):
        """Sets the scan_time of this DataForListRegistryImagesOutput.


        :param scan_time: The scan_time of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: int
        """

        self._scan_time = scan_time

    @property
    def size(self):
        """Gets the size of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The size of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this DataForListRegistryImagesOutput.


        :param size: The size of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def tag(self):
        """Gets the tag of this DataForListRegistryImagesOutput.  # noqa: E501


        :return: The tag of this DataForListRegistryImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this DataForListRegistryImagesOutput.


        :param tag: The tag of this DataForListRegistryImagesOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRegistryImagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRegistryImagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRegistryImagesOutput):
            return True

        return self.to_dict() != other.to_dict()
