# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListGatewaysInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ids': 'list[str]',
        'name': 'str',
        'status': 'str',
        'tags': 'list[TagForListGatewaysInput]',
        'type': 'str',
        'vpc_ids': 'list[str]'
    }

    attribute_map = {
        'ids': 'Ids',
        'name': 'Name',
        'status': 'Status',
        'tags': 'Tags',
        'type': 'Type',
        'vpc_ids': 'VpcIds'
    }

    def __init__(self, ids=None, name=None, status=None, tags=None, type=None, vpc_ids=None, _configuration=None):  # noqa: E501
        """FilterForListGatewaysInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ids = None
        self._name = None
        self._status = None
        self._tags = None
        self._type = None
        self._vpc_ids = None
        self.discriminator = None

        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type
        if vpc_ids is not None:
            self.vpc_ids = vpc_ids

    @property
    def ids(self):
        """Gets the ids of this FilterForListGatewaysInput.  # noqa: E501


        :return: The ids of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListGatewaysInput.


        :param ids: The ids of this FilterForListGatewaysInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListGatewaysInput.  # noqa: E501


        :return: The name of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListGatewaysInput.


        :param name: The name of this FilterForListGatewaysInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this FilterForListGatewaysInput.  # noqa: E501


        :return: The status of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListGatewaysInput.


        :param status: The status of this FilterForListGatewaysInput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this FilterForListGatewaysInput.  # noqa: E501


        :return: The tags of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: list[TagForListGatewaysInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this FilterForListGatewaysInput.


        :param tags: The tags of this FilterForListGatewaysInput.  # noqa: E501
        :type: list[TagForListGatewaysInput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this FilterForListGatewaysInput.  # noqa: E501


        :return: The type of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this FilterForListGatewaysInput.


        :param type: The type of this FilterForListGatewaysInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def vpc_ids(self):
        """Gets the vpc_ids of this FilterForListGatewaysInput.  # noqa: E501


        :return: The vpc_ids of this FilterForListGatewaysInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_ids

    @vpc_ids.setter
    def vpc_ids(self, vpc_ids):
        """Sets the vpc_ids of this FilterForListGatewaysInput.


        :param vpc_ids: The vpc_ids of this FilterForListGatewaysInput.  # noqa: E501
        :type: list[str]
        """

        self._vpc_ids = vpc_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListGatewaysInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListGatewaysInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListGatewaysInput):
            return True

        return self.to_dict() != other.to_dict()
