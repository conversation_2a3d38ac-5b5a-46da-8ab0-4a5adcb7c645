# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'dns_config': 'DNSConfigForCreateTaskInput',
        'diagnose_config': 'DiagnoseConfigForCreateTaskInput',
        'finish_time': 'int',
        'http_config': 'HTTPConfigForCreateTaskInput',
        'interval_seconds': 'int',
        'line_id_list': 'list[int]',
        'name': 'str',
        'node_count': 'int',
        'period_config': 'PeriodConfigForCreateTaskInput',
        'ping_config': 'PingConfigForCreateTaskInput',
        'project_name': 'str',
        'tcp_config': 'TCPConfigForCreateTaskInput',
        'type': 'int',
        'udp_config': 'UDPConfigForCreateTaskInput'
    }

    attribute_map = {
        'address': 'Address',
        'dns_config': 'DNSConfig',
        'diagnose_config': 'DiagnoseConfig',
        'finish_time': 'FinishTime',
        'http_config': 'HTTPConfig',
        'interval_seconds': 'IntervalSeconds',
        'line_id_list': 'LineIDList',
        'name': 'Name',
        'node_count': 'NodeCount',
        'period_config': 'PeriodConfig',
        'ping_config': 'PingConfig',
        'project_name': 'ProjectName',
        'tcp_config': 'TCPConfig',
        'type': 'Type',
        'udp_config': 'UDPConfig'
    }

    def __init__(self, address=None, dns_config=None, diagnose_config=None, finish_time=None, http_config=None, interval_seconds=None, line_id_list=None, name=None, node_count=None, period_config=None, ping_config=None, project_name=None, tcp_config=None, type=None, udp_config=None, _configuration=None):  # noqa: E501
        """CreateTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._dns_config = None
        self._diagnose_config = None
        self._finish_time = None
        self._http_config = None
        self._interval_seconds = None
        self._line_id_list = None
        self._name = None
        self._node_count = None
        self._period_config = None
        self._ping_config = None
        self._project_name = None
        self._tcp_config = None
        self._type = None
        self._udp_config = None
        self.discriminator = None

        self.address = address
        if dns_config is not None:
            self.dns_config = dns_config
        if diagnose_config is not None:
            self.diagnose_config = diagnose_config
        self.finish_time = finish_time
        if http_config is not None:
            self.http_config = http_config
        self.interval_seconds = interval_seconds
        if line_id_list is not None:
            self.line_id_list = line_id_list
        self.name = name
        self.node_count = node_count
        if period_config is not None:
            self.period_config = period_config
        if ping_config is not None:
            self.ping_config = ping_config
        if project_name is not None:
            self.project_name = project_name
        if tcp_config is not None:
            self.tcp_config = tcp_config
        self.type = type
        if udp_config is not None:
            self.udp_config = udp_config

    @property
    def address(self):
        """Gets the address of this CreateTaskRequest.  # noqa: E501


        :return: The address of this CreateTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this CreateTaskRequest.


        :param address: The address of this CreateTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and address is None:
            raise ValueError("Invalid value for `address`, must not be `None`")  # noqa: E501

        self._address = address

    @property
    def dns_config(self):
        """Gets the dns_config of this CreateTaskRequest.  # noqa: E501


        :return: The dns_config of this CreateTaskRequest.  # noqa: E501
        :rtype: DNSConfigForCreateTaskInput
        """
        return self._dns_config

    @dns_config.setter
    def dns_config(self, dns_config):
        """Sets the dns_config of this CreateTaskRequest.


        :param dns_config: The dns_config of this CreateTaskRequest.  # noqa: E501
        :type: DNSConfigForCreateTaskInput
        """

        self._dns_config = dns_config

    @property
    def diagnose_config(self):
        """Gets the diagnose_config of this CreateTaskRequest.  # noqa: E501


        :return: The diagnose_config of this CreateTaskRequest.  # noqa: E501
        :rtype: DiagnoseConfigForCreateTaskInput
        """
        return self._diagnose_config

    @diagnose_config.setter
    def diagnose_config(self, diagnose_config):
        """Sets the diagnose_config of this CreateTaskRequest.


        :param diagnose_config: The diagnose_config of this CreateTaskRequest.  # noqa: E501
        :type: DiagnoseConfigForCreateTaskInput
        """

        self._diagnose_config = diagnose_config

    @property
    def finish_time(self):
        """Gets the finish_time of this CreateTaskRequest.  # noqa: E501


        :return: The finish_time of this CreateTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this CreateTaskRequest.


        :param finish_time: The finish_time of this CreateTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and finish_time is None:
            raise ValueError("Invalid value for `finish_time`, must not be `None`")  # noqa: E501

        self._finish_time = finish_time

    @property
    def http_config(self):
        """Gets the http_config of this CreateTaskRequest.  # noqa: E501


        :return: The http_config of this CreateTaskRequest.  # noqa: E501
        :rtype: HTTPConfigForCreateTaskInput
        """
        return self._http_config

    @http_config.setter
    def http_config(self, http_config):
        """Sets the http_config of this CreateTaskRequest.


        :param http_config: The http_config of this CreateTaskRequest.  # noqa: E501
        :type: HTTPConfigForCreateTaskInput
        """

        self._http_config = http_config

    @property
    def interval_seconds(self):
        """Gets the interval_seconds of this CreateTaskRequest.  # noqa: E501


        :return: The interval_seconds of this CreateTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._interval_seconds

    @interval_seconds.setter
    def interval_seconds(self, interval_seconds):
        """Sets the interval_seconds of this CreateTaskRequest.


        :param interval_seconds: The interval_seconds of this CreateTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and interval_seconds is None:
            raise ValueError("Invalid value for `interval_seconds`, must not be `None`")  # noqa: E501

        self._interval_seconds = interval_seconds

    @property
    def line_id_list(self):
        """Gets the line_id_list of this CreateTaskRequest.  # noqa: E501


        :return: The line_id_list of this CreateTaskRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._line_id_list

    @line_id_list.setter
    def line_id_list(self, line_id_list):
        """Sets the line_id_list of this CreateTaskRequest.


        :param line_id_list: The line_id_list of this CreateTaskRequest.  # noqa: E501
        :type: list[int]
        """

        self._line_id_list = line_id_list

    @property
    def name(self):
        """Gets the name of this CreateTaskRequest.  # noqa: E501


        :return: The name of this CreateTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateTaskRequest.


        :param name: The name of this CreateTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def node_count(self):
        """Gets the node_count of this CreateTaskRequest.  # noqa: E501


        :return: The node_count of this CreateTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._node_count

    @node_count.setter
    def node_count(self, node_count):
        """Sets the node_count of this CreateTaskRequest.


        :param node_count: The node_count of this CreateTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and node_count is None:
            raise ValueError("Invalid value for `node_count`, must not be `None`")  # noqa: E501

        self._node_count = node_count

    @property
    def period_config(self):
        """Gets the period_config of this CreateTaskRequest.  # noqa: E501


        :return: The period_config of this CreateTaskRequest.  # noqa: E501
        :rtype: PeriodConfigForCreateTaskInput
        """
        return self._period_config

    @period_config.setter
    def period_config(self, period_config):
        """Sets the period_config of this CreateTaskRequest.


        :param period_config: The period_config of this CreateTaskRequest.  # noqa: E501
        :type: PeriodConfigForCreateTaskInput
        """

        self._period_config = period_config

    @property
    def ping_config(self):
        """Gets the ping_config of this CreateTaskRequest.  # noqa: E501


        :return: The ping_config of this CreateTaskRequest.  # noqa: E501
        :rtype: PingConfigForCreateTaskInput
        """
        return self._ping_config

    @ping_config.setter
    def ping_config(self, ping_config):
        """Sets the ping_config of this CreateTaskRequest.


        :param ping_config: The ping_config of this CreateTaskRequest.  # noqa: E501
        :type: PingConfigForCreateTaskInput
        """

        self._ping_config = ping_config

    @property
    def project_name(self):
        """Gets the project_name of this CreateTaskRequest.  # noqa: E501


        :return: The project_name of this CreateTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateTaskRequest.


        :param project_name: The project_name of this CreateTaskRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tcp_config(self):
        """Gets the tcp_config of this CreateTaskRequest.  # noqa: E501


        :return: The tcp_config of this CreateTaskRequest.  # noqa: E501
        :rtype: TCPConfigForCreateTaskInput
        """
        return self._tcp_config

    @tcp_config.setter
    def tcp_config(self, tcp_config):
        """Sets the tcp_config of this CreateTaskRequest.


        :param tcp_config: The tcp_config of this CreateTaskRequest.  # noqa: E501
        :type: TCPConfigForCreateTaskInput
        """

        self._tcp_config = tcp_config

    @property
    def type(self):
        """Gets the type of this CreateTaskRequest.  # noqa: E501


        :return: The type of this CreateTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateTaskRequest.


        :param type: The type of this CreateTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def udp_config(self):
        """Gets the udp_config of this CreateTaskRequest.  # noqa: E501


        :return: The udp_config of this CreateTaskRequest.  # noqa: E501
        :rtype: UDPConfigForCreateTaskInput
        """
        return self._udp_config

    @udp_config.setter
    def udp_config(self, udp_config):
        """Sets the udp_config of this CreateTaskRequest.


        :param udp_config: The udp_config of this CreateTaskRequest.  # noqa: E501
        :type: UDPConfigForCreateTaskInput
        """

        self._udp_config = udp_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
