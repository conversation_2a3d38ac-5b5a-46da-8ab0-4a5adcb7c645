# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class JoinRedPacketListForListRedPacketRecordAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amount': 'int',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'is_priority_user': 'int',
        'phone_number': 'str',
        'status': 'int',
        'user_agent': 'str',
        'user_id': 'int',
        'user_name': 'str',
        'user_tel': 'str',
        'wechat_name': 'str',
        'wechat_open_id': 'str',
        'withdrawal_status': 'str'
    }

    attribute_map = {
        'amount': 'Amount',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'is_priority_user': 'IsPriorityUser',
        'phone_number': 'PhoneNumber',
        'status': 'Status',
        'user_agent': 'UserAgent',
        'user_id': 'UserID',
        'user_name': 'UserName',
        'user_tel': 'UserTel',
        'wechat_name': 'WechatName',
        'wechat_open_id': 'WechatOpenID',
        'withdrawal_status': 'WithdrawalStatus'
    }

    def __init__(self, amount=None, email=None, external_id=None, extra=None, ip=None, is_priority_user=None, phone_number=None, status=None, user_agent=None, user_id=None, user_name=None, user_tel=None, wechat_name=None, wechat_open_id=None, withdrawal_status=None, _configuration=None):  # noqa: E501
        """JoinRedPacketListForListRedPacketRecordAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._amount = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._is_priority_user = None
        self._phone_number = None
        self._status = None
        self._user_agent = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self._wechat_name = None
        self._wechat_open_id = None
        self._withdrawal_status = None
        self.discriminator = None

        if amount is not None:
            self.amount = amount
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if is_priority_user is not None:
            self.is_priority_user = is_priority_user
        if phone_number is not None:
            self.phone_number = phone_number
        if status is not None:
            self.status = status
        if user_agent is not None:
            self.user_agent = user_agent
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel
        if wechat_name is not None:
            self.wechat_name = wechat_name
        if wechat_open_id is not None:
            self.wechat_open_id = wechat_open_id
        if withdrawal_status is not None:
            self.withdrawal_status = withdrawal_status

    @property
    def amount(self):
        """Gets the amount of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The amount of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param amount: The amount of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._amount = amount

    @property
    def email(self):
        """Gets the email of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The email of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param email: The email of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The external_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param external_id: The external_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The extra of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param extra: The extra of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The ip of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param ip: The ip of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_priority_user(self):
        """Gets the is_priority_user of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The is_priority_user of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_priority_user

    @is_priority_user.setter
    def is_priority_user(self, is_priority_user):
        """Sets the is_priority_user of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param is_priority_user: The is_priority_user of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_priority_user = is_priority_user

    @property
    def phone_number(self):
        """Gets the phone_number of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The phone_number of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._phone_number

    @phone_number.setter
    def phone_number(self, phone_number):
        """Sets the phone_number of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param phone_number: The phone_number of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._phone_number = phone_number

    @property
    def status(self):
        """Gets the status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param status: The status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def user_agent(self):
        """Gets the user_agent of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The user_agent of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param user_agent: The user_agent of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_id(self):
        """Gets the user_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The user_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param user_id: The user_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The user_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param user_name: The user_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The user_tel of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param user_tel: The user_tel of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    @property
    def wechat_name(self):
        """Gets the wechat_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The wechat_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._wechat_name

    @wechat_name.setter
    def wechat_name(self, wechat_name):
        """Sets the wechat_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param wechat_name: The wechat_name of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._wechat_name = wechat_name

    @property
    def wechat_open_id(self):
        """Gets the wechat_open_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The wechat_open_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._wechat_open_id

    @wechat_open_id.setter
    def wechat_open_id(self, wechat_open_id):
        """Sets the wechat_open_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param wechat_open_id: The wechat_open_id of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._wechat_open_id = wechat_open_id

    @property
    def withdrawal_status(self):
        """Gets the withdrawal_status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501


        :return: The withdrawal_status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._withdrawal_status

    @withdrawal_status.setter
    def withdrawal_status(self, withdrawal_status):
        """Sets the withdrawal_status of this JoinRedPacketListForListRedPacketRecordAPIOutput.


        :param withdrawal_status: The withdrawal_status of this JoinRedPacketListForListRedPacketRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._withdrawal_status = withdrawal_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(JoinRedPacketListForListRedPacketRecordAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, JoinRedPacketListForListRedPacketRecordAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, JoinRedPacketListForListRedPacketRecordAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
