# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicInfoForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_list_version': 'str',
        'auto_upgrade_minor_version': 'str',
        'backup_free_quota_size': 'int',
        'backup_use': 'float',
        'basic_backup_binlog_size': 'int',
        'basic_backup_data_size': 'int',
        'create_time': 'str',
        'current_kernel_version': 'str',
        'db_engine_version': 'str',
        'data_sync_mode': 'str',
        'deletion_protection': 'str',
        'dr_dts_task_id': 'str',
        'dr_dts_task_name': 'str',
        'dr_dts_task_status': 'str',
        'dr_seconds_behind_master': 'int',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'lower_case_table_names': 'str',
        'maintenance_window': 'MaintenanceWindowForDescribeDBInstanceDetailOutput',
        'master_instance_id': 'str',
        'master_instance_name': 'str',
        'master_region': 'str',
        'memory': 'int',
        'node_number': 'str',
        'node_spec': 'str',
        'project_name': 'str',
        'region_id': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'storage_use': 'float',
        'subnet_id': 'str',
        'tags': 'list[TagForDescribeDBInstanceDetailOutput]',
        'time_zone': 'str',
        'update_time': 'str',
        'vcpu': 'int',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'allow_list_version': 'AllowListVersion',
        'auto_upgrade_minor_version': 'AutoUpgradeMinorVersion',
        'backup_free_quota_size': 'BackupFreeQuotaSize',
        'backup_use': 'BackupUse',
        'basic_backup_binlog_size': 'BasicBackupBinlogSize',
        'basic_backup_data_size': 'BasicBackupDataSize',
        'create_time': 'CreateTime',
        'current_kernel_version': 'CurrentKernelVersion',
        'db_engine_version': 'DBEngineVersion',
        'data_sync_mode': 'DataSyncMode',
        'deletion_protection': 'DeletionProtection',
        'dr_dts_task_id': 'DrDtsTaskId',
        'dr_dts_task_name': 'DrDtsTaskName',
        'dr_dts_task_status': 'DrDtsTaskStatus',
        'dr_seconds_behind_master': 'DrSecondsBehindMaster',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'lower_case_table_names': 'LowerCaseTableNames',
        'maintenance_window': 'MaintenanceWindow',
        'master_instance_id': 'MasterInstanceId',
        'master_instance_name': 'MasterInstanceName',
        'master_region': 'MasterRegion',
        'memory': 'Memory',
        'node_number': 'NodeNumber',
        'node_spec': 'NodeSpec',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'storage_use': 'StorageUse',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'time_zone': 'TimeZone',
        'update_time': 'UpdateTime',
        'vcpu': 'VCPU',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, allow_list_version=None, auto_upgrade_minor_version=None, backup_free_quota_size=None, backup_use=None, basic_backup_binlog_size=None, basic_backup_data_size=None, create_time=None, current_kernel_version=None, db_engine_version=None, data_sync_mode=None, deletion_protection=None, dr_dts_task_id=None, dr_dts_task_name=None, dr_dts_task_status=None, dr_seconds_behind_master=None, instance_id=None, instance_name=None, instance_status=None, instance_type=None, lower_case_table_names=None, maintenance_window=None, master_instance_id=None, master_instance_name=None, master_region=None, memory=None, node_number=None, node_spec=None, project_name=None, region_id=None, storage_space=None, storage_type=None, storage_use=None, subnet_id=None, tags=None, time_zone=None, update_time=None, vcpu=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """BasicInfoForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_list_version = None
        self._auto_upgrade_minor_version = None
        self._backup_free_quota_size = None
        self._backup_use = None
        self._basic_backup_binlog_size = None
        self._basic_backup_data_size = None
        self._create_time = None
        self._current_kernel_version = None
        self._db_engine_version = None
        self._data_sync_mode = None
        self._deletion_protection = None
        self._dr_dts_task_id = None
        self._dr_dts_task_name = None
        self._dr_dts_task_status = None
        self._dr_seconds_behind_master = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_type = None
        self._lower_case_table_names = None
        self._maintenance_window = None
        self._master_instance_id = None
        self._master_instance_name = None
        self._master_region = None
        self._memory = None
        self._node_number = None
        self._node_spec = None
        self._project_name = None
        self._region_id = None
        self._storage_space = None
        self._storage_type = None
        self._storage_use = None
        self._subnet_id = None
        self._tags = None
        self._time_zone = None
        self._update_time = None
        self._vcpu = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if allow_list_version is not None:
            self.allow_list_version = allow_list_version
        if auto_upgrade_minor_version is not None:
            self.auto_upgrade_minor_version = auto_upgrade_minor_version
        if backup_free_quota_size is not None:
            self.backup_free_quota_size = backup_free_quota_size
        if backup_use is not None:
            self.backup_use = backup_use
        if basic_backup_binlog_size is not None:
            self.basic_backup_binlog_size = basic_backup_binlog_size
        if basic_backup_data_size is not None:
            self.basic_backup_data_size = basic_backup_data_size
        if create_time is not None:
            self.create_time = create_time
        if current_kernel_version is not None:
            self.current_kernel_version = current_kernel_version
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if data_sync_mode is not None:
            self.data_sync_mode = data_sync_mode
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if dr_dts_task_id is not None:
            self.dr_dts_task_id = dr_dts_task_id
        if dr_dts_task_name is not None:
            self.dr_dts_task_name = dr_dts_task_name
        if dr_dts_task_status is not None:
            self.dr_dts_task_status = dr_dts_task_status
        if dr_seconds_behind_master is not None:
            self.dr_seconds_behind_master = dr_seconds_behind_master
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if lower_case_table_names is not None:
            self.lower_case_table_names = lower_case_table_names
        if maintenance_window is not None:
            self.maintenance_window = maintenance_window
        if master_instance_id is not None:
            self.master_instance_id = master_instance_id
        if master_instance_name is not None:
            self.master_instance_name = master_instance_name
        if master_region is not None:
            self.master_region = master_region
        if memory is not None:
            self.memory = memory
        if node_number is not None:
            self.node_number = node_number
        if node_spec is not None:
            self.node_spec = node_spec
        if project_name is not None:
            self.project_name = project_name
        if region_id is not None:
            self.region_id = region_id
        if storage_space is not None:
            self.storage_space = storage_space
        if storage_type is not None:
            self.storage_type = storage_type
        if storage_use is not None:
            self.storage_use = storage_use
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if time_zone is not None:
            self.time_zone = time_zone
        if update_time is not None:
            self.update_time = update_time
        if vcpu is not None:
            self.vcpu = vcpu
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def allow_list_version(self):
        """Gets the allow_list_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The allow_list_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_version

    @allow_list_version.setter
    def allow_list_version(self, allow_list_version):
        """Sets the allow_list_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param allow_list_version: The allow_list_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_version = allow_list_version

    @property
    def auto_upgrade_minor_version(self):
        """Gets the auto_upgrade_minor_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The auto_upgrade_minor_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_upgrade_minor_version

    @auto_upgrade_minor_version.setter
    def auto_upgrade_minor_version(self, auto_upgrade_minor_version):
        """Sets the auto_upgrade_minor_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param auto_upgrade_minor_version: The auto_upgrade_minor_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._auto_upgrade_minor_version = auto_upgrade_minor_version

    @property
    def backup_free_quota_size(self):
        """Gets the backup_free_quota_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The backup_free_quota_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._backup_free_quota_size

    @backup_free_quota_size.setter
    def backup_free_quota_size(self, backup_free_quota_size):
        """Sets the backup_free_quota_size of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param backup_free_quota_size: The backup_free_quota_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._backup_free_quota_size = backup_free_quota_size

    @property
    def backup_use(self):
        """Gets the backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._backup_use

    @backup_use.setter
    def backup_use(self, backup_use):
        """Sets the backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param backup_use: The backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: float
        """

        self._backup_use = backup_use

    @property
    def basic_backup_binlog_size(self):
        """Gets the basic_backup_binlog_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The basic_backup_binlog_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._basic_backup_binlog_size

    @basic_backup_binlog_size.setter
    def basic_backup_binlog_size(self, basic_backup_binlog_size):
        """Sets the basic_backup_binlog_size of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param basic_backup_binlog_size: The basic_backup_binlog_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._basic_backup_binlog_size = basic_backup_binlog_size

    @property
    def basic_backup_data_size(self):
        """Gets the basic_backup_data_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The basic_backup_data_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._basic_backup_data_size

    @basic_backup_data_size.setter
    def basic_backup_data_size(self, basic_backup_data_size):
        """Sets the basic_backup_data_size of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param basic_backup_data_size: The basic_backup_data_size of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._basic_backup_data_size = basic_backup_data_size

    @property
    def create_time(self):
        """Gets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def current_kernel_version(self):
        """Gets the current_kernel_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The current_kernel_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._current_kernel_version

    @current_kernel_version.setter
    def current_kernel_version(self, current_kernel_version):
        """Sets the current_kernel_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param current_kernel_version: The current_kernel_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._current_kernel_version = current_kernel_version

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param db_engine_version: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def data_sync_mode(self):
        """Gets the data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_sync_mode

    @data_sync_mode.setter
    def data_sync_mode(self, data_sync_mode):
        """Sets the data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param data_sync_mode: The data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._data_sync_mode = data_sync_mode

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The deletion_protection of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param deletion_protection: The deletion_protection of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._deletion_protection = deletion_protection

    @property
    def dr_dts_task_id(self):
        """Gets the dr_dts_task_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dr_dts_task_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dr_dts_task_id

    @dr_dts_task_id.setter
    def dr_dts_task_id(self, dr_dts_task_id):
        """Sets the dr_dts_task_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param dr_dts_task_id: The dr_dts_task_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dr_dts_task_id = dr_dts_task_id

    @property
    def dr_dts_task_name(self):
        """Gets the dr_dts_task_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dr_dts_task_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dr_dts_task_name

    @dr_dts_task_name.setter
    def dr_dts_task_name(self, dr_dts_task_name):
        """Sets the dr_dts_task_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param dr_dts_task_name: The dr_dts_task_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dr_dts_task_name = dr_dts_task_name

    @property
    def dr_dts_task_status(self):
        """Gets the dr_dts_task_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dr_dts_task_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dr_dts_task_status

    @dr_dts_task_status.setter
    def dr_dts_task_status(self, dr_dts_task_status):
        """Sets the dr_dts_task_status of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param dr_dts_task_status: The dr_dts_task_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dr_dts_task_status = dr_dts_task_status

    @property
    def dr_seconds_behind_master(self):
        """Gets the dr_seconds_behind_master of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dr_seconds_behind_master of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._dr_seconds_behind_master

    @dr_seconds_behind_master.setter
    def dr_seconds_behind_master(self, dr_seconds_behind_master):
        """Sets the dr_seconds_behind_master of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param dr_seconds_behind_master: The dr_seconds_behind_master of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._dr_seconds_behind_master = dr_seconds_behind_master

    @property
    def instance_id(self):
        """Gets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_status: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_type: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def lower_case_table_names(self):
        """Gets the lower_case_table_names of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The lower_case_table_names of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._lower_case_table_names

    @lower_case_table_names.setter
    def lower_case_table_names(self, lower_case_table_names):
        """Sets the lower_case_table_names of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param lower_case_table_names: The lower_case_table_names of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._lower_case_table_names = lower_case_table_names

    @property
    def maintenance_window(self):
        """Gets the maintenance_window of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The maintenance_window of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: MaintenanceWindowForDescribeDBInstanceDetailOutput
        """
        return self._maintenance_window

    @maintenance_window.setter
    def maintenance_window(self, maintenance_window):
        """Sets the maintenance_window of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param maintenance_window: The maintenance_window of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: MaintenanceWindowForDescribeDBInstanceDetailOutput
        """

        self._maintenance_window = maintenance_window

    @property
    def master_instance_id(self):
        """Gets the master_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The master_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._master_instance_id

    @master_instance_id.setter
    def master_instance_id(self, master_instance_id):
        """Sets the master_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param master_instance_id: The master_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._master_instance_id = master_instance_id

    @property
    def master_instance_name(self):
        """Gets the master_instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The master_instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._master_instance_name

    @master_instance_name.setter
    def master_instance_name(self, master_instance_name):
        """Sets the master_instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param master_instance_name: The master_instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._master_instance_name = master_instance_name

    @property
    def master_region(self):
        """Gets the master_region of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The master_region of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._master_region

    @master_region.setter
    def master_region(self, master_region):
        """Sets the master_region of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param master_region: The master_region of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._master_region = master_region

    @property
    def memory(self):
        """Gets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param memory: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def node_number(self):
        """Gets the node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_number

    @node_number.setter
    def node_number(self, node_number):
        """Sets the node_number of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param node_number: The node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_number = node_number

    @property
    def node_spec(self):
        """Gets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param node_spec: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_spec = node_spec

    @property
    def project_name(self):
        """Gets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param project_name: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param region_id: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def storage_space(self):
        """Gets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_space: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_type: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    @property
    def storage_use(self):
        """Gets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._storage_use

    @storage_use.setter
    def storage_use(self, storage_use):
        """Sets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_use: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: float
        """

        self._storage_use = storage_use

    @property
    def subnet_id(self):
        """Gets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param subnet_id: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[TagForDescribeDBInstanceDetailOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param tags: The tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[TagForDescribeDBInstanceDetailOutput]
        """

        self._tags = tags

    @property
    def time_zone(self):
        """Gets the time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param time_zone: The time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    @property
    def update_time(self):
        """Gets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param update_time: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vcpu(self):
        """Gets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vcpu: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def vpc_id(self):
        """Gets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vpc_id: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param zone_id: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicInfoForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
