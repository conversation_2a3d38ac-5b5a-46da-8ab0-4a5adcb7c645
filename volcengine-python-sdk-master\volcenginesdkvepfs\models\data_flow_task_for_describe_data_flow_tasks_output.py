# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataFlowTaskForDescribeDataFlowTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'data_flow_task_id': 'str',
        'data_storage': 'str',
        'data_storage_path': 'str',
        'data_type': 'str',
        'end_time': 'str',
        'entry_list_file_info': 'EntryListFileInfoForDescribeDataFlowTasksOutput',
        'entry_list_file_url': 'str',
        'exec_count': 'str',
        'exec_size': 'str',
        'export_symlink_policy': 'str',
        'failed_count': 'str',
        'file_system_id': 'str',
        'fileset_id': 'str',
        'fileset_path': 'str',
        'queue_count': 'str',
        'reports': 'list[ReportForDescribeDataFlowTasksOutput]',
        'same_name_file_policy': 'str',
        'start_time': 'str',
        'status': 'str',
        'sub_path': 'str',
        'task_action': 'str',
        'total_size': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'data_flow_task_id': 'DataFlowTaskId',
        'data_storage': 'DataStorage',
        'data_storage_path': 'DataStoragePath',
        'data_type': 'DataType',
        'end_time': 'EndTime',
        'entry_list_file_info': 'EntryListFileInfo',
        'entry_list_file_url': 'EntryListFileUrl',
        'exec_count': 'ExecCount',
        'exec_size': 'ExecSize',
        'export_symlink_policy': 'ExportSymlinkPolicy',
        'failed_count': 'FailedCount',
        'file_system_id': 'FileSystemId',
        'fileset_id': 'FilesetId',
        'fileset_path': 'FilesetPath',
        'queue_count': 'QueueCount',
        'reports': 'Reports',
        'same_name_file_policy': 'SameNameFilePolicy',
        'start_time': 'StartTime',
        'status': 'Status',
        'sub_path': 'SubPath',
        'task_action': 'TaskAction',
        'total_size': 'TotalSize',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, data_flow_task_id=None, data_storage=None, data_storage_path=None, data_type=None, end_time=None, entry_list_file_info=None, entry_list_file_url=None, exec_count=None, exec_size=None, export_symlink_policy=None, failed_count=None, file_system_id=None, fileset_id=None, fileset_path=None, queue_count=None, reports=None, same_name_file_policy=None, start_time=None, status=None, sub_path=None, task_action=None, total_size=None, update_time=None, _configuration=None):  # noqa: E501
        """DataFlowTaskForDescribeDataFlowTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._data_flow_task_id = None
        self._data_storage = None
        self._data_storage_path = None
        self._data_type = None
        self._end_time = None
        self._entry_list_file_info = None
        self._entry_list_file_url = None
        self._exec_count = None
        self._exec_size = None
        self._export_symlink_policy = None
        self._failed_count = None
        self._file_system_id = None
        self._fileset_id = None
        self._fileset_path = None
        self._queue_count = None
        self._reports = None
        self._same_name_file_policy = None
        self._start_time = None
        self._status = None
        self._sub_path = None
        self._task_action = None
        self._total_size = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if data_flow_task_id is not None:
            self.data_flow_task_id = data_flow_task_id
        if data_storage is not None:
            self.data_storage = data_storage
        if data_storage_path is not None:
            self.data_storage_path = data_storage_path
        if data_type is not None:
            self.data_type = data_type
        if end_time is not None:
            self.end_time = end_time
        if entry_list_file_info is not None:
            self.entry_list_file_info = entry_list_file_info
        if entry_list_file_url is not None:
            self.entry_list_file_url = entry_list_file_url
        if exec_count is not None:
            self.exec_count = exec_count
        if exec_size is not None:
            self.exec_size = exec_size
        if export_symlink_policy is not None:
            self.export_symlink_policy = export_symlink_policy
        if failed_count is not None:
            self.failed_count = failed_count
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if fileset_id is not None:
            self.fileset_id = fileset_id
        if fileset_path is not None:
            self.fileset_path = fileset_path
        if queue_count is not None:
            self.queue_count = queue_count
        if reports is not None:
            self.reports = reports
        if same_name_file_policy is not None:
            self.same_name_file_policy = same_name_file_policy
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if sub_path is not None:
            self.sub_path = sub_path
        if task_action is not None:
            self.task_action = task_action
        if total_size is not None:
            self.total_size = total_size
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param create_time: The create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def data_flow_task_id(self):
        """Gets the data_flow_task_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The data_flow_task_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_task_id

    @data_flow_task_id.setter
    def data_flow_task_id(self, data_flow_task_id):
        """Sets the data_flow_task_id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param data_flow_task_id: The data_flow_task_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._data_flow_task_id = data_flow_task_id

    @property
    def data_storage(self):
        """Gets the data_storage of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The data_storage of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_storage

    @data_storage.setter
    def data_storage(self, data_storage):
        """Sets the data_storage of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param data_storage: The data_storage of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._data_storage = data_storage

    @property
    def data_storage_path(self):
        """Gets the data_storage_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The data_storage_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_storage_path

    @data_storage_path.setter
    def data_storage_path(self, data_storage_path):
        """Sets the data_storage_path of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param data_storage_path: The data_storage_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._data_storage_path = data_storage_path

    @property
    def data_type(self):
        """Gets the data_type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The data_type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param data_type: The data_type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def end_time(self):
        """Gets the end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param end_time: The end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def entry_list_file_info(self):
        """Gets the entry_list_file_info of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The entry_list_file_info of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: EntryListFileInfoForDescribeDataFlowTasksOutput
        """
        return self._entry_list_file_info

    @entry_list_file_info.setter
    def entry_list_file_info(self, entry_list_file_info):
        """Sets the entry_list_file_info of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param entry_list_file_info: The entry_list_file_info of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: EntryListFileInfoForDescribeDataFlowTasksOutput
        """

        self._entry_list_file_info = entry_list_file_info

    @property
    def entry_list_file_url(self):
        """Gets the entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_url

    @entry_list_file_url.setter
    def entry_list_file_url(self, entry_list_file_url):
        """Sets the entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param entry_list_file_url: The entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._entry_list_file_url = entry_list_file_url

    @property
    def exec_count(self):
        """Gets the exec_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The exec_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._exec_count

    @exec_count.setter
    def exec_count(self, exec_count):
        """Sets the exec_count of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param exec_count: The exec_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._exec_count = exec_count

    @property
    def exec_size(self):
        """Gets the exec_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The exec_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._exec_size

    @exec_size.setter
    def exec_size(self, exec_size):
        """Sets the exec_size of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param exec_size: The exec_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._exec_size = exec_size

    @property
    def export_symlink_policy(self):
        """Gets the export_symlink_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The export_symlink_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._export_symlink_policy

    @export_symlink_policy.setter
    def export_symlink_policy(self, export_symlink_policy):
        """Sets the export_symlink_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param export_symlink_policy: The export_symlink_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._export_symlink_policy = export_symlink_policy

    @property
    def failed_count(self):
        """Gets the failed_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The failed_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._failed_count

    @failed_count.setter
    def failed_count(self, failed_count):
        """Sets the failed_count of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param failed_count: The failed_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._failed_count = failed_count

    @property
    def file_system_id(self):
        """Gets the file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param file_system_id: The file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def fileset_id(self):
        """Gets the fileset_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The fileset_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._fileset_id

    @fileset_id.setter
    def fileset_id(self, fileset_id):
        """Sets the fileset_id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param fileset_id: The fileset_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._fileset_id = fileset_id

    @property
    def fileset_path(self):
        """Gets the fileset_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The fileset_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._fileset_path

    @fileset_path.setter
    def fileset_path(self, fileset_path):
        """Sets the fileset_path of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param fileset_path: The fileset_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._fileset_path = fileset_path

    @property
    def queue_count(self):
        """Gets the queue_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The queue_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._queue_count

    @queue_count.setter
    def queue_count(self, queue_count):
        """Sets the queue_count of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param queue_count: The queue_count of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._queue_count = queue_count

    @property
    def reports(self):
        """Gets the reports of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The reports of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: list[ReportForDescribeDataFlowTasksOutput]
        """
        return self._reports

    @reports.setter
    def reports(self, reports):
        """Sets the reports of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param reports: The reports of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: list[ReportForDescribeDataFlowTasksOutput]
        """

        self._reports = reports

    @property
    def same_name_file_policy(self):
        """Gets the same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._same_name_file_policy

    @same_name_file_policy.setter
    def same_name_file_policy(self, same_name_file_policy):
        """Sets the same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param same_name_file_policy: The same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._same_name_file_policy = same_name_file_policy

    @property
    def start_time(self):
        """Gets the start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param start_time: The start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param status: The status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def sub_path(self):
        """Gets the sub_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The sub_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_path

    @sub_path.setter
    def sub_path(self, sub_path):
        """Sets the sub_path of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param sub_path: The sub_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._sub_path = sub_path

    @property
    def task_action(self):
        """Gets the task_action of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The task_action of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_action

    @task_action.setter
    def task_action(self, task_action):
        """Sets the task_action of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param task_action: The task_action of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_action = task_action

    @property
    def total_size(self):
        """Gets the total_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The total_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._total_size

    @total_size.setter
    def total_size(self, total_size):
        """Sets the total_size of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param total_size: The total_size of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._total_size = total_size

    @property
    def update_time(self):
        """Gets the update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param update_time: The update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataFlowTaskForDescribeDataFlowTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataFlowTaskForDescribeDataFlowTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataFlowTaskForDescribeDataFlowTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
