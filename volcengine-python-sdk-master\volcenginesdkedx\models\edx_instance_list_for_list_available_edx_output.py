# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EDXInstanceListForListAvailableEDXOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'edx_instance_id': 'str',
        'edx_name': 'str'
    }

    attribute_map = {
        'edx_instance_id': 'EDXInstanceId',
        'edx_name': 'EDXName'
    }

    def __init__(self, edx_instance_id=None, edx_name=None, _configuration=None):  # noqa: E501
        """EDXInstanceListForListAvailableEDXOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._edx_instance_id = None
        self._edx_name = None
        self.discriminator = None

        if edx_instance_id is not None:
            self.edx_instance_id = edx_instance_id
        if edx_name is not None:
            self.edx_name = edx_name

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501


        :return: The edx_instance_id of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this EDXInstanceListForListAvailableEDXOutput.


        :param edx_instance_id: The edx_instance_id of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501
        :type: str
        """

        self._edx_instance_id = edx_instance_id

    @property
    def edx_name(self):
        """Gets the edx_name of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501


        :return: The edx_name of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._edx_name

    @edx_name.setter
    def edx_name(self, edx_name):
        """Sets the edx_name of this EDXInstanceListForListAvailableEDXOutput.


        :param edx_name: The edx_name of this EDXInstanceListForListAvailableEDXOutput.  # noqa: E501
        :type: str
        """

        self._edx_name = edx_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EDXInstanceListForListAvailableEDXOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EDXInstanceListForListAvailableEDXOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EDXInstanceListForListAvailableEDXOutput):
            return True

        return self.to_dict() != other.to_dict()
