# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmDataType2439ForGetHidsAlarmInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'args': 'list[str]',
        'nspid': 'str',
        'pid': 'str',
        'probe_hook': 'str',
        'stack_trace': 'str',
        'stack_trace_hash': 'str'
    }

    attribute_map = {
        'args': 'Args',
        'nspid': 'Nspid',
        'pid': 'Pid',
        'probe_hook': 'ProbeHook',
        'stack_trace': 'StackTrace',
        'stack_trace_hash': 'StackTraceHash'
    }

    def __init__(self, args=None, nspid=None, pid=None, probe_hook=None, stack_trace=None, stack_trace_hash=None, _configuration=None):  # noqa: E501
        """AlarmDataType2439ForGetHidsAlarmInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._args = None
        self._nspid = None
        self._pid = None
        self._probe_hook = None
        self._stack_trace = None
        self._stack_trace_hash = None
        self.discriminator = None

        if args is not None:
            self.args = args
        if nspid is not None:
            self.nspid = nspid
        if pid is not None:
            self.pid = pid
        if probe_hook is not None:
            self.probe_hook = probe_hook
        if stack_trace is not None:
            self.stack_trace = stack_trace
        if stack_trace_hash is not None:
            self.stack_trace_hash = stack_trace_hash

    @property
    def args(self):
        """Gets the args of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The args of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._args

    @args.setter
    def args(self, args):
        """Sets the args of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param args: The args of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._args = args

    @property
    def nspid(self):
        """Gets the nspid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The nspid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._nspid

    @nspid.setter
    def nspid(self, nspid):
        """Sets the nspid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param nspid: The nspid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._nspid = nspid

    @property
    def pid(self):
        """Gets the pid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The pid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param pid: The pid of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def probe_hook(self):
        """Gets the probe_hook of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The probe_hook of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._probe_hook

    @probe_hook.setter
    def probe_hook(self, probe_hook):
        """Sets the probe_hook of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param probe_hook: The probe_hook of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._probe_hook = probe_hook

    @property
    def stack_trace(self):
        """Gets the stack_trace of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The stack_trace of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace

    @stack_trace.setter
    def stack_trace(self, stack_trace):
        """Sets the stack_trace of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param stack_trace: The stack_trace of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace = stack_trace

    @property
    def stack_trace_hash(self):
        """Gets the stack_trace_hash of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The stack_trace_hash of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace_hash

    @stack_trace_hash.setter
    def stack_trace_hash(self, stack_trace_hash):
        """Sets the stack_trace_hash of this AlarmDataType2439ForGetHidsAlarmInfoOutput.


        :param stack_trace_hash: The stack_trace_hash of this AlarmDataType2439ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace_hash = stack_trace_hash

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmDataType2439ForGetHidsAlarmInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmDataType2439ForGetHidsAlarmInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmDataType2439ForGetHidsAlarmInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
