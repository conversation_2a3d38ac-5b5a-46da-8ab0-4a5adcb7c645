# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RemoveAclEntriesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'acl_id': 'str',
        'entries': 'list[str]'
    }

    attribute_map = {
        'acl_id': 'AclId',
        'entries': 'Entries'
    }

    def __init__(self, acl_id=None, entries=None, _configuration=None):  # noqa: E501
        """RemoveAclEntriesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._acl_id = None
        self._entries = None
        self.discriminator = None

        self.acl_id = acl_id
        if entries is not None:
            self.entries = entries

    @property
    def acl_id(self):
        """Gets the acl_id of this RemoveAclEntriesRequest.  # noqa: E501


        :return: The acl_id of this RemoveAclEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._acl_id

    @acl_id.setter
    def acl_id(self, acl_id):
        """Sets the acl_id of this RemoveAclEntriesRequest.


        :param acl_id: The acl_id of this RemoveAclEntriesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and acl_id is None:
            raise ValueError("Invalid value for `acl_id`, must not be `None`")  # noqa: E501

        self._acl_id = acl_id

    @property
    def entries(self):
        """Gets the entries of this RemoveAclEntriesRequest.  # noqa: E501


        :return: The entries of this RemoveAclEntriesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._entries

    @entries.setter
    def entries(self, entries):
        """Sets the entries of this RemoveAclEntriesRequest.


        :param entries: The entries of this RemoveAclEntriesRequest.  # noqa: E501
        :type: list[str]
        """

        self._entries = entries

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RemoveAclEntriesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RemoveAclEntriesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RemoveAclEntriesRequest):
            return True

        return self.to_dict() != other.to_dict()
