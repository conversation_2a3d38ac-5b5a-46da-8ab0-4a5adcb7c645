# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNLBListenerAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_id': 'str',
        'connection_timeout': 'int',
        'description': 'str',
        'enabled': 'bool',
        'listener_id': 'str',
        'listener_name': 'str',
        'security_policy_id': 'str',
        'server_group_id': 'str'
    }

    attribute_map = {
        'certificate_id': 'CertificateId',
        'connection_timeout': 'ConnectionTimeout',
        'description': 'Description',
        'enabled': 'Enabled',
        'listener_id': 'ListenerId',
        'listener_name': 'ListenerName',
        'security_policy_id': 'SecurityPolicyId',
        'server_group_id': 'ServerGroupId'
    }

    def __init__(self, certificate_id=None, connection_timeout=None, description=None, enabled=None, listener_id=None, listener_name=None, security_policy_id=None, server_group_id=None, _configuration=None):  # noqa: E501
        """ModifyNLBListenerAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_id = None
        self._connection_timeout = None
        self._description = None
        self._enabled = None
        self._listener_id = None
        self._listener_name = None
        self._security_policy_id = None
        self._server_group_id = None
        self.discriminator = None

        if certificate_id is not None:
            self.certificate_id = certificate_id
        if connection_timeout is not None:
            self.connection_timeout = connection_timeout
        if description is not None:
            self.description = description
        if enabled is not None:
            self.enabled = enabled
        self.listener_id = listener_id
        if listener_name is not None:
            self.listener_name = listener_name
        if security_policy_id is not None:
            self.security_policy_id = security_policy_id
        if server_group_id is not None:
            self.server_group_id = server_group_id

    @property
    def certificate_id(self):
        """Gets the certificate_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The certificate_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this ModifyNLBListenerAttributesRequest.


        :param certificate_id: The certificate_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def connection_timeout(self):
        """Gets the connection_timeout of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The connection_timeout of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._connection_timeout

    @connection_timeout.setter
    def connection_timeout(self, connection_timeout):
        """Sets the connection_timeout of this ModifyNLBListenerAttributesRequest.


        :param connection_timeout: The connection_timeout of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: int
        """

        self._connection_timeout = connection_timeout

    @property
    def description(self):
        """Gets the description of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The description of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyNLBListenerAttributesRequest.


        :param description: The description of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enabled(self):
        """Gets the enabled of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The enabled of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this ModifyNLBListenerAttributesRequest.


        :param enabled: The enabled of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def listener_id(self):
        """Gets the listener_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The listener_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this ModifyNLBListenerAttributesRequest.


        :param listener_id: The listener_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and listener_id is None:
            raise ValueError("Invalid value for `listener_id`, must not be `None`")  # noqa: E501

        self._listener_id = listener_id

    @property
    def listener_name(self):
        """Gets the listener_name of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The listener_name of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._listener_name

    @listener_name.setter
    def listener_name(self, listener_name):
        """Sets the listener_name of this ModifyNLBListenerAttributesRequest.


        :param listener_name: The listener_name of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._listener_name = listener_name

    @property
    def security_policy_id(self):
        """Gets the security_policy_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The security_policy_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_id

    @security_policy_id.setter
    def security_policy_id(self, security_policy_id):
        """Sets the security_policy_id of this ModifyNLBListenerAttributesRequest.


        :param security_policy_id: The security_policy_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._security_policy_id = security_policy_id

    @property
    def server_group_id(self):
        """Gets the server_group_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501


        :return: The server_group_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this ModifyNLBListenerAttributesRequest.


        :param server_group_id: The server_group_id of this ModifyNLBListenerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNLBListenerAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNLBListenerAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNLBListenerAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
