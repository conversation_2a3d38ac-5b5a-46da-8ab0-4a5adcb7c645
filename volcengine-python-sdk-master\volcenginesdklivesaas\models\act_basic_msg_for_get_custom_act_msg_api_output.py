# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActBasicMsgForGetCustomActMsgAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'announcement': 'str',
        'background_color': 'str',
        'color_theme_index': 'str',
        'cover_image_url': 'str',
        'font_color': 'str',
        'header_image_url': 'str',
        'interaction_color': 'str',
        'is_auto_start_enable': 'int',
        'is_comment_translate_enable': 'int',
        'is_count_down_enable': 'int',
        'is_cover_image_enable': 'int',
        'is_header_image_enable': 'int',
        'is_language_enable': 'int',
        'is_mobile_back_image_enable': 'int',
        'is_pc_header_image_enable': 'int',
        'is_page_limit_enable': 'int',
        'is_pc_back_image_enable': 'int',
        'is_people_count_enable': 'int',
        'is_preview_video_enable': 'int',
        'is_replay_auto_online_enable': 'int',
        'is_share_icon_enable': 'int',
        'is_thumb_up_enable': 'int',
        'is_watermark_image_enable': 'int',
        'live_time': 'int',
        'mobile_back_image_url': 'str',
        'name': 'str',
        'pc_header_image_url': 'str',
        'page_limit_type': 'str',
        'pc_back_image_url': 'str',
        'player_top_type': 'list[str]',
        'preview_video_vid': 'str',
        'share_icon_url': 'str',
        'site_tags': 'list[SiteTagForGetCustomActMsgAPIOutput]',
        'text_site_tags': 'list[TextSiteTagForGetCustomActMsgAPIOutput]',
        'thumb_up_url': 'str',
        'watermark_image_url': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'announcement': 'Announcement',
        'background_color': 'BackgroundColor',
        'color_theme_index': 'ColorThemeIndex',
        'cover_image_url': 'CoverImageUrl',
        'font_color': 'FontColor',
        'header_image_url': 'HeaderImageUrl',
        'interaction_color': 'InteractionColor',
        'is_auto_start_enable': 'IsAutoStartEnable',
        'is_comment_translate_enable': 'IsCommentTranslateEnable',
        'is_count_down_enable': 'IsCountDownEnable',
        'is_cover_image_enable': 'IsCoverImageEnable',
        'is_header_image_enable': 'IsHeaderImageEnable',
        'is_language_enable': 'IsLanguageEnable',
        'is_mobile_back_image_enable': 'IsMobileBackImageEnable',
        'is_pc_header_image_enable': 'IsPCHeaderImageEnable',
        'is_page_limit_enable': 'IsPageLimitEnable',
        'is_pc_back_image_enable': 'IsPcBackImageEnable',
        'is_people_count_enable': 'IsPeopleCountEnable',
        'is_preview_video_enable': 'IsPreviewVideoEnable',
        'is_replay_auto_online_enable': 'IsReplayAutoOnlineEnable',
        'is_share_icon_enable': 'IsShareIconEnable',
        'is_thumb_up_enable': 'IsThumbUpEnable',
        'is_watermark_image_enable': 'IsWatermarkImageEnable',
        'live_time': 'LiveTime',
        'mobile_back_image_url': 'MobileBackImageUrl',
        'name': 'Name',
        'pc_header_image_url': 'PCHeaderImageUrl',
        'page_limit_type': 'PageLimitType',
        'pc_back_image_url': 'PcBackImageUrl',
        'player_top_type': 'PlayerTopType',
        'preview_video_vid': 'PreviewVideoVid',
        'share_icon_url': 'ShareIconUrl',
        'site_tags': 'SiteTags',
        'text_site_tags': 'TextSiteTags',
        'thumb_up_url': 'ThumbUpUrl',
        'watermark_image_url': 'WatermarkImageUrl'
    }

    def __init__(self, activity_id=None, announcement=None, background_color=None, color_theme_index=None, cover_image_url=None, font_color=None, header_image_url=None, interaction_color=None, is_auto_start_enable=None, is_comment_translate_enable=None, is_count_down_enable=None, is_cover_image_enable=None, is_header_image_enable=None, is_language_enable=None, is_mobile_back_image_enable=None, is_pc_header_image_enable=None, is_page_limit_enable=None, is_pc_back_image_enable=None, is_people_count_enable=None, is_preview_video_enable=None, is_replay_auto_online_enable=None, is_share_icon_enable=None, is_thumb_up_enable=None, is_watermark_image_enable=None, live_time=None, mobile_back_image_url=None, name=None, pc_header_image_url=None, page_limit_type=None, pc_back_image_url=None, player_top_type=None, preview_video_vid=None, share_icon_url=None, site_tags=None, text_site_tags=None, thumb_up_url=None, watermark_image_url=None, _configuration=None):  # noqa: E501
        """ActBasicMsgForGetCustomActMsgAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._announcement = None
        self._background_color = None
        self._color_theme_index = None
        self._cover_image_url = None
        self._font_color = None
        self._header_image_url = None
        self._interaction_color = None
        self._is_auto_start_enable = None
        self._is_comment_translate_enable = None
        self._is_count_down_enable = None
        self._is_cover_image_enable = None
        self._is_header_image_enable = None
        self._is_language_enable = None
        self._is_mobile_back_image_enable = None
        self._is_pc_header_image_enable = None
        self._is_page_limit_enable = None
        self._is_pc_back_image_enable = None
        self._is_people_count_enable = None
        self._is_preview_video_enable = None
        self._is_replay_auto_online_enable = None
        self._is_share_icon_enable = None
        self._is_thumb_up_enable = None
        self._is_watermark_image_enable = None
        self._live_time = None
        self._mobile_back_image_url = None
        self._name = None
        self._pc_header_image_url = None
        self._page_limit_type = None
        self._pc_back_image_url = None
        self._player_top_type = None
        self._preview_video_vid = None
        self._share_icon_url = None
        self._site_tags = None
        self._text_site_tags = None
        self._thumb_up_url = None
        self._watermark_image_url = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if announcement is not None:
            self.announcement = announcement
        if background_color is not None:
            self.background_color = background_color
        if color_theme_index is not None:
            self.color_theme_index = color_theme_index
        if cover_image_url is not None:
            self.cover_image_url = cover_image_url
        if font_color is not None:
            self.font_color = font_color
        if header_image_url is not None:
            self.header_image_url = header_image_url
        if interaction_color is not None:
            self.interaction_color = interaction_color
        if is_auto_start_enable is not None:
            self.is_auto_start_enable = is_auto_start_enable
        if is_comment_translate_enable is not None:
            self.is_comment_translate_enable = is_comment_translate_enable
        if is_count_down_enable is not None:
            self.is_count_down_enable = is_count_down_enable
        if is_cover_image_enable is not None:
            self.is_cover_image_enable = is_cover_image_enable
        if is_header_image_enable is not None:
            self.is_header_image_enable = is_header_image_enable
        if is_language_enable is not None:
            self.is_language_enable = is_language_enable
        if is_mobile_back_image_enable is not None:
            self.is_mobile_back_image_enable = is_mobile_back_image_enable
        if is_pc_header_image_enable is not None:
            self.is_pc_header_image_enable = is_pc_header_image_enable
        if is_page_limit_enable is not None:
            self.is_page_limit_enable = is_page_limit_enable
        if is_pc_back_image_enable is not None:
            self.is_pc_back_image_enable = is_pc_back_image_enable
        if is_people_count_enable is not None:
            self.is_people_count_enable = is_people_count_enable
        if is_preview_video_enable is not None:
            self.is_preview_video_enable = is_preview_video_enable
        if is_replay_auto_online_enable is not None:
            self.is_replay_auto_online_enable = is_replay_auto_online_enable
        if is_share_icon_enable is not None:
            self.is_share_icon_enable = is_share_icon_enable
        if is_thumb_up_enable is not None:
            self.is_thumb_up_enable = is_thumb_up_enable
        if is_watermark_image_enable is not None:
            self.is_watermark_image_enable = is_watermark_image_enable
        if live_time is not None:
            self.live_time = live_time
        if mobile_back_image_url is not None:
            self.mobile_back_image_url = mobile_back_image_url
        if name is not None:
            self.name = name
        if pc_header_image_url is not None:
            self.pc_header_image_url = pc_header_image_url
        if page_limit_type is not None:
            self.page_limit_type = page_limit_type
        if pc_back_image_url is not None:
            self.pc_back_image_url = pc_back_image_url
        if player_top_type is not None:
            self.player_top_type = player_top_type
        if preview_video_vid is not None:
            self.preview_video_vid = preview_video_vid
        if share_icon_url is not None:
            self.share_icon_url = share_icon_url
        if site_tags is not None:
            self.site_tags = site_tags
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags
        if thumb_up_url is not None:
            self.thumb_up_url = thumb_up_url
        if watermark_image_url is not None:
            self.watermark_image_url = watermark_image_url

    @property
    def activity_id(self):
        """Gets the activity_id of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The activity_id of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param activity_id: The activity_id of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def announcement(self):
        """Gets the announcement of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The announcement of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._announcement

    @announcement.setter
    def announcement(self, announcement):
        """Sets the announcement of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param announcement: The announcement of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._announcement = announcement

    @property
    def background_color(self):
        """Gets the background_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The background_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._background_color

    @background_color.setter
    def background_color(self, background_color):
        """Sets the background_color of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param background_color: The background_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._background_color = background_color

    @property
    def color_theme_index(self):
        """Gets the color_theme_index of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The color_theme_index of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._color_theme_index

    @color_theme_index.setter
    def color_theme_index(self, color_theme_index):
        """Sets the color_theme_index of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param color_theme_index: The color_theme_index of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._color_theme_index = color_theme_index

    @property
    def cover_image_url(self):
        """Gets the cover_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The cover_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image_url

    @cover_image_url.setter
    def cover_image_url(self, cover_image_url):
        """Sets the cover_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param cover_image_url: The cover_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._cover_image_url = cover_image_url

    @property
    def font_color(self):
        """Gets the font_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The font_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._font_color

    @font_color.setter
    def font_color(self, font_color):
        """Sets the font_color of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param font_color: The font_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._font_color = font_color

    @property
    def header_image_url(self):
        """Gets the header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._header_image_url

    @header_image_url.setter
    def header_image_url(self, header_image_url):
        """Sets the header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param header_image_url: The header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._header_image_url = header_image_url

    @property
    def interaction_color(self):
        """Gets the interaction_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The interaction_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._interaction_color

    @interaction_color.setter
    def interaction_color(self, interaction_color):
        """Sets the interaction_color of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param interaction_color: The interaction_color of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._interaction_color = interaction_color

    @property
    def is_auto_start_enable(self):
        """Gets the is_auto_start_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_auto_start_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_start_enable

    @is_auto_start_enable.setter
    def is_auto_start_enable(self, is_auto_start_enable):
        """Sets the is_auto_start_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_auto_start_enable: The is_auto_start_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_auto_start_enable = is_auto_start_enable

    @property
    def is_comment_translate_enable(self):
        """Gets the is_comment_translate_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_comment_translate_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_comment_translate_enable

    @is_comment_translate_enable.setter
    def is_comment_translate_enable(self, is_comment_translate_enable):
        """Sets the is_comment_translate_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_comment_translate_enable: The is_comment_translate_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_comment_translate_enable = is_comment_translate_enable

    @property
    def is_count_down_enable(self):
        """Gets the is_count_down_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_count_down_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_count_down_enable

    @is_count_down_enable.setter
    def is_count_down_enable(self, is_count_down_enable):
        """Sets the is_count_down_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_count_down_enable: The is_count_down_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_count_down_enable = is_count_down_enable

    @property
    def is_cover_image_enable(self):
        """Gets the is_cover_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_cover_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_cover_image_enable

    @is_cover_image_enable.setter
    def is_cover_image_enable(self, is_cover_image_enable):
        """Sets the is_cover_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_cover_image_enable: The is_cover_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_cover_image_enable = is_cover_image_enable

    @property
    def is_header_image_enable(self):
        """Gets the is_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_header_image_enable

    @is_header_image_enable.setter
    def is_header_image_enable(self, is_header_image_enable):
        """Sets the is_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_header_image_enable: The is_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_header_image_enable = is_header_image_enable

    @property
    def is_language_enable(self):
        """Gets the is_language_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_language_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_language_enable

    @is_language_enable.setter
    def is_language_enable(self, is_language_enable):
        """Sets the is_language_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_language_enable: The is_language_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_language_enable = is_language_enable

    @property
    def is_mobile_back_image_enable(self):
        """Gets the is_mobile_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_mobile_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_mobile_back_image_enable

    @is_mobile_back_image_enable.setter
    def is_mobile_back_image_enable(self, is_mobile_back_image_enable):
        """Sets the is_mobile_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_mobile_back_image_enable: The is_mobile_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_mobile_back_image_enable = is_mobile_back_image_enable

    @property
    def is_pc_header_image_enable(self):
        """Gets the is_pc_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_pc_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_pc_header_image_enable

    @is_pc_header_image_enable.setter
    def is_pc_header_image_enable(self, is_pc_header_image_enable):
        """Sets the is_pc_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_pc_header_image_enable: The is_pc_header_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_pc_header_image_enable = is_pc_header_image_enable

    @property
    def is_page_limit_enable(self):
        """Gets the is_page_limit_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_page_limit_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_page_limit_enable

    @is_page_limit_enable.setter
    def is_page_limit_enable(self, is_page_limit_enable):
        """Sets the is_page_limit_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_page_limit_enable: The is_page_limit_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_page_limit_enable = is_page_limit_enable

    @property
    def is_pc_back_image_enable(self):
        """Gets the is_pc_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_pc_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_pc_back_image_enable

    @is_pc_back_image_enable.setter
    def is_pc_back_image_enable(self, is_pc_back_image_enable):
        """Sets the is_pc_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_pc_back_image_enable: The is_pc_back_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_pc_back_image_enable = is_pc_back_image_enable

    @property
    def is_people_count_enable(self):
        """Gets the is_people_count_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_people_count_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_people_count_enable

    @is_people_count_enable.setter
    def is_people_count_enable(self, is_people_count_enable):
        """Sets the is_people_count_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_people_count_enable: The is_people_count_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_people_count_enable = is_people_count_enable

    @property
    def is_preview_video_enable(self):
        """Gets the is_preview_video_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_preview_video_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_preview_video_enable

    @is_preview_video_enable.setter
    def is_preview_video_enable(self, is_preview_video_enable):
        """Sets the is_preview_video_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_preview_video_enable: The is_preview_video_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_preview_video_enable = is_preview_video_enable

    @property
    def is_replay_auto_online_enable(self):
        """Gets the is_replay_auto_online_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_replay_auto_online_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_replay_auto_online_enable

    @is_replay_auto_online_enable.setter
    def is_replay_auto_online_enable(self, is_replay_auto_online_enable):
        """Sets the is_replay_auto_online_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_replay_auto_online_enable: The is_replay_auto_online_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_replay_auto_online_enable = is_replay_auto_online_enable

    @property
    def is_share_icon_enable(self):
        """Gets the is_share_icon_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_share_icon_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_share_icon_enable

    @is_share_icon_enable.setter
    def is_share_icon_enable(self, is_share_icon_enable):
        """Sets the is_share_icon_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_share_icon_enable: The is_share_icon_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_share_icon_enable = is_share_icon_enable

    @property
    def is_thumb_up_enable(self):
        """Gets the is_thumb_up_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_thumb_up_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_thumb_up_enable

    @is_thumb_up_enable.setter
    def is_thumb_up_enable(self, is_thumb_up_enable):
        """Sets the is_thumb_up_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_thumb_up_enable: The is_thumb_up_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_thumb_up_enable = is_thumb_up_enable

    @property
    def is_watermark_image_enable(self):
        """Gets the is_watermark_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_watermark_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_watermark_image_enable

    @is_watermark_image_enable.setter
    def is_watermark_image_enable(self, is_watermark_image_enable):
        """Sets the is_watermark_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param is_watermark_image_enable: The is_watermark_image_enable of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_watermark_image_enable = is_watermark_image_enable

    @property
    def live_time(self):
        """Gets the live_time of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The live_time of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param live_time: The live_time of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def mobile_back_image_url(self):
        """Gets the mobile_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The mobile_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._mobile_back_image_url

    @mobile_back_image_url.setter
    def mobile_back_image_url(self, mobile_back_image_url):
        """Sets the mobile_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param mobile_back_image_url: The mobile_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._mobile_back_image_url = mobile_back_image_url

    @property
    def name(self):
        """Gets the name of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The name of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param name: The name of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def pc_header_image_url(self):
        """Gets the pc_header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The pc_header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._pc_header_image_url

    @pc_header_image_url.setter
    def pc_header_image_url(self, pc_header_image_url):
        """Sets the pc_header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param pc_header_image_url: The pc_header_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._pc_header_image_url = pc_header_image_url

    @property
    def page_limit_type(self):
        """Gets the page_limit_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The page_limit_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._page_limit_type

    @page_limit_type.setter
    def page_limit_type(self, page_limit_type):
        """Sets the page_limit_type of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param page_limit_type: The page_limit_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._page_limit_type = page_limit_type

    @property
    def pc_back_image_url(self):
        """Gets the pc_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The pc_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._pc_back_image_url

    @pc_back_image_url.setter
    def pc_back_image_url(self, pc_back_image_url):
        """Sets the pc_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param pc_back_image_url: The pc_back_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._pc_back_image_url = pc_back_image_url

    @property
    def player_top_type(self):
        """Gets the player_top_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The player_top_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._player_top_type

    @player_top_type.setter
    def player_top_type(self, player_top_type):
        """Sets the player_top_type of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param player_top_type: The player_top_type of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: list[str]
        """

        self._player_top_type = player_top_type

    @property
    def preview_video_vid(self):
        """Gets the preview_video_vid of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The preview_video_vid of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_vid

    @preview_video_vid.setter
    def preview_video_vid(self, preview_video_vid):
        """Sets the preview_video_vid of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param preview_video_vid: The preview_video_vid of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._preview_video_vid = preview_video_vid

    @property
    def share_icon_url(self):
        """Gets the share_icon_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The share_icon_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._share_icon_url

    @share_icon_url.setter
    def share_icon_url(self, share_icon_url):
        """Sets the share_icon_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param share_icon_url: The share_icon_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._share_icon_url = share_icon_url

    @property
    def site_tags(self):
        """Gets the site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: list[SiteTagForGetCustomActMsgAPIOutput]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param site_tags: The site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: list[SiteTagForGetCustomActMsgAPIOutput]
        """

        self._site_tags = site_tags

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The text_site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: list[TextSiteTagForGetCustomActMsgAPIOutput]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param text_site_tags: The text_site_tags of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: list[TextSiteTagForGetCustomActMsgAPIOutput]
        """

        self._text_site_tags = text_site_tags

    @property
    def thumb_up_url(self):
        """Gets the thumb_up_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The thumb_up_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._thumb_up_url

    @thumb_up_url.setter
    def thumb_up_url(self, thumb_up_url):
        """Sets the thumb_up_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param thumb_up_url: The thumb_up_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._thumb_up_url = thumb_up_url

    @property
    def watermark_image_url(self):
        """Gets the watermark_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The watermark_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._watermark_image_url

    @watermark_image_url.setter
    def watermark_image_url(self, watermark_image_url):
        """Sets the watermark_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.


        :param watermark_image_url: The watermark_image_url of this ActBasicMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._watermark_image_url = watermark_image_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActBasicMsgForGetCustomActMsgAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActBasicMsgForGetCustomActMsgAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActBasicMsgForGetCustomActMsgAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
