# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AttachFileSystemForDescribeMountServicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'customer_path': 'str',
        'file_system_id': 'str',
        'file_system_name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'customer_path': 'CustomerPath',
        'file_system_id': 'FileSystemId',
        'file_system_name': 'FileSystemName',
        'status': 'Status'
    }

    def __init__(self, account_id=None, customer_path=None, file_system_id=None, file_system_name=None, status=None, _configuration=None):  # noqa: E501
        """AttachFileSystemForDescribeMountServicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._customer_path = None
        self._file_system_id = None
        self._file_system_name = None
        self._status = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if customer_path is not None:
            self.customer_path = customer_path
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if file_system_name is not None:
            self.file_system_name = file_system_name
        if status is not None:
            self.status = status

    @property
    def account_id(self):
        """Gets the account_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501


        :return: The account_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this AttachFileSystemForDescribeMountServicesOutput.


        :param account_id: The account_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def customer_path(self):
        """Gets the customer_path of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501


        :return: The customer_path of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_path

    @customer_path.setter
    def customer_path(self, customer_path):
        """Sets the customer_path of this AttachFileSystemForDescribeMountServicesOutput.


        :param customer_path: The customer_path of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._customer_path = customer_path

    @property
    def file_system_id(self):
        """Gets the file_system_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501


        :return: The file_system_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this AttachFileSystemForDescribeMountServicesOutput.


        :param file_system_id: The file_system_id of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def file_system_name(self):
        """Gets the file_system_name of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501


        :return: The file_system_name of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_name

    @file_system_name.setter
    def file_system_name(self, file_system_name):
        """Sets the file_system_name of this AttachFileSystemForDescribeMountServicesOutput.


        :param file_system_name: The file_system_name of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_name = file_system_name

    @property
    def status(self):
        """Gets the status of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501


        :return: The status of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AttachFileSystemForDescribeMountServicesOutput.


        :param status: The status of this AttachFileSystemForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AttachFileSystemForDescribeMountServicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AttachFileSystemForDescribeMountServicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AttachFileSystemForDescribeMountServicesOutput):
            return True

        return self.to_dict() != other.to_dict()
