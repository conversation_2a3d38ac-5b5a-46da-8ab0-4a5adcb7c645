# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateServiceControlPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'policy_id': 'str',
        'policy_name': 'str',
        'statement': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'policy_id': 'PolicyID',
        'policy_name': 'PolicyName',
        'statement': 'Statement'
    }

    def __init__(self, description=None, policy_id=None, policy_name=None, statement=None, _configuration=None):  # noqa: E501
        """UpdateServiceControlPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._policy_id = None
        self._policy_name = None
        self._statement = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.policy_id = policy_id
        self.policy_name = policy_name
        self.statement = statement

    @property
    def description(self):
        """Gets the description of this UpdateServiceControlPolicyRequest.  # noqa: E501


        :return: The description of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateServiceControlPolicyRequest.


        :param description: The description of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def policy_id(self):
        """Gets the policy_id of this UpdateServiceControlPolicyRequest.  # noqa: E501


        :return: The policy_id of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_id

    @policy_id.setter
    def policy_id(self, policy_id):
        """Sets the policy_id of this UpdateServiceControlPolicyRequest.


        :param policy_id: The policy_id of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_id is None:
            raise ValueError("Invalid value for `policy_id`, must not be `None`")  # noqa: E501

        self._policy_id = policy_id

    @property
    def policy_name(self):
        """Gets the policy_name of this UpdateServiceControlPolicyRequest.  # noqa: E501


        :return: The policy_name of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_name

    @policy_name.setter
    def policy_name(self, policy_name):
        """Sets the policy_name of this UpdateServiceControlPolicyRequest.


        :param policy_name: The policy_name of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_name is None:
            raise ValueError("Invalid value for `policy_name`, must not be `None`")  # noqa: E501

        self._policy_name = policy_name

    @property
    def statement(self):
        """Gets the statement of this UpdateServiceControlPolicyRequest.  # noqa: E501


        :return: The statement of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._statement

    @statement.setter
    def statement(self, statement):
        """Sets the statement of this UpdateServiceControlPolicyRequest.


        :param statement: The statement of this UpdateServiceControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and statement is None:
            raise ValueError("Invalid value for `statement`, must not be `None`")  # noqa: E501

        self._statement = statement

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateServiceControlPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateServiceControlPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateServiceControlPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
