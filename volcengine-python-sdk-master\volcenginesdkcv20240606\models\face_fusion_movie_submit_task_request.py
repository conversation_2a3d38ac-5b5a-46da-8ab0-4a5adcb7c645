# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FaceFusionMovieSubmitTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dermabrasion': 'float',
        'enable_face_beautify': 'bool',
        'gpen': 'float',
        'image_url': 'str',
        'logo_info': 'LogoInfoForFaceFusionMovieSubmitTaskInput',
        'ref_img_url': 'str',
        'req_key': 'str',
        'sharpening': 'float',
        'source_similarity': 'str',
        'video_url': 'str',
        'whitening': 'float'
    }

    attribute_map = {
        'dermabrasion': 'dermabrasion',
        'enable_face_beautify': 'enable_face_beautify',
        'gpen': 'gpen',
        'image_url': 'image_url',
        'logo_info': 'logo_info',
        'ref_img_url': 'ref_img_url',
        'req_key': 'req_key',
        'sharpening': 'sharpening',
        'source_similarity': 'source_similarity',
        'video_url': 'video_url',
        'whitening': 'whitening'
    }

    def __init__(self, dermabrasion=None, enable_face_beautify=None, gpen=None, image_url=None, logo_info=None, ref_img_url=None, req_key=None, sharpening=None, source_similarity=None, video_url=None, whitening=None, _configuration=None):  # noqa: E501
        """FaceFusionMovieSubmitTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dermabrasion = None
        self._enable_face_beautify = None
        self._gpen = None
        self._image_url = None
        self._logo_info = None
        self._ref_img_url = None
        self._req_key = None
        self._sharpening = None
        self._source_similarity = None
        self._video_url = None
        self._whitening = None
        self.discriminator = None

        if dermabrasion is not None:
            self.dermabrasion = dermabrasion
        if enable_face_beautify is not None:
            self.enable_face_beautify = enable_face_beautify
        if gpen is not None:
            self.gpen = gpen
        self.image_url = image_url
        if logo_info is not None:
            self.logo_info = logo_info
        if ref_img_url is not None:
            self.ref_img_url = ref_img_url
        self.req_key = req_key
        if sharpening is not None:
            self.sharpening = sharpening
        if source_similarity is not None:
            self.source_similarity = source_similarity
        self.video_url = video_url
        if whitening is not None:
            self.whitening = whitening

    @property
    def dermabrasion(self):
        """Gets the dermabrasion of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The dermabrasion of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: float
        """
        return self._dermabrasion

    @dermabrasion.setter
    def dermabrasion(self, dermabrasion):
        """Sets the dermabrasion of this FaceFusionMovieSubmitTaskRequest.


        :param dermabrasion: The dermabrasion of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: float
        """

        self._dermabrasion = dermabrasion

    @property
    def enable_face_beautify(self):
        """Gets the enable_face_beautify of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The enable_face_beautify of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_face_beautify

    @enable_face_beautify.setter
    def enable_face_beautify(self, enable_face_beautify):
        """Sets the enable_face_beautify of this FaceFusionMovieSubmitTaskRequest.


        :param enable_face_beautify: The enable_face_beautify of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: bool
        """

        self._enable_face_beautify = enable_face_beautify

    @property
    def gpen(self):
        """Gets the gpen of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The gpen of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: float
        """
        return self._gpen

    @gpen.setter
    def gpen(self, gpen):
        """Sets the gpen of this FaceFusionMovieSubmitTaskRequest.


        :param gpen: The gpen of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: float
        """

        self._gpen = gpen

    @property
    def image_url(self):
        """Gets the image_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The image_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_url

    @image_url.setter
    def image_url(self, image_url):
        """Sets the image_url of this FaceFusionMovieSubmitTaskRequest.


        :param image_url: The image_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_url is None:
            raise ValueError("Invalid value for `image_url`, must not be `None`")  # noqa: E501

        self._image_url = image_url

    @property
    def logo_info(self):
        """Gets the logo_info of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The logo_info of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: LogoInfoForFaceFusionMovieSubmitTaskInput
        """
        return self._logo_info

    @logo_info.setter
    def logo_info(self, logo_info):
        """Sets the logo_info of this FaceFusionMovieSubmitTaskRequest.


        :param logo_info: The logo_info of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: LogoInfoForFaceFusionMovieSubmitTaskInput
        """

        self._logo_info = logo_info

    @property
    def ref_img_url(self):
        """Gets the ref_img_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The ref_img_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._ref_img_url

    @ref_img_url.setter
    def ref_img_url(self, ref_img_url):
        """Sets the ref_img_url of this FaceFusionMovieSubmitTaskRequest.


        :param ref_img_url: The ref_img_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: str
        """

        self._ref_img_url = ref_img_url

    @property
    def req_key(self):
        """Gets the req_key of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The req_key of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this FaceFusionMovieSubmitTaskRequest.


        :param req_key: The req_key of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    @property
    def sharpening(self):
        """Gets the sharpening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The sharpening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: float
        """
        return self._sharpening

    @sharpening.setter
    def sharpening(self, sharpening):
        """Sets the sharpening of this FaceFusionMovieSubmitTaskRequest.


        :param sharpening: The sharpening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: float
        """

        self._sharpening = sharpening

    @property
    def source_similarity(self):
        """Gets the source_similarity of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The source_similarity of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_similarity

    @source_similarity.setter
    def source_similarity(self, source_similarity):
        """Sets the source_similarity of this FaceFusionMovieSubmitTaskRequest.


        :param source_similarity: The source_similarity of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: str
        """

        self._source_similarity = source_similarity

    @property
    def video_url(self):
        """Gets the video_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The video_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._video_url

    @video_url.setter
    def video_url(self, video_url):
        """Sets the video_url of this FaceFusionMovieSubmitTaskRequest.


        :param video_url: The video_url of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and video_url is None:
            raise ValueError("Invalid value for `video_url`, must not be `None`")  # noqa: E501

        self._video_url = video_url

    @property
    def whitening(self):
        """Gets the whitening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501


        :return: The whitening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :rtype: float
        """
        return self._whitening

    @whitening.setter
    def whitening(self, whitening):
        """Sets the whitening of this FaceFusionMovieSubmitTaskRequest.


        :param whitening: The whitening of this FaceFusionMovieSubmitTaskRequest.  # noqa: E501
        :type: float
        """

        self._whitening = whitening

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FaceFusionMovieSubmitTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FaceFusionMovieSubmitTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FaceFusionMovieSubmitTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
