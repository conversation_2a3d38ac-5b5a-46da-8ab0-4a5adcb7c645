# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class KubeletConfigForCreateDefaultNodePoolInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eviction_hard': 'list[EvictionHardForCreateDefaultNodePoolInput]',
        'feature_gates': 'FeatureGatesForCreateDefaultNodePoolInput',
        'kube_api_burst': 'int',
        'kube_api_qps': 'int',
        'registry_burst': 'int',
        'registry_pull_qps': 'int',
        'serialize_image_pulls': 'bool',
        'topology_manager_policy': 'str',
        'topology_manager_scope': 'str'
    }

    attribute_map = {
        'eviction_hard': 'EvictionHard',
        'feature_gates': 'FeatureGates',
        'kube_api_burst': 'KubeApiBurst',
        'kube_api_qps': 'KubeApiQps',
        'registry_burst': 'RegistryBurst',
        'registry_pull_qps': 'RegistryPullQps',
        'serialize_image_pulls': 'SerializeImagePulls',
        'topology_manager_policy': 'TopologyManagerPolicy',
        'topology_manager_scope': 'TopologyManagerScope'
    }

    def __init__(self, eviction_hard=None, feature_gates=None, kube_api_burst=None, kube_api_qps=None, registry_burst=None, registry_pull_qps=None, serialize_image_pulls=None, topology_manager_policy=None, topology_manager_scope=None, _configuration=None):  # noqa: E501
        """KubeletConfigForCreateDefaultNodePoolInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eviction_hard = None
        self._feature_gates = None
        self._kube_api_burst = None
        self._kube_api_qps = None
        self._registry_burst = None
        self._registry_pull_qps = None
        self._serialize_image_pulls = None
        self._topology_manager_policy = None
        self._topology_manager_scope = None
        self.discriminator = None

        if eviction_hard is not None:
            self.eviction_hard = eviction_hard
        if feature_gates is not None:
            self.feature_gates = feature_gates
        if kube_api_burst is not None:
            self.kube_api_burst = kube_api_burst
        if kube_api_qps is not None:
            self.kube_api_qps = kube_api_qps
        if registry_burst is not None:
            self.registry_burst = registry_burst
        if registry_pull_qps is not None:
            self.registry_pull_qps = registry_pull_qps
        if serialize_image_pulls is not None:
            self.serialize_image_pulls = serialize_image_pulls
        if topology_manager_policy is not None:
            self.topology_manager_policy = topology_manager_policy
        if topology_manager_scope is not None:
            self.topology_manager_scope = topology_manager_scope

    @property
    def eviction_hard(self):
        """Gets the eviction_hard of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The eviction_hard of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: list[EvictionHardForCreateDefaultNodePoolInput]
        """
        return self._eviction_hard

    @eviction_hard.setter
    def eviction_hard(self, eviction_hard):
        """Sets the eviction_hard of this KubeletConfigForCreateDefaultNodePoolInput.


        :param eviction_hard: The eviction_hard of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: list[EvictionHardForCreateDefaultNodePoolInput]
        """

        self._eviction_hard = eviction_hard

    @property
    def feature_gates(self):
        """Gets the feature_gates of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The feature_gates of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: FeatureGatesForCreateDefaultNodePoolInput
        """
        return self._feature_gates

    @feature_gates.setter
    def feature_gates(self, feature_gates):
        """Sets the feature_gates of this KubeletConfigForCreateDefaultNodePoolInput.


        :param feature_gates: The feature_gates of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: FeatureGatesForCreateDefaultNodePoolInput
        """

        self._feature_gates = feature_gates

    @property
    def kube_api_burst(self):
        """Gets the kube_api_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The kube_api_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: int
        """
        return self._kube_api_burst

    @kube_api_burst.setter
    def kube_api_burst(self, kube_api_burst):
        """Sets the kube_api_burst of this KubeletConfigForCreateDefaultNodePoolInput.


        :param kube_api_burst: The kube_api_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                kube_api_burst is not None and kube_api_burst > 100):  # noqa: E501
            raise ValueError("Invalid value for `kube_api_burst`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                kube_api_burst is not None and kube_api_burst < 1):  # noqa: E501
            raise ValueError("Invalid value for `kube_api_burst`, must be a value greater than or equal to `1`")  # noqa: E501

        self._kube_api_burst = kube_api_burst

    @property
    def kube_api_qps(self):
        """Gets the kube_api_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The kube_api_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: int
        """
        return self._kube_api_qps

    @kube_api_qps.setter
    def kube_api_qps(self, kube_api_qps):
        """Sets the kube_api_qps of this KubeletConfigForCreateDefaultNodePoolInput.


        :param kube_api_qps: The kube_api_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                kube_api_qps is not None and kube_api_qps > 50):  # noqa: E501
            raise ValueError("Invalid value for `kube_api_qps`, must be a value less than or equal to `50`")  # noqa: E501
        if (self._configuration.client_side_validation and
                kube_api_qps is not None and kube_api_qps < 1):  # noqa: E501
            raise ValueError("Invalid value for `kube_api_qps`, must be a value greater than or equal to `1`")  # noqa: E501

        self._kube_api_qps = kube_api_qps

    @property
    def registry_burst(self):
        """Gets the registry_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The registry_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: int
        """
        return self._registry_burst

    @registry_burst.setter
    def registry_burst(self, registry_burst):
        """Sets the registry_burst of this KubeletConfigForCreateDefaultNodePoolInput.


        :param registry_burst: The registry_burst of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                registry_burst is not None and registry_burst > 100):  # noqa: E501
            raise ValueError("Invalid value for `registry_burst`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                registry_burst is not None and registry_burst < 1):  # noqa: E501
            raise ValueError("Invalid value for `registry_burst`, must be a value greater than or equal to `1`")  # noqa: E501

        self._registry_burst = registry_burst

    @property
    def registry_pull_qps(self):
        """Gets the registry_pull_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The registry_pull_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: int
        """
        return self._registry_pull_qps

    @registry_pull_qps.setter
    def registry_pull_qps(self, registry_pull_qps):
        """Sets the registry_pull_qps of this KubeletConfigForCreateDefaultNodePoolInput.


        :param registry_pull_qps: The registry_pull_qps of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                registry_pull_qps is not None and registry_pull_qps > 50):  # noqa: E501
            raise ValueError("Invalid value for `registry_pull_qps`, must be a value less than or equal to `50`")  # noqa: E501
        if (self._configuration.client_side_validation and
                registry_pull_qps is not None and registry_pull_qps < 1):  # noqa: E501
            raise ValueError("Invalid value for `registry_pull_qps`, must be a value greater than or equal to `1`")  # noqa: E501

        self._registry_pull_qps = registry_pull_qps

    @property
    def serialize_image_pulls(self):
        """Gets the serialize_image_pulls of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The serialize_image_pulls of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: bool
        """
        return self._serialize_image_pulls

    @serialize_image_pulls.setter
    def serialize_image_pulls(self, serialize_image_pulls):
        """Sets the serialize_image_pulls of this KubeletConfigForCreateDefaultNodePoolInput.


        :param serialize_image_pulls: The serialize_image_pulls of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: bool
        """

        self._serialize_image_pulls = serialize_image_pulls

    @property
    def topology_manager_policy(self):
        """Gets the topology_manager_policy of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The topology_manager_policy of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: str
        """
        return self._topology_manager_policy

    @topology_manager_policy.setter
    def topology_manager_policy(self, topology_manager_policy):
        """Sets the topology_manager_policy of this KubeletConfigForCreateDefaultNodePoolInput.


        :param topology_manager_policy: The topology_manager_policy of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["restricted", "best-effort", "none", "single-numa-node"]  # noqa: E501
        if (self._configuration.client_side_validation and
                topology_manager_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `topology_manager_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(topology_manager_policy, allowed_values)
            )

        self._topology_manager_policy = topology_manager_policy

    @property
    def topology_manager_scope(self):
        """Gets the topology_manager_scope of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501


        :return: The topology_manager_scope of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :rtype: str
        """
        return self._topology_manager_scope

    @topology_manager_scope.setter
    def topology_manager_scope(self, topology_manager_scope):
        """Sets the topology_manager_scope of this KubeletConfigForCreateDefaultNodePoolInput.


        :param topology_manager_scope: The topology_manager_scope of this KubeletConfigForCreateDefaultNodePoolInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["pod", "container"]  # noqa: E501
        if (self._configuration.client_side_validation and
                topology_manager_scope not in allowed_values):
            raise ValueError(
                "Invalid value for `topology_manager_scope` ({0}), must be one of {1}"  # noqa: E501
                .format(topology_manager_scope, allowed_values)
            )

        self._topology_manager_scope = topology_manager_scope

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(KubeletConfigForCreateDefaultNodePoolInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, KubeletConfigForCreateDefaultNodePoolInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, KubeletConfigForCreateDefaultNodePoolInput):
            return True

        return self.to_dict() != other.to_dict()
