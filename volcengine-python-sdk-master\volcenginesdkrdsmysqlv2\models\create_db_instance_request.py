# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_list_ids': 'list[str]',
        'auto_storage_scaling_config': 'AutoStorageScalingConfigForCreateDBInstanceInput',
        'charge_info': 'ChargeInfoForCreateDBInstanceInput',
        'db_engine_version': 'str',
        'db_param_group_id': 'str',
        'db_time_zone': 'str',
        'deletion_protection': 'str',
        'instance_name': 'str',
        'instance_tags': 'list[InstanceTagForCreateDBInstanceInput]',
        'instance_type': 'str',
        'lower_case_table_names': 'str',
        'maintenance_window': 'MaintenanceWindowForCreateDBInstanceInput',
        'node_info': 'list[NodeInfoForCreateDBInstanceInput]',
        'port': 'int',
        'project_name': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'subnet_id': 'str',
        'super_account_name': 'str',
        'super_account_password': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'allow_list_ids': 'AllowListIds',
        'auto_storage_scaling_config': 'AutoStorageScalingConfig',
        'charge_info': 'ChargeInfo',
        'db_engine_version': 'DBEngineVersion',
        'db_param_group_id': 'DBParamGroupId',
        'db_time_zone': 'DBTimeZone',
        'deletion_protection': 'DeletionProtection',
        'instance_name': 'InstanceName',
        'instance_tags': 'InstanceTags',
        'instance_type': 'InstanceType',
        'lower_case_table_names': 'LowerCaseTableNames',
        'maintenance_window': 'MaintenanceWindow',
        'node_info': 'NodeInfo',
        'port': 'Port',
        'project_name': 'ProjectName',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'subnet_id': 'SubnetId',
        'super_account_name': 'SuperAccountName',
        'super_account_password': 'SuperAccountPassword',
        'vpc_id': 'VpcId'
    }

    def __init__(self, allow_list_ids=None, auto_storage_scaling_config=None, charge_info=None, db_engine_version=None, db_param_group_id=None, db_time_zone=None, deletion_protection=None, instance_name=None, instance_tags=None, instance_type=None, lower_case_table_names=None, maintenance_window=None, node_info=None, port=None, project_name=None, storage_space=None, storage_type=None, subnet_id=None, super_account_name=None, super_account_password=None, vpc_id=None, _configuration=None):  # noqa: E501
        """CreateDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_list_ids = None
        self._auto_storage_scaling_config = None
        self._charge_info = None
        self._db_engine_version = None
        self._db_param_group_id = None
        self._db_time_zone = None
        self._deletion_protection = None
        self._instance_name = None
        self._instance_tags = None
        self._instance_type = None
        self._lower_case_table_names = None
        self._maintenance_window = None
        self._node_info = None
        self._port = None
        self._project_name = None
        self._storage_space = None
        self._storage_type = None
        self._subnet_id = None
        self._super_account_name = None
        self._super_account_password = None
        self._vpc_id = None
        self.discriminator = None

        if allow_list_ids is not None:
            self.allow_list_ids = allow_list_ids
        if auto_storage_scaling_config is not None:
            self.auto_storage_scaling_config = auto_storage_scaling_config
        if charge_info is not None:
            self.charge_info = charge_info
        self.db_engine_version = db_engine_version
        if db_param_group_id is not None:
            self.db_param_group_id = db_param_group_id
        if db_time_zone is not None:
            self.db_time_zone = db_time_zone
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_tags is not None:
            self.instance_tags = instance_tags
        if instance_type is not None:
            self.instance_type = instance_type
        if lower_case_table_names is not None:
            self.lower_case_table_names = lower_case_table_names
        if maintenance_window is not None:
            self.maintenance_window = maintenance_window
        if node_info is not None:
            self.node_info = node_info
        if port is not None:
            self.port = port
        if project_name is not None:
            self.project_name = project_name
        self.storage_space = storage_space
        self.storage_type = storage_type
        self.subnet_id = subnet_id
        if super_account_name is not None:
            self.super_account_name = super_account_name
        if super_account_password is not None:
            self.super_account_password = super_account_password
        self.vpc_id = vpc_id

    @property
    def allow_list_ids(self):
        """Gets the allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501


        :return: The allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allow_list_ids

    @allow_list_ids.setter
    def allow_list_ids(self, allow_list_ids):
        """Sets the allow_list_ids of this CreateDBInstanceRequest.


        :param allow_list_ids: The allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501
        :type: list[str]
        """

        self._allow_list_ids = allow_list_ids

    @property
    def auto_storage_scaling_config(self):
        """Gets the auto_storage_scaling_config of this CreateDBInstanceRequest.  # noqa: E501


        :return: The auto_storage_scaling_config of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: AutoStorageScalingConfigForCreateDBInstanceInput
        """
        return self._auto_storage_scaling_config

    @auto_storage_scaling_config.setter
    def auto_storage_scaling_config(self, auto_storage_scaling_config):
        """Sets the auto_storage_scaling_config of this CreateDBInstanceRequest.


        :param auto_storage_scaling_config: The auto_storage_scaling_config of this CreateDBInstanceRequest.  # noqa: E501
        :type: AutoStorageScalingConfigForCreateDBInstanceInput
        """

        self._auto_storage_scaling_config = auto_storage_scaling_config

    @property
    def charge_info(self):
        """Gets the charge_info of this CreateDBInstanceRequest.  # noqa: E501


        :return: The charge_info of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: ChargeInfoForCreateDBInstanceInput
        """
        return self._charge_info

    @charge_info.setter
    def charge_info(self, charge_info):
        """Sets the charge_info of this CreateDBInstanceRequest.


        :param charge_info: The charge_info of this CreateDBInstanceRequest.  # noqa: E501
        :type: ChargeInfoForCreateDBInstanceInput
        """

        self._charge_info = charge_info

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this CreateDBInstanceRequest.  # noqa: E501


        :return: The db_engine_version of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this CreateDBInstanceRequest.


        :param db_engine_version: The db_engine_version of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and db_engine_version is None:
            raise ValueError("Invalid value for `db_engine_version`, must not be `None`")  # noqa: E501

        self._db_engine_version = db_engine_version

    @property
    def db_param_group_id(self):
        """Gets the db_param_group_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The db_param_group_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_param_group_id

    @db_param_group_id.setter
    def db_param_group_id(self, db_param_group_id):
        """Sets the db_param_group_id of this CreateDBInstanceRequest.


        :param db_param_group_id: The db_param_group_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._db_param_group_id = db_param_group_id

    @property
    def db_time_zone(self):
        """Gets the db_time_zone of this CreateDBInstanceRequest.  # noqa: E501


        :return: The db_time_zone of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_time_zone

    @db_time_zone.setter
    def db_time_zone(self, db_time_zone):
        """Sets the db_time_zone of this CreateDBInstanceRequest.


        :param db_time_zone: The db_time_zone of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._db_time_zone = db_time_zone

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this CreateDBInstanceRequest.  # noqa: E501


        :return: The deletion_protection of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this CreateDBInstanceRequest.


        :param deletion_protection: The deletion_protection of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._deletion_protection = deletion_protection

    @property
    def instance_name(self):
        """Gets the instance_name of this CreateDBInstanceRequest.  # noqa: E501


        :return: The instance_name of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this CreateDBInstanceRequest.


        :param instance_name: The instance_name of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_tags(self):
        """Gets the instance_tags of this CreateDBInstanceRequest.  # noqa: E501


        :return: The instance_tags of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: list[InstanceTagForCreateDBInstanceInput]
        """
        return self._instance_tags

    @instance_tags.setter
    def instance_tags(self, instance_tags):
        """Sets the instance_tags of this CreateDBInstanceRequest.


        :param instance_tags: The instance_tags of this CreateDBInstanceRequest.  # noqa: E501
        :type: list[InstanceTagForCreateDBInstanceInput]
        """

        self._instance_tags = instance_tags

    @property
    def instance_type(self):
        """Gets the instance_type of this CreateDBInstanceRequest.  # noqa: E501


        :return: The instance_type of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this CreateDBInstanceRequest.


        :param instance_type: The instance_type of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def lower_case_table_names(self):
        """Gets the lower_case_table_names of this CreateDBInstanceRequest.  # noqa: E501


        :return: The lower_case_table_names of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._lower_case_table_names

    @lower_case_table_names.setter
    def lower_case_table_names(self, lower_case_table_names):
        """Sets the lower_case_table_names of this CreateDBInstanceRequest.


        :param lower_case_table_names: The lower_case_table_names of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._lower_case_table_names = lower_case_table_names

    @property
    def maintenance_window(self):
        """Gets the maintenance_window of this CreateDBInstanceRequest.  # noqa: E501


        :return: The maintenance_window of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: MaintenanceWindowForCreateDBInstanceInput
        """
        return self._maintenance_window

    @maintenance_window.setter
    def maintenance_window(self, maintenance_window):
        """Sets the maintenance_window of this CreateDBInstanceRequest.


        :param maintenance_window: The maintenance_window of this CreateDBInstanceRequest.  # noqa: E501
        :type: MaintenanceWindowForCreateDBInstanceInput
        """

        self._maintenance_window = maintenance_window

    @property
    def node_info(self):
        """Gets the node_info of this CreateDBInstanceRequest.  # noqa: E501


        :return: The node_info of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: list[NodeInfoForCreateDBInstanceInput]
        """
        return self._node_info

    @node_info.setter
    def node_info(self, node_info):
        """Sets the node_info of this CreateDBInstanceRequest.


        :param node_info: The node_info of this CreateDBInstanceRequest.  # noqa: E501
        :type: list[NodeInfoForCreateDBInstanceInput]
        """

        self._node_info = node_info

    @property
    def port(self):
        """Gets the port of this CreateDBInstanceRequest.  # noqa: E501


        :return: The port of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this CreateDBInstanceRequest.


        :param port: The port of this CreateDBInstanceRequest.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def project_name(self):
        """Gets the project_name of this CreateDBInstanceRequest.  # noqa: E501


        :return: The project_name of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateDBInstanceRequest.


        :param project_name: The project_name of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def storage_space(self):
        """Gets the storage_space of this CreateDBInstanceRequest.  # noqa: E501


        :return: The storage_space of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this CreateDBInstanceRequest.


        :param storage_space: The storage_space of this CreateDBInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and storage_space is None:
            raise ValueError("Invalid value for `storage_space`, must not be `None`")  # noqa: E501

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this CreateDBInstanceRequest.  # noqa: E501


        :return: The storage_type of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this CreateDBInstanceRequest.


        :param storage_type: The storage_type of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and storage_type is None:
            raise ValueError("Invalid value for `storage_type`, must not be `None`")  # noqa: E501

        self._storage_type = storage_type

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The subnet_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateDBInstanceRequest.


        :param subnet_id: The subnet_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def super_account_name(self):
        """Gets the super_account_name of this CreateDBInstanceRequest.  # noqa: E501


        :return: The super_account_name of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._super_account_name

    @super_account_name.setter
    def super_account_name(self, super_account_name):
        """Sets the super_account_name of this CreateDBInstanceRequest.


        :param super_account_name: The super_account_name of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._super_account_name = super_account_name

    @property
    def super_account_password(self):
        """Gets the super_account_password of this CreateDBInstanceRequest.  # noqa: E501


        :return: The super_account_password of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._super_account_password

    @super_account_password.setter
    def super_account_password(self, super_account_password):
        """Sets the super_account_password of this CreateDBInstanceRequest.


        :param super_account_password: The super_account_password of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._super_account_password = super_account_password

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The vpc_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateDBInstanceRequest.


        :param vpc_id: The vpc_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
