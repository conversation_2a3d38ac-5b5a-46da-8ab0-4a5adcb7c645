# coding: utf-8

"""
    sqs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListQueuesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'queue_display_name_prefix': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'queue_display_name_prefix': 'QueueDisplayNamePrefix'
    }

    def __init__(self, max_results=None, next_token=None, queue_display_name_prefix=None, _configuration=None):  # noqa: E501
        """ListQueuesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._queue_display_name_prefix = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if queue_display_name_prefix is not None:
            self.queue_display_name_prefix = queue_display_name_prefix

    @property
    def max_results(self):
        """Gets the max_results of this ListQueuesRequest.  # noqa: E501


        :return: The max_results of this ListQueuesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListQueuesRequest.


        :param max_results: The max_results of this ListQueuesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_results is not None and max_results > 100):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_results is not None and max_results < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListQueuesRequest.  # noqa: E501


        :return: The next_token of this ListQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListQueuesRequest.


        :param next_token: The next_token of this ListQueuesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def queue_display_name_prefix(self):
        """Gets the queue_display_name_prefix of this ListQueuesRequest.  # noqa: E501


        :return: The queue_display_name_prefix of this ListQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._queue_display_name_prefix

    @queue_display_name_prefix.setter
    def queue_display_name_prefix(self, queue_display_name_prefix):
        """Sets the queue_display_name_prefix of this ListQueuesRequest.


        :param queue_display_name_prefix: The queue_display_name_prefix of this ListQueuesRequest.  # noqa: E501
        :type: str
        """

        self._queue_display_name_prefix = queue_display_name_prefix

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListQueuesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListQueuesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListQueuesRequest):
            return True

        return self.to_dict() != other.to_dict()
