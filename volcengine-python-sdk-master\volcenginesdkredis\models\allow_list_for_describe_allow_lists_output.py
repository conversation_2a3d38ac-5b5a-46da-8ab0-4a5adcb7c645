# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AllowListForDescribeAllowListsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_list_category': 'str',
        'allow_list_desc': 'str',
        'allow_list_ip_num': 'int',
        'allow_list_id': 'str',
        'allow_list_name': 'str',
        'allow_list_type': 'str',
        'associated_instance_num': 'int',
        'project_name': 'str',
        'security_group_bind_infos': 'list[SecurityGroupBindInfoForDescribeAllowListsOutput]'
    }

    attribute_map = {
        'allow_list_category': 'AllowListCategory',
        'allow_list_desc': 'AllowListDesc',
        'allow_list_ip_num': 'AllowListIPNum',
        'allow_list_id': 'AllowListId',
        'allow_list_name': 'AllowListName',
        'allow_list_type': 'AllowListType',
        'associated_instance_num': 'AssociatedInstanceNum',
        'project_name': 'ProjectName',
        'security_group_bind_infos': 'SecurityGroupBindInfos'
    }

    def __init__(self, allow_list_category=None, allow_list_desc=None, allow_list_ip_num=None, allow_list_id=None, allow_list_name=None, allow_list_type=None, associated_instance_num=None, project_name=None, security_group_bind_infos=None, _configuration=None):  # noqa: E501
        """AllowListForDescribeAllowListsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_list_category = None
        self._allow_list_desc = None
        self._allow_list_ip_num = None
        self._allow_list_id = None
        self._allow_list_name = None
        self._allow_list_type = None
        self._associated_instance_num = None
        self._project_name = None
        self._security_group_bind_infos = None
        self.discriminator = None

        if allow_list_category is not None:
            self.allow_list_category = allow_list_category
        if allow_list_desc is not None:
            self.allow_list_desc = allow_list_desc
        if allow_list_ip_num is not None:
            self.allow_list_ip_num = allow_list_ip_num
        if allow_list_id is not None:
            self.allow_list_id = allow_list_id
        if allow_list_name is not None:
            self.allow_list_name = allow_list_name
        if allow_list_type is not None:
            self.allow_list_type = allow_list_type
        if associated_instance_num is not None:
            self.associated_instance_num = associated_instance_num
        if project_name is not None:
            self.project_name = project_name
        if security_group_bind_infos is not None:
            self.security_group_bind_infos = security_group_bind_infos

    @property
    def allow_list_category(self):
        """Gets the allow_list_category of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_category of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_category

    @allow_list_category.setter
    def allow_list_category(self, allow_list_category):
        """Sets the allow_list_category of this AllowListForDescribeAllowListsOutput.


        :param allow_list_category: The allow_list_category of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_category = allow_list_category

    @property
    def allow_list_desc(self):
        """Gets the allow_list_desc of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_desc of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_desc

    @allow_list_desc.setter
    def allow_list_desc(self, allow_list_desc):
        """Sets the allow_list_desc of this AllowListForDescribeAllowListsOutput.


        :param allow_list_desc: The allow_list_desc of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_desc = allow_list_desc

    @property
    def allow_list_ip_num(self):
        """Gets the allow_list_ip_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_ip_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: int
        """
        return self._allow_list_ip_num

    @allow_list_ip_num.setter
    def allow_list_ip_num(self, allow_list_ip_num):
        """Sets the allow_list_ip_num of this AllowListForDescribeAllowListsOutput.


        :param allow_list_ip_num: The allow_list_ip_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: int
        """

        self._allow_list_ip_num = allow_list_ip_num

    @property
    def allow_list_id(self):
        """Gets the allow_list_id of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_id of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_id

    @allow_list_id.setter
    def allow_list_id(self, allow_list_id):
        """Sets the allow_list_id of this AllowListForDescribeAllowListsOutput.


        :param allow_list_id: The allow_list_id of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_id = allow_list_id

    @property
    def allow_list_name(self):
        """Gets the allow_list_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_name

    @allow_list_name.setter
    def allow_list_name(self, allow_list_name):
        """Sets the allow_list_name of this AllowListForDescribeAllowListsOutput.


        :param allow_list_name: The allow_list_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_name = allow_list_name

    @property
    def allow_list_type(self):
        """Gets the allow_list_type of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The allow_list_type of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_list_type

    @allow_list_type.setter
    def allow_list_type(self, allow_list_type):
        """Sets the allow_list_type of this AllowListForDescribeAllowListsOutput.


        :param allow_list_type: The allow_list_type of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._allow_list_type = allow_list_type

    @property
    def associated_instance_num(self):
        """Gets the associated_instance_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The associated_instance_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: int
        """
        return self._associated_instance_num

    @associated_instance_num.setter
    def associated_instance_num(self, associated_instance_num):
        """Sets the associated_instance_num of this AllowListForDescribeAllowListsOutput.


        :param associated_instance_num: The associated_instance_num of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: int
        """

        self._associated_instance_num = associated_instance_num

    @property
    def project_name(self):
        """Gets the project_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The project_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this AllowListForDescribeAllowListsOutput.


        :param project_name: The project_name of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def security_group_bind_infos(self):
        """Gets the security_group_bind_infos of this AllowListForDescribeAllowListsOutput.  # noqa: E501


        :return: The security_group_bind_infos of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :rtype: list[SecurityGroupBindInfoForDescribeAllowListsOutput]
        """
        return self._security_group_bind_infos

    @security_group_bind_infos.setter
    def security_group_bind_infos(self, security_group_bind_infos):
        """Sets the security_group_bind_infos of this AllowListForDescribeAllowListsOutput.


        :param security_group_bind_infos: The security_group_bind_infos of this AllowListForDescribeAllowListsOutput.  # noqa: E501
        :type: list[SecurityGroupBindInfoForDescribeAllowListsOutput]
        """

        self._security_group_bind_infos = security_group_bind_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AllowListForDescribeAllowListsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AllowListForDescribeAllowListsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AllowListForDescribeAllowListsOutput):
            return True

        return self.to_dict() != other.to_dict()
