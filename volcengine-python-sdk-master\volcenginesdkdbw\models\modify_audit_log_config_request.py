# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyAuditLogConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'instance_type': 'str',
        'sql_retention_day': 'int'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'sql_retention_day': 'SqlRetentionDay'
    }

    def __init__(self, instance_id=None, instance_type=None, sql_retention_day=None, _configuration=None):  # noqa: E501
        """ModifyAuditLogConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._instance_type = None
        self._sql_retention_day = None
        self.discriminator = None

        self.instance_id = instance_id
        self.instance_type = instance_type
        if sql_retention_day is not None:
            self.sql_retention_day = sql_retention_day

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyAuditLogConfigRequest.  # noqa: E501


        :return: The instance_id of this ModifyAuditLogConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyAuditLogConfigRequest.


        :param instance_id: The instance_id of this ModifyAuditLogConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this ModifyAuditLogConfigRequest.  # noqa: E501


        :return: The instance_type of this ModifyAuditLogConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this ModifyAuditLogConfigRequest.


        :param instance_type: The instance_type of this ModifyAuditLogConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_type is None:
            raise ValueError("Invalid value for `instance_type`, must not be `None`")  # noqa: E501
        allowed_values = ["MySQL", "Postgres", "Mongo", "Redis", "VeDBMySQL", "MetaRDS", "MSSQL", "ByteRDS", "MySQLSharding", "MetaMySQL"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def sql_retention_day(self):
        """Gets the sql_retention_day of this ModifyAuditLogConfigRequest.  # noqa: E501


        :return: The sql_retention_day of this ModifyAuditLogConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._sql_retention_day

    @sql_retention_day.setter
    def sql_retention_day(self, sql_retention_day):
        """Sets the sql_retention_day of this ModifyAuditLogConfigRequest.


        :param sql_retention_day: The sql_retention_day of this ModifyAuditLogConfigRequest.  # noqa: E501
        :type: int
        """

        self._sql_retention_day = sql_retention_day

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyAuditLogConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyAuditLogConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyAuditLogConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
