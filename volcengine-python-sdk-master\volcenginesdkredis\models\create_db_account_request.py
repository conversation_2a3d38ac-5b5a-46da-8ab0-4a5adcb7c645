# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBAccountRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_name': 'str',
        'client_token': 'str',
        'description': 'str',
        'instance_id': 'str',
        'password': 'str',
        'role_name': 'str'
    }

    attribute_map = {
        'account_name': 'AccountName',
        'client_token': 'ClientToken',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'password': 'Password',
        'role_name': 'RoleName'
    }

    def __init__(self, account_name=None, client_token=None, description=None, instance_id=None, password=None, role_name=None, _configuration=None):  # noqa: E501
        """CreateDBAccountRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_name = None
        self._client_token = None
        self._description = None
        self._instance_id = None
        self._password = None
        self._role_name = None
        self.discriminator = None

        self.account_name = account_name
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.instance_id = instance_id
        self.password = password
        self.role_name = role_name

    @property
    def account_name(self):
        """Gets the account_name of this CreateDBAccountRequest.  # noqa: E501


        :return: The account_name of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this CreateDBAccountRequest.


        :param account_name: The account_name of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_name is None:
            raise ValueError("Invalid value for `account_name`, must not be `None`")  # noqa: E501

        self._account_name = account_name

    @property
    def client_token(self):
        """Gets the client_token of this CreateDBAccountRequest.  # noqa: E501


        :return: The client_token of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateDBAccountRequest.


        :param client_token: The client_token of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateDBAccountRequest.  # noqa: E501


        :return: The description of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDBAccountRequest.


        :param description: The description of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDBAccountRequest.  # noqa: E501


        :return: The instance_id of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDBAccountRequest.


        :param instance_id: The instance_id of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def password(self):
        """Gets the password of this CreateDBAccountRequest.  # noqa: E501


        :return: The password of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this CreateDBAccountRequest.


        :param password: The password of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and password is None:
            raise ValueError("Invalid value for `password`, must not be `None`")  # noqa: E501

        self._password = password

    @property
    def role_name(self):
        """Gets the role_name of this CreateDBAccountRequest.  # noqa: E501


        :return: The role_name of this CreateDBAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this CreateDBAccountRequest.


        :param role_name: The role_name of this CreateDBAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and role_name is None:
            raise ValueError("Invalid value for `role_name`, must not be `None`")  # noqa: E501

        self._role_name = role_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBAccountRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBAccountRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBAccountRequest):
            return True

        return self.to_dict() != other.to_dict()
