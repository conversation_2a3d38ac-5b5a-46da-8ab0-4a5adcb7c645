# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HandleVulnRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'after_status': 'str',
        'agent_id_list': 'list[str]',
        'asset_ids': 'list[str]',
        'asset_type': 'str',
        'before_status': 'str',
        'cwpp_id_list': 'list[str]',
        'reason': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'after_status': 'AfterStatus',
        'agent_id_list': 'AgentIDList',
        'asset_ids': 'AssetIDs',
        'asset_type': 'AssetType',
        'before_status': 'BeforeStatus',
        'cwpp_id_list': 'CwppIDList',
        'reason': 'Reason',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, after_status=None, agent_id_list=None, asset_ids=None, asset_type=None, before_status=None, cwpp_id_list=None, reason=None, top_group_id=None, _configuration=None):  # noqa: E501
        """HandleVulnRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._after_status = None
        self._agent_id_list = None
        self._asset_ids = None
        self._asset_type = None
        self._before_status = None
        self._cwpp_id_list = None
        self._reason = None
        self._top_group_id = None
        self.discriminator = None

        if after_status is not None:
            self.after_status = after_status
        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if asset_ids is not None:
            self.asset_ids = asset_ids
        if asset_type is not None:
            self.asset_type = asset_type
        if before_status is not None:
            self.before_status = before_status
        if cwpp_id_list is not None:
            self.cwpp_id_list = cwpp_id_list
        if reason is not None:
            self.reason = reason
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def after_status(self):
        """Gets the after_status of this HandleVulnRequest.  # noqa: E501


        :return: The after_status of this HandleVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._after_status

    @after_status.setter
    def after_status(self, after_status):
        """Sets the after_status of this HandleVulnRequest.


        :param after_status: The after_status of this HandleVulnRequest.  # noqa: E501
        :type: str
        """

        self._after_status = after_status

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this HandleVulnRequest.  # noqa: E501


        :return: The agent_id_list of this HandleVulnRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this HandleVulnRequest.


        :param agent_id_list: The agent_id_list of this HandleVulnRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def asset_ids(self):
        """Gets the asset_ids of this HandleVulnRequest.  # noqa: E501


        :return: The asset_ids of this HandleVulnRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_ids

    @asset_ids.setter
    def asset_ids(self, asset_ids):
        """Sets the asset_ids of this HandleVulnRequest.


        :param asset_ids: The asset_ids of this HandleVulnRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_ids = asset_ids

    @property
    def asset_type(self):
        """Gets the asset_type of this HandleVulnRequest.  # noqa: E501


        :return: The asset_type of this HandleVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this HandleVulnRequest.


        :param asset_type: The asset_type of this HandleVulnRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def before_status(self):
        """Gets the before_status of this HandleVulnRequest.  # noqa: E501


        :return: The before_status of this HandleVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._before_status

    @before_status.setter
    def before_status(self, before_status):
        """Sets the before_status of this HandleVulnRequest.


        :param before_status: The before_status of this HandleVulnRequest.  # noqa: E501
        :type: str
        """

        self._before_status = before_status

    @property
    def cwpp_id_list(self):
        """Gets the cwpp_id_list of this HandleVulnRequest.  # noqa: E501


        :return: The cwpp_id_list of this HandleVulnRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cwpp_id_list

    @cwpp_id_list.setter
    def cwpp_id_list(self, cwpp_id_list):
        """Sets the cwpp_id_list of this HandleVulnRequest.


        :param cwpp_id_list: The cwpp_id_list of this HandleVulnRequest.  # noqa: E501
        :type: list[str]
        """

        self._cwpp_id_list = cwpp_id_list

    @property
    def reason(self):
        """Gets the reason of this HandleVulnRequest.  # noqa: E501


        :return: The reason of this HandleVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this HandleVulnRequest.


        :param reason: The reason of this HandleVulnRequest.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def top_group_id(self):
        """Gets the top_group_id of this HandleVulnRequest.  # noqa: E501


        :return: The top_group_id of this HandleVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this HandleVulnRequest.


        :param top_group_id: The top_group_id of this HandleVulnRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HandleVulnRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HandleVulnRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HandleVulnRequest):
            return True

        return self.to_dict() != other.to_dict()
