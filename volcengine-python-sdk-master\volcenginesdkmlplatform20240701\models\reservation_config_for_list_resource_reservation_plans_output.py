# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReservationConfigForListResourceReservationPlansOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_duration_hours': 'int',
        'min_duration_hours': 'int',
        'recurrence_end_time': 'str',
        'recurrence_interval': 'str',
        'recurrence_start_time': 'str',
        'reservation_type': 'str'
    }

    attribute_map = {
        'max_duration_hours': 'MaxDurationHours',
        'min_duration_hours': 'MinDurationHours',
        'recurrence_end_time': 'RecurrenceEndTime',
        'recurrence_interval': 'RecurrenceInterval',
        'recurrence_start_time': 'RecurrenceStartTime',
        'reservation_type': 'ReservationType'
    }

    def __init__(self, max_duration_hours=None, min_duration_hours=None, recurrence_end_time=None, recurrence_interval=None, recurrence_start_time=None, reservation_type=None, _configuration=None):  # noqa: E501
        """ReservationConfigForListResourceReservationPlansOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_duration_hours = None
        self._min_duration_hours = None
        self._recurrence_end_time = None
        self._recurrence_interval = None
        self._recurrence_start_time = None
        self._reservation_type = None
        self.discriminator = None

        if max_duration_hours is not None:
            self.max_duration_hours = max_duration_hours
        if min_duration_hours is not None:
            self.min_duration_hours = min_duration_hours
        if recurrence_end_time is not None:
            self.recurrence_end_time = recurrence_end_time
        if recurrence_interval is not None:
            self.recurrence_interval = recurrence_interval
        if recurrence_start_time is not None:
            self.recurrence_start_time = recurrence_start_time
        if reservation_type is not None:
            self.reservation_type = reservation_type

    @property
    def max_duration_hours(self):
        """Gets the max_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The max_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_duration_hours

    @max_duration_hours.setter
    def max_duration_hours(self, max_duration_hours):
        """Sets the max_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.


        :param max_duration_hours: The max_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: int
        """

        self._max_duration_hours = max_duration_hours

    @property
    def min_duration_hours(self):
        """Gets the min_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The min_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: int
        """
        return self._min_duration_hours

    @min_duration_hours.setter
    def min_duration_hours(self, min_duration_hours):
        """Sets the min_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.


        :param min_duration_hours: The min_duration_hours of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: int
        """

        self._min_duration_hours = min_duration_hours

    @property
    def recurrence_end_time(self):
        """Gets the recurrence_end_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The recurrence_end_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._recurrence_end_time

    @recurrence_end_time.setter
    def recurrence_end_time(self, recurrence_end_time):
        """Sets the recurrence_end_time of this ReservationConfigForListResourceReservationPlansOutput.


        :param recurrence_end_time: The recurrence_end_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._recurrence_end_time = recurrence_end_time

    @property
    def recurrence_interval(self):
        """Gets the recurrence_interval of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The recurrence_interval of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._recurrence_interval

    @recurrence_interval.setter
    def recurrence_interval(self, recurrence_interval):
        """Sets the recurrence_interval of this ReservationConfigForListResourceReservationPlansOutput.


        :param recurrence_interval: The recurrence_interval of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._recurrence_interval = recurrence_interval

    @property
    def recurrence_start_time(self):
        """Gets the recurrence_start_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The recurrence_start_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._recurrence_start_time

    @recurrence_start_time.setter
    def recurrence_start_time(self, recurrence_start_time):
        """Sets the recurrence_start_time of this ReservationConfigForListResourceReservationPlansOutput.


        :param recurrence_start_time: The recurrence_start_time of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._recurrence_start_time = recurrence_start_time

    @property
    def reservation_type(self):
        """Gets the reservation_type of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501


        :return: The reservation_type of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._reservation_type

    @reservation_type.setter
    def reservation_type(self, reservation_type):
        """Sets the reservation_type of this ReservationConfigForListResourceReservationPlansOutput.


        :param reservation_type: The reservation_type of this ReservationConfigForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._reservation_type = reservation_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReservationConfigForListResourceReservationPlansOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReservationConfigForListResourceReservationPlansOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReservationConfigForListResourceReservationPlansOutput):
            return True

        return self.to_dict() != other.to_dict()
