# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResolverEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direction': 'str',
        'endpoint_type': 'str',
        'ip_configs': 'list[IpConfigForCreateResolverEndpointInput]',
        'name': 'str',
        'project_name': 'str',
        'security_group_id': 'str',
        'tags': 'list[TagForCreateResolverEndpointInput]',
        'vpc_id': 'str',
        'vpc_region': 'str',
        'vpc_trns': 'list[str]'
    }

    attribute_map = {
        'direction': 'Direction',
        'endpoint_type': 'EndpointType',
        'ip_configs': 'IpConfigs',
        'name': 'Name',
        'project_name': 'ProjectName',
        'security_group_id': 'SecurityGroupID',
        'tags': 'Tags',
        'vpc_id': 'VpcID',
        'vpc_region': 'VpcRegion',
        'vpc_trns': 'VpcTrns'
    }

    def __init__(self, direction=None, endpoint_type=None, ip_configs=None, name=None, project_name=None, security_group_id=None, tags=None, vpc_id=None, vpc_region=None, vpc_trns=None, _configuration=None):  # noqa: E501
        """CreateResolverEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direction = None
        self._endpoint_type = None
        self._ip_configs = None
        self._name = None
        self._project_name = None
        self._security_group_id = None
        self._tags = None
        self._vpc_id = None
        self._vpc_region = None
        self._vpc_trns = None
        self.discriminator = None

        if direction is not None:
            self.direction = direction
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if ip_configs is not None:
            self.ip_configs = ip_configs
        self.name = name
        if project_name is not None:
            self.project_name = project_name
        if security_group_id is not None:
            self.security_group_id = security_group_id
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id
        self.vpc_region = vpc_region
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns

    @property
    def direction(self):
        """Gets the direction of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The direction of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this CreateResolverEndpointRequest.


        :param direction: The direction of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._direction = direction

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_type of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this CreateResolverEndpointRequest.


        :param endpoint_type: The endpoint_type of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def ip_configs(self):
        """Gets the ip_configs of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The ip_configs of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: list[IpConfigForCreateResolverEndpointInput]
        """
        return self._ip_configs

    @ip_configs.setter
    def ip_configs(self, ip_configs):
        """Sets the ip_configs of this CreateResolverEndpointRequest.


        :param ip_configs: The ip_configs of this CreateResolverEndpointRequest.  # noqa: E501
        :type: list[IpConfigForCreateResolverEndpointInput]
        """

        self._ip_configs = ip_configs

    @property
    def name(self):
        """Gets the name of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The name of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResolverEndpointRequest.


        :param name: The name of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The project_name of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateResolverEndpointRequest.


        :param project_name: The project_name of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def security_group_id(self):
        """Gets the security_group_id of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The security_group_id of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this CreateResolverEndpointRequest.


        :param security_group_id: The security_group_id of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    @property
    def tags(self):
        """Gets the tags of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The tags of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: list[TagForCreateResolverEndpointInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateResolverEndpointRequest.


        :param tags: The tags of this CreateResolverEndpointRequest.  # noqa: E501
        :type: list[TagForCreateResolverEndpointInput]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The vpc_id of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateResolverEndpointRequest.


        :param vpc_id: The vpc_id of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def vpc_region(self):
        """Gets the vpc_region of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The vpc_region of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_region

    @vpc_region.setter
    def vpc_region(self, vpc_region):
        """Sets the vpc_region of this CreateResolverEndpointRequest.


        :param vpc_region: The vpc_region of this CreateResolverEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_region is None:
            raise ValueError("Invalid value for `vpc_region`, must not be `None`")  # noqa: E501

        self._vpc_region = vpc_region

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this CreateResolverEndpointRequest.  # noqa: E501


        :return: The vpc_trns of this CreateResolverEndpointRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this CreateResolverEndpointRequest.


        :param vpc_trns: The vpc_trns of this CreateResolverEndpointRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResolverEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResolverEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResolverEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
