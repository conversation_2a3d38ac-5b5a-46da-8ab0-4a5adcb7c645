# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HistoryVersionForGetDeploymentOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'change_type': 'str',
        'create_time': 'str',
        'id': 'str',
        'priority': 'int',
        'resource_queue_id': 'str',
        'roles': 'list[RoleForGetDeploymentOutput]'
    }

    attribute_map = {
        'change_type': 'ChangeType',
        'create_time': 'CreateTime',
        'id': 'Id',
        'priority': 'Priority',
        'resource_queue_id': 'ResourceQueueId',
        'roles': 'Roles'
    }

    def __init__(self, change_type=None, create_time=None, id=None, priority=None, resource_queue_id=None, roles=None, _configuration=None):  # noqa: E501
        """HistoryVersionForGetDeploymentOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._change_type = None
        self._create_time = None
        self._id = None
        self._priority = None
        self._resource_queue_id = None
        self._roles = None
        self.discriminator = None

        if change_type is not None:
            self.change_type = change_type
        if create_time is not None:
            self.create_time = create_time
        if id is not None:
            self.id = id
        if priority is not None:
            self.priority = priority
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if roles is not None:
            self.roles = roles

    @property
    def change_type(self):
        """Gets the change_type of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The change_type of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._change_type

    @change_type.setter
    def change_type(self, change_type):
        """Sets the change_type of this HistoryVersionForGetDeploymentOutput.


        :param change_type: The change_type of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._change_type = change_type

    @property
    def create_time(self):
        """Gets the create_time of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The create_time of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this HistoryVersionForGetDeploymentOutput.


        :param create_time: The create_time of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def id(self):
        """Gets the id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this HistoryVersionForGetDeploymentOutput.


        :param id: The id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def priority(self):
        """Gets the priority of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The priority of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this HistoryVersionForGetDeploymentOutput.


        :param priority: The priority of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The resource_queue_id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this HistoryVersionForGetDeploymentOutput.


        :param resource_queue_id: The resource_queue_id of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def roles(self):
        """Gets the roles of this HistoryVersionForGetDeploymentOutput.  # noqa: E501


        :return: The roles of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :rtype: list[RoleForGetDeploymentOutput]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this HistoryVersionForGetDeploymentOutput.


        :param roles: The roles of this HistoryVersionForGetDeploymentOutput.  # noqa: E501
        :type: list[RoleForGetDeploymentOutput]
        """

        self._roles = roles

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HistoryVersionForGetDeploymentOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HistoryVersionForGetDeploymentOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HistoryVersionForGetDeploymentOutput):
            return True

        return self.to_dict() != other.to_dict()
