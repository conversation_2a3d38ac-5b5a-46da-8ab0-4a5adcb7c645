# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PortForGetDevDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_public_network_access': 'bool',
        'external_ip': 'str',
        'external_port': 'int',
        'internal_ip': 'str',
        'internal_port': 'int',
        'name': 'str',
        'private_endpoint_url': 'str',
        'public_endpoint_url': 'str',
        'state': 'str',
        'state_hint': 'str',
        'type': 'str'
    }

    attribute_map = {
        'enable_public_network_access': 'EnablePublicNetworkAccess',
        'external_ip': 'ExternalIp',
        'external_port': 'ExternalPort',
        'internal_ip': 'InternalIp',
        'internal_port': 'InternalPort',
        'name': 'Name',
        'private_endpoint_url': 'PrivateEndpointUrl',
        'public_endpoint_url': 'PublicEndpointUrl',
        'state': 'State',
        'state_hint': 'StateHint',
        'type': 'Type'
    }

    def __init__(self, enable_public_network_access=None, external_ip=None, external_port=None, internal_ip=None, internal_port=None, name=None, private_endpoint_url=None, public_endpoint_url=None, state=None, state_hint=None, type=None, _configuration=None):  # noqa: E501
        """PortForGetDevDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_public_network_access = None
        self._external_ip = None
        self._external_port = None
        self._internal_ip = None
        self._internal_port = None
        self._name = None
        self._private_endpoint_url = None
        self._public_endpoint_url = None
        self._state = None
        self._state_hint = None
        self._type = None
        self.discriminator = None

        if enable_public_network_access is not None:
            self.enable_public_network_access = enable_public_network_access
        if external_ip is not None:
            self.external_ip = external_ip
        if external_port is not None:
            self.external_port = external_port
        if internal_ip is not None:
            self.internal_ip = internal_ip
        if internal_port is not None:
            self.internal_port = internal_port
        if name is not None:
            self.name = name
        if private_endpoint_url is not None:
            self.private_endpoint_url = private_endpoint_url
        if public_endpoint_url is not None:
            self.public_endpoint_url = public_endpoint_url
        if state is not None:
            self.state = state
        if state_hint is not None:
            self.state_hint = state_hint
        if type is not None:
            self.type = type

    @property
    def enable_public_network_access(self):
        """Gets the enable_public_network_access of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The enable_public_network_access of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_public_network_access

    @enable_public_network_access.setter
    def enable_public_network_access(self, enable_public_network_access):
        """Sets the enable_public_network_access of this PortForGetDevDetailOutput.


        :param enable_public_network_access: The enable_public_network_access of this PortForGetDevDetailOutput.  # noqa: E501
        :type: bool
        """

        self._enable_public_network_access = enable_public_network_access

    @property
    def external_ip(self):
        """Gets the external_ip of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The external_ip of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_ip

    @external_ip.setter
    def external_ip(self, external_ip):
        """Sets the external_ip of this PortForGetDevDetailOutput.


        :param external_ip: The external_ip of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._external_ip = external_ip

    @property
    def external_port(self):
        """Gets the external_port of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The external_port of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._external_port

    @external_port.setter
    def external_port(self, external_port):
        """Sets the external_port of this PortForGetDevDetailOutput.


        :param external_port: The external_port of this PortForGetDevDetailOutput.  # noqa: E501
        :type: int
        """

        self._external_port = external_port

    @property
    def internal_ip(self):
        """Gets the internal_ip of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The internal_ip of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._internal_ip

    @internal_ip.setter
    def internal_ip(self, internal_ip):
        """Sets the internal_ip of this PortForGetDevDetailOutput.


        :param internal_ip: The internal_ip of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._internal_ip = internal_ip

    @property
    def internal_port(self):
        """Gets the internal_port of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The internal_port of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._internal_port

    @internal_port.setter
    def internal_port(self, internal_port):
        """Sets the internal_port of this PortForGetDevDetailOutput.


        :param internal_port: The internal_port of this PortForGetDevDetailOutput.  # noqa: E501
        :type: int
        """

        self._internal_port = internal_port

    @property
    def name(self):
        """Gets the name of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The name of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this PortForGetDevDetailOutput.


        :param name: The name of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def private_endpoint_url(self):
        """Gets the private_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The private_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_endpoint_url

    @private_endpoint_url.setter
    def private_endpoint_url(self, private_endpoint_url):
        """Sets the private_endpoint_url of this PortForGetDevDetailOutput.


        :param private_endpoint_url: The private_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._private_endpoint_url = private_endpoint_url

    @property
    def public_endpoint_url(self):
        """Gets the public_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The public_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_endpoint_url

    @public_endpoint_url.setter
    def public_endpoint_url(self, public_endpoint_url):
        """Sets the public_endpoint_url of this PortForGetDevDetailOutput.


        :param public_endpoint_url: The public_endpoint_url of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._public_endpoint_url = public_endpoint_url

    @property
    def state(self):
        """Gets the state of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The state of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this PortForGetDevDetailOutput.


        :param state: The state of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def state_hint(self):
        """Gets the state_hint of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The state_hint of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._state_hint

    @state_hint.setter
    def state_hint(self, state_hint):
        """Sets the state_hint of this PortForGetDevDetailOutput.


        :param state_hint: The state_hint of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._state_hint = state_hint

    @property
    def type(self):
        """Gets the type of this PortForGetDevDetailOutput.  # noqa: E501


        :return: The type of this PortForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this PortForGetDevDetailOutput.


        :param type: The type of this PortForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PortForGetDevDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PortForGetDevDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PortForGetDevDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
