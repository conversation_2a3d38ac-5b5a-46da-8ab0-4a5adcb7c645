# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserQuotaInfoForDescribeDirQuotasOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_count_limit': 'int',
        'file_count_real': 'int',
        'quota_type': 'str',
        'size_limit': 'int',
        'size_real': 'int',
        'user_type': 'str'
    }

    attribute_map = {
        'file_count_limit': 'FileCountLimit',
        'file_count_real': 'FileCountReal',
        'quota_type': 'QuotaType',
        'size_limit': 'SizeLimit',
        'size_real': 'SizeReal',
        'user_type': 'UserType'
    }

    def __init__(self, file_count_limit=None, file_count_real=None, quota_type=None, size_limit=None, size_real=None, user_type=None, _configuration=None):  # noqa: E501
        """UserQuotaInfoForDescribeDirQuotasOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_count_limit = None
        self._file_count_real = None
        self._quota_type = None
        self._size_limit = None
        self._size_real = None
        self._user_type = None
        self.discriminator = None

        if file_count_limit is not None:
            self.file_count_limit = file_count_limit
        if file_count_real is not None:
            self.file_count_real = file_count_real
        if quota_type is not None:
            self.quota_type = quota_type
        if size_limit is not None:
            self.size_limit = size_limit
        if size_real is not None:
            self.size_real = size_real
        if user_type is not None:
            self.user_type = user_type

    @property
    def file_count_limit(self):
        """Gets the file_count_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The file_count_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_count_limit

    @file_count_limit.setter
    def file_count_limit(self, file_count_limit):
        """Sets the file_count_limit of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param file_count_limit: The file_count_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: int
        """

        self._file_count_limit = file_count_limit

    @property
    def file_count_real(self):
        """Gets the file_count_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The file_count_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_count_real

    @file_count_real.setter
    def file_count_real(self, file_count_real):
        """Sets the file_count_real of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param file_count_real: The file_count_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: int
        """

        self._file_count_real = file_count_real

    @property
    def quota_type(self):
        """Gets the quota_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The quota_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param quota_type: The quota_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    @property
    def size_limit(self):
        """Gets the size_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The size_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: int
        """
        return self._size_limit

    @size_limit.setter
    def size_limit(self, size_limit):
        """Sets the size_limit of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param size_limit: The size_limit of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: int
        """

        self._size_limit = size_limit

    @property
    def size_real(self):
        """Gets the size_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The size_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: int
        """
        return self._size_real

    @size_real.setter
    def size_real(self, size_real):
        """Sets the size_real of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param size_real: The size_real of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: int
        """

        self._size_real = size_real

    @property
    def user_type(self):
        """Gets the user_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501


        :return: The user_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_type

    @user_type.setter
    def user_type(self, user_type):
        """Sets the user_type of this UserQuotaInfoForDescribeDirQuotasOutput.


        :param user_type: The user_type of this UserQuotaInfoForDescribeDirQuotasOutput.  # noqa: E501
        :type: str
        """

        self._user_type = user_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserQuotaInfoForDescribeDirQuotasOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserQuotaInfoForDescribeDirQuotasOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserQuotaInfoForDescribeDirQuotasOutput):
            return True

        return self.to_dict() != other.to_dict()
