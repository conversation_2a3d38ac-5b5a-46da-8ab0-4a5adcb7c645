# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListRuleFilesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'description': 'str',
        'id': 'str',
        'last_update_time': 'str',
        'name': 'str',
        'rule_count': 'int',
        'status': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'id': 'Id',
        'last_update_time': 'LastUpdateTime',
        'name': 'Name',
        'rule_count': 'RuleCount',
        'status': 'Status'
    }

    def __init__(self, create_time=None, description=None, id=None, last_update_time=None, name=None, rule_count=None, status=None, _configuration=None):  # noqa: E501
        """ItemForListRuleFilesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._id = None
        self._last_update_time = None
        self._name = None
        self._rule_count = None
        self._status = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if last_update_time is not None:
            self.last_update_time = last_update_time
        if name is not None:
            self.name = name
        if rule_count is not None:
            self.rule_count = rule_count
        if status is not None:
            self.status = status

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The create_time of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListRuleFilesOutput.


        :param create_time: The create_time of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The description of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListRuleFilesOutput.


        :param description: The description of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The id of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListRuleFilesOutput.


        :param id: The id of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def last_update_time(self):
        """Gets the last_update_time of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The last_update_time of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_update_time

    @last_update_time.setter
    def last_update_time(self, last_update_time):
        """Sets the last_update_time of this ItemForListRuleFilesOutput.


        :param last_update_time: The last_update_time of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._last_update_time = last_update_time

    @property
    def name(self):
        """Gets the name of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The name of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListRuleFilesOutput.


        :param name: The name of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def rule_count(self):
        """Gets the rule_count of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The rule_count of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: int
        """
        return self._rule_count

    @rule_count.setter
    def rule_count(self, rule_count):
        """Sets the rule_count of this ItemForListRuleFilesOutput.


        :param rule_count: The rule_count of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: int
        """

        self._rule_count = rule_count

    @property
    def status(self):
        """Gets the status of this ItemForListRuleFilesOutput.  # noqa: E501


        :return: The status of this ItemForListRuleFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListRuleFilesOutput.


        :param status: The status of this ItemForListRuleFilesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListRuleFilesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListRuleFilesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListRuleFilesOutput):
            return True

        return self.to_dict() != other.to_dict()
