# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ViewingRestrictionForUpdateViewingRestrictionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comment_restriction': 'CommentRestrictionForUpdateViewingRestrictionInput',
        'custom_viewing_restriction': 'CustomViewingRestrictionForUpdateViewingRestrictionInput',
        'password_viewing_restriction': 'PasswordViewingRestrictionForUpdateViewingRestrictionInput',
        'phone_viewing_restriction': 'PhoneViewingRestrictionForUpdateViewingRestrictionInput',
        'viewing_account_type': 'int',
        'viewing_restriction_button_title': 'str',
        'white_list_viewing_restriction': 'ConvertWhiteListViewingRestrictionForUpdateViewingRestrictionInput'
    }

    attribute_map = {
        'comment_restriction': 'CommentRestriction',
        'custom_viewing_restriction': 'CustomViewingRestriction',
        'password_viewing_restriction': 'PasswordViewingRestriction',
        'phone_viewing_restriction': 'PhoneViewingRestriction',
        'viewing_account_type': 'ViewingAccountType',
        'viewing_restriction_button_title': 'ViewingRestrictionButtonTitle',
        'white_list_viewing_restriction': 'WhiteListViewingRestriction'
    }

    def __init__(self, comment_restriction=None, custom_viewing_restriction=None, password_viewing_restriction=None, phone_viewing_restriction=None, viewing_account_type=None, viewing_restriction_button_title=None, white_list_viewing_restriction=None, _configuration=None):  # noqa: E501
        """ViewingRestrictionForUpdateViewingRestrictionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comment_restriction = None
        self._custom_viewing_restriction = None
        self._password_viewing_restriction = None
        self._phone_viewing_restriction = None
        self._viewing_account_type = None
        self._viewing_restriction_button_title = None
        self._white_list_viewing_restriction = None
        self.discriminator = None

        if comment_restriction is not None:
            self.comment_restriction = comment_restriction
        if custom_viewing_restriction is not None:
            self.custom_viewing_restriction = custom_viewing_restriction
        if password_viewing_restriction is not None:
            self.password_viewing_restriction = password_viewing_restriction
        if phone_viewing_restriction is not None:
            self.phone_viewing_restriction = phone_viewing_restriction
        if viewing_account_type is not None:
            self.viewing_account_type = viewing_account_type
        if viewing_restriction_button_title is not None:
            self.viewing_restriction_button_title = viewing_restriction_button_title
        if white_list_viewing_restriction is not None:
            self.white_list_viewing_restriction = white_list_viewing_restriction

    @property
    def comment_restriction(self):
        """Gets the comment_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The comment_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: CommentRestrictionForUpdateViewingRestrictionInput
        """
        return self._comment_restriction

    @comment_restriction.setter
    def comment_restriction(self, comment_restriction):
        """Sets the comment_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param comment_restriction: The comment_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: CommentRestrictionForUpdateViewingRestrictionInput
        """

        self._comment_restriction = comment_restriction

    @property
    def custom_viewing_restriction(self):
        """Gets the custom_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The custom_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: CustomViewingRestrictionForUpdateViewingRestrictionInput
        """
        return self._custom_viewing_restriction

    @custom_viewing_restriction.setter
    def custom_viewing_restriction(self, custom_viewing_restriction):
        """Sets the custom_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param custom_viewing_restriction: The custom_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: CustomViewingRestrictionForUpdateViewingRestrictionInput
        """

        self._custom_viewing_restriction = custom_viewing_restriction

    @property
    def password_viewing_restriction(self):
        """Gets the password_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The password_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: PasswordViewingRestrictionForUpdateViewingRestrictionInput
        """
        return self._password_viewing_restriction

    @password_viewing_restriction.setter
    def password_viewing_restriction(self, password_viewing_restriction):
        """Sets the password_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param password_viewing_restriction: The password_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: PasswordViewingRestrictionForUpdateViewingRestrictionInput
        """

        self._password_viewing_restriction = password_viewing_restriction

    @property
    def phone_viewing_restriction(self):
        """Gets the phone_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The phone_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: PhoneViewingRestrictionForUpdateViewingRestrictionInput
        """
        return self._phone_viewing_restriction

    @phone_viewing_restriction.setter
    def phone_viewing_restriction(self, phone_viewing_restriction):
        """Sets the phone_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param phone_viewing_restriction: The phone_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: PhoneViewingRestrictionForUpdateViewingRestrictionInput
        """

        self._phone_viewing_restriction = phone_viewing_restriction

    @property
    def viewing_account_type(self):
        """Gets the viewing_account_type of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The viewing_account_type of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: int
        """
        return self._viewing_account_type

    @viewing_account_type.setter
    def viewing_account_type(self, viewing_account_type):
        """Sets the viewing_account_type of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param viewing_account_type: The viewing_account_type of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: int
        """

        self._viewing_account_type = viewing_account_type

    @property
    def viewing_restriction_button_title(self):
        """Gets the viewing_restriction_button_title of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The viewing_restriction_button_title of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: str
        """
        return self._viewing_restriction_button_title

    @viewing_restriction_button_title.setter
    def viewing_restriction_button_title(self, viewing_restriction_button_title):
        """Sets the viewing_restriction_button_title of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param viewing_restriction_button_title: The viewing_restriction_button_title of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: str
        """

        self._viewing_restriction_button_title = viewing_restriction_button_title

    @property
    def white_list_viewing_restriction(self):
        """Gets the white_list_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The white_list_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: ConvertWhiteListViewingRestrictionForUpdateViewingRestrictionInput
        """
        return self._white_list_viewing_restriction

    @white_list_viewing_restriction.setter
    def white_list_viewing_restriction(self, white_list_viewing_restriction):
        """Sets the white_list_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.


        :param white_list_viewing_restriction: The white_list_viewing_restriction of this ViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: ConvertWhiteListViewingRestrictionForUpdateViewingRestrictionInput
        """

        self._white_list_viewing_restriction = white_list_viewing_restriction

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ViewingRestrictionForUpdateViewingRestrictionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ViewingRestrictionForUpdateViewingRestrictionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ViewingRestrictionForUpdateViewingRestrictionInput):
            return True

        return self.to_dict() != other.to_dict()
