# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MongosNodeSpecForDescribeNodeSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu_num': 'float',
        'max_conn': 'int',
        'mem_in_gb': 'float',
        'spec_name': 'str'
    }

    attribute_map = {
        'cpu_num': 'CpuNum',
        'max_conn': 'MaxConn',
        'mem_in_gb': 'MemInGb',
        'spec_name': 'SpecName'
    }

    def __init__(self, cpu_num=None, max_conn=None, mem_in_gb=None, spec_name=None, _configuration=None):  # noqa: E501
        """MongosNodeSpecForDescribeNodeSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu_num = None
        self._max_conn = None
        self._mem_in_gb = None
        self._spec_name = None
        self.discriminator = None

        if cpu_num is not None:
            self.cpu_num = cpu_num
        if max_conn is not None:
            self.max_conn = max_conn
        if mem_in_gb is not None:
            self.mem_in_gb = mem_in_gb
        if spec_name is not None:
            self.spec_name = spec_name

    @property
    def cpu_num(self):
        """Gets the cpu_num of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501


        :return: The cpu_num of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu_num

    @cpu_num.setter
    def cpu_num(self, cpu_num):
        """Sets the cpu_num of this MongosNodeSpecForDescribeNodeSpecsOutput.


        :param cpu_num: The cpu_num of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :type: float
        """

        self._cpu_num = cpu_num

    @property
    def max_conn(self):
        """Gets the max_conn of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501


        :return: The max_conn of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_conn

    @max_conn.setter
    def max_conn(self, max_conn):
        """Sets the max_conn of this MongosNodeSpecForDescribeNodeSpecsOutput.


        :param max_conn: The max_conn of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :type: int
        """

        self._max_conn = max_conn

    @property
    def mem_in_gb(self):
        """Gets the mem_in_gb of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501


        :return: The mem_in_gb of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :rtype: float
        """
        return self._mem_in_gb

    @mem_in_gb.setter
    def mem_in_gb(self, mem_in_gb):
        """Sets the mem_in_gb of this MongosNodeSpecForDescribeNodeSpecsOutput.


        :param mem_in_gb: The mem_in_gb of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :type: float
        """

        self._mem_in_gb = mem_in_gb

    @property
    def spec_name(self):
        """Gets the spec_name of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501


        :return: The spec_name of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_name

    @spec_name.setter
    def spec_name(self, spec_name):
        """Sets the spec_name of this MongosNodeSpecForDescribeNodeSpecsOutput.


        :param spec_name: The spec_name of this MongosNodeSpecForDescribeNodeSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_name = spec_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MongosNodeSpecForDescribeNodeSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MongosNodeSpecForDescribeNodeSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MongosNodeSpecForDescribeNodeSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
