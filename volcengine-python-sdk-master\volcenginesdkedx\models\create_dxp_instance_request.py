# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDXPInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ap': 'str',
        'area': 'str',
        'city': 'str',
        'isp': 'str',
        'instance_name': 'str',
        'module_type': 'str',
        'port_type': 'str',
        'switch_id': 'str',
        'switch_interface_id': 'str'
    }

    attribute_map = {
        'ap': 'AP',
        'area': 'Area',
        'city': 'City',
        'isp': 'ISP',
        'instance_name': 'InstanceName',
        'module_type': 'ModuleType',
        'port_type': 'PortType',
        'switch_id': 'SwitchId',
        'switch_interface_id': 'SwitchInterfaceId'
    }

    def __init__(self, ap=None, area=None, city=None, isp=None, instance_name=None, module_type=None, port_type=None, switch_id=None, switch_interface_id=None, _configuration=None):  # noqa: E501
        """CreateDXPInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ap = None
        self._area = None
        self._city = None
        self._isp = None
        self._instance_name = None
        self._module_type = None
        self._port_type = None
        self._switch_id = None
        self._switch_interface_id = None
        self.discriminator = None

        self.ap = ap
        self.area = area
        if city is not None:
            self.city = city
        self.isp = isp
        self.instance_name = instance_name
        self.module_type = module_type
        self.port_type = port_type
        if switch_id is not None:
            self.switch_id = switch_id
        if switch_interface_id is not None:
            self.switch_interface_id = switch_interface_id

    @property
    def ap(self):
        """Gets the ap of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The ap of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._ap

    @ap.setter
    def ap(self, ap):
        """Sets the ap of this CreateDXPInstanceRequest.


        :param ap: The ap of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ap is None:
            raise ValueError("Invalid value for `ap`, must not be `None`")  # noqa: E501

        self._ap = ap

    @property
    def area(self):
        """Gets the area of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The area of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this CreateDXPInstanceRequest.


        :param area: The area of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and area is None:
            raise ValueError("Invalid value for `area`, must not be `None`")  # noqa: E501

        self._area = area

    @property
    def city(self):
        """Gets the city of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The city of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this CreateDXPInstanceRequest.


        :param city: The city of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def isp(self):
        """Gets the isp of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The isp of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this CreateDXPInstanceRequest.


        :param isp: The isp of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and isp is None:
            raise ValueError("Invalid value for `isp`, must not be `None`")  # noqa: E501

        self._isp = isp

    @property
    def instance_name(self):
        """Gets the instance_name of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The instance_name of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this CreateDXPInstanceRequest.


        :param instance_name: The instance_name of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_name is None:
            raise ValueError("Invalid value for `instance_name`, must not be `None`")  # noqa: E501

        self._instance_name = instance_name

    @property
    def module_type(self):
        """Gets the module_type of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The module_type of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._module_type

    @module_type.setter
    def module_type(self, module_type):
        """Sets the module_type of this CreateDXPInstanceRequest.


        :param module_type: The module_type of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and module_type is None:
            raise ValueError("Invalid value for `module_type`, must not be `None`")  # noqa: E501

        self._module_type = module_type

    @property
    def port_type(self):
        """Gets the port_type of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The port_type of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._port_type

    @port_type.setter
    def port_type(self, port_type):
        """Sets the port_type of this CreateDXPInstanceRequest.


        :param port_type: The port_type of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and port_type is None:
            raise ValueError("Invalid value for `port_type`, must not be `None`")  # noqa: E501

        self._port_type = port_type

    @property
    def switch_id(self):
        """Gets the switch_id of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The switch_id of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_id

    @switch_id.setter
    def switch_id(self, switch_id):
        """Sets the switch_id of this CreateDXPInstanceRequest.


        :param switch_id: The switch_id of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """

        self._switch_id = switch_id

    @property
    def switch_interface_id(self):
        """Gets the switch_interface_id of this CreateDXPInstanceRequest.  # noqa: E501


        :return: The switch_interface_id of this CreateDXPInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_interface_id

    @switch_interface_id.setter
    def switch_interface_id(self, switch_interface_id):
        """Sets the switch_interface_id of this CreateDXPInstanceRequest.


        :param switch_interface_id: The switch_interface_id of this CreateDXPInstanceRequest.  # noqa: E501
        :type: str
        """

        self._switch_interface_id = switch_interface_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDXPInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDXPInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDXPInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
