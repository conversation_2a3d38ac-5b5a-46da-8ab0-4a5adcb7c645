# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePermissionRulesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'permission_rules': 'list[PermissionRuleForDescribePermissionRulesOutput]',
        'total_count': 'int'
    }

    attribute_map = {
        'permission_rules': 'PermissionRules',
        'total_count': 'TotalCount'
    }

    def __init__(self, permission_rules=None, total_count=None, _configuration=None):  # noqa: E501
        """DescribePermissionRulesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._permission_rules = None
        self._total_count = None
        self.discriminator = None

        if permission_rules is not None:
            self.permission_rules = permission_rules
        if total_count is not None:
            self.total_count = total_count

    @property
    def permission_rules(self):
        """Gets the permission_rules of this DescribePermissionRulesResponse.  # noqa: E501


        :return: The permission_rules of this DescribePermissionRulesResponse.  # noqa: E501
        :rtype: list[PermissionRuleForDescribePermissionRulesOutput]
        """
        return self._permission_rules

    @permission_rules.setter
    def permission_rules(self, permission_rules):
        """Sets the permission_rules of this DescribePermissionRulesResponse.


        :param permission_rules: The permission_rules of this DescribePermissionRulesResponse.  # noqa: E501
        :type: list[PermissionRuleForDescribePermissionRulesOutput]
        """

        self._permission_rules = permission_rules

    @property
    def total_count(self):
        """Gets the total_count of this DescribePermissionRulesResponse.  # noqa: E501


        :return: The total_count of this DescribePermissionRulesResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this DescribePermissionRulesResponse.


        :param total_count: The total_count of this DescribePermissionRulesResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePermissionRulesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePermissionRulesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePermissionRulesResponse):
            return True

        return self.to_dict() != other.to_dict()
