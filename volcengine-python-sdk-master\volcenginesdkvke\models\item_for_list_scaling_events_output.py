# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListScalingEventsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'str',
        'id': 'str',
        'message': 'str',
        'node_ids': 'list[str]',
        'node_pool_id': 'str',
        'replicas': 'int',
        'start_time': 'str',
        'status': 'str',
        'type': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'id': 'Id',
        'message': 'Message',
        'node_ids': 'NodeIds',
        'node_pool_id': 'NodePoolId',
        'replicas': 'Replicas',
        'start_time': 'StartTime',
        'status': 'Status',
        'type': 'Type'
    }

    def __init__(self, end_time=None, id=None, message=None, node_ids=None, node_pool_id=None, replicas=None, start_time=None, status=None, type=None, _configuration=None):  # noqa: E501
        """ItemForListScalingEventsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._id = None
        self._message = None
        self._node_ids = None
        self._node_pool_id = None
        self._replicas = None
        self._start_time = None
        self._status = None
        self._type = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if id is not None:
            self.id = id
        if message is not None:
            self.message = message
        if node_ids is not None:
            self.node_ids = node_ids
        if node_pool_id is not None:
            self.node_pool_id = node_pool_id
        if replicas is not None:
            self.replicas = replicas
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type

    @property
    def end_time(self):
        """Gets the end_time of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The end_time of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ItemForListScalingEventsOutput.


        :param end_time: The end_time of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def id(self):
        """Gets the id of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The id of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListScalingEventsOutput.


        :param id: The id of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def message(self):
        """Gets the message of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The message of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListScalingEventsOutput.


        :param message: The message of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def node_ids(self):
        """Gets the node_ids of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The node_ids of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_ids

    @node_ids.setter
    def node_ids(self, node_ids):
        """Sets the node_ids of this ItemForListScalingEventsOutput.


        :param node_ids: The node_ids of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: list[str]
        """

        self._node_ids = node_ids

    @property
    def node_pool_id(self):
        """Gets the node_pool_id of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The node_pool_id of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_pool_id

    @node_pool_id.setter
    def node_pool_id(self, node_pool_id):
        """Sets the node_pool_id of this ItemForListScalingEventsOutput.


        :param node_pool_id: The node_pool_id of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._node_pool_id = node_pool_id

    @property
    def replicas(self):
        """Gets the replicas of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The replicas of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this ItemForListScalingEventsOutput.


        :param replicas: The replicas of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The start_time of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListScalingEventsOutput.


        :param start_time: The start_time of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The status of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListScalingEventsOutput.


        :param status: The status of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this ItemForListScalingEventsOutput.  # noqa: E501


        :return: The type of this ItemForListScalingEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListScalingEventsOutput.


        :param type: The type of this ItemForListScalingEventsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListScalingEventsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListScalingEventsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListScalingEventsOutput):
            return True

        return self.to_dict() != other.to_dict()
