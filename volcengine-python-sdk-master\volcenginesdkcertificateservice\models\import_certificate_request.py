# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImportCertificateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_info': 'CertificateInfoForImportCertificateInput',
        'gm_certificate_info': 'GmCertificateInfoForImportCertificateInput',
        'no_verify_and_fix_chain': 'bool',
        'project_name': 'str',
        'repeatable': 'bool',
        'tag': 'str',
        'tags': 'list[TagForImportCertificateInput]'
    }

    attribute_map = {
        'certificate_info': 'CertificateInfo',
        'gm_certificate_info': 'GmCertificateInfo',
        'no_verify_and_fix_chain': 'NoVerifyAndFixChain',
        'project_name': 'ProjectName',
        'repeatable': 'Repeatable',
        'tag': 'Tag',
        'tags': 'Tags'
    }

    def __init__(self, certificate_info=None, gm_certificate_info=None, no_verify_and_fix_chain=None, project_name=None, repeatable=None, tag=None, tags=None, _configuration=None):  # noqa: E501
        """ImportCertificateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_info = None
        self._gm_certificate_info = None
        self._no_verify_and_fix_chain = None
        self._project_name = None
        self._repeatable = None
        self._tag = None
        self._tags = None
        self.discriminator = None

        if certificate_info is not None:
            self.certificate_info = certificate_info
        if gm_certificate_info is not None:
            self.gm_certificate_info = gm_certificate_info
        if no_verify_and_fix_chain is not None:
            self.no_verify_and_fix_chain = no_verify_and_fix_chain
        if project_name is not None:
            self.project_name = project_name
        if repeatable is not None:
            self.repeatable = repeatable
        if tag is not None:
            self.tag = tag
        if tags is not None:
            self.tags = tags

    @property
    def certificate_info(self):
        """Gets the certificate_info of this ImportCertificateRequest.  # noqa: E501


        :return: The certificate_info of this ImportCertificateRequest.  # noqa: E501
        :rtype: CertificateInfoForImportCertificateInput
        """
        return self._certificate_info

    @certificate_info.setter
    def certificate_info(self, certificate_info):
        """Sets the certificate_info of this ImportCertificateRequest.


        :param certificate_info: The certificate_info of this ImportCertificateRequest.  # noqa: E501
        :type: CertificateInfoForImportCertificateInput
        """

        self._certificate_info = certificate_info

    @property
    def gm_certificate_info(self):
        """Gets the gm_certificate_info of this ImportCertificateRequest.  # noqa: E501


        :return: The gm_certificate_info of this ImportCertificateRequest.  # noqa: E501
        :rtype: GmCertificateInfoForImportCertificateInput
        """
        return self._gm_certificate_info

    @gm_certificate_info.setter
    def gm_certificate_info(self, gm_certificate_info):
        """Sets the gm_certificate_info of this ImportCertificateRequest.


        :param gm_certificate_info: The gm_certificate_info of this ImportCertificateRequest.  # noqa: E501
        :type: GmCertificateInfoForImportCertificateInput
        """

        self._gm_certificate_info = gm_certificate_info

    @property
    def no_verify_and_fix_chain(self):
        """Gets the no_verify_and_fix_chain of this ImportCertificateRequest.  # noqa: E501


        :return: The no_verify_and_fix_chain of this ImportCertificateRequest.  # noqa: E501
        :rtype: bool
        """
        return self._no_verify_and_fix_chain

    @no_verify_and_fix_chain.setter
    def no_verify_and_fix_chain(self, no_verify_and_fix_chain):
        """Sets the no_verify_and_fix_chain of this ImportCertificateRequest.


        :param no_verify_and_fix_chain: The no_verify_and_fix_chain of this ImportCertificateRequest.  # noqa: E501
        :type: bool
        """

        self._no_verify_and_fix_chain = no_verify_and_fix_chain

    @property
    def project_name(self):
        """Gets the project_name of this ImportCertificateRequest.  # noqa: E501


        :return: The project_name of this ImportCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ImportCertificateRequest.


        :param project_name: The project_name of this ImportCertificateRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def repeatable(self):
        """Gets the repeatable of this ImportCertificateRequest.  # noqa: E501


        :return: The repeatable of this ImportCertificateRequest.  # noqa: E501
        :rtype: bool
        """
        return self._repeatable

    @repeatable.setter
    def repeatable(self, repeatable):
        """Sets the repeatable of this ImportCertificateRequest.


        :param repeatable: The repeatable of this ImportCertificateRequest.  # noqa: E501
        :type: bool
        """

        self._repeatable = repeatable

    @property
    def tag(self):
        """Gets the tag of this ImportCertificateRequest.  # noqa: E501


        :return: The tag of this ImportCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ImportCertificateRequest.


        :param tag: The tag of this ImportCertificateRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tags(self):
        """Gets the tags of this ImportCertificateRequest.  # noqa: E501


        :return: The tags of this ImportCertificateRequest.  # noqa: E501
        :rtype: list[TagForImportCertificateInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ImportCertificateRequest.


        :param tags: The tags of this ImportCertificateRequest.  # noqa: E501
        :type: list[TagForImportCertificateInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImportCertificateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImportCertificateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImportCertificateRequest):
            return True

        return self.to_dict() != other.to_dict()
