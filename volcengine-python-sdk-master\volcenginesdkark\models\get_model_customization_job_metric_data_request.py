# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetModelCustomizationJobMetricDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'from_step': 'int',
        'max_metric_point': 'str',
        'metrics': 'list[str]',
        'min_sample_step': 'str',
        'model_customization_job_id': 'str',
        'to_step': 'int'
    }

    attribute_map = {
        'from_step': 'FromStep',
        'max_metric_point': 'MaxMetricPoint',
        'metrics': 'Metrics',
        'min_sample_step': 'MinSampleStep',
        'model_customization_job_id': 'ModelCustomizationJobId',
        'to_step': 'ToStep'
    }

    def __init__(self, from_step=None, max_metric_point=None, metrics=None, min_sample_step=None, model_customization_job_id=None, to_step=None, _configuration=None):  # noqa: E501
        """GetModelCustomizationJobMetricDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._from_step = None
        self._max_metric_point = None
        self._metrics = None
        self._min_sample_step = None
        self._model_customization_job_id = None
        self._to_step = None
        self.discriminator = None

        self.from_step = from_step
        if max_metric_point is not None:
            self.max_metric_point = max_metric_point
        if metrics is not None:
            self.metrics = metrics
        if min_sample_step is not None:
            self.min_sample_step = min_sample_step
        self.model_customization_job_id = model_customization_job_id
        self.to_step = to_step

    @property
    def from_step(self):
        """Gets the from_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The from_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._from_step

    @from_step.setter
    def from_step(self, from_step):
        """Sets the from_step of this GetModelCustomizationJobMetricDataRequest.


        :param from_step: The from_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and from_step is None:
            raise ValueError("Invalid value for `from_step`, must not be `None`")  # noqa: E501

        self._from_step = from_step

    @property
    def max_metric_point(self):
        """Gets the max_metric_point of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The max_metric_point of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_metric_point

    @max_metric_point.setter
    def max_metric_point(self, max_metric_point):
        """Sets the max_metric_point of this GetModelCustomizationJobMetricDataRequest.


        :param max_metric_point: The max_metric_point of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: str
        """

        self._max_metric_point = max_metric_point

    @property
    def metrics(self):
        """Gets the metrics of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The metrics of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._metrics

    @metrics.setter
    def metrics(self, metrics):
        """Sets the metrics of this GetModelCustomizationJobMetricDataRequest.


        :param metrics: The metrics of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._metrics = metrics

    @property
    def min_sample_step(self):
        """Gets the min_sample_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The min_sample_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._min_sample_step

    @min_sample_step.setter
    def min_sample_step(self, min_sample_step):
        """Sets the min_sample_step of this GetModelCustomizationJobMetricDataRequest.


        :param min_sample_step: The min_sample_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: str
        """

        self._min_sample_step = min_sample_step

    @property
    def model_customization_job_id(self):
        """Gets the model_customization_job_id of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The model_customization_job_id of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._model_customization_job_id

    @model_customization_job_id.setter
    def model_customization_job_id(self, model_customization_job_id):
        """Sets the model_customization_job_id of this GetModelCustomizationJobMetricDataRequest.


        :param model_customization_job_id: The model_customization_job_id of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and model_customization_job_id is None:
            raise ValueError("Invalid value for `model_customization_job_id`, must not be `None`")  # noqa: E501

        self._model_customization_job_id = model_customization_job_id

    @property
    def to_step(self):
        """Gets the to_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501


        :return: The to_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._to_step

    @to_step.setter
    def to_step(self, to_step):
        """Sets the to_step of this GetModelCustomizationJobMetricDataRequest.


        :param to_step: The to_step of this GetModelCustomizationJobMetricDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and to_step is None:
            raise ValueError("Invalid value for `to_step`, must not be `None`")  # noqa: E501

        self._to_step = to_step

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetModelCustomizationJobMetricDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetModelCustomizationJobMetricDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetModelCustomizationJobMetricDataRequest):
            return True

        return self.to_dict() != other.to_dict()
