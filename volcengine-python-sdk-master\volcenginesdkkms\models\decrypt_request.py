# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DecryptRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ciphertext_blob': 'str',
        'encryption_context': 'dict(str, str)'
    }

    attribute_map = {
        'ciphertext_blob': 'CiphertextBlob',
        'encryption_context': 'EncryptionContext'
    }

    def __init__(self, ciphertext_blob=None, encryption_context=None, _configuration=None):  # noqa: E501
        """DecryptRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ciphertext_blob = None
        self._encryption_context = None
        self.discriminator = None

        self.ciphertext_blob = ciphertext_blob
        if encryption_context is not None:
            self.encryption_context = encryption_context

    @property
    def ciphertext_blob(self):
        """Gets the ciphertext_blob of this DecryptRequest.  # noqa: E501


        :return: The ciphertext_blob of this DecryptRequest.  # noqa: E501
        :rtype: str
        """
        return self._ciphertext_blob

    @ciphertext_blob.setter
    def ciphertext_blob(self, ciphertext_blob):
        """Sets the ciphertext_blob of this DecryptRequest.


        :param ciphertext_blob: The ciphertext_blob of this DecryptRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ciphertext_blob is None:
            raise ValueError("Invalid value for `ciphertext_blob`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                ciphertext_blob is not None and len(ciphertext_blob) < 19):
            raise ValueError("Invalid value for `ciphertext_blob`, length must be greater than or equal to `19`")  # noqa: E501

        self._ciphertext_blob = ciphertext_blob

    @property
    def encryption_context(self):
        """Gets the encryption_context of this DecryptRequest.  # noqa: E501


        :return: The encryption_context of this DecryptRequest.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._encryption_context

    @encryption_context.setter
    def encryption_context(self, encryption_context):
        """Sets the encryption_context of this DecryptRequest.


        :param encryption_context: The encryption_context of this DecryptRequest.  # noqa: E501
        :type: dict(str, str)
        """

        self._encryption_context = encryption_context

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DecryptRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DecryptRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DecryptRequest):
            return True

        return self.to_dict() != other.to_dict()
