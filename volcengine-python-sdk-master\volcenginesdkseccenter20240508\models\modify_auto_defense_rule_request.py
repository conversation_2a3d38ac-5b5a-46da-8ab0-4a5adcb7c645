# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyAutoDefenseRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'conditions': 'ConditionsForModifyAutoDefenseRuleInput',
        'enable': 'bool',
        'flag': 'bool',
        'if_all_host': 'bool',
        'leaf_group_ids': 'list[str]',
        'rule_id': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'conditions': 'Conditions',
        'enable': 'Enable',
        'flag': 'Flag',
        'if_all_host': 'IfAllHost',
        'leaf_group_ids': 'LeafGroupIDs',
        'rule_id': 'RuleID',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id_list=None, conditions=None, enable=None, flag=None, if_all_host=None, leaf_group_ids=None, rule_id=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ModifyAutoDefenseRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._conditions = None
        self._enable = None
        self._flag = None
        self._if_all_host = None
        self._leaf_group_ids = None
        self._rule_id = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if conditions is not None:
            self.conditions = conditions
        if enable is not None:
            self.enable = enable
        if flag is not None:
            self.flag = flag
        if if_all_host is not None:
            self.if_all_host = if_all_host
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.rule_id = rule_id
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The agent_id_list of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this ModifyAutoDefenseRuleRequest.


        :param agent_id_list: The agent_id_list of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def conditions(self):
        """Gets the conditions of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The conditions of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: ConditionsForModifyAutoDefenseRuleInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this ModifyAutoDefenseRuleRequest.


        :param conditions: The conditions of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: ConditionsForModifyAutoDefenseRuleInput
        """

        self._conditions = conditions

    @property
    def enable(self):
        """Gets the enable of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The enable of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this ModifyAutoDefenseRuleRequest.


        :param enable: The enable of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def flag(self):
        """Gets the flag of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The flag of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: bool
        """
        return self._flag

    @flag.setter
    def flag(self, flag):
        """Sets the flag of this ModifyAutoDefenseRuleRequest.


        :param flag: The flag of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: bool
        """

        self._flag = flag

    @property
    def if_all_host(self):
        """Gets the if_all_host of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The if_all_host of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_all_host

    @if_all_host.setter
    def if_all_host(self, if_all_host):
        """Sets the if_all_host of this ModifyAutoDefenseRuleRequest.


        :param if_all_host: The if_all_host of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: bool
        """

        self._if_all_host = if_all_host

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The leaf_group_ids of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ModifyAutoDefenseRuleRequest.


        :param leaf_group_ids: The leaf_group_ids of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def rule_id(self):
        """Gets the rule_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The rule_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this ModifyAutoDefenseRuleRequest.


        :param rule_id: The rule_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501


        :return: The top_group_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ModifyAutoDefenseRuleRequest.


        :param top_group_id: The top_group_id of this ModifyAutoDefenseRuleRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyAutoDefenseRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyAutoDefenseRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyAutoDefenseRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
