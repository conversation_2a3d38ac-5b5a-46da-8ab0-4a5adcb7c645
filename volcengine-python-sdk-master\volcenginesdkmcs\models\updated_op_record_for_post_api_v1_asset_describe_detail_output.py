# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'op_detail': 'str',
        'op_time_milli': 'int'
    }

    attribute_map = {
        'op_detail': 'op_detail',
        'op_time_milli': 'op_time_milli'
    }

    def __init__(self, op_detail=None, op_time_milli=None, _configuration=None):  # noqa: E501
        """UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._op_detail = None
        self._op_time_milli = None
        self.discriminator = None

        if op_detail is not None:
            self.op_detail = op_detail
        if op_time_milli is not None:
            self.op_time_milli = op_time_milli

    @property
    def op_detail(self):
        """Gets the op_detail of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501


        :return: The op_detail of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._op_detail

    @op_detail.setter
    def op_detail(self, op_detail):
        """Sets the op_detail of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.


        :param op_detail: The op_detail of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501
        :type: str
        """

        self._op_detail = op_detail

    @property
    def op_time_milli(self):
        """Gets the op_time_milli of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501


        :return: The op_time_milli of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._op_time_milli

    @op_time_milli.setter
    def op_time_milli(self, op_time_milli):
        """Sets the op_time_milli of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.


        :param op_time_milli: The op_time_milli of this UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput.  # noqa: E501
        :type: int
        """

        self._op_time_milli = op_time_milli

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
