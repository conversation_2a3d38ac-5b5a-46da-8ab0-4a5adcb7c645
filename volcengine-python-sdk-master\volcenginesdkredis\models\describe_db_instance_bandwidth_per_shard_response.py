# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceBandwidthPerShardResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_bandwidth_per_shard': 'int',
        'default_bandwidth_per_shard': 'int'
    }

    attribute_map = {
        'additional_bandwidth_per_shard': 'AdditionalBandwidthPerShard',
        'default_bandwidth_per_shard': 'DefaultBandwidthPerShard'
    }

    def __init__(self, additional_bandwidth_per_shard=None, default_bandwidth_per_shard=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceBandwidthPerShardResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_bandwidth_per_shard = None
        self._default_bandwidth_per_shard = None
        self.discriminator = None

        if additional_bandwidth_per_shard is not None:
            self.additional_bandwidth_per_shard = additional_bandwidth_per_shard
        if default_bandwidth_per_shard is not None:
            self.default_bandwidth_per_shard = default_bandwidth_per_shard

    @property
    def additional_bandwidth_per_shard(self):
        """Gets the additional_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501


        :return: The additional_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501
        :rtype: int
        """
        return self._additional_bandwidth_per_shard

    @additional_bandwidth_per_shard.setter
    def additional_bandwidth_per_shard(self, additional_bandwidth_per_shard):
        """Sets the additional_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.


        :param additional_bandwidth_per_shard: The additional_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501
        :type: int
        """

        self._additional_bandwidth_per_shard = additional_bandwidth_per_shard

    @property
    def default_bandwidth_per_shard(self):
        """Gets the default_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501


        :return: The default_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501
        :rtype: int
        """
        return self._default_bandwidth_per_shard

    @default_bandwidth_per_shard.setter
    def default_bandwidth_per_shard(self, default_bandwidth_per_shard):
        """Sets the default_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.


        :param default_bandwidth_per_shard: The default_bandwidth_per_shard of this DescribeDBInstanceBandwidthPerShardResponse.  # noqa: E501
        :type: int
        """

        self._default_bandwidth_per_shard = default_bandwidth_per_shard

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceBandwidthPerShardResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceBandwidthPerShardResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceBandwidthPerShardResponse):
            return True

        return self.to_dict() != other.to_dict()
