# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebRespCodeResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'distribution': 'list[DistributionForDescWebRespCodeOutput]',
        'trend': 'list[TrendForDescWebRespCodeOutput]'
    }

    attribute_map = {
        'distribution': 'Distribution',
        'trend': 'Trend'
    }

    def __init__(self, distribution=None, trend=None, _configuration=None):  # noqa: E501
        """DescWebRespCodeResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._distribution = None
        self._trend = None
        self.discriminator = None

        if distribution is not None:
            self.distribution = distribution
        if trend is not None:
            self.trend = trend

    @property
    def distribution(self):
        """Gets the distribution of this DescWebRespCodeResponse.  # noqa: E501


        :return: The distribution of this DescWebRespCodeResponse.  # noqa: E501
        :rtype: list[DistributionForDescWebRespCodeOutput]
        """
        return self._distribution

    @distribution.setter
    def distribution(self, distribution):
        """Sets the distribution of this DescWebRespCodeResponse.


        :param distribution: The distribution of this DescWebRespCodeResponse.  # noqa: E501
        :type: list[DistributionForDescWebRespCodeOutput]
        """

        self._distribution = distribution

    @property
    def trend(self):
        """Gets the trend of this DescWebRespCodeResponse.  # noqa: E501


        :return: The trend of this DescWebRespCodeResponse.  # noqa: E501
        :rtype: list[TrendForDescWebRespCodeOutput]
        """
        return self._trend

    @trend.setter
    def trend(self, trend):
        """Sets the trend of this DescWebRespCodeResponse.


        :param trend: The trend of this DescWebRespCodeResponse.  # noqa: E501
        :type: list[TrendForDescWebRespCodeOutput]
        """

        self._trend = trend

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebRespCodeResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebRespCodeResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebRespCodeResponse):
            return True

        return self.to_dict() != other.to_dict()
