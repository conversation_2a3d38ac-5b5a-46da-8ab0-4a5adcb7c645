# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeImageSharePermissionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accounts': 'list[AccountForDescribeImageSharePermissionOutput]',
        'image_id': 'str',
        'next_token': 'str',
        'total_count': 'int'
    }

    attribute_map = {
        'accounts': 'Accounts',
        'image_id': 'ImageId',
        'next_token': 'NextToken',
        'total_count': 'TotalCount'
    }

    def __init__(self, accounts=None, image_id=None, next_token=None, total_count=None, _configuration=None):  # noqa: E501
        """DescribeImageSharePermissionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accounts = None
        self._image_id = None
        self._next_token = None
        self._total_count = None
        self.discriminator = None

        if accounts is not None:
            self.accounts = accounts
        if image_id is not None:
            self.image_id = image_id
        if next_token is not None:
            self.next_token = next_token
        if total_count is not None:
            self.total_count = total_count

    @property
    def accounts(self):
        """Gets the accounts of this DescribeImageSharePermissionResponse.  # noqa: E501


        :return: The accounts of this DescribeImageSharePermissionResponse.  # noqa: E501
        :rtype: list[AccountForDescribeImageSharePermissionOutput]
        """
        return self._accounts

    @accounts.setter
    def accounts(self, accounts):
        """Sets the accounts of this DescribeImageSharePermissionResponse.


        :param accounts: The accounts of this DescribeImageSharePermissionResponse.  # noqa: E501
        :type: list[AccountForDescribeImageSharePermissionOutput]
        """

        self._accounts = accounts

    @property
    def image_id(self):
        """Gets the image_id of this DescribeImageSharePermissionResponse.  # noqa: E501


        :return: The image_id of this DescribeImageSharePermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this DescribeImageSharePermissionResponse.


        :param image_id: The image_id of this DescribeImageSharePermissionResponse.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def next_token(self):
        """Gets the next_token of this DescribeImageSharePermissionResponse.  # noqa: E501


        :return: The next_token of this DescribeImageSharePermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeImageSharePermissionResponse.


        :param next_token: The next_token of this DescribeImageSharePermissionResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def total_count(self):
        """Gets the total_count of this DescribeImageSharePermissionResponse.  # noqa: E501


        :return: The total_count of this DescribeImageSharePermissionResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this DescribeImageSharePermissionResponse.


        :param total_count: The total_count of this DescribeImageSharePermissionResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeImageSharePermissionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeImageSharePermissionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeImageSharePermissionResponse):
            return True

        return self.to_dict() != other.to_dict()
