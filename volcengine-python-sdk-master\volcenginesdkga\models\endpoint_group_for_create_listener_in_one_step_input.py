# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointGroupForCreateListenerInOneStepInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_configurations': 'list[EndpointConfigurationForCreateListenerInOneStepInput]',
        'endpoint_type': 'str',
        'healthy_config': 'HealthyConfigForCreateListenerInOneStepInput',
        'keep_client_ip': 'bool',
        'keep_client_ip_method': 'str',
        'name': 'str',
        'region': 'str',
        'source_ip': 'list[SourceIPForCreateListenerInOneStepInput]',
        'traffic_percentage': 'int'
    }

    attribute_map = {
        'endpoint_configurations': 'EndpointConfigurations',
        'endpoint_type': 'EndpointType',
        'healthy_config': 'HealthyConfig',
        'keep_client_ip': 'KeepClientIP',
        'keep_client_ip_method': 'KeepClientIPMethod',
        'name': 'Name',
        'region': 'Region',
        'source_ip': 'SourceIP',
        'traffic_percentage': 'TrafficPercentage'
    }

    def __init__(self, endpoint_configurations=None, endpoint_type=None, healthy_config=None, keep_client_ip=None, keep_client_ip_method=None, name=None, region=None, source_ip=None, traffic_percentage=None, _configuration=None):  # noqa: E501
        """EndpointGroupForCreateListenerInOneStepInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_configurations = None
        self._endpoint_type = None
        self._healthy_config = None
        self._keep_client_ip = None
        self._keep_client_ip_method = None
        self._name = None
        self._region = None
        self._source_ip = None
        self._traffic_percentage = None
        self.discriminator = None

        if endpoint_configurations is not None:
            self.endpoint_configurations = endpoint_configurations
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if healthy_config is not None:
            self.healthy_config = healthy_config
        if keep_client_ip is not None:
            self.keep_client_ip = keep_client_ip
        if keep_client_ip_method is not None:
            self.keep_client_ip_method = keep_client_ip_method
        if name is not None:
            self.name = name
        if region is not None:
            self.region = region
        if source_ip is not None:
            self.source_ip = source_ip
        if traffic_percentage is not None:
            self.traffic_percentage = traffic_percentage

    @property
    def endpoint_configurations(self):
        """Gets the endpoint_configurations of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The endpoint_configurations of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: list[EndpointConfigurationForCreateListenerInOneStepInput]
        """
        return self._endpoint_configurations

    @endpoint_configurations.setter
    def endpoint_configurations(self, endpoint_configurations):
        """Sets the endpoint_configurations of this EndpointGroupForCreateListenerInOneStepInput.


        :param endpoint_configurations: The endpoint_configurations of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: list[EndpointConfigurationForCreateListenerInOneStepInput]
        """

        self._endpoint_configurations = endpoint_configurations

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The endpoint_type of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this EndpointGroupForCreateListenerInOneStepInput.


        :param endpoint_type: The endpoint_type of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def healthy_config(self):
        """Gets the healthy_config of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The healthy_config of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: HealthyConfigForCreateListenerInOneStepInput
        """
        return self._healthy_config

    @healthy_config.setter
    def healthy_config(self, healthy_config):
        """Sets the healthy_config of this EndpointGroupForCreateListenerInOneStepInput.


        :param healthy_config: The healthy_config of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: HealthyConfigForCreateListenerInOneStepInput
        """

        self._healthy_config = healthy_config

    @property
    def keep_client_ip(self):
        """Gets the keep_client_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The keep_client_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: bool
        """
        return self._keep_client_ip

    @keep_client_ip.setter
    def keep_client_ip(self, keep_client_ip):
        """Sets the keep_client_ip of this EndpointGroupForCreateListenerInOneStepInput.


        :param keep_client_ip: The keep_client_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: bool
        """

        self._keep_client_ip = keep_client_ip

    @property
    def keep_client_ip_method(self):
        """Gets the keep_client_ip_method of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The keep_client_ip_method of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: str
        """
        return self._keep_client_ip_method

    @keep_client_ip_method.setter
    def keep_client_ip_method(self, keep_client_ip_method):
        """Sets the keep_client_ip_method of this EndpointGroupForCreateListenerInOneStepInput.


        :param keep_client_ip_method: The keep_client_ip_method of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: str
        """

        self._keep_client_ip_method = keep_client_ip_method

    @property
    def name(self):
        """Gets the name of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The name of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this EndpointGroupForCreateListenerInOneStepInput.


        :param name: The name of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def region(self):
        """Gets the region of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The region of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this EndpointGroupForCreateListenerInOneStepInput.


        :param region: The region of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def source_ip(self):
        """Gets the source_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The source_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: list[SourceIPForCreateListenerInOneStepInput]
        """
        return self._source_ip

    @source_ip.setter
    def source_ip(self, source_ip):
        """Sets the source_ip of this EndpointGroupForCreateListenerInOneStepInput.


        :param source_ip: The source_ip of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: list[SourceIPForCreateListenerInOneStepInput]
        """

        self._source_ip = source_ip

    @property
    def traffic_percentage(self):
        """Gets the traffic_percentage of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501


        :return: The traffic_percentage of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :rtype: int
        """
        return self._traffic_percentage

    @traffic_percentage.setter
    def traffic_percentage(self, traffic_percentage):
        """Sets the traffic_percentage of this EndpointGroupForCreateListenerInOneStepInput.


        :param traffic_percentage: The traffic_percentage of this EndpointGroupForCreateListenerInOneStepInput.  # noqa: E501
        :type: int
        """

        self._traffic_percentage = traffic_percentage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointGroupForCreateListenerInOneStepInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointGroupForCreateListenerInOneStepInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointGroupForCreateListenerInOneStepInput):
            return True

        return self.to_dict() != other.to_dict()
