# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRegistryNamespacesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'namespace': 'str',
        'namespace_create_time': 'int',
        'region': 'str',
        'registry_name': 'str',
        'registry_type': 'str',
        'total_image_count': 'int'
    }

    attribute_map = {
        'id': 'ID',
        'namespace': 'Namespace',
        'namespace_create_time': 'NamespaceCreateTime',
        'region': 'Region',
        'registry_name': 'RegistryName',
        'registry_type': 'RegistryType',
        'total_image_count': 'TotalImageCount'
    }

    def __init__(self, id=None, namespace=None, namespace_create_time=None, region=None, registry_name=None, registry_type=None, total_image_count=None, _configuration=None):  # noqa: E501
        """DataForListRegistryNamespacesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._namespace = None
        self._namespace_create_time = None
        self._region = None
        self._registry_name = None
        self._registry_type = None
        self._total_image_count = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if namespace is not None:
            self.namespace = namespace
        if namespace_create_time is not None:
            self.namespace_create_time = namespace_create_time
        if region is not None:
            self.region = region
        if registry_name is not None:
            self.registry_name = registry_name
        if registry_type is not None:
            self.registry_type = registry_type
        if total_image_count is not None:
            self.total_image_count = total_image_count

    @property
    def id(self):
        """Gets the id of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The id of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRegistryNamespacesOutput.


        :param id: The id of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def namespace(self):
        """Gets the namespace of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The namespace of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListRegistryNamespacesOutput.


        :param namespace: The namespace of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def namespace_create_time(self):
        """Gets the namespace_create_time of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The namespace_create_time of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: int
        """
        return self._namespace_create_time

    @namespace_create_time.setter
    def namespace_create_time(self, namespace_create_time):
        """Sets the namespace_create_time of this DataForListRegistryNamespacesOutput.


        :param namespace_create_time: The namespace_create_time of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: int
        """

        self._namespace_create_time = namespace_create_time

    @property
    def region(self):
        """Gets the region of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The region of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListRegistryNamespacesOutput.


        :param region: The region of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def registry_name(self):
        """Gets the registry_name of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The registry_name of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_name

    @registry_name.setter
    def registry_name(self, registry_name):
        """Sets the registry_name of this DataForListRegistryNamespacesOutput.


        :param registry_name: The registry_name of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._registry_name = registry_name

    @property
    def registry_type(self):
        """Gets the registry_type of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The registry_type of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_type

    @registry_type.setter
    def registry_type(self, registry_type):
        """Sets the registry_type of this DataForListRegistryNamespacesOutput.


        :param registry_type: The registry_type of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._registry_type = registry_type

    @property
    def total_image_count(self):
        """Gets the total_image_count of this DataForListRegistryNamespacesOutput.  # noqa: E501


        :return: The total_image_count of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_image_count

    @total_image_count.setter
    def total_image_count(self, total_image_count):
        """Sets the total_image_count of this DataForListRegistryNamespacesOutput.


        :param total_image_count: The total_image_count of this DataForListRegistryNamespacesOutput.  # noqa: E501
        :type: int
        """

        self._total_image_count = total_image_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRegistryNamespacesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRegistryNamespacesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRegistryNamespacesOutput):
            return True

        return self.to_dict() != other.to_dict()
