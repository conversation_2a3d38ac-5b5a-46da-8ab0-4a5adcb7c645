# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CloudVendorForGetApiV1OverviewSecurityScoresOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_num': 'int',
        'cloud_vendor': 'str',
        'risk_resource': 'int',
        'security_score': 'str'
    }

    attribute_map = {
        'account_num': 'account_num',
        'cloud_vendor': 'cloud_vendor',
        'risk_resource': 'risk_resource',
        'security_score': 'security_score'
    }

    def __init__(self, account_num=None, cloud_vendor=None, risk_resource=None, security_score=None, _configuration=None):  # noqa: E501
        """CloudVendorForGetApiV1OverviewSecurityScoresOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_num = None
        self._cloud_vendor = None
        self._risk_resource = None
        self._security_score = None
        self.discriminator = None

        if account_num is not None:
            self.account_num = account_num
        if cloud_vendor is not None:
            self.cloud_vendor = cloud_vendor
        if risk_resource is not None:
            self.risk_resource = risk_resource
        if security_score is not None:
            self.security_score = security_score

    @property
    def account_num(self):
        """Gets the account_num of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501


        :return: The account_num of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_num

    @account_num.setter
    def account_num(self, account_num):
        """Sets the account_num of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.


        :param account_num: The account_num of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :type: int
        """

        self._account_num = account_num

    @property
    def cloud_vendor(self):
        """Gets the cloud_vendor of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501


        :return: The cloud_vendor of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_vendor

    @cloud_vendor.setter
    def cloud_vendor(self, cloud_vendor):
        """Sets the cloud_vendor of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.


        :param cloud_vendor: The cloud_vendor of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :type: str
        """

        self._cloud_vendor = cloud_vendor

    @property
    def risk_resource(self):
        """Gets the risk_resource of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501


        :return: The risk_resource of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :rtype: int
        """
        return self._risk_resource

    @risk_resource.setter
    def risk_resource(self, risk_resource):
        """Sets the risk_resource of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.


        :param risk_resource: The risk_resource of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :type: int
        """

        self._risk_resource = risk_resource

    @property
    def security_score(self):
        """Gets the security_score of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501


        :return: The security_score of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_score

    @security_score.setter
    def security_score(self, security_score):
        """Sets the security_score of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.


        :param security_score: The security_score of this CloudVendorForGetApiV1OverviewSecurityScoresOutput.  # noqa: E501
        :type: str
        """

        self._security_score = security_score

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CloudVendorForGetApiV1OverviewSecurityScoresOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CloudVendorForGetApiV1OverviewSecurityScoresOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CloudVendorForGetApiV1OverviewSecurityScoresOutput):
            return True

        return self.to_dict() != other.to_dict()
