# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CfsForGetJobOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_name': 'str',
        'namespace_id': 'str',
        'options': 'OptionsForGetJobOutput',
        'tos': 'TosForGetJobOutput'
    }

    attribute_map = {
        'file_system_name': 'FileSystemName',
        'namespace_id': 'NamespaceId',
        'options': 'Options',
        'tos': 'Tos'
    }

    def __init__(self, file_system_name=None, namespace_id=None, options=None, tos=None, _configuration=None):  # noqa: E501
        """CfsForGetJobOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_name = None
        self._namespace_id = None
        self._options = None
        self._tos = None
        self.discriminator = None

        if file_system_name is not None:
            self.file_system_name = file_system_name
        if namespace_id is not None:
            self.namespace_id = namespace_id
        if options is not None:
            self.options = options
        if tos is not None:
            self.tos = tos

    @property
    def file_system_name(self):
        """Gets the file_system_name of this CfsForGetJobOutput.  # noqa: E501


        :return: The file_system_name of this CfsForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_name

    @file_system_name.setter
    def file_system_name(self, file_system_name):
        """Sets the file_system_name of this CfsForGetJobOutput.


        :param file_system_name: The file_system_name of this CfsForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._file_system_name = file_system_name

    @property
    def namespace_id(self):
        """Gets the namespace_id of this CfsForGetJobOutput.  # noqa: E501


        :return: The namespace_id of this CfsForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace_id

    @namespace_id.setter
    def namespace_id(self, namespace_id):
        """Sets the namespace_id of this CfsForGetJobOutput.


        :param namespace_id: The namespace_id of this CfsForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._namespace_id = namespace_id

    @property
    def options(self):
        """Gets the options of this CfsForGetJobOutput.  # noqa: E501


        :return: The options of this CfsForGetJobOutput.  # noqa: E501
        :rtype: OptionsForGetJobOutput
        """
        return self._options

    @options.setter
    def options(self, options):
        """Sets the options of this CfsForGetJobOutput.


        :param options: The options of this CfsForGetJobOutput.  # noqa: E501
        :type: OptionsForGetJobOutput
        """

        self._options = options

    @property
    def tos(self):
        """Gets the tos of this CfsForGetJobOutput.  # noqa: E501


        :return: The tos of this CfsForGetJobOutput.  # noqa: E501
        :rtype: TosForGetJobOutput
        """
        return self._tos

    @tos.setter
    def tos(self, tos):
        """Sets the tos of this CfsForGetJobOutput.


        :param tos: The tos of this CfsForGetJobOutput.  # noqa: E501
        :type: TosForGetJobOutput
        """

        self._tos = tos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CfsForGetJobOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CfsForGetJobOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CfsForGetJobOutput):
            return True

        return self.to_dict() != other.to_dict()
