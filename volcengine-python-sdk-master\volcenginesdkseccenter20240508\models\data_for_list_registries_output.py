# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRegistriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attributes': 'AttributesForListRegistriesOutput',
        'collect_time': 'int',
        'flow_rate_limit': 'int',
        'id': 'str',
        'name': 'str',
        'registry': 'str',
        'registry_create_time': 'int',
        'registry_type': 'str',
        'vpc_auth_status': 'int'
    }

    attribute_map = {
        'attributes': 'Attributes',
        'collect_time': 'CollectTime',
        'flow_rate_limit': 'FlowRateLimit',
        'id': 'ID',
        'name': 'Name',
        'registry': 'Registry',
        'registry_create_time': 'RegistryCreateTime',
        'registry_type': 'RegistryType',
        'vpc_auth_status': 'VpcAuthStatus'
    }

    def __init__(self, attributes=None, collect_time=None, flow_rate_limit=None, id=None, name=None, registry=None, registry_create_time=None, registry_type=None, vpc_auth_status=None, _configuration=None):  # noqa: E501
        """DataForListRegistriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attributes = None
        self._collect_time = None
        self._flow_rate_limit = None
        self._id = None
        self._name = None
        self._registry = None
        self._registry_create_time = None
        self._registry_type = None
        self._vpc_auth_status = None
        self.discriminator = None

        if attributes is not None:
            self.attributes = attributes
        if collect_time is not None:
            self.collect_time = collect_time
        if flow_rate_limit is not None:
            self.flow_rate_limit = flow_rate_limit
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if registry is not None:
            self.registry = registry
        if registry_create_time is not None:
            self.registry_create_time = registry_create_time
        if registry_type is not None:
            self.registry_type = registry_type
        if vpc_auth_status is not None:
            self.vpc_auth_status = vpc_auth_status

    @property
    def attributes(self):
        """Gets the attributes of this DataForListRegistriesOutput.  # noqa: E501


        :return: The attributes of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: AttributesForListRegistriesOutput
        """
        return self._attributes

    @attributes.setter
    def attributes(self, attributes):
        """Sets the attributes of this DataForListRegistriesOutput.


        :param attributes: The attributes of this DataForListRegistriesOutput.  # noqa: E501
        :type: AttributesForListRegistriesOutput
        """

        self._attributes = attributes

    @property
    def collect_time(self):
        """Gets the collect_time of this DataForListRegistriesOutput.  # noqa: E501


        :return: The collect_time of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._collect_time

    @collect_time.setter
    def collect_time(self, collect_time):
        """Sets the collect_time of this DataForListRegistriesOutput.


        :param collect_time: The collect_time of this DataForListRegistriesOutput.  # noqa: E501
        :type: int
        """

        self._collect_time = collect_time

    @property
    def flow_rate_limit(self):
        """Gets the flow_rate_limit of this DataForListRegistriesOutput.  # noqa: E501


        :return: The flow_rate_limit of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._flow_rate_limit

    @flow_rate_limit.setter
    def flow_rate_limit(self, flow_rate_limit):
        """Sets the flow_rate_limit of this DataForListRegistriesOutput.


        :param flow_rate_limit: The flow_rate_limit of this DataForListRegistriesOutput.  # noqa: E501
        :type: int
        """

        self._flow_rate_limit = flow_rate_limit

    @property
    def id(self):
        """Gets the id of this DataForListRegistriesOutput.  # noqa: E501


        :return: The id of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRegistriesOutput.


        :param id: The id of this DataForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForListRegistriesOutput.  # noqa: E501


        :return: The name of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListRegistriesOutput.


        :param name: The name of this DataForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def registry(self):
        """Gets the registry of this DataForListRegistriesOutput.  # noqa: E501


        :return: The registry of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry

    @registry.setter
    def registry(self, registry):
        """Sets the registry of this DataForListRegistriesOutput.


        :param registry: The registry of this DataForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._registry = registry

    @property
    def registry_create_time(self):
        """Gets the registry_create_time of this DataForListRegistriesOutput.  # noqa: E501


        :return: The registry_create_time of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._registry_create_time

    @registry_create_time.setter
    def registry_create_time(self, registry_create_time):
        """Sets the registry_create_time of this DataForListRegistriesOutput.


        :param registry_create_time: The registry_create_time of this DataForListRegistriesOutput.  # noqa: E501
        :type: int
        """

        self._registry_create_time = registry_create_time

    @property
    def registry_type(self):
        """Gets the registry_type of this DataForListRegistriesOutput.  # noqa: E501


        :return: The registry_type of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_type

    @registry_type.setter
    def registry_type(self, registry_type):
        """Sets the registry_type of this DataForListRegistriesOutput.


        :param registry_type: The registry_type of this DataForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._registry_type = registry_type

    @property
    def vpc_auth_status(self):
        """Gets the vpc_auth_status of this DataForListRegistriesOutput.  # noqa: E501


        :return: The vpc_auth_status of this DataForListRegistriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._vpc_auth_status

    @vpc_auth_status.setter
    def vpc_auth_status(self, vpc_auth_status):
        """Sets the vpc_auth_status of this DataForListRegistriesOutput.


        :param vpc_auth_status: The vpc_auth_status of this DataForListRegistriesOutput.  # noqa: E501
        :type: int
        """

        self._vpc_auth_status = vpc_auth_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRegistriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRegistriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRegistriesOutput):
            return True

        return self.to_dict() != other.to_dict()
