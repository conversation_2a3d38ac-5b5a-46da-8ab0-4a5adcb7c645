# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SpaceForListSpacesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_type': 'str',
        'callback_url': 'str',
        'created_at': 'str',
        'description': 'str',
        'domains': 'DomainsForListSpacesOutput',
        'gb': 'GBForListSpacesOutput',
        'hls_low_latency': 'bool',
        'region': 'str',
        'sip_server': 'SipServerForListSpacesOutput',
        'space_id': 'str',
        'space_name': 'str',
        'status': 'str',
        'tags': 'list[TagForListSpacesOutput]',
        'template': 'TemplateForListSpacesOutput',
        'updated_at': 'str'
    }

    attribute_map = {
        'access_type': 'AccessType',
        'callback_url': 'CallbackURL',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'domains': 'Domains',
        'gb': 'GB',
        'hls_low_latency': 'HLSLowLatency',
        'region': 'Region',
        'sip_server': 'SipServer',
        'space_id': 'SpaceID',
        'space_name': 'SpaceName',
        'status': 'Status',
        'tags': 'Tags',
        'template': 'Template',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, access_type=None, callback_url=None, created_at=None, description=None, domains=None, gb=None, hls_low_latency=None, region=None, sip_server=None, space_id=None, space_name=None, status=None, tags=None, template=None, updated_at=None, _configuration=None):  # noqa: E501
        """SpaceForListSpacesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_type = None
        self._callback_url = None
        self._created_at = None
        self._description = None
        self._domains = None
        self._gb = None
        self._hls_low_latency = None
        self._region = None
        self._sip_server = None
        self._space_id = None
        self._space_name = None
        self._status = None
        self._tags = None
        self._template = None
        self._updated_at = None
        self.discriminator = None

        if access_type is not None:
            self.access_type = access_type
        if callback_url is not None:
            self.callback_url = callback_url
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if domains is not None:
            self.domains = domains
        if gb is not None:
            self.gb = gb
        if hls_low_latency is not None:
            self.hls_low_latency = hls_low_latency
        if region is not None:
            self.region = region
        if sip_server is not None:
            self.sip_server = sip_server
        if space_id is not None:
            self.space_id = space_id
        if space_name is not None:
            self.space_name = space_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if template is not None:
            self.template = template
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def access_type(self):
        """Gets the access_type of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The access_type of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_type

    @access_type.setter
    def access_type(self, access_type):
        """Sets the access_type of this SpaceForListSpacesOutput.


        :param access_type: The access_type of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._access_type = access_type

    @property
    def callback_url(self):
        """Gets the callback_url of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The callback_url of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._callback_url

    @callback_url.setter
    def callback_url(self, callback_url):
        """Sets the callback_url of this SpaceForListSpacesOutput.


        :param callback_url: The callback_url of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._callback_url = callback_url

    @property
    def created_at(self):
        """Gets the created_at of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The created_at of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this SpaceForListSpacesOutput.


        :param created_at: The created_at of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The description of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SpaceForListSpacesOutput.


        :param description: The description of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def domains(self):
        """Gets the domains of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The domains of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: DomainsForListSpacesOutput
        """
        return self._domains

    @domains.setter
    def domains(self, domains):
        """Sets the domains of this SpaceForListSpacesOutput.


        :param domains: The domains of this SpaceForListSpacesOutput.  # noqa: E501
        :type: DomainsForListSpacesOutput
        """

        self._domains = domains

    @property
    def gb(self):
        """Gets the gb of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The gb of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: GBForListSpacesOutput
        """
        return self._gb

    @gb.setter
    def gb(self, gb):
        """Sets the gb of this SpaceForListSpacesOutput.


        :param gb: The gb of this SpaceForListSpacesOutput.  # noqa: E501
        :type: GBForListSpacesOutput
        """

        self._gb = gb

    @property
    def hls_low_latency(self):
        """Gets the hls_low_latency of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The hls_low_latency of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._hls_low_latency

    @hls_low_latency.setter
    def hls_low_latency(self, hls_low_latency):
        """Sets the hls_low_latency of this SpaceForListSpacesOutput.


        :param hls_low_latency: The hls_low_latency of this SpaceForListSpacesOutput.  # noqa: E501
        :type: bool
        """

        self._hls_low_latency = hls_low_latency

    @property
    def region(self):
        """Gets the region of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The region of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this SpaceForListSpacesOutput.


        :param region: The region of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def sip_server(self):
        """Gets the sip_server of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The sip_server of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: SipServerForListSpacesOutput
        """
        return self._sip_server

    @sip_server.setter
    def sip_server(self, sip_server):
        """Sets the sip_server of this SpaceForListSpacesOutput.


        :param sip_server: The sip_server of this SpaceForListSpacesOutput.  # noqa: E501
        :type: SipServerForListSpacesOutput
        """

        self._sip_server = sip_server

    @property
    def space_id(self):
        """Gets the space_id of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The space_id of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this SpaceForListSpacesOutput.


        :param space_id: The space_id of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def space_name(self):
        """Gets the space_name of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The space_name of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_name

    @space_name.setter
    def space_name(self, space_name):
        """Sets the space_name of this SpaceForListSpacesOutput.


        :param space_name: The space_name of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._space_name = space_name

    @property
    def status(self):
        """Gets the status of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The status of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SpaceForListSpacesOutput.


        :param status: The status of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The tags of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: list[TagForListSpacesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this SpaceForListSpacesOutput.


        :param tags: The tags of this SpaceForListSpacesOutput.  # noqa: E501
        :type: list[TagForListSpacesOutput]
        """

        self._tags = tags

    @property
    def template(self):
        """Gets the template of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The template of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: TemplateForListSpacesOutput
        """
        return self._template

    @template.setter
    def template(self, template):
        """Sets the template of this SpaceForListSpacesOutput.


        :param template: The template of this SpaceForListSpacesOutput.  # noqa: E501
        :type: TemplateForListSpacesOutput
        """

        self._template = template

    @property
    def updated_at(self):
        """Gets the updated_at of this SpaceForListSpacesOutput.  # noqa: E501


        :return: The updated_at of this SpaceForListSpacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this SpaceForListSpacesOutput.


        :param updated_at: The updated_at of this SpaceForListSpacesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SpaceForListSpacesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SpaceForListSpacesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SpaceForListSpacesOutput):
            return True

        return self.to_dict() != other.to_dict()
