# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListApplicationConfigsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'cluster_id': 'str',
        'config_file_name': 'str',
        'config_item_key': 'str'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'cluster_id': 'ClusterId',
        'config_file_name': 'ConfigFileName',
        'config_item_key': 'ConfigItemKey'
    }

    def __init__(self, application_name=None, cluster_id=None, config_file_name=None, config_item_key=None, _configuration=None):  # noqa: E501
        """ListApplicationConfigsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._cluster_id = None
        self._config_file_name = None
        self._config_item_key = None
        self.discriminator = None

        self.application_name = application_name
        self.cluster_id = cluster_id
        if config_file_name is not None:
            self.config_file_name = config_file_name
        if config_item_key is not None:
            self.config_item_key = config_item_key

    @property
    def application_name(self):
        """Gets the application_name of this ListApplicationConfigsRequest.  # noqa: E501


        :return: The application_name of this ListApplicationConfigsRequest.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ListApplicationConfigsRequest.


        :param application_name: The application_name of this ListApplicationConfigsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and application_name is None:
            raise ValueError("Invalid value for `application_name`, must not be `None`")  # noqa: E501

        self._application_name = application_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListApplicationConfigsRequest.  # noqa: E501


        :return: The cluster_id of this ListApplicationConfigsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListApplicationConfigsRequest.


        :param cluster_id: The cluster_id of this ListApplicationConfigsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def config_file_name(self):
        """Gets the config_file_name of this ListApplicationConfigsRequest.  # noqa: E501


        :return: The config_file_name of this ListApplicationConfigsRequest.  # noqa: E501
        :rtype: str
        """
        return self._config_file_name

    @config_file_name.setter
    def config_file_name(self, config_file_name):
        """Sets the config_file_name of this ListApplicationConfigsRequest.


        :param config_file_name: The config_file_name of this ListApplicationConfigsRequest.  # noqa: E501
        :type: str
        """

        self._config_file_name = config_file_name

    @property
    def config_item_key(self):
        """Gets the config_item_key of this ListApplicationConfigsRequest.  # noqa: E501


        :return: The config_item_key of this ListApplicationConfigsRequest.  # noqa: E501
        :rtype: str
        """
        return self._config_item_key

    @config_item_key.setter
    def config_item_key(self, config_item_key):
        """Sets the config_item_key of this ListApplicationConfigsRequest.


        :param config_item_key: The config_item_key of this ListApplicationConfigsRequest.  # noqa: E501
        :type: str
        """

        self._config_item_key = config_item_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListApplicationConfigsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListApplicationConfigsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListApplicationConfigsRequest):
            return True

        return self.to_dict() != other.to_dict()
