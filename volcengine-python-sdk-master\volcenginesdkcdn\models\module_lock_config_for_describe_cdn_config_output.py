# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModuleLockConfigForDescribeCdnConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'browser_cache_locked': 'bool',
        'cache_key_locked': 'bool',
        'cache_locked': 'bool',
        'compression_locked': 'bool',
        'customize_access_rule_locked': 'bool',
        'download_speed_limit_locked': 'bool',
        'error_page_locked': 'bool',
        'ip_access_rule_locked': 'bool',
        'negative_cache_locked': 'bool',
        'origin_access_rule_locked': 'bool',
        'origin_arg_locked': 'bool',
        'origin_locked': 'bool',
        'origin_response_header_locked': 'bool',
        'origin_rewrite_locked': 'bool',
        'quic_locked': 'bool',
        'redirection_rewrite_locked': 'bool',
        'referer_access_rule_locked': 'bool',
        'remote_auth_locked': 'bool',
        'request_block_rule_locked': 'bool',
        'request_header_locked': 'bool',
        'response_header_locked': 'bool',
        'rule_engine_locked': 'bool',
        'share_cache_locked': 'bool',
        'sign_url_auth_locked': 'bool',
        'ua_access_rule_locked': 'bool'
    }

    attribute_map = {
        'browser_cache_locked': 'BrowserCacheLocked',
        'cache_key_locked': 'CacheKeyLocked',
        'cache_locked': 'CacheLocked',
        'compression_locked': 'CompressionLocked',
        'customize_access_rule_locked': 'CustomizeAccessRuleLocked',
        'download_speed_limit_locked': 'DownloadSpeedLimitLocked',
        'error_page_locked': 'ErrorPageLocked',
        'ip_access_rule_locked': 'IpAccessRuleLocked',
        'negative_cache_locked': 'NegativeCacheLocked',
        'origin_access_rule_locked': 'OriginAccessRuleLocked',
        'origin_arg_locked': 'OriginArgLocked',
        'origin_locked': 'OriginLocked',
        'origin_response_header_locked': 'OriginResponseHeaderLocked',
        'origin_rewrite_locked': 'OriginRewriteLocked',
        'quic_locked': 'QuicLocked',
        'redirection_rewrite_locked': 'RedirectionRewriteLocked',
        'referer_access_rule_locked': 'RefererAccessRuleLocked',
        'remote_auth_locked': 'RemoteAuthLocked',
        'request_block_rule_locked': 'RequestBlockRuleLocked',
        'request_header_locked': 'RequestHeaderLocked',
        'response_header_locked': 'ResponseHeaderLocked',
        'rule_engine_locked': 'RuleEngineLocked',
        'share_cache_locked': 'ShareCacheLocked',
        'sign_url_auth_locked': 'SignUrlAuthLocked',
        'ua_access_rule_locked': 'UAAccessRuleLocked'
    }

    def __init__(self, browser_cache_locked=None, cache_key_locked=None, cache_locked=None, compression_locked=None, customize_access_rule_locked=None, download_speed_limit_locked=None, error_page_locked=None, ip_access_rule_locked=None, negative_cache_locked=None, origin_access_rule_locked=None, origin_arg_locked=None, origin_locked=None, origin_response_header_locked=None, origin_rewrite_locked=None, quic_locked=None, redirection_rewrite_locked=None, referer_access_rule_locked=None, remote_auth_locked=None, request_block_rule_locked=None, request_header_locked=None, response_header_locked=None, rule_engine_locked=None, share_cache_locked=None, sign_url_auth_locked=None, ua_access_rule_locked=None, _configuration=None):  # noqa: E501
        """ModuleLockConfigForDescribeCdnConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._browser_cache_locked = None
        self._cache_key_locked = None
        self._cache_locked = None
        self._compression_locked = None
        self._customize_access_rule_locked = None
        self._download_speed_limit_locked = None
        self._error_page_locked = None
        self._ip_access_rule_locked = None
        self._negative_cache_locked = None
        self._origin_access_rule_locked = None
        self._origin_arg_locked = None
        self._origin_locked = None
        self._origin_response_header_locked = None
        self._origin_rewrite_locked = None
        self._quic_locked = None
        self._redirection_rewrite_locked = None
        self._referer_access_rule_locked = None
        self._remote_auth_locked = None
        self._request_block_rule_locked = None
        self._request_header_locked = None
        self._response_header_locked = None
        self._rule_engine_locked = None
        self._share_cache_locked = None
        self._sign_url_auth_locked = None
        self._ua_access_rule_locked = None
        self.discriminator = None

        if browser_cache_locked is not None:
            self.browser_cache_locked = browser_cache_locked
        if cache_key_locked is not None:
            self.cache_key_locked = cache_key_locked
        if cache_locked is not None:
            self.cache_locked = cache_locked
        if compression_locked is not None:
            self.compression_locked = compression_locked
        if customize_access_rule_locked is not None:
            self.customize_access_rule_locked = customize_access_rule_locked
        if download_speed_limit_locked is not None:
            self.download_speed_limit_locked = download_speed_limit_locked
        if error_page_locked is not None:
            self.error_page_locked = error_page_locked
        if ip_access_rule_locked is not None:
            self.ip_access_rule_locked = ip_access_rule_locked
        if negative_cache_locked is not None:
            self.negative_cache_locked = negative_cache_locked
        if origin_access_rule_locked is not None:
            self.origin_access_rule_locked = origin_access_rule_locked
        if origin_arg_locked is not None:
            self.origin_arg_locked = origin_arg_locked
        if origin_locked is not None:
            self.origin_locked = origin_locked
        if origin_response_header_locked is not None:
            self.origin_response_header_locked = origin_response_header_locked
        if origin_rewrite_locked is not None:
            self.origin_rewrite_locked = origin_rewrite_locked
        if quic_locked is not None:
            self.quic_locked = quic_locked
        if redirection_rewrite_locked is not None:
            self.redirection_rewrite_locked = redirection_rewrite_locked
        if referer_access_rule_locked is not None:
            self.referer_access_rule_locked = referer_access_rule_locked
        if remote_auth_locked is not None:
            self.remote_auth_locked = remote_auth_locked
        if request_block_rule_locked is not None:
            self.request_block_rule_locked = request_block_rule_locked
        if request_header_locked is not None:
            self.request_header_locked = request_header_locked
        if response_header_locked is not None:
            self.response_header_locked = response_header_locked
        if rule_engine_locked is not None:
            self.rule_engine_locked = rule_engine_locked
        if share_cache_locked is not None:
            self.share_cache_locked = share_cache_locked
        if sign_url_auth_locked is not None:
            self.sign_url_auth_locked = sign_url_auth_locked
        if ua_access_rule_locked is not None:
            self.ua_access_rule_locked = ua_access_rule_locked

    @property
    def browser_cache_locked(self):
        """Gets the browser_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The browser_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._browser_cache_locked

    @browser_cache_locked.setter
    def browser_cache_locked(self, browser_cache_locked):
        """Sets the browser_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param browser_cache_locked: The browser_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._browser_cache_locked = browser_cache_locked

    @property
    def cache_key_locked(self):
        """Gets the cache_key_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The cache_key_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cache_key_locked

    @cache_key_locked.setter
    def cache_key_locked(self, cache_key_locked):
        """Sets the cache_key_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param cache_key_locked: The cache_key_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._cache_key_locked = cache_key_locked

    @property
    def cache_locked(self):
        """Gets the cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cache_locked

    @cache_locked.setter
    def cache_locked(self, cache_locked):
        """Sets the cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param cache_locked: The cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._cache_locked = cache_locked

    @property
    def compression_locked(self):
        """Gets the compression_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The compression_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._compression_locked

    @compression_locked.setter
    def compression_locked(self, compression_locked):
        """Sets the compression_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param compression_locked: The compression_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._compression_locked = compression_locked

    @property
    def customize_access_rule_locked(self):
        """Gets the customize_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The customize_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._customize_access_rule_locked

    @customize_access_rule_locked.setter
    def customize_access_rule_locked(self, customize_access_rule_locked):
        """Sets the customize_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param customize_access_rule_locked: The customize_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._customize_access_rule_locked = customize_access_rule_locked

    @property
    def download_speed_limit_locked(self):
        """Gets the download_speed_limit_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The download_speed_limit_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._download_speed_limit_locked

    @download_speed_limit_locked.setter
    def download_speed_limit_locked(self, download_speed_limit_locked):
        """Sets the download_speed_limit_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param download_speed_limit_locked: The download_speed_limit_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._download_speed_limit_locked = download_speed_limit_locked

    @property
    def error_page_locked(self):
        """Gets the error_page_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The error_page_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._error_page_locked

    @error_page_locked.setter
    def error_page_locked(self, error_page_locked):
        """Sets the error_page_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param error_page_locked: The error_page_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._error_page_locked = error_page_locked

    @property
    def ip_access_rule_locked(self):
        """Gets the ip_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The ip_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._ip_access_rule_locked

    @ip_access_rule_locked.setter
    def ip_access_rule_locked(self, ip_access_rule_locked):
        """Sets the ip_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param ip_access_rule_locked: The ip_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._ip_access_rule_locked = ip_access_rule_locked

    @property
    def negative_cache_locked(self):
        """Gets the negative_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The negative_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._negative_cache_locked

    @negative_cache_locked.setter
    def negative_cache_locked(self, negative_cache_locked):
        """Sets the negative_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param negative_cache_locked: The negative_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._negative_cache_locked = negative_cache_locked

    @property
    def origin_access_rule_locked(self):
        """Gets the origin_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The origin_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._origin_access_rule_locked

    @origin_access_rule_locked.setter
    def origin_access_rule_locked(self, origin_access_rule_locked):
        """Sets the origin_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param origin_access_rule_locked: The origin_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._origin_access_rule_locked = origin_access_rule_locked

    @property
    def origin_arg_locked(self):
        """Gets the origin_arg_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The origin_arg_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._origin_arg_locked

    @origin_arg_locked.setter
    def origin_arg_locked(self, origin_arg_locked):
        """Sets the origin_arg_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param origin_arg_locked: The origin_arg_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._origin_arg_locked = origin_arg_locked

    @property
    def origin_locked(self):
        """Gets the origin_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The origin_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._origin_locked

    @origin_locked.setter
    def origin_locked(self, origin_locked):
        """Sets the origin_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param origin_locked: The origin_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._origin_locked = origin_locked

    @property
    def origin_response_header_locked(self):
        """Gets the origin_response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The origin_response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._origin_response_header_locked

    @origin_response_header_locked.setter
    def origin_response_header_locked(self, origin_response_header_locked):
        """Sets the origin_response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param origin_response_header_locked: The origin_response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._origin_response_header_locked = origin_response_header_locked

    @property
    def origin_rewrite_locked(self):
        """Gets the origin_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The origin_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._origin_rewrite_locked

    @origin_rewrite_locked.setter
    def origin_rewrite_locked(self, origin_rewrite_locked):
        """Sets the origin_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param origin_rewrite_locked: The origin_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._origin_rewrite_locked = origin_rewrite_locked

    @property
    def quic_locked(self):
        """Gets the quic_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The quic_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._quic_locked

    @quic_locked.setter
    def quic_locked(self, quic_locked):
        """Sets the quic_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param quic_locked: The quic_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._quic_locked = quic_locked

    @property
    def redirection_rewrite_locked(self):
        """Gets the redirection_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The redirection_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._redirection_rewrite_locked

    @redirection_rewrite_locked.setter
    def redirection_rewrite_locked(self, redirection_rewrite_locked):
        """Sets the redirection_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param redirection_rewrite_locked: The redirection_rewrite_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._redirection_rewrite_locked = redirection_rewrite_locked

    @property
    def referer_access_rule_locked(self):
        """Gets the referer_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The referer_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._referer_access_rule_locked

    @referer_access_rule_locked.setter
    def referer_access_rule_locked(self, referer_access_rule_locked):
        """Sets the referer_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param referer_access_rule_locked: The referer_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._referer_access_rule_locked = referer_access_rule_locked

    @property
    def remote_auth_locked(self):
        """Gets the remote_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The remote_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._remote_auth_locked

    @remote_auth_locked.setter
    def remote_auth_locked(self, remote_auth_locked):
        """Sets the remote_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param remote_auth_locked: The remote_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._remote_auth_locked = remote_auth_locked

    @property
    def request_block_rule_locked(self):
        """Gets the request_block_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The request_block_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._request_block_rule_locked

    @request_block_rule_locked.setter
    def request_block_rule_locked(self, request_block_rule_locked):
        """Sets the request_block_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param request_block_rule_locked: The request_block_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._request_block_rule_locked = request_block_rule_locked

    @property
    def request_header_locked(self):
        """Gets the request_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The request_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._request_header_locked

    @request_header_locked.setter
    def request_header_locked(self, request_header_locked):
        """Sets the request_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param request_header_locked: The request_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._request_header_locked = request_header_locked

    @property
    def response_header_locked(self):
        """Gets the response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._response_header_locked

    @response_header_locked.setter
    def response_header_locked(self, response_header_locked):
        """Sets the response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param response_header_locked: The response_header_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._response_header_locked = response_header_locked

    @property
    def rule_engine_locked(self):
        """Gets the rule_engine_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The rule_engine_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._rule_engine_locked

    @rule_engine_locked.setter
    def rule_engine_locked(self, rule_engine_locked):
        """Sets the rule_engine_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param rule_engine_locked: The rule_engine_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._rule_engine_locked = rule_engine_locked

    @property
    def share_cache_locked(self):
        """Gets the share_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The share_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._share_cache_locked

    @share_cache_locked.setter
    def share_cache_locked(self, share_cache_locked):
        """Sets the share_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param share_cache_locked: The share_cache_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._share_cache_locked = share_cache_locked

    @property
    def sign_url_auth_locked(self):
        """Gets the sign_url_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The sign_url_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._sign_url_auth_locked

    @sign_url_auth_locked.setter
    def sign_url_auth_locked(self, sign_url_auth_locked):
        """Sets the sign_url_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param sign_url_auth_locked: The sign_url_auth_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._sign_url_auth_locked = sign_url_auth_locked

    @property
    def ua_access_rule_locked(self):
        """Gets the ua_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501


        :return: The ua_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._ua_access_rule_locked

    @ua_access_rule_locked.setter
    def ua_access_rule_locked(self, ua_access_rule_locked):
        """Sets the ua_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.


        :param ua_access_rule_locked: The ua_access_rule_locked of this ModuleLockConfigForDescribeCdnConfigOutput.  # noqa: E501
        :type: bool
        """

        self._ua_access_rule_locked = ua_access_rule_locked

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModuleLockConfigForDescribeCdnConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModuleLockConfigForDescribeCdnConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModuleLockConfigForDescribeCdnConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
