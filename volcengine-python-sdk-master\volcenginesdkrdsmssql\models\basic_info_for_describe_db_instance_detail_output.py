# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicInfoForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_use': 'float',
        'create_time': 'str',
        'db_engine_version': 'str',
        'inner_version': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'maintenance_time': 'str',
        'memory': 'int',
        'node_spec': 'str',
        'primary_instance_id': 'str',
        'project_name': 'str',
        'read_only_number': 'int',
        'region_id': 'str',
        'server_collation': 'str',
        'slow_query_enable': 'bool',
        'slow_query_time': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'storage_use': 'float',
        'subnet_id': 'str',
        'tags': 'list[TagForDescribeDBInstanceDetailOutput]',
        'time_zone': 'str',
        'update_time': 'str',
        'vcpu': 'int',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'backup_use': 'BackupUse',
        'create_time': 'CreateTime',
        'db_engine_version': 'DBEngineVersion',
        'inner_version': 'InnerVersion',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'maintenance_time': 'MaintenanceTime',
        'memory': 'Memory',
        'node_spec': 'NodeSpec',
        'primary_instance_id': 'PrimaryInstanceId',
        'project_name': 'ProjectName',
        'read_only_number': 'ReadOnlyNumber',
        'region_id': 'RegionId',
        'server_collation': 'ServerCollation',
        'slow_query_enable': 'SlowQueryEnable',
        'slow_query_time': 'SlowQueryTime',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'storage_use': 'StorageUse',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'time_zone': 'TimeZone',
        'update_time': 'UpdateTime',
        'vcpu': 'VCPU',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, backup_use=None, create_time=None, db_engine_version=None, inner_version=None, instance_id=None, instance_name=None, instance_status=None, instance_type=None, maintenance_time=None, memory=None, node_spec=None, primary_instance_id=None, project_name=None, read_only_number=None, region_id=None, server_collation=None, slow_query_enable=None, slow_query_time=None, storage_space=None, storage_type=None, storage_use=None, subnet_id=None, tags=None, time_zone=None, update_time=None, vcpu=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """BasicInfoForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_use = None
        self._create_time = None
        self._db_engine_version = None
        self._inner_version = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_type = None
        self._maintenance_time = None
        self._memory = None
        self._node_spec = None
        self._primary_instance_id = None
        self._project_name = None
        self._read_only_number = None
        self._region_id = None
        self._server_collation = None
        self._slow_query_enable = None
        self._slow_query_time = None
        self._storage_space = None
        self._storage_type = None
        self._storage_use = None
        self._subnet_id = None
        self._tags = None
        self._time_zone = None
        self._update_time = None
        self._vcpu = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if backup_use is not None:
            self.backup_use = backup_use
        if create_time is not None:
            self.create_time = create_time
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if inner_version is not None:
            self.inner_version = inner_version
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if maintenance_time is not None:
            self.maintenance_time = maintenance_time
        if memory is not None:
            self.memory = memory
        if node_spec is not None:
            self.node_spec = node_spec
        if primary_instance_id is not None:
            self.primary_instance_id = primary_instance_id
        if project_name is not None:
            self.project_name = project_name
        if read_only_number is not None:
            self.read_only_number = read_only_number
        if region_id is not None:
            self.region_id = region_id
        if server_collation is not None:
            self.server_collation = server_collation
        if slow_query_enable is not None:
            self.slow_query_enable = slow_query_enable
        if slow_query_time is not None:
            self.slow_query_time = slow_query_time
        if storage_space is not None:
            self.storage_space = storage_space
        if storage_type is not None:
            self.storage_type = storage_type
        if storage_use is not None:
            self.storage_use = storage_use
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if time_zone is not None:
            self.time_zone = time_zone
        if update_time is not None:
            self.update_time = update_time
        if vcpu is not None:
            self.vcpu = vcpu
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def backup_use(self):
        """Gets the backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._backup_use

    @backup_use.setter
    def backup_use(self, backup_use):
        """Sets the backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param backup_use: The backup_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: float
        """

        self._backup_use = backup_use

    @property
    def create_time(self):
        """Gets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param db_engine_version: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def inner_version(self):
        """Gets the inner_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The inner_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._inner_version

    @inner_version.setter
    def inner_version(self, inner_version):
        """Sets the inner_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param inner_version: The inner_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._inner_version = inner_version

    @property
    def instance_id(self):
        """Gets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_status: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_type: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def maintenance_time(self):
        """Gets the maintenance_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The maintenance_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._maintenance_time

    @maintenance_time.setter
    def maintenance_time(self, maintenance_time):
        """Sets the maintenance_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param maintenance_time: The maintenance_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._maintenance_time = maintenance_time

    @property
    def memory(self):
        """Gets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param memory: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def node_spec(self):
        """Gets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param node_spec: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_spec = node_spec

    @property
    def primary_instance_id(self):
        """Gets the primary_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The primary_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_instance_id

    @primary_instance_id.setter
    def primary_instance_id(self, primary_instance_id):
        """Sets the primary_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param primary_instance_id: The primary_instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._primary_instance_id = primary_instance_id

    @property
    def project_name(self):
        """Gets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param project_name: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def read_only_number(self):
        """Gets the read_only_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The read_only_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._read_only_number

    @read_only_number.setter
    def read_only_number(self, read_only_number):
        """Sets the read_only_number of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param read_only_number: The read_only_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._read_only_number = read_only_number

    @property
    def region_id(self):
        """Gets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param region_id: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def server_collation(self):
        """Gets the server_collation of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The server_collation of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_collation

    @server_collation.setter
    def server_collation(self, server_collation):
        """Sets the server_collation of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param server_collation: The server_collation of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._server_collation = server_collation

    @property
    def slow_query_enable(self):
        """Gets the slow_query_enable of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The slow_query_enable of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._slow_query_enable

    @slow_query_enable.setter
    def slow_query_enable(self, slow_query_enable):
        """Sets the slow_query_enable of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param slow_query_enable: The slow_query_enable of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: bool
        """

        self._slow_query_enable = slow_query_enable

    @property
    def slow_query_time(self):
        """Gets the slow_query_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The slow_query_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._slow_query_time

    @slow_query_time.setter
    def slow_query_time(self, slow_query_time):
        """Sets the slow_query_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param slow_query_time: The slow_query_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._slow_query_time = slow_query_time

    @property
    def storage_space(self):
        """Gets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_space: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_type: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    @property
    def storage_use(self):
        """Gets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._storage_use

    @storage_use.setter
    def storage_use(self, storage_use):
        """Sets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_use: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: float
        """

        self._storage_use = storage_use

    @property
    def subnet_id(self):
        """Gets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param subnet_id: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[TagForDescribeDBInstanceDetailOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param tags: The tags of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[TagForDescribeDBInstanceDetailOutput]
        """

        self._tags = tags

    @property
    def time_zone(self):
        """Gets the time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param time_zone: The time_zone of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    @property
    def update_time(self):
        """Gets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param update_time: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vcpu(self):
        """Gets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vcpu: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def vpc_id(self):
        """Gets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vpc_id: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param zone_id: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicInfoForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
