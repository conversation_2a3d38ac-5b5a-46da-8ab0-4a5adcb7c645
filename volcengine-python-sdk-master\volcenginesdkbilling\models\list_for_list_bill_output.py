# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListBillOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bill_category_parent': 'str',
        'bill_detail_num': 'str',
        'bill_id': 'str',
        'bill_period': 'str',
        'billing_mode': 'str',
        'busi_period': 'str',
        'business_mode': 'str',
        'coupon_amount': 'str',
        'credit_carried_amount': 'str',
        'currency': 'str',
        'discount_bill_amount': 'str',
        'expense_begin_time': 'str',
        'expense_end_time': 'str',
        'original_bill_amount': 'str',
        'owner_customer_name': 'str',
        'owner_id': 'str',
        'owner_user_name': 'str',
        'paid_amount': 'str',
        'pay_status': 'str',
        'payable_amount': 'str',
        'payer_customer_name': 'str',
        'payer_id': 'str',
        'payer_user_name': 'str',
        'preferential_bill_amount': 'str',
        'product': 'str',
        'product_zh': 'str',
        'round_bill_amount': 'str',
        'seller_customer_name': 'str',
        'seller_id': 'str',
        'seller_user_name': 'str',
        'settlement_type': 'str',
        'subject_name': 'str',
        'trade_time': 'str',
        'unpaid_amount': 'str'
    }

    attribute_map = {
        'bill_category_parent': 'BillCategoryParent',
        'bill_detail_num': 'BillDetailNum',
        'bill_id': 'BillID',
        'bill_period': 'BillPeriod',
        'billing_mode': 'BillingMode',
        'busi_period': 'BusiPeriod',
        'business_mode': 'BusinessMode',
        'coupon_amount': 'CouponAmount',
        'credit_carried_amount': 'CreditCarriedAmount',
        'currency': 'Currency',
        'discount_bill_amount': 'DiscountBillAmount',
        'expense_begin_time': 'ExpenseBeginTime',
        'expense_end_time': 'ExpenseEndTime',
        'original_bill_amount': 'OriginalBillAmount',
        'owner_customer_name': 'OwnerCustomerName',
        'owner_id': 'OwnerID',
        'owner_user_name': 'OwnerUserName',
        'paid_amount': 'PaidAmount',
        'pay_status': 'PayStatus',
        'payable_amount': 'PayableAmount',
        'payer_customer_name': 'PayerCustomerName',
        'payer_id': 'PayerID',
        'payer_user_name': 'PayerUserName',
        'preferential_bill_amount': 'PreferentialBillAmount',
        'product': 'Product',
        'product_zh': 'ProductZh',
        'round_bill_amount': 'RoundBillAmount',
        'seller_customer_name': 'SellerCustomerName',
        'seller_id': 'SellerID',
        'seller_user_name': 'SellerUserName',
        'settlement_type': 'SettlementType',
        'subject_name': 'SubjectName',
        'trade_time': 'TradeTime',
        'unpaid_amount': 'UnpaidAmount'
    }

    def __init__(self, bill_category_parent=None, bill_detail_num=None, bill_id=None, bill_period=None, billing_mode=None, busi_period=None, business_mode=None, coupon_amount=None, credit_carried_amount=None, currency=None, discount_bill_amount=None, expense_begin_time=None, expense_end_time=None, original_bill_amount=None, owner_customer_name=None, owner_id=None, owner_user_name=None, paid_amount=None, pay_status=None, payable_amount=None, payer_customer_name=None, payer_id=None, payer_user_name=None, preferential_bill_amount=None, product=None, product_zh=None, round_bill_amount=None, seller_customer_name=None, seller_id=None, seller_user_name=None, settlement_type=None, subject_name=None, trade_time=None, unpaid_amount=None, _configuration=None):  # noqa: E501
        """ListForListBillOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bill_category_parent = None
        self._bill_detail_num = None
        self._bill_id = None
        self._bill_period = None
        self._billing_mode = None
        self._busi_period = None
        self._business_mode = None
        self._coupon_amount = None
        self._credit_carried_amount = None
        self._currency = None
        self._discount_bill_amount = None
        self._expense_begin_time = None
        self._expense_end_time = None
        self._original_bill_amount = None
        self._owner_customer_name = None
        self._owner_id = None
        self._owner_user_name = None
        self._paid_amount = None
        self._pay_status = None
        self._payable_amount = None
        self._payer_customer_name = None
        self._payer_id = None
        self._payer_user_name = None
        self._preferential_bill_amount = None
        self._product = None
        self._product_zh = None
        self._round_bill_amount = None
        self._seller_customer_name = None
        self._seller_id = None
        self._seller_user_name = None
        self._settlement_type = None
        self._subject_name = None
        self._trade_time = None
        self._unpaid_amount = None
        self.discriminator = None

        if bill_category_parent is not None:
            self.bill_category_parent = bill_category_parent
        if bill_detail_num is not None:
            self.bill_detail_num = bill_detail_num
        if bill_id is not None:
            self.bill_id = bill_id
        if bill_period is not None:
            self.bill_period = bill_period
        if billing_mode is not None:
            self.billing_mode = billing_mode
        if busi_period is not None:
            self.busi_period = busi_period
        if business_mode is not None:
            self.business_mode = business_mode
        if coupon_amount is not None:
            self.coupon_amount = coupon_amount
        if credit_carried_amount is not None:
            self.credit_carried_amount = credit_carried_amount
        if currency is not None:
            self.currency = currency
        if discount_bill_amount is not None:
            self.discount_bill_amount = discount_bill_amount
        if expense_begin_time is not None:
            self.expense_begin_time = expense_begin_time
        if expense_end_time is not None:
            self.expense_end_time = expense_end_time
        if original_bill_amount is not None:
            self.original_bill_amount = original_bill_amount
        if owner_customer_name is not None:
            self.owner_customer_name = owner_customer_name
        if owner_id is not None:
            self.owner_id = owner_id
        if owner_user_name is not None:
            self.owner_user_name = owner_user_name
        if paid_amount is not None:
            self.paid_amount = paid_amount
        if pay_status is not None:
            self.pay_status = pay_status
        if payable_amount is not None:
            self.payable_amount = payable_amount
        if payer_customer_name is not None:
            self.payer_customer_name = payer_customer_name
        if payer_id is not None:
            self.payer_id = payer_id
        if payer_user_name is not None:
            self.payer_user_name = payer_user_name
        if preferential_bill_amount is not None:
            self.preferential_bill_amount = preferential_bill_amount
        if product is not None:
            self.product = product
        if product_zh is not None:
            self.product_zh = product_zh
        if round_bill_amount is not None:
            self.round_bill_amount = round_bill_amount
        if seller_customer_name is not None:
            self.seller_customer_name = seller_customer_name
        if seller_id is not None:
            self.seller_id = seller_id
        if seller_user_name is not None:
            self.seller_user_name = seller_user_name
        if settlement_type is not None:
            self.settlement_type = settlement_type
        if subject_name is not None:
            self.subject_name = subject_name
        if trade_time is not None:
            self.trade_time = trade_time
        if unpaid_amount is not None:
            self.unpaid_amount = unpaid_amount

    @property
    def bill_category_parent(self):
        """Gets the bill_category_parent of this ListForListBillOutput.  # noqa: E501


        :return: The bill_category_parent of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_category_parent

    @bill_category_parent.setter
    def bill_category_parent(self, bill_category_parent):
        """Sets the bill_category_parent of this ListForListBillOutput.


        :param bill_category_parent: The bill_category_parent of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._bill_category_parent = bill_category_parent

    @property
    def bill_detail_num(self):
        """Gets the bill_detail_num of this ListForListBillOutput.  # noqa: E501


        :return: The bill_detail_num of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_detail_num

    @bill_detail_num.setter
    def bill_detail_num(self, bill_detail_num):
        """Sets the bill_detail_num of this ListForListBillOutput.


        :param bill_detail_num: The bill_detail_num of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._bill_detail_num = bill_detail_num

    @property
    def bill_id(self):
        """Gets the bill_id of this ListForListBillOutput.  # noqa: E501


        :return: The bill_id of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_id

    @bill_id.setter
    def bill_id(self, bill_id):
        """Sets the bill_id of this ListForListBillOutput.


        :param bill_id: The bill_id of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._bill_id = bill_id

    @property
    def bill_period(self):
        """Gets the bill_period of this ListForListBillOutput.  # noqa: E501


        :return: The bill_period of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_period

    @bill_period.setter
    def bill_period(self, bill_period):
        """Sets the bill_period of this ListForListBillOutput.


        :param bill_period: The bill_period of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._bill_period = bill_period

    @property
    def billing_mode(self):
        """Gets the billing_mode of this ListForListBillOutput.  # noqa: E501


        :return: The billing_mode of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this ListForListBillOutput.


        :param billing_mode: The billing_mode of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._billing_mode = billing_mode

    @property
    def busi_period(self):
        """Gets the busi_period of this ListForListBillOutput.  # noqa: E501


        :return: The busi_period of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._busi_period

    @busi_period.setter
    def busi_period(self, busi_period):
        """Sets the busi_period of this ListForListBillOutput.


        :param busi_period: The busi_period of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._busi_period = busi_period

    @property
    def business_mode(self):
        """Gets the business_mode of this ListForListBillOutput.  # noqa: E501


        :return: The business_mode of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_mode

    @business_mode.setter
    def business_mode(self, business_mode):
        """Sets the business_mode of this ListForListBillOutput.


        :param business_mode: The business_mode of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._business_mode = business_mode

    @property
    def coupon_amount(self):
        """Gets the coupon_amount of this ListForListBillOutput.  # noqa: E501


        :return: The coupon_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._coupon_amount

    @coupon_amount.setter
    def coupon_amount(self, coupon_amount):
        """Sets the coupon_amount of this ListForListBillOutput.


        :param coupon_amount: The coupon_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._coupon_amount = coupon_amount

    @property
    def credit_carried_amount(self):
        """Gets the credit_carried_amount of this ListForListBillOutput.  # noqa: E501


        :return: The credit_carried_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._credit_carried_amount

    @credit_carried_amount.setter
    def credit_carried_amount(self, credit_carried_amount):
        """Sets the credit_carried_amount of this ListForListBillOutput.


        :param credit_carried_amount: The credit_carried_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._credit_carried_amount = credit_carried_amount

    @property
    def currency(self):
        """Gets the currency of this ListForListBillOutput.  # noqa: E501


        :return: The currency of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this ListForListBillOutput.


        :param currency: The currency of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def discount_bill_amount(self):
        """Gets the discount_bill_amount of this ListForListBillOutput.  # noqa: E501


        :return: The discount_bill_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_bill_amount

    @discount_bill_amount.setter
    def discount_bill_amount(self, discount_bill_amount):
        """Sets the discount_bill_amount of this ListForListBillOutput.


        :param discount_bill_amount: The discount_bill_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._discount_bill_amount = discount_bill_amount

    @property
    def expense_begin_time(self):
        """Gets the expense_begin_time of this ListForListBillOutput.  # noqa: E501


        :return: The expense_begin_time of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_begin_time

    @expense_begin_time.setter
    def expense_begin_time(self, expense_begin_time):
        """Sets the expense_begin_time of this ListForListBillOutput.


        :param expense_begin_time: The expense_begin_time of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._expense_begin_time = expense_begin_time

    @property
    def expense_end_time(self):
        """Gets the expense_end_time of this ListForListBillOutput.  # noqa: E501


        :return: The expense_end_time of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._expense_end_time

    @expense_end_time.setter
    def expense_end_time(self, expense_end_time):
        """Sets the expense_end_time of this ListForListBillOutput.


        :param expense_end_time: The expense_end_time of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._expense_end_time = expense_end_time

    @property
    def original_bill_amount(self):
        """Gets the original_bill_amount of this ListForListBillOutput.  # noqa: E501


        :return: The original_bill_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_bill_amount

    @original_bill_amount.setter
    def original_bill_amount(self, original_bill_amount):
        """Sets the original_bill_amount of this ListForListBillOutput.


        :param original_bill_amount: The original_bill_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._original_bill_amount = original_bill_amount

    @property
    def owner_customer_name(self):
        """Gets the owner_customer_name of this ListForListBillOutput.  # noqa: E501


        :return: The owner_customer_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_customer_name

    @owner_customer_name.setter
    def owner_customer_name(self, owner_customer_name):
        """Sets the owner_customer_name of this ListForListBillOutput.


        :param owner_customer_name: The owner_customer_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._owner_customer_name = owner_customer_name

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListBillOutput.  # noqa: E501


        :return: The owner_id of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListBillOutput.


        :param owner_id: The owner_id of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def owner_user_name(self):
        """Gets the owner_user_name of this ListForListBillOutput.  # noqa: E501


        :return: The owner_user_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_user_name

    @owner_user_name.setter
    def owner_user_name(self, owner_user_name):
        """Sets the owner_user_name of this ListForListBillOutput.


        :param owner_user_name: The owner_user_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._owner_user_name = owner_user_name

    @property
    def paid_amount(self):
        """Gets the paid_amount of this ListForListBillOutput.  # noqa: E501


        :return: The paid_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_amount

    @paid_amount.setter
    def paid_amount(self, paid_amount):
        """Sets the paid_amount of this ListForListBillOutput.


        :param paid_amount: The paid_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._paid_amount = paid_amount

    @property
    def pay_status(self):
        """Gets the pay_status of this ListForListBillOutput.  # noqa: E501


        :return: The pay_status of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._pay_status

    @pay_status.setter
    def pay_status(self, pay_status):
        """Sets the pay_status of this ListForListBillOutput.


        :param pay_status: The pay_status of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._pay_status = pay_status

    @property
    def payable_amount(self):
        """Gets the payable_amount of this ListForListBillOutput.  # noqa: E501


        :return: The payable_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._payable_amount

    @payable_amount.setter
    def payable_amount(self, payable_amount):
        """Sets the payable_amount of this ListForListBillOutput.


        :param payable_amount: The payable_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._payable_amount = payable_amount

    @property
    def payer_customer_name(self):
        """Gets the payer_customer_name of this ListForListBillOutput.  # noqa: E501


        :return: The payer_customer_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_customer_name

    @payer_customer_name.setter
    def payer_customer_name(self, payer_customer_name):
        """Sets the payer_customer_name of this ListForListBillOutput.


        :param payer_customer_name: The payer_customer_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._payer_customer_name = payer_customer_name

    @property
    def payer_id(self):
        """Gets the payer_id of this ListForListBillOutput.  # noqa: E501


        :return: The payer_id of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_id

    @payer_id.setter
    def payer_id(self, payer_id):
        """Sets the payer_id of this ListForListBillOutput.


        :param payer_id: The payer_id of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._payer_id = payer_id

    @property
    def payer_user_name(self):
        """Gets the payer_user_name of this ListForListBillOutput.  # noqa: E501


        :return: The payer_user_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_user_name

    @payer_user_name.setter
    def payer_user_name(self, payer_user_name):
        """Sets the payer_user_name of this ListForListBillOutput.


        :param payer_user_name: The payer_user_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._payer_user_name = payer_user_name

    @property
    def preferential_bill_amount(self):
        """Gets the preferential_bill_amount of this ListForListBillOutput.  # noqa: E501


        :return: The preferential_bill_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._preferential_bill_amount

    @preferential_bill_amount.setter
    def preferential_bill_amount(self, preferential_bill_amount):
        """Sets the preferential_bill_amount of this ListForListBillOutput.


        :param preferential_bill_amount: The preferential_bill_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._preferential_bill_amount = preferential_bill_amount

    @property
    def product(self):
        """Gets the product of this ListForListBillOutput.  # noqa: E501


        :return: The product of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListForListBillOutput.


        :param product: The product of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def product_zh(self):
        """Gets the product_zh of this ListForListBillOutput.  # noqa: E501


        :return: The product_zh of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_zh

    @product_zh.setter
    def product_zh(self, product_zh):
        """Sets the product_zh of this ListForListBillOutput.


        :param product_zh: The product_zh of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._product_zh = product_zh

    @property
    def round_bill_amount(self):
        """Gets the round_bill_amount of this ListForListBillOutput.  # noqa: E501


        :return: The round_bill_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._round_bill_amount

    @round_bill_amount.setter
    def round_bill_amount(self, round_bill_amount):
        """Sets the round_bill_amount of this ListForListBillOutput.


        :param round_bill_amount: The round_bill_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._round_bill_amount = round_bill_amount

    @property
    def seller_customer_name(self):
        """Gets the seller_customer_name of this ListForListBillOutput.  # noqa: E501


        :return: The seller_customer_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_customer_name

    @seller_customer_name.setter
    def seller_customer_name(self, seller_customer_name):
        """Sets the seller_customer_name of this ListForListBillOutput.


        :param seller_customer_name: The seller_customer_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._seller_customer_name = seller_customer_name

    @property
    def seller_id(self):
        """Gets the seller_id of this ListForListBillOutput.  # noqa: E501


        :return: The seller_id of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this ListForListBillOutput.


        :param seller_id: The seller_id of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def seller_user_name(self):
        """Gets the seller_user_name of this ListForListBillOutput.  # noqa: E501


        :return: The seller_user_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_user_name

    @seller_user_name.setter
    def seller_user_name(self, seller_user_name):
        """Sets the seller_user_name of this ListForListBillOutput.


        :param seller_user_name: The seller_user_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._seller_user_name = seller_user_name

    @property
    def settlement_type(self):
        """Gets the settlement_type of this ListForListBillOutput.  # noqa: E501


        :return: The settlement_type of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._settlement_type

    @settlement_type.setter
    def settlement_type(self, settlement_type):
        """Sets the settlement_type of this ListForListBillOutput.


        :param settlement_type: The settlement_type of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._settlement_type = settlement_type

    @property
    def subject_name(self):
        """Gets the subject_name of this ListForListBillOutput.  # noqa: E501


        :return: The subject_name of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_name

    @subject_name.setter
    def subject_name(self, subject_name):
        """Sets the subject_name of this ListForListBillOutput.


        :param subject_name: The subject_name of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._subject_name = subject_name

    @property
    def trade_time(self):
        """Gets the trade_time of this ListForListBillOutput.  # noqa: E501


        :return: The trade_time of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._trade_time

    @trade_time.setter
    def trade_time(self, trade_time):
        """Sets the trade_time of this ListForListBillOutput.


        :param trade_time: The trade_time of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._trade_time = trade_time

    @property
    def unpaid_amount(self):
        """Gets the unpaid_amount of this ListForListBillOutput.  # noqa: E501


        :return: The unpaid_amount of this ListForListBillOutput.  # noqa: E501
        :rtype: str
        """
        return self._unpaid_amount

    @unpaid_amount.setter
    def unpaid_amount(self, unpaid_amount):
        """Sets the unpaid_amount of this ListForListBillOutput.


        :param unpaid_amount: The unpaid_amount of this ListForListBillOutput.  # noqa: E501
        :type: str
        """

        self._unpaid_amount = unpaid_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListBillOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListBillOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListBillOutput):
            return True

        return self.to_dict() != other.to_dict()
