# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListWorkspaceInstanceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'active_series': 'int',
        'availability_zone_replicas': 'int',
        'downsampling_periods': 'list[str]',
        'id': 'str',
        'ingest_samples_per_second': 'int',
        'query_concurrency': 'int',
        'query_per_second': 'int',
        'replicas_per_zone': 'int',
        'retention_period': 'str',
        'scan_samples_per_second': 'int',
        'scan_series_per_second': 'int'
    }

    attribute_map = {
        'active_series': 'ActiveSeries',
        'availability_zone_replicas': 'AvailabilityZoneReplicas',
        'downsampling_periods': 'DownsamplingPeriods',
        'id': 'Id',
        'ingest_samples_per_second': 'IngestSamplesPerSecond',
        'query_concurrency': 'QueryConcurrency',
        'query_per_second': 'QueryPerSecond',
        'replicas_per_zone': 'ReplicasPerZone',
        'retention_period': 'RetentionPeriod',
        'scan_samples_per_second': 'ScanSamplesPerSecond',
        'scan_series_per_second': 'ScanSeriesPerSecond'
    }

    def __init__(self, active_series=None, availability_zone_replicas=None, downsampling_periods=None, id=None, ingest_samples_per_second=None, query_concurrency=None, query_per_second=None, replicas_per_zone=None, retention_period=None, scan_samples_per_second=None, scan_series_per_second=None, _configuration=None):  # noqa: E501
        """ItemForListWorkspaceInstanceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._active_series = None
        self._availability_zone_replicas = None
        self._downsampling_periods = None
        self._id = None
        self._ingest_samples_per_second = None
        self._query_concurrency = None
        self._query_per_second = None
        self._replicas_per_zone = None
        self._retention_period = None
        self._scan_samples_per_second = None
        self._scan_series_per_second = None
        self.discriminator = None

        if active_series is not None:
            self.active_series = active_series
        if availability_zone_replicas is not None:
            self.availability_zone_replicas = availability_zone_replicas
        if downsampling_periods is not None:
            self.downsampling_periods = downsampling_periods
        if id is not None:
            self.id = id
        if ingest_samples_per_second is not None:
            self.ingest_samples_per_second = ingest_samples_per_second
        if query_concurrency is not None:
            self.query_concurrency = query_concurrency
        if query_per_second is not None:
            self.query_per_second = query_per_second
        if replicas_per_zone is not None:
            self.replicas_per_zone = replicas_per_zone
        if retention_period is not None:
            self.retention_period = retention_period
        if scan_samples_per_second is not None:
            self.scan_samples_per_second = scan_samples_per_second
        if scan_series_per_second is not None:
            self.scan_series_per_second = scan_series_per_second

    @property
    def active_series(self):
        """Gets the active_series of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The active_series of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._active_series

    @active_series.setter
    def active_series(self, active_series):
        """Sets the active_series of this ItemForListWorkspaceInstanceTypesOutput.


        :param active_series: The active_series of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._active_series = active_series

    @property
    def availability_zone_replicas(self):
        """Gets the availability_zone_replicas of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The availability_zone_replicas of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._availability_zone_replicas

    @availability_zone_replicas.setter
    def availability_zone_replicas(self, availability_zone_replicas):
        """Sets the availability_zone_replicas of this ItemForListWorkspaceInstanceTypesOutput.


        :param availability_zone_replicas: The availability_zone_replicas of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._availability_zone_replicas = availability_zone_replicas

    @property
    def downsampling_periods(self):
        """Gets the downsampling_periods of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The downsampling_periods of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._downsampling_periods

    @downsampling_periods.setter
    def downsampling_periods(self, downsampling_periods):
        """Sets the downsampling_periods of this ItemForListWorkspaceInstanceTypesOutput.


        :param downsampling_periods: The downsampling_periods of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: list[str]
        """

        self._downsampling_periods = downsampling_periods

    @property
    def id(self):
        """Gets the id of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The id of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListWorkspaceInstanceTypesOutput.


        :param id: The id of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def ingest_samples_per_second(self):
        """Gets the ingest_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The ingest_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._ingest_samples_per_second

    @ingest_samples_per_second.setter
    def ingest_samples_per_second(self, ingest_samples_per_second):
        """Sets the ingest_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.


        :param ingest_samples_per_second: The ingest_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._ingest_samples_per_second = ingest_samples_per_second

    @property
    def query_concurrency(self):
        """Gets the query_concurrency of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The query_concurrency of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._query_concurrency

    @query_concurrency.setter
    def query_concurrency(self, query_concurrency):
        """Sets the query_concurrency of this ItemForListWorkspaceInstanceTypesOutput.


        :param query_concurrency: The query_concurrency of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._query_concurrency = query_concurrency

    @property
    def query_per_second(self):
        """Gets the query_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The query_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._query_per_second

    @query_per_second.setter
    def query_per_second(self, query_per_second):
        """Sets the query_per_second of this ItemForListWorkspaceInstanceTypesOutput.


        :param query_per_second: The query_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._query_per_second = query_per_second

    @property
    def replicas_per_zone(self):
        """Gets the replicas_per_zone of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The replicas_per_zone of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._replicas_per_zone

    @replicas_per_zone.setter
    def replicas_per_zone(self, replicas_per_zone):
        """Sets the replicas_per_zone of this ItemForListWorkspaceInstanceTypesOutput.


        :param replicas_per_zone: The replicas_per_zone of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._replicas_per_zone = replicas_per_zone

    @property
    def retention_period(self):
        """Gets the retention_period of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The retention_period of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._retention_period

    @retention_period.setter
    def retention_period(self, retention_period):
        """Sets the retention_period of this ItemForListWorkspaceInstanceTypesOutput.


        :param retention_period: The retention_period of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: str
        """

        self._retention_period = retention_period

    @property
    def scan_samples_per_second(self):
        """Gets the scan_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The scan_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._scan_samples_per_second

    @scan_samples_per_second.setter
    def scan_samples_per_second(self, scan_samples_per_second):
        """Sets the scan_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.


        :param scan_samples_per_second: The scan_samples_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._scan_samples_per_second = scan_samples_per_second

    @property
    def scan_series_per_second(self):
        """Gets the scan_series_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501


        :return: The scan_series_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._scan_series_per_second

    @scan_series_per_second.setter
    def scan_series_per_second(self, scan_series_per_second):
        """Sets the scan_series_per_second of this ItemForListWorkspaceInstanceTypesOutput.


        :param scan_series_per_second: The scan_series_per_second of this ItemForListWorkspaceInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._scan_series_per_second = scan_series_per_second

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListWorkspaceInstanceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListWorkspaceInstanceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListWorkspaceInstanceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
