# coding: utf-8

"""
    resourcecenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetResourceCenterStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'init_status': 'str',
        'service_status': 'str'
    }

    attribute_map = {
        'init_status': 'InitStatus',
        'service_status': 'ServiceStatus'
    }

    def __init__(self, init_status=None, service_status=None, _configuration=None):  # noqa: E501
        """GetResourceCenterStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._init_status = None
        self._service_status = None
        self.discriminator = None

        if init_status is not None:
            self.init_status = init_status
        if service_status is not None:
            self.service_status = service_status

    @property
    def init_status(self):
        """Gets the init_status of this GetResourceCenterStatusResponse.  # noqa: E501


        :return: The init_status of this GetResourceCenterStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._init_status

    @init_status.setter
    def init_status(self, init_status):
        """Sets the init_status of this GetResourceCenterStatusResponse.


        :param init_status: The init_status of this GetResourceCenterStatusResponse.  # noqa: E501
        :type: str
        """

        self._init_status = init_status

    @property
    def service_status(self):
        """Gets the service_status of this GetResourceCenterStatusResponse.  # noqa: E501


        :return: The service_status of this GetResourceCenterStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_status

    @service_status.setter
    def service_status(self, service_status):
        """Sets the service_status of this GetResourceCenterStatusResponse.


        :param service_status: The service_status of this GetResourceCenterStatusResponse.  # noqa: E501
        :type: str
        """

        self._service_status = service_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetResourceCenterStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetResourceCenterStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetResourceCenterStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
