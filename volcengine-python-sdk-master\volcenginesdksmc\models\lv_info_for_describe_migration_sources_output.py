# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LvInfoForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'block_devices': 'list[str]',
        'fs_type': 'str',
        'lv_name': 'str',
        'lv_path': 'str',
        'lv_size': 'int',
        'lv_uuid': 'str',
        'lv_used_size': 'int',
        'mount_point': 'str',
        'vg_name': 'str'
    }

    attribute_map = {
        'block_devices': 'BlockDevices',
        'fs_type': 'FsType',
        'lv_name': 'LvName',
        'lv_path': 'LvPath',
        'lv_size': 'LvSize',
        'lv_uuid': 'LvUUID',
        'lv_used_size': 'LvUsedSize',
        'mount_point': 'MountPoint',
        'vg_name': 'VgName'
    }

    def __init__(self, block_devices=None, fs_type=None, lv_name=None, lv_path=None, lv_size=None, lv_uuid=None, lv_used_size=None, mount_point=None, vg_name=None, _configuration=None):  # noqa: E501
        """LvInfoForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._block_devices = None
        self._fs_type = None
        self._lv_name = None
        self._lv_path = None
        self._lv_size = None
        self._lv_uuid = None
        self._lv_used_size = None
        self._mount_point = None
        self._vg_name = None
        self.discriminator = None

        if block_devices is not None:
            self.block_devices = block_devices
        if fs_type is not None:
            self.fs_type = fs_type
        if lv_name is not None:
            self.lv_name = lv_name
        if lv_path is not None:
            self.lv_path = lv_path
        if lv_size is not None:
            self.lv_size = lv_size
        if lv_uuid is not None:
            self.lv_uuid = lv_uuid
        if lv_used_size is not None:
            self.lv_used_size = lv_used_size
        if mount_point is not None:
            self.mount_point = mount_point
        if vg_name is not None:
            self.vg_name = vg_name

    @property
    def block_devices(self):
        """Gets the block_devices of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The block_devices of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._block_devices

    @block_devices.setter
    def block_devices(self, block_devices):
        """Sets the block_devices of this LvInfoForDescribeMigrationSourcesOutput.


        :param block_devices: The block_devices of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._block_devices = block_devices

    @property
    def fs_type(self):
        """Gets the fs_type of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The fs_type of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._fs_type

    @fs_type.setter
    def fs_type(self, fs_type):
        """Sets the fs_type of this LvInfoForDescribeMigrationSourcesOutput.


        :param fs_type: The fs_type of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._fs_type = fs_type

    @property
    def lv_name(self):
        """Gets the lv_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._lv_name

    @lv_name.setter
    def lv_name(self, lv_name):
        """Sets the lv_name of this LvInfoForDescribeMigrationSourcesOutput.


        :param lv_name: The lv_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._lv_name = lv_name

    @property
    def lv_path(self):
        """Gets the lv_path of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_path of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._lv_path

    @lv_path.setter
    def lv_path(self, lv_path):
        """Sets the lv_path of this LvInfoForDescribeMigrationSourcesOutput.


        :param lv_path: The lv_path of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._lv_path = lv_path

    @property
    def lv_size(self):
        """Gets the lv_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._lv_size

    @lv_size.setter
    def lv_size(self, lv_size):
        """Sets the lv_size of this LvInfoForDescribeMigrationSourcesOutput.


        :param lv_size: The lv_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._lv_size = lv_size

    @property
    def lv_uuid(self):
        """Gets the lv_uuid of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_uuid of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._lv_uuid

    @lv_uuid.setter
    def lv_uuid(self, lv_uuid):
        """Sets the lv_uuid of this LvInfoForDescribeMigrationSourcesOutput.


        :param lv_uuid: The lv_uuid of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._lv_uuid = lv_uuid

    @property
    def lv_used_size(self):
        """Gets the lv_used_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_used_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._lv_used_size

    @lv_used_size.setter
    def lv_used_size(self, lv_used_size):
        """Sets the lv_used_size of this LvInfoForDescribeMigrationSourcesOutput.


        :param lv_used_size: The lv_used_size of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._lv_used_size = lv_used_size

    @property
    def mount_point(self):
        """Gets the mount_point of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The mount_point of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_point

    @mount_point.setter
    def mount_point(self, mount_point):
        """Sets the mount_point of this LvInfoForDescribeMigrationSourcesOutput.


        :param mount_point: The mount_point of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._mount_point = mount_point

    @property
    def vg_name(self):
        """Gets the vg_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The vg_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vg_name

    @vg_name.setter
    def vg_name(self, vg_name):
        """Sets the vg_name of this LvInfoForDescribeMigrationSourcesOutput.


        :param vg_name: The vg_name of this LvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._vg_name = vg_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LvInfoForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LvInfoForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LvInfoForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
