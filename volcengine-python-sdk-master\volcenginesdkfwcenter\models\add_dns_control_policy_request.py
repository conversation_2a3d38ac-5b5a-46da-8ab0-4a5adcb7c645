# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddDnsControlPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'destination': 'str',
        'destination_type': 'str',
        'source': 'list[SourceForAddDnsControlPolicyInput]'
    }

    attribute_map = {
        'description': 'Description',
        'destination': 'Destination',
        'destination_type': 'DestinationType',
        'source': 'Source'
    }

    def __init__(self, description=None, destination=None, destination_type=None, source=None, _configuration=None):  # noqa: E501
        """AddDnsControlPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._destination = None
        self._destination_type = None
        self._source = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.destination = destination
        self.destination_type = destination_type
        if source is not None:
            self.source = source

    @property
    def description(self):
        """Gets the description of this AddDnsControlPolicyRequest.  # noqa: E501


        :return: The description of this AddDnsControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this AddDnsControlPolicyRequest.


        :param description: The description of this AddDnsControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 100):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `100`")  # noqa: E501

        self._description = description

    @property
    def destination(self):
        """Gets the destination of this AddDnsControlPolicyRequest.  # noqa: E501


        :return: The destination of this AddDnsControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination

    @destination.setter
    def destination(self, destination):
        """Sets the destination of this AddDnsControlPolicyRequest.


        :param destination: The destination of this AddDnsControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and destination is None:
            raise ValueError("Invalid value for `destination`, must not be `None`")  # noqa: E501

        self._destination = destination

    @property
    def destination_type(self):
        """Gets the destination_type of this AddDnsControlPolicyRequest.  # noqa: E501


        :return: The destination_type of this AddDnsControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_type

    @destination_type.setter
    def destination_type(self, destination_type):
        """Sets the destination_type of this AddDnsControlPolicyRequest.


        :param destination_type: The destination_type of this AddDnsControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and destination_type is None:
            raise ValueError("Invalid value for `destination_type`, must not be `None`")  # noqa: E501
        allowed_values = ["group", "domain"]  # noqa: E501
        if (self._configuration.client_side_validation and
                destination_type not in allowed_values):
            raise ValueError(
                "Invalid value for `destination_type` ({0}), must be one of {1}"  # noqa: E501
                .format(destination_type, allowed_values)
            )

        self._destination_type = destination_type

    @property
    def source(self):
        """Gets the source of this AddDnsControlPolicyRequest.  # noqa: E501


        :return: The source of this AddDnsControlPolicyRequest.  # noqa: E501
        :rtype: list[SourceForAddDnsControlPolicyInput]
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this AddDnsControlPolicyRequest.


        :param source: The source of this AddDnsControlPolicyRequest.  # noqa: E501
        :type: list[SourceForAddDnsControlPolicyInput]
        """

        self._source = source

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddDnsControlPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddDnsControlPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddDnsControlPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
