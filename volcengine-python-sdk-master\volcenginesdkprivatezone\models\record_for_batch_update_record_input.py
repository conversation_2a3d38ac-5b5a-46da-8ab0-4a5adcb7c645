# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RecordForBatchUpdateRecordInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable': 'bool',
        'host': 'str',
        'line': 'str',
        'record_id': 'str',
        'remark': 'str',
        'ttl': 'int',
        'type': 'str',
        'value': 'str',
        'weight': 'int'
    }

    attribute_map = {
        'enable': 'Enable',
        'host': 'Host',
        'line': 'Line',
        'record_id': 'RecordID',
        'remark': 'Remark',
        'ttl': 'TTL',
        'type': 'Type',
        'value': 'Value',
        'weight': 'Weight'
    }

    def __init__(self, enable=None, host=None, line=None, record_id=None, remark=None, ttl=None, type=None, value=None, weight=None, _configuration=None):  # noqa: E501
        """RecordForBatchUpdateRecordInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable = None
        self._host = None
        self._line = None
        self._record_id = None
        self._remark = None
        self._ttl = None
        self._type = None
        self._value = None
        self._weight = None
        self.discriminator = None

        if enable is not None:
            self.enable = enable
        if host is not None:
            self.host = host
        if line is not None:
            self.line = line
        if record_id is not None:
            self.record_id = record_id
        if remark is not None:
            self.remark = remark
        if ttl is not None:
            self.ttl = ttl
        if type is not None:
            self.type = type
        if value is not None:
            self.value = value
        if weight is not None:
            self.weight = weight

    @property
    def enable(self):
        """Gets the enable of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The enable of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this RecordForBatchUpdateRecordInput.


        :param enable: The enable of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def host(self):
        """Gets the host of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The host of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this RecordForBatchUpdateRecordInput.


        :param host: The host of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def line(self):
        """Gets the line of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The line of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this RecordForBatchUpdateRecordInput.


        :param line: The line of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def record_id(self):
        """Gets the record_id of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The record_id of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._record_id

    @record_id.setter
    def record_id(self, record_id):
        """Sets the record_id of this RecordForBatchUpdateRecordInput.


        :param record_id: The record_id of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._record_id = record_id

    @property
    def remark(self):
        """Gets the remark of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The remark of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this RecordForBatchUpdateRecordInput.


        :param remark: The remark of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def ttl(self):
        """Gets the ttl of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The ttl of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: int
        """
        return self._ttl

    @ttl.setter
    def ttl(self, ttl):
        """Sets the ttl of this RecordForBatchUpdateRecordInput.


        :param ttl: The ttl of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: int
        """

        self._ttl = ttl

    @property
    def type(self):
        """Gets the type of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The type of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this RecordForBatchUpdateRecordInput.


        :param type: The type of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def value(self):
        """Gets the value of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The value of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this RecordForBatchUpdateRecordInput.


        :param value: The value of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: str
        """

        self._value = value

    @property
    def weight(self):
        """Gets the weight of this RecordForBatchUpdateRecordInput.  # noqa: E501


        :return: The weight of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :rtype: int
        """
        return self._weight

    @weight.setter
    def weight(self, weight):
        """Sets the weight of this RecordForBatchUpdateRecordInput.


        :param weight: The weight of this RecordForBatchUpdateRecordInput.  # noqa: E501
        :type: int
        """

        self._weight = weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RecordForBatchUpdateRecordInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RecordForBatchUpdateRecordInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RecordForBatchUpdateRecordInput):
            return True

        return self.to_dict() != other.to_dict()
