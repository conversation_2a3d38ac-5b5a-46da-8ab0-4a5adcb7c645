# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateClusterUserRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'description': 'str',
        'user_group_names': 'list[str]',
        'user_name': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'description': 'Description',
        'user_group_names': 'UserGroupNames',
        'user_name': 'UserName'
    }

    def __init__(self, cluster_id=None, description=None, user_group_names=None, user_name=None, _configuration=None):  # noqa: E501
        """UpdateClusterUserRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._description = None
        self._user_group_names = None
        self._user_name = None
        self.discriminator = None

        self.cluster_id = cluster_id
        if description is not None:
            self.description = description
        if user_group_names is not None:
            self.user_group_names = user_group_names
        self.user_name = user_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this UpdateClusterUserRequest.  # noqa: E501


        :return: The cluster_id of this UpdateClusterUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this UpdateClusterUserRequest.


        :param cluster_id: The cluster_id of this UpdateClusterUserRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def description(self):
        """Gets the description of this UpdateClusterUserRequest.  # noqa: E501


        :return: The description of this UpdateClusterUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateClusterUserRequest.


        :param description: The description of this UpdateClusterUserRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def user_group_names(self):
        """Gets the user_group_names of this UpdateClusterUserRequest.  # noqa: E501


        :return: The user_group_names of this UpdateClusterUserRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_group_names

    @user_group_names.setter
    def user_group_names(self, user_group_names):
        """Sets the user_group_names of this UpdateClusterUserRequest.


        :param user_group_names: The user_group_names of this UpdateClusterUserRequest.  # noqa: E501
        :type: list[str]
        """

        self._user_group_names = user_group_names

    @property
    def user_name(self):
        """Gets the user_name of this UpdateClusterUserRequest.  # noqa: E501


        :return: The user_name of this UpdateClusterUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this UpdateClusterUserRequest.


        :param user_name: The user_name of this UpdateClusterUserRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user_name is None:
            raise ValueError("Invalid value for `user_name`, must not be `None`")  # noqa: E501

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateClusterUserRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateClusterUserRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateClusterUserRequest):
            return True

        return self.to_dict() != other.to_dict()
