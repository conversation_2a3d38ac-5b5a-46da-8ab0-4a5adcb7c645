# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTransitRouterTrafficQosQueueEntryRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_percent': 'int',
        'client_token': 'str',
        'description': 'str',
        'dscps': 'list[int]',
        'transit_router_traffic_qos_queue_entry_name': 'str',
        'transit_router_traffic_qos_queue_policy_id': 'str'
    }

    attribute_map = {
        'bandwidth_percent': 'BandwidthPercent',
        'client_token': 'ClientToken',
        'description': 'Description',
        'dscps': 'Dscps',
        'transit_router_traffic_qos_queue_entry_name': 'TransitRouterTrafficQosQueueEntryName',
        'transit_router_traffic_qos_queue_policy_id': 'TransitRouterTrafficQosQueuePolicyId'
    }

    def __init__(self, bandwidth_percent=None, client_token=None, description=None, dscps=None, transit_router_traffic_qos_queue_entry_name=None, transit_router_traffic_qos_queue_policy_id=None, _configuration=None):  # noqa: E501
        """CreateTransitRouterTrafficQosQueueEntryRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_percent = None
        self._client_token = None
        self._description = None
        self._dscps = None
        self._transit_router_traffic_qos_queue_entry_name = None
        self._transit_router_traffic_qos_queue_policy_id = None
        self.discriminator = None

        self.bandwidth_percent = bandwidth_percent
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if dscps is not None:
            self.dscps = dscps
        self.transit_router_traffic_qos_queue_entry_name = transit_router_traffic_qos_queue_entry_name
        self.transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id

    @property
    def bandwidth_percent(self):
        """Gets the bandwidth_percent of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The bandwidth_percent of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_percent

    @bandwidth_percent.setter
    def bandwidth_percent(self, bandwidth_percent):
        """Sets the bandwidth_percent of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param bandwidth_percent: The bandwidth_percent of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth_percent is None:
            raise ValueError("Invalid value for `bandwidth_percent`, must not be `None`")  # noqa: E501

        self._bandwidth_percent = bandwidth_percent

    @property
    def client_token(self):
        """Gets the client_token of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The client_token of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param client_token: The client_token of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The description of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param description: The description of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dscps(self):
        """Gets the dscps of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The dscps of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._dscps

    @dscps.setter
    def dscps(self, dscps):
        """Sets the dscps of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param dscps: The dscps of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: list[int]
        """

        self._dscps = dscps

    @property
    def transit_router_traffic_qos_queue_entry_name(self):
        """Gets the transit_router_traffic_qos_queue_entry_name of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_entry_name of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_entry_name

    @transit_router_traffic_qos_queue_entry_name.setter
    def transit_router_traffic_qos_queue_entry_name(self, transit_router_traffic_qos_queue_entry_name):
        """Sets the transit_router_traffic_qos_queue_entry_name of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param transit_router_traffic_qos_queue_entry_name: The transit_router_traffic_qos_queue_entry_name of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_traffic_qos_queue_entry_name is None:
            raise ValueError("Invalid value for `transit_router_traffic_qos_queue_entry_name`, must not be `None`")  # noqa: E501

        self._transit_router_traffic_qos_queue_entry_name = transit_router_traffic_qos_queue_entry_name

    @property
    def transit_router_traffic_qos_queue_policy_id(self):
        """Gets the transit_router_traffic_qos_queue_policy_id of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_policy_id of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_policy_id

    @transit_router_traffic_qos_queue_policy_id.setter
    def transit_router_traffic_qos_queue_policy_id(self, transit_router_traffic_qos_queue_policy_id):
        """Sets the transit_router_traffic_qos_queue_policy_id of this CreateTransitRouterTrafficQosQueueEntryRequest.


        :param transit_router_traffic_qos_queue_policy_id: The transit_router_traffic_qos_queue_policy_id of this CreateTransitRouterTrafficQosQueueEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_traffic_qos_queue_policy_id is None:
            raise ValueError("Invalid value for `transit_router_traffic_qos_queue_policy_id`, must not be `None`")  # noqa: E501

        self._transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTransitRouterTrafficQosQueueEntryRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTransitRouterTrafficQosQueueEntryRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTransitRouterTrafficQosQueueEntryRequest):
            return True

        return self.to_dict() != other.to_dict()
