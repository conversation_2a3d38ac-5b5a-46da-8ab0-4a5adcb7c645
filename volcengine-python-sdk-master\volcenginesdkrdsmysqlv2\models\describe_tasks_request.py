# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTasksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_end_time': 'str',
        'creation_start_time': 'str',
        'instance_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'task_action': 'str',
        'task_category': 'list[str]',
        'task_id': 'str',
        'task_source': 'str',
        'task_status': 'list[str]',
        'task_type': 'str'
    }

    attribute_map = {
        'creation_end_time': 'CreationEndTime',
        'creation_start_time': 'CreationStartTime',
        'instance_id': 'InstanceId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'task_action': 'TaskAction',
        'task_category': 'TaskCategory',
        'task_id': 'TaskId',
        'task_source': 'TaskSource',
        'task_status': 'TaskStatus',
        'task_type': 'TaskType'
    }

    def __init__(self, creation_end_time=None, creation_start_time=None, instance_id=None, page_number=None, page_size=None, project_name=None, task_action=None, task_category=None, task_id=None, task_source=None, task_status=None, task_type=None, _configuration=None):  # noqa: E501
        """DescribeTasksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_end_time = None
        self._creation_start_time = None
        self._instance_id = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._task_action = None
        self._task_category = None
        self._task_id = None
        self._task_source = None
        self._task_status = None
        self._task_type = None
        self.discriminator = None

        if creation_end_time is not None:
            self.creation_end_time = creation_end_time
        if creation_start_time is not None:
            self.creation_start_time = creation_start_time
        if instance_id is not None:
            self.instance_id = instance_id
        self.page_number = page_number
        self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if task_action is not None:
            self.task_action = task_action
        if task_category is not None:
            self.task_category = task_category
        if task_id is not None:
            self.task_id = task_id
        if task_source is not None:
            self.task_source = task_source
        if task_status is not None:
            self.task_status = task_status
        if task_type is not None:
            self.task_type = task_type

    @property
    def creation_end_time(self):
        """Gets the creation_end_time of this DescribeTasksRequest.  # noqa: E501


        :return: The creation_end_time of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._creation_end_time

    @creation_end_time.setter
    def creation_end_time(self, creation_end_time):
        """Sets the creation_end_time of this DescribeTasksRequest.


        :param creation_end_time: The creation_end_time of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._creation_end_time = creation_end_time

    @property
    def creation_start_time(self):
        """Gets the creation_start_time of this DescribeTasksRequest.  # noqa: E501


        :return: The creation_start_time of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._creation_start_time

    @creation_start_time.setter
    def creation_start_time(self, creation_start_time):
        """Sets the creation_start_time of this DescribeTasksRequest.


        :param creation_start_time: The creation_start_time of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._creation_start_time = creation_start_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeTasksRequest.  # noqa: E501


        :return: The instance_id of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeTasksRequest.


        :param instance_id: The instance_id of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTasksRequest.  # noqa: E501


        :return: The page_number of this DescribeTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTasksRequest.


        :param page_number: The page_number of this DescribeTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTasksRequest.  # noqa: E501


        :return: The page_size of this DescribeTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTasksRequest.


        :param page_size: The page_size of this DescribeTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeTasksRequest.  # noqa: E501


        :return: The project_name of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeTasksRequest.


        :param project_name: The project_name of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def task_action(self):
        """Gets the task_action of this DescribeTasksRequest.  # noqa: E501


        :return: The task_action of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_action

    @task_action.setter
    def task_action(self, task_action):
        """Sets the task_action of this DescribeTasksRequest.


        :param task_action: The task_action of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_action = task_action

    @property
    def task_category(self):
        """Gets the task_category of this DescribeTasksRequest.  # noqa: E501


        :return: The task_category of this DescribeTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._task_category

    @task_category.setter
    def task_category(self, task_category):
        """Sets the task_category of this DescribeTasksRequest.


        :param task_category: The task_category of this DescribeTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._task_category = task_category

    @property
    def task_id(self):
        """Gets the task_id of this DescribeTasksRequest.  # noqa: E501


        :return: The task_id of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DescribeTasksRequest.


        :param task_id: The task_id of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_source(self):
        """Gets the task_source of this DescribeTasksRequest.  # noqa: E501


        :return: The task_source of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_source

    @task_source.setter
    def task_source(self, task_source):
        """Sets the task_source of this DescribeTasksRequest.


        :param task_source: The task_source of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_source = task_source

    @property
    def task_status(self):
        """Gets the task_status of this DescribeTasksRequest.  # noqa: E501


        :return: The task_status of this DescribeTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this DescribeTasksRequest.


        :param task_status: The task_status of this DescribeTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._task_status = task_status

    @property
    def task_type(self):
        """Gets the task_type of this DescribeTasksRequest.  # noqa: E501


        :return: The task_type of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this DescribeTasksRequest.


        :param task_type: The task_type of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTasksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTasksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTasksRequest):
            return True

        return self.to_dict() != other.to_dict()
