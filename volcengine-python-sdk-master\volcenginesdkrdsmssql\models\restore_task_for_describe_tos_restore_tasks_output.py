# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreTaskForDescribeTosRestoreTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'instance_name': 'str',
        'is_replace': 'bool',
        'restore_task_id': 'str',
        'restore_type': 'str',
        'task_desc': 'str',
        'task_end_time': 'str',
        'task_start_time': 'str',
        'task_status': 'str',
        'task_type': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'is_replace': 'IsReplace',
        'restore_task_id': 'RestoreTaskId',
        'restore_type': 'RestoreType',
        'task_desc': 'TaskDesc',
        'task_end_time': 'TaskEndTime',
        'task_start_time': 'TaskStartTime',
        'task_status': 'TaskStatus',
        'task_type': 'TaskType'
    }

    def __init__(self, instance_id=None, instance_name=None, is_replace=None, restore_task_id=None, restore_type=None, task_desc=None, task_end_time=None, task_start_time=None, task_status=None, task_type=None, _configuration=None):  # noqa: E501
        """RestoreTaskForDescribeTosRestoreTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._instance_name = None
        self._is_replace = None
        self._restore_task_id = None
        self._restore_type = None
        self._task_desc = None
        self._task_end_time = None
        self._task_start_time = None
        self._task_status = None
        self._task_type = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if is_replace is not None:
            self.is_replace = is_replace
        if restore_task_id is not None:
            self.restore_task_id = restore_task_id
        if restore_type is not None:
            self.restore_type = restore_type
        if task_desc is not None:
            self.task_desc = task_desc
        if task_end_time is not None:
            self.task_end_time = task_end_time
        if task_start_time is not None:
            self.task_start_time = task_start_time
        if task_status is not None:
            self.task_status = task_status
        if task_type is not None:
            self.task_type = task_type

    @property
    def instance_id(self):
        """Gets the instance_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The instance_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param instance_id: The instance_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The instance_name of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param instance_name: The instance_name of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def is_replace(self):
        """Gets the is_replace of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The is_replace of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_replace

    @is_replace.setter
    def is_replace(self, is_replace):
        """Sets the is_replace of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param is_replace: The is_replace of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: bool
        """

        self._is_replace = is_replace

    @property
    def restore_task_id(self):
        """Gets the restore_task_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The restore_task_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._restore_task_id

    @restore_task_id.setter
    def restore_task_id(self, restore_task_id):
        """Sets the restore_task_id of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param restore_task_id: The restore_task_id of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._restore_task_id = restore_task_id

    @property
    def restore_type(self):
        """Gets the restore_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The restore_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._restore_type

    @restore_type.setter
    def restore_type(self, restore_type):
        """Sets the restore_type of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param restore_type: The restore_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._restore_type = restore_type

    @property
    def task_desc(self):
        """Gets the task_desc of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The task_desc of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_desc

    @task_desc.setter
    def task_desc(self, task_desc):
        """Sets the task_desc of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param task_desc: The task_desc of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_desc = task_desc

    @property
    def task_end_time(self):
        """Gets the task_end_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The task_end_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_end_time

    @task_end_time.setter
    def task_end_time(self, task_end_time):
        """Sets the task_end_time of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param task_end_time: The task_end_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_end_time = task_end_time

    @property
    def task_start_time(self):
        """Gets the task_start_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The task_start_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_start_time

    @task_start_time.setter
    def task_start_time(self, task_start_time):
        """Sets the task_start_time of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param task_start_time: The task_start_time of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_start_time = task_start_time

    @property
    def task_status(self):
        """Gets the task_status of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The task_status of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param task_status: The task_status of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_status = task_status

    @property
    def task_type(self):
        """Gets the task_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501


        :return: The task_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this RestoreTaskForDescribeTosRestoreTasksOutput.


        :param task_type: The task_type of this RestoreTaskForDescribeTosRestoreTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreTaskForDescribeTosRestoreTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreTaskForDescribeTosRestoreTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreTaskForDescribeTosRestoreTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
