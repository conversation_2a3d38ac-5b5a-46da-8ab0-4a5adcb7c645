# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GenerateDataKeyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ciphertext_blob': 'str',
        'plaintext': 'str'
    }

    attribute_map = {
        'ciphertext_blob': 'CiphertextBlob',
        'plaintext': 'Plaintext'
    }

    def __init__(self, ciphertext_blob=None, plaintext=None, _configuration=None):  # noqa: E501
        """GenerateDataKeyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ciphertext_blob = None
        self._plaintext = None
        self.discriminator = None

        if ciphertext_blob is not None:
            self.ciphertext_blob = ciphertext_blob
        if plaintext is not None:
            self.plaintext = plaintext

    @property
    def ciphertext_blob(self):
        """Gets the ciphertext_blob of this GenerateDataKeyResponse.  # noqa: E501


        :return: The ciphertext_blob of this GenerateDataKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._ciphertext_blob

    @ciphertext_blob.setter
    def ciphertext_blob(self, ciphertext_blob):
        """Sets the ciphertext_blob of this GenerateDataKeyResponse.


        :param ciphertext_blob: The ciphertext_blob of this GenerateDataKeyResponse.  # noqa: E501
        :type: str
        """

        self._ciphertext_blob = ciphertext_blob

    @property
    def plaintext(self):
        """Gets the plaintext of this GenerateDataKeyResponse.  # noqa: E501


        :return: The plaintext of this GenerateDataKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._plaintext

    @plaintext.setter
    def plaintext(self, plaintext):
        """Sets the plaintext of this GenerateDataKeyResponse.


        :param plaintext: The plaintext of this GenerateDataKeyResponse.  # noqa: E501
        :type: str
        """

        self._plaintext = plaintext

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GenerateDataKeyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GenerateDataKeyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GenerateDataKeyResponse):
            return True

        return self.to_dict() != other.to_dict()
