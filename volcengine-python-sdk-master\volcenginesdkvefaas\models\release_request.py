# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReleaseRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'function_id': 'str',
        'max_instance': 'int',
        'revision_number': 'int',
        'rolling_step': 'int',
        'target_traffic_weight': 'int'
    }

    attribute_map = {
        'description': 'Description',
        'function_id': 'FunctionId',
        'max_instance': 'MaxInstance',
        'revision_number': 'RevisionNumber',
        'rolling_step': 'RollingStep',
        'target_traffic_weight': 'TargetTrafficWeight'
    }

    def __init__(self, description=None, function_id=None, max_instance=None, revision_number=None, rolling_step=None, target_traffic_weight=None, _configuration=None):  # noqa: E501
        """ReleaseRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._function_id = None
        self._max_instance = None
        self._revision_number = None
        self._rolling_step = None
        self._target_traffic_weight = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.function_id = function_id
        if max_instance is not None:
            self.max_instance = max_instance
        self.revision_number = revision_number
        if rolling_step is not None:
            self.rolling_step = rolling_step
        if target_traffic_weight is not None:
            self.target_traffic_weight = target_traffic_weight

    @property
    def description(self):
        """Gets the description of this ReleaseRequest.  # noqa: E501


        :return: The description of this ReleaseRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ReleaseRequest.


        :param description: The description of this ReleaseRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def function_id(self):
        """Gets the function_id of this ReleaseRequest.  # noqa: E501


        :return: The function_id of this ReleaseRequest.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this ReleaseRequest.


        :param function_id: The function_id of this ReleaseRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and function_id is None:
            raise ValueError("Invalid value for `function_id`, must not be `None`")  # noqa: E501

        self._function_id = function_id

    @property
    def max_instance(self):
        """Gets the max_instance of this ReleaseRequest.  # noqa: E501


        :return: The max_instance of this ReleaseRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_instance

    @max_instance.setter
    def max_instance(self, max_instance):
        """Sets the max_instance of this ReleaseRequest.


        :param max_instance: The max_instance of this ReleaseRequest.  # noqa: E501
        :type: int
        """

        self._max_instance = max_instance

    @property
    def revision_number(self):
        """Gets the revision_number of this ReleaseRequest.  # noqa: E501


        :return: The revision_number of this ReleaseRequest.  # noqa: E501
        :rtype: int
        """
        return self._revision_number

    @revision_number.setter
    def revision_number(self, revision_number):
        """Sets the revision_number of this ReleaseRequest.


        :param revision_number: The revision_number of this ReleaseRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and revision_number is None:
            raise ValueError("Invalid value for `revision_number`, must not be `None`")  # noqa: E501

        self._revision_number = revision_number

    @property
    def rolling_step(self):
        """Gets the rolling_step of this ReleaseRequest.  # noqa: E501


        :return: The rolling_step of this ReleaseRequest.  # noqa: E501
        :rtype: int
        """
        return self._rolling_step

    @rolling_step.setter
    def rolling_step(self, rolling_step):
        """Sets the rolling_step of this ReleaseRequest.


        :param rolling_step: The rolling_step of this ReleaseRequest.  # noqa: E501
        :type: int
        """

        self._rolling_step = rolling_step

    @property
    def target_traffic_weight(self):
        """Gets the target_traffic_weight of this ReleaseRequest.  # noqa: E501


        :return: The target_traffic_weight of this ReleaseRequest.  # noqa: E501
        :rtype: int
        """
        return self._target_traffic_weight

    @target_traffic_weight.setter
    def target_traffic_weight(self, target_traffic_weight):
        """Sets the target_traffic_weight of this ReleaseRequest.


        :param target_traffic_weight: The target_traffic_weight of this ReleaseRequest.  # noqa: E501
        :type: int
        """

        self._target_traffic_weight = target_traffic_weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReleaseRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReleaseRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReleaseRequest):
            return True

        return self.to_dict() != other.to_dict()
