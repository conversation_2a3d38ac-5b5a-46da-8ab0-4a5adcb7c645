# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubscribeRuleForListAlertStrategiesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contact_groups': 'list[ContactGroupForListAlertStrategiesOutput]',
        'contact_robots': 'list[ContactRobotForListAlertStrategiesOutput]',
        'notify_config': 'NotifyConfigForListAlertStrategiesOutput'
    }

    attribute_map = {
        'contact_groups': 'ContactGroups',
        'contact_robots': 'ContactRobots',
        'notify_config': 'NotifyConfig'
    }

    def __init__(self, contact_groups=None, contact_robots=None, notify_config=None, _configuration=None):  # noqa: E501
        """SubscribeRuleForListAlertStrategiesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._contact_groups = None
        self._contact_robots = None
        self._notify_config = None
        self.discriminator = None

        if contact_groups is not None:
            self.contact_groups = contact_groups
        if contact_robots is not None:
            self.contact_robots = contact_robots
        if notify_config is not None:
            self.notify_config = notify_config

    @property
    def contact_groups(self):
        """Gets the contact_groups of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The contact_groups of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: list[ContactGroupForListAlertStrategiesOutput]
        """
        return self._contact_groups

    @contact_groups.setter
    def contact_groups(self, contact_groups):
        """Sets the contact_groups of this SubscribeRuleForListAlertStrategiesOutput.


        :param contact_groups: The contact_groups of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: list[ContactGroupForListAlertStrategiesOutput]
        """

        self._contact_groups = contact_groups

    @property
    def contact_robots(self):
        """Gets the contact_robots of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The contact_robots of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: list[ContactRobotForListAlertStrategiesOutput]
        """
        return self._contact_robots

    @contact_robots.setter
    def contact_robots(self, contact_robots):
        """Sets the contact_robots of this SubscribeRuleForListAlertStrategiesOutput.


        :param contact_robots: The contact_robots of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: list[ContactRobotForListAlertStrategiesOutput]
        """

        self._contact_robots = contact_robots

    @property
    def notify_config(self):
        """Gets the notify_config of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The notify_config of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: NotifyConfigForListAlertStrategiesOutput
        """
        return self._notify_config

    @notify_config.setter
    def notify_config(self, notify_config):
        """Sets the notify_config of this SubscribeRuleForListAlertStrategiesOutput.


        :param notify_config: The notify_config of this SubscribeRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: NotifyConfigForListAlertStrategiesOutput
        """

        self._notify_config = notify_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubscribeRuleForListAlertStrategiesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubscribeRuleForListAlertStrategiesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubscribeRuleForListAlertStrategiesOutput):
            return True

        return self.to_dict() != other.to_dict()
