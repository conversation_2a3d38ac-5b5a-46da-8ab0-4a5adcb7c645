# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateDataFlowRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'evict_policy': 'EvictPolicyForUpdateDataFlowInput',
        'export_policy': 'ExportPolicyForUpdateDataFlowInput',
        'id': 'str',
        'import_policy': 'ImportPolicyForUpdateDataFlowInput',
        'name': 'str',
        'same_name_file_policy': 'str'
    }

    attribute_map = {
        'evict_policy': 'EvictPolicy',
        'export_policy': 'ExportPolicy',
        'id': 'Id',
        'import_policy': 'ImportPolicy',
        'name': 'Name',
        'same_name_file_policy': 'SameNameFilePolicy'
    }

    def __init__(self, evict_policy=None, export_policy=None, id=None, import_policy=None, name=None, same_name_file_policy=None, _configuration=None):  # noqa: E501
        """UpdateDataFlowRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._evict_policy = None
        self._export_policy = None
        self._id = None
        self._import_policy = None
        self._name = None
        self._same_name_file_policy = None
        self.discriminator = None

        if evict_policy is not None:
            self.evict_policy = evict_policy
        if export_policy is not None:
            self.export_policy = export_policy
        self.id = id
        if import_policy is not None:
            self.import_policy = import_policy
        if name is not None:
            self.name = name
        if same_name_file_policy is not None:
            self.same_name_file_policy = same_name_file_policy

    @property
    def evict_policy(self):
        """Gets the evict_policy of this UpdateDataFlowRequest.  # noqa: E501


        :return: The evict_policy of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: EvictPolicyForUpdateDataFlowInput
        """
        return self._evict_policy

    @evict_policy.setter
    def evict_policy(self, evict_policy):
        """Sets the evict_policy of this UpdateDataFlowRequest.


        :param evict_policy: The evict_policy of this UpdateDataFlowRequest.  # noqa: E501
        :type: EvictPolicyForUpdateDataFlowInput
        """

        self._evict_policy = evict_policy

    @property
    def export_policy(self):
        """Gets the export_policy of this UpdateDataFlowRequest.  # noqa: E501


        :return: The export_policy of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: ExportPolicyForUpdateDataFlowInput
        """
        return self._export_policy

    @export_policy.setter
    def export_policy(self, export_policy):
        """Sets the export_policy of this UpdateDataFlowRequest.


        :param export_policy: The export_policy of this UpdateDataFlowRequest.  # noqa: E501
        :type: ExportPolicyForUpdateDataFlowInput
        """

        self._export_policy = export_policy

    @property
    def id(self):
        """Gets the id of this UpdateDataFlowRequest.  # noqa: E501


        :return: The id of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateDataFlowRequest.


        :param id: The id of this UpdateDataFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def import_policy(self):
        """Gets the import_policy of this UpdateDataFlowRequest.  # noqa: E501


        :return: The import_policy of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: ImportPolicyForUpdateDataFlowInput
        """
        return self._import_policy

    @import_policy.setter
    def import_policy(self, import_policy):
        """Sets the import_policy of this UpdateDataFlowRequest.


        :param import_policy: The import_policy of this UpdateDataFlowRequest.  # noqa: E501
        :type: ImportPolicyForUpdateDataFlowInput
        """

        self._import_policy = import_policy

    @property
    def name(self):
        """Gets the name of this UpdateDataFlowRequest.  # noqa: E501


        :return: The name of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateDataFlowRequest.


        :param name: The name of this UpdateDataFlowRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def same_name_file_policy(self):
        """Gets the same_name_file_policy of this UpdateDataFlowRequest.  # noqa: E501


        :return: The same_name_file_policy of this UpdateDataFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._same_name_file_policy

    @same_name_file_policy.setter
    def same_name_file_policy(self, same_name_file_policy):
        """Sets the same_name_file_policy of this UpdateDataFlowRequest.


        :param same_name_file_policy: The same_name_file_policy of this UpdateDataFlowRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Skip", "KeepLatest", "OverWrite"]  # noqa: E501
        if (self._configuration.client_side_validation and
                same_name_file_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `same_name_file_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(same_name_file_policy, allowed_values)
            )

        self._same_name_file_policy = same_name_file_policy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateDataFlowRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateDataFlowRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateDataFlowRequest):
            return True

        return self.to_dict() != other.to_dict()
