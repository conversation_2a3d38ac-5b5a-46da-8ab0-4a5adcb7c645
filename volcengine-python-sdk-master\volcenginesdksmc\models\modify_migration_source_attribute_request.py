# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyMigrationSourceAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'migration_source_description': 'str',
        'source_id': 'str',
        'source_name': 'str'
    }

    attribute_map = {
        'migration_source_description': 'MigrationSourceDescription',
        'source_id': 'SourceId',
        'source_name': 'SourceName'
    }

    def __init__(self, migration_source_description=None, source_id=None, source_name=None, _configuration=None):  # noqa: E501
        """ModifyMigrationSourceAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._migration_source_description = None
        self._source_id = None
        self._source_name = None
        self.discriminator = None

        if migration_source_description is not None:
            self.migration_source_description = migration_source_description
        self.source_id = source_id
        if source_name is not None:
            self.source_name = source_name

    @property
    def migration_source_description(self):
        """Gets the migration_source_description of this ModifyMigrationSourceAttributeRequest.  # noqa: E501


        :return: The migration_source_description of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_source_description

    @migration_source_description.setter
    def migration_source_description(self, migration_source_description):
        """Sets the migration_source_description of this ModifyMigrationSourceAttributeRequest.


        :param migration_source_description: The migration_source_description of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :type: str
        """

        self._migration_source_description = migration_source_description

    @property
    def source_id(self):
        """Gets the source_id of this ModifyMigrationSourceAttributeRequest.  # noqa: E501


        :return: The source_id of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_id

    @source_id.setter
    def source_id(self, source_id):
        """Sets the source_id of this ModifyMigrationSourceAttributeRequest.


        :param source_id: The source_id of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_id is None:
            raise ValueError("Invalid value for `source_id`, must not be `None`")  # noqa: E501

        self._source_id = source_id

    @property
    def source_name(self):
        """Gets the source_name of this ModifyMigrationSourceAttributeRequest.  # noqa: E501


        :return: The source_name of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_name

    @source_name.setter
    def source_name(self, source_name):
        """Sets the source_name of this ModifyMigrationSourceAttributeRequest.


        :param source_name: The source_name of this ModifyMigrationSourceAttributeRequest.  # noqa: E501
        :type: str
        """

        self._source_name = source_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyMigrationSourceAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyMigrationSourceAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyMigrationSourceAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
