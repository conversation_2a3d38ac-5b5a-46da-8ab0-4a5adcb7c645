# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceSpecRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'modify_type': 'str',
        'node_info': 'list[NodeInfoForModifyDBInstanceSpecInput]',
        'specified_switch_end_time': 'str',
        'specified_switch_start_time': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'switch_type': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'modify_type': 'ModifyType',
        'node_info': 'NodeInfo',
        'specified_switch_end_time': 'SpecifiedSwitchEndTime',
        'specified_switch_start_time': 'SpecifiedSwitchStartTime',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'switch_type': 'SwitchType'
    }

    def __init__(self, instance_id=None, modify_type=None, node_info=None, specified_switch_end_time=None, specified_switch_start_time=None, storage_space=None, storage_type=None, switch_type=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceSpecRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._modify_type = None
        self._node_info = None
        self._specified_switch_end_time = None
        self._specified_switch_start_time = None
        self._storage_space = None
        self._storage_type = None
        self._switch_type = None
        self.discriminator = None

        self.instance_id = instance_id
        if modify_type is not None:
            self.modify_type = modify_type
        if node_info is not None:
            self.node_info = node_info
        if specified_switch_end_time is not None:
            self.specified_switch_end_time = specified_switch_end_time
        if specified_switch_start_time is not None:
            self.specified_switch_start_time = specified_switch_start_time
        if storage_space is not None:
            self.storage_space = storage_space
        self.storage_type = storage_type
        if switch_type is not None:
            self.switch_type = switch_type

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceSpecRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def modify_type(self):
        """Gets the modify_type of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The modify_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._modify_type

    @modify_type.setter
    def modify_type(self, modify_type):
        """Sets the modify_type of this ModifyDBInstanceSpecRequest.


        :param modify_type: The modify_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._modify_type = modify_type

    @property
    def node_info(self):
        """Gets the node_info of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The node_info of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: list[NodeInfoForModifyDBInstanceSpecInput]
        """
        return self._node_info

    @node_info.setter
    def node_info(self, node_info):
        """Sets the node_info of this ModifyDBInstanceSpecRequest.


        :param node_info: The node_info of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: list[NodeInfoForModifyDBInstanceSpecInput]
        """

        self._node_info = node_info

    @property
    def specified_switch_end_time(self):
        """Gets the specified_switch_end_time of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The specified_switch_end_time of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_end_time

    @specified_switch_end_time.setter
    def specified_switch_end_time(self, specified_switch_end_time):
        """Sets the specified_switch_end_time of this ModifyDBInstanceSpecRequest.


        :param specified_switch_end_time: The specified_switch_end_time of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_end_time = specified_switch_end_time

    @property
    def specified_switch_start_time(self):
        """Gets the specified_switch_start_time of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The specified_switch_start_time of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_start_time

    @specified_switch_start_time.setter
    def specified_switch_start_time(self, specified_switch_start_time):
        """Sets the specified_switch_start_time of this ModifyDBInstanceSpecRequest.


        :param specified_switch_start_time: The specified_switch_start_time of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_start_time = specified_switch_start_time

    @property
    def storage_space(self):
        """Gets the storage_space of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The storage_space of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this ModifyDBInstanceSpecRequest.


        :param storage_space: The storage_space of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The storage_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this ModifyDBInstanceSpecRequest.


        :param storage_type: The storage_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and storage_type is None:
            raise ValueError("Invalid value for `storage_type`, must not be `None`")  # noqa: E501

        self._storage_type = storage_type

    @property
    def switch_type(self):
        """Gets the switch_type of this ModifyDBInstanceSpecRequest.  # noqa: E501


        :return: The switch_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_type

    @switch_type.setter
    def switch_type(self, switch_type):
        """Sets the switch_type of this ModifyDBInstanceSpecRequest.


        :param switch_type: The switch_type of this ModifyDBInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._switch_type = switch_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceSpecRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceSpecRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceSpecRequest):
            return True

        return self.to_dict() != other.to_dict()
