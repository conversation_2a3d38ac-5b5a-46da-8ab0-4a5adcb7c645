# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAvailableCrossRegionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'target_region_ids': 'list[str]'
    }

    attribute_map = {
        'target_region_ids': 'TargetRegionIds'
    }

    def __init__(self, target_region_ids=None, _configuration=None):  # noqa: E501
        """DescribeAvailableCrossRegionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._target_region_ids = None
        self.discriminator = None

        if target_region_ids is not None:
            self.target_region_ids = target_region_ids

    @property
    def target_region_ids(self):
        """Gets the target_region_ids of this DescribeAvailableCrossRegionResponse.  # noqa: E501


        :return: The target_region_ids of this DescribeAvailableCrossRegionResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._target_region_ids

    @target_region_ids.setter
    def target_region_ids(self, target_region_ids):
        """Sets the target_region_ids of this DescribeAvailableCrossRegionResponse.


        :param target_region_ids: The target_region_ids of this DescribeAvailableCrossRegionResponse.  # noqa: E501
        :type: list[str]
        """

        self._target_region_ids = target_region_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAvailableCrossRegionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAvailableCrossRegionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAvailableCrossRegionResponse):
            return True

        return self.to_dict() != other.to_dict()
