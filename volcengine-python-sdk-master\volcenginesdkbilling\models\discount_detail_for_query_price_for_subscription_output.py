# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiscountDetailForQueryPriceForSubscriptionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'discount_price': 'str',
        'discount_type': 'str'
    }

    attribute_map = {
        'discount_price': 'DiscountPrice',
        'discount_type': 'DiscountType'
    }

    def __init__(self, discount_price=None, discount_type=None, _configuration=None):  # noqa: E501
        """DiscountDetailForQueryPriceForSubscriptionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._discount_price = None
        self._discount_type = None
        self.discriminator = None

        if discount_price is not None:
            self.discount_price = discount_price
        if discount_type is not None:
            self.discount_type = discount_type

    @property
    def discount_price(self):
        """Gets the discount_price of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501


        :return: The discount_price of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_price

    @discount_price.setter
    def discount_price(self, discount_price):
        """Sets the discount_price of this DiscountDetailForQueryPriceForSubscriptionOutput.


        :param discount_price: The discount_price of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501
        :type: str
        """

        self._discount_price = discount_price

    @property
    def discount_type(self):
        """Gets the discount_type of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501


        :return: The discount_type of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_type

    @discount_type.setter
    def discount_type(self, discount_type):
        """Sets the discount_type of this DiscountDetailForQueryPriceForSubscriptionOutput.


        :param discount_type: The discount_type of this DiscountDetailForQueryPriceForSubscriptionOutput.  # noqa: E501
        :type: str
        """

        self._discount_type = discount_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiscountDetailForQueryPriceForSubscriptionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiscountDetailForQueryPriceForSubscriptionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiscountDetailForQueryPriceForSubscriptionOutput):
            return True

        return self.to_dict() != other.to_dict()
