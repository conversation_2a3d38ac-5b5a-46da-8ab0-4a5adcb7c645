# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListCloudEnvsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'cloud_platform': 'str',
        'comment': 'str',
        'error_msg': 'str',
        'id': 'str',
        'key_type': 'str',
        'status': 'int',
        'sync_period': 'int',
        'sync_status': 'int'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'cloud_platform': 'CloudPlatform',
        'comment': 'Comment',
        'error_msg': 'ErrorMsg',
        'id': 'ID',
        'key_type': 'KeyType',
        'status': 'Status',
        'sync_period': 'SyncPeriod',
        'sync_status': 'SyncStatus'
    }

    def __init__(self, access_key=None, cloud_platform=None, comment=None, error_msg=None, id=None, key_type=None, status=None, sync_period=None, sync_status=None, _configuration=None):  # noqa: E501
        """DataForListCloudEnvsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._cloud_platform = None
        self._comment = None
        self._error_msg = None
        self._id = None
        self._key_type = None
        self._status = None
        self._sync_period = None
        self._sync_status = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if cloud_platform is not None:
            self.cloud_platform = cloud_platform
        if comment is not None:
            self.comment = comment
        if error_msg is not None:
            self.error_msg = error_msg
        if id is not None:
            self.id = id
        if key_type is not None:
            self.key_type = key_type
        if status is not None:
            self.status = status
        if sync_period is not None:
            self.sync_period = sync_period
        if sync_status is not None:
            self.sync_status = sync_status

    @property
    def access_key(self):
        """Gets the access_key of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The access_key of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this DataForListCloudEnvsOutput.


        :param access_key: The access_key of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def cloud_platform(self):
        """Gets the cloud_platform of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The cloud_platform of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_platform

    @cloud_platform.setter
    def cloud_platform(self, cloud_platform):
        """Sets the cloud_platform of this DataForListCloudEnvsOutput.


        :param cloud_platform: The cloud_platform of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._cloud_platform = cloud_platform

    @property
    def comment(self):
        """Gets the comment of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The comment of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._comment

    @comment.setter
    def comment(self, comment):
        """Sets the comment of this DataForListCloudEnvsOutput.


        :param comment: The comment of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._comment = comment

    @property
    def error_msg(self):
        """Gets the error_msg of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The error_msg of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_msg

    @error_msg.setter
    def error_msg(self, error_msg):
        """Sets the error_msg of this DataForListCloudEnvsOutput.


        :param error_msg: The error_msg of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._error_msg = error_msg

    @property
    def id(self):
        """Gets the id of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The id of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListCloudEnvsOutput.


        :param id: The id of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def key_type(self):
        """Gets the key_type of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The key_type of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: str
        """
        return self._key_type

    @key_type.setter
    def key_type(self, key_type):
        """Sets the key_type of this DataForListCloudEnvsOutput.


        :param key_type: The key_type of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: str
        """

        self._key_type = key_type

    @property
    def status(self):
        """Gets the status of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The status of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListCloudEnvsOutput.


        :param status: The status of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def sync_period(self):
        """Gets the sync_period of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The sync_period of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: int
        """
        return self._sync_period

    @sync_period.setter
    def sync_period(self, sync_period):
        """Sets the sync_period of this DataForListCloudEnvsOutput.


        :param sync_period: The sync_period of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: int
        """

        self._sync_period = sync_period

    @property
    def sync_status(self):
        """Gets the sync_status of this DataForListCloudEnvsOutput.  # noqa: E501


        :return: The sync_status of this DataForListCloudEnvsOutput.  # noqa: E501
        :rtype: int
        """
        return self._sync_status

    @sync_status.setter
    def sync_status(self, sync_status):
        """Sets the sync_status of this DataForListCloudEnvsOutput.


        :param sync_status: The sync_status of this DataForListCloudEnvsOutput.  # noqa: E501
        :type: int
        """

        self._sync_status = sync_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListCloudEnvsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListCloudEnvsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListCloudEnvsOutput):
            return True

        return self.to_dict() != other.to_dict()
