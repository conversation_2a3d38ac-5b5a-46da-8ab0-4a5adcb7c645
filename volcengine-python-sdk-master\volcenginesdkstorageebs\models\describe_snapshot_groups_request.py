# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSnapshotGroupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'snapshot_group_ids': 'list[str]',
        'status': 'list[str]'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'snapshot_group_ids': 'SnapshotGroupIds',
        'status': 'Status'
    }

    def __init__(self, instance_id=None, name=None, page_number=None, page_size=None, project_name=None, snapshot_group_ids=None, status=None, _configuration=None):  # noqa: E501
        """DescribeSnapshotGroupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._snapshot_group_ids = None
        self._status = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if name is not None:
            self.name = name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if snapshot_group_ids is not None:
            self.snapshot_group_ids = snapshot_group_ids
        if status is not None:
            self.status = status

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The instance_id of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeSnapshotGroupsRequest.


        :param instance_id: The instance_id of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def name(self):
        """Gets the name of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The name of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeSnapshotGroupsRequest.


        :param name: The name of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The page_number of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSnapshotGroupsRequest.


        :param page_number: The page_number of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The page_size of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSnapshotGroupsRequest.


        :param page_size: The page_size of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The project_name of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeSnapshotGroupsRequest.


        :param project_name: The project_name of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def snapshot_group_ids(self):
        """Gets the snapshot_group_ids of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The snapshot_group_ids of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._snapshot_group_ids

    @snapshot_group_ids.setter
    def snapshot_group_ids(self, snapshot_group_ids):
        """Sets the snapshot_group_ids of this DescribeSnapshotGroupsRequest.


        :param snapshot_group_ids: The snapshot_group_ids of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: list[str]
        """

        self._snapshot_group_ids = snapshot_group_ids

    @property
    def status(self):
        """Gets the status of this DescribeSnapshotGroupsRequest.  # noqa: E501


        :return: The status of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeSnapshotGroupsRequest.


        :param status: The status of this DescribeSnapshotGroupsRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSnapshotGroupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSnapshotGroupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSnapshotGroupsRequest):
            return True

        return self.to_dict() != other.to_dict()
