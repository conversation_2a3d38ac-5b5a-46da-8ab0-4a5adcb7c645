# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ScalingActivityForDescribeScalingActivitiesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_type': 'str',
        'actual_adjust_instance_number': 'int',
        'cooldown': 'int',
        'created_at': 'str',
        'current_instance_number': 'int',
        'expected_run_time': 'str',
        'max_instance_number': 'int',
        'min_instance_number': 'int',
        'related_instances': 'list[RelatedInstanceForDescribeScalingActivitiesOutput]',
        'result_msg': 'str',
        'scaling_activity_id': 'str',
        'scaling_group_id': 'str',
        'status_code': 'str',
        'stopped_at': 'str',
        'task_category': 'str'
    }

    attribute_map = {
        'activity_type': 'ActivityType',
        'actual_adjust_instance_number': 'ActualAdjustInstanceNumber',
        'cooldown': 'Cooldown',
        'created_at': 'CreatedAt',
        'current_instance_number': 'CurrentInstanceNumber',
        'expected_run_time': 'ExpectedRunTime',
        'max_instance_number': 'MaxInstanceNumber',
        'min_instance_number': 'MinInstanceNumber',
        'related_instances': 'RelatedInstances',
        'result_msg': 'ResultMsg',
        'scaling_activity_id': 'ScalingActivityId',
        'scaling_group_id': 'ScalingGroupId',
        'status_code': 'StatusCode',
        'stopped_at': 'StoppedAt',
        'task_category': 'TaskCategory'
    }

    def __init__(self, activity_type=None, actual_adjust_instance_number=None, cooldown=None, created_at=None, current_instance_number=None, expected_run_time=None, max_instance_number=None, min_instance_number=None, related_instances=None, result_msg=None, scaling_activity_id=None, scaling_group_id=None, status_code=None, stopped_at=None, task_category=None, _configuration=None):  # noqa: E501
        """ScalingActivityForDescribeScalingActivitiesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_type = None
        self._actual_adjust_instance_number = None
        self._cooldown = None
        self._created_at = None
        self._current_instance_number = None
        self._expected_run_time = None
        self._max_instance_number = None
        self._min_instance_number = None
        self._related_instances = None
        self._result_msg = None
        self._scaling_activity_id = None
        self._scaling_group_id = None
        self._status_code = None
        self._stopped_at = None
        self._task_category = None
        self.discriminator = None

        if activity_type is not None:
            self.activity_type = activity_type
        if actual_adjust_instance_number is not None:
            self.actual_adjust_instance_number = actual_adjust_instance_number
        if cooldown is not None:
            self.cooldown = cooldown
        if created_at is not None:
            self.created_at = created_at
        if current_instance_number is not None:
            self.current_instance_number = current_instance_number
        if expected_run_time is not None:
            self.expected_run_time = expected_run_time
        if max_instance_number is not None:
            self.max_instance_number = max_instance_number
        if min_instance_number is not None:
            self.min_instance_number = min_instance_number
        if related_instances is not None:
            self.related_instances = related_instances
        if result_msg is not None:
            self.result_msg = result_msg
        if scaling_activity_id is not None:
            self.scaling_activity_id = scaling_activity_id
        if scaling_group_id is not None:
            self.scaling_group_id = scaling_group_id
        if status_code is not None:
            self.status_code = status_code
        if stopped_at is not None:
            self.stopped_at = stopped_at
        if task_category is not None:
            self.task_category = task_category

    @property
    def activity_type(self):
        """Gets the activity_type of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The activity_type of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._activity_type

    @activity_type.setter
    def activity_type(self, activity_type):
        """Sets the activity_type of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param activity_type: The activity_type of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._activity_type = activity_type

    @property
    def actual_adjust_instance_number(self):
        """Gets the actual_adjust_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The actual_adjust_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._actual_adjust_instance_number

    @actual_adjust_instance_number.setter
    def actual_adjust_instance_number(self, actual_adjust_instance_number):
        """Sets the actual_adjust_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param actual_adjust_instance_number: The actual_adjust_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: int
        """

        self._actual_adjust_instance_number = actual_adjust_instance_number

    @property
    def cooldown(self):
        """Gets the cooldown of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The cooldown of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._cooldown

    @cooldown.setter
    def cooldown(self, cooldown):
        """Sets the cooldown of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param cooldown: The cooldown of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: int
        """

        self._cooldown = cooldown

    @property
    def created_at(self):
        """Gets the created_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The created_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param created_at: The created_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def current_instance_number(self):
        """Gets the current_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The current_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._current_instance_number

    @current_instance_number.setter
    def current_instance_number(self, current_instance_number):
        """Sets the current_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param current_instance_number: The current_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: int
        """

        self._current_instance_number = current_instance_number

    @property
    def expected_run_time(self):
        """Gets the expected_run_time of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The expected_run_time of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expected_run_time

    @expected_run_time.setter
    def expected_run_time(self, expected_run_time):
        """Sets the expected_run_time of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param expected_run_time: The expected_run_time of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._expected_run_time = expected_run_time

    @property
    def max_instance_number(self):
        """Gets the max_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The max_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_instance_number

    @max_instance_number.setter
    def max_instance_number(self, max_instance_number):
        """Sets the max_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param max_instance_number: The max_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: int
        """

        self._max_instance_number = max_instance_number

    @property
    def min_instance_number(self):
        """Gets the min_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The min_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._min_instance_number

    @min_instance_number.setter
    def min_instance_number(self, min_instance_number):
        """Sets the min_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param min_instance_number: The min_instance_number of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: int
        """

        self._min_instance_number = min_instance_number

    @property
    def related_instances(self):
        """Gets the related_instances of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The related_instances of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: list[RelatedInstanceForDescribeScalingActivitiesOutput]
        """
        return self._related_instances

    @related_instances.setter
    def related_instances(self, related_instances):
        """Sets the related_instances of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param related_instances: The related_instances of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: list[RelatedInstanceForDescribeScalingActivitiesOutput]
        """

        self._related_instances = related_instances

    @property
    def result_msg(self):
        """Gets the result_msg of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The result_msg of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._result_msg

    @result_msg.setter
    def result_msg(self, result_msg):
        """Sets the result_msg of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param result_msg: The result_msg of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._result_msg = result_msg

    @property
    def scaling_activity_id(self):
        """Gets the scaling_activity_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The scaling_activity_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scaling_activity_id

    @scaling_activity_id.setter
    def scaling_activity_id(self, scaling_activity_id):
        """Sets the scaling_activity_id of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param scaling_activity_id: The scaling_activity_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._scaling_activity_id = scaling_activity_id

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The scaling_group_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param scaling_group_id: The scaling_group_id of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._scaling_group_id = scaling_group_id

    @property
    def status_code(self):
        """Gets the status_code of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The status_code of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param status_code: The status_code of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._status_code = status_code

    @property
    def stopped_at(self):
        """Gets the stopped_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The stopped_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._stopped_at

    @stopped_at.setter
    def stopped_at(self, stopped_at):
        """Sets the stopped_at of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param stopped_at: The stopped_at of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._stopped_at = stopped_at

    @property
    def task_category(self):
        """Gets the task_category of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501


        :return: The task_category of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_category

    @task_category.setter
    def task_category(self, task_category):
        """Sets the task_category of this ScalingActivityForDescribeScalingActivitiesOutput.


        :param task_category: The task_category of this ScalingActivityForDescribeScalingActivitiesOutput.  # noqa: E501
        :type: str
        """

        self._task_category = task_category

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ScalingActivityForDescribeScalingActivitiesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ScalingActivityForDescribeScalingActivitiesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ScalingActivityForDescribeScalingActivitiesOutput):
            return True

        return self.to_dict() != other.to_dict()
