# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListAlertSamplesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_id': 'str',
        'level': 'str',
        'phase': 'str',
        'timestamp': 'int',
        'value': 'float'
    }

    attribute_map = {
        'alert_id': 'AlertId',
        'level': 'Level',
        'phase': 'Phase',
        'timestamp': 'Timestamp',
        'value': 'Value'
    }

    def __init__(self, alert_id=None, level=None, phase=None, timestamp=None, value=None, _configuration=None):  # noqa: E501
        """ItemForListAlertSamplesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_id = None
        self._level = None
        self._phase = None
        self._timestamp = None
        self._value = None
        self.discriminator = None

        if alert_id is not None:
            self.alert_id = alert_id
        if level is not None:
            self.level = level
        if phase is not None:
            self.phase = phase
        if timestamp is not None:
            self.timestamp = timestamp
        if value is not None:
            self.value = value

    @property
    def alert_id(self):
        """Gets the alert_id of this ItemForListAlertSamplesOutput.  # noqa: E501


        :return: The alert_id of this ItemForListAlertSamplesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_id

    @alert_id.setter
    def alert_id(self, alert_id):
        """Sets the alert_id of this ItemForListAlertSamplesOutput.


        :param alert_id: The alert_id of this ItemForListAlertSamplesOutput.  # noqa: E501
        :type: str
        """

        self._alert_id = alert_id

    @property
    def level(self):
        """Gets the level of this ItemForListAlertSamplesOutput.  # noqa: E501


        :return: The level of this ItemForListAlertSamplesOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ItemForListAlertSamplesOutput.


        :param level: The level of this ItemForListAlertSamplesOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def phase(self):
        """Gets the phase of this ItemForListAlertSamplesOutput.  # noqa: E501


        :return: The phase of this ItemForListAlertSamplesOutput.  # noqa: E501
        :rtype: str
        """
        return self._phase

    @phase.setter
    def phase(self, phase):
        """Sets the phase of this ItemForListAlertSamplesOutput.


        :param phase: The phase of this ItemForListAlertSamplesOutput.  # noqa: E501
        :type: str
        """

        self._phase = phase

    @property
    def timestamp(self):
        """Gets the timestamp of this ItemForListAlertSamplesOutput.  # noqa: E501


        :return: The timestamp of this ItemForListAlertSamplesOutput.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this ItemForListAlertSamplesOutput.


        :param timestamp: The timestamp of this ItemForListAlertSamplesOutput.  # noqa: E501
        :type: int
        """

        self._timestamp = timestamp

    @property
    def value(self):
        """Gets the value of this ItemForListAlertSamplesOutput.  # noqa: E501


        :return: The value of this ItemForListAlertSamplesOutput.  # noqa: E501
        :rtype: float
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this ItemForListAlertSamplesOutput.


        :param value: The value of this ItemForListAlertSamplesOutput.  # noqa: E501
        :type: float
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListAlertSamplesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListAlertSamplesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListAlertSamplesOutput):
            return True

        return self.to_dict() != other.to_dict()
