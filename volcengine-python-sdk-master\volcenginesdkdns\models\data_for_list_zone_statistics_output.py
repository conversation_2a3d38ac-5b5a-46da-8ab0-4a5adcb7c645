# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListZoneStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cache_stage': 'int',
        'error': 'int',
        'name': 'str',
        'not_exists': 'int',
        'success': 'int',
        'timestamp': 'int',
        'total': 'int',
        'zid': 'int'
    }

    attribute_map = {
        'cache_stage': 'CacheStage',
        'error': 'Error',
        'name': 'Name',
        'not_exists': 'NotExists',
        'success': 'Success',
        'timestamp': 'Timestamp',
        'total': 'Total',
        'zid': 'ZID'
    }

    def __init__(self, cache_stage=None, error=None, name=None, not_exists=None, success=None, timestamp=None, total=None, zid=None, _configuration=None):  # noqa: E501
        """DataForListZoneStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cache_stage = None
        self._error = None
        self._name = None
        self._not_exists = None
        self._success = None
        self._timestamp = None
        self._total = None
        self._zid = None
        self.discriminator = None

        if cache_stage is not None:
            self.cache_stage = cache_stage
        if error is not None:
            self.error = error
        if name is not None:
            self.name = name
        if not_exists is not None:
            self.not_exists = not_exists
        if success is not None:
            self.success = success
        if timestamp is not None:
            self.timestamp = timestamp
        if total is not None:
            self.total = total
        if zid is not None:
            self.zid = zid

    @property
    def cache_stage(self):
        """Gets the cache_stage of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The cache_stage of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._cache_stage

    @cache_stage.setter
    def cache_stage(self, cache_stage):
        """Sets the cache_stage of this DataForListZoneStatisticsOutput.


        :param cache_stage: The cache_stage of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._cache_stage = cache_stage

    @property
    def error(self):
        """Gets the error of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The error of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._error

    @error.setter
    def error(self, error):
        """Sets the error of this DataForListZoneStatisticsOutput.


        :param error: The error of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._error = error

    @property
    def name(self):
        """Gets the name of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The name of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListZoneStatisticsOutput.


        :param name: The name of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def not_exists(self):
        """Gets the not_exists of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The not_exists of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._not_exists

    @not_exists.setter
    def not_exists(self, not_exists):
        """Sets the not_exists of this DataForListZoneStatisticsOutput.


        :param not_exists: The not_exists of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._not_exists = not_exists

    @property
    def success(self):
        """Gets the success of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The success of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._success

    @success.setter
    def success(self, success):
        """Sets the success of this DataForListZoneStatisticsOutput.


        :param success: The success of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._success = success

    @property
    def timestamp(self):
        """Gets the timestamp of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The timestamp of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this DataForListZoneStatisticsOutput.


        :param timestamp: The timestamp of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._timestamp = timestamp

    @property
    def total(self):
        """Gets the total of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The total of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DataForListZoneStatisticsOutput.


        :param total: The total of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._total = total

    @property
    def zid(self):
        """Gets the zid of this DataForListZoneStatisticsOutput.  # noqa: E501


        :return: The zid of this DataForListZoneStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this DataForListZoneStatisticsOutput.


        :param zid: The zid of this DataForListZoneStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListZoneStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListZoneStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListZoneStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
