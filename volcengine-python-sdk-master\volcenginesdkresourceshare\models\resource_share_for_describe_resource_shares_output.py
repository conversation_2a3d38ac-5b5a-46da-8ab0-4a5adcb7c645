# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceShareForDescribeResourceSharesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_share_type': 'str',
        'create_time': 'str',
        'owning_account_id': 'int',
        'resource_share_id': 'str',
        'resource_share_name': 'str',
        'resource_share_trn': 'str',
        'status': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'allow_share_type': 'AllowShareType',
        'create_time': 'CreateTime',
        'owning_account_id': 'OwningAccountId',
        'resource_share_id': 'ResourceShareId',
        'resource_share_name': 'ResourceShareName',
        'resource_share_trn': 'ResourceShareTrn',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, allow_share_type=None, create_time=None, owning_account_id=None, resource_share_id=None, resource_share_name=None, resource_share_trn=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """ResourceShareForDescribeResourceSharesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_share_type = None
        self._create_time = None
        self._owning_account_id = None
        self._resource_share_id = None
        self._resource_share_name = None
        self._resource_share_trn = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if allow_share_type is not None:
            self.allow_share_type = allow_share_type
        if create_time is not None:
            self.create_time = create_time
        if owning_account_id is not None:
            self.owning_account_id = owning_account_id
        if resource_share_id is not None:
            self.resource_share_id = resource_share_id
        if resource_share_name is not None:
            self.resource_share_name = resource_share_name
        if resource_share_trn is not None:
            self.resource_share_trn = resource_share_trn
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def allow_share_type(self):
        """Gets the allow_share_type of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The allow_share_type of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._allow_share_type

    @allow_share_type.setter
    def allow_share_type(self, allow_share_type):
        """Sets the allow_share_type of this ResourceShareForDescribeResourceSharesOutput.


        :param allow_share_type: The allow_share_type of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._allow_share_type = allow_share_type

    @property
    def create_time(self):
        """Gets the create_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The create_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ResourceShareForDescribeResourceSharesOutput.


        :param create_time: The create_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def owning_account_id(self):
        """Gets the owning_account_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The owning_account_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: int
        """
        return self._owning_account_id

    @owning_account_id.setter
    def owning_account_id(self, owning_account_id):
        """Sets the owning_account_id of this ResourceShareForDescribeResourceSharesOutput.


        :param owning_account_id: The owning_account_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: int
        """

        self._owning_account_id = owning_account_id

    @property
    def resource_share_id(self):
        """Gets the resource_share_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The resource_share_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_id

    @resource_share_id.setter
    def resource_share_id(self, resource_share_id):
        """Sets the resource_share_id of this ResourceShareForDescribeResourceSharesOutput.


        :param resource_share_id: The resource_share_id of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_id = resource_share_id

    @property
    def resource_share_name(self):
        """Gets the resource_share_name of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The resource_share_name of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_name

    @resource_share_name.setter
    def resource_share_name(self, resource_share_name):
        """Sets the resource_share_name of this ResourceShareForDescribeResourceSharesOutput.


        :param resource_share_name: The resource_share_name of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_name = resource_share_name

    @property
    def resource_share_trn(self):
        """Gets the resource_share_trn of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The resource_share_trn of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trn

    @resource_share_trn.setter
    def resource_share_trn(self, resource_share_trn):
        """Sets the resource_share_trn of this ResourceShareForDescribeResourceSharesOutput.


        :param resource_share_trn: The resource_share_trn of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_trn = resource_share_trn

    @property
    def status(self):
        """Gets the status of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The status of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResourceShareForDescribeResourceSharesOutput.


        :param status: The status of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501


        :return: The update_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ResourceShareForDescribeResourceSharesOutput.


        :param update_time: The update_time of this ResourceShareForDescribeResourceSharesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceShareForDescribeResourceSharesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceShareForDescribeResourceSharesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceShareForDescribeResourceSharesOutput):
            return True

        return self.to_dict() != other.to_dict()
