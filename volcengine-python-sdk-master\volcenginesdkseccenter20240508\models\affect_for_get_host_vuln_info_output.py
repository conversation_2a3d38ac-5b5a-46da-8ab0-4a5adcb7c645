# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AffectForGetHostVulnInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'cmdline': 'str',
        'container_create_time': 'str',
        'container_host_name': 'str',
        'container_id': 'str',
        'container_ip': 'str',
        'container_name': 'str',
        'container_net_mode': 'str',
        'container_query_result': 'str',
        'container_run_time': 'str',
        'container_state': 'str',
        'cves': 'list[str]',
        'image_id': 'str',
        'image_name': 'str',
        'pid_list': 'list[PidListForGetHostVulnInfoOutput]',
        'pod_id': 'str',
        'pod_name': 'str',
        'software_name': 'str',
        'software_path': 'str',
        'software_source': 'str',
        'software_version': 'str',
        'state': 'str',
        'type': 'str',
        'update_time': 'int',
        'action': 'str',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'control_time': 'int',
        'create_time': 'int',
        'cve_id': 'str',
        'cwpp_id': 'str',
        'fix_version': 'str',
        'level': 'str',
        'namespace': 'str',
        'operate_reason': 'str',
        'pod_hash': 'str',
        'status': 'str',
        'tag': 'list[str]',
        'vuln_name': 'str',
        'vuln_type': 'str',
        'workload_id': 'str',
        'workload_name': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'cmdline': 'Cmdline',
        'container_create_time': 'ContainerCreateTime',
        'container_host_name': 'ContainerHostName',
        'container_id': 'ContainerID',
        'container_ip': 'ContainerIP',
        'container_name': 'ContainerName',
        'container_net_mode': 'ContainerNetMode',
        'container_query_result': 'ContainerQueryResult',
        'container_run_time': 'ContainerRunTime',
        'container_state': 'ContainerState',
        'cves': 'Cves',
        'image_id': 'ImageID',
        'image_name': 'ImageName',
        'pid_list': 'PidList',
        'pod_id': 'PodID',
        'pod_name': 'PodName',
        'software_name': 'SoftwareName',
        'software_path': 'SoftwarePath',
        'software_source': 'SoftwareSource',
        'software_version': 'SoftwareVersion',
        'state': 'State',
        'type': 'Type',
        'update_time': 'UpdateTime',
        'action': 'action',
        'cluster_id': 'cluster_id',
        'cluster_name': 'cluster_name',
        'control_time': 'control_time',
        'create_time': 'create_time',
        'cve_id': 'cve_id',
        'cwpp_id': 'cwpp_id',
        'fix_version': 'fix_version',
        'level': 'level',
        'namespace': 'namespace',
        'operate_reason': 'operate_reason',
        'pod_hash': 'pod_hash',
        'status': 'status',
        'tag': 'tag',
        'vuln_name': 'vuln_name',
        'vuln_type': 'vuln_type',
        'workload_id': 'workload_id',
        'workload_name': 'workload_name'
    }

    def __init__(self, agent_id=None, asset_id=None, cmdline=None, container_create_time=None, container_host_name=None, container_id=None, container_ip=None, container_name=None, container_net_mode=None, container_query_result=None, container_run_time=None, container_state=None, cves=None, image_id=None, image_name=None, pid_list=None, pod_id=None, pod_name=None, software_name=None, software_path=None, software_source=None, software_version=None, state=None, type=None, update_time=None, action=None, cluster_id=None, cluster_name=None, control_time=None, create_time=None, cve_id=None, cwpp_id=None, fix_version=None, level=None, namespace=None, operate_reason=None, pod_hash=None, status=None, tag=None, vuln_name=None, vuln_type=None, workload_id=None, workload_name=None, _configuration=None):  # noqa: E501
        """AffectForGetHostVulnInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._cmdline = None
        self._container_create_time = None
        self._container_host_name = None
        self._container_id = None
        self._container_ip = None
        self._container_name = None
        self._container_net_mode = None
        self._container_query_result = None
        self._container_run_time = None
        self._container_state = None
        self._cves = None
        self._image_id = None
        self._image_name = None
        self._pid_list = None
        self._pod_id = None
        self._pod_name = None
        self._software_name = None
        self._software_path = None
        self._software_source = None
        self._software_version = None
        self._state = None
        self._type = None
        self._update_time = None
        self._action = None
        self._cluster_id = None
        self._cluster_name = None
        self._control_time = None
        self._create_time = None
        self._cve_id = None
        self._cwpp_id = None
        self._fix_version = None
        self._level = None
        self._namespace = None
        self._operate_reason = None
        self._pod_hash = None
        self._status = None
        self._tag = None
        self._vuln_name = None
        self._vuln_type = None
        self._workload_id = None
        self._workload_name = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if cmdline is not None:
            self.cmdline = cmdline
        if container_create_time is not None:
            self.container_create_time = container_create_time
        if container_host_name is not None:
            self.container_host_name = container_host_name
        if container_id is not None:
            self.container_id = container_id
        if container_ip is not None:
            self.container_ip = container_ip
        if container_name is not None:
            self.container_name = container_name
        if container_net_mode is not None:
            self.container_net_mode = container_net_mode
        if container_query_result is not None:
            self.container_query_result = container_query_result
        if container_run_time is not None:
            self.container_run_time = container_run_time
        if container_state is not None:
            self.container_state = container_state
        if cves is not None:
            self.cves = cves
        if image_id is not None:
            self.image_id = image_id
        if image_name is not None:
            self.image_name = image_name
        if pid_list is not None:
            self.pid_list = pid_list
        if pod_id is not None:
            self.pod_id = pod_id
        if pod_name is not None:
            self.pod_name = pod_name
        if software_name is not None:
            self.software_name = software_name
        if software_path is not None:
            self.software_path = software_path
        if software_source is not None:
            self.software_source = software_source
        if software_version is not None:
            self.software_version = software_version
        if state is not None:
            self.state = state
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time
        if action is not None:
            self.action = action
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if control_time is not None:
            self.control_time = control_time
        if create_time is not None:
            self.create_time = create_time
        if cve_id is not None:
            self.cve_id = cve_id
        if cwpp_id is not None:
            self.cwpp_id = cwpp_id
        if fix_version is not None:
            self.fix_version = fix_version
        if level is not None:
            self.level = level
        if namespace is not None:
            self.namespace = namespace
        if operate_reason is not None:
            self.operate_reason = operate_reason
        if pod_hash is not None:
            self.pod_hash = pod_hash
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if vuln_name is not None:
            self.vuln_name = vuln_name
        if vuln_type is not None:
            self.vuln_type = vuln_type
        if workload_id is not None:
            self.workload_id = workload_id
        if workload_name is not None:
            self.workload_name = workload_name

    @property
    def agent_id(self):
        """Gets the agent_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The agent_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this AffectForGetHostVulnInfoOutput.


        :param agent_id: The agent_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The asset_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this AffectForGetHostVulnInfoOutput.


        :param asset_id: The asset_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def cmdline(self):
        """Gets the cmdline of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cmdline of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this AffectForGetHostVulnInfoOutput.


        :param cmdline: The cmdline of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def container_create_time(self):
        """Gets the container_create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_create_time

    @container_create_time.setter
    def container_create_time(self, container_create_time):
        """Sets the container_create_time of this AffectForGetHostVulnInfoOutput.


        :param container_create_time: The container_create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_create_time = container_create_time

    @property
    def container_host_name(self):
        """Gets the container_host_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_host_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_host_name

    @container_host_name.setter
    def container_host_name(self, container_host_name):
        """Sets the container_host_name of this AffectForGetHostVulnInfoOutput.


        :param container_host_name: The container_host_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_host_name = container_host_name

    @property
    def container_id(self):
        """Gets the container_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this AffectForGetHostVulnInfoOutput.


        :param container_id: The container_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def container_ip(self):
        """Gets the container_ip of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_ip of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_ip

    @container_ip.setter
    def container_ip(self, container_ip):
        """Sets the container_ip of this AffectForGetHostVulnInfoOutput.


        :param container_ip: The container_ip of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_ip = container_ip

    @property
    def container_name(self):
        """Gets the container_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this AffectForGetHostVulnInfoOutput.


        :param container_name: The container_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def container_net_mode(self):
        """Gets the container_net_mode of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_net_mode of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_net_mode

    @container_net_mode.setter
    def container_net_mode(self, container_net_mode):
        """Sets the container_net_mode of this AffectForGetHostVulnInfoOutput.


        :param container_net_mode: The container_net_mode of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_net_mode = container_net_mode

    @property
    def container_query_result(self):
        """Gets the container_query_result of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_query_result of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_query_result

    @container_query_result.setter
    def container_query_result(self, container_query_result):
        """Sets the container_query_result of this AffectForGetHostVulnInfoOutput.


        :param container_query_result: The container_query_result of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_query_result = container_query_result

    @property
    def container_run_time(self):
        """Gets the container_run_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_run_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_run_time

    @container_run_time.setter
    def container_run_time(self, container_run_time):
        """Sets the container_run_time of this AffectForGetHostVulnInfoOutput.


        :param container_run_time: The container_run_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_run_time = container_run_time

    @property
    def container_state(self):
        """Gets the container_state of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The container_state of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_state

    @container_state.setter
    def container_state(self, container_state):
        """Sets the container_state of this AffectForGetHostVulnInfoOutput.


        :param container_state: The container_state of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_state = container_state

    @property
    def cves(self):
        """Gets the cves of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cves of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cves

    @cves.setter
    def cves(self, cves):
        """Sets the cves of this AffectForGetHostVulnInfoOutput.


        :param cves: The cves of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._cves = cves

    @property
    def image_id(self):
        """Gets the image_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The image_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this AffectForGetHostVulnInfoOutput.


        :param image_id: The image_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def image_name(self):
        """Gets the image_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The image_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this AffectForGetHostVulnInfoOutput.


        :param image_name: The image_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def pid_list(self):
        """Gets the pid_list of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The pid_list of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: list[PidListForGetHostVulnInfoOutput]
        """
        return self._pid_list

    @pid_list.setter
    def pid_list(self, pid_list):
        """Sets the pid_list of this AffectForGetHostVulnInfoOutput.


        :param pid_list: The pid_list of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: list[PidListForGetHostVulnInfoOutput]
        """

        self._pid_list = pid_list

    @property
    def pod_id(self):
        """Gets the pod_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The pod_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_id

    @pod_id.setter
    def pod_id(self, pod_id):
        """Sets the pod_id of this AffectForGetHostVulnInfoOutput.


        :param pod_id: The pod_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._pod_id = pod_id

    @property
    def pod_name(self):
        """Gets the pod_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The pod_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_name

    @pod_name.setter
    def pod_name(self, pod_name):
        """Sets the pod_name of this AffectForGetHostVulnInfoOutput.


        :param pod_name: The pod_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._pod_name = pod_name

    @property
    def software_name(self):
        """Gets the software_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The software_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._software_name

    @software_name.setter
    def software_name(self, software_name):
        """Sets the software_name of this AffectForGetHostVulnInfoOutput.


        :param software_name: The software_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._software_name = software_name

    @property
    def software_path(self):
        """Gets the software_path of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The software_path of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._software_path

    @software_path.setter
    def software_path(self, software_path):
        """Sets the software_path of this AffectForGetHostVulnInfoOutput.


        :param software_path: The software_path of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._software_path = software_path

    @property
    def software_source(self):
        """Gets the software_source of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The software_source of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._software_source

    @software_source.setter
    def software_source(self, software_source):
        """Sets the software_source of this AffectForGetHostVulnInfoOutput.


        :param software_source: The software_source of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._software_source = software_source

    @property
    def software_version(self):
        """Gets the software_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The software_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._software_version

    @software_version.setter
    def software_version(self, software_version):
        """Sets the software_version of this AffectForGetHostVulnInfoOutput.


        :param software_version: The software_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._software_version = software_version

    @property
    def state(self):
        """Gets the state of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The state of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this AffectForGetHostVulnInfoOutput.


        :param state: The state of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def type(self):
        """Gets the type of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The type of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this AffectForGetHostVulnInfoOutput.


        :param type: The type of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The update_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this AffectForGetHostVulnInfoOutput.


        :param update_time: The update_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def action(self):
        """Gets the action of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The action of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this AffectForGetHostVulnInfoOutput.


        :param action: The action of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def cluster_id(self):
        """Gets the cluster_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cluster_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this AffectForGetHostVulnInfoOutput.


        :param cluster_id: The cluster_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cluster_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this AffectForGetHostVulnInfoOutput.


        :param cluster_name: The cluster_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def control_time(self):
        """Gets the control_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The control_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._control_time

    @control_time.setter
    def control_time(self, control_time):
        """Sets the control_time of this AffectForGetHostVulnInfoOutput.


        :param control_time: The control_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: int
        """

        self._control_time = control_time

    @property
    def create_time(self):
        """Gets the create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this AffectForGetHostVulnInfoOutput.


        :param create_time: The create_time of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def cve_id(self):
        """Gets the cve_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cve_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this AffectForGetHostVulnInfoOutput.


        :param cve_id: The cve_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def cwpp_id(self):
        """Gets the cwpp_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The cwpp_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cwpp_id

    @cwpp_id.setter
    def cwpp_id(self, cwpp_id):
        """Sets the cwpp_id of this AffectForGetHostVulnInfoOutput.


        :param cwpp_id: The cwpp_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cwpp_id = cwpp_id

    @property
    def fix_version(self):
        """Gets the fix_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The fix_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._fix_version

    @fix_version.setter
    def fix_version(self, fix_version):
        """Sets the fix_version of this AffectForGetHostVulnInfoOutput.


        :param fix_version: The fix_version of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._fix_version = fix_version

    @property
    def level(self):
        """Gets the level of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The level of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this AffectForGetHostVulnInfoOutput.


        :param level: The level of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def namespace(self):
        """Gets the namespace of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The namespace of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this AffectForGetHostVulnInfoOutput.


        :param namespace: The namespace of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def operate_reason(self):
        """Gets the operate_reason of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The operate_reason of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_reason

    @operate_reason.setter
    def operate_reason(self, operate_reason):
        """Sets the operate_reason of this AffectForGetHostVulnInfoOutput.


        :param operate_reason: The operate_reason of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._operate_reason = operate_reason

    @property
    def pod_hash(self):
        """Gets the pod_hash of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The pod_hash of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_hash

    @pod_hash.setter
    def pod_hash(self, pod_hash):
        """Sets the pod_hash of this AffectForGetHostVulnInfoOutput.


        :param pod_hash: The pod_hash of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._pod_hash = pod_hash

    @property
    def status(self):
        """Gets the status of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The status of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AffectForGetHostVulnInfoOutput.


        :param status: The status of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The tag of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this AffectForGetHostVulnInfoOutput.


        :param tag: The tag of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def vuln_name(self):
        """Gets the vuln_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The vuln_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this AffectForGetHostVulnInfoOutput.


        :param vuln_name: The vuln_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    @property
    def vuln_type(self):
        """Gets the vuln_type of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The vuln_type of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_type

    @vuln_type.setter
    def vuln_type(self, vuln_type):
        """Sets the vuln_type of this AffectForGetHostVulnInfoOutput.


        :param vuln_type: The vuln_type of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._vuln_type = vuln_type

    @property
    def workload_id(self):
        """Gets the workload_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The workload_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._workload_id

    @workload_id.setter
    def workload_id(self, workload_id):
        """Sets the workload_id of this AffectForGetHostVulnInfoOutput.


        :param workload_id: The workload_id of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._workload_id = workload_id

    @property
    def workload_name(self):
        """Gets the workload_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501


        :return: The workload_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._workload_name

    @workload_name.setter
    def workload_name(self, workload_name):
        """Sets the workload_name of this AffectForGetHostVulnInfoOutput.


        :param workload_name: The workload_name of this AffectForGetHostVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._workload_name = workload_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AffectForGetHostVulnInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AffectForGetHostVulnInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AffectForGetHostVulnInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
