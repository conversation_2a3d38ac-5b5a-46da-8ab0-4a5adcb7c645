# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuizConfigForListActivityQuizConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'deadline': 'int',
        'id': 'int',
        'quiz_analysis': 'str',
        'quiz_options': 'list[QuizOptionForListActivityQuizConfigsOutput]',
        'quiz_result_type': 'int',
        'quiz_status': 'int',
        'quiz_title': 'str',
        'quiz_title_type': 'int',
        'right_options': 'list[str]',
        'send_time': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'deadline': 'Deadline',
        'id': 'Id',
        'quiz_analysis': 'QuizAnalysis',
        'quiz_options': 'QuizOptions',
        'quiz_result_type': 'QuizResultType',
        'quiz_status': 'QuizStatus',
        'quiz_title': 'QuizTitle',
        'quiz_title_type': 'QuizTitleType',
        'right_options': 'RightOptions',
        'send_time': 'SendTime'
    }

    def __init__(self, activity_id=None, deadline=None, id=None, quiz_analysis=None, quiz_options=None, quiz_result_type=None, quiz_status=None, quiz_title=None, quiz_title_type=None, right_options=None, send_time=None, _configuration=None):  # noqa: E501
        """QuizConfigForListActivityQuizConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._deadline = None
        self._id = None
        self._quiz_analysis = None
        self._quiz_options = None
        self._quiz_result_type = None
        self._quiz_status = None
        self._quiz_title = None
        self._quiz_title_type = None
        self._right_options = None
        self._send_time = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if deadline is not None:
            self.deadline = deadline
        if id is not None:
            self.id = id
        if quiz_analysis is not None:
            self.quiz_analysis = quiz_analysis
        if quiz_options is not None:
            self.quiz_options = quiz_options
        if quiz_result_type is not None:
            self.quiz_result_type = quiz_result_type
        if quiz_status is not None:
            self.quiz_status = quiz_status
        if quiz_title is not None:
            self.quiz_title = quiz_title
        if quiz_title_type is not None:
            self.quiz_title_type = quiz_title_type
        if right_options is not None:
            self.right_options = right_options
        if send_time is not None:
            self.send_time = send_time

    @property
    def activity_id(self):
        """Gets the activity_id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The activity_id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this QuizConfigForListActivityQuizConfigsOutput.


        :param activity_id: The activity_id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def deadline(self):
        """Gets the deadline of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The deadline of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._deadline

    @deadline.setter
    def deadline(self, deadline):
        """Sets the deadline of this QuizConfigForListActivityQuizConfigsOutput.


        :param deadline: The deadline of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._deadline = deadline

    @property
    def id(self):
        """Gets the id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this QuizConfigForListActivityQuizConfigsOutput.


        :param id: The id of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def quiz_analysis(self):
        """Gets the quiz_analysis of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_analysis of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._quiz_analysis

    @quiz_analysis.setter
    def quiz_analysis(self, quiz_analysis):
        """Sets the quiz_analysis of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_analysis: The quiz_analysis of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: str
        """

        self._quiz_analysis = quiz_analysis

    @property
    def quiz_options(self):
        """Gets the quiz_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: list[QuizOptionForListActivityQuizConfigsOutput]
        """
        return self._quiz_options

    @quiz_options.setter
    def quiz_options(self, quiz_options):
        """Sets the quiz_options of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_options: The quiz_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: list[QuizOptionForListActivityQuizConfigsOutput]
        """

        self._quiz_options = quiz_options

    @property
    def quiz_result_type(self):
        """Gets the quiz_result_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_result_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_result_type

    @quiz_result_type.setter
    def quiz_result_type(self, quiz_result_type):
        """Sets the quiz_result_type of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_result_type: The quiz_result_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._quiz_result_type = quiz_result_type

    @property
    def quiz_status(self):
        """Gets the quiz_status of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_status of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_status

    @quiz_status.setter
    def quiz_status(self, quiz_status):
        """Sets the quiz_status of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_status: The quiz_status of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._quiz_status = quiz_status

    @property
    def quiz_title(self):
        """Gets the quiz_title of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_title of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._quiz_title

    @quiz_title.setter
    def quiz_title(self, quiz_title):
        """Sets the quiz_title of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_title: The quiz_title of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: str
        """

        self._quiz_title = quiz_title

    @property
    def quiz_title_type(self):
        """Gets the quiz_title_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The quiz_title_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_title_type

    @quiz_title_type.setter
    def quiz_title_type(self, quiz_title_type):
        """Sets the quiz_title_type of this QuizConfigForListActivityQuizConfigsOutput.


        :param quiz_title_type: The quiz_title_type of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._quiz_title_type = quiz_title_type

    @property
    def right_options(self):
        """Gets the right_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The right_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._right_options

    @right_options.setter
    def right_options(self, right_options):
        """Sets the right_options of this QuizConfigForListActivityQuizConfigsOutput.


        :param right_options: The right_options of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: list[str]
        """

        self._right_options = right_options

    @property
    def send_time(self):
        """Gets the send_time of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501


        :return: The send_time of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this QuizConfigForListActivityQuizConfigsOutput.


        :param send_time: The send_time of this QuizConfigForListActivityQuizConfigsOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuizConfigForListActivityQuizConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuizConfigForListActivityQuizConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuizConfigForListActivityQuizConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
