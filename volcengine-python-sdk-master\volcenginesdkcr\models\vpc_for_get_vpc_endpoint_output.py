# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VpcForGetVpcEndpointOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'create_time': 'str',
        'ip': 'str',
        'region': 'str',
        'status': 'str',
        'subnet_id': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'create_time': 'CreateTime',
        'ip': 'Ip',
        'region': 'Region',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId'
    }

    def __init__(self, account_id=None, create_time=None, ip=None, region=None, status=None, subnet_id=None, vpc_id=None, _configuration=None):  # noqa: E501
        """VpcForGetVpcEndpointOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._create_time = None
        self._ip = None
        self._region = None
        self._status = None
        self._subnet_id = None
        self._vpc_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if create_time is not None:
            self.create_time = create_time
        if ip is not None:
            self.ip = ip
        if region is not None:
            self.region = region
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def account_id(self):
        """Gets the account_id of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The account_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this VpcForGetVpcEndpointOutput.


        :param account_id: The account_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def create_time(self):
        """Gets the create_time of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The create_time of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this VpcForGetVpcEndpointOutput.


        :param create_time: The create_time of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def ip(self):
        """Gets the ip of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The ip of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this VpcForGetVpcEndpointOutput.


        :param ip: The ip of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def region(self):
        """Gets the region of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The region of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this VpcForGetVpcEndpointOutput.


        :param region: The region of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def status(self):
        """Gets the status of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The status of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VpcForGetVpcEndpointOutput.


        :param status: The status of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The subnet_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this VpcForGetVpcEndpointOutput.


        :param subnet_id: The subnet_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this VpcForGetVpcEndpointOutput.  # noqa: E501


        :return: The vpc_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this VpcForGetVpcEndpointOutput.


        :param vpc_id: The vpc_id of this VpcForGetVpcEndpointOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VpcForGetVpcEndpointOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VpcForGetVpcEndpointOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VpcForGetVpcEndpointOutput):
            return True

        return self.to_dict() != other.to_dict()
