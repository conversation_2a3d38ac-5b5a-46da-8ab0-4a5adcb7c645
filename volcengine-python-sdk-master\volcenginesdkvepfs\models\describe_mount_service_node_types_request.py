# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeMountServiceNodeTypesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'language_code': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'language_code': 'LanguageCode',
        'zone_id': 'ZoneId'
    }

    def __init__(self, language_code=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeMountServiceNodeTypesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._language_code = None
        self._zone_id = None
        self.discriminator = None

        if language_code is not None:
            self.language_code = language_code
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def language_code(self):
        """Gets the language_code of this DescribeMountServiceNodeTypesRequest.  # noqa: E501


        :return: The language_code of this DescribeMountServiceNodeTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._language_code

    @language_code.setter
    def language_code(self, language_code):
        """Sets the language_code of this DescribeMountServiceNodeTypesRequest.


        :param language_code: The language_code of this DescribeMountServiceNodeTypesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["zh", "en"]  # noqa: E501
        if (self._configuration.client_side_validation and
                language_code not in allowed_values):
            raise ValueError(
                "Invalid value for `language_code` ({0}), must be one of {1}"  # noqa: E501
                .format(language_code, allowed_values)
            )

        self._language_code = language_code

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeMountServiceNodeTypesRequest.  # noqa: E501


        :return: The zone_id of this DescribeMountServiceNodeTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeMountServiceNodeTypesRequest.


        :param zone_id: The zone_id of this DescribeMountServiceNodeTypesRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeMountServiceNodeTypesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeMountServiceNodeTypesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeMountServiceNodeTypesRequest):
            return True

        return self.to_dict() != other.to_dict()
