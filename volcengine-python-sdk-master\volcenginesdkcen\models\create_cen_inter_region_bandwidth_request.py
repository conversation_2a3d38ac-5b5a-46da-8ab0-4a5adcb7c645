# coding: utf-8

"""
    cen

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateCenInterRegionBandwidthRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'cen_id': 'str',
        'local_region_id': 'str',
        'peer_region_id': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'cen_id': 'CenId',
        'local_region_id': 'LocalRegionId',
        'peer_region_id': 'PeerRegionId'
    }

    def __init__(self, bandwidth=None, cen_id=None, local_region_id=None, peer_region_id=None, _configuration=None):  # noqa: E501
        """CreateCenInterRegionBandwidthRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._cen_id = None
        self._local_region_id = None
        self._peer_region_id = None
        self.discriminator = None

        self.bandwidth = bandwidth
        self.cen_id = cen_id
        self.local_region_id = local_region_id
        self.peer_region_id = peer_region_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this CreateCenInterRegionBandwidthRequest.  # noqa: E501


        :return: The bandwidth of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this CreateCenInterRegionBandwidthRequest.


        :param bandwidth: The bandwidth of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth is None:
            raise ValueError("Invalid value for `bandwidth`, must not be `None`")  # noqa: E501

        self._bandwidth = bandwidth

    @property
    def cen_id(self):
        """Gets the cen_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501


        :return: The cen_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :rtype: str
        """
        return self._cen_id

    @cen_id.setter
    def cen_id(self, cen_id):
        """Sets the cen_id of this CreateCenInterRegionBandwidthRequest.


        :param cen_id: The cen_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cen_id is None:
            raise ValueError("Invalid value for `cen_id`, must not be `None`")  # noqa: E501

        self._cen_id = cen_id

    @property
    def local_region_id(self):
        """Gets the local_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501


        :return: The local_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :rtype: str
        """
        return self._local_region_id

    @local_region_id.setter
    def local_region_id(self, local_region_id):
        """Sets the local_region_id of this CreateCenInterRegionBandwidthRequest.


        :param local_region_id: The local_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and local_region_id is None:
            raise ValueError("Invalid value for `local_region_id`, must not be `None`")  # noqa: E501

        self._local_region_id = local_region_id

    @property
    def peer_region_id(self):
        """Gets the peer_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501


        :return: The peer_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_region_id

    @peer_region_id.setter
    def peer_region_id(self, peer_region_id):
        """Sets the peer_region_id of this CreateCenInterRegionBandwidthRequest.


        :param peer_region_id: The peer_region_id of this CreateCenInterRegionBandwidthRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_region_id is None:
            raise ValueError("Invalid value for `peer_region_id`, must not be `None`")  # noqa: E501

        self._peer_region_id = peer_region_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateCenInterRegionBandwidthRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateCenInterRegionBandwidthRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateCenInterRegionBandwidthRequest):
            return True

        return self.to_dict() != other.to_dict()
