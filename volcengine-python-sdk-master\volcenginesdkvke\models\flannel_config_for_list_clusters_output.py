# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FlannelConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_pods_per_node': 'int',
        'pod_cidrs': 'list[str]'
    }

    attribute_map = {
        'max_pods_per_node': 'MaxPodsPerNode',
        'pod_cidrs': 'PodCidrs'
    }

    def __init__(self, max_pods_per_node=None, pod_cidrs=None, _configuration=None):  # noqa: E501
        """FlannelConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_pods_per_node = None
        self._pod_cidrs = None
        self.discriminator = None

        if max_pods_per_node is not None:
            self.max_pods_per_node = max_pods_per_node
        if pod_cidrs is not None:
            self.pod_cidrs = pod_cidrs

    @property
    def max_pods_per_node(self):
        """Gets the max_pods_per_node of this FlannelConfigForListClustersOutput.  # noqa: E501


        :return: The max_pods_per_node of this FlannelConfigForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_pods_per_node

    @max_pods_per_node.setter
    def max_pods_per_node(self, max_pods_per_node):
        """Sets the max_pods_per_node of this FlannelConfigForListClustersOutput.


        :param max_pods_per_node: The max_pods_per_node of this FlannelConfigForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._max_pods_per_node = max_pods_per_node

    @property
    def pod_cidrs(self):
        """Gets the pod_cidrs of this FlannelConfigForListClustersOutput.  # noqa: E501


        :return: The pod_cidrs of this FlannelConfigForListClustersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._pod_cidrs

    @pod_cidrs.setter
    def pod_cidrs(self, pod_cidrs):
        """Sets the pod_cidrs of this FlannelConfigForListClustersOutput.


        :param pod_cidrs: The pod_cidrs of this FlannelConfigForListClustersOutput.  # noqa: E501
        :type: list[str]
        """

        self._pod_cidrs = pod_cidrs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FlannelConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FlannelConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FlannelConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
