# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PresenterChatAPIV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'audience_group_id': 'int',
        'comment': 'str',
        'top_status': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'audience_group_id': 'AudienceGroupId',
        'comment': 'Comment',
        'top_status': 'TopStatus'
    }

    def __init__(self, activity_id=None, audience_group_id=None, comment=None, top_status=None, _configuration=None):  # noqa: E501
        """PresenterChatAPIV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._audience_group_id = None
        self._comment = None
        self._top_status = None
        self.discriminator = None

        self.activity_id = activity_id
        if audience_group_id is not None:
            self.audience_group_id = audience_group_id
        self.comment = comment
        if top_status is not None:
            self.top_status = top_status

    @property
    def activity_id(self):
        """Gets the activity_id of this PresenterChatAPIV2Request.  # noqa: E501


        :return: The activity_id of this PresenterChatAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this PresenterChatAPIV2Request.


        :param activity_id: The activity_id of this PresenterChatAPIV2Request.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def audience_group_id(self):
        """Gets the audience_group_id of this PresenterChatAPIV2Request.  # noqa: E501


        :return: The audience_group_id of this PresenterChatAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._audience_group_id

    @audience_group_id.setter
    def audience_group_id(self, audience_group_id):
        """Sets the audience_group_id of this PresenterChatAPIV2Request.


        :param audience_group_id: The audience_group_id of this PresenterChatAPIV2Request.  # noqa: E501
        :type: int
        """

        self._audience_group_id = audience_group_id

    @property
    def comment(self):
        """Gets the comment of this PresenterChatAPIV2Request.  # noqa: E501


        :return: The comment of this PresenterChatAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._comment

    @comment.setter
    def comment(self, comment):
        """Sets the comment of this PresenterChatAPIV2Request.


        :param comment: The comment of this PresenterChatAPIV2Request.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and comment is None:
            raise ValueError("Invalid value for `comment`, must not be `None`")  # noqa: E501

        self._comment = comment

    @property
    def top_status(self):
        """Gets the top_status of this PresenterChatAPIV2Request.  # noqa: E501


        :return: The top_status of this PresenterChatAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._top_status

    @top_status.setter
    def top_status(self, top_status):
        """Sets the top_status of this PresenterChatAPIV2Request.


        :param top_status: The top_status of this PresenterChatAPIV2Request.  # noqa: E501
        :type: int
        """

        self._top_status = top_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PresenterChatAPIV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PresenterChatAPIV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PresenterChatAPIV2Request):
            return True

        return self.to_dict() != other.to_dict()
