# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SandboxForListSandboxesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'availability_zone': 'str',
        'created_at': 'str',
        'expire_at': 'str',
        'function_id': 'str',
        'id': 'str',
        'instance_type': 'str',
        'metadata': 'MetadataForListSandboxesOutput',
        'revision_number': 'int',
        'status': 'str'
    }

    attribute_map = {
        'availability_zone': 'AvailabilityZone',
        'created_at': 'CreatedAt',
        'expire_at': 'ExpireAt',
        'function_id': 'FunctionId',
        'id': 'Id',
        'instance_type': 'InstanceType',
        'metadata': 'Metadata',
        'revision_number': 'RevisionNumber',
        'status': 'Status'
    }

    def __init__(self, availability_zone=None, created_at=None, expire_at=None, function_id=None, id=None, instance_type=None, metadata=None, revision_number=None, status=None, _configuration=None):  # noqa: E501
        """SandboxForListSandboxesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._availability_zone = None
        self._created_at = None
        self._expire_at = None
        self._function_id = None
        self._id = None
        self._instance_type = None
        self._metadata = None
        self._revision_number = None
        self._status = None
        self.discriminator = None

        if availability_zone is not None:
            self.availability_zone = availability_zone
        if created_at is not None:
            self.created_at = created_at
        if expire_at is not None:
            self.expire_at = expire_at
        if function_id is not None:
            self.function_id = function_id
        if id is not None:
            self.id = id
        if instance_type is not None:
            self.instance_type = instance_type
        if metadata is not None:
            self.metadata = metadata
        if revision_number is not None:
            self.revision_number = revision_number
        if status is not None:
            self.status = status

    @property
    def availability_zone(self):
        """Gets the availability_zone of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The availability_zone of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._availability_zone

    @availability_zone.setter
    def availability_zone(self, availability_zone):
        """Sets the availability_zone of this SandboxForListSandboxesOutput.


        :param availability_zone: The availability_zone of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._availability_zone = availability_zone

    @property
    def created_at(self):
        """Gets the created_at of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The created_at of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this SandboxForListSandboxesOutput.


        :param created_at: The created_at of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def expire_at(self):
        """Gets the expire_at of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The expire_at of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_at

    @expire_at.setter
    def expire_at(self, expire_at):
        """Sets the expire_at of this SandboxForListSandboxesOutput.


        :param expire_at: The expire_at of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._expire_at = expire_at

    @property
    def function_id(self):
        """Gets the function_id of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The function_id of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this SandboxForListSandboxesOutput.


        :param function_id: The function_id of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._function_id = function_id

    @property
    def id(self):
        """Gets the id of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The id of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SandboxForListSandboxesOutput.


        :param id: The id of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def instance_type(self):
        """Gets the instance_type of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The instance_type of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this SandboxForListSandboxesOutput.


        :param instance_type: The instance_type of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def metadata(self):
        """Gets the metadata of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The metadata of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: MetadataForListSandboxesOutput
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this SandboxForListSandboxesOutput.


        :param metadata: The metadata of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: MetadataForListSandboxesOutput
        """

        self._metadata = metadata

    @property
    def revision_number(self):
        """Gets the revision_number of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The revision_number of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: int
        """
        return self._revision_number

    @revision_number.setter
    def revision_number(self, revision_number):
        """Sets the revision_number of this SandboxForListSandboxesOutput.


        :param revision_number: The revision_number of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: int
        """

        self._revision_number = revision_number

    @property
    def status(self):
        """Gets the status of this SandboxForListSandboxesOutput.  # noqa: E501


        :return: The status of this SandboxForListSandboxesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SandboxForListSandboxesOutput.


        :param status: The status of this SandboxForListSandboxesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SandboxForListSandboxesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SandboxForListSandboxesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SandboxForListSandboxesOutput):
            return True

        return self.to_dict() != other.to_dict()
