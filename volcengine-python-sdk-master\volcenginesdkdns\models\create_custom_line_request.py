# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateCustomLineRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip_segments': 'list[str]',
        'name_cn': 'str',
        'remark': 'str'
    }

    attribute_map = {
        'ip_segments': 'IPSegments',
        'name_cn': 'NameCN',
        'remark': 'Remark'
    }

    def __init__(self, ip_segments=None, name_cn=None, remark=None, _configuration=None):  # noqa: E501
        """CreateCustomLineRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip_segments = None
        self._name_cn = None
        self._remark = None
        self.discriminator = None

        if ip_segments is not None:
            self.ip_segments = ip_segments
        self.name_cn = name_cn
        if remark is not None:
            self.remark = remark

    @property
    def ip_segments(self):
        """Gets the ip_segments of this CreateCustomLineRequest.  # noqa: E501


        :return: The ip_segments of this CreateCustomLineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_segments

    @ip_segments.setter
    def ip_segments(self, ip_segments):
        """Sets the ip_segments of this CreateCustomLineRequest.


        :param ip_segments: The ip_segments of this CreateCustomLineRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_segments = ip_segments

    @property
    def name_cn(self):
        """Gets the name_cn of this CreateCustomLineRequest.  # noqa: E501


        :return: The name_cn of this CreateCustomLineRequest.  # noqa: E501
        :rtype: str
        """
        return self._name_cn

    @name_cn.setter
    def name_cn(self, name_cn):
        """Sets the name_cn of this CreateCustomLineRequest.


        :param name_cn: The name_cn of this CreateCustomLineRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name_cn is None:
            raise ValueError("Invalid value for `name_cn`, must not be `None`")  # noqa: E501

        self._name_cn = name_cn

    @property
    def remark(self):
        """Gets the remark of this CreateCustomLineRequest.  # noqa: E501


        :return: The remark of this CreateCustomLineRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this CreateCustomLineRequest.


        :param remark: The remark of this CreateCustomLineRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateCustomLineRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateCustomLineRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateCustomLineRequest):
            return True

        return self.to_dict() != other.to_dict()
