# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertCredentialForGetDeploymentOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'registry_token': 'str',
        'registry_username': 'str'
    }

    attribute_map = {
        'registry_token': 'RegistryToken',
        'registry_username': 'RegistryUsername'
    }

    def __init__(self, registry_token=None, registry_username=None, _configuration=None):  # noqa: E501
        """ConvertCredentialForGetDeploymentOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._registry_token = None
        self._registry_username = None
        self.discriminator = None

        if registry_token is not None:
            self.registry_token = registry_token
        if registry_username is not None:
            self.registry_username = registry_username

    @property
    def registry_token(self):
        """Gets the registry_token of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501


        :return: The registry_token of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_token

    @registry_token.setter
    def registry_token(self, registry_token):
        """Sets the registry_token of this ConvertCredentialForGetDeploymentOutput.


        :param registry_token: The registry_token of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._registry_token = registry_token

    @property
    def registry_username(self):
        """Gets the registry_username of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501


        :return: The registry_username of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_username

    @registry_username.setter
    def registry_username(self, registry_username):
        """Sets the registry_username of this ConvertCredentialForGetDeploymentOutput.


        :param registry_username: The registry_username of this ConvertCredentialForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._registry_username = registry_username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertCredentialForGetDeploymentOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertCredentialForGetDeploymentOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertCredentialForGetDeploymentOutput):
            return True

        return self.to_dict() != other.to_dict()
