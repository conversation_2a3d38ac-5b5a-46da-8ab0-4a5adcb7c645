# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RoleForUpdateDeploymentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'command': 'str',
        'credential': 'CredentialForUpdateDeploymentInput',
        'envs': 'list[EnvForUpdateDeploymentInput]',
        'expose': 'bool',
        'graceful_shutdown_period': 'int',
        'id': 'str',
        'image': 'ImageForUpdateDeploymentInput',
        'liveness_probe': 'LivenessProbeForUpdateDeploymentInput',
        'model': 'ModelForUpdateDeploymentInput',
        'name': 'str',
        'node_affinity_spec': 'NodeAffinitySpecForUpdateDeploymentInput',
        'ports': 'list[PortForUpdateDeploymentInput]',
        'readiness_probe': 'ReadinessProbeForUpdateDeploymentInput',
        'replicas': 'int',
        'resource': 'ResourceForUpdateDeploymentInput',
        'rolling_update_strategy': 'RollingUpdateStrategyForUpdateDeploymentInput',
        'security_group_ids': 'list[str]',
        'shard_num': 'int',
        'startup_probe': 'StartupProbeForUpdateDeploymentInput',
        'storages': 'list[StorageForUpdateDeploymentInput]'
    }

    attribute_map = {
        'command': 'Command',
        'credential': 'Credential',
        'envs': 'Envs',
        'expose': 'Expose',
        'graceful_shutdown_period': 'GracefulShutdownPeriod',
        'id': 'Id',
        'image': 'Image',
        'liveness_probe': 'LivenessProbe',
        'model': 'Model',
        'name': 'Name',
        'node_affinity_spec': 'NodeAffinitySpec',
        'ports': 'Ports',
        'readiness_probe': 'ReadinessProbe',
        'replicas': 'Replicas',
        'resource': 'Resource',
        'rolling_update_strategy': 'RollingUpdateStrategy',
        'security_group_ids': 'SecurityGroupIds',
        'shard_num': 'ShardNum',
        'startup_probe': 'StartupProbe',
        'storages': 'Storages'
    }

    def __init__(self, command=None, credential=None, envs=None, expose=None, graceful_shutdown_period=None, id=None, image=None, liveness_probe=None, model=None, name=None, node_affinity_spec=None, ports=None, readiness_probe=None, replicas=None, resource=None, rolling_update_strategy=None, security_group_ids=None, shard_num=None, startup_probe=None, storages=None, _configuration=None):  # noqa: E501
        """RoleForUpdateDeploymentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._command = None
        self._credential = None
        self._envs = None
        self._expose = None
        self._graceful_shutdown_period = None
        self._id = None
        self._image = None
        self._liveness_probe = None
        self._model = None
        self._name = None
        self._node_affinity_spec = None
        self._ports = None
        self._readiness_probe = None
        self._replicas = None
        self._resource = None
        self._rolling_update_strategy = None
        self._security_group_ids = None
        self._shard_num = None
        self._startup_probe = None
        self._storages = None
        self.discriminator = None

        if command is not None:
            self.command = command
        if credential is not None:
            self.credential = credential
        if envs is not None:
            self.envs = envs
        if expose is not None:
            self.expose = expose
        if graceful_shutdown_period is not None:
            self.graceful_shutdown_period = graceful_shutdown_period
        if id is not None:
            self.id = id
        if image is not None:
            self.image = image
        if liveness_probe is not None:
            self.liveness_probe = liveness_probe
        if model is not None:
            self.model = model
        if name is not None:
            self.name = name
        if node_affinity_spec is not None:
            self.node_affinity_spec = node_affinity_spec
        if ports is not None:
            self.ports = ports
        if readiness_probe is not None:
            self.readiness_probe = readiness_probe
        if replicas is not None:
            self.replicas = replicas
        if resource is not None:
            self.resource = resource
        if rolling_update_strategy is not None:
            self.rolling_update_strategy = rolling_update_strategy
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if shard_num is not None:
            self.shard_num = shard_num
        if startup_probe is not None:
            self.startup_probe = startup_probe
        if storages is not None:
            self.storages = storages

    @property
    def command(self):
        """Gets the command of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The command of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._command

    @command.setter
    def command(self, command):
        """Sets the command of this RoleForUpdateDeploymentInput.


        :param command: The command of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._command = command

    @property
    def credential(self):
        """Gets the credential of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The credential of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: CredentialForUpdateDeploymentInput
        """
        return self._credential

    @credential.setter
    def credential(self, credential):
        """Sets the credential of this RoleForUpdateDeploymentInput.


        :param credential: The credential of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: CredentialForUpdateDeploymentInput
        """

        self._credential = credential

    @property
    def envs(self):
        """Gets the envs of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The envs of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: list[EnvForUpdateDeploymentInput]
        """
        return self._envs

    @envs.setter
    def envs(self, envs):
        """Sets the envs of this RoleForUpdateDeploymentInput.


        :param envs: The envs of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: list[EnvForUpdateDeploymentInput]
        """

        self._envs = envs

    @property
    def expose(self):
        """Gets the expose of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The expose of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: bool
        """
        return self._expose

    @expose.setter
    def expose(self, expose):
        """Sets the expose of this RoleForUpdateDeploymentInput.


        :param expose: The expose of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: bool
        """

        self._expose = expose

    @property
    def graceful_shutdown_period(self):
        """Gets the graceful_shutdown_period of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The graceful_shutdown_period of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._graceful_shutdown_period

    @graceful_shutdown_period.setter
    def graceful_shutdown_period(self, graceful_shutdown_period):
        """Sets the graceful_shutdown_period of this RoleForUpdateDeploymentInput.


        :param graceful_shutdown_period: The graceful_shutdown_period of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._graceful_shutdown_period = graceful_shutdown_period

    @property
    def id(self):
        """Gets the id of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The id of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this RoleForUpdateDeploymentInput.


        :param id: The id of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def image(self):
        """Gets the image of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The image of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: ImageForUpdateDeploymentInput
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this RoleForUpdateDeploymentInput.


        :param image: The image of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: ImageForUpdateDeploymentInput
        """

        self._image = image

    @property
    def liveness_probe(self):
        """Gets the liveness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The liveness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: LivenessProbeForUpdateDeploymentInput
        """
        return self._liveness_probe

    @liveness_probe.setter
    def liveness_probe(self, liveness_probe):
        """Sets the liveness_probe of this RoleForUpdateDeploymentInput.


        :param liveness_probe: The liveness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: LivenessProbeForUpdateDeploymentInput
        """

        self._liveness_probe = liveness_probe

    @property
    def model(self):
        """Gets the model of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The model of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: ModelForUpdateDeploymentInput
        """
        return self._model

    @model.setter
    def model(self, model):
        """Sets the model of this RoleForUpdateDeploymentInput.


        :param model: The model of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: ModelForUpdateDeploymentInput
        """

        self._model = model

    @property
    def name(self):
        """Gets the name of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The name of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RoleForUpdateDeploymentInput.


        :param name: The name of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_affinity_spec(self):
        """Gets the node_affinity_spec of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The node_affinity_spec of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: NodeAffinitySpecForUpdateDeploymentInput
        """
        return self._node_affinity_spec

    @node_affinity_spec.setter
    def node_affinity_spec(self, node_affinity_spec):
        """Sets the node_affinity_spec of this RoleForUpdateDeploymentInput.


        :param node_affinity_spec: The node_affinity_spec of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: NodeAffinitySpecForUpdateDeploymentInput
        """

        self._node_affinity_spec = node_affinity_spec

    @property
    def ports(self):
        """Gets the ports of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The ports of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: list[PortForUpdateDeploymentInput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this RoleForUpdateDeploymentInput.


        :param ports: The ports of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: list[PortForUpdateDeploymentInput]
        """

        self._ports = ports

    @property
    def readiness_probe(self):
        """Gets the readiness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The readiness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: ReadinessProbeForUpdateDeploymentInput
        """
        return self._readiness_probe

    @readiness_probe.setter
    def readiness_probe(self, readiness_probe):
        """Sets the readiness_probe of this RoleForUpdateDeploymentInput.


        :param readiness_probe: The readiness_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: ReadinessProbeForUpdateDeploymentInput
        """

        self._readiness_probe = readiness_probe

    @property
    def replicas(self):
        """Gets the replicas of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The replicas of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this RoleForUpdateDeploymentInput.


        :param replicas: The replicas of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    @property
    def resource(self):
        """Gets the resource of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The resource of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: ResourceForUpdateDeploymentInput
        """
        return self._resource

    @resource.setter
    def resource(self, resource):
        """Sets the resource of this RoleForUpdateDeploymentInput.


        :param resource: The resource of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: ResourceForUpdateDeploymentInput
        """

        self._resource = resource

    @property
    def rolling_update_strategy(self):
        """Gets the rolling_update_strategy of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The rolling_update_strategy of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: RollingUpdateStrategyForUpdateDeploymentInput
        """
        return self._rolling_update_strategy

    @rolling_update_strategy.setter
    def rolling_update_strategy(self, rolling_update_strategy):
        """Sets the rolling_update_strategy of this RoleForUpdateDeploymentInput.


        :param rolling_update_strategy: The rolling_update_strategy of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: RollingUpdateStrategyForUpdateDeploymentInput
        """

        self._rolling_update_strategy = rolling_update_strategy

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The security_group_ids of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this RoleForUpdateDeploymentInput.


        :param security_group_ids: The security_group_ids of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def shard_num(self):
        """Gets the shard_num of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The shard_num of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._shard_num

    @shard_num.setter
    def shard_num(self, shard_num):
        """Sets the shard_num of this RoleForUpdateDeploymentInput.


        :param shard_num: The shard_num of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._shard_num = shard_num

    @property
    def startup_probe(self):
        """Gets the startup_probe of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The startup_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: StartupProbeForUpdateDeploymentInput
        """
        return self._startup_probe

    @startup_probe.setter
    def startup_probe(self, startup_probe):
        """Sets the startup_probe of this RoleForUpdateDeploymentInput.


        :param startup_probe: The startup_probe of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: StartupProbeForUpdateDeploymentInput
        """

        self._startup_probe = startup_probe

    @property
    def storages(self):
        """Gets the storages of this RoleForUpdateDeploymentInput.  # noqa: E501


        :return: The storages of this RoleForUpdateDeploymentInput.  # noqa: E501
        :rtype: list[StorageForUpdateDeploymentInput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this RoleForUpdateDeploymentInput.


        :param storages: The storages of this RoleForUpdateDeploymentInput.  # noqa: E501
        :type: list[StorageForUpdateDeploymentInput]
        """

        self._storages = storages

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RoleForUpdateDeploymentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RoleForUpdateDeploymentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RoleForUpdateDeploymentInput):
            return True

        return self.to_dict() != other.to_dict()
