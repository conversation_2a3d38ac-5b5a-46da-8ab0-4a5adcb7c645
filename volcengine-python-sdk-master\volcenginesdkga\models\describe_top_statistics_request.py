# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTopStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'str',
        'input_id_type': 'str',
        'sort_metric': 'str',
        'start_time': 'str',
        'input_id': 'list[str]'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'input_id_type': 'InputIdType',
        'sort_metric': 'SortMetric',
        'start_time': 'StartTime',
        'input_id': 'inputId'
    }

    def __init__(self, end_time=None, input_id_type=None, sort_metric=None, start_time=None, input_id=None, _configuration=None):  # noqa: E501
        """DescribeTopStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._input_id_type = None
        self._sort_metric = None
        self._start_time = None
        self._input_id = None
        self.discriminator = None

        self.end_time = end_time
        self.input_id_type = input_id_type
        if sort_metric is not None:
            self.sort_metric = sort_metric
        self.start_time = start_time
        if input_id is not None:
            self.input_id = input_id

    @property
    def end_time(self):
        """Gets the end_time of this DescribeTopStatisticsRequest.  # noqa: E501


        :return: The end_time of this DescribeTopStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeTopStatisticsRequest.


        :param end_time: The end_time of this DescribeTopStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def input_id_type(self):
        """Gets the input_id_type of this DescribeTopStatisticsRequest.  # noqa: E501


        :return: The input_id_type of this DescribeTopStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._input_id_type

    @input_id_type.setter
    def input_id_type(self, input_id_type):
        """Sets the input_id_type of this DescribeTopStatisticsRequest.


        :param input_id_type: The input_id_type of this DescribeTopStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and input_id_type is None:
            raise ValueError("Invalid value for `input_id_type`, must not be `None`")  # noqa: E501

        self._input_id_type = input_id_type

    @property
    def sort_metric(self):
        """Gets the sort_metric of this DescribeTopStatisticsRequest.  # noqa: E501


        :return: The sort_metric of this DescribeTopStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_metric

    @sort_metric.setter
    def sort_metric(self, sort_metric):
        """Sets the sort_metric of this DescribeTopStatisticsRequest.


        :param sort_metric: The sort_metric of this DescribeTopStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._sort_metric = sort_metric

    @property
    def start_time(self):
        """Gets the start_time of this DescribeTopStatisticsRequest.  # noqa: E501


        :return: The start_time of this DescribeTopStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeTopStatisticsRequest.


        :param start_time: The start_time of this DescribeTopStatisticsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def input_id(self):
        """Gets the input_id of this DescribeTopStatisticsRequest.  # noqa: E501


        :return: The input_id of this DescribeTopStatisticsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._input_id

    @input_id.setter
    def input_id(self, input_id):
        """Sets the input_id of this DescribeTopStatisticsRequest.


        :param input_id: The input_id of this DescribeTopStatisticsRequest.  # noqa: E501
        :type: list[str]
        """

        self._input_id = input_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTopStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTopStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTopStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
