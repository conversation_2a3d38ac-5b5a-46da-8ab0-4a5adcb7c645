# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SyncTypeSupportStatusForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'block_reason': 'str',
        'is_support': 'bool',
        'sync_type': 'str'
    }

    attribute_map = {
        'block_reason': 'BlockReason',
        'is_support': 'IsSupport',
        'sync_type': 'SyncType'
    }

    def __init__(self, block_reason=None, is_support=None, sync_type=None, _configuration=None):  # noqa: E501
        """SyncTypeSupportStatusForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._block_reason = None
        self._is_support = None
        self._sync_type = None
        self.discriminator = None

        if block_reason is not None:
            self.block_reason = block_reason
        if is_support is not None:
            self.is_support = is_support
        if sync_type is not None:
            self.sync_type = sync_type

    @property
    def block_reason(self):
        """Gets the block_reason of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The block_reason of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._block_reason

    @block_reason.setter
    def block_reason(self, block_reason):
        """Sets the block_reason of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.


        :param block_reason: The block_reason of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._block_reason = block_reason

    @property
    def is_support(self):
        """Gets the is_support of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The is_support of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_support

    @is_support.setter
    def is_support(self, is_support):
        """Sets the is_support of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.


        :param is_support: The is_support of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: bool
        """

        self._is_support = is_support

    @property
    def sync_type(self):
        """Gets the sync_type of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The sync_type of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sync_type

    @sync_type.setter
    def sync_type(self, sync_type):
        """Sets the sync_type of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.


        :param sync_type: The sync_type of this SyncTypeSupportStatusForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._sync_type = sync_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SyncTypeSupportStatusForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SyncTypeSupportStatusForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SyncTypeSupportStatusForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
