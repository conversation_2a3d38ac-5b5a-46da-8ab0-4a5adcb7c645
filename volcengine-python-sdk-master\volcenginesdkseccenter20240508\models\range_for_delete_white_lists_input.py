# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RangeForDeleteWhiteListsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_area': 'str',
        'cluster_name': 'str',
        'hostname': 'str',
        'ip': 'str',
        'is_global': 'bool'
    }

    attribute_map = {
        'cluster_area': 'ClusterArea',
        'cluster_name': 'ClusterName',
        'hostname': 'Hostname',
        'ip': 'IP',
        'is_global': 'IsGlobal'
    }

    def __init__(self, cluster_area=None, cluster_name=None, hostname=None, ip=None, is_global=None, _configuration=None):  # noqa: E501
        """RangeForDeleteWhiteListsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_area = None
        self._cluster_name = None
        self._hostname = None
        self._ip = None
        self._is_global = None
        self.discriminator = None

        if cluster_area is not None:
            self.cluster_area = cluster_area
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if is_global is not None:
            self.is_global = is_global

    @property
    def cluster_area(self):
        """Gets the cluster_area of this RangeForDeleteWhiteListsInput.  # noqa: E501


        :return: The cluster_area of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_area

    @cluster_area.setter
    def cluster_area(self, cluster_area):
        """Sets the cluster_area of this RangeForDeleteWhiteListsInput.


        :param cluster_area: The cluster_area of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._cluster_area = cluster_area

    @property
    def cluster_name(self):
        """Gets the cluster_name of this RangeForDeleteWhiteListsInput.  # noqa: E501


        :return: The cluster_name of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this RangeForDeleteWhiteListsInput.


        :param cluster_name: The cluster_name of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def hostname(self):
        """Gets the hostname of this RangeForDeleteWhiteListsInput.  # noqa: E501


        :return: The hostname of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this RangeForDeleteWhiteListsInput.


        :param hostname: The hostname of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this RangeForDeleteWhiteListsInput.  # noqa: E501


        :return: The ip of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this RangeForDeleteWhiteListsInput.


        :param ip: The ip of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_global(self):
        """Gets the is_global of this RangeForDeleteWhiteListsInput.  # noqa: E501


        :return: The is_global of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :rtype: bool
        """
        return self._is_global

    @is_global.setter
    def is_global(self, is_global):
        """Sets the is_global of this RangeForDeleteWhiteListsInput.


        :param is_global: The is_global of this RangeForDeleteWhiteListsInput.  # noqa: E501
        :type: bool
        """

        self._is_global = is_global

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RangeForDeleteWhiteListsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RangeForDeleteWhiteListsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RangeForDeleteWhiteListsInput):
            return True

        return self.to_dict() != other.to_dict()
