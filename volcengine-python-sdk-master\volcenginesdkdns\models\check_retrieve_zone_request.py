# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckRetrieveZoneRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'retrieve_type': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'retrieve_type': 'RetrieveType',
        'zone_name': 'ZoneName'
    }

    def __init__(self, retrieve_type=None, zone_name=None, _configuration=None):  # noqa: E501
        """CheckRetrieveZoneRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._retrieve_type = None
        self._zone_name = None
        self.discriminator = None

        if retrieve_type is not None:
            self.retrieve_type = retrieve_type
        self.zone_name = zone_name

    @property
    def retrieve_type(self):
        """Gets the retrieve_type of this CheckRetrieveZoneRequest.  # noqa: E501


        :return: The retrieve_type of this CheckRetrieveZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._retrieve_type

    @retrieve_type.setter
    def retrieve_type(self, retrieve_type):
        """Sets the retrieve_type of this CheckRetrieveZoneRequest.


        :param retrieve_type: The retrieve_type of this CheckRetrieveZoneRequest.  # noqa: E501
        :type: str
        """

        self._retrieve_type = retrieve_type

    @property
    def zone_name(self):
        """Gets the zone_name of this CheckRetrieveZoneRequest.  # noqa: E501


        :return: The zone_name of this CheckRetrieveZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this CheckRetrieveZoneRequest.


        :param zone_name: The zone_name of this CheckRetrieveZoneRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_name is None:
            raise ValueError("Invalid value for `zone_name`, must not be `None`")  # noqa: E501

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckRetrieveZoneRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckRetrieveZoneRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckRetrieveZoneRequest):
            return True

        return self.to_dict() != other.to_dict()
