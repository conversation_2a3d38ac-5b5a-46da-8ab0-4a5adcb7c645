# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActivityCouponForCreateActivityCouponsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_close_icon': 'bool',
        'count': 'int',
        'coupon_id': 'int',
        'cut_off_time': 'int',
        'duration': 'int',
        'rule': 'int'
    }

    attribute_map = {
        'allow_close_icon': 'AllowCloseIcon',
        'count': 'Count',
        'coupon_id': 'CouponId',
        'cut_off_time': 'CutOffTime',
        'duration': 'Duration',
        'rule': 'Rule'
    }

    def __init__(self, allow_close_icon=None, count=None, coupon_id=None, cut_off_time=None, duration=None, rule=None, _configuration=None):  # noqa: E501
        """ActivityCouponForCreateActivityCouponsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_close_icon = None
        self._count = None
        self._coupon_id = None
        self._cut_off_time = None
        self._duration = None
        self._rule = None
        self.discriminator = None

        if allow_close_icon is not None:
            self.allow_close_icon = allow_close_icon
        if count is not None:
            self.count = count
        if coupon_id is not None:
            self.coupon_id = coupon_id
        if cut_off_time is not None:
            self.cut_off_time = cut_off_time
        if duration is not None:
            self.duration = duration
        if rule is not None:
            self.rule = rule

    @property
    def allow_close_icon(self):
        """Gets the allow_close_icon of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The allow_close_icon of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: bool
        """
        return self._allow_close_icon

    @allow_close_icon.setter
    def allow_close_icon(self, allow_close_icon):
        """Sets the allow_close_icon of this ActivityCouponForCreateActivityCouponsInput.


        :param allow_close_icon: The allow_close_icon of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: bool
        """

        self._allow_close_icon = allow_close_icon

    @property
    def count(self):
        """Gets the count of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The count of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ActivityCouponForCreateActivityCouponsInput.


        :param count: The count of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def coupon_id(self):
        """Gets the coupon_id of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The coupon_id of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: int
        """
        return self._coupon_id

    @coupon_id.setter
    def coupon_id(self, coupon_id):
        """Sets the coupon_id of this ActivityCouponForCreateActivityCouponsInput.


        :param coupon_id: The coupon_id of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: int
        """

        self._coupon_id = coupon_id

    @property
    def cut_off_time(self):
        """Gets the cut_off_time of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The cut_off_time of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: int
        """
        return self._cut_off_time

    @cut_off_time.setter
    def cut_off_time(self, cut_off_time):
        """Sets the cut_off_time of this ActivityCouponForCreateActivityCouponsInput.


        :param cut_off_time: The cut_off_time of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: int
        """

        self._cut_off_time = cut_off_time

    @property
    def duration(self):
        """Gets the duration of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The duration of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ActivityCouponForCreateActivityCouponsInput.


        :param duration: The duration of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def rule(self):
        """Gets the rule of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501


        :return: The rule of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :rtype: int
        """
        return self._rule

    @rule.setter
    def rule(self, rule):
        """Sets the rule of this ActivityCouponForCreateActivityCouponsInput.


        :param rule: The rule of this ActivityCouponForCreateActivityCouponsInput.  # noqa: E501
        :type: int
        """

        self._rule = rule

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActivityCouponForCreateActivityCouponsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActivityCouponForCreateActivityCouponsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActivityCouponForCreateActivityCouponsInput):
            return True

        return self.to_dict() != other.to_dict()
