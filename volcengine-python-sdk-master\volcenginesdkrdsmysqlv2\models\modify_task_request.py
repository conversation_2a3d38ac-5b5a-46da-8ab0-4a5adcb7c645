# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'modify_begin_time': 'str',
        'modify_end_time': 'str',
        'modify_type': 'str',
        'project_name': 'str',
        'switch_time': 'str',
        'task_event_operation': 'str',
        'task_id': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'modify_begin_time': 'ModifyBeginTime',
        'modify_end_time': 'ModifyEndTime',
        'modify_type': 'ModifyType',
        'project_name': 'ProjectName',
        'switch_time': 'SwitchTime',
        'task_event_operation': 'TaskEventOperation',
        'task_id': 'TaskId'
    }

    def __init__(self, instance_id=None, modify_begin_time=None, modify_end_time=None, modify_type=None, project_name=None, switch_time=None, task_event_operation=None, task_id=None, _configuration=None):  # noqa: E501
        """ModifyTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._modify_begin_time = None
        self._modify_end_time = None
        self._modify_type = None
        self._project_name = None
        self._switch_time = None
        self._task_event_operation = None
        self._task_id = None
        self.discriminator = None

        self.instance_id = instance_id
        if modify_begin_time is not None:
            self.modify_begin_time = modify_begin_time
        if modify_end_time is not None:
            self.modify_end_time = modify_end_time
        if modify_type is not None:
            self.modify_type = modify_type
        if project_name is not None:
            self.project_name = project_name
        if switch_time is not None:
            self.switch_time = switch_time
        self.task_event_operation = task_event_operation
        self.task_id = task_id

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyTaskRequest.  # noqa: E501


        :return: The instance_id of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyTaskRequest.


        :param instance_id: The instance_id of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def modify_begin_time(self):
        """Gets the modify_begin_time of this ModifyTaskRequest.  # noqa: E501


        :return: The modify_begin_time of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._modify_begin_time

    @modify_begin_time.setter
    def modify_begin_time(self, modify_begin_time):
        """Sets the modify_begin_time of this ModifyTaskRequest.


        :param modify_begin_time: The modify_begin_time of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """

        self._modify_begin_time = modify_begin_time

    @property
    def modify_end_time(self):
        """Gets the modify_end_time of this ModifyTaskRequest.  # noqa: E501


        :return: The modify_end_time of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._modify_end_time

    @modify_end_time.setter
    def modify_end_time(self, modify_end_time):
        """Sets the modify_end_time of this ModifyTaskRequest.


        :param modify_end_time: The modify_end_time of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """

        self._modify_end_time = modify_end_time

    @property
    def modify_type(self):
        """Gets the modify_type of this ModifyTaskRequest.  # noqa: E501


        :return: The modify_type of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._modify_type

    @modify_type.setter
    def modify_type(self, modify_type):
        """Sets the modify_type of this ModifyTaskRequest.


        :param modify_type: The modify_type of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """

        self._modify_type = modify_type

    @property
    def project_name(self):
        """Gets the project_name of this ModifyTaskRequest.  # noqa: E501


        :return: The project_name of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ModifyTaskRequest.


        :param project_name: The project_name of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def switch_time(self):
        """Gets the switch_time of this ModifyTaskRequest.  # noqa: E501


        :return: The switch_time of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_time

    @switch_time.setter
    def switch_time(self, switch_time):
        """Sets the switch_time of this ModifyTaskRequest.


        :param switch_time: The switch_time of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """

        self._switch_time = switch_time

    @property
    def task_event_operation(self):
        """Gets the task_event_operation of this ModifyTaskRequest.  # noqa: E501


        :return: The task_event_operation of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_event_operation

    @task_event_operation.setter
    def task_event_operation(self, task_event_operation):
        """Sets the task_event_operation of this ModifyTaskRequest.


        :param task_event_operation: The task_event_operation of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_event_operation is None:
            raise ValueError("Invalid value for `task_event_operation`, must not be `None`")  # noqa: E501

        self._task_event_operation = task_event_operation

    @property
    def task_id(self):
        """Gets the task_id of this ModifyTaskRequest.  # noqa: E501


        :return: The task_id of this ModifyTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this ModifyTaskRequest.


        :param task_id: The task_id of this ModifyTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_id is None:
            raise ValueError("Invalid value for `task_id`, must not be `None`")  # noqa: E501

        self._task_id = task_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
