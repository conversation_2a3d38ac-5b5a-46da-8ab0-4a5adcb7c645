# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BlockActionForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'error_page': 'str',
        'redirect_url': 'str',
        'status_code': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'error_page': 'ErrorPage',
        'redirect_url': 'RedirectUrl',
        'status_code': 'StatusCode'
    }

    def __init__(self, action=None, error_page=None, redirect_url=None, status_code=None, _configuration=None):  # noqa: E501
        """BlockActionForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._error_page = None
        self._redirect_url = None
        self._status_code = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if error_page is not None:
            self.error_page = error_page
        if redirect_url is not None:
            self.redirect_url = redirect_url
        if status_code is not None:
            self.status_code = status_code

    @property
    def action(self):
        """Gets the action of this BlockActionForAddCdnDomainInput.  # noqa: E501


        :return: The action of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this BlockActionForAddCdnDomainInput.


        :param action: The action of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def error_page(self):
        """Gets the error_page of this BlockActionForAddCdnDomainInput.  # noqa: E501


        :return: The error_page of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._error_page

    @error_page.setter
    def error_page(self, error_page):
        """Sets the error_page of this BlockActionForAddCdnDomainInput.


        :param error_page: The error_page of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._error_page = error_page

    @property
    def redirect_url(self):
        """Gets the redirect_url of this BlockActionForAddCdnDomainInput.  # noqa: E501


        :return: The redirect_url of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._redirect_url

    @redirect_url.setter
    def redirect_url(self, redirect_url):
        """Sets the redirect_url of this BlockActionForAddCdnDomainInput.


        :param redirect_url: The redirect_url of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._redirect_url = redirect_url

    @property
    def status_code(self):
        """Gets the status_code of this BlockActionForAddCdnDomainInput.  # noqa: E501


        :return: The status_code of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this BlockActionForAddCdnDomainInput.


        :param status_code: The status_code of this BlockActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._status_code = status_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BlockActionForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BlockActionForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BlockActionForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
