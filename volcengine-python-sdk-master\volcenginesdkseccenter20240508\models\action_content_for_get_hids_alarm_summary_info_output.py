# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActionContentForGetHidsAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'isolate_file_list': 'list[str]',
        'kill_proc_list': 'list[KillProcListForGetHidsAlarmSummaryInfoOutput]',
        'rm_file_list': 'list[str]'
    }

    attribute_map = {
        'isolate_file_list': 'IsolateFileList',
        'kill_proc_list': 'KillProcList',
        'rm_file_list': 'RmFileList'
    }

    def __init__(self, isolate_file_list=None, kill_proc_list=None, rm_file_list=None, _configuration=None):  # noqa: E501
        """ActionContentForGetHidsAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._isolate_file_list = None
        self._kill_proc_list = None
        self._rm_file_list = None
        self.discriminator = None

        if isolate_file_list is not None:
            self.isolate_file_list = isolate_file_list
        if kill_proc_list is not None:
            self.kill_proc_list = kill_proc_list
        if rm_file_list is not None:
            self.rm_file_list = rm_file_list

    @property
    def isolate_file_list(self):
        """Gets the isolate_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The isolate_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._isolate_file_list

    @isolate_file_list.setter
    def isolate_file_list(self, isolate_file_list):
        """Sets the isolate_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.


        :param isolate_file_list: The isolate_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._isolate_file_list = isolate_file_list

    @property
    def kill_proc_list(self):
        """Gets the kill_proc_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The kill_proc_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[KillProcListForGetHidsAlarmSummaryInfoOutput]
        """
        return self._kill_proc_list

    @kill_proc_list.setter
    def kill_proc_list(self, kill_proc_list):
        """Sets the kill_proc_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.


        :param kill_proc_list: The kill_proc_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[KillProcListForGetHidsAlarmSummaryInfoOutput]
        """

        self._kill_proc_list = kill_proc_list

    @property
    def rm_file_list(self):
        """Gets the rm_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The rm_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._rm_file_list

    @rm_file_list.setter
    def rm_file_list(self, rm_file_list):
        """Sets the rm_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.


        :param rm_file_list: The rm_file_list of this ActionContentForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._rm_file_list = rm_file_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActionContentForGetHidsAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActionContentForGetHidsAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActionContentForGetHidsAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
