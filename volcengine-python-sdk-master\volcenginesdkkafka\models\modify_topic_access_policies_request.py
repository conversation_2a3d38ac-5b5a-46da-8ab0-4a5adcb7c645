# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTopicAccessPoliciesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_policies': 'list[AccessPolicyForModifyTopicAccessPoliciesInput]',
        'all_authority': 'bool',
        'delete_policies': 'list[str]',
        'instance_id': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'access_policies': 'AccessPolicies',
        'all_authority': 'AllAuthority',
        'delete_policies': 'DeletePolicies',
        'instance_id': 'InstanceId',
        'topic_name': 'TopicName'
    }

    def __init__(self, access_policies=None, all_authority=None, delete_policies=None, instance_id=None, topic_name=None, _configuration=None):  # noqa: E501
        """ModifyTopicAccessPoliciesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_policies = None
        self._all_authority = None
        self._delete_policies = None
        self._instance_id = None
        self._topic_name = None
        self.discriminator = None

        if access_policies is not None:
            self.access_policies = access_policies
        self.all_authority = all_authority
        if delete_policies is not None:
            self.delete_policies = delete_policies
        self.instance_id = instance_id
        self.topic_name = topic_name

    @property
    def access_policies(self):
        """Gets the access_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501


        :return: The access_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :rtype: list[AccessPolicyForModifyTopicAccessPoliciesInput]
        """
        return self._access_policies

    @access_policies.setter
    def access_policies(self, access_policies):
        """Sets the access_policies of this ModifyTopicAccessPoliciesRequest.


        :param access_policies: The access_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :type: list[AccessPolicyForModifyTopicAccessPoliciesInput]
        """

        self._access_policies = access_policies

    @property
    def all_authority(self):
        """Gets the all_authority of this ModifyTopicAccessPoliciesRequest.  # noqa: E501


        :return: The all_authority of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this ModifyTopicAccessPoliciesRequest.


        :param all_authority: The all_authority of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and all_authority is None:
            raise ValueError("Invalid value for `all_authority`, must not be `None`")  # noqa: E501

        self._all_authority = all_authority

    @property
    def delete_policies(self):
        """Gets the delete_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501


        :return: The delete_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._delete_policies

    @delete_policies.setter
    def delete_policies(self, delete_policies):
        """Sets the delete_policies of this ModifyTopicAccessPoliciesRequest.


        :param delete_policies: The delete_policies of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :type: list[str]
        """

        self._delete_policies = delete_policies

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyTopicAccessPoliciesRequest.  # noqa: E501


        :return: The instance_id of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyTopicAccessPoliciesRequest.


        :param instance_id: The instance_id of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def topic_name(self):
        """Gets the topic_name of this ModifyTopicAccessPoliciesRequest.  # noqa: E501


        :return: The topic_name of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this ModifyTopicAccessPoliciesRequest.


        :param topic_name: The topic_name of this ModifyTopicAccessPoliciesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and topic_name is None:
            raise ValueError("Invalid value for `topic_name`, must not be `None`")  # noqa: E501

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTopicAccessPoliciesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTopicAccessPoliciesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTopicAccessPoliciesRequest):
            return True

        return self.to_dict() != other.to_dict()
