# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FeedbackForGetAlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'feedback_complement': 'str',
        'feedback_ioc_feature': 'str',
        'feedback_result': 'str',
        'feedback_tag': 'list[str]'
    }

    attribute_map = {
        'feedback_complement': 'FeedbackComplement',
        'feedback_ioc_feature': 'FeedbackIOCFeature',
        'feedback_result': 'FeedbackResult',
        'feedback_tag': 'FeedbackTag'
    }

    def __init__(self, feedback_complement=None, feedback_ioc_feature=None, feedback_result=None, feedback_tag=None, _configuration=None):  # noqa: E501
        """FeedbackForGetAlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._feedback_complement = None
        self._feedback_ioc_feature = None
        self._feedback_result = None
        self._feedback_tag = None
        self.discriminator = None

        if feedback_complement is not None:
            self.feedback_complement = feedback_complement
        if feedback_ioc_feature is not None:
            self.feedback_ioc_feature = feedback_ioc_feature
        if feedback_result is not None:
            self.feedback_result = feedback_result
        if feedback_tag is not None:
            self.feedback_tag = feedback_tag

    @property
    def feedback_complement(self):
        """Gets the feedback_complement of this FeedbackForGetAlarmDetailOutput.  # noqa: E501


        :return: The feedback_complement of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._feedback_complement

    @feedback_complement.setter
    def feedback_complement(self, feedback_complement):
        """Sets the feedback_complement of this FeedbackForGetAlarmDetailOutput.


        :param feedback_complement: The feedback_complement of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._feedback_complement = feedback_complement

    @property
    def feedback_ioc_feature(self):
        """Gets the feedback_ioc_feature of this FeedbackForGetAlarmDetailOutput.  # noqa: E501


        :return: The feedback_ioc_feature of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._feedback_ioc_feature

    @feedback_ioc_feature.setter
    def feedback_ioc_feature(self, feedback_ioc_feature):
        """Sets the feedback_ioc_feature of this FeedbackForGetAlarmDetailOutput.


        :param feedback_ioc_feature: The feedback_ioc_feature of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._feedback_ioc_feature = feedback_ioc_feature

    @property
    def feedback_result(self):
        """Gets the feedback_result of this FeedbackForGetAlarmDetailOutput.  # noqa: E501


        :return: The feedback_result of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._feedback_result

    @feedback_result.setter
    def feedback_result(self, feedback_result):
        """Sets the feedback_result of this FeedbackForGetAlarmDetailOutput.


        :param feedback_result: The feedback_result of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._feedback_result = feedback_result

    @property
    def feedback_tag(self):
        """Gets the feedback_tag of this FeedbackForGetAlarmDetailOutput.  # noqa: E501


        :return: The feedback_tag of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._feedback_tag

    @feedback_tag.setter
    def feedback_tag(self, feedback_tag):
        """Sets the feedback_tag of this FeedbackForGetAlarmDetailOutput.


        :param feedback_tag: The feedback_tag of this FeedbackForGetAlarmDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._feedback_tag = feedback_tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FeedbackForGetAlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FeedbackForGetAlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FeedbackForGetAlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
