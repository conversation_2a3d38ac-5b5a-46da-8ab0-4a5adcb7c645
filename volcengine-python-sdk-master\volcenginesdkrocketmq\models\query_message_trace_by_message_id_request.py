# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryMessageTraceByMessageIdRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'message_born_timestamp': 'int',
        'message_id': 'str',
        'need_produce_trace_info': 'bool',
        'query_end_timestamp': 'int',
        'query_start_timestamp': 'int',
        'topic': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'message_born_timestamp': 'MessageBornTimestamp',
        'message_id': 'MessageId',
        'need_produce_trace_info': 'NeedProduceTraceInfo',
        'query_end_timestamp': 'QueryEndTimestamp',
        'query_start_timestamp': 'QueryStartTimestamp',
        'topic': 'Topic'
    }

    def __init__(self, instance_id=None, message_born_timestamp=None, message_id=None, need_produce_trace_info=None, query_end_timestamp=None, query_start_timestamp=None, topic=None, _configuration=None):  # noqa: E501
        """QueryMessageTraceByMessageIdRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._message_born_timestamp = None
        self._message_id = None
        self._need_produce_trace_info = None
        self._query_end_timestamp = None
        self._query_start_timestamp = None
        self._topic = None
        self.discriminator = None

        self.instance_id = instance_id
        if message_born_timestamp is not None:
            self.message_born_timestamp = message_born_timestamp
        self.message_id = message_id
        self.need_produce_trace_info = need_produce_trace_info
        self.query_end_timestamp = query_end_timestamp
        self.query_start_timestamp = query_start_timestamp
        self.topic = topic

    @property
    def instance_id(self):
        """Gets the instance_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The instance_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this QueryMessageTraceByMessageIdRequest.


        :param instance_id: The instance_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def message_born_timestamp(self):
        """Gets the message_born_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The message_born_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: int
        """
        return self._message_born_timestamp

    @message_born_timestamp.setter
    def message_born_timestamp(self, message_born_timestamp):
        """Sets the message_born_timestamp of this QueryMessageTraceByMessageIdRequest.


        :param message_born_timestamp: The message_born_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: int
        """

        self._message_born_timestamp = message_born_timestamp

    @property
    def message_id(self):
        """Gets the message_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The message_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: str
        """
        return self._message_id

    @message_id.setter
    def message_id(self, message_id):
        """Sets the message_id of this QueryMessageTraceByMessageIdRequest.


        :param message_id: The message_id of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and message_id is None:
            raise ValueError("Invalid value for `message_id`, must not be `None`")  # noqa: E501

        self._message_id = message_id

    @property
    def need_produce_trace_info(self):
        """Gets the need_produce_trace_info of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The need_produce_trace_info of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: bool
        """
        return self._need_produce_trace_info

    @need_produce_trace_info.setter
    def need_produce_trace_info(self, need_produce_trace_info):
        """Sets the need_produce_trace_info of this QueryMessageTraceByMessageIdRequest.


        :param need_produce_trace_info: The need_produce_trace_info of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and need_produce_trace_info is None:
            raise ValueError("Invalid value for `need_produce_trace_info`, must not be `None`")  # noqa: E501

        self._need_produce_trace_info = need_produce_trace_info

    @property
    def query_end_timestamp(self):
        """Gets the query_end_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The query_end_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: int
        """
        return self._query_end_timestamp

    @query_end_timestamp.setter
    def query_end_timestamp(self, query_end_timestamp):
        """Sets the query_end_timestamp of this QueryMessageTraceByMessageIdRequest.


        :param query_end_timestamp: The query_end_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and query_end_timestamp is None:
            raise ValueError("Invalid value for `query_end_timestamp`, must not be `None`")  # noqa: E501

        self._query_end_timestamp = query_end_timestamp

    @property
    def query_start_timestamp(self):
        """Gets the query_start_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The query_start_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: int
        """
        return self._query_start_timestamp

    @query_start_timestamp.setter
    def query_start_timestamp(self, query_start_timestamp):
        """Sets the query_start_timestamp of this QueryMessageTraceByMessageIdRequest.


        :param query_start_timestamp: The query_start_timestamp of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and query_start_timestamp is None:
            raise ValueError("Invalid value for `query_start_timestamp`, must not be `None`")  # noqa: E501

        self._query_start_timestamp = query_start_timestamp

    @property
    def topic(self):
        """Gets the topic of this QueryMessageTraceByMessageIdRequest.  # noqa: E501


        :return: The topic of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :rtype: str
        """
        return self._topic

    @topic.setter
    def topic(self, topic):
        """Sets the topic of this QueryMessageTraceByMessageIdRequest.


        :param topic: The topic of this QueryMessageTraceByMessageIdRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and topic is None:
            raise ValueError("Invalid value for `topic`, must not be `None`")  # noqa: E501

        self._topic = topic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryMessageTraceByMessageIdRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryMessageTraceByMessageIdRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryMessageTraceByMessageIdRequest):
            return True

        return self.to_dict() != other.to_dict()
