# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TestWeComBotWebhookResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'error_message': 'str',
        'status_code': 'int',
        'we_com_code': 'int',
        'we_com_message': 'str'
    }

    attribute_map = {
        'error_message': 'ErrorMessage',
        'status_code': 'StatusCode',
        'we_com_code': 'WeComCode',
        'we_com_message': 'WeComMessage'
    }

    def __init__(self, error_message=None, status_code=None, we_com_code=None, we_com_message=None, _configuration=None):  # noqa: E501
        """TestWeComBotWebhookResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._error_message = None
        self._status_code = None
        self._we_com_code = None
        self._we_com_message = None
        self.discriminator = None

        if error_message is not None:
            self.error_message = error_message
        if status_code is not None:
            self.status_code = status_code
        if we_com_code is not None:
            self.we_com_code = we_com_code
        if we_com_message is not None:
            self.we_com_message = we_com_message

    @property
    def error_message(self):
        """Gets the error_message of this TestWeComBotWebhookResponse.  # noqa: E501


        :return: The error_message of this TestWeComBotWebhookResponse.  # noqa: E501
        :rtype: str
        """
        return self._error_message

    @error_message.setter
    def error_message(self, error_message):
        """Sets the error_message of this TestWeComBotWebhookResponse.


        :param error_message: The error_message of this TestWeComBotWebhookResponse.  # noqa: E501
        :type: str
        """

        self._error_message = error_message

    @property
    def status_code(self):
        """Gets the status_code of this TestWeComBotWebhookResponse.  # noqa: E501


        :return: The status_code of this TestWeComBotWebhookResponse.  # noqa: E501
        :rtype: int
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this TestWeComBotWebhookResponse.


        :param status_code: The status_code of this TestWeComBotWebhookResponse.  # noqa: E501
        :type: int
        """

        self._status_code = status_code

    @property
    def we_com_code(self):
        """Gets the we_com_code of this TestWeComBotWebhookResponse.  # noqa: E501


        :return: The we_com_code of this TestWeComBotWebhookResponse.  # noqa: E501
        :rtype: int
        """
        return self._we_com_code

    @we_com_code.setter
    def we_com_code(self, we_com_code):
        """Sets the we_com_code of this TestWeComBotWebhookResponse.


        :param we_com_code: The we_com_code of this TestWeComBotWebhookResponse.  # noqa: E501
        :type: int
        """

        self._we_com_code = we_com_code

    @property
    def we_com_message(self):
        """Gets the we_com_message of this TestWeComBotWebhookResponse.  # noqa: E501


        :return: The we_com_message of this TestWeComBotWebhookResponse.  # noqa: E501
        :rtype: str
        """
        return self._we_com_message

    @we_com_message.setter
    def we_com_message(self, we_com_message):
        """Sets the we_com_message of this TestWeComBotWebhookResponse.


        :param we_com_message: The we_com_message of this TestWeComBotWebhookResponse.  # noqa: E501
        :type: str
        """

        self._we_com_message = we_com_message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TestWeComBotWebhookResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TestWeComBotWebhookResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TestWeComBotWebhookResponse):
            return True

        return self.to_dict() != other.to_dict()
