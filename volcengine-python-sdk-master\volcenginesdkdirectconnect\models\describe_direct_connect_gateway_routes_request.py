# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDirectConnectGatewayRoutesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'destination_cidr_block': 'str',
        'direct_connect_gateway_id': 'str',
        'direct_connect_gateway_route_ids': 'list[str]',
        'next_hop_id': 'str',
        'next_hop_type': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'route_type': 'str'
    }

    attribute_map = {
        'destination_cidr_block': 'DestinationCidrBlock',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'direct_connect_gateway_route_ids': 'DirectConnectGatewayRouteIds',
        'next_hop_id': 'NextHopId',
        'next_hop_type': 'NextHopType',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'route_type': 'RouteType'
    }

    def __init__(self, destination_cidr_block=None, direct_connect_gateway_id=None, direct_connect_gateway_route_ids=None, next_hop_id=None, next_hop_type=None, page_number=None, page_size=None, route_type=None, _configuration=None):  # noqa: E501
        """DescribeDirectConnectGatewayRoutesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._destination_cidr_block = None
        self._direct_connect_gateway_id = None
        self._direct_connect_gateway_route_ids = None
        self._next_hop_id = None
        self._next_hop_type = None
        self._page_number = None
        self._page_size = None
        self._route_type = None
        self.discriminator = None

        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if direct_connect_gateway_route_ids is not None:
            self.direct_connect_gateway_route_ids = direct_connect_gateway_route_ids
        if next_hop_id is not None:
            self.next_hop_id = next_hop_id
        if next_hop_type is not None:
            self.next_hop_type = next_hop_type
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if route_type is not None:
            self.route_type = route_type

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The destination_cidr_block of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this DescribeDirectConnectGatewayRoutesRequest.


        :param destination_cidr_block: The destination_cidr_block of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The direct_connect_gateway_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this DescribeDirectConnectGatewayRoutesRequest.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def direct_connect_gateway_route_ids(self):
        """Gets the direct_connect_gateway_route_ids of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The direct_connect_gateway_route_ids of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._direct_connect_gateway_route_ids

    @direct_connect_gateway_route_ids.setter
    def direct_connect_gateway_route_ids(self, direct_connect_gateway_route_ids):
        """Sets the direct_connect_gateway_route_ids of this DescribeDirectConnectGatewayRoutesRequest.


        :param direct_connect_gateway_route_ids: The direct_connect_gateway_route_ids of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: list[str]
        """

        self._direct_connect_gateway_route_ids = direct_connect_gateway_route_ids

    @property
    def next_hop_id(self):
        """Gets the next_hop_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The next_hop_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_id

    @next_hop_id.setter
    def next_hop_id(self, next_hop_id):
        """Sets the next_hop_id of this DescribeDirectConnectGatewayRoutesRequest.


        :param next_hop_id: The next_hop_id of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: str
        """

        self._next_hop_id = next_hop_id

    @property
    def next_hop_type(self):
        """Gets the next_hop_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The next_hop_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_type

    @next_hop_type.setter
    def next_hop_type(self, next_hop_type):
        """Sets the next_hop_type of this DescribeDirectConnectGatewayRoutesRequest.


        :param next_hop_type: The next_hop_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: str
        """

        self._next_hop_type = next_hop_type

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The page_number of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDirectConnectGatewayRoutesRequest.


        :param page_number: The page_number of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The page_size of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDirectConnectGatewayRoutesRequest.


        :param page_size: The page_size of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def route_type(self):
        """Gets the route_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501


        :return: The route_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._route_type

    @route_type.setter
    def route_type(self, route_type):
        """Sets the route_type of this DescribeDirectConnectGatewayRoutesRequest.


        :param route_type: The route_type of this DescribeDirectConnectGatewayRoutesRequest.  # noqa: E501
        :type: str
        """

        self._route_type = route_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDirectConnectGatewayRoutesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDirectConnectGatewayRoutesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDirectConnectGatewayRoutesRequest):
            return True

        return self.to_dict() != other.to_dict()
