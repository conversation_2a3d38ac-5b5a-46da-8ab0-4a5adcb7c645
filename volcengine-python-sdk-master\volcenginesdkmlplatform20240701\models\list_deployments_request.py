# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDeploymentsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time_after': 'str',
        'create_time_before': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'resource_group_id': 'str',
        'resource_queue_id': 'str',
        'service_id': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'StatusForListDeploymentsInput',
        'update_time_after': 'str',
        'update_time_before': 'str'
    }

    attribute_map = {
        'create_time_after': 'CreateTimeAfter',
        'create_time_before': 'CreateTimeBefore',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'resource_group_id': 'ResourceGroupId',
        'resource_queue_id': 'ResourceQueueId',
        'service_id': 'ServiceId',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'update_time_after': 'UpdateTimeAfter',
        'update_time_before': 'UpdateTimeBefore'
    }

    def __init__(self, create_time_after=None, create_time_before=None, page_number=None, page_size=None, resource_group_id=None, resource_queue_id=None, service_id=None, sort_by=None, sort_order=None, status=None, update_time_after=None, update_time_before=None, _configuration=None):  # noqa: E501
        """ListDeploymentsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time_after = None
        self._create_time_before = None
        self._page_number = None
        self._page_size = None
        self._resource_group_id = None
        self._resource_queue_id = None
        self._service_id = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._update_time_after = None
        self._update_time_before = None
        self.discriminator = None

        if create_time_after is not None:
            self.create_time_after = create_time_after
        if create_time_before is not None:
            self.create_time_before = create_time_before
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if resource_group_id is not None:
            self.resource_group_id = resource_group_id
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if service_id is not None:
            self.service_id = service_id
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if update_time_after is not None:
            self.update_time_after = update_time_after
        if update_time_before is not None:
            self.update_time_before = update_time_before

    @property
    def create_time_after(self):
        """Gets the create_time_after of this ListDeploymentsRequest.  # noqa: E501


        :return: The create_time_after of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_after

    @create_time_after.setter
    def create_time_after(self, create_time_after):
        """Sets the create_time_after of this ListDeploymentsRequest.


        :param create_time_after: The create_time_after of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._create_time_after = create_time_after

    @property
    def create_time_before(self):
        """Gets the create_time_before of this ListDeploymentsRequest.  # noqa: E501


        :return: The create_time_before of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_before

    @create_time_before.setter
    def create_time_before(self, create_time_before):
        """Sets the create_time_before of this ListDeploymentsRequest.


        :param create_time_before: The create_time_before of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._create_time_before = create_time_before

    @property
    def page_number(self):
        """Gets the page_number of this ListDeploymentsRequest.  # noqa: E501


        :return: The page_number of this ListDeploymentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListDeploymentsRequest.


        :param page_number: The page_number of this ListDeploymentsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListDeploymentsRequest.  # noqa: E501


        :return: The page_size of this ListDeploymentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListDeploymentsRequest.


        :param page_size: The page_size of this ListDeploymentsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size < 10):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value greater than or equal to `10`")  # noqa: E501

        self._page_size = page_size

    @property
    def resource_group_id(self):
        """Gets the resource_group_id of this ListDeploymentsRequest.  # noqa: E501


        :return: The resource_group_id of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_group_id

    @resource_group_id.setter
    def resource_group_id(self, resource_group_id):
        """Sets the resource_group_id of this ListDeploymentsRequest.


        :param resource_group_id: The resource_group_id of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._resource_group_id = resource_group_id

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this ListDeploymentsRequest.  # noqa: E501


        :return: The resource_queue_id of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this ListDeploymentsRequest.


        :param resource_queue_id: The resource_queue_id of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def service_id(self):
        """Gets the service_id of this ListDeploymentsRequest.  # noqa: E501


        :return: The service_id of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this ListDeploymentsRequest.


        :param service_id: The service_id of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def sort_by(self):
        """Gets the sort_by of this ListDeploymentsRequest.  # noqa: E501


        :return: The sort_by of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListDeploymentsRequest.


        :param sort_by: The sort_by of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreateTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListDeploymentsRequest.  # noqa: E501


        :return: The sort_order of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListDeploymentsRequest.


        :param sort_order: The sort_order of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Ascend", "Descend"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListDeploymentsRequest.  # noqa: E501


        :return: The status of this ListDeploymentsRequest.  # noqa: E501
        :rtype: StatusForListDeploymentsInput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListDeploymentsRequest.


        :param status: The status of this ListDeploymentsRequest.  # noqa: E501
        :type: StatusForListDeploymentsInput
        """

        self._status = status

    @property
    def update_time_after(self):
        """Gets the update_time_after of this ListDeploymentsRequest.  # noqa: E501


        :return: The update_time_after of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_time_after

    @update_time_after.setter
    def update_time_after(self, update_time_after):
        """Sets the update_time_after of this ListDeploymentsRequest.


        :param update_time_after: The update_time_after of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._update_time_after = update_time_after

    @property
    def update_time_before(self):
        """Gets the update_time_before of this ListDeploymentsRequest.  # noqa: E501


        :return: The update_time_before of this ListDeploymentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_time_before

    @update_time_before.setter
    def update_time_before(self, update_time_before):
        """Sets the update_time_before of this ListDeploymentsRequest.


        :param update_time_before: The update_time_before of this ListDeploymentsRequest.  # noqa: E501
        :type: str
        """

        self._update_time_before = update_time_before

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDeploymentsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDeploymentsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDeploymentsRequest):
            return True

        return self.to_dict() != other.to_dict()
