# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HistoryVersionForGetServiceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apig_config': 'ApigConfigForGetServiceOutput',
        'clb_config': 'CLBConfigForGetServiceOutput',
        'change_type': 'str',
        'create_time': 'str',
        'id': 'str',
        'ports': 'list[PortForGetServiceOutput]',
        'traffic_config': 'TrafficConfigForGetServiceOutput',
        'vpc_id': 'str'
    }

    attribute_map = {
        'apig_config': 'ApigConfig',
        'clb_config': 'CLBConfig',
        'change_type': 'ChangeType',
        'create_time': 'CreateTime',
        'id': 'Id',
        'ports': 'Ports',
        'traffic_config': 'TrafficConfig',
        'vpc_id': 'VpcId'
    }

    def __init__(self, apig_config=None, clb_config=None, change_type=None, create_time=None, id=None, ports=None, traffic_config=None, vpc_id=None, _configuration=None):  # noqa: E501
        """HistoryVersionForGetServiceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apig_config = None
        self._clb_config = None
        self._change_type = None
        self._create_time = None
        self._id = None
        self._ports = None
        self._traffic_config = None
        self._vpc_id = None
        self.discriminator = None

        if apig_config is not None:
            self.apig_config = apig_config
        if clb_config is not None:
            self.clb_config = clb_config
        if change_type is not None:
            self.change_type = change_type
        if create_time is not None:
            self.create_time = create_time
        if id is not None:
            self.id = id
        if ports is not None:
            self.ports = ports
        if traffic_config is not None:
            self.traffic_config = traffic_config
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def apig_config(self):
        """Gets the apig_config of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The apig_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: ApigConfigForGetServiceOutput
        """
        return self._apig_config

    @apig_config.setter
    def apig_config(self, apig_config):
        """Sets the apig_config of this HistoryVersionForGetServiceOutput.


        :param apig_config: The apig_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: ApigConfigForGetServiceOutput
        """

        self._apig_config = apig_config

    @property
    def clb_config(self):
        """Gets the clb_config of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The clb_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: CLBConfigForGetServiceOutput
        """
        return self._clb_config

    @clb_config.setter
    def clb_config(self, clb_config):
        """Sets the clb_config of this HistoryVersionForGetServiceOutput.


        :param clb_config: The clb_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: CLBConfigForGetServiceOutput
        """

        self._clb_config = clb_config

    @property
    def change_type(self):
        """Gets the change_type of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The change_type of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._change_type

    @change_type.setter
    def change_type(self, change_type):
        """Sets the change_type of this HistoryVersionForGetServiceOutput.


        :param change_type: The change_type of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._change_type = change_type

    @property
    def create_time(self):
        """Gets the create_time of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The create_time of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this HistoryVersionForGetServiceOutput.


        :param create_time: The create_time of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def id(self):
        """Gets the id of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The id of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this HistoryVersionForGetServiceOutput.


        :param id: The id of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def ports(self):
        """Gets the ports of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The ports of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: list[PortForGetServiceOutput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this HistoryVersionForGetServiceOutput.


        :param ports: The ports of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: list[PortForGetServiceOutput]
        """

        self._ports = ports

    @property
    def traffic_config(self):
        """Gets the traffic_config of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The traffic_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: TrafficConfigForGetServiceOutput
        """
        return self._traffic_config

    @traffic_config.setter
    def traffic_config(self, traffic_config):
        """Sets the traffic_config of this HistoryVersionForGetServiceOutput.


        :param traffic_config: The traffic_config of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: TrafficConfigForGetServiceOutput
        """

        self._traffic_config = traffic_config

    @property
    def vpc_id(self):
        """Gets the vpc_id of this HistoryVersionForGetServiceOutput.  # noqa: E501


        :return: The vpc_id of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this HistoryVersionForGetServiceOutput.


        :param vpc_id: The vpc_id of this HistoryVersionForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HistoryVersionForGetServiceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HistoryVersionForGetServiceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HistoryVersionForGetServiceOutput):
            return True

        return self.to_dict() != other.to_dict()
