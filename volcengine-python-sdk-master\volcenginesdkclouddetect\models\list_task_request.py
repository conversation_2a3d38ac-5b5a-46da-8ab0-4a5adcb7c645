# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'id': 'str',
        'name': 'str',
        'owner': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'project_name': 'str'
    }

    attribute_map = {
        'address': 'Address',
        'id': 'ID',
        'name': 'Name',
        'owner': 'Owner',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_name': 'ProjectName'
    }

    def __init__(self, address=None, id=None, name=None, owner=None, page_num=None, page_size=None, project_name=None, _configuration=None):  # noqa: E501
        """ListTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._id = None
        self._name = None
        self._owner = None
        self._page_num = None
        self._page_size = None
        self._project_name = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if owner is not None:
            self.owner = owner
        self.page_num = page_num
        self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name

    @property
    def address(self):
        """Gets the address of this ListTaskRequest.  # noqa: E501


        :return: The address of this ListTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this ListTaskRequest.


        :param address: The address of this ListTaskRequest.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def id(self):
        """Gets the id of this ListTaskRequest.  # noqa: E501


        :return: The id of this ListTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ListTaskRequest.


        :param id: The id of this ListTaskRequest.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ListTaskRequest.  # noqa: E501


        :return: The name of this ListTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListTaskRequest.


        :param name: The name of this ListTaskRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner(self):
        """Gets the owner of this ListTaskRequest.  # noqa: E501


        :return: The owner of this ListTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this ListTaskRequest.


        :param owner: The owner of this ListTaskRequest.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def page_num(self):
        """Gets the page_num of this ListTaskRequest.  # noqa: E501


        :return: The page_num of this ListTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListTaskRequest.


        :param page_num: The page_num of this ListTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_num is None:
            raise ValueError("Invalid value for `page_num`, must not be `None`")  # noqa: E501

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListTaskRequest.  # noqa: E501


        :return: The page_size of this ListTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListTaskRequest.


        :param page_size: The page_size of this ListTaskRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListTaskRequest.  # noqa: E501


        :return: The project_name of this ListTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListTaskRequest.


        :param project_name: The project_name of this ListTaskRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
