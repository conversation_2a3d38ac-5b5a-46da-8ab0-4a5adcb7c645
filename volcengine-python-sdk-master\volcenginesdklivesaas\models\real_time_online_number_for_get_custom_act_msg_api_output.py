# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RealTimeOnlineNumberForGetCustomActMsgAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fake_popularity': 'int',
        'real_popularity': 'int'
    }

    attribute_map = {
        'fake_popularity': 'FakePopularity',
        'real_popularity': 'RealPopularity'
    }

    def __init__(self, fake_popularity=None, real_popularity=None, _configuration=None):  # noqa: E501
        """RealTimeOnlineNumberForGetCustomActMsgAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fake_popularity = None
        self._real_popularity = None
        self.discriminator = None

        if fake_popularity is not None:
            self.fake_popularity = fake_popularity
        if real_popularity is not None:
            self.real_popularity = real_popularity

    @property
    def fake_popularity(self):
        """Gets the fake_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The fake_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._fake_popularity

    @fake_popularity.setter
    def fake_popularity(self, fake_popularity):
        """Sets the fake_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.


        :param fake_popularity: The fake_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._fake_popularity = fake_popularity

    @property
    def real_popularity(self):
        """Gets the real_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The real_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._real_popularity

    @real_popularity.setter
    def real_popularity(self, real_popularity):
        """Sets the real_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.


        :param real_popularity: The real_popularity of this RealTimeOnlineNumberForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._real_popularity = real_popularity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RealTimeOnlineNumberForGetCustomActMsgAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RealTimeOnlineNumberForGetCustomActMsgAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RealTimeOnlineNumberForGetCustomActMsgAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
