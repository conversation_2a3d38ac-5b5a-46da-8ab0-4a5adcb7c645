# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatisticsResultForDescribeStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'detail_info': 'list[DetailInfoForDescribeStatisticsOutput]',
        'listener_id': 'str',
        'region': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'detail_info': 'DetailInfo',
        'listener_id': 'ListenerId',
        'region': 'Region'
    }

    def __init__(self, accelerator_id=None, detail_info=None, listener_id=None, region=None, _configuration=None):  # noqa: E501
        """StatisticsResultForDescribeStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._detail_info = None
        self._listener_id = None
        self._region = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if detail_info is not None:
            self.detail_info = detail_info
        if listener_id is not None:
            self.listener_id = listener_id
        if region is not None:
            self.region = region

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The accelerator_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this StatisticsResultForDescribeStatisticsOutput.


        :param accelerator_id: The accelerator_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def detail_info(self):
        """Gets the detail_info of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The detail_info of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: list[DetailInfoForDescribeStatisticsOutput]
        """
        return self._detail_info

    @detail_info.setter
    def detail_info(self, detail_info):
        """Sets the detail_info of this StatisticsResultForDescribeStatisticsOutput.


        :param detail_info: The detail_info of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :type: list[DetailInfoForDescribeStatisticsOutput]
        """

        self._detail_info = detail_info

    @property
    def listener_id(self):
        """Gets the listener_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The listener_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this StatisticsResultForDescribeStatisticsOutput.


        :param listener_id: The listener_id of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def region(self):
        """Gets the region of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The region of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this StatisticsResultForDescribeStatisticsOutput.


        :param region: The region of this StatisticsResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatisticsResultForDescribeStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatisticsResultForDescribeStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatisticsResultForDescribeStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
