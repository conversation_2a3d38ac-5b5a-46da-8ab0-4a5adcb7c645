# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PageDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnose_detail': 'DiagnoseDetailForGetTaskResultOutput',
        'http_request_header': 'str',
        'http_response_header': 'str',
        'page_count_element': 'int',
        'page_count_error_element': 'int',
        'page_download_bytes_total': 'int',
        'page_elem_speed_avg': 'float',
        'page_element_speed_avg': 'int',
        'page_error_elem_proportion': 'float',
        'page_error_element_proportion': 'int',
        'page_request_id': 'str',
        'page_time_first_screen': 'int',
        'page_time_total': 'int'
    }

    attribute_map = {
        'diagnose_detail': 'DiagnoseDetail',
        'http_request_header': 'HTTPRequestHeader',
        'http_response_header': 'HTTPResponseHeader',
        'page_count_element': 'PageCountElement',
        'page_count_error_element': 'PageCountErrorElement',
        'page_download_bytes_total': 'PageDownloadBytesTotal',
        'page_elem_speed_avg': 'PageElemSpeedAvg',
        'page_element_speed_avg': 'PageElementSpeedAvg',
        'page_error_elem_proportion': 'PageErrorElemProportion',
        'page_error_element_proportion': 'PageErrorElementProportion',
        'page_request_id': 'PageRequestID',
        'page_time_first_screen': 'PageTimeFirstScreen',
        'page_time_total': 'PageTimeTotal'
    }

    def __init__(self, diagnose_detail=None, http_request_header=None, http_response_header=None, page_count_element=None, page_count_error_element=None, page_download_bytes_total=None, page_elem_speed_avg=None, page_element_speed_avg=None, page_error_elem_proportion=None, page_error_element_proportion=None, page_request_id=None, page_time_first_screen=None, page_time_total=None, _configuration=None):  # noqa: E501
        """PageDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnose_detail = None
        self._http_request_header = None
        self._http_response_header = None
        self._page_count_element = None
        self._page_count_error_element = None
        self._page_download_bytes_total = None
        self._page_elem_speed_avg = None
        self._page_element_speed_avg = None
        self._page_error_elem_proportion = None
        self._page_error_element_proportion = None
        self._page_request_id = None
        self._page_time_first_screen = None
        self._page_time_total = None
        self.discriminator = None

        if diagnose_detail is not None:
            self.diagnose_detail = diagnose_detail
        if http_request_header is not None:
            self.http_request_header = http_request_header
        if http_response_header is not None:
            self.http_response_header = http_response_header
        if page_count_element is not None:
            self.page_count_element = page_count_element
        if page_count_error_element is not None:
            self.page_count_error_element = page_count_error_element
        if page_download_bytes_total is not None:
            self.page_download_bytes_total = page_download_bytes_total
        if page_elem_speed_avg is not None:
            self.page_elem_speed_avg = page_elem_speed_avg
        if page_element_speed_avg is not None:
            self.page_element_speed_avg = page_element_speed_avg
        if page_error_elem_proportion is not None:
            self.page_error_elem_proportion = page_error_elem_proportion
        if page_error_element_proportion is not None:
            self.page_error_element_proportion = page_error_element_proportion
        if page_request_id is not None:
            self.page_request_id = page_request_id
        if page_time_first_screen is not None:
            self.page_time_first_screen = page_time_first_screen
        if page_time_total is not None:
            self.page_time_total = page_time_total

    @property
    def diagnose_detail(self):
        """Gets the diagnose_detail of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The diagnose_detail of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: DiagnoseDetailForGetTaskResultOutput
        """
        return self._diagnose_detail

    @diagnose_detail.setter
    def diagnose_detail(self, diagnose_detail):
        """Sets the diagnose_detail of this PageDetailForGetTaskResultOutput.


        :param diagnose_detail: The diagnose_detail of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: DiagnoseDetailForGetTaskResultOutput
        """

        self._diagnose_detail = diagnose_detail

    @property
    def http_request_header(self):
        """Gets the http_request_header of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_request_header of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_request_header

    @http_request_header.setter
    def http_request_header(self, http_request_header):
        """Sets the http_request_header of this PageDetailForGetTaskResultOutput.


        :param http_request_header: The http_request_header of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_request_header = http_request_header

    @property
    def http_response_header(self):
        """Gets the http_response_header of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_response_header of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_response_header

    @http_response_header.setter
    def http_response_header(self, http_response_header):
        """Sets the http_response_header of this PageDetailForGetTaskResultOutput.


        :param http_response_header: The http_response_header of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_response_header = http_response_header

    @property
    def page_count_element(self):
        """Gets the page_count_element of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_count_element of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_count_element

    @page_count_element.setter
    def page_count_element(self, page_count_element):
        """Sets the page_count_element of this PageDetailForGetTaskResultOutput.


        :param page_count_element: The page_count_element of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_count_element = page_count_element

    @property
    def page_count_error_element(self):
        """Gets the page_count_error_element of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_count_error_element of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_count_error_element

    @page_count_error_element.setter
    def page_count_error_element(self, page_count_error_element):
        """Sets the page_count_error_element of this PageDetailForGetTaskResultOutput.


        :param page_count_error_element: The page_count_error_element of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_count_error_element = page_count_error_element

    @property
    def page_download_bytes_total(self):
        """Gets the page_download_bytes_total of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_download_bytes_total of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_download_bytes_total

    @page_download_bytes_total.setter
    def page_download_bytes_total(self, page_download_bytes_total):
        """Sets the page_download_bytes_total of this PageDetailForGetTaskResultOutput.


        :param page_download_bytes_total: The page_download_bytes_total of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_download_bytes_total = page_download_bytes_total

    @property
    def page_elem_speed_avg(self):
        """Gets the page_elem_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_elem_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._page_elem_speed_avg

    @page_elem_speed_avg.setter
    def page_elem_speed_avg(self, page_elem_speed_avg):
        """Sets the page_elem_speed_avg of this PageDetailForGetTaskResultOutput.


        :param page_elem_speed_avg: The page_elem_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._page_elem_speed_avg = page_elem_speed_avg

    @property
    def page_element_speed_avg(self):
        """Gets the page_element_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_element_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_element_speed_avg

    @page_element_speed_avg.setter
    def page_element_speed_avg(self, page_element_speed_avg):
        """Sets the page_element_speed_avg of this PageDetailForGetTaskResultOutput.


        :param page_element_speed_avg: The page_element_speed_avg of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_element_speed_avg = page_element_speed_avg

    @property
    def page_error_elem_proportion(self):
        """Gets the page_error_elem_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_error_elem_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._page_error_elem_proportion

    @page_error_elem_proportion.setter
    def page_error_elem_proportion(self, page_error_elem_proportion):
        """Sets the page_error_elem_proportion of this PageDetailForGetTaskResultOutput.


        :param page_error_elem_proportion: The page_error_elem_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._page_error_elem_proportion = page_error_elem_proportion

    @property
    def page_error_element_proportion(self):
        """Gets the page_error_element_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_error_element_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_error_element_proportion

    @page_error_element_proportion.setter
    def page_error_element_proportion(self, page_error_element_proportion):
        """Sets the page_error_element_proportion of this PageDetailForGetTaskResultOutput.


        :param page_error_element_proportion: The page_error_element_proportion of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_error_element_proportion = page_error_element_proportion

    @property
    def page_request_id(self):
        """Gets the page_request_id of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_request_id of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._page_request_id

    @page_request_id.setter
    def page_request_id(self, page_request_id):
        """Sets the page_request_id of this PageDetailForGetTaskResultOutput.


        :param page_request_id: The page_request_id of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._page_request_id = page_request_id

    @property
    def page_time_first_screen(self):
        """Gets the page_time_first_screen of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_time_first_screen of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_time_first_screen

    @page_time_first_screen.setter
    def page_time_first_screen(self, page_time_first_screen):
        """Sets the page_time_first_screen of this PageDetailForGetTaskResultOutput.


        :param page_time_first_screen: The page_time_first_screen of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_time_first_screen = page_time_first_screen

    @property
    def page_time_total(self):
        """Gets the page_time_total of this PageDetailForGetTaskResultOutput.  # noqa: E501


        :return: The page_time_total of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_time_total

    @page_time_total.setter
    def page_time_total(self, page_time_total):
        """Sets the page_time_total of this PageDetailForGetTaskResultOutput.


        :param page_time_total: The page_time_total of this PageDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._page_time_total = page_time_total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PageDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PageDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PageDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
