# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SaleForDescribeZonesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_type': 'str',
        'protocol_type': 'str',
        'status': 'str',
        'storage_type': 'str'
    }

    attribute_map = {
        'file_system_type': 'FileSystemType',
        'protocol_type': 'ProtocolType',
        'status': 'Status',
        'storage_type': 'StorageType'
    }

    def __init__(self, file_system_type=None, protocol_type=None, status=None, storage_type=None, _configuration=None):  # noqa: E501
        """SaleForDescribeZonesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_type = None
        self._protocol_type = None
        self._status = None
        self._storage_type = None
        self.discriminator = None

        if file_system_type is not None:
            self.file_system_type = file_system_type
        if protocol_type is not None:
            self.protocol_type = protocol_type
        if status is not None:
            self.status = status
        if storage_type is not None:
            self.storage_type = storage_type

    @property
    def file_system_type(self):
        """Gets the file_system_type of this SaleForDescribeZonesOutput.  # noqa: E501


        :return: The file_system_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_type

    @file_system_type.setter
    def file_system_type(self, file_system_type):
        """Sets the file_system_type of this SaleForDescribeZonesOutput.


        :param file_system_type: The file_system_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_type = file_system_type

    @property
    def protocol_type(self):
        """Gets the protocol_type of this SaleForDescribeZonesOutput.  # noqa: E501


        :return: The protocol_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this SaleForDescribeZonesOutput.


        :param protocol_type: The protocol_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._protocol_type = protocol_type

    @property
    def status(self):
        """Gets the status of this SaleForDescribeZonesOutput.  # noqa: E501


        :return: The status of this SaleForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SaleForDescribeZonesOutput.


        :param status: The status of this SaleForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def storage_type(self):
        """Gets the storage_type of this SaleForDescribeZonesOutput.  # noqa: E501


        :return: The storage_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this SaleForDescribeZonesOutput.


        :param storage_type: The storage_type of this SaleForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SaleForDescribeZonesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SaleForDescribeZonesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SaleForDescribeZonesOutput):
            return True

        return self.to_dict() != other.to_dict()
