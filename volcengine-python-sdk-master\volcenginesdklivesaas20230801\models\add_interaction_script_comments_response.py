# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddInteractionScriptCommentsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fail_list': 'list[FailListForAddInteractionScriptCommentsOutput]',
        'latest_comment_id': 'int',
        'success_amount': 'int'
    }

    attribute_map = {
        'fail_list': 'FailList',
        'latest_comment_id': 'LatestCommentId',
        'success_amount': 'SuccessAmount'
    }

    def __init__(self, fail_list=None, latest_comment_id=None, success_amount=None, _configuration=None):  # noqa: E501
        """AddInteractionScriptCommentsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fail_list = None
        self._latest_comment_id = None
        self._success_amount = None
        self.discriminator = None

        if fail_list is not None:
            self.fail_list = fail_list
        if latest_comment_id is not None:
            self.latest_comment_id = latest_comment_id
        if success_amount is not None:
            self.success_amount = success_amount

    @property
    def fail_list(self):
        """Gets the fail_list of this AddInteractionScriptCommentsResponse.  # noqa: E501


        :return: The fail_list of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :rtype: list[FailListForAddInteractionScriptCommentsOutput]
        """
        return self._fail_list

    @fail_list.setter
    def fail_list(self, fail_list):
        """Sets the fail_list of this AddInteractionScriptCommentsResponse.


        :param fail_list: The fail_list of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :type: list[FailListForAddInteractionScriptCommentsOutput]
        """

        self._fail_list = fail_list

    @property
    def latest_comment_id(self):
        """Gets the latest_comment_id of this AddInteractionScriptCommentsResponse.  # noqa: E501


        :return: The latest_comment_id of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :rtype: int
        """
        return self._latest_comment_id

    @latest_comment_id.setter
    def latest_comment_id(self, latest_comment_id):
        """Sets the latest_comment_id of this AddInteractionScriptCommentsResponse.


        :param latest_comment_id: The latest_comment_id of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :type: int
        """

        self._latest_comment_id = latest_comment_id

    @property
    def success_amount(self):
        """Gets the success_amount of this AddInteractionScriptCommentsResponse.  # noqa: E501


        :return: The success_amount of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :rtype: int
        """
        return self._success_amount

    @success_amount.setter
    def success_amount(self, success_amount):
        """Sets the success_amount of this AddInteractionScriptCommentsResponse.


        :param success_amount: The success_amount of this AddInteractionScriptCommentsResponse.  # noqa: E501
        :type: int
        """

        self._success_amount = success_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddInteractionScriptCommentsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddInteractionScriptCommentsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddInteractionScriptCommentsResponse):
            return True

        return self.to_dict() != other.to_dict()
