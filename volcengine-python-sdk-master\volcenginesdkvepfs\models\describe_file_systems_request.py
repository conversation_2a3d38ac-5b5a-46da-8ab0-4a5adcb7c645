# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeFileSystemsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_ids': 'str',
        'filters': 'list[FilterForDescribeFileSystemsInput]',
        'language_code': 'str',
        'order_by': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project': 'str',
        'tag_filters': 'list[TagFilterForDescribeFileSystemsInput]'
    }

    attribute_map = {
        'file_system_ids': 'FileSystemIds',
        'filters': 'Filters',
        'language_code': 'LanguageCode',
        'order_by': 'OrderBy',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project': 'Project',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, file_system_ids=None, filters=None, language_code=None, order_by=None, page_number=None, page_size=None, project=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeFileSystemsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_ids = None
        self._filters = None
        self._language_code = None
        self._order_by = None
        self._page_number = None
        self._page_size = None
        self._project = None
        self._tag_filters = None
        self.discriminator = None

        if file_system_ids is not None:
            self.file_system_ids = file_system_ids
        if filters is not None:
            self.filters = filters
        if language_code is not None:
            self.language_code = language_code
        if order_by is not None:
            self.order_by = order_by
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project is not None:
            self.project = project
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def file_system_ids(self):
        """Gets the file_system_ids of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The file_system_ids of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_ids

    @file_system_ids.setter
    def file_system_ids(self, file_system_ids):
        """Sets the file_system_ids of this DescribeFileSystemsRequest.


        :param file_system_ids: The file_system_ids of this DescribeFileSystemsRequest.  # noqa: E501
        :type: str
        """

        self._file_system_ids = file_system_ids

    @property
    def filters(self):
        """Gets the filters of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The filters of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: list[FilterForDescribeFileSystemsInput]
        """
        return self._filters

    @filters.setter
    def filters(self, filters):
        """Sets the filters of this DescribeFileSystemsRequest.


        :param filters: The filters of this DescribeFileSystemsRequest.  # noqa: E501
        :type: list[FilterForDescribeFileSystemsInput]
        """

        self._filters = filters

    @property
    def language_code(self):
        """Gets the language_code of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The language_code of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: str
        """
        return self._language_code

    @language_code.setter
    def language_code(self, language_code):
        """Sets the language_code of this DescribeFileSystemsRequest.


        :param language_code: The language_code of this DescribeFileSystemsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["zh", "en"]  # noqa: E501
        if (self._configuration.client_side_validation and
                language_code not in allowed_values):
            raise ValueError(
                "Invalid value for `language_code` ({0}), must be one of {1}"  # noqa: E501
                .format(language_code, allowed_values)
            )

        self._language_code = language_code

    @property
    def order_by(self):
        """Gets the order_by of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The order_by of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this DescribeFileSystemsRequest.


        :param order_by: The order_by of this DescribeFileSystemsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreateTimeDesc", "CreateTimeAsc", "CapacityDesc", "CapacityAsc", "IdDesc", "IdAsc", "VersionNumberDesc", "VersionNumberAsc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_by not in allowed_values):
            raise ValueError(
                "Invalid value for `order_by` ({0}), must be one of {1}"  # noqa: E501
                .format(order_by, allowed_values)
            )

        self._order_by = order_by

    @property
    def page_number(self):
        """Gets the page_number of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The page_number of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeFileSystemsRequest.


        :param page_number: The page_number of this DescribeFileSystemsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The page_size of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeFileSystemsRequest.


        :param page_size: The page_size of this DescribeFileSystemsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project(self):
        """Gets the project of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The project of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this DescribeFileSystemsRequest.


        :param project: The project of this DescribeFileSystemsRequest.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeFileSystemsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeFileSystemsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeFileSystemsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeFileSystemsRequest.


        :param tag_filters: The tag_filters of this DescribeFileSystemsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeFileSystemsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeFileSystemsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeFileSystemsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeFileSystemsRequest):
            return True

        return self.to_dict() != other.to_dict()
