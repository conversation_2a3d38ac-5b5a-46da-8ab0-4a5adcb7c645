# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAcceleratorRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_period': 'int',
        'billing_spec': 'str',
        'billing_type': 'str',
        'charge_type': 'str',
        'duration': 'int',
        'name': 'str',
        'project_name': 'str',
        'renew_type': 'int',
        'resource_tags': 'list[ResourceTagForCreateAcceleratorInput]'
    }

    attribute_map = {
        'billing_period': 'BillingPeriod',
        'billing_spec': 'BillingSpec',
        'billing_type': 'BillingType',
        'charge_type': 'ChargeType',
        'duration': 'Duration',
        'name': 'Name',
        'project_name': 'ProjectName',
        'renew_type': 'RenewType',
        'resource_tags': 'ResourceTags'
    }

    def __init__(self, billing_period=None, billing_spec=None, billing_type=None, charge_type=None, duration=None, name=None, project_name=None, renew_type=None, resource_tags=None, _configuration=None):  # noqa: E501
        """CreateAcceleratorRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_period = None
        self._billing_spec = None
        self._billing_type = None
        self._charge_type = None
        self._duration = None
        self._name = None
        self._project_name = None
        self._renew_type = None
        self._resource_tags = None
        self.discriminator = None

        if billing_period is not None:
            self.billing_period = billing_period
        self.billing_spec = billing_spec
        if billing_type is not None:
            self.billing_type = billing_type
        self.charge_type = charge_type
        if duration is not None:
            self.duration = duration
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if renew_type is not None:
            self.renew_type = renew_type
        if resource_tags is not None:
            self.resource_tags = resource_tags

    @property
    def billing_period(self):
        """Gets the billing_period of this CreateAcceleratorRequest.  # noqa: E501


        :return: The billing_period of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: int
        """
        return self._billing_period

    @billing_period.setter
    def billing_period(self, billing_period):
        """Sets the billing_period of this CreateAcceleratorRequest.


        :param billing_period: The billing_period of this CreateAcceleratorRequest.  # noqa: E501
        :type: int
        """

        self._billing_period = billing_period

    @property
    def billing_spec(self):
        """Gets the billing_spec of this CreateAcceleratorRequest.  # noqa: E501


        :return: The billing_spec of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_spec

    @billing_spec.setter
    def billing_spec(self, billing_spec):
        """Sets the billing_spec of this CreateAcceleratorRequest.


        :param billing_spec: The billing_spec of this CreateAcceleratorRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and billing_spec is None:
            raise ValueError("Invalid value for `billing_spec`, must not be `None`")  # noqa: E501

        self._billing_spec = billing_spec

    @property
    def billing_type(self):
        """Gets the billing_type of this CreateAcceleratorRequest.  # noqa: E501


        :return: The billing_type of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this CreateAcceleratorRequest.


        :param billing_type: The billing_type of this CreateAcceleratorRequest.  # noqa: E501
        :type: str
        """

        self._billing_type = billing_type

    @property
    def charge_type(self):
        """Gets the charge_type of this CreateAcceleratorRequest.  # noqa: E501


        :return: The charge_type of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this CreateAcceleratorRequest.


        :param charge_type: The charge_type of this CreateAcceleratorRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def duration(self):
        """Gets the duration of this CreateAcceleratorRequest.  # noqa: E501


        :return: The duration of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this CreateAcceleratorRequest.


        :param duration: The duration of this CreateAcceleratorRequest.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def name(self):
        """Gets the name of this CreateAcceleratorRequest.  # noqa: E501


        :return: The name of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateAcceleratorRequest.


        :param name: The name of this CreateAcceleratorRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateAcceleratorRequest.  # noqa: E501


        :return: The project_name of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateAcceleratorRequest.


        :param project_name: The project_name of this CreateAcceleratorRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def renew_type(self):
        """Gets the renew_type of this CreateAcceleratorRequest.  # noqa: E501


        :return: The renew_type of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this CreateAcceleratorRequest.


        :param renew_type: The renew_type of this CreateAcceleratorRequest.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    @property
    def resource_tags(self):
        """Gets the resource_tags of this CreateAcceleratorRequest.  # noqa: E501


        :return: The resource_tags of this CreateAcceleratorRequest.  # noqa: E501
        :rtype: list[ResourceTagForCreateAcceleratorInput]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this CreateAcceleratorRequest.


        :param resource_tags: The resource_tags of this CreateAcceleratorRequest.  # noqa: E501
        :type: list[ResourceTagForCreateAcceleratorInput]
        """

        self._resource_tags = resource_tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAcceleratorRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAcceleratorRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAcceleratorRequest):
            return True

        return self.to_dict() != other.to_dict()
