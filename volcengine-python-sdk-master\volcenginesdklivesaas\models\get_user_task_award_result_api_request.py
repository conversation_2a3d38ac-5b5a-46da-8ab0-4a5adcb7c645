# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetUserTaskAwardResultAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_item_type': 'int',
        'external_id': 'str',
        'task_award_id': 'str',
        'task_award_item_id': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_item_type': 'AwardItemType',
        'external_id': 'ExternalId',
        'task_award_id': 'TaskAwardId',
        'task_award_item_id': 'TaskAwardItemId'
    }

    def __init__(self, activity_id=None, award_item_type=None, external_id=None, task_award_id=None, task_award_item_id=None, _configuration=None):  # noqa: E501
        """GetUserTaskAwardResultAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_item_type = None
        self._external_id = None
        self._task_award_id = None
        self._task_award_item_id = None
        self.discriminator = None

        self.activity_id = activity_id
        self.award_item_type = award_item_type
        self.external_id = external_id
        self.task_award_id = task_award_id
        self.task_award_item_id = task_award_item_id

    @property
    def activity_id(self):
        """Gets the activity_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501


        :return: The activity_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetUserTaskAwardResultAPIRequest.


        :param activity_id: The activity_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def award_item_type(self):
        """Gets the award_item_type of this GetUserTaskAwardResultAPIRequest.  # noqa: E501


        :return: The award_item_type of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._award_item_type

    @award_item_type.setter
    def award_item_type(self, award_item_type):
        """Sets the award_item_type of this GetUserTaskAwardResultAPIRequest.


        :param award_item_type: The award_item_type of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and award_item_type is None:
            raise ValueError("Invalid value for `award_item_type`, must not be `None`")  # noqa: E501

        self._award_item_type = award_item_type

    @property
    def external_id(self):
        """Gets the external_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501


        :return: The external_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this GetUserTaskAwardResultAPIRequest.


        :param external_id: The external_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and external_id is None:
            raise ValueError("Invalid value for `external_id`, must not be `None`")  # noqa: E501

        self._external_id = external_id

    @property
    def task_award_id(self):
        """Gets the task_award_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501


        :return: The task_award_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_award_id

    @task_award_id.setter
    def task_award_id(self, task_award_id):
        """Sets the task_award_id of this GetUserTaskAwardResultAPIRequest.


        :param task_award_id: The task_award_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_award_id is None:
            raise ValueError("Invalid value for `task_award_id`, must not be `None`")  # noqa: E501

        self._task_award_id = task_award_id

    @property
    def task_award_item_id(self):
        """Gets the task_award_item_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501


        :return: The task_award_item_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_award_item_id

    @task_award_item_id.setter
    def task_award_item_id(self, task_award_item_id):
        """Sets the task_award_item_id of this GetUserTaskAwardResultAPIRequest.


        :param task_award_item_id: The task_award_item_id of this GetUserTaskAwardResultAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_award_item_id is None:
            raise ValueError("Invalid value for `task_award_item_id`, must not be `None`")  # noqa: E501

        self._task_award_item_id = task_award_item_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetUserTaskAwardResultAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetUserTaskAwardResultAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetUserTaskAwardResultAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
