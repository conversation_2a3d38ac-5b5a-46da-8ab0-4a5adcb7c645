# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAccessKeyDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'all_authority': 'str',
        'description': 'str',
        'instance_id': 'str',
        'topic_permissions': 'list[TopicPermissionForDescribeAccessKeyDetailOutput]'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'all_authority': 'AllAuthority',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'topic_permissions': 'TopicPermissions'
    }

    def __init__(self, access_key=None, all_authority=None, description=None, instance_id=None, topic_permissions=None, _configuration=None):  # noqa: E501
        """DescribeAccessKeyDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._all_authority = None
        self._description = None
        self._instance_id = None
        self._topic_permissions = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if all_authority is not None:
            self.all_authority = all_authority
        if description is not None:
            self.description = description
        if instance_id is not None:
            self.instance_id = instance_id
        if topic_permissions is not None:
            self.topic_permissions = topic_permissions

    @property
    def access_key(self):
        """Gets the access_key of this DescribeAccessKeyDetailResponse.  # noqa: E501


        :return: The access_key of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this DescribeAccessKeyDetailResponse.


        :param access_key: The access_key of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def all_authority(self):
        """Gets the all_authority of this DescribeAccessKeyDetailResponse.  # noqa: E501


        :return: The all_authority of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this DescribeAccessKeyDetailResponse.


        :param all_authority: The all_authority of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :type: str
        """

        self._all_authority = all_authority

    @property
    def description(self):
        """Gets the description of this DescribeAccessKeyDetailResponse.  # noqa: E501


        :return: The description of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeAccessKeyDetailResponse.


        :param description: The description of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeAccessKeyDetailResponse.  # noqa: E501


        :return: The instance_id of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeAccessKeyDetailResponse.


        :param instance_id: The instance_id of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def topic_permissions(self):
        """Gets the topic_permissions of this DescribeAccessKeyDetailResponse.  # noqa: E501


        :return: The topic_permissions of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :rtype: list[TopicPermissionForDescribeAccessKeyDetailOutput]
        """
        return self._topic_permissions

    @topic_permissions.setter
    def topic_permissions(self, topic_permissions):
        """Sets the topic_permissions of this DescribeAccessKeyDetailResponse.


        :param topic_permissions: The topic_permissions of this DescribeAccessKeyDetailResponse.  # noqa: E501
        :type: list[TopicPermissionForDescribeAccessKeyDetailOutput]
        """

        self._topic_permissions = topic_permissions

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAccessKeyDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAccessKeyDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAccessKeyDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
