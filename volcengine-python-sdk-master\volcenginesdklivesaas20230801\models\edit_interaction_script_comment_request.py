# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EditInteractionScriptCommentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comment': 'CommentForEditInteractionScriptCommentInput',
        'interaction_script_id': 'int'
    }

    attribute_map = {
        'comment': 'Comment',
        'interaction_script_id': 'InteractionScriptId'
    }

    def __init__(self, comment=None, interaction_script_id=None, _configuration=None):  # noqa: E501
        """EditInteractionScriptCommentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comment = None
        self._interaction_script_id = None
        self.discriminator = None

        if comment is not None:
            self.comment = comment
        self.interaction_script_id = interaction_script_id

    @property
    def comment(self):
        """Gets the comment of this EditInteractionScriptCommentRequest.  # noqa: E501


        :return: The comment of this EditInteractionScriptCommentRequest.  # noqa: E501
        :rtype: CommentForEditInteractionScriptCommentInput
        """
        return self._comment

    @comment.setter
    def comment(self, comment):
        """Sets the comment of this EditInteractionScriptCommentRequest.


        :param comment: The comment of this EditInteractionScriptCommentRequest.  # noqa: E501
        :type: CommentForEditInteractionScriptCommentInput
        """

        self._comment = comment

    @property
    def interaction_script_id(self):
        """Gets the interaction_script_id of this EditInteractionScriptCommentRequest.  # noqa: E501


        :return: The interaction_script_id of this EditInteractionScriptCommentRequest.  # noqa: E501
        :rtype: int
        """
        return self._interaction_script_id

    @interaction_script_id.setter
    def interaction_script_id(self, interaction_script_id):
        """Sets the interaction_script_id of this EditInteractionScriptCommentRequest.


        :param interaction_script_id: The interaction_script_id of this EditInteractionScriptCommentRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and interaction_script_id is None:
            raise ValueError("Invalid value for `interaction_script_id`, must not be `None`")  # noqa: E501

        self._interaction_script_id = interaction_script_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EditInteractionScriptCommentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EditInteractionScriptCommentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EditInteractionScriptCommentRequest):
            return True

        return self.to_dict() != other.to_dict()
