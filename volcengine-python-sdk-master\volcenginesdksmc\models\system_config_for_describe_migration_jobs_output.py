# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SystemConfigForDescribeMigrationJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'source_image_info': 'SourceImageInfoForDescribeMigrationJobsOutput',
        'target_image_info': 'TargetImageInfoForDescribeMigrationJobsOutput'
    }

    attribute_map = {
        'source_image_info': 'SourceImageInfo',
        'target_image_info': 'TargetImageInfo'
    }

    def __init__(self, source_image_info=None, target_image_info=None, _configuration=None):  # noqa: E501
        """SystemConfigForDescribeMigrationJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._source_image_info = None
        self._target_image_info = None
        self.discriminator = None

        if source_image_info is not None:
            self.source_image_info = source_image_info
        if target_image_info is not None:
            self.target_image_info = target_image_info

    @property
    def source_image_info(self):
        """Gets the source_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The source_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: SourceImageInfoForDescribeMigrationJobsOutput
        """
        return self._source_image_info

    @source_image_info.setter
    def source_image_info(self, source_image_info):
        """Sets the source_image_info of this SystemConfigForDescribeMigrationJobsOutput.


        :param source_image_info: The source_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: SourceImageInfoForDescribeMigrationJobsOutput
        """

        self._source_image_info = source_image_info

    @property
    def target_image_info(self):
        """Gets the target_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The target_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: TargetImageInfoForDescribeMigrationJobsOutput
        """
        return self._target_image_info

    @target_image_info.setter
    def target_image_info(self, target_image_info):
        """Sets the target_image_info of this SystemConfigForDescribeMigrationJobsOutput.


        :param target_image_info: The target_image_info of this SystemConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: TargetImageInfoForDescribeMigrationJobsOutput
        """

        self._target_image_info = target_image_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SystemConfigForDescribeMigrationJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SystemConfigForDescribeMigrationJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SystemConfigForDescribeMigrationJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
