# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AudienceLinkForGetActivityLinksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'landscape_mode_url': 'str',
        'portrait_moede_url': 'str'
    }

    attribute_map = {
        'landscape_mode_url': 'LandscapeModeUrl',
        'portrait_moede_url': 'PortraitMoedeUrl'
    }

    def __init__(self, landscape_mode_url=None, portrait_moede_url=None, _configuration=None):  # noqa: E501
        """AudienceLinkForGetActivityLinksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._landscape_mode_url = None
        self._portrait_moede_url = None
        self.discriminator = None

        if landscape_mode_url is not None:
            self.landscape_mode_url = landscape_mode_url
        if portrait_moede_url is not None:
            self.portrait_moede_url = portrait_moede_url

    @property
    def landscape_mode_url(self):
        """Gets the landscape_mode_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501


        :return: The landscape_mode_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._landscape_mode_url

    @landscape_mode_url.setter
    def landscape_mode_url(self, landscape_mode_url):
        """Sets the landscape_mode_url of this AudienceLinkForGetActivityLinksOutput.


        :param landscape_mode_url: The landscape_mode_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501
        :type: str
        """

        self._landscape_mode_url = landscape_mode_url

    @property
    def portrait_moede_url(self):
        """Gets the portrait_moede_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501


        :return: The portrait_moede_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._portrait_moede_url

    @portrait_moede_url.setter
    def portrait_moede_url(self, portrait_moede_url):
        """Sets the portrait_moede_url of this AudienceLinkForGetActivityLinksOutput.


        :param portrait_moede_url: The portrait_moede_url of this AudienceLinkForGetActivityLinksOutput.  # noqa: E501
        :type: str
        """

        self._portrait_moede_url = portrait_moede_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AudienceLinkForGetActivityLinksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AudienceLinkForGetActivityLinksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AudienceLinkForGetActivityLinksOutput):
            return True

        return self.to_dict() != other.to_dict()
