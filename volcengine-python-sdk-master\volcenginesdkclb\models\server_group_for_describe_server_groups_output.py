# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ServerGroupForDescribeServerGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address_ip_version': 'str',
        'any_port_enabled': 'str',
        'create_time': 'str',
        'description': 'str',
        'server_group_id': 'str',
        'server_group_name': 'str',
        'tags': 'list[TagForDescribeServerGroupsOutput]',
        'type': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'address_ip_version': 'AddressIpVersion',
        'any_port_enabled': 'AnyPortEnabled',
        'create_time': 'CreateTime',
        'description': 'Description',
        'server_group_id': 'ServerGroupId',
        'server_group_name': 'ServerGroupName',
        'tags': 'Tags',
        'type': 'Type',
        'update_time': 'UpdateTime'
    }

    def __init__(self, address_ip_version=None, any_port_enabled=None, create_time=None, description=None, server_group_id=None, server_group_name=None, tags=None, type=None, update_time=None, _configuration=None):  # noqa: E501
        """ServerGroupForDescribeServerGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address_ip_version = None
        self._any_port_enabled = None
        self._create_time = None
        self._description = None
        self._server_group_id = None
        self._server_group_name = None
        self._tags = None
        self._type = None
        self._update_time = None
        self.discriminator = None

        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if any_port_enabled is not None:
            self.any_port_enabled = any_port_enabled
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The address_ip_version of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this ServerGroupForDescribeServerGroupsOutput.


        :param address_ip_version: The address_ip_version of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def any_port_enabled(self):
        """Gets the any_port_enabled of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The any_port_enabled of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._any_port_enabled

    @any_port_enabled.setter
    def any_port_enabled(self, any_port_enabled):
        """Sets the any_port_enabled of this ServerGroupForDescribeServerGroupsOutput.


        :param any_port_enabled: The any_port_enabled of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._any_port_enabled = any_port_enabled

    @property
    def create_time(self):
        """Gets the create_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The create_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ServerGroupForDescribeServerGroupsOutput.


        :param create_time: The create_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The description of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ServerGroupForDescribeServerGroupsOutput.


        :param description: The description of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def server_group_id(self):
        """Gets the server_group_id of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The server_group_id of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this ServerGroupForDescribeServerGroupsOutput.


        :param server_group_id: The server_group_id of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def server_group_name(self):
        """Gets the server_group_name of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The server_group_name of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this ServerGroupForDescribeServerGroupsOutput.


        :param server_group_name: The server_group_name of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def tags(self):
        """Gets the tags of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The tags of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: list[TagForDescribeServerGroupsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ServerGroupForDescribeServerGroupsOutput.


        :param tags: The tags of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: list[TagForDescribeServerGroupsOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The type of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ServerGroupForDescribeServerGroupsOutput.


        :param type: The type of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501


        :return: The update_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ServerGroupForDescribeServerGroupsOutput.


        :param update_time: The update_time of this ServerGroupForDescribeServerGroupsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ServerGroupForDescribeServerGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ServerGroupForDescribeServerGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ServerGroupForDescribeServerGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
