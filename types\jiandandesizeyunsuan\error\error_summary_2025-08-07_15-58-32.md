## 准确率：42.80%  （(236 - 135) / 236）

## 运行时间: 2025-08-07_15-56-24

**使用模型ID：** doubao-seed-1-6-250615

**使用图片文件夹：** /images

## 错题

- 第 3 张图片: 037a8d2a77664b8caf6c032e1677e096.jpg
- 第 4 张图片: 03a6a879aaa74c23b04fc37b6cf2b7b5.jpg
- 第 5 张图片: 049a6e14e83a4c3492dae37fba50459b.jpg
- 第 7 张图片: 066e3de949c04685ae8b9bef93f1a52f.jpg
- 第 8 张图片: 08abab793d18480fb5f5b71036c5ac76.jpg
- 第 9 张图片: 09eeec74919a466283729d694cb94425.jpg
- 第 11 张图片: 0fd0b3e3b1a145cc9840505ca7adfb8b.jpg
- 第 15 张图片: 13a990bbdefd47ac9ebb9dc7169ee095.jpg
- 第 16 张图片: 14620e1a4abc4c4a939f77d2ff688eb7.jpg
- 第 18 张图片: 16e208765c6f46d0bc8d80f6ac01a6c2.jpg
- 第 21 张图片: 1a746dc5ea8a490ea7a933e55c85939d.jpg
- 第 22 张图片: 1a8a17e034624b3ea8c10446eb11f25c.jpg
- 第 29 张图片: 1eb33d5f0bea4440a80f85978330c642.jpg
- 第 32 张图片: 21d82e1bc0164fa493194861208c4f52.jpg
- 第 33 张图片: 240fd91397bb4b838450e32f588acac5.jpg
- 第 34 张图片: 253d6553790a471a888a2f4aa4f4e59c.jpg
- 第 35 张图片: 2616d2f3cce84dcd828f38beaab5302d.jpg
- 第 39 张图片: 2a5fa54d82284affb690558eaa49ecbf.jpg
- 第 40 张图片: 2bcd9d8c4ede49efa21a0ebd69c7766f.jpg
- 第 41 张图片: 2bd364c0afea48d38a1be02e309bed16.jpg
- 第 42 张图片: 2d4285da05ee44fa97ee92e19178c89c.jpg
- 第 45 张图片: 2f26a976da5c43df92987953cfb26e2c.jpg
- 第 46 张图片: 2f61a4d5c19e432e9585ecb0559c200e.jpg
- 第 49 张图片: 3352be9115304b67aa815f956eaf6c43.jpg
- 第 51 张图片: 359829674c30477eaa60d68d622a369a.jpg
- 第 52 张图片: 378ec761313c4f499110292958c04b3c.jpg
- 第 54 张图片: 3be9649d312b46c0a9087839a2796555.jpg
- 第 57 张图片: 408a6a4ce09b46c187fe10c1d9616a69.jpg
- 第 63 张图片: 47aaf3c73f2342cebc3dc8bdf6c4d090.jpg
- 第 64 张图片: 47b4d8662eaa452d9c8def39b7a51cb0.jpg
- 第 65 张图片: 47b833f6d2fe4fc78bf4fc814aa90f9f.jpg
- 第 67 张图片: 47fe582aa08e427d890254e90dbe026b.jpg
- 第 68 张图片: 48392a0f182c4342853e31879fde8bea.jpg
- 第 69 张图片: 4878cca8323f459bafb7765ff9966cec.jpg
- 第 70 张图片: 48e1127bbe354ccebb98b1b7374a0dc3.jpg
- 第 72 张图片: 4b6f701dfcae4c4ebaa84d0c16dd0318.jpg
- 第 73 张图片: 4d2b22c11dd34e5083d5d33ac5ef9da5.jpg
- 第 74 张图片: 4dc55c3e7b4b45d6ac63b3c8a3d46620.jpg
- 第 75 张图片: 4e254c3789a94603b9c6811c2f595ae0.jpg
- 第 76 张图片: 4e5c091224a14e3bbaab103d9301dcce.jpg
- 第 78 张图片: 4edd90ef54804eddbb3189ffec32cb7c.jpg
- 第 79 张图片: 4f9a4f2c47e94aca959cad738132e097.jpg
- 第 80 张图片: 5006e3fbdef349bba6e3583df9831378.jpg
- 第 83 张图片: 555a8b0f64974a27b8fc5a1c258c2fcc.jpg
- 第 85 张图片: 56b4acc190634af38fcd7b89cb24376d.jpg
- 第 86 张图片: 57834bbdbccf4a9599b8e824e3284d45.jpg
- 第 90 张图片: 5afc1708f8ab44d9bd222c7f1ea9fe6a.jpg
- 第 91 张图片: 5b9e8d7311e14684b3203eb7991cfbe6.jpg
- 第 94 张图片: 6166afd575264747825fd59bac26e338.jpg
- 第 97 张图片: 620b499b8e3242769d766bb7f9dc38a4.jpg
- 第 99 张图片: 63c0da2d288f4d6886068ad1569bde05.jpg
- 第 100 张图片: 64cba5d941ce4f96b6a6f97edc572008.jpg
- 第 101 张图片: 64e3e495a199417e8a8e4620728db510.jpg
- 第 102 张图片: 662f05762efd4e409e847909e1efe6f7.jpg
- 第 103 张图片: 6768ccf0e7724e8a98a43c0be94e2a3e.jpg
- 第 104 张图片: 6812bf8f1bcf431bbc412686e722f216.jpg
- 第 106 张图片: 6a2a73ca9d644b5488fb65a988544b12.jpg
- 第 107 张图片: 6a791c16c64743cba8f4e10a3ffafbea.jpg
- 第 108 张图片: 6aae76f544a1408caf310d75fcb3940d.jpg
- 第 112 张图片: 6db37c7ed5f94d42814f79744088f691.jpg
- 第 113 张图片: 6eeefb48b1004340a7d3d74fd065e5e9.jpg
- 第 116 张图片: 71cf79c1bce44b748bd77890eb05e701.jpg
- 第 117 张图片: 723ecc34b5a1411191b752466ff27674.jpg
- 第 118 张图片: 73505ed74af64f7e8c33078fa5dafcbb.jpg
- 第 121 张图片: 793ddd316bcf4c608576091beaec24fc.jpg
- 第 124 张图片: 79cff13f63ec4b558b6c09a1c75950a8.jpg
- 第 125 张图片: 79d2ce7013d243e19197c8d48cd80a39.jpg
- 第 126 张图片: 7a45180600544238a447105bca060273.jpg
- 第 128 张图片: 7f35d6129ae449ac8078d0b40b835a41.jpg
- 第 129 张图片: 7f41eae9d2f94fa2bad4dcf95af2529e.jpg
- 第 130 张图片: 7f734a014cea4343bada6d73fa5008fc.jpg
- 第 132 张图片: 836ad0526d3c4bbaa90ae119f8375188.jpg
- 第 133 张图片: 85119effdcb24215ae82a90692c42ed9.jpg
- 第 134 张图片: 88fc7a151a3e40ed89ff0f65bcc414da.jpg
- 第 136 张图片: 8ad24a09126c4f5590ae13f4b1390cd0.jpg
- 第 140 张图片: 8df94d5708174e278f7bc3fcbd9be1ef.jpg
- 第 142 张图片: 8eebf087dea24ad78b429dd51cb24e16.jpg
- 第 143 张图片: 91d8a3ca9326441ca54cfc7d4bebfb53.jpg
- 第 144 张图片: 9524ce80e52e48c4b10b50c5b28640fc.jpg
- 第 145 张图片: 955406664f3e49f587f83a4d12fdaa53.jpg
- 第 146 张图片: 97540b962de444fa87d0ee5168e9fb03.jpg
- 第 147 张图片: 9963f1bce80c4fb09de9950967575088.jpg
- 第 148 张图片: 9be95136439b4e54978bb87b9c7530b0.jpg
- 第 149 张图片: 9dc264a13f734fb89ea4c4151f4f2178.jpg
- 第 152 张图片: a02482ff4496415a8d973baf0b9133bb.jpg
- 第 155 张图片: a284d8c07b754d3cb86270af4b8dee7b.jpg
- 第 158 张图片: a4e14ecb8fd3477f8d852215f70a4710.jpg
- 第 159 张图片: a5505f0a457a48d28ca03432d6f1b312.jpg
- 第 160 张图片: a5ad5df73ed4477a8a738ccf7b67b9a3.jpg
- 第 163 张图片: a8b1c1480034464c857a0d00cd0443ad.jpg
- 第 164 张图片: aa5a2e472510417f977bc40a05bfd3ab.jpg
- 第 167 张图片: abc83c1366e34a87807e9307071b9e53.jpg
- 第 169 张图片: ac398a81ac4e4eb6b464eda2e7e7b9db.jpg
- 第 170 张图片: ac5eb24b0f0e4dd4bad2c05fc47bc334.jpg
- 第 171 张图片: accbb2f5b4aa4dcfa659e97865c57650.jpg
- 第 172 张图片: ad04e7f29b54400abb1a8187bfffcfef.jpg
- 第 174 张图片: adf68e3a57c54d41ad9b8f84ff32a1dc.jpg
- 第 175 张图片: ae73f4cb4bbf4b4789688153af9ecc1f.jpg
- 第 176 张图片: aef00fd83be34f4f90a30df7698bfab2.jpg
- 第 177 张图片: b0c29e41096645d9a7b15cc423fef656.jpg
- 第 178 张图片: b16dba83fb514aa896783ebbe2c08245.jpg
- 第 179 张图片: b1a42e27088f41ed93db6142c4164995.jpg
- 第 180 张图片: b29b0a3526e543a7bc3da3cc80f296db.jpg
- 第 181 张图片: b63cac27107f47c2b5b40bc3a9cdb05e.jpg
- 第 182 张图片: b67db8be9d2746349d44c650673295f2.jpg
- 第 188 张图片: bb1499178fb946a98b42df15d9968e90.jpg
- 第 192 张图片: c26b4c0c14ff412193da720ed99dad55.jpg
- 第 193 张图片: c38c3229eb5d4694a7e981f0dcb4e68d.jpg
- 第 196 张图片: c9a3d1414682402ba2c5b354c37bfc0a.jpg
- 第 197 张图片: ccd12c0cfac24c6d9a99e14c380b7ee8.jpg
- 第 199 张图片: cf3db06ab57e4d75acbb2d44116c9190.jpg
- 第 201 张图片: d10de923f1a24802ae094d517e438031.jpg
- 第 202 张图片: d14c4dbfb5bc40629168fcc5a09cd436.jpg
- 第 204 张图片: d4544e69005e4238bf84931cb24d86b9.jpg
- 第 205 张图片: d6c39fd02c1f43b5a599dde38f1c0d89.jpg
- 第 209 张图片: e00670ac00b4430abc2bd3d7a6e5fc85.jpg
- 第 211 张图片: e2085418bfc44c91921d64a3f6df5d9c.jpg
- 第 212 张图片: e2f1e7ae919b42c5ad2b232fd40759ca.jpg
- 第 213 张图片: e2f6f3922d734fdfab4c614243ff4871.jpg
- 第 214 张图片: e6761829d30e4f328f4a2a2733f86613.jpg
- 第 215 张图片: e8eda7de49864852908e47463a1d27af.jpg
- 第 220 张图片: e9feb8c0d62f44d3b8b3da84d13e9206.jpg
- 第 221 张图片: ebc687553ab84bae89fc58e6e1bbf0fb.jpg
- 第 222 张图片: ee21276da8b6457897865974d8613a92.jpg
- 第 223 张图片: eef978599ed24584a3617e2ad7524664.jpg
- 第 224 张图片: ef9d2d23349c4856bbede25d99a5ee8a.jpg
- 第 225 张图片: efa31758a21e4c0587d13ff854e75107.jpg
- 第 226 张图片: f31c24530b61441faf634675ef9eaa32.jpg
- 第 227 张图片: f53b65a196c94f96ac99952e3c536554.jpg
- 第 228 张图片: f56984f2b57143748bf8615e1fe5dbd2.jpg
- 第 231 张图片: fae27a27abf0456295d3a165486db741.jpg
- 第 232 张图片: fbb49a62f2f9428793cef82ef406e9c2.jpg
- 第 233 张图片: fcbf00df24934943b0420f52e320bf30.jpg
- 第 234 张图片: fcd2f8efc9f440d9a1d5d5c4b6a9357a.jpg
- 第 236 张图片: fe614c76d0634edaa536e57274d58617.jpg

==================================================
处理第 3 张图片: 037a8d2a77664b8caf6c032e1677e096.jpg

==================================================
![037a8d2a77664b8caf6c032e1677e096.jpg](../images/037a8d2a77664b8caf6c032e1677e096.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"20/28","题目3":"1/12","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"1","题目8":"1/3"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "20/28", "题目 3": "1/2", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4又4/5", "题目 7": "1", "题目 8": "1/3"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略92178个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.12秒
### token用量
- total_tokens: 1540
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 4 张图片: 03a6a879aaa74c23b04fc37b6cf2b7b5.jpg

==================================================
![03a6a879aaa74c23b04fc37b6cf2b7b5.jpg](../images/03a6a879aaa74c23b04fc37b6cf2b7b5.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"NAN","题目4":"1 1/9","题目5":"NAN","题目6":"NAN","题目 7":"NAN","题目8":"NAN","题目9":"7/11","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "NAN", "题目 8": "NAN", "题目 9": "7/11", "题目 10": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略103814个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.82秒
### token用量
- total_tokens: 1703
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 5 张图片: 049a6e14e83a4c3492dae37fba50459b.jpg

==================================================
![049a6e14e83a4c3492dae37fba50459b.jpg](../images/049a6e14e83a4c3492dae37fba50459b.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"3/4","题目 7":"0.027","题目8":"8/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "23/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "8/15", "题目 9": "8/11", "题目 10": "1.2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略138870个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.79秒
### token用量
- total_tokens: 1720
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 7 张图片: 066e3de949c04685ae8b9bef93f1a52f.jpg

==================================================
![066e3de949c04685ae8b9bef93f1a52f.jpg](../images/066e3de949c04685ae8b9bef93f1a52f.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"1.5374"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.16-1.5374"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略28930个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.64秒
### token用量
- total_tokens: 503
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 8 张图片: 08abab793d18480fb5f5b71036c5ac76.jpg

==================================================
![08abab793d18480fb5f5b71036c5ac76.jpg](../images/08abab793d18480fb5f5b71036c5ac76.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"0.875","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "0.875", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略90114个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.05秒
### token用量
- total_tokens: 1545
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 09eeec74919a466283729d694cb94425.jpg

==================================================
![09eeec74919a466283729d694cb94425.jpg](../images/09eeec74919a466283729d694cb94425.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1 - 1/4 - 7/12 = 1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3 = 16本", "题目 4": "75×3/5 = 45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略162094个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.62秒
### token用量
- total_tokens: 3056
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 0fd0b3e3b1a145cc9840505ca7adfb8b.jpg

==================================================
![0fd0b3e3b1a145cc9840505ca7adfb8b.jpg](../images/0fd0b3e3b1a145cc9840505ca7adfb8b.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "44/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略77946个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.72秒
### token用量
- total_tokens: 1542
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 15 张图片: 13a990bbdefd47ac9ebb9dc7169ee095.jpg

==================================================
![13a990bbdefd47ac9ebb9dc7169ee095.jpg](../images/13a990bbdefd47ac9ebb9dc7169ee095.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/5", "题目 9": "3000", "题目 10": "1"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略72474个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.84秒
### token用量
- total_tokens: 1057
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 16 张图片: 14620e1a4abc4c4a939f77d2ff688eb7.jpg

==================================================
![14620e1a4abc4c4a939f77d2ff688eb7.jpg](../images/14620e1a4abc4c4a939f77d2ff688eb7.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "14/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "11/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略137146个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：11.99秒
### token用量
- total_tokens: 1723
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 18 张图片: 16e208765c6f46d0bc8d80f6ac01a6c2.jpg

==================================================
![16e208765c6f46d0bc8d80f6ac01a6c2.jpg](../images/16e208765c6f46d0bc8d80f6ac01a6c2.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3","题目9":"300","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "300", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略81022个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.50秒
### token用量
- total_tokens: 1058
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 21 张图片: 1a746dc5ea8a490ea7a933e55c85939d.jpg

==================================================
![1a746dc5ea8a490ea7a933e55c85939d.jpg](../images/1a746dc5ea8a490ea7a933e55c85939d.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3", "题目 9": "3000", "题目 10": "7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76470个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.36秒
### token用量
- total_tokens: 1055
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 22 张图片: 1a8a17e034624b3ea8c10446eb11f25c.jpg

==================================================
![1a8a17e034624b3ea8c10446eb11f25c.jpg](../images/1a8a17e034624b3ea8c10446eb11f25c.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略168534个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.13秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 1eb33d5f0bea4440a80f85978330c642.jpg

==================================================
![1eb33d5f0bea4440a80f85978330c642.jpg](../images/1eb33d5f0bea4440a80f85978330c642.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "1/7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76678个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.53秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 32 张图片: 21d82e1bc0164fa493194861208c4f52.jpg

==================================================
![21d82e1bc0164fa493194861208c4f52.jpg](../images/21d82e1bc0164fa493194861208c4f52.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略75382个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.12秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 33 张图片: 240fd91397bb4b838450e32f588acac5.jpg

==================================================
![240fd91397bb4b838450e32f588acac5.jpg](../images/240fd91397bb4b838450e32f588acac5.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"98","题目3":"40本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/6", "题目 2": "420÷3×7=980(千克)", "题目 3": "24÷3×2=16(本)", "题目 4": "75÷5×3=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略224726个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.23秒
### token用量
- total_tokens: 3044
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 253d6553790a471a888a2f4aa4f4e59c.jpg

==================================================
![253d6553790a471a888a2f4aa4f4e59c.jpg](../images/253d6553790a471a888a2f4aa4f4e59c.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/4=3/12  1-(3/12+7/12)=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16(本)", "题目 4": "75×3/5=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略169746个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.79秒
### token用量
- total_tokens: 3065
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 35 张图片: 2616d2f3cce84dcd828f38beaab5302d.jpg

==================================================
![2616d2f3cce84dcd828f38beaab5302d.jpg](../images/2616d2f3cce84dcd828f38beaab5302d.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16(本)", "题目 4": "75×3/5=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略169998个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.70秒
### token用量
- total_tokens: 3054
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 39 张图片: 2a5fa54d82284affb690558eaa49ecbf.jpg

==================================================
![2a5fa54d82284affb690558eaa49ecbf.jpg](../images/2a5fa54d82284affb690558eaa49ecbf.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12.6","题目4":"0.975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12.6", "题目 4": "0.9775"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略26430个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.37秒
### token用量
- total_tokens: 500
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 2bcd9d8c4ede49efa21a0ebd69c7766f.jpg

==================================================
![2bcd9d8c4ede49efa21a0ebd69c7766f.jpg](../images/2bcd9d8c4ede49efa21a0ebd69c7766f.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"0.5","题目 7":"0.027","题目8":"8 4/15","题目9":"8/11","题目10":"1 6/10"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "0.5", "题目 7": "0.027", "题目 8": "84/15", "题目 9": "8/11", "题目 10": "6/10"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略130110个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.13秒
### token用量
- total_tokens: 1722
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 41 张图片: 2bd364c0afea48d38a1be02e309bed16.jpg

==================================================
![2bd364c0afea48d38a1be02e309bed16.jpg](../images/2bd364c0afea48d38a1be02e309bed16.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略79550个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.04秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 2d4285da05ee44fa97ee92e19178c89c.jpg

==================================================
![2d4285da05ee44fa97ee92e19178c89c.jpg](../images/2d4285da05ee44fa97ee92e19178c89c.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"3/9","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"8/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "23/18", "题目 6": "6/8", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1 2/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略134670个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.35秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 2f26a976da5c43df92987953cfb26e2c.jpg

==================================================
![2f26a976da5c43df92987953cfb26e2c.jpg](../images/2f26a976da5c43df92987953cfb26e2c.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980(千克)", "题目 3": "24×2/3=16(本)", "题目 4": "75×3/5=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略174214个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.08秒
### token用量
- total_tokens: 3055
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 46 张图片: 2f61a4d5c19e432e9585ecb0559c200e.jpg

==================================================
![2f61a4d5c19e432e9585ecb0559c200e.jpg](../images/2f61a4d5c19e432e9585ecb0559c200e.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980","题目3":"16本","题目4":"51公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3×7=980", "题目 3": "24÷3×2=16本", "题目 4": "75÷5×3=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略169098个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.94秒
### token用量
- total_tokens: 3050
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 49 张图片: 3352be9115304b67aa815f956eaf6c43.jpg

==================================================
![3352be9115304b67aa815f956eaf6c43.jpg](../images/3352be9115304b67aa815f956eaf6c43.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "35/42", "题目 4": "6/42", "题目 5": "29/42", "题目 6": "5/9", "题目 7": "6/9", "题目 8": "11/9", "题目 9": "1 5/18", "题目 10": "6/8", "题目 11": "3/4", "题目 12": "0.027", "题目 13": "8 14/15", "题目 14": "8/11", "题目 15": "1.2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略118602个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.72秒
### token用量
- total_tokens: 1782
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 359829674c30477eaa60d68d622a369a.jpg

==================================================
![359829674c30477eaa60d68d622a369a.jpg](../images/359829674c30477eaa60d68d622a369a.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "13/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略139278个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.23秒
### token用量
- total_tokens: 1723
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 52 张图片: 378ec761313c4f499110292958c04b3c.jpg

==================================================
![378ec761313c4f499110292958c04b3c.jpg](../images/378ec761313c4f499110292958c04b3c.jpg)

### response_template答案：
```json
{"题目1":"NAN","题目2":"0.1","题目3":"12","题目4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "0.1", "题目 3": "12.0", "题目 4": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略22530个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.76秒
### token用量
- total_tokens: 494
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 54 张图片: 3be9649d312b46c0a9087839a2796555.jpg

==================================================
![3be9649d312b46c0a9087839a2796555.jpg](../images/3be9649d312b46c0a9087839a2796555.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0075"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略27806个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.66秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 408a6a4ce09b46c187fe10c1d9616a69.jpg

==================================================
![408a6a4ce09b46c187fe10c1d9616a69.jpg](../images/408a6a4ce09b46c187fe10c1d9616a69.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"0.845","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "0.845", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "44/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略82510个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.55秒
### token用量
- total_tokens: 1544
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 63 张图片: 47aaf3c73f2342cebc3dc8bdf6c4d090.jpg

==================================================
![47aaf3c73f2342cebc3dc8bdf6c4d090.jpg](../images/47aaf3c73f2342cebc3dc8bdf6c4d090.jpg)

### response_template答案：
```json
{"题目1":"6/22","题目2":"5/7","题目3":"0.875","题目4":"1.75","题目5":"0.512","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "6/22", "题目 2": "5/7", "题目 3": "0.875", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略82286个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.23秒
### token用量
- total_tokens: 1545
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 47b4d8662eaa452d9c8def39b7a51cb0.jpg

==================================================
![47b4d8662eaa452d9c8def39b7a51cb0.jpg](../images/47b4d8662eaa452d9c8def39b7a51cb0.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"29/35","题目3":"29/42","题目4":"11/9","题目5":"1 6/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1.1"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "29/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "16/18", "题目 6": "3/4", "题目 7": "0.092", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略109930个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.60秒
### token用量
- total_tokens: 1723
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 65 张图片: 47b833f6d2fe4fc78bf4fc814aa90f9f.jpg

==================================================
![47b833f6d2fe4fc78bf4fc814aa90f9f.jpg](../images/47b833f6d2fe4fc78bf4fc814aa90f9f.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略84294个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.21秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 47fe582aa08e427d890254e90dbe026b.jpg

==================================================
![47fe582aa08e427d890254e90dbe026b.jpg](../images/47fe582aa08e427d890254e90dbe026b.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略82174个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.95秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 68 张图片: 48392a0f182c4342853e31879fde8bea.jpg

==================================================
![48392a0f182c4342853e31879fde8bea.jpg](../images/48392a0f182c4342853e31879fde8bea.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"11/35","题目3":"29/42","题目4":"NAN","题目5":"NAN","题目6":"3/4","题目 7":"NAN","题目8":"NAN","题目9":"NAN","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "11/35", "题目 3": "29/42", "题目 4": "", "题目 5": "", "题目 6": "6/8-3/4", "题目 7": "", "题目 8": "", "题目 9": "", "题目 10": ""}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略100358个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.82秒
### token用量
- total_tokens: 1694
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 69 张图片: 4878cca8323f459bafb7765ff9966cec.jpg

==================================================
![4878cca8323f459bafb7765ff9966cec.jpg](../images/4878cca8323f459bafb7765ff9966cec.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1 - 1/4 - 7/12 = 1/6", "题目 2": "420 ÷ 3/7 = 980(kg)", "题目 3": "24 × 2/3 = 16(本)", "题目 4": "75 × 3/5 = 45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略177494个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.13秒
### token用量
- total_tokens: 3065
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 48e1127bbe354ccebb98b1b7374a0dc3.jpg

==================================================
![48e1127bbe354ccebb98b1b7374a0dc3.jpg](../images/48e1127bbe354ccebb98b1b7374a0dc3.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"1.2","题目4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略23826个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.53秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 72 张图片: 4b6f701dfcae4c4ebaa84d0c16dd0318.jpg

==================================================
![4b6f701dfcae4c4ebaa84d0c16dd0318.jpg](../images/4b6f701dfcae4c4ebaa84d0c16dd0318.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980 kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略181618个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.10秒
### token用量
- total_tokens: 3053
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 73 张图片: 4d2b22c11dd34e5083d5d33ac5ef9da5.jpg

==================================================
![4d2b22c11dd34e5083d5d33ac5ef9da5.jpg](../images/4d2b22c11dd34e5083d5d33ac5ef9da5.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略78954个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.67秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 74 张图片: 4dc55c3e7b4b45d6ac63b3c8a3d46620.jpg

==================================================
![4dc55c3e7b4b45d6ac63b3c8a3d46620.jpg](../images/4dc55c3e7b4b45d6ac63b3c8a3d46620.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"11/35","题目3":"29/42","题目4":"1 2/9","题目5":"2 1/18","题目6":"0.75","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "11/35", "题目 3": "29/42", "题目 4": "12/9", "题目 5": "31/18", "题目 6": "0.75", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "12/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略107030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.76秒
### token用量
- total_tokens: 1724
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 4e254c3789a94603b9c6811c2f595ae0.jpg

==================================================
![4e254c3789a94603b9c6811c2f595ae0.jpg](../images/4e254c3789a94603b9c6811c2f595ae0.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"0.75","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "34/35", "题目 3": "29/42", "题目 4": "12/9", "题目 5": "5/18", "题目 6": "0.75", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "11/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略134802个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.39秒
### token用量
- total_tokens: 1723
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 76 张图片: 4e5c091224a14e3bbaab103d9301dcce.jpg

==================================================
![4e5c091224a14e3bbaab103d9301dcce.jpg](../images/4e5c091224a14e3bbaab103d9301dcce.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"22/17","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"6/8","题目 7":"0.009","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "27/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "6/8", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "1.2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略117210个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.96秒
### token用量
- total_tokens: 1722
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 78 张图片: 4edd90ef54804eddbb3189ffec32cb7c.jpg

==================================================
![4edd90ef54804eddbb3189ffec32cb7c.jpg](../images/4edd90ef54804eddbb3189ffec32cb7c.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"NAN","题目4":"NAN","题目5":"NAN","题目6":"NAN","题目 7":"NAN","题目8":"NAN","题目9":"NAN","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "0.4+3/35=", "题目 3": "5/6-1/7=", "题目 4": "5÷9+2/3=", "题目 5": "12/3÷7/18=", "题目 6": "7/8-0.125=", "题目 7": "0.3³=0.027", "题目 8": "9-1÷15=814/15", "题目 9": "1-7/11+4/11=8/11", "题目 10": "() - 3/5=1.2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略108614个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：12.07秒
### token用量
- total_tokens: 1771
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 79 张图片: 4f9a4f2c47e94aca959cad738132e097.jpg

==================================================
![4f9a4f2c47e94aca959cad738132e097.jpg](../images/4f9a4f2c47e94aca959cad738132e097.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"5/8","题目6":"4 4/5","题目 7":"25/64","题目8":"26/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "NAN", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "27/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略107474个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.08秒
### token用量
- total_tokens: 1541
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 5006e3fbdef349bba6e3583df9831378.jpg

==================================================
![5006e3fbdef349bba6e3583df9831378.jpg](../images/5006e3fbdef349bba6e3583df9831378.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 15/18","题目6":"3/4","题目 7":"300","题目8":"8 14/15","题目9":"7/11","题目10":"1.6"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "3/4", "题目 7": "700", "题目 8": "814/15", "题目 9": "7/11", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略111746个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.78秒
### token用量
- total_tokens: 1720
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 83 张图片: 555a8b0f64974a27b8fc5a1c258c2fcc.jpg

==================================================
![555a8b0f64974a27b8fc5a1c258c2fcc.jpg](../images/555a8b0f64974a27b8fc5a1c258c2fcc.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980千克","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/12+7/12）=1/6", "题目 2": "420÷3×7=980千克", "题目 3": "24÷3×2=16本", "题目 4": "75÷5×3=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170698个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.06秒
### token用量
- total_tokens: 3054
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 56b4acc190634af38fcd7b89cb24376d.jpg

==================================================
![56b4acc190634af38fcd7b89cb24376d.jpg](../images/56b4acc190634af38fcd7b89cb24376d.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980(kg)", "题目 3": "24×2/3=16(本)", "题目 4": "75×3/5=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略180342个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.34秒
### token用量
- total_tokens: 3055
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 86 张图片: 57834bbdbccf4a9599b8e824e3284d45.jpg

==================================================
![57834bbdbccf4a9599b8e824e3284d45.jpg](../images/57834bbdbccf4a9599b8e824e3284d45.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"NAN","题目8":"3","题目9":"3000","题目10":"7"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略75598个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.81秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 90 张图片: 5afc1708f8ab44d9bd222c7f1ea9fe6a.jpg

==================================================
![5afc1708f8ab44d9bd222c7f1ea9fe6a.jpg](../images/5afc1708f8ab44d9bd222c7f1ea9fe6a.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"NAN","题目3":"15","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "NAN", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略70594个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.99秒
### token用量
- total_tokens: 1058
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 91 张图片: 5b9e8d7311e14684b3203eb7991cfbe6.jpg

==================================================
![5b9e8d7311e14684b3203eb7991cfbe6.jpg](../images/5b9e8d7311e14684b3203eb7991cfbe6.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.3","题目 7":"780","题目8":"3","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略84786个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.63秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 94 张图片: 6166afd575264747825fd59bac26e338.jpg

==================================================
![6166afd575264747825fd59bac26e338.jpg](../images/6166afd575264747825fd59bac26e338.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略82510个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.25秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 97 张图片: 620b499b8e3242769d766bb7f9dc38a4.jpg

==================================================
![620b499b8e3242769d766bb7f9dc38a4.jpg](../images/620b499b8e3242769d766bb7f9dc38a4.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 1/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "1 2/9", "题目 5": "1 5/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "6/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略159270个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.09秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 99 张图片: 63c0da2d288f4d6886068ad1569bde05.jpg

==================================================
![63c0da2d288f4d6886068ad1569bde05.jpg](../images/63c0da2d288f4d6886068ad1569bde05.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76322个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.20秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 100 张图片: 64cba5d941ce4f96b6a6f97edc572008.jpg

==================================================
![64cba5d941ce4f96b6a6f97edc572008.jpg](../images/64cba5d941ce4f96b6a6f97edc572008.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "1 2/9", "题目 5": "1 7/18", "题目 6": "6/8-3/40", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1 2/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略123342个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.14秒
### token用量
- total_tokens: 1732
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 101 张图片: 64e3e495a199417e8a8e4620728db510.jpg

==================================================
![64e3e495a199417e8a8e4620728db510.jpg](../images/64e3e495a199417e8a8e4620728db510.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"215","题目3":"1.5","题目4":"80","题目5":"545","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "215", "题目 3": "1.5", "题目 4": "80", "题目 5": "545", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略85338个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.77秒
### token用量
- total_tokens: 1060
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 102 张图片: 662f05762efd4e409e847909e1efe6f7.jpg

==================================================
![662f05762efd4e409e847909e1efe6f7.jpg](../images/662f05762efd4e409e847909e1efe6f7.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"0.75","题目 7":"0.0027","题目8":"8 14/15","题目9":"8/11","题目10":"1.8"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "1 5/18", "题目 6": "0.75", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1.8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略127750个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.44秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 103 张图片: 6768ccf0e7724e8a98a43c0be94e2a3e.jpg

==================================================
![6768ccf0e7724e8a98a43c0be94e2a3e.jpg](../images/6768ccf0e7724e8a98a43c0be94e2a3e.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略80878个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.30秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 104 张图片: 6812bf8f1bcf431bbc412686e722f216.jpg

==================================================
![6812bf8f1bcf431bbc412686e722f216.jpg](../images/6812bf8f1bcf431bbc412686e722f216.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980千克","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1 - 1/4 - 7/12 = 1/6", "题目 2": "420 ÷ 3/7 = 980(千克) = 980千克", "题目 3": "24×2/3 = 16(本)", "题目 4": "75×3/5 = 45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略185622个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.04秒
### token用量
- total_tokens: 3070
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 6a2a73ca9d644b5488fb65a988544b12.jpg

==================================================
![6a2a73ca9d644b5488fb65a988544b12.jpg](../images/6a2a73ca9d644b5488fb65a988544b12.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"NAN","题目4":"NAN","题目5":"NAN","题目6":"NAN","题目 7":"0.9","题目8":"NAN","题目9":"8/11","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "0.75", "题目 7": "0.9", "题目 8": "NAN", "题目 9": "8/11"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略106478个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.17秒
### token用量
- total_tokens: 1695
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 107 张图片: 6a791c16c64743cba8f4e10a3ffafbea.jpg

==================================================
![6a791c16c64743cba8f4e10a3ffafbea.jpg](../images/6a791c16c64743cba8f4e10a3ffafbea.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980 kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略177902个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.17秒
### token用量
- total_tokens: 3053
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 108 张图片: 6aae76f544a1408caf310d75fcb3940d.jpg

==================================================
![6aae76f544a1408caf310d75fcb3940d.jpg](../images/6aae76f544a1408caf310d75fcb3940d.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/9"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略84162个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.17秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 112 张图片: 6db37c7ed5f94d42814f79744088f691.jpg

==================================================
![6db37c7ed5f94d42814f79744088f691.jpg](../images/6db37c7ed5f94d42814f79744088f691.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1/4=3/12 1-(7/12+3/12)=1/6", "题目 2": "420÷3×7=980(kg)", "题目 3": "24÷3×2=16(本)", "题目 4": "75÷5×3=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略168650个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.10秒
### token用量
- total_tokens: 3065
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 6eeefb48b1004340a7d3d74fd065e5e9.jpg

==================================================
![6eeefb48b1004340a7d3d74fd065e5e9.jpg](../images/6eeefb48b1004340a7d3d74fd065e5e9.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"NAN","题目3":"12","题目4":"NAN"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "", "题目 3": "12.0", "题目 4": ""}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略23326个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：4.70秒
### token用量
- total_tokens: 491
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 71cf79c1bce44b748bd77890eb05e701.jpg

==================================================
![71cf79c1bce44b748bd77890eb05e701.jpg](../images/71cf79c1bce44b748bd77890eb05e701.jpg)

### response_template答案：
```json
{"题目1":"11/3","题目2":"28/20","题目3":"4/1.625","题目4":"1.75","题目5":"0.552","题目6":"5/4","题目 7":"0.625/1.6","题目8":"0.6/2.5"}
```
### 响应内容：
```json
{"题目 1": "11/3", "题目 2": "20/20", "题目 3": "4/1.625", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "5/4", "题目 7": "0.625/1.6", "题目 8": "0.6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略88914个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.52秒
### token用量
- total_tokens: 1553
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 723ecc34b5a1411191b752466ff27674.jpg

==================================================
![723ecc34b5a1411191b752466ff27674.jpg](../images/723ecc34b5a1411191b752466ff27674.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"10","题目4":"0.15"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.15"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略25162个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.06秒
### token用量
- total_tokens: 496
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 118 张图片: 73505ed74af64f7e8c33078fa5dafcbb.jpg

==================================================
![73505ed74af64f7e8c33078fa5dafcbb.jpg](../images/73505ed74af64f7e8c33078fa5dafcbb.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"34/70","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"NAN","题目 7":"NAN","题目8":"8 14/15","题目9":"8/11","题目10":"1.6"}
```
### 响应内容：
```json
{"题目 1": "13/13", "题目 2": "34/70", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "5/18", "题目 6": "0.75", "题目 7": "0.027", "题目 8": "8又14/15", "题目 9": "8/11", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略123394个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.85秒
### token用量
- total_tokens: 1727
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 121 张图片: 793ddd316bcf4c608576091beaec24fc.jpg

==================================================
![793ddd316bcf4c608576091beaec24fc.jpg](../images/793ddd316bcf4c608576091beaec24fc.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"75"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略32254个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.00秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 79cff13f63ec4b558b6c09a1c75950a8.jpg

==================================================
![79cff13f63ec4b558b6c09a1c75950a8.jpg](../images/79cff13f63ec4b558b6c09a1c75950a8.jpg)

### response_template答案：
```json
{"题目1":"NAN","题目2":"1260(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "NAN", "题目 2": "420÷7×3=180(kg) 180×7=1260(kg)", "题目 3": "(24÷3)×2=16(本)", "题目 4": "(75÷5)×3=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略178834个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.97秒
### token用量
- total_tokens: 3059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 125 张图片: 79d2ce7013d243e19197c8d48cd80a39.jpg

==================================================
![79d2ce7013d243e19197c8d48cd80a39.jpg](../images/79d2ce7013d243e19197c8d48cd80a39.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"31/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"3/4","题目 7":"0.0027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "31/35", "题目 3": "19/42", "题目 4": "11/9", "题目 5": "23/18", "题目 6": "2/4", "题目 7": "0.0027", "题目 8": "84/5", "题目 9": "8/11", "题目 10": "1.2 1/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略125674个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.31秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 126 张图片: 7a45180600544238a447105bca060273.jpg

==================================================
![7a45180600544238a447105bca060273.jpg](../images/7a45180600544238a447105bca060273.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0225"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.022"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略27126个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.68秒
### token用量
- total_tokens: 497
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 128 张图片: 7f35d6129ae449ac8078d0b40b835a41.jpg

==================================================
![7f35d6129ae449ac8078d0b40b835a41.jpg](../images/7f35d6129ae449ac8078d0b40b835a41.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"5/8","题目4":"1.75","题目5":"0.552","题目6":"24/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "5/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "14/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略81462个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.32秒
### token用量
- total_tokens: 1542
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 7f41eae9d2f94fa2bad4dcf95af2529e.jpg

==================================================
![7f41eae9d2f94fa2bad4dcf95af2529e.jpg](../images/7f41eae9d2f94fa2bad4dcf95af2529e.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980千克","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3×7=980千克", "题目 3": "24÷3×2=16本", "题目 4": "75÷5×3=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略182890个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.18秒
### token用量
- total_tokens: 3053
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 130 张图片: 7f734a014cea4343bada6d73fa5008fc.jpg

==================================================
![7f734a014cea4343bada6d73fa5008fc.jpg](../images/7f734a014cea4343bada6d73fa5008fc.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"14/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "14/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "23/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1 1/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略126650个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.86秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 836ad0526d3c4bbaa90ae119f8375188.jpg

==================================================
![836ad0526d3c4bbaa90ae119f8375188.jpg](../images/836ad0526d3c4bbaa90ae119f8375188.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1 - 1/4 - 7/12 = 1/6", "题目 2": "420 ÷ 3/7 = 980kg", "题目 3": "24 × (1 - 1/3) = 16本", "题目 4": "75 × 3/5 = 45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170174个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.57秒
### token用量
- total_tokens: 3065
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 85119effdcb24215ae82a90692c42ed9.jpg

==================================================
![85119effdcb24215ae82a90692c42ed9.jpg](../images/85119effdcb24215ae82a90692c42ed9.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"11/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"0.75","题目 7":"0.027","题目8":"126/135","题目9":"7/11","题目10":"7/5"}
```
### 响应内容：
```json
{"题目 1": "13/13=1", "题目 2": "11/35", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "23/18", "题目 6": "0.75", "题目 7": "0.027", "题目 8": "126/135", "题目 9": "7/11", "题目 10": "7/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略118910个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.53秒
### token用量
- total_tokens: 1730
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 134 张图片: 88fc7a151a3e40ed89ff0f65bcc414da.jpg

==================================================
![88fc7a151a3e40ed89ff0f65bcc414da.jpg](../images/88fc7a151a3e40ed89ff0f65bcc414da.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"0","题目10":"1 1/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "2/42", "题目 4": "11/9", "题目 5": "15/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "0", "题目 10": "1.2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略132850个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.95秒
### token用量
- total_tokens: 1718
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 8ad24a09126c4f5590ae13f4b1390cd0.jpg

==================================================
![8ad24a09126c4f5590ae13f4b1390cd0.jpg](../images/8ad24a09126c4f5590ae13f4b1390cd0.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45公顷)"}
```
### 响应内容：
```json
{"题目 1": "1-7/12-3/12=2/12=1/6", "题目 2": "420÷3/7=180(kg)", "题目 3": "24×(2/3-1/3)=16(本)", "题目 4": "75×(1-2/5)=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略205030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.38秒
### token用量
- total_tokens: 3069
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 140 张图片: 8df94d5708174e278f7bc3fcbd9be1ef.jpg

==================================================
![8df94d5708174e278f7bc3fcbd9be1ef.jpg](../images/8df94d5708174e278f7bc3fcbd9be1ef.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.55","题目6":"4 4/5","题目 7":"25/64","题目8":"NAN"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略80282个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.02秒
### token用量
- total_tokens: 1543
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 142 张图片: 8eebf087dea24ad78b429dd51cb24e16.jpg

==================================================
![8eebf087dea24ad78b429dd51cb24e16.jpg](../images/8eebf087dea24ad78b429dd51cb24e16.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"NAN","题目4":"5/11","题目5":"NAN","题目6":"6/8","题目 7":"NAN","题目8":"8/12","题目9":"0","题目10":"10"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "6/8", "题目 7": "NAN", "题目 8": "NAN", "题目 9": "NAN", "题目 10": "10"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略104378个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.23秒
### token用量
- total_tokens: 1701
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 91d8a3ca9326441ca54cfc7d4bebfb53.jpg

==================================================
![91d8a3ca9326441ca54cfc7d4bebfb53.jpg](../images/91d8a3ca9326441ca54cfc7d4bebfb53.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略163894个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.83秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 144 张图片: 9524ce80e52e48c4b10b50c5b28640fc.jpg

==================================================
![9524ce80e52e48c4b10b50c5b28640fc.jpg](../images/9524ce80e52e48c4b10b50c5b28640fc.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "44/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略84730个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.91秒
### token用量
- total_tokens: 1542
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 145 张图片: 955406664f3e49f587f83a4d12fdaa53.jpg

==================================================
![955406664f3e49f587f83a4d12fdaa53.jpg](../images/955406664f3e49f587f83a4d12fdaa53.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略80922个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.97秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 97540b962de444fa87d0ee5168e9fb03.jpg

==================================================
![97540b962de444fa87d0ee5168e9fb03.jpg](../images/97540b962de444fa87d0ee5168e9fb03.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"25/18","题目6":"3/4","题目 7":"0.27","题目8":"NAN","题目9":"NAN","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "0.27", "题目 8": "NAN", "题目 9": "NAN", "题目 10": "1\\frac{1}{5}"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略119622个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：12.87秒
### token用量
- total_tokens: 1708
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 9963f1bce80c4fb09de9950967575088.jpg

==================================================
![9963f1bce80c4fb09de9950967575088.jpg](../images/9963f1bce80c4fb09de9950967575088.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.098"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略25910个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：4.99秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 148 张图片: 9be95136439b4e54978bb87b9c7530b0.jpg

==================================================
![9be95136439b4e54978bb87b9c7530b0.jpg](../images/9be95136439b4e54978bb87b9c7530b0.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980 kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略171862个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：11.31秒
### token用量
- total_tokens: 3053
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: 9dc264a13f734fb89ea4c4151f4f2178.jpg

==================================================
![9dc264a13f734fb89ea4c4151f4f2178.jpg](../images/9dc264a13f734fb89ea4c4151f4f2178.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980（千克）","题目3":"16（本）","题目4":"45（公顷）"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980(千克)", "题目 3": "24×(1-1/3)=16(本)", "题目 4": "75×(1-2/5)=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略181426个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.30秒
### token用量
- total_tokens: 3061
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 152 张图片: a02482ff4496415a8d973baf0b9133bb.jpg

==================================================
![a02482ff4496415a8d973baf0b9133bb.jpg](../images/a02482ff4496415a8d973baf0b9133bb.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"NAN","题目3":"12","题目4":"0.225"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "", "题目 3": "12", "题目 4": "0.225"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略26122个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：4.59秒
### token用量
- total_tokens: 494
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 155 张图片: a284d8c07b754d3cb86270af4b8dee7b.jpg

==================================================
![a284d8c07b754d3cb86270af4b8dee7b.jpg](../images/a284d8c07b754d3cb86270af4b8dee7b.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略181246个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：11.27秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: a4e14ecb8fd3477f8d852215f70a4710.jpg

==================================================
![a4e14ecb8fd3477f8d852215f70a4710.jpg](../images/a4e14ecb8fd3477f8d852215f70a4710.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"9.6","题目4":"0.099"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "9.6", "题目 4": "0.0999"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略26302个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.77秒
### token用量
- total_tokens: 499
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: a5505f0a457a48d28ca03432d6f1b312.jpg

==================================================
![a5505f0a457a48d28ca03432d6f1b312.jpg](../images/a5505f0a457a48d28ca03432d6f1b312.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.125"}
```
### 响应内容：
```json
{"题目 1": "181.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.125"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略24218个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：4.78秒
### token用量
- total_tokens: 497
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: a5ad5df73ed4477a8a738ccf7b67b9a3.jpg

==================================================
![a5ad5df73ed4477a8a738ccf7b67b9a3.jpg](../images/a5ad5df73ed4477a8a738ccf7b67b9a3.jpg)

### response_template答案：
```json
{"题目1":"13/13","题目2":"24/35","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1.6"}
```
### 响应内容：
```json
{"题目 1": "13/13", "题目 2": "24/35", "题目 3": "29/42", "题目 4": "12/9", "题目 5": "15/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略116786个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.66秒
### token用量
- total_tokens: 1726
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 163 张图片: a8b1c1480034464c857a0d00cd0443ad.jpg

==================================================
![a8b1c1480034464c857a0d00cd0443ad.jpg](../images/a8b1c1480034464c857a0d00cd0443ad.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略162986个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.17秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: aa5a2e472510417f977bc40a05bfd3ab.jpg

==================================================
![aa5a2e472510417f977bc40a05bfd3ab.jpg](../images/aa5a2e472510417f977bc40a05bfd3ab.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"24/42","题目4":"1 1/3","题目5":"1 5/18","题目6":"6/8=3/4","题目 7":"0.00027","题目8":"NAN","题目9":"0","题目10":"4/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "11/3", "题目 5": "15/18", "题目 6": "6/8=3/4", "题目 7": "0.00027", "题目 8": "8", "题目 9": "0", "题目 10": "4/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略135886个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.48秒
### token用量
- total_tokens: 1720
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 167 张图片: abc83c1366e34a87807e9307071b9e53.jpg

==================================================
![abc83c1366e34a87807e9307071b9e53.jpg](../images/abc83c1366e34a87807e9307071b9e53.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0915"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略29642个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.16秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: ac398a81ac4e4eb6b464eda2e7e7b9db.jpg

==================================================
![ac398a81ac4e4eb6b464eda2e7e7b9db.jpg](../images/ac398a81ac4e4eb6b464eda2e7e7b9db.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略83018个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.69秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 170 张图片: ac5eb24b0f0e4dd4bad2c05fc47bc334.jpg

==================================================
![ac5eb24b0f0e4dd4bad2c05fc47bc334.jpg](../images/ac5eb24b0f0e4dd4bad2c05fc47bc334.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"NAN","题目8":"NAN"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "18/75"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略83566个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.15秒
### token用量
- total_tokens: 1544
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: accbb2f5b4aa4dcfa659e97865c57650.jpg

==================================================
![accbb2f5b4aa4dcfa659e97865c57650.jpg](../images/accbb2f5b4aa4dcfa659e97865c57650.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"5/35","题目3":"29/42","题目4":"5/3","题目5":"5/18","题目6":"NAN","题目 7":"0.9","题目8":"0.53","题目9":"8/11","题目10":"3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "5/35", "题目 3": "429/42", "题目 4": "5/3", "题目 5": "5/18", "题目 6": "NAN", "题目 7": "0.9", "题目 8": "0.53", "题目 9": "8/11", "题目 10": "2/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略111266个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.64秒
### token用量
- total_tokens: 1715
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: ad04e7f29b54400abb1a8187bfffcfef.jpg

==================================================
![ad04e7f29b54400abb1a8187bfffcfef.jpg](../images/ad04e7f29b54400abb1a8187bfffcfef.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"31/35","题目3":"29/42","题目4":"11/9","题目5":"23/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1 4/5"}
```
### 响应内容：
```json
{"题目 1": "13/13=1", "题目 2": "4/5+3/35=31/35", "题目 3": "5/6-1/7=29/42", "题目 4": "5/9+2/3=11/9", "题目 5": "5/3-7/18=23/18", "题目 6": "6/8=3/4", "题目 7": "0.027", "题目 8": "9-1/15=814/15", "题目 9": "11/11-7/11+4/11=8/11", "题目 10": "13/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略149222个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.99秒
### token用量
- total_tokens: 1790
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 174 张图片: adf68e3a57c54d41ad9b8f84ff32a1dc.jpg

==================================================
![adf68e3a57c54d41ad9b8f84ff32a1dc.jpg](../images/adf68e3a57c54d41ad9b8f84ff32a1dc.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "1/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略86262个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.13秒
### token用量
- total_tokens: 1543
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 175 张图片: ae73f4cb4bbf4b4789688153af9ecc1f.jpg

==================================================
![ae73f4cb4bbf4b4789688153af9ecc1f.jpg](../images/ae73f4cb4bbf4b4789688153af9ecc1f.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"NAN","题目4":"NAN","题目5":"5/8","题目6":"NAN","题目 7":"NAN","题目8":"NAN","题目9":"1","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "1.1", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "0.027", "题目 8": "NAN", "题目 9": "1", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略105326个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.59秒
### token用量
- total_tokens: 1704
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 176 张图片: aef00fd83be34f4f90a30df7698bfab2.jpg

==================================================
![aef00fd83be34f4f90a30df7698bfab2.jpg](../images/aef00fd83be34f4f90a30df7698bfab2.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"1 1/3","题目5":"1 5/8","题目6":"3/4","题目 7":"0.0027","题目8":"8 14/15","题目9":"8/11","题目10":"1 3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "17/35", "题目 3": "29/42", "题目 4": "1 1/3", "题目 5": "5/8", "题目 6": "3/4", "题目 7": "0.0027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "1 2/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略122542个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.64秒
### token用量
- total_tokens: 1725
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: b0c29e41096645d9a7b15cc423fef656.jpg

==================================================
![b0c29e41096645d9a7b15cc423fef656.jpg](../images/b0c29e41096645d9a7b15cc423fef656.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"1100千克","题目3":"16本","题目4":"25公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3=140 4×140=680 420+680=1100千克", "题目 3": "24÷3=8 2×8=16本", "题目 4": "75×1/3=25公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略184966个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.22秒
### token用量
- total_tokens: 3078
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 178 张图片: b16dba83fb514aa896783ebbe2c08245.jpg

==================================================
![b16dba83fb514aa896783ebbe2c08245.jpg](../images/b16dba83fb514aa896783ebbe2c08245.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略229750个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.49秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: b1a42e27088f41ed93db6142c4164995.jpg

==================================================
![b1a42e27088f41ed93db6142c4164995.jpg](../images/b1a42e27088f41ed93db6142c4164995.jpg)

### response_template答案：
```json
{"题目1":"2/12","题目2":"240kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=2/12", "题目 2": "420×（1-3/7）=240kg", "题目 3": "24÷3=8", "题目 4": "75÷5×3=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170446个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.11秒
### token用量
- total_tokens: 3054
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: b29b0a3526e543a7bc3da3cc80f296db.jpg

==================================================
![b29b0a3526e543a7bc3da3cc80f296db.jpg](../images/b29b0a3526e543a7bc3da3cc80f296db.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980(kg)","题目3":"16(本)","题目4":"45(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1-(\frac{1}{4}+\frac{7}{12})=\frac{1}{6}", "题目 2": "420÷\frac{3}{7}=980(kg)", "题目 3": "24×(1-\frac{1}{3})=16(本)", "题目 4": "75×\frac{3}{5}=45(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略174234个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：10.12秒
### token用量
- total_tokens: 3075
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: b63cac27107f47c2b5b40bc3a9cdb05e.jpg

==================================================
![b63cac27107f47c2b5b40bc3a9cdb05e.jpg](../images/b63cac27107f47c2b5b40bc3a9cdb05e.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"1 2/9","题目5":"1 5/18","题目6":"6/8","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"1.6"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "11/35", "题目 3": "29/42", "题目 4": "12/9", "题目 5": "15/18", "题目 6": "6/8", "题目 7": "0.27", "题目 8": "814/15", "题目 9": "8/11", "题目 10": "1.6"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略117734个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.50秒
### token用量
- total_tokens: 1721
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 182 张图片: b67db8be9d2746349d44c650673295f2.jpg

==================================================
![b67db8be9d2746349d44c650673295f2.jpg](../images/b67db8be9d2746349d44c650673295f2.jpg)

### response_template答案：
```json
{"题目1":"2/12","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-1/4-7/12=2/12", "题目 2": "420÷3×7=980", "题目 3": "24÷3×2=16", "题目 4": "75÷5×3=45"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略204634个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.07秒
### token用量
- total_tokens: 3049
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 188 张图片: bb1499178fb946a98b42df15d9968e90.jpg

==================================================
![bb1499178fb946a98b42df15d9968e90.jpg](../images/bb1499178fb946a98b42df15d9968e90.jpg)

### response_template答案：
```json
{"题目1":"1/16","题目2":"240千克","题目3":"16本","题目4":"30公顷"}
```
### 响应内容：
```json
{"题目 1": "1/12×(1-1/4)=1/16", "题目 2": "420×(1-3/7)=240(千克)", "题目 3": "(24÷3)×2=16(本)", "题目 4": "75×(1-3/5)=30(公顷)"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略166850个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.96秒
### token用量
- total_tokens: 3064
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: c26b4c0c14ff412193da720ed99dad55.jpg

==================================================
![c26b4c0c14ff412193da720ed99dad55.jpg](../images/c26b4c0c14ff412193da720ed99dad55.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3", "题目 9": "3000", "题目 10": "7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略78766个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.14秒
### token用量
- total_tokens: 1055
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 193 张图片: c38c3229eb5d4694a7e981f0dcb4e68d.jpg

==================================================
![c38c3229eb5d4694a7e981f0dcb4e68d.jpg](../images/c38c3229eb5d4694a7e981f0dcb4e68d.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24÷3×2=16本", "题目 4": "75÷5×3=45"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略177594个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.42秒
### token用量
- total_tokens: 3052
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 196 张图片: c9a3d1414682402ba2c5b354c37bfc0a.jpg

==================================================
![c9a3d1414682402ba2c5b354c37bfc0a.jpg](../images/c9a3d1414682402ba2c5b354c37bfc0a.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"18/75"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "42/5", "题目 7": "25/64", "题目 8": "18/75"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略103426个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.71秒
### token用量
- total_tokens: 1543
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 197 张图片: ccd12c0cfac24c6d9a99e14c380b7ee8.jpg

==================================================
![ccd12c0cfac24c6d9a99e14c380b7ee8.jpg](../images/ccd12c0cfac24c6d9a99e14c380b7ee8.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=140×7=980kg", "题目 3": "24÷3×2=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略172310个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.70秒
### token用量
- total_tokens: 3059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: cf3db06ab57e4d75acbb2d44116c9190.jpg

==================================================
![cf3db06ab57e4d75acbb2d44116c9190.jpg](../images/cf3db06ab57e4d75acbb2d44116c9190.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"141/42","题目4":"NAN","题目5":"1 1/18","题目6":"3/8","题目 7":"0.9","题目8":"NAN","题目9":"0","题目10":"3/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "0", "题目 7": "0.09", "题目 8": "NAN", "题目 9": "0", "题目 10": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略101022个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.87秒
### token用量
- total_tokens: 1700
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: d10de923f1a24802ae094d517e438031.jpg

==================================================
![d10de923f1a24802ae094d517e438031.jpg](../images/d10de923f1a24802ae094d517e438031.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"NAN","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略77638个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.01秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: d14c4dbfb5bc40629168fcc5a09cd436.jpg

==================================================
![d14c4dbfb5bc40629168fcc5a09cd436.jpg](../images/d14c4dbfb5bc40629168fcc5a09cd436.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0575"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略30630个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.41秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: d4544e69005e4238bf84931cb24d86b9.jpg

==================================================
![d4544e69005e4238bf84931cb24d86b9.jpg](../images/d4544e69005e4238bf84931cb24d86b9.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.097"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0975"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略24902个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.33秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: d6c39fd02c1f43b5a599dde38f1c0d89.jpg

==================================================
![d6c39fd02c1f43b5a599dde38f1c0d89.jpg](../images/d6c39fd02c1f43b5a599dde38f1c0d89.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"140kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/4=3/12 1-10/12=2/12=1/6", "题目 2": "420÷7=60kg 420÷3=140kg", "题目 3": "(24÷3)×2=16本", "题目 4": "75÷5=15 15×3=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略196446个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.87秒
### token用量
- total_tokens: 3077
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: e00670ac00b4430abc2bd3d7a6e5fc85.jpg

==================================================
![e00670ac00b4430abc2bd3d7a6e5fc85.jpg](../images/e00670ac00b4430abc2bd3d7a6e5fc85.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"NAN","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"NAN","题目8":"3/2"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4 4/5", "题目 7": "", "题目 8": "3/2"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略72006个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.93秒
### token用量
- total_tokens: 1532
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 211 张图片: e2085418bfc44c91921d64a3f6df5d9c.jpg

==================================================
![e2085418bfc44c91921d64a3f6df5d9c.jpg](../images/e2085418bfc44c91921d64a3f6df5d9c.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"5/7","题目3":"7/8","题目4":"1.75","题目5":"0.552","题目6":"4 4/5","题目 7":"25/64","题目8":"6/25"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "7/8", "题目 4": "1.75", "题目 5": "0.552", "题目 6": "4/5", "题目 7": "25/64", "题目 8": "6/25"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略83950个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.21秒
### token用量
- total_tokens: 1541
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: e2f1e7ae919b42c5ad2b232fd40759ca.jpg

==================================================
![e2f1e7ae919b42c5ad2b232fd40759ca.jpg](../images/e2f1e7ae919b42c5ad2b232fd40759ca.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略74438个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.44秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 213 张图片: e2f6f3922d734fdfab4c614243ff4871.jpg

==================================================
![e2f6f3922d734fdfab4c614243ff4871.jpg](../images/e2f6f3922d734fdfab4c614243ff4871.jpg)

### response_template答案：
```json
{"题目1":"481.1","题目2":"10","题目3":"12","题目4":"0.09"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.09"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略26278个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.56秒
### token用量
- total_tokens: 496
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: e6761829d30e4f328f4a2a2733f86613.jpg

==================================================
![e6761829d30e4f328f4a2a2733f86613.jpg](../images/e6761829d30e4f328f4a2a2733f86613.jpg)

### response_template答案：
```json
{"题目1":"3/11","题目2":"20/7","题目3":"1/62","题目4":"1.85","题目5":"5.64","题目6":"4 4/5","题目 7":"25/64","题目8":"18/75"}
```
### 响应内容：
```json
{"题目 1": "3/11", "题目 2": "5/7", "题目 3": "NAN", "题目 4": "1.85", "题目 5": "5.64", "题目 6": "4 4/5", "题目 7": "25/64", "题目 8": "18/75"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略91914个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.32秒
### token用量
- total_tokens: 1542
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: e8eda7de49864852908e47463a1d27af.jpg

==================================================
![e8eda7de49864852908e47463a1d27af.jpg](../images/e8eda7de49864852908e47463a1d27af.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"17/35","题目3":"29/42","题目4":"11/9","题目5":"1 5/18","题目6":"3/4","题目 7":"0.027","题目8":"8 14/15","题目9":"8/11","题目10":"8/5"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "1/5", "题目 3": "29/42", "题目 4": "11/9", "题目 5": "1 5/18", "题目 6": "3/4", "题目 7": "0.027", "题目 8": "8 14/15", "题目 9": "8/11", "题目 10": "8/5"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略122990个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.41秒
### token用量
- total_tokens: 1722
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: e9feb8c0d62f44d3b8b3da84d13e9206.jpg

==================================================
![e9feb8c0d62f44d3b8b3da84d13e9206.jpg](../images/e9feb8c0d62f44d3b8b3da84d13e9206.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0995"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略27362个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：5.83秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: ebc687553ab84bae89fc58e6e1bbf0fb.jpg

==================================================
![ebc687553ab84bae89fc58e6e1bbf0fb.jpg](../images/ebc687553ab84bae89fc58e6e1bbf0fb.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-(1/4+7/12)=1/6", "题目 2": "420÷3×7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略179762个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.14秒
### token用量
- total_tokens: 3051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: ee21276da8b6457897865974d8613a92.jpg

==================================================
![ee21276da8b6457897865974d8613a92.jpg](../images/ee21276da8b6457897865974d8613a92.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"85","题目6":"0.5","题目 7":"NAN","题目8":"NAN","题目9":"4000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "85", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/5", "题目 9": "4000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76530个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.01秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: eef978599ed24584a3617e2ad7524664.jpg

==================================================
![eef978599ed24584a3617e2ad7524664.jpg](../images/eef978599ed24584a3617e2ad7524664.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980 kg","题目3":"16本","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=1/6", "题目 2": "420÷3/7=980kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45公顷"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略172262个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.92秒
### token用量
- total_tokens: 3053
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: ef9d2d23349c4856bbede25d99a5ee8a.jpg

==================================================
![ef9d2d23349c4856bbede25d99a5ee8a.jpg](../images/ef9d2d23349c4856bbede25d99a5ee8a.jpg)

### response_template答案：
```json
{"题目1":"2/12","题目2":"760","题目3":"16","题目4":"45公顷"}
```
### 响应内容：
```json
{"题目 1": "1/4+7/12=10/12 1-10/12=2/12", "题目 2": "420÷3/7=980", "题目 3": "24÷3×2=16", "题目 4": "75÷5×3=45"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略222490个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.60秒
### token用量
- total_tokens: 3061
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: efa31758a21e4c0587d13ff854e75107.jpg

==================================================
![efa31758a21e4c0587d13ff854e75107.jpg](../images/efa31758a21e4c0587d13ff854e75107.jpg)

### response_template答案：
```json
{"题目1":"26/27","题目2":"370(kg)","题目3":"4(本)","题目4":"23(公顷)"}
```
### 响应内容：
```json
{"题目 1": "1-（1/4+7/12）=2/12", "题目 2": "420÷（1+3/7）=370（kg）", "题目 3": "24÷6=4（本）", "题目 4": "75÷3=23（公顷）"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略174650个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.69秒
### token用量
- total_tokens: 3059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: f31c24530b61441faf634675ef9eaa32.jpg

==================================================
![f31c24530b61441faf634675ef9eaa32.jpg](../images/f31c24530b61441faf634675ef9eaa32.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"NAN","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.3", "题目 7": "780", "题目 8": "3/5", "题目 9": "3000", "题目 10": "7/7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略71834个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.81秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 227 张图片: f53b65a196c94f96ac99952e3c536554.jpg

==================================================
![f53b65a196c94f96ac99952e3c536554.jpg](../images/f53b65a196c94f96ac99952e3c536554.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略80858个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.22秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 228 张图片: f56984f2b57143748bf8615e1fe5dbd2.jpg

==================================================
![f56984f2b57143748bf8615e1fe5dbd2.jpg](../images/f56984f2b57143748bf8615e1fe5dbd2.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/7"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略85106个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.20秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 231 张图片: fae27a27abf0456295d3a165486db741.jpg

==================================================
![fae27a27abf0456295d3a165486db741.jpg](../images/fae27a27abf0456295d3a165486db741.jpg)

### response_template答案：
```json
{"题目1":"1/6","题目2":"980Kg","题目3":"16本","题目4":"45"}
```
### 响应内容：
```json
{"题目 1": "1-(1×7/12+1×1/4)=1/6", "题目 2": "420÷3/7=980Kg", "题目 3": "24×2/3=16本", "题目 4": "75×3/5=45"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191338个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.61秒
### token用量
- total_tokens: 3054
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 232 张图片: fbb49a62f2f9428793cef82ef406e9c2.jpg

==================================================
![fbb49a62f2f9428793cef82ef406e9c2.jpg](../images/fbb49a62f2f9428793cef82ef406e9c2.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"780","题目8":"3/7","题目9":"3000","题目10":"7/9"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "1"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略74390个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：7.14秒
### token用量
- total_tokens: 1057
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 233 张图片: fcbf00df24934943b0420f52e320bf30.jpg

==================================================
![fcbf00df24934943b0420f52e320bf30.jpg](../images/fcbf00df24934943b0420f52e320bf30.jpg)

### response_template答案：
```json
{"题目1":"981.1","题目2":"10","题目3":"12","题目4":"0.0975"}
```
### 响应内容：
```json
{"题目 1": "981.1", "题目 2": "10", "题目 3": "12", "题目 4": "0.0575"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略27162个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：6.65秒
### token用量
- total_tokens: 498
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 234 张图片: fcd2f8efc9f440d9a1d5d5c4b6a9357a.jpg

==================================================
![fcd2f8efc9f440d9a1d5d5c4b6a9357a.jpg](../images/fcd2f8efc9f440d9a1d5d5c4b6a9357a.jpg)

### response_template答案：
```json
{"题目1":"1","题目2":"NAN","题目3":"29/35","题目4":"1 3/9","题目5":"NAN","题目6":"NAN","题目 7":"0.9","题目8":"NAN","题目9":"0","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "1", "题目 2": "NAN", "题目 3": "29/35", "题目 4": "1 5/9", "题目 5": "NAN", "题目 6": "NAN", "题目 7": "0.09", "题目 8": "NAN", "题目 9": "0", "题目 10": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略104086个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：8.28秒
### token用量
- total_tokens: 1707
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 236 张图片: fe614c76d0634edaa536e57274d58617.jpg

==================================================
![fe614c76d0634edaa536e57274d58617.jpg](../images/fe614c76d0634edaa536e57274d58617.jpg)

### response_template答案：
```json
{"题目1":"120","题目2":"115","题目3":"1.5","题目4":"80","题目5":"95","题目6":"0.5","题目 7":"NAN","题目8":"NAN","题目9":"3000","题目10":"NAN"}
```
### 响应内容：
```json
{"题目 1": "120", "题目 2": "115", "题目 3": "1.5", "题目 4": "80", "题目 5": "95", "题目 6": "0.5", "题目 7": "780", "题目 8": "3/7", "题目 9": "3000", "题目 10": "7/8"}
```
### 请求体：
```json
{
  "model": "doubao-seed-1-6-250615",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略80730个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 16384,
  "thinking": {
    "type": "disabled"
  },
  "top_p": 0.7,
  "response_format": {
    "type": "json_object"
  }
}
```
### 响应时间：9.64秒
### token用量
- total_tokens: 1059
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有错题处理完成！
==================================================
