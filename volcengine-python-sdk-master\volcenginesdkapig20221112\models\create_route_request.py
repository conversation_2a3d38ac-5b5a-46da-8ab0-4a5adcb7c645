# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRouteRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'advanced_setting': 'AdvancedSettingForCreateRouteInput',
        'enable': 'bool',
        'match_rule': 'MatchRuleForCreateRouteInput',
        'name': 'str',
        'priority': 'int',
        'resource_type': 'str',
        'service_id': 'str',
        'tags': 'list[TagForCreateRouteInput]',
        'upstream_list': 'list[UpstreamListForCreateRouteInput]'
    }

    attribute_map = {
        'advanced_setting': 'AdvancedSetting',
        'enable': 'Enable',
        'match_rule': 'MatchRule',
        'name': 'Name',
        'priority': 'Priority',
        'resource_type': 'ResourceType',
        'service_id': 'ServiceId',
        'tags': 'Tags',
        'upstream_list': 'UpstreamList'
    }

    def __init__(self, advanced_setting=None, enable=None, match_rule=None, name=None, priority=None, resource_type=None, service_id=None, tags=None, upstream_list=None, _configuration=None):  # noqa: E501
        """CreateRouteRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._advanced_setting = None
        self._enable = None
        self._match_rule = None
        self._name = None
        self._priority = None
        self._resource_type = None
        self._service_id = None
        self._tags = None
        self._upstream_list = None
        self.discriminator = None

        if advanced_setting is not None:
            self.advanced_setting = advanced_setting
        if enable is not None:
            self.enable = enable
        if match_rule is not None:
            self.match_rule = match_rule
        self.name = name
        if priority is not None:
            self.priority = priority
        if resource_type is not None:
            self.resource_type = resource_type
        self.service_id = service_id
        if tags is not None:
            self.tags = tags
        if upstream_list is not None:
            self.upstream_list = upstream_list

    @property
    def advanced_setting(self):
        """Gets the advanced_setting of this CreateRouteRequest.  # noqa: E501


        :return: The advanced_setting of this CreateRouteRequest.  # noqa: E501
        :rtype: AdvancedSettingForCreateRouteInput
        """
        return self._advanced_setting

    @advanced_setting.setter
    def advanced_setting(self, advanced_setting):
        """Sets the advanced_setting of this CreateRouteRequest.


        :param advanced_setting: The advanced_setting of this CreateRouteRequest.  # noqa: E501
        :type: AdvancedSettingForCreateRouteInput
        """

        self._advanced_setting = advanced_setting

    @property
    def enable(self):
        """Gets the enable of this CreateRouteRequest.  # noqa: E501


        :return: The enable of this CreateRouteRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this CreateRouteRequest.


        :param enable: The enable of this CreateRouteRequest.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def match_rule(self):
        """Gets the match_rule of this CreateRouteRequest.  # noqa: E501


        :return: The match_rule of this CreateRouteRequest.  # noqa: E501
        :rtype: MatchRuleForCreateRouteInput
        """
        return self._match_rule

    @match_rule.setter
    def match_rule(self, match_rule):
        """Sets the match_rule of this CreateRouteRequest.


        :param match_rule: The match_rule of this CreateRouteRequest.  # noqa: E501
        :type: MatchRuleForCreateRouteInput
        """

        self._match_rule = match_rule

    @property
    def name(self):
        """Gets the name of this CreateRouteRequest.  # noqa: E501


        :return: The name of this CreateRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateRouteRequest.


        :param name: The name of this CreateRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def priority(self):
        """Gets the priority of this CreateRouteRequest.  # noqa: E501


        :return: The priority of this CreateRouteRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this CreateRouteRequest.


        :param priority: The priority of this CreateRouteRequest.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def resource_type(self):
        """Gets the resource_type of this CreateRouteRequest.  # noqa: E501


        :return: The resource_type of this CreateRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this CreateRouteRequest.


        :param resource_type: The resource_type of this CreateRouteRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_id(self):
        """Gets the service_id of this CreateRouteRequest.  # noqa: E501


        :return: The service_id of this CreateRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this CreateRouteRequest.


        :param service_id: The service_id of this CreateRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and service_id is None:
            raise ValueError("Invalid value for `service_id`, must not be `None`")  # noqa: E501

        self._service_id = service_id

    @property
    def tags(self):
        """Gets the tags of this CreateRouteRequest.  # noqa: E501


        :return: The tags of this CreateRouteRequest.  # noqa: E501
        :rtype: list[TagForCreateRouteInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateRouteRequest.


        :param tags: The tags of this CreateRouteRequest.  # noqa: E501
        :type: list[TagForCreateRouteInput]
        """

        self._tags = tags

    @property
    def upstream_list(self):
        """Gets the upstream_list of this CreateRouteRequest.  # noqa: E501


        :return: The upstream_list of this CreateRouteRequest.  # noqa: E501
        :rtype: list[UpstreamListForCreateRouteInput]
        """
        return self._upstream_list

    @upstream_list.setter
    def upstream_list(self, upstream_list):
        """Sets the upstream_list of this CreateRouteRequest.


        :param upstream_list: The upstream_list of this CreateRouteRequest.  # noqa: E501
        :type: list[UpstreamListForCreateRouteInput]
        """

        self._upstream_list = upstream_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRouteRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRouteRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRouteRequest):
            return True

        return self.to_dict() != other.to_dict()
