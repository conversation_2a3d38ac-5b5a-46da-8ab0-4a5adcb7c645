# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsumerForGetConsumerOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comments': 'str',
        'create_time': 'str',
        'gateway_id': 'str',
        'gateway_name': 'str',
        'id': 'str',
        'name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'comments': 'Comments',
        'create_time': 'CreateTime',
        'gateway_id': 'GatewayId',
        'gateway_name': 'GatewayName',
        'id': 'Id',
        'name': 'Name',
        'update_time': 'UpdateTime'
    }

    def __init__(self, comments=None, create_time=None, gateway_id=None, gateway_name=None, id=None, name=None, update_time=None, _configuration=None):  # noqa: E501
        """ConsumerForGetConsumerOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comments = None
        self._create_time = None
        self._gateway_id = None
        self._gateway_name = None
        self._id = None
        self._name = None
        self._update_time = None
        self.discriminator = None

        if comments is not None:
            self.comments = comments
        if create_time is not None:
            self.create_time = create_time
        if gateway_id is not None:
            self.gateway_id = gateway_id
        if gateway_name is not None:
            self.gateway_name = gateway_name
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if update_time is not None:
            self.update_time = update_time

    @property
    def comments(self):
        """Gets the comments of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The comments of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this ConsumerForGetConsumerOutput.


        :param comments: The comments of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def create_time(self):
        """Gets the create_time of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The create_time of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ConsumerForGetConsumerOutput.


        :param create_time: The create_time of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def gateway_id(self):
        """Gets the gateway_id of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The gateway_id of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this ConsumerForGetConsumerOutput.


        :param gateway_id: The gateway_id of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._gateway_id = gateway_id

    @property
    def gateway_name(self):
        """Gets the gateway_name of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The gateway_name of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._gateway_name

    @gateway_name.setter
    def gateway_name(self, gateway_name):
        """Sets the gateway_name of this ConsumerForGetConsumerOutput.


        :param gateway_name: The gateway_name of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._gateway_name = gateway_name

    @property
    def id(self):
        """Gets the id of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The id of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ConsumerForGetConsumerOutput.


        :param id: The id of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The name of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ConsumerForGetConsumerOutput.


        :param name: The name of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def update_time(self):
        """Gets the update_time of this ConsumerForGetConsumerOutput.  # noqa: E501


        :return: The update_time of this ConsumerForGetConsumerOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ConsumerForGetConsumerOutput.


        :param update_time: The update_time of this ConsumerForGetConsumerOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsumerForGetConsumerOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsumerForGetConsumerOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsumerForGetConsumerOutput):
            return True

        return self.to_dict() != other.to_dict()
