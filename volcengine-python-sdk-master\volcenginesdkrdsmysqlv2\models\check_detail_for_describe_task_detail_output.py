# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckDetailForDescribeTaskDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'impact': 'str',
        'issue': 'str'
    }

    attribute_map = {
        'impact': 'Impact',
        'issue': 'Issue'
    }

    def __init__(self, impact=None, issue=None, _configuration=None):  # noqa: E501
        """CheckDetailForDescribeTaskDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._impact = None
        self._issue = None
        self.discriminator = None

        if impact is not None:
            self.impact = impact
        if issue is not None:
            self.issue = issue

    @property
    def impact(self):
        """Gets the impact of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501


        :return: The impact of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._impact

    @impact.setter
    def impact(self, impact):
        """Sets the impact of this CheckDetailForDescribeTaskDetailOutput.


        :param impact: The impact of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501
        :type: str
        """

        self._impact = impact

    @property
    def issue(self):
        """Gets the issue of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501


        :return: The issue of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._issue

    @issue.setter
    def issue(self, issue):
        """Sets the issue of this CheckDetailForDescribeTaskDetailOutput.


        :param issue: The issue of this CheckDetailForDescribeTaskDetailOutput.  # noqa: E501
        :type: str
        """

        self._issue = issue

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckDetailForDescribeTaskDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckDetailForDescribeTaskDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckDetailForDescribeTaskDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
