# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccountForDescribeDBAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_name': 'str',
        'account_privileges': 'list[AccountPrivilegeForDescribeDBAccountsOutput]',
        'account_type': 'str'
    }

    attribute_map = {
        'account_name': 'AccountName',
        'account_privileges': 'AccountPrivileges',
        'account_type': 'AccountType'
    }

    def __init__(self, account_name=None, account_privileges=None, account_type=None, _configuration=None):  # noqa: E501
        """AccountForDescribeDBAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_name = None
        self._account_privileges = None
        self._account_type = None
        self.discriminator = None

        if account_name is not None:
            self.account_name = account_name
        if account_privileges is not None:
            self.account_privileges = account_privileges
        if account_type is not None:
            self.account_type = account_type

    @property
    def account_name(self):
        """Gets the account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this AccountForDescribeDBAccountsOutput.


        :param account_name: The account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def account_privileges(self):
        """Gets the account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: list[AccountPrivilegeForDescribeDBAccountsOutput]
        """
        return self._account_privileges

    @account_privileges.setter
    def account_privileges(self, account_privileges):
        """Sets the account_privileges of this AccountForDescribeDBAccountsOutput.


        :param account_privileges: The account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: list[AccountPrivilegeForDescribeDBAccountsOutput]
        """

        self._account_privileges = account_privileges

    @property
    def account_type(self):
        """Gets the account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_type

    @account_type.setter
    def account_type(self, account_type):
        """Sets the account_type of this AccountForDescribeDBAccountsOutput.


        :param account_type: The account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_type = account_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccountForDescribeDBAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccountForDescribeDBAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccountForDescribeDBAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
