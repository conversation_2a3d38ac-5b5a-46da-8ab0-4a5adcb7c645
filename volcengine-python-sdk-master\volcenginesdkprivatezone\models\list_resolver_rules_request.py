# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResolverRulesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_id': 'int',
        'endpoint_trn': 'str',
        'name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForListResolverRulesInput]',
        'zone_name': 'str'
    }

    attribute_map = {
        'endpoint_id': 'EndpointID',
        'endpoint_trn': 'EndpointTrn',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'zone_name': 'ZoneName'
    }

    def __init__(self, endpoint_id=None, endpoint_trn=None, name=None, page_number=None, page_size=None, project_name=None, tag_filters=None, zone_name=None, _configuration=None):  # noqa: E501
        """ListResolverRulesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_id = None
        self._endpoint_trn = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self._zone_name = None
        self.discriminator = None

        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_trn is not None:
            self.endpoint_trn = endpoint_trn
        if name is not None:
            self.name = name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this ListResolverRulesRequest.  # noqa: E501


        :return: The endpoint_id of this ListResolverRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this ListResolverRulesRequest.


        :param endpoint_id: The endpoint_id of this ListResolverRulesRequest.  # noqa: E501
        :type: int
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_trn(self):
        """Gets the endpoint_trn of this ListResolverRulesRequest.  # noqa: E501


        :return: The endpoint_trn of this ListResolverRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_trn

    @endpoint_trn.setter
    def endpoint_trn(self, endpoint_trn):
        """Sets the endpoint_trn of this ListResolverRulesRequest.


        :param endpoint_trn: The endpoint_trn of this ListResolverRulesRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_trn = endpoint_trn

    @property
    def name(self):
        """Gets the name of this ListResolverRulesRequest.  # noqa: E501


        :return: The name of this ListResolverRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListResolverRulesRequest.


        :param name: The name of this ListResolverRulesRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this ListResolverRulesRequest.  # noqa: E501


        :return: The page_number of this ListResolverRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListResolverRulesRequest.


        :param page_number: The page_number of this ListResolverRulesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListResolverRulesRequest.  # noqa: E501


        :return: The page_size of this ListResolverRulesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListResolverRulesRequest.


        :param page_size: The page_size of this ListResolverRulesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListResolverRulesRequest.  # noqa: E501


        :return: The project_name of this ListResolverRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListResolverRulesRequest.


        :param project_name: The project_name of this ListResolverRulesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this ListResolverRulesRequest.  # noqa: E501


        :return: The tag_filters of this ListResolverRulesRequest.  # noqa: E501
        :rtype: list[TagFilterForListResolverRulesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this ListResolverRulesRequest.


        :param tag_filters: The tag_filters of this ListResolverRulesRequest.  # noqa: E501
        :type: list[TagFilterForListResolverRulesInput]
        """

        self._tag_filters = tag_filters

    @property
    def zone_name(self):
        """Gets the zone_name of this ListResolverRulesRequest.  # noqa: E501


        :return: The zone_name of this ListResolverRulesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this ListResolverRulesRequest.


        :param zone_name: The zone_name of this ListResolverRulesRequest.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResolverRulesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResolverRulesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResolverRulesRequest):
            return True

        return self.to_dict() != other.to_dict()
