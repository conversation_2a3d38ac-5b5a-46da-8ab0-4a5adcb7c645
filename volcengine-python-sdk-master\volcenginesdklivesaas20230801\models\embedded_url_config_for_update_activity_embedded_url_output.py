# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'int',
        'menu_name': 'str',
        'mobile_url': 'str',
        'pc_url': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'menu_name': 'MenuName',
        'mobile_url': 'MobileUrl',
        'pc_url': 'PCUrl'
    }

    def __init__(self, id=None, menu_name=None, mobile_url=None, pc_url=None, _configuration=None):  # noqa: E501
        """EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._menu_name = None
        self._mobile_url = None
        self._pc_url = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if menu_name is not None:
            self.menu_name = menu_name
        if mobile_url is not None:
            self.mobile_url = mobile_url
        if pc_url is not None:
            self.pc_url = pc_url

    @property
    def id(self):
        """Gets the id of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501


        :return: The id of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.


        :param id: The id of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def menu_name(self):
        """Gets the menu_name of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501


        :return: The menu_name of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :rtype: str
        """
        return self._menu_name

    @menu_name.setter
    def menu_name(self, menu_name):
        """Sets the menu_name of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.


        :param menu_name: The menu_name of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :type: str
        """

        self._menu_name = menu_name

    @property
    def mobile_url(self):
        """Gets the mobile_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501


        :return: The mobile_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :rtype: str
        """
        return self._mobile_url

    @mobile_url.setter
    def mobile_url(self, mobile_url):
        """Sets the mobile_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.


        :param mobile_url: The mobile_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :type: str
        """

        self._mobile_url = mobile_url

    @property
    def pc_url(self):
        """Gets the pc_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501


        :return: The pc_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :rtype: str
        """
        return self._pc_url

    @pc_url.setter
    def pc_url(self, pc_url):
        """Sets the pc_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.


        :param pc_url: The pc_url of this EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput.  # noqa: E501
        :type: str
        """

        self._pc_url = pc_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EmbeddedUrlConfigForUpdateActivityEmbeddedUrlOutput):
            return True

        return self.to_dict() != other.to_dict()
