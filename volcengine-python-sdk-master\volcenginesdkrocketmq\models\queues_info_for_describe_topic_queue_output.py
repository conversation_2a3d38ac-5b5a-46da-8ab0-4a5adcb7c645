# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueuesInfoForDescribeTopicQueueOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_offset': 'int',
        'last_update_timestamp': 'int',
        'message_count': 'int',
        'queue_id': 'str',
        'start_offset': 'int'
    }

    attribute_map = {
        'end_offset': 'EndOffset',
        'last_update_timestamp': 'LastUpdateTimestamp',
        'message_count': 'MessageCount',
        'queue_id': 'QueueId',
        'start_offset': 'StartOffset'
    }

    def __init__(self, end_offset=None, last_update_timestamp=None, message_count=None, queue_id=None, start_offset=None, _configuration=None):  # noqa: E501
        """QueuesInfoForDescribeTopicQueueOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_offset = None
        self._last_update_timestamp = None
        self._message_count = None
        self._queue_id = None
        self._start_offset = None
        self.discriminator = None

        if end_offset is not None:
            self.end_offset = end_offset
        if last_update_timestamp is not None:
            self.last_update_timestamp = last_update_timestamp
        if message_count is not None:
            self.message_count = message_count
        if queue_id is not None:
            self.queue_id = queue_id
        if start_offset is not None:
            self.start_offset = start_offset

    @property
    def end_offset(self):
        """Gets the end_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501


        :return: The end_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_offset

    @end_offset.setter
    def end_offset(self, end_offset):
        """Sets the end_offset of this QueuesInfoForDescribeTopicQueueOutput.


        :param end_offset: The end_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :type: int
        """

        self._end_offset = end_offset

    @property
    def last_update_timestamp(self):
        """Gets the last_update_timestamp of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501


        :return: The last_update_timestamp of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_update_timestamp

    @last_update_timestamp.setter
    def last_update_timestamp(self, last_update_timestamp):
        """Sets the last_update_timestamp of this QueuesInfoForDescribeTopicQueueOutput.


        :param last_update_timestamp: The last_update_timestamp of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :type: int
        """

        self._last_update_timestamp = last_update_timestamp

    @property
    def message_count(self):
        """Gets the message_count of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501


        :return: The message_count of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_count

    @message_count.setter
    def message_count(self, message_count):
        """Sets the message_count of this QueuesInfoForDescribeTopicQueueOutput.


        :param message_count: The message_count of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :type: int
        """

        self._message_count = message_count

    @property
    def queue_id(self):
        """Gets the queue_id of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501


        :return: The queue_id of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._queue_id

    @queue_id.setter
    def queue_id(self, queue_id):
        """Sets the queue_id of this QueuesInfoForDescribeTopicQueueOutput.


        :param queue_id: The queue_id of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :type: str
        """

        self._queue_id = queue_id

    @property
    def start_offset(self):
        """Gets the start_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501


        :return: The start_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_offset

    @start_offset.setter
    def start_offset(self, start_offset):
        """Sets the start_offset of this QueuesInfoForDescribeTopicQueueOutput.


        :param start_offset: The start_offset of this QueuesInfoForDescribeTopicQueueOutput.  # noqa: E501
        :type: int
        """

        self._start_offset = start_offset

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueuesInfoForDescribeTopicQueueOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueuesInfoForDescribeTopicQueueOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueuesInfoForDescribeTopicQueueOutput):
            return True

        return self.to_dict() != other.to_dict()
