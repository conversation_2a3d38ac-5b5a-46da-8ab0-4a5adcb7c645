# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTransitRouterPeerAttachmentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'client_token': 'str',
        'description': 'str',
        'peer_transit_router_id': 'str',
        'peer_transit_router_region_id': 'str',
        'tags': 'list[TagForCreateTransitRouterPeerAttachmentInput]',
        'transit_router_attachment_name': 'str',
        'transit_router_bandwidth_package_id': 'str',
        'transit_router_id': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'client_token': 'ClientToken',
        'description': 'Description',
        'peer_transit_router_id': 'PeerTransitRouterId',
        'peer_transit_router_region_id': 'PeerTransitRouterRegionId',
        'tags': 'Tags',
        'transit_router_attachment_name': 'TransitRouterAttachmentName',
        'transit_router_bandwidth_package_id': 'TransitRouterBandwidthPackageId',
        'transit_router_id': 'TransitRouterId'
    }

    def __init__(self, bandwidth=None, client_token=None, description=None, peer_transit_router_id=None, peer_transit_router_region_id=None, tags=None, transit_router_attachment_name=None, transit_router_bandwidth_package_id=None, transit_router_id=None, _configuration=None):  # noqa: E501
        """CreateTransitRouterPeerAttachmentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._client_token = None
        self._description = None
        self._peer_transit_router_id = None
        self._peer_transit_router_region_id = None
        self._tags = None
        self._transit_router_attachment_name = None
        self._transit_router_bandwidth_package_id = None
        self._transit_router_id = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.peer_transit_router_id = peer_transit_router_id
        self.peer_transit_router_region_id = peer_transit_router_region_id
        if tags is not None:
            self.tags = tags
        if transit_router_attachment_name is not None:
            self.transit_router_attachment_name = transit_router_attachment_name
        if transit_router_bandwidth_package_id is not None:
            self.transit_router_bandwidth_package_id = transit_router_bandwidth_package_id
        self.transit_router_id = transit_router_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The bandwidth of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this CreateTransitRouterPeerAttachmentRequest.


        :param bandwidth: The bandwidth of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def client_token(self):
        """Gets the client_token of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The client_token of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTransitRouterPeerAttachmentRequest.


        :param client_token: The client_token of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The description of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTransitRouterPeerAttachmentRequest.


        :param description: The description of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def peer_transit_router_id(self):
        """Gets the peer_transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The peer_transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_transit_router_id

    @peer_transit_router_id.setter
    def peer_transit_router_id(self, peer_transit_router_id):
        """Sets the peer_transit_router_id of this CreateTransitRouterPeerAttachmentRequest.


        :param peer_transit_router_id: The peer_transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_transit_router_id is None:
            raise ValueError("Invalid value for `peer_transit_router_id`, must not be `None`")  # noqa: E501

        self._peer_transit_router_id = peer_transit_router_id

    @property
    def peer_transit_router_region_id(self):
        """Gets the peer_transit_router_region_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The peer_transit_router_region_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_transit_router_region_id

    @peer_transit_router_region_id.setter
    def peer_transit_router_region_id(self, peer_transit_router_region_id):
        """Sets the peer_transit_router_region_id of this CreateTransitRouterPeerAttachmentRequest.


        :param peer_transit_router_region_id: The peer_transit_router_region_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_transit_router_region_id is None:
            raise ValueError("Invalid value for `peer_transit_router_region_id`, must not be `None`")  # noqa: E501

        self._peer_transit_router_region_id = peer_transit_router_region_id

    @property
    def tags(self):
        """Gets the tags of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The tags of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: list[TagForCreateTransitRouterPeerAttachmentInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateTransitRouterPeerAttachmentRequest.


        :param tags: The tags of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: list[TagForCreateTransitRouterPeerAttachmentInput]
        """

        self._tags = tags

    @property
    def transit_router_attachment_name(self):
        """Gets the transit_router_attachment_name of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The transit_router_attachment_name of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_name

    @transit_router_attachment_name.setter
    def transit_router_attachment_name(self, transit_router_attachment_name):
        """Sets the transit_router_attachment_name of this CreateTransitRouterPeerAttachmentRequest.


        :param transit_router_attachment_name: The transit_router_attachment_name of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_name = transit_router_attachment_name

    @property
    def transit_router_bandwidth_package_id(self):
        """Gets the transit_router_bandwidth_package_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The transit_router_bandwidth_package_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_bandwidth_package_id

    @transit_router_bandwidth_package_id.setter
    def transit_router_bandwidth_package_id(self, transit_router_bandwidth_package_id):
        """Sets the transit_router_bandwidth_package_id of this CreateTransitRouterPeerAttachmentRequest.


        :param transit_router_bandwidth_package_id: The transit_router_bandwidth_package_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501


        :return: The transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this CreateTransitRouterPeerAttachmentRequest.


        :param transit_router_id: The transit_router_id of this CreateTransitRouterPeerAttachmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_id is None:
            raise ValueError("Invalid value for `transit_router_id`, must not be `None`")  # noqa: E501

        self._transit_router_id = transit_router_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTransitRouterPeerAttachmentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTransitRouterPeerAttachmentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTransitRouterPeerAttachmentRequest):
            return True

        return self.to_dict() != other.to_dict()
