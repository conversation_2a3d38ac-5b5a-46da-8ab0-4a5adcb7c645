# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBListenerHealthResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'healthy_count': 'int',
        'listener_id': 'str',
        'next_token': 'str',
        'request_id': 'str',
        'results': 'list[ResultForDescribeNLBListenerHealthOutput]',
        'server_group_id': 'str',
        'status': 'str',
        'unhealthy_count': 'int'
    }

    attribute_map = {
        'healthy_count': 'HealthyCount',
        'listener_id': 'ListenerId',
        'next_token': 'NextToken',
        'request_id': 'RequestId',
        'results': 'Results',
        'server_group_id': 'ServerGroupId',
        'status': 'Status',
        'unhealthy_count': 'UnhealthyCount'
    }

    def __init__(self, healthy_count=None, listener_id=None, next_token=None, request_id=None, results=None, server_group_id=None, status=None, unhealthy_count=None, _configuration=None):  # noqa: E501
        """DescribeNLBListenerHealthResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._healthy_count = None
        self._listener_id = None
        self._next_token = None
        self._request_id = None
        self._results = None
        self._server_group_id = None
        self._status = None
        self._unhealthy_count = None
        self.discriminator = None

        if healthy_count is not None:
            self.healthy_count = healthy_count
        if listener_id is not None:
            self.listener_id = listener_id
        if next_token is not None:
            self.next_token = next_token
        if request_id is not None:
            self.request_id = request_id
        if results is not None:
            self.results = results
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if status is not None:
            self.status = status
        if unhealthy_count is not None:
            self.unhealthy_count = unhealthy_count

    @property
    def healthy_count(self):
        """Gets the healthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The healthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: int
        """
        return self._healthy_count

    @healthy_count.setter
    def healthy_count(self, healthy_count):
        """Sets the healthy_count of this DescribeNLBListenerHealthResponse.


        :param healthy_count: The healthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: int
        """

        self._healthy_count = healthy_count

    @property
    def listener_id(self):
        """Gets the listener_id of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The listener_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this DescribeNLBListenerHealthResponse.


        :param listener_id: The listener_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def next_token(self):
        """Gets the next_token of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The next_token of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeNLBListenerHealthResponse.


        :param next_token: The next_token of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def request_id(self):
        """Gets the request_id of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The request_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeNLBListenerHealthResponse.


        :param request_id: The request_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def results(self):
        """Gets the results of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The results of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: list[ResultForDescribeNLBListenerHealthOutput]
        """
        return self._results

    @results.setter
    def results(self, results):
        """Sets the results of this DescribeNLBListenerHealthResponse.


        :param results: The results of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: list[ResultForDescribeNLBListenerHealthOutput]
        """

        self._results = results

    @property
    def server_group_id(self):
        """Gets the server_group_id of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The server_group_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this DescribeNLBListenerHealthResponse.


        :param server_group_id: The server_group_id of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def status(self):
        """Gets the status of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The status of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNLBListenerHealthResponse.


        :param status: The status of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def unhealthy_count(self):
        """Gets the unhealthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501


        :return: The unhealthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :rtype: int
        """
        return self._unhealthy_count

    @unhealthy_count.setter
    def unhealthy_count(self, unhealthy_count):
        """Sets the unhealthy_count of this DescribeNLBListenerHealthResponse.


        :param unhealthy_count: The unhealthy_count of this DescribeNLBListenerHealthResponse.  # noqa: E501
        :type: int
        """

        self._unhealthy_count = unhealthy_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBListenerHealthResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBListenerHealthResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBListenerHealthResponse):
            return True

        return self.to_dict() != other.to_dict()
