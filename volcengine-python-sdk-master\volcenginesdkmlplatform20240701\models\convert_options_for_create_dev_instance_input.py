# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertOptionsForCreateDevInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'internal_sync': 'int',
        'replicas': 'int'
    }

    attribute_map = {
        'internal_sync': 'InternalSync',
        'replicas': 'Replicas'
    }

    def __init__(self, internal_sync=None, replicas=None, _configuration=None):  # noqa: E501
        """ConvertOptionsForCreateDevInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._internal_sync = None
        self._replicas = None
        self.discriminator = None

        if internal_sync is not None:
            self.internal_sync = internal_sync
        if replicas is not None:
            self.replicas = replicas

    @property
    def internal_sync(self):
        """Gets the internal_sync of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501


        :return: The internal_sync of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._internal_sync

    @internal_sync.setter
    def internal_sync(self, internal_sync):
        """Sets the internal_sync of this ConvertOptionsForCreateDevInstanceInput.


        :param internal_sync: The internal_sync of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501
        :type: int
        """

        self._internal_sync = internal_sync

    @property
    def replicas(self):
        """Gets the replicas of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501


        :return: The replicas of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this ConvertOptionsForCreateDevInstanceInput.


        :param replicas: The replicas of this ConvertOptionsForCreateDevInstanceInput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertOptionsForCreateDevInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertOptionsForCreateDevInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertOptionsForCreateDevInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
