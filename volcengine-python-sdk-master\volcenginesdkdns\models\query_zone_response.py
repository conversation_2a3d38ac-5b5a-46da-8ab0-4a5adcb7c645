# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryZoneResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocate_dns_server_list': 'list[str]',
        'auto_renew': 'bool',
        'configuration_code': 'str',
        'dns_security': 'str',
        'expired_time': 'int',
        'instance_no': 'str',
        'is_ns_correct': 'bool',
        'is_sub_domain': 'bool',
        'real_dns_server_list': 'list[str]',
        'record_count': 'int',
        'remark': 'str',
        'stage': 'int',
        'status': 'int',
        'sub_domain_host': 'str',
        'trade_code': 'str',
        'updated_at': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'allocate_dns_server_list': 'AllocateDNSServerList',
        'auto_renew': 'AutoRenew',
        'configuration_code': 'ConfigurationCode',
        'dns_security': 'DnsSecurity',
        'expired_time': 'ExpiredTime',
        'instance_no': 'InstanceNo',
        'is_ns_correct': 'IsNSCorrect',
        'is_sub_domain': 'IsSubDomain',
        'real_dns_server_list': 'RealDNSServerList',
        'record_count': 'RecordCount',
        'remark': 'Remark',
        'stage': 'Stage',
        'status': 'Status',
        'sub_domain_host': 'SubDomainHost',
        'trade_code': 'TradeCode',
        'updated_at': 'UpdatedAt',
        'zone_name': 'ZoneName'
    }

    def __init__(self, allocate_dns_server_list=None, auto_renew=None, configuration_code=None, dns_security=None, expired_time=None, instance_no=None, is_ns_correct=None, is_sub_domain=None, real_dns_server_list=None, record_count=None, remark=None, stage=None, status=None, sub_domain_host=None, trade_code=None, updated_at=None, zone_name=None, _configuration=None):  # noqa: E501
        """QueryZoneResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocate_dns_server_list = None
        self._auto_renew = None
        self._configuration_code = None
        self._dns_security = None
        self._expired_time = None
        self._instance_no = None
        self._is_ns_correct = None
        self._is_sub_domain = None
        self._real_dns_server_list = None
        self._record_count = None
        self._remark = None
        self._stage = None
        self._status = None
        self._sub_domain_host = None
        self._trade_code = None
        self._updated_at = None
        self._zone_name = None
        self.discriminator = None

        if allocate_dns_server_list is not None:
            self.allocate_dns_server_list = allocate_dns_server_list
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if dns_security is not None:
            self.dns_security = dns_security
        if expired_time is not None:
            self.expired_time = expired_time
        if instance_no is not None:
            self.instance_no = instance_no
        if is_ns_correct is not None:
            self.is_ns_correct = is_ns_correct
        if is_sub_domain is not None:
            self.is_sub_domain = is_sub_domain
        if real_dns_server_list is not None:
            self.real_dns_server_list = real_dns_server_list
        if record_count is not None:
            self.record_count = record_count
        if remark is not None:
            self.remark = remark
        if stage is not None:
            self.stage = stage
        if status is not None:
            self.status = status
        if sub_domain_host is not None:
            self.sub_domain_host = sub_domain_host
        if trade_code is not None:
            self.trade_code = trade_code
        if updated_at is not None:
            self.updated_at = updated_at
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def allocate_dns_server_list(self):
        """Gets the allocate_dns_server_list of this QueryZoneResponse.  # noqa: E501


        :return: The allocate_dns_server_list of this QueryZoneResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._allocate_dns_server_list

    @allocate_dns_server_list.setter
    def allocate_dns_server_list(self, allocate_dns_server_list):
        """Sets the allocate_dns_server_list of this QueryZoneResponse.


        :param allocate_dns_server_list: The allocate_dns_server_list of this QueryZoneResponse.  # noqa: E501
        :type: list[str]
        """

        self._allocate_dns_server_list = allocate_dns_server_list

    @property
    def auto_renew(self):
        """Gets the auto_renew of this QueryZoneResponse.  # noqa: E501


        :return: The auto_renew of this QueryZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this QueryZoneResponse.


        :param auto_renew: The auto_renew of this QueryZoneResponse.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def configuration_code(self):
        """Gets the configuration_code of this QueryZoneResponse.  # noqa: E501


        :return: The configuration_code of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this QueryZoneResponse.


        :param configuration_code: The configuration_code of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def dns_security(self):
        """Gets the dns_security of this QueryZoneResponse.  # noqa: E501


        :return: The dns_security of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._dns_security

    @dns_security.setter
    def dns_security(self, dns_security):
        """Sets the dns_security of this QueryZoneResponse.


        :param dns_security: The dns_security of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._dns_security = dns_security

    @property
    def expired_time(self):
        """Gets the expired_time of this QueryZoneResponse.  # noqa: E501


        :return: The expired_time of this QueryZoneResponse.  # noqa: E501
        :rtype: int
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this QueryZoneResponse.


        :param expired_time: The expired_time of this QueryZoneResponse.  # noqa: E501
        :type: int
        """

        self._expired_time = expired_time

    @property
    def instance_no(self):
        """Gets the instance_no of this QueryZoneResponse.  # noqa: E501


        :return: The instance_no of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this QueryZoneResponse.


        :param instance_no: The instance_no of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def is_ns_correct(self):
        """Gets the is_ns_correct of this QueryZoneResponse.  # noqa: E501


        :return: The is_ns_correct of this QueryZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_ns_correct

    @is_ns_correct.setter
    def is_ns_correct(self, is_ns_correct):
        """Sets the is_ns_correct of this QueryZoneResponse.


        :param is_ns_correct: The is_ns_correct of this QueryZoneResponse.  # noqa: E501
        :type: bool
        """

        self._is_ns_correct = is_ns_correct

    @property
    def is_sub_domain(self):
        """Gets the is_sub_domain of this QueryZoneResponse.  # noqa: E501


        :return: The is_sub_domain of this QueryZoneResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_sub_domain

    @is_sub_domain.setter
    def is_sub_domain(self, is_sub_domain):
        """Sets the is_sub_domain of this QueryZoneResponse.


        :param is_sub_domain: The is_sub_domain of this QueryZoneResponse.  # noqa: E501
        :type: bool
        """

        self._is_sub_domain = is_sub_domain

    @property
    def real_dns_server_list(self):
        """Gets the real_dns_server_list of this QueryZoneResponse.  # noqa: E501


        :return: The real_dns_server_list of this QueryZoneResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._real_dns_server_list

    @real_dns_server_list.setter
    def real_dns_server_list(self, real_dns_server_list):
        """Sets the real_dns_server_list of this QueryZoneResponse.


        :param real_dns_server_list: The real_dns_server_list of this QueryZoneResponse.  # noqa: E501
        :type: list[str]
        """

        self._real_dns_server_list = real_dns_server_list

    @property
    def record_count(self):
        """Gets the record_count of this QueryZoneResponse.  # noqa: E501


        :return: The record_count of this QueryZoneResponse.  # noqa: E501
        :rtype: int
        """
        return self._record_count

    @record_count.setter
    def record_count(self, record_count):
        """Sets the record_count of this QueryZoneResponse.


        :param record_count: The record_count of this QueryZoneResponse.  # noqa: E501
        :type: int
        """

        self._record_count = record_count

    @property
    def remark(self):
        """Gets the remark of this QueryZoneResponse.  # noqa: E501


        :return: The remark of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this QueryZoneResponse.


        :param remark: The remark of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def stage(self):
        """Gets the stage of this QueryZoneResponse.  # noqa: E501


        :return: The stage of this QueryZoneResponse.  # noqa: E501
        :rtype: int
        """
        return self._stage

    @stage.setter
    def stage(self, stage):
        """Sets the stage of this QueryZoneResponse.


        :param stage: The stage of this QueryZoneResponse.  # noqa: E501
        :type: int
        """

        self._stage = stage

    @property
    def status(self):
        """Gets the status of this QueryZoneResponse.  # noqa: E501


        :return: The status of this QueryZoneResponse.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this QueryZoneResponse.


        :param status: The status of this QueryZoneResponse.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def sub_domain_host(self):
        """Gets the sub_domain_host of this QueryZoneResponse.  # noqa: E501


        :return: The sub_domain_host of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._sub_domain_host

    @sub_domain_host.setter
    def sub_domain_host(self, sub_domain_host):
        """Sets the sub_domain_host of this QueryZoneResponse.


        :param sub_domain_host: The sub_domain_host of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._sub_domain_host = sub_domain_host

    @property
    def trade_code(self):
        """Gets the trade_code of this QueryZoneResponse.  # noqa: E501


        :return: The trade_code of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._trade_code

    @trade_code.setter
    def trade_code(self, trade_code):
        """Sets the trade_code of this QueryZoneResponse.


        :param trade_code: The trade_code of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._trade_code = trade_code

    @property
    def updated_at(self):
        """Gets the updated_at of this QueryZoneResponse.  # noqa: E501


        :return: The updated_at of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this QueryZoneResponse.


        :param updated_at: The updated_at of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def zone_name(self):
        """Gets the zone_name of this QueryZoneResponse.  # noqa: E501


        :return: The zone_name of this QueryZoneResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this QueryZoneResponse.


        :param zone_name: The zone_name of this QueryZoneResponse.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryZoneResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryZoneResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryZoneResponse):
            return True

        return self.to_dict() != other.to_dict()
