# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DcInfoForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dc': 'str',
        'dc_name': 'str',
        'isp': 'int',
        'region': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'dc': 'Dc',
        'dc_name': 'DcName',
        'isp': 'Isp',
        'region': 'Region',
        'zone_id': 'ZoneId'
    }

    def __init__(self, dc=None, dc_name=None, isp=None, region=None, zone_id=None, _configuration=None):  # noqa: E501
        """DcInfoForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dc = None
        self._dc_name = None
        self._isp = None
        self._region = None
        self._zone_id = None
        self.discriminator = None

        if dc is not None:
            self.dc = dc
        if dc_name is not None:
            self.dc_name = dc_name
        if isp is not None:
            self.isp = isp
        if region is not None:
            self.region = region
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def dc(self):
        """Gets the dc of this DcInfoForListPodOutput.  # noqa: E501


        :return: The dc of this DcInfoForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._dc

    @dc.setter
    def dc(self, dc):
        """Sets the dc of this DcInfoForListPodOutput.


        :param dc: The dc of this DcInfoForListPodOutput.  # noqa: E501
        :type: str
        """

        self._dc = dc

    @property
    def dc_name(self):
        """Gets the dc_name of this DcInfoForListPodOutput.  # noqa: E501


        :return: The dc_name of this DcInfoForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._dc_name

    @dc_name.setter
    def dc_name(self, dc_name):
        """Sets the dc_name of this DcInfoForListPodOutput.


        :param dc_name: The dc_name of this DcInfoForListPodOutput.  # noqa: E501
        :type: str
        """

        self._dc_name = dc_name

    @property
    def isp(self):
        """Gets the isp of this DcInfoForListPodOutput.  # noqa: E501


        :return: The isp of this DcInfoForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DcInfoForListPodOutput.


        :param isp: The isp of this DcInfoForListPodOutput.  # noqa: E501
        :type: int
        """

        self._isp = isp

    @property
    def region(self):
        """Gets the region of this DcInfoForListPodOutput.  # noqa: E501


        :return: The region of this DcInfoForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DcInfoForListPodOutput.


        :param region: The region of this DcInfoForListPodOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def zone_id(self):
        """Gets the zone_id of this DcInfoForListPodOutput.  # noqa: E501


        :return: The zone_id of this DcInfoForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DcInfoForListPodOutput.


        :param zone_id: The zone_id of this DcInfoForListPodOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DcInfoForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DcInfoForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DcInfoForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
