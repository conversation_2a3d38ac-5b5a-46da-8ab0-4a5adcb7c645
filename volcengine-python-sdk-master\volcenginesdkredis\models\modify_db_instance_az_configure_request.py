# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceAZConfigureRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apply_immediately': 'bool',
        'backup_point_name': 'str',
        'client_token': 'str',
        'configure_nodes': 'list[ConfigureNodeForModifyDBInstanceAZConfigureInput]',
        'create_backup': 'bool',
        'instance_id': 'str',
        'multi_az': 'str'
    }

    attribute_map = {
        'apply_immediately': 'ApplyImmediately',
        'backup_point_name': 'BackupPointName',
        'client_token': 'ClientToken',
        'configure_nodes': 'ConfigureNodes',
        'create_backup': 'CreateBackup',
        'instance_id': 'InstanceId',
        'multi_az': 'MultiAZ'
    }

    def __init__(self, apply_immediately=None, backup_point_name=None, client_token=None, configure_nodes=None, create_backup=None, instance_id=None, multi_az=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceAZConfigureRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apply_immediately = None
        self._backup_point_name = None
        self._client_token = None
        self._configure_nodes = None
        self._create_backup = None
        self._instance_id = None
        self._multi_az = None
        self.discriminator = None

        self.apply_immediately = apply_immediately
        if backup_point_name is not None:
            self.backup_point_name = backup_point_name
        if client_token is not None:
            self.client_token = client_token
        if configure_nodes is not None:
            self.configure_nodes = configure_nodes
        if create_backup is not None:
            self.create_backup = create_backup
        self.instance_id = instance_id
        self.multi_az = multi_az

    @property
    def apply_immediately(self):
        """Gets the apply_immediately of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The apply_immediately of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: bool
        """
        return self._apply_immediately

    @apply_immediately.setter
    def apply_immediately(self, apply_immediately):
        """Sets the apply_immediately of this ModifyDBInstanceAZConfigureRequest.


        :param apply_immediately: The apply_immediately of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and apply_immediately is None:
            raise ValueError("Invalid value for `apply_immediately`, must not be `None`")  # noqa: E501

        self._apply_immediately = apply_immediately

    @property
    def backup_point_name(self):
        """Gets the backup_point_name of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The backup_point_name of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_name

    @backup_point_name.setter
    def backup_point_name(self, backup_point_name):
        """Sets the backup_point_name of this ModifyDBInstanceAZConfigureRequest.


        :param backup_point_name: The backup_point_name of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_name = backup_point_name

    @property
    def client_token(self):
        """Gets the client_token of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The client_token of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyDBInstanceAZConfigureRequest.


        :param client_token: The client_token of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def configure_nodes(self):
        """Gets the configure_nodes of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The configure_nodes of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: list[ConfigureNodeForModifyDBInstanceAZConfigureInput]
        """
        return self._configure_nodes

    @configure_nodes.setter
    def configure_nodes(self, configure_nodes):
        """Sets the configure_nodes of this ModifyDBInstanceAZConfigureRequest.


        :param configure_nodes: The configure_nodes of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: list[ConfigureNodeForModifyDBInstanceAZConfigureInput]
        """

        self._configure_nodes = configure_nodes

    @property
    def create_backup(self):
        """Gets the create_backup of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The create_backup of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: bool
        """
        return self._create_backup

    @create_backup.setter
    def create_backup(self, create_backup):
        """Sets the create_backup of this ModifyDBInstanceAZConfigureRequest.


        :param create_backup: The create_backup of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: bool
        """

        self._create_backup = create_backup

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceAZConfigureRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def multi_az(self):
        """Gets the multi_az of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501


        :return: The multi_az of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :rtype: str
        """
        return self._multi_az

    @multi_az.setter
    def multi_az(self, multi_az):
        """Sets the multi_az of this ModifyDBInstanceAZConfigureRequest.


        :param multi_az: The multi_az of this ModifyDBInstanceAZConfigureRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and multi_az is None:
            raise ValueError("Invalid value for `multi_az`, must not be `None`")  # noqa: E501

        self._multi_az = multi_az

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceAZConfigureRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceAZConfigureRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceAZConfigureRequest):
            return True

        return self.to_dict() != other.to_dict()
