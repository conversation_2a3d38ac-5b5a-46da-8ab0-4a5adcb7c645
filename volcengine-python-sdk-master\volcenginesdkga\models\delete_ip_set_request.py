# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteIPSetRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'delete_ip_sets': 'list[str]',
        'ip_set_id': 'str',
        'ip_version': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'delete_ip_sets': 'DeleteIPSets',
        'ip_set_id': 'IPSetId',
        'ip_version': 'IPVersion'
    }

    def __init__(self, accelerator_id=None, delete_ip_sets=None, ip_set_id=None, ip_version=None, _configuration=None):  # noqa: E501
        """DeleteIPSetRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._delete_ip_sets = None
        self._ip_set_id = None
        self._ip_version = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        if delete_ip_sets is not None:
            self.delete_ip_sets = delete_ip_sets
        self.ip_set_id = ip_set_id
        self.ip_version = ip_version

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this DeleteIPSetRequest.  # noqa: E501


        :return: The accelerator_id of this DeleteIPSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this DeleteIPSetRequest.


        :param accelerator_id: The accelerator_id of this DeleteIPSetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def delete_ip_sets(self):
        """Gets the delete_ip_sets of this DeleteIPSetRequest.  # noqa: E501


        :return: The delete_ip_sets of this DeleteIPSetRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._delete_ip_sets

    @delete_ip_sets.setter
    def delete_ip_sets(self, delete_ip_sets):
        """Sets the delete_ip_sets of this DeleteIPSetRequest.


        :param delete_ip_sets: The delete_ip_sets of this DeleteIPSetRequest.  # noqa: E501
        :type: list[str]
        """

        self._delete_ip_sets = delete_ip_sets

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this DeleteIPSetRequest.  # noqa: E501


        :return: The ip_set_id of this DeleteIPSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this DeleteIPSetRequest.


        :param ip_set_id: The ip_set_id of this DeleteIPSetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_set_id is None:
            raise ValueError("Invalid value for `ip_set_id`, must not be `None`")  # noqa: E501

        self._ip_set_id = ip_set_id

    @property
    def ip_version(self):
        """Gets the ip_version of this DeleteIPSetRequest.  # noqa: E501


        :return: The ip_version of this DeleteIPSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this DeleteIPSetRequest.


        :param ip_version: The ip_version of this DeleteIPSetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_version is None:
            raise ValueError("Invalid value for `ip_version`, must not be `None`")  # noqa: E501

        self._ip_version = ip_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteIPSetRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteIPSetRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteIPSetRequest):
            return True

        return self.to_dict() != other.to_dict()
