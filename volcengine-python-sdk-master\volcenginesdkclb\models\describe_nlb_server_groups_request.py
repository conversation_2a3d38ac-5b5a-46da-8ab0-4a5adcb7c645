# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBServerGroupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'server_group_ids': 'list[str]',
        'server_group_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeNLBServerGroupsInput]',
        'type': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'server_group_ids': 'ServerGroupIds',
        'server_group_name': 'ServerGroupName',
        'tag_filters': 'TagFilters',
        'type': 'Type',
        'vpc_id': 'VpcId'
    }

    def __init__(self, max_results=None, next_token=None, project_name=None, server_group_ids=None, server_group_name=None, tag_filters=None, type=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeNLBServerGroupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._server_group_ids = None
        self._server_group_name = None
        self._tag_filters = None
        self._type = None
        self._vpc_id = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if server_group_ids is not None:
            self.server_group_ids = server_group_ids
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if type is not None:
            self.type = type
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def max_results(self):
        """Gets the max_results of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The max_results of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeNLBServerGroupsRequest.


        :param max_results: The max_results of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The next_token of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeNLBServerGroupsRequest.


        :param next_token: The next_token of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The project_name of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeNLBServerGroupsRequest.


        :param project_name: The project_name of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def server_group_ids(self):
        """Gets the server_group_ids of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The server_group_ids of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._server_group_ids

    @server_group_ids.setter
    def server_group_ids(self, server_group_ids):
        """Sets the server_group_ids of this DescribeNLBServerGroupsRequest.


        :param server_group_ids: The server_group_ids of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: list[str]
        """

        self._server_group_ids = server_group_ids

    @property
    def server_group_name(self):
        """Gets the server_group_name of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The server_group_name of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this DescribeNLBServerGroupsRequest.


        :param server_group_name: The server_group_name of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeNLBServerGroupsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeNLBServerGroupsRequest.


        :param tag_filters: The tag_filters of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeNLBServerGroupsInput]
        """

        self._tag_filters = tag_filters

    @property
    def type(self):
        """Gets the type of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The type of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DescribeNLBServerGroupsRequest.


        :param type: The type of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeNLBServerGroupsRequest.  # noqa: E501


        :return: The vpc_id of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeNLBServerGroupsRequest.


        :param vpc_id: The vpc_id of this DescribeNLBServerGroupsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBServerGroupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBServerGroupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBServerGroupsRequest):
            return True

        return self.to_dict() != other.to_dict()
