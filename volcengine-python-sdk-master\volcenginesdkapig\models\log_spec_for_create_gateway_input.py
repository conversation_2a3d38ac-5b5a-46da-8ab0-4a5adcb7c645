# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LogSpecForCreateGatewayInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable': 'bool',
        'project_id': 'str',
        'topic_id': 'str'
    }

    attribute_map = {
        'enable': 'Enable',
        'project_id': 'ProjectId',
        'topic_id': 'TopicId'
    }

    def __init__(self, enable=None, project_id=None, topic_id=None, _configuration=None):  # noqa: E501
        """LogSpecForCreateGatewayInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable = None
        self._project_id = None
        self._topic_id = None
        self.discriminator = None

        if enable is not None:
            self.enable = enable
        if project_id is not None:
            self.project_id = project_id
        if topic_id is not None:
            self.topic_id = topic_id

    @property
    def enable(self):
        """Gets the enable of this LogSpecForCreateGatewayInput.  # noqa: E501


        :return: The enable of this LogSpecForCreateGatewayInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this LogSpecForCreateGatewayInput.


        :param enable: The enable of this LogSpecForCreateGatewayInput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def project_id(self):
        """Gets the project_id of this LogSpecForCreateGatewayInput.  # noqa: E501


        :return: The project_id of this LogSpecForCreateGatewayInput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this LogSpecForCreateGatewayInput.


        :param project_id: The project_id of this LogSpecForCreateGatewayInput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def topic_id(self):
        """Gets the topic_id of this LogSpecForCreateGatewayInput.  # noqa: E501


        :return: The topic_id of this LogSpecForCreateGatewayInput.  # noqa: E501
        :rtype: str
        """
        return self._topic_id

    @topic_id.setter
    def topic_id(self, topic_id):
        """Sets the topic_id of this LogSpecForCreateGatewayInput.


        :param topic_id: The topic_id of this LogSpecForCreateGatewayInput.  # noqa: E501
        :type: str
        """

        self._topic_id = topic_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LogSpecForCreateGatewayInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LogSpecForCreateGatewayInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LogSpecForCreateGatewayInput):
            return True

        return self.to_dict() != other.to_dict()
