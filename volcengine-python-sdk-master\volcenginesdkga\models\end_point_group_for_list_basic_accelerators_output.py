# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndPointGroupForListBasicAcceleratorsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_point_group_id': 'str',
        'region': 'str'
    }

    attribute_map = {
        'end_point_group_id': 'EndPointGroupId',
        'region': 'Region'
    }

    def __init__(self, end_point_group_id=None, region=None, _configuration=None):  # noqa: E501
        """EndPointGroupForListBasicAcceleratorsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_point_group_id = None
        self._region = None
        self.discriminator = None

        if end_point_group_id is not None:
            self.end_point_group_id = end_point_group_id
        if region is not None:
            self.region = region

    @property
    def end_point_group_id(self):
        """Gets the end_point_group_id of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501


        :return: The end_point_group_id of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_point_group_id

    @end_point_group_id.setter
    def end_point_group_id(self, end_point_group_id):
        """Sets the end_point_group_id of this EndPointGroupForListBasicAcceleratorsOutput.


        :param end_point_group_id: The end_point_group_id of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501
        :type: str
        """

        self._end_point_group_id = end_point_group_id

    @property
    def region(self):
        """Gets the region of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501


        :return: The region of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this EndPointGroupForListBasicAcceleratorsOutput.


        :param region: The region of this EndPointGroupForListBasicAcceleratorsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndPointGroupForListBasicAcceleratorsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndPointGroupForListBasicAcceleratorsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndPointGroupForListBasicAcceleratorsOutput):
            return True

        return self.to_dict() != other.to_dict()
