# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBaselineCheckDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'description_cn': 'str',
        'level': 'str',
        'name': 'str',
        'name_cn': 'str',
        'resolve': 'str',
        'resolve_cn': 'str',
        'type': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'description_cn': 'DescriptionCn',
        'level': 'Level',
        'name': 'Name',
        'name_cn': 'NameCn',
        'resolve': 'Resolve',
        'resolve_cn': 'ResolveCn',
        'type': 'Type'
    }

    def __init__(self, description=None, description_cn=None, level=None, name=None, name_cn=None, resolve=None, resolve_cn=None, type=None, _configuration=None):  # noqa: E501
        """ListBaselineCheckDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._description_cn = None
        self._level = None
        self._name = None
        self._name_cn = None
        self._resolve = None
        self._resolve_cn = None
        self._type = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if description_cn is not None:
            self.description_cn = description_cn
        if level is not None:
            self.level = level
        if name is not None:
            self.name = name
        if name_cn is not None:
            self.name_cn = name_cn
        if resolve is not None:
            self.resolve = resolve
        if resolve_cn is not None:
            self.resolve_cn = resolve_cn
        if type is not None:
            self.type = type

    @property
    def description(self):
        """Gets the description of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The description of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ListBaselineCheckDetailResponse.


        :param description: The description of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def description_cn(self):
        """Gets the description_cn of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The description_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._description_cn

    @description_cn.setter
    def description_cn(self, description_cn):
        """Sets the description_cn of this ListBaselineCheckDetailResponse.


        :param description_cn: The description_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._description_cn = description_cn

    @property
    def level(self):
        """Gets the level of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The level of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ListBaselineCheckDetailResponse.


        :param level: The level of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def name(self):
        """Gets the name of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The name of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListBaselineCheckDetailResponse.


        :param name: The name of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def name_cn(self):
        """Gets the name_cn of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The name_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._name_cn

    @name_cn.setter
    def name_cn(self, name_cn):
        """Sets the name_cn of this ListBaselineCheckDetailResponse.


        :param name_cn: The name_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._name_cn = name_cn

    @property
    def resolve(self):
        """Gets the resolve of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The resolve of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resolve

    @resolve.setter
    def resolve(self, resolve):
        """Sets the resolve of this ListBaselineCheckDetailResponse.


        :param resolve: The resolve of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._resolve = resolve

    @property
    def resolve_cn(self):
        """Gets the resolve_cn of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The resolve_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resolve_cn

    @resolve_cn.setter
    def resolve_cn(self, resolve_cn):
        """Sets the resolve_cn of this ListBaselineCheckDetailResponse.


        :param resolve_cn: The resolve_cn of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._resolve_cn = resolve_cn

    @property
    def type(self):
        """Gets the type of this ListBaselineCheckDetailResponse.  # noqa: E501


        :return: The type of this ListBaselineCheckDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ListBaselineCheckDetailResponse.


        :param type: The type of this ListBaselineCheckDetailResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBaselineCheckDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBaselineCheckDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBaselineCheckDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
