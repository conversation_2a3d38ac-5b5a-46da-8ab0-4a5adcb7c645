# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActivityCouponPickDataForGetActivityCouponPickDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'external_user_id': 'str',
        'pickup_time': 'int',
        'user_id': 'int',
        'user_nick_name': 'str'
    }

    attribute_map = {
        'external_user_id': 'ExternalUserId',
        'pickup_time': 'PickupTime',
        'user_id': 'UserId',
        'user_nick_name': 'UserNickName'
    }

    def __init__(self, external_user_id=None, pickup_time=None, user_id=None, user_nick_name=None, _configuration=None):  # noqa: E501
        """ActivityCouponPickDataForGetActivityCouponPickDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._external_user_id = None
        self._pickup_time = None
        self._user_id = None
        self._user_nick_name = None
        self.discriminator = None

        if external_user_id is not None:
            self.external_user_id = external_user_id
        if pickup_time is not None:
            self.pickup_time = pickup_time
        if user_id is not None:
            self.user_id = user_id
        if user_nick_name is not None:
            self.user_nick_name = user_nick_name

    @property
    def external_user_id(self):
        """Gets the external_user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501


        :return: The external_user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.


        :param external_user_id: The external_user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def pickup_time(self):
        """Gets the pickup_time of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501


        :return: The pickup_time of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._pickup_time

    @pickup_time.setter
    def pickup_time(self, pickup_time):
        """Sets the pickup_time of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.


        :param pickup_time: The pickup_time of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :type: int
        """

        self._pickup_time = pickup_time

    @property
    def user_id(self):
        """Gets the user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501


        :return: The user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.


        :param user_id: The user_id of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_nick_name(self):
        """Gets the user_nick_name of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501


        :return: The user_nick_name of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_nick_name

    @user_nick_name.setter
    def user_nick_name(self, user_nick_name):
        """Sets the user_nick_name of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.


        :param user_nick_name: The user_nick_name of this ActivityCouponPickDataForGetActivityCouponPickDataOutput.  # noqa: E501
        :type: str
        """

        self._user_nick_name = user_nick_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActivityCouponPickDataForGetActivityCouponPickDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActivityCouponPickDataForGetActivityCouponPickDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActivityCouponPickDataForGetActivityCouponPickDataOutput):
            return True

        return self.to_dict() != other.to_dict()
