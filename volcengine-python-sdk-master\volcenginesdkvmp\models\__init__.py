# coding: utf-8

# flake8: noqa
"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkvmp.models.active_for_create_notify_template_input import ActiveForCreateNotifyTemplateInput
from volcenginesdkvmp.models.active_for_list_notify_templates_output import ActiveForListNotifyTemplatesOutput
from volcenginesdkvmp.models.active_for_update_notify_template_input import ActiveForUpdateNotifyTemplateInput
from volcenginesdkvmp.models.alerting_rule_query_for_get_alert_output import AlertingRuleQueryForGetAlertOutput
from volcenginesdkvmp.models.alerting_rule_query_for_list_alerts_output import AlertingRuleQueryForListAlertsOutput
from volcenginesdkvmp.models.annotation_for_create_alerting_rule_input import AnnotationForCreateAlertingRuleInput
from volcenginesdkvmp.models.annotation_for_get_alerting_rule_output import AnnotationForGetAlertingRuleOutput
from volcenginesdkvmp.models.annotation_for_list_alerting_rules_output import AnnotationForListAlertingRulesOutput
from volcenginesdkvmp.models.annotation_for_update_alerting_rule_input import AnnotationForUpdateAlertingRuleInput
from volcenginesdkvmp.models.create_alerting_rule_request import CreateAlertingRuleRequest
from volcenginesdkvmp.models.create_alerting_rule_response import CreateAlertingRuleResponse
from volcenginesdkvmp.models.create_contact_group_request import CreateContactGroupRequest
from volcenginesdkvmp.models.create_contact_group_response import CreateContactGroupResponse
from volcenginesdkvmp.models.create_contact_request import CreateContactRequest
from volcenginesdkvmp.models.create_contact_response import CreateContactResponse
from volcenginesdkvmp.models.create_notify_group_policy_request import CreateNotifyGroupPolicyRequest
from volcenginesdkvmp.models.create_notify_group_policy_response import CreateNotifyGroupPolicyResponse
from volcenginesdkvmp.models.create_notify_policy_request import CreateNotifyPolicyRequest
from volcenginesdkvmp.models.create_notify_policy_response import CreateNotifyPolicyResponse
from volcenginesdkvmp.models.create_notify_template_request import CreateNotifyTemplateRequest
from volcenginesdkvmp.models.create_notify_template_response import CreateNotifyTemplateResponse
from volcenginesdkvmp.models.create_rule_file_request import CreateRuleFileRequest
from volcenginesdkvmp.models.create_rule_file_response import CreateRuleFileResponse
from volcenginesdkvmp.models.create_workspace_request import CreateWorkspaceRequest
from volcenginesdkvmp.models.create_workspace_response import CreateWorkspaceResponse
from volcenginesdkvmp.models.data_for_delete_alerting_rules_output import DataForDeleteAlertingRulesOutput
from volcenginesdkvmp.models.data_for_delete_contact_groups_output import DataForDeleteContactGroupsOutput
from volcenginesdkvmp.models.data_for_delete_contacts_output import DataForDeleteContactsOutput
from volcenginesdkvmp.models.data_for_disable_alerting_rules_output import DataForDisableAlertingRulesOutput
from volcenginesdkvmp.models.data_for_enable_alerting_rules_output import DataForEnableAlertingRulesOutput
from volcenginesdkvmp.models.delete_alerting_rules_request import DeleteAlertingRulesRequest
from volcenginesdkvmp.models.delete_alerting_rules_response import DeleteAlertingRulesResponse
from volcenginesdkvmp.models.delete_contact_groups_request import DeleteContactGroupsRequest
from volcenginesdkvmp.models.delete_contact_groups_response import DeleteContactGroupsResponse
from volcenginesdkvmp.models.delete_contacts_request import DeleteContactsRequest
from volcenginesdkvmp.models.delete_contacts_response import DeleteContactsResponse
from volcenginesdkvmp.models.delete_notify_group_policy_request import DeleteNotifyGroupPolicyRequest
from volcenginesdkvmp.models.delete_notify_group_policy_response import DeleteNotifyGroupPolicyResponse
from volcenginesdkvmp.models.delete_notify_policy_request import DeleteNotifyPolicyRequest
from volcenginesdkvmp.models.delete_notify_policy_response import DeleteNotifyPolicyResponse
from volcenginesdkvmp.models.delete_notify_template_request import DeleteNotifyTemplateRequest
from volcenginesdkvmp.models.delete_notify_template_response import DeleteNotifyTemplateResponse
from volcenginesdkvmp.models.delete_rule_file_request import DeleteRuleFileRequest
from volcenginesdkvmp.models.delete_rule_file_response import DeleteRuleFileResponse
from volcenginesdkvmp.models.delete_workspace_request import DeleteWorkspaceRequest
from volcenginesdkvmp.models.delete_workspace_response import DeleteWorkspaceResponse
from volcenginesdkvmp.models.ding_talk_bot_webhook_for_create_contact_input import DingTalkBotWebhookForCreateContactInput
from volcenginesdkvmp.models.ding_talk_bot_webhook_for_get_contact_output import DingTalkBotWebhookForGetContactOutput
from volcenginesdkvmp.models.ding_talk_bot_webhook_for_list_contacts_output import DingTalkBotWebhookForListContactsOutput
from volcenginesdkvmp.models.ding_talk_bot_webhook_for_update_contact_input import DingTalkBotWebhookForUpdateContactInput
from volcenginesdkvmp.models.disable_alerting_rules_request import DisableAlertingRulesRequest
from volcenginesdkvmp.models.disable_alerting_rules_response import DisableAlertingRulesResponse
from volcenginesdkvmp.models.enable_alerting_rules_request import EnableAlertingRulesRequest
from volcenginesdkvmp.models.enable_alerting_rules_response import EnableAlertingRulesResponse
from volcenginesdkvmp.models.error_for_delete_alerting_rules_output import ErrorForDeleteAlertingRulesOutput
from volcenginesdkvmp.models.error_for_delete_contact_groups_output import ErrorForDeleteContactGroupsOutput
from volcenginesdkvmp.models.error_for_delete_contacts_output import ErrorForDeleteContactsOutput
from volcenginesdkvmp.models.error_for_disable_alerting_rules_output import ErrorForDisableAlertingRulesOutput
from volcenginesdkvmp.models.error_for_enable_alerting_rules_output import ErrorForEnableAlertingRulesOutput
from volcenginesdkvmp.models.filter_for_list_alert_samples_input import FilterForListAlertSamplesInput
from volcenginesdkvmp.models.filter_for_list_alerting_rules_input import FilterForListAlertingRulesInput
from volcenginesdkvmp.models.filter_for_list_alerts_input import FilterForListAlertsInput
from volcenginesdkvmp.models.filter_for_list_contact_groups_input import FilterForListContactGroupsInput
from volcenginesdkvmp.models.filter_for_list_contacts_input import FilterForListContactsInput
from volcenginesdkvmp.models.filter_for_list_notify_group_policies_input import FilterForListNotifyGroupPoliciesInput
from volcenginesdkvmp.models.filter_for_list_notify_policies_input import FilterForListNotifyPoliciesInput
from volcenginesdkvmp.models.filter_for_list_notify_templates_input import FilterForListNotifyTemplatesInput
from volcenginesdkvmp.models.filter_for_list_rule_files_input import FilterForListRuleFilesInput
from volcenginesdkvmp.models.filter_for_list_rules_input import FilterForListRulesInput
from volcenginesdkvmp.models.filters_for_list_workspace_instance_types_input import FiltersForListWorkspaceInstanceTypesInput
from volcenginesdkvmp.models.filters_for_list_workspace_status_input import FiltersForListWorkspaceStatusInput
from volcenginesdkvmp.models.filters_for_list_workspaces_input import FiltersForListWorkspacesInput
from volcenginesdkvmp.models.get_alert_request import GetAlertRequest
from volcenginesdkvmp.models.get_alert_response import GetAlertResponse
from volcenginesdkvmp.models.get_alerting_rule_request import GetAlertingRuleRequest
from volcenginesdkvmp.models.get_alerting_rule_response import GetAlertingRuleResponse
from volcenginesdkvmp.models.get_contact_group_request import GetContactGroupRequest
from volcenginesdkvmp.models.get_contact_group_response import GetContactGroupResponse
from volcenginesdkvmp.models.get_contact_request import GetContactRequest
from volcenginesdkvmp.models.get_contact_response import GetContactResponse
from volcenginesdkvmp.models.get_notify_group_policy_request import GetNotifyGroupPolicyRequest
from volcenginesdkvmp.models.get_notify_group_policy_response import GetNotifyGroupPolicyResponse
from volcenginesdkvmp.models.get_notify_policy_request import GetNotifyPolicyRequest
from volcenginesdkvmp.models.get_notify_policy_response import GetNotifyPolicyResponse
from volcenginesdkvmp.models.get_rule_file_request import GetRuleFileRequest
from volcenginesdkvmp.models.get_rule_file_response import GetRuleFileResponse
from volcenginesdkvmp.models.get_workspace_request import GetWorkspaceRequest
from volcenginesdkvmp.models.get_workspace_response import GetWorkspaceResponse
from volcenginesdkvmp.models.instance_type_for_get_workspace_output import InstanceTypeForGetWorkspaceOutput
from volcenginesdkvmp.models.item_for_list_alert_samples_output import ItemForListAlertSamplesOutput
from volcenginesdkvmp.models.item_for_list_alerting_rules_output import ItemForListAlertingRulesOutput
from volcenginesdkvmp.models.item_for_list_alerts_output import ItemForListAlertsOutput
from volcenginesdkvmp.models.item_for_list_contact_groups_output import ItemForListContactGroupsOutput
from volcenginesdkvmp.models.item_for_list_contacts_output import ItemForListContactsOutput
from volcenginesdkvmp.models.item_for_list_notify_group_policies_output import ItemForListNotifyGroupPoliciesOutput
from volcenginesdkvmp.models.item_for_list_notify_policies_output import ItemForListNotifyPoliciesOutput
from volcenginesdkvmp.models.item_for_list_notify_templates_output import ItemForListNotifyTemplatesOutput
from volcenginesdkvmp.models.item_for_list_rule_files_output import ItemForListRuleFilesOutput
from volcenginesdkvmp.models.item_for_list_rules_output import ItemForListRulesOutput
from volcenginesdkvmp.models.item_for_list_workspace_instance_types_output import ItemForListWorkspaceInstanceTypesOutput
from volcenginesdkvmp.models.item_for_list_workspace_status_output import ItemForListWorkspaceStatusOutput
from volcenginesdkvmp.models.item_for_list_workspaces_output import ItemForListWorkspacesOutput
from volcenginesdkvmp.models.label_for_create_alerting_rule_input import LabelForCreateAlertingRuleInput
from volcenginesdkvmp.models.label_for_get_alert_output import LabelForGetAlertOutput
from volcenginesdkvmp.models.label_for_get_alerting_rule_output import LabelForGetAlertingRuleOutput
from volcenginesdkvmp.models.label_for_list_alerting_rules_output import LabelForListAlertingRulesOutput
from volcenginesdkvmp.models.label_for_list_alerts_output import LabelForListAlertsOutput
from volcenginesdkvmp.models.label_for_list_rules_output import LabelForListRulesOutput
from volcenginesdkvmp.models.label_for_update_alerting_rule_input import LabelForUpdateAlertingRuleInput
from volcenginesdkvmp.models.lark_bot_webhook_for_create_contact_input import LarkBotWebhookForCreateContactInput
from volcenginesdkvmp.models.lark_bot_webhook_for_get_contact_output import LarkBotWebhookForGetContactOutput
from volcenginesdkvmp.models.lark_bot_webhook_for_list_contacts_output import LarkBotWebhookForListContactsOutput
from volcenginesdkvmp.models.lark_bot_webhook_for_update_contact_input import LarkBotWebhookForUpdateContactInput
from volcenginesdkvmp.models.level_for_create_alerting_rule_input import LevelForCreateAlertingRuleInput
from volcenginesdkvmp.models.level_for_create_notify_group_policy_input import LevelForCreateNotifyGroupPolicyInput
from volcenginesdkvmp.models.level_for_create_notify_policy_input import LevelForCreateNotifyPolicyInput
from volcenginesdkvmp.models.level_for_get_alert_output import LevelForGetAlertOutput
from volcenginesdkvmp.models.level_for_get_alerting_rule_output import LevelForGetAlertingRuleOutput
from volcenginesdkvmp.models.level_for_get_notify_group_policy_output import LevelForGetNotifyGroupPolicyOutput
from volcenginesdkvmp.models.level_for_get_notify_policy_output import LevelForGetNotifyPolicyOutput
from volcenginesdkvmp.models.level_for_list_alerting_rules_output import LevelForListAlertingRulesOutput
from volcenginesdkvmp.models.level_for_list_alerts_output import LevelForListAlertsOutput
from volcenginesdkvmp.models.level_for_list_notify_group_policies_output import LevelForListNotifyGroupPoliciesOutput
from volcenginesdkvmp.models.level_for_list_notify_policies_output import LevelForListNotifyPoliciesOutput
from volcenginesdkvmp.models.level_for_update_alerting_rule_input import LevelForUpdateAlertingRuleInput
from volcenginesdkvmp.models.level_for_update_notify_group_policy_input import LevelForUpdateNotifyGroupPolicyInput
from volcenginesdkvmp.models.level_for_update_notify_policy_input import LevelForUpdateNotifyPolicyInput
from volcenginesdkvmp.models.list_alert_samples_request import ListAlertSamplesRequest
from volcenginesdkvmp.models.list_alert_samples_response import ListAlertSamplesResponse
from volcenginesdkvmp.models.list_alerting_rules_request import ListAlertingRulesRequest
from volcenginesdkvmp.models.list_alerting_rules_response import ListAlertingRulesResponse
from volcenginesdkvmp.models.list_alerts_request import ListAlertsRequest
from volcenginesdkvmp.models.list_alerts_response import ListAlertsResponse
from volcenginesdkvmp.models.list_contact_groups_request import ListContactGroupsRequest
from volcenginesdkvmp.models.list_contact_groups_response import ListContactGroupsResponse
from volcenginesdkvmp.models.list_contacts_request import ListContactsRequest
from volcenginesdkvmp.models.list_contacts_response import ListContactsResponse
from volcenginesdkvmp.models.list_notify_group_policies_request import ListNotifyGroupPoliciesRequest
from volcenginesdkvmp.models.list_notify_group_policies_response import ListNotifyGroupPoliciesResponse
from volcenginesdkvmp.models.list_notify_policies_request import ListNotifyPoliciesRequest
from volcenginesdkvmp.models.list_notify_policies_response import ListNotifyPoliciesResponse
from volcenginesdkvmp.models.list_notify_templates_request import ListNotifyTemplatesRequest
from volcenginesdkvmp.models.list_notify_templates_response import ListNotifyTemplatesResponse
from volcenginesdkvmp.models.list_rule_files_request import ListRuleFilesRequest
from volcenginesdkvmp.models.list_rule_files_response import ListRuleFilesResponse
from volcenginesdkvmp.models.list_rules_request import ListRulesRequest
from volcenginesdkvmp.models.list_rules_response import ListRulesResponse
from volcenginesdkvmp.models.list_tags_for_resources_request import ListTagsForResourcesRequest
from volcenginesdkvmp.models.list_tags_for_resources_response import ListTagsForResourcesResponse
from volcenginesdkvmp.models.list_workspace_instance_types_request import ListWorkspaceInstanceTypesRequest
from volcenginesdkvmp.models.list_workspace_instance_types_response import ListWorkspaceInstanceTypesResponse
from volcenginesdkvmp.models.list_workspace_status_request import ListWorkspaceStatusRequest
from volcenginesdkvmp.models.list_workspace_status_response import ListWorkspaceStatusResponse
from volcenginesdkvmp.models.list_workspaces_request import ListWorkspacesRequest
from volcenginesdkvmp.models.list_workspaces_response import ListWorkspacesResponse
from volcenginesdkvmp.models.query_for_create_alerting_rule_input import QueryForCreateAlertingRuleInput
from volcenginesdkvmp.models.query_for_get_alerting_rule_output import QueryForGetAlertingRuleOutput
from volcenginesdkvmp.models.query_for_list_alerting_rules_output import QueryForListAlertingRulesOutput
from volcenginesdkvmp.models.query_for_update_alerting_rule_input import QueryForUpdateAlertingRuleInput
from volcenginesdkvmp.models.quota_for_get_workspace_output import QuotaForGetWorkspaceOutput
from volcenginesdkvmp.models.quota_for_update_workspace_input import QuotaForUpdateWorkspaceInput
from volcenginesdkvmp.models.resolved_for_create_notify_template_input import ResolvedForCreateNotifyTemplateInput
from volcenginesdkvmp.models.resolved_for_list_notify_templates_output import ResolvedForListNotifyTemplatesOutput
from volcenginesdkvmp.models.resolved_for_update_notify_template_input import ResolvedForUpdateNotifyTemplateInput
from volcenginesdkvmp.models.resource_for_get_alert_output import ResourceForGetAlertOutput
from volcenginesdkvmp.models.resource_for_list_alerts_output import ResourceForListAlertsOutput
from volcenginesdkvmp.models.resource_tag_for_list_tags_for_resources_output import ResourceTagForListTagsForResourcesOutput
from volcenginesdkvmp.models.send_validation_message_request import SendValidationMessageRequest
from volcenginesdkvmp.models.send_validation_message_response import SendValidationMessageResponse
from volcenginesdkvmp.models.tag_filter_for_list_tags_for_resources_input import TagFilterForListTagsForResourcesInput
from volcenginesdkvmp.models.tag_filter_for_list_workspaces_input import TagFilterForListWorkspacesInput
from volcenginesdkvmp.models.tag_for_create_workspace_input import TagForCreateWorkspaceInput
from volcenginesdkvmp.models.tag_for_get_workspace_output import TagForGetWorkspaceOutput
from volcenginesdkvmp.models.tag_for_list_workspaces_output import TagForListWorkspacesOutput
from volcenginesdkvmp.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkvmp.models.tag_resources_request import TagResourcesRequest
from volcenginesdkvmp.models.tag_resources_response import TagResourcesResponse
from volcenginesdkvmp.models.test_ding_talk_bot_webhook_request import TestDingTalkBotWebhookRequest
from volcenginesdkvmp.models.test_ding_talk_bot_webhook_response import TestDingTalkBotWebhookResponse
from volcenginesdkvmp.models.test_lark_bot_webhook_request import TestLarkBotWebhookRequest
from volcenginesdkvmp.models.test_lark_bot_webhook_response import TestLarkBotWebhookResponse
from volcenginesdkvmp.models.test_we_com_bot_webhook_request import TestWeComBotWebhookRequest
from volcenginesdkvmp.models.test_we_com_bot_webhook_response import TestWeComBotWebhookResponse
from volcenginesdkvmp.models.test_webhook_request import TestWebhookRequest
from volcenginesdkvmp.models.test_webhook_response import TestWebhookResponse
from volcenginesdkvmp.models.unsuccessful_item_for_delete_alerting_rules_output import UnsuccessfulItemForDeleteAlertingRulesOutput
from volcenginesdkvmp.models.unsuccessful_item_for_delete_contact_groups_output import UnsuccessfulItemForDeleteContactGroupsOutput
from volcenginesdkvmp.models.unsuccessful_item_for_delete_contacts_output import UnsuccessfulItemForDeleteContactsOutput
from volcenginesdkvmp.models.unsuccessful_item_for_disable_alerting_rules_output import UnsuccessfulItemForDisableAlertingRulesOutput
from volcenginesdkvmp.models.unsuccessful_item_for_enable_alerting_rules_output import UnsuccessfulItemForEnableAlertingRulesOutput
from volcenginesdkvmp.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkvmp.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkvmp.models.update_alerting_rule_request import UpdateAlertingRuleRequest
from volcenginesdkvmp.models.update_alerting_rule_response import UpdateAlertingRuleResponse
from volcenginesdkvmp.models.update_contact_group_request import UpdateContactGroupRequest
from volcenginesdkvmp.models.update_contact_group_response import UpdateContactGroupResponse
from volcenginesdkvmp.models.update_contact_request import UpdateContactRequest
from volcenginesdkvmp.models.update_contact_response import UpdateContactResponse
from volcenginesdkvmp.models.update_notify_group_policy_request import UpdateNotifyGroupPolicyRequest
from volcenginesdkvmp.models.update_notify_group_policy_response import UpdateNotifyGroupPolicyResponse
from volcenginesdkvmp.models.update_notify_policy_request import UpdateNotifyPolicyRequest
from volcenginesdkvmp.models.update_notify_policy_response import UpdateNotifyPolicyResponse
from volcenginesdkvmp.models.update_notify_template_request import UpdateNotifyTemplateRequest
from volcenginesdkvmp.models.update_notify_template_response import UpdateNotifyTemplateResponse
from volcenginesdkvmp.models.update_rule_file_request import UpdateRuleFileRequest
from volcenginesdkvmp.models.update_rule_file_response import UpdateRuleFileResponse
from volcenginesdkvmp.models.update_workspace_request import UpdateWorkspaceRequest
from volcenginesdkvmp.models.update_workspace_response import UpdateWorkspaceResponse
from volcenginesdkvmp.models.usage_for_list_workspace_status_output import UsageForListWorkspaceStatusOutput
from volcenginesdkvmp.models.we_com_bot_webhook_for_create_contact_input import WeComBotWebhookForCreateContactInput
from volcenginesdkvmp.models.we_com_bot_webhook_for_get_contact_output import WeComBotWebhookForGetContactOutput
from volcenginesdkvmp.models.we_com_bot_webhook_for_list_contacts_output import WeComBotWebhookForListContactsOutput
from volcenginesdkvmp.models.we_com_bot_webhook_for_update_contact_input import WeComBotWebhookForUpdateContactInput
from volcenginesdkvmp.models.webhook_for_create_contact_input import WebhookForCreateContactInput
from volcenginesdkvmp.models.webhook_for_get_contact_output import WebhookForGetContactOutput
from volcenginesdkvmp.models.webhook_for_list_contacts_output import WebhookForListContactsOutput
from volcenginesdkvmp.models.webhook_for_update_contact_input import WebhookForUpdateContactInput
