# coding: utf-8

"""
    coze20250601

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCozeUserRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'query_string': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'query_string': 'QueryString',
        'user_name': 'UserName'
    }

    def __init__(self, page_number=None, page_size=None, query_string=None, user_name=None, _configuration=None):  # noqa: E501
        """ListCozeUserRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._query_string = None
        self._user_name = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if query_string is not None:
            self.query_string = query_string
        if user_name is not None:
            self.user_name = user_name

    @property
    def page_number(self):
        """Gets the page_number of this ListCozeUserRequest.  # noqa: E501


        :return: The page_number of this ListCozeUserRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListCozeUserRequest.


        :param page_number: The page_number of this ListCozeUserRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListCozeUserRequest.  # noqa: E501


        :return: The page_size of this ListCozeUserRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCozeUserRequest.


        :param page_size: The page_size of this ListCozeUserRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def query_string(self):
        """Gets the query_string of this ListCozeUserRequest.  # noqa: E501


        :return: The query_string of this ListCozeUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._query_string

    @query_string.setter
    def query_string(self, query_string):
        """Sets the query_string of this ListCozeUserRequest.


        :param query_string: The query_string of this ListCozeUserRequest.  # noqa: E501
        :type: str
        """

        self._query_string = query_string

    @property
    def user_name(self):
        """Gets the user_name of this ListCozeUserRequest.  # noqa: E501


        :return: The user_name of this ListCozeUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ListCozeUserRequest.


        :param user_name: The user_name of this ListCozeUserRequest.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCozeUserRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCozeUserRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCozeUserRequest):
            return True

        return self.to_dict() != other.to_dict()
