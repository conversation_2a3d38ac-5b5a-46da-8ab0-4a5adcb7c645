# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeEnterpriseDBInstanceParamsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'params': 'list[ParamForDescribeEnterpriseDBInstanceParamsOutput]',
        'total_params_num': 'int'
    }

    attribute_map = {
        'params': 'Params',
        'total_params_num': 'TotalParamsNum'
    }

    def __init__(self, params=None, total_params_num=None, _configuration=None):  # noqa: E501
        """DescribeEnterpriseDBInstanceParamsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._params = None
        self._total_params_num = None
        self.discriminator = None

        if params is not None:
            self.params = params
        if total_params_num is not None:
            self.total_params_num = total_params_num

    @property
    def params(self):
        """Gets the params of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501


        :return: The params of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501
        :rtype: list[ParamForDescribeEnterpriseDBInstanceParamsOutput]
        """
        return self._params

    @params.setter
    def params(self, params):
        """Sets the params of this DescribeEnterpriseDBInstanceParamsResponse.


        :param params: The params of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501
        :type: list[ParamForDescribeEnterpriseDBInstanceParamsOutput]
        """

        self._params = params

    @property
    def total_params_num(self):
        """Gets the total_params_num of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501


        :return: The total_params_num of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_params_num

    @total_params_num.setter
    def total_params_num(self, total_params_num):
        """Sets the total_params_num of this DescribeEnterpriseDBInstanceParamsResponse.


        :param total_params_num: The total_params_num of this DescribeEnterpriseDBInstanceParamsResponse.  # noqa: E501
        :type: int
        """

        self._total_params_num = total_params_num

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeEnterpriseDBInstanceParamsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeEnterpriseDBInstanceParamsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeEnterpriseDBInstanceParamsResponse):
            return True

        return self.to_dict() != other.to_dict()
