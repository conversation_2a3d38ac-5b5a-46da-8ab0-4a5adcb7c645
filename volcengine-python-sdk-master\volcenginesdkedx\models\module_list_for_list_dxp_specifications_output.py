# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModuleListForListDXPSpecificationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available': 'bool',
        'module_type': 'str'
    }

    attribute_map = {
        'available': 'Available',
        'module_type': 'ModuleType'
    }

    def __init__(self, available=None, module_type=None, _configuration=None):  # noqa: E501
        """ModuleListForListDXPSpecificationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available = None
        self._module_type = None
        self.discriminator = None

        if available is not None:
            self.available = available
        if module_type is not None:
            self.module_type = module_type

    @property
    def available(self):
        """Gets the available of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501


        :return: The available of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._available

    @available.setter
    def available(self, available):
        """Sets the available of this ModuleListForListDXPSpecificationsOutput.


        :param available: The available of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501
        :type: bool
        """

        self._available = available

    @property
    def module_type(self):
        """Gets the module_type of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501


        :return: The module_type of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._module_type

    @module_type.setter
    def module_type(self, module_type):
        """Sets the module_type of this ModuleListForListDXPSpecificationsOutput.


        :param module_type: The module_type of this ModuleListForListDXPSpecificationsOutput.  # noqa: E501
        :type: str
        """

        self._module_type = module_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModuleListForListDXPSpecificationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModuleListForListDXPSpecificationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModuleListForListDXPSpecificationsOutput):
            return True

        return self.to_dict() != other.to_dict()
