# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeStatisticalRankingRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'domain': 'str',
        'end_time': 'int',
        'item': 'str',
        'metric': 'str',
        'start_time': 'int',
        'ua_type': 'str'
    }

    attribute_map = {
        'area': 'Area',
        'domain': 'Domain',
        'end_time': 'EndTime',
        'item': 'Item',
        'metric': 'Metric',
        'start_time': 'StartTime',
        'ua_type': 'UaType'
    }

    def __init__(self, area=None, domain=None, end_time=None, item=None, metric=None, start_time=None, ua_type=None, _configuration=None):  # noqa: E501
        """DescribeStatisticalRankingRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._domain = None
        self._end_time = None
        self._item = None
        self._metric = None
        self._start_time = None
        self._ua_type = None
        self.discriminator = None

        if area is not None:
            self.area = area
        self.domain = domain
        self.end_time = end_time
        self.item = item
        self.metric = metric
        self.start_time = start_time
        if ua_type is not None:
            self.ua_type = ua_type

    @property
    def area(self):
        """Gets the area of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The area of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this DescribeStatisticalRankingRequest.


        :param area: The area of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def domain(self):
        """Gets the domain of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The domain of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeStatisticalRankingRequest.


        :param domain: The domain of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def end_time(self):
        """Gets the end_time of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The end_time of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeStatisticalRankingRequest.


        :param end_time: The end_time of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def item(self):
        """Gets the item of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The item of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: str
        """
        return self._item

    @item.setter
    def item(self, item):
        """Sets the item of this DescribeStatisticalRankingRequest.


        :param item: The item of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and item is None:
            raise ValueError("Invalid value for `item`, must not be `None`")  # noqa: E501

        self._item = item

    @property
    def metric(self):
        """Gets the metric of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The metric of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric

    @metric.setter
    def metric(self, metric):
        """Sets the metric of this DescribeStatisticalRankingRequest.


        :param metric: The metric of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric is None:
            raise ValueError("Invalid value for `metric`, must not be `None`")  # noqa: E501

        self._metric = metric

    @property
    def start_time(self):
        """Gets the start_time of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The start_time of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeStatisticalRankingRequest.


        :param start_time: The start_time of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def ua_type(self):
        """Gets the ua_type of this DescribeStatisticalRankingRequest.  # noqa: E501


        :return: The ua_type of this DescribeStatisticalRankingRequest.  # noqa: E501
        :rtype: str
        """
        return self._ua_type

    @ua_type.setter
    def ua_type(self, ua_type):
        """Sets the ua_type of this DescribeStatisticalRankingRequest.


        :param ua_type: The ua_type of this DescribeStatisticalRankingRequest.  # noqa: E501
        :type: str
        """

        self._ua_type = ua_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeStatisticalRankingRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeStatisticalRankingRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeStatisticalRankingRequest):
            return True

        return self.to_dict() != other.to_dict()
