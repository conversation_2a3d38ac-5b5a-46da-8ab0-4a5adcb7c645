# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListEndpointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'description': 'str',
        'endpoint_model_type': 'str',
        'id': 'str',
        'model_reference': 'ModelReferenceForListEndpointsOutput',
        'model_unit_id': 'str',
        'name': 'str',
        'project_name': 'str',
        'rate_limit': 'RateLimitForListEndpointsOutput',
        'rolling_id': 'str',
        'status': 'str',
        'status_reason': 'str',
        'support_rolling': 'bool',
        'tags': 'list[TagForListEndpointsOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'endpoint_model_type': 'EndpointModelType',
        'id': 'Id',
        'model_reference': 'ModelReference',
        'model_unit_id': 'ModelUnitId',
        'name': 'Name',
        'project_name': 'ProjectName',
        'rate_limit': 'RateLimit',
        'rolling_id': 'RollingId',
        'status': 'Status',
        'status_reason': 'StatusReason',
        'support_rolling': 'SupportRolling',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, description=None, endpoint_model_type=None, id=None, model_reference=None, model_unit_id=None, name=None, project_name=None, rate_limit=None, rolling_id=None, status=None, status_reason=None, support_rolling=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListEndpointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._endpoint_model_type = None
        self._id = None
        self._model_reference = None
        self._model_unit_id = None
        self._name = None
        self._project_name = None
        self._rate_limit = None
        self._rolling_id = None
        self._status = None
        self._status_reason = None
        self._support_rolling = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if endpoint_model_type is not None:
            self.endpoint_model_type = endpoint_model_type
        if id is not None:
            self.id = id
        if model_reference is not None:
            self.model_reference = model_reference
        if model_unit_id is not None:
            self.model_unit_id = model_unit_id
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if rate_limit is not None:
            self.rate_limit = rate_limit
        if rolling_id is not None:
            self.rolling_id = rolling_id
        if status is not None:
            self.status = status
        if status_reason is not None:
            self.status_reason = status_reason
        if support_rolling is not None:
            self.support_rolling = support_rolling
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The create_time of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListEndpointsOutput.


        :param create_time: The create_time of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The description of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListEndpointsOutput.


        :param description: The description of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def endpoint_model_type(self):
        """Gets the endpoint_model_type of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The endpoint_model_type of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_model_type

    @endpoint_model_type.setter
    def endpoint_model_type(self, endpoint_model_type):
        """Sets the endpoint_model_type of this ItemForListEndpointsOutput.


        :param endpoint_model_type: The endpoint_model_type of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_model_type = endpoint_model_type

    @property
    def id(self):
        """Gets the id of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The id of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListEndpointsOutput.


        :param id: The id of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def model_reference(self):
        """Gets the model_reference of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The model_reference of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: ModelReferenceForListEndpointsOutput
        """
        return self._model_reference

    @model_reference.setter
    def model_reference(self, model_reference):
        """Sets the model_reference of this ItemForListEndpointsOutput.


        :param model_reference: The model_reference of this ItemForListEndpointsOutput.  # noqa: E501
        :type: ModelReferenceForListEndpointsOutput
        """

        self._model_reference = model_reference

    @property
    def model_unit_id(self):
        """Gets the model_unit_id of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The model_unit_id of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._model_unit_id

    @model_unit_id.setter
    def model_unit_id(self, model_unit_id):
        """Sets the model_unit_id of this ItemForListEndpointsOutput.


        :param model_unit_id: The model_unit_id of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._model_unit_id = model_unit_id

    @property
    def name(self):
        """Gets the name of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The name of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListEndpointsOutput.


        :param name: The name of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The project_name of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ItemForListEndpointsOutput.


        :param project_name: The project_name of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rate_limit(self):
        """Gets the rate_limit of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The rate_limit of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: RateLimitForListEndpointsOutput
        """
        return self._rate_limit

    @rate_limit.setter
    def rate_limit(self, rate_limit):
        """Sets the rate_limit of this ItemForListEndpointsOutput.


        :param rate_limit: The rate_limit of this ItemForListEndpointsOutput.  # noqa: E501
        :type: RateLimitForListEndpointsOutput
        """

        self._rate_limit = rate_limit

    @property
    def rolling_id(self):
        """Gets the rolling_id of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The rolling_id of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._rolling_id

    @rolling_id.setter
    def rolling_id(self, rolling_id):
        """Sets the rolling_id of this ItemForListEndpointsOutput.


        :param rolling_id: The rolling_id of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._rolling_id = rolling_id

    @property
    def status(self):
        """Gets the status of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The status of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListEndpointsOutput.


        :param status: The status of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def status_reason(self):
        """Gets the status_reason of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The status_reason of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status_reason

    @status_reason.setter
    def status_reason(self, status_reason):
        """Sets the status_reason of this ItemForListEndpointsOutput.


        :param status_reason: The status_reason of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._status_reason = status_reason

    @property
    def support_rolling(self):
        """Gets the support_rolling of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The support_rolling of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._support_rolling

    @support_rolling.setter
    def support_rolling(self, support_rolling):
        """Sets the support_rolling of this ItemForListEndpointsOutput.


        :param support_rolling: The support_rolling of this ItemForListEndpointsOutput.  # noqa: E501
        :type: bool
        """

        self._support_rolling = support_rolling

    @property
    def tags(self):
        """Gets the tags of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The tags of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: list[TagForListEndpointsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListEndpointsOutput.


        :param tags: The tags of this ItemForListEndpointsOutput.  # noqa: E501
        :type: list[TagForListEndpointsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListEndpointsOutput.  # noqa: E501


        :return: The update_time of this ItemForListEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListEndpointsOutput.


        :param update_time: The update_time of this ItemForListEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListEndpointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListEndpointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListEndpointsOutput):
            return True

        return self.to_dict() != other.to_dict()
