# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserEnterFormForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enter_review_age': 'str',
        'enter_review_birthday': 'str',
        'enter_review_company': 'str',
        'enter_review_education': 'str',
        'enter_review_email': 'str',
        'enter_review_industry': 'str',
        'enter_review_name': 'str',
        'enter_review_position': 'str',
        'enter_review_sex': 'str',
        'enter_review_tel': 'str'
    }

    attribute_map = {
        'enter_review_age': 'EnterReviewAge',
        'enter_review_birthday': 'EnterReviewBirthday',
        'enter_review_company': 'EnterReviewCompany',
        'enter_review_education': 'EnterReviewEducation',
        'enter_review_email': 'EnterReviewEmail',
        'enter_review_industry': 'EnterReviewIndustry',
        'enter_review_name': 'EnterReviewName',
        'enter_review_position': 'EnterReviewPosition',
        'enter_review_sex': 'EnterReviewSex',
        'enter_review_tel': 'EnterReviewTel'
    }

    def __init__(self, enter_review_age=None, enter_review_birthday=None, enter_review_company=None, enter_review_education=None, enter_review_email=None, enter_review_industry=None, enter_review_name=None, enter_review_position=None, enter_review_sex=None, enter_review_tel=None, _configuration=None):  # noqa: E501
        """UserEnterFormForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enter_review_age = None
        self._enter_review_birthday = None
        self._enter_review_company = None
        self._enter_review_education = None
        self._enter_review_email = None
        self._enter_review_industry = None
        self._enter_review_name = None
        self._enter_review_position = None
        self._enter_review_sex = None
        self._enter_review_tel = None
        self.discriminator = None

        if enter_review_age is not None:
            self.enter_review_age = enter_review_age
        if enter_review_birthday is not None:
            self.enter_review_birthday = enter_review_birthday
        if enter_review_company is not None:
            self.enter_review_company = enter_review_company
        if enter_review_education is not None:
            self.enter_review_education = enter_review_education
        if enter_review_email is not None:
            self.enter_review_email = enter_review_email
        if enter_review_industry is not None:
            self.enter_review_industry = enter_review_industry
        if enter_review_name is not None:
            self.enter_review_name = enter_review_name
        if enter_review_position is not None:
            self.enter_review_position = enter_review_position
        if enter_review_sex is not None:
            self.enter_review_sex = enter_review_sex
        if enter_review_tel is not None:
            self.enter_review_tel = enter_review_tel

    @property
    def enter_review_age(self):
        """Gets the enter_review_age of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_age of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_age

    @enter_review_age.setter
    def enter_review_age(self, enter_review_age):
        """Sets the enter_review_age of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_age: The enter_review_age of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_age = enter_review_age

    @property
    def enter_review_birthday(self):
        """Gets the enter_review_birthday of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_birthday of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_birthday

    @enter_review_birthday.setter
    def enter_review_birthday(self, enter_review_birthday):
        """Sets the enter_review_birthday of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_birthday: The enter_review_birthday of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_birthday = enter_review_birthday

    @property
    def enter_review_company(self):
        """Gets the enter_review_company of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_company of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_company

    @enter_review_company.setter
    def enter_review_company(self, enter_review_company):
        """Sets the enter_review_company of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_company: The enter_review_company of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_company = enter_review_company

    @property
    def enter_review_education(self):
        """Gets the enter_review_education of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_education of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_education

    @enter_review_education.setter
    def enter_review_education(self, enter_review_education):
        """Sets the enter_review_education of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_education: The enter_review_education of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_education = enter_review_education

    @property
    def enter_review_email(self):
        """Gets the enter_review_email of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_email of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_email

    @enter_review_email.setter
    def enter_review_email(self, enter_review_email):
        """Sets the enter_review_email of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_email: The enter_review_email of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_email = enter_review_email

    @property
    def enter_review_industry(self):
        """Gets the enter_review_industry of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_industry of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_industry

    @enter_review_industry.setter
    def enter_review_industry(self, enter_review_industry):
        """Sets the enter_review_industry of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_industry: The enter_review_industry of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_industry = enter_review_industry

    @property
    def enter_review_name(self):
        """Gets the enter_review_name of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_name of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_name

    @enter_review_name.setter
    def enter_review_name(self, enter_review_name):
        """Sets the enter_review_name of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_name: The enter_review_name of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_name = enter_review_name

    @property
    def enter_review_position(self):
        """Gets the enter_review_position of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_position of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_position

    @enter_review_position.setter
    def enter_review_position(self, enter_review_position):
        """Sets the enter_review_position of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_position: The enter_review_position of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_position = enter_review_position

    @property
    def enter_review_sex(self):
        """Gets the enter_review_sex of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_sex of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_sex

    @enter_review_sex.setter
    def enter_review_sex(self, enter_review_sex):
        """Sets the enter_review_sex of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_sex: The enter_review_sex of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_sex = enter_review_sex

    @property
    def enter_review_tel(self):
        """Gets the enter_review_tel of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501


        :return: The enter_review_tel of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_review_tel

    @enter_review_tel.setter
    def enter_review_tel(self, enter_review_tel):
        """Sets the enter_review_tel of this UserEnterFormForListAccountUserDataOutput.


        :param enter_review_tel: The enter_review_tel of this UserEnterFormForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._enter_review_tel = enter_review_tel

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserEnterFormForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserEnterFormForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserEnterFormForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
