# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskProgressForDescribeTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'step_extra_info': 'list[StepExtraInfoForDescribeTasksOutput]',
        'step_status': 'str'
    }

    attribute_map = {
        'name': 'Name',
        'step_extra_info': 'StepExtraInfo',
        'step_status': 'StepStatus'
    }

    def __init__(self, name=None, step_extra_info=None, step_status=None, _configuration=None):  # noqa: E501
        """TaskProgressForDescribeTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._step_extra_info = None
        self._step_status = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if step_extra_info is not None:
            self.step_extra_info = step_extra_info
        if step_status is not None:
            self.step_status = step_status

    @property
    def name(self):
        """Gets the name of this TaskProgressForDescribeTasksOutput.  # noqa: E501


        :return: The name of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this TaskProgressForDescribeTasksOutput.


        :param name: The name of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def step_extra_info(self):
        """Gets the step_extra_info of this TaskProgressForDescribeTasksOutput.  # noqa: E501


        :return: The step_extra_info of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :rtype: list[StepExtraInfoForDescribeTasksOutput]
        """
        return self._step_extra_info

    @step_extra_info.setter
    def step_extra_info(self, step_extra_info):
        """Sets the step_extra_info of this TaskProgressForDescribeTasksOutput.


        :param step_extra_info: The step_extra_info of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :type: list[StepExtraInfoForDescribeTasksOutput]
        """

        self._step_extra_info = step_extra_info

    @property
    def step_status(self):
        """Gets the step_status of this TaskProgressForDescribeTasksOutput.  # noqa: E501


        :return: The step_status of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._step_status

    @step_status.setter
    def step_status(self, step_status):
        """Sets the step_status of this TaskProgressForDescribeTasksOutput.


        :param step_status: The step_status of this TaskProgressForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._step_status = step_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskProgressForDescribeTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskProgressForDescribeTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskProgressForDescribeTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
