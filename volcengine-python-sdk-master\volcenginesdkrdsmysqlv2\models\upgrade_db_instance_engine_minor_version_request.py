# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpgradeDBInstanceEngineMinorVersionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'estimate_only': 'bool',
        'instance_id': 'str',
        'specified_switch_end_time': 'str',
        'specified_switch_start_time': 'str',
        'switch_type': 'str',
        'target_minor_version': 'str'
    }

    attribute_map = {
        'estimate_only': 'EstimateOnly',
        'instance_id': 'InstanceId',
        'specified_switch_end_time': 'SpecifiedSwitchEndTime',
        'specified_switch_start_time': 'SpecifiedSwitchStartTime',
        'switch_type': 'SwitchType',
        'target_minor_version': 'TargetMinorVersion'
    }

    def __init__(self, estimate_only=None, instance_id=None, specified_switch_end_time=None, specified_switch_start_time=None, switch_type=None, target_minor_version=None, _configuration=None):  # noqa: E501
        """UpgradeDBInstanceEngineMinorVersionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._estimate_only = None
        self._instance_id = None
        self._specified_switch_end_time = None
        self._specified_switch_start_time = None
        self._switch_type = None
        self._target_minor_version = None
        self.discriminator = None

        if estimate_only is not None:
            self.estimate_only = estimate_only
        self.instance_id = instance_id
        if specified_switch_end_time is not None:
            self.specified_switch_end_time = specified_switch_end_time
        if specified_switch_start_time is not None:
            self.specified_switch_start_time = specified_switch_start_time
        if switch_type is not None:
            self.switch_type = switch_type
        self.target_minor_version = target_minor_version

    @property
    def estimate_only(self):
        """Gets the estimate_only of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The estimate_only of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: bool
        """
        return self._estimate_only

    @estimate_only.setter
    def estimate_only(self, estimate_only):
        """Sets the estimate_only of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param estimate_only: The estimate_only of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: bool
        """

        self._estimate_only = estimate_only

    @property
    def instance_id(self):
        """Gets the instance_id of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The instance_id of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param instance_id: The instance_id of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def specified_switch_end_time(self):
        """Gets the specified_switch_end_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The specified_switch_end_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_end_time

    @specified_switch_end_time.setter
    def specified_switch_end_time(self, specified_switch_end_time):
        """Sets the specified_switch_end_time of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param specified_switch_end_time: The specified_switch_end_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_end_time = specified_switch_end_time

    @property
    def specified_switch_start_time(self):
        """Gets the specified_switch_start_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The specified_switch_start_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_start_time

    @specified_switch_start_time.setter
    def specified_switch_start_time(self, specified_switch_start_time):
        """Sets the specified_switch_start_time of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param specified_switch_start_time: The specified_switch_start_time of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_start_time = specified_switch_start_time

    @property
    def switch_type(self):
        """Gets the switch_type of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The switch_type of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_type

    @switch_type.setter
    def switch_type(self, switch_type):
        """Sets the switch_type of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param switch_type: The switch_type of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: str
        """

        self._switch_type = switch_type

    @property
    def target_minor_version(self):
        """Gets the target_minor_version of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501


        :return: The target_minor_version of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :rtype: str
        """
        return self._target_minor_version

    @target_minor_version.setter
    def target_minor_version(self, target_minor_version):
        """Sets the target_minor_version of this UpgradeDBInstanceEngineMinorVersionRequest.


        :param target_minor_version: The target_minor_version of this UpgradeDBInstanceEngineMinorVersionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and target_minor_version is None:
            raise ValueError("Invalid value for `target_minor_version`, must not be `None`")  # noqa: E501

        self._target_minor_version = target_minor_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpgradeDBInstanceEngineMinorVersionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpgradeDBInstanceEngineMinorVersionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpgradeDBInstanceEngineMinorVersionRequest):
            return True

        return self.to_dict() != other.to_dict()
