# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListApplicationHistoryRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deployment_id': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'start_time_left': 'str',
        'start_time_right': 'str'
    }

    attribute_map = {
        'deployment_id': 'DeploymentId',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'start_time_left': 'StartTimeLeft',
        'start_time_right': 'StartTimeRight'
    }

    def __init__(self, deployment_id=None, max_results=None, next_token=None, start_time_left=None, start_time_right=None, _configuration=None):  # noqa: E501
        """ListApplicationHistoryRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deployment_id = None
        self._max_results = None
        self._next_token = None
        self._start_time_left = None
        self._start_time_right = None
        self.discriminator = None

        if deployment_id is not None:
            self.deployment_id = deployment_id
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if start_time_left is not None:
            self.start_time_left = start_time_left
        if start_time_right is not None:
            self.start_time_right = start_time_right

    @property
    def deployment_id(self):
        """Gets the deployment_id of this ListApplicationHistoryRequest.  # noqa: E501


        :return: The deployment_id of this ListApplicationHistoryRequest.  # noqa: E501
        :rtype: str
        """
        return self._deployment_id

    @deployment_id.setter
    def deployment_id(self, deployment_id):
        """Sets the deployment_id of this ListApplicationHistoryRequest.


        :param deployment_id: The deployment_id of this ListApplicationHistoryRequest.  # noqa: E501
        :type: str
        """

        self._deployment_id = deployment_id

    @property
    def max_results(self):
        """Gets the max_results of this ListApplicationHistoryRequest.  # noqa: E501


        :return: The max_results of this ListApplicationHistoryRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListApplicationHistoryRequest.


        :param max_results: The max_results of this ListApplicationHistoryRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListApplicationHistoryRequest.  # noqa: E501


        :return: The next_token of this ListApplicationHistoryRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListApplicationHistoryRequest.


        :param next_token: The next_token of this ListApplicationHistoryRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def start_time_left(self):
        """Gets the start_time_left of this ListApplicationHistoryRequest.  # noqa: E501


        :return: The start_time_left of this ListApplicationHistoryRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time_left

    @start_time_left.setter
    def start_time_left(self, start_time_left):
        """Sets the start_time_left of this ListApplicationHistoryRequest.


        :param start_time_left: The start_time_left of this ListApplicationHistoryRequest.  # noqa: E501
        :type: str
        """

        self._start_time_left = start_time_left

    @property
    def start_time_right(self):
        """Gets the start_time_right of this ListApplicationHistoryRequest.  # noqa: E501


        :return: The start_time_right of this ListApplicationHistoryRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time_right

    @start_time_right.setter
    def start_time_right(self, start_time_right):
        """Sets the start_time_right of this ListApplicationHistoryRequest.


        :param start_time_right: The start_time_right of this ListApplicationHistoryRequest.  # noqa: E501
        :type: str
        """

        self._start_time_right = start_time_right

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListApplicationHistoryRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListApplicationHistoryRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListApplicationHistoryRequest):
            return True

        return self.to_dict() != other.to_dict()
