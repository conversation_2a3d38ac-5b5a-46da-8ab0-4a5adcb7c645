# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LoopVideoForCreateActivityAPIV2Input(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'index': 'int',
        'offset': 'int',
        'video_name': 'str',
        'video_vid': 'str'
    }

    attribute_map = {
        'index': 'Index',
        'offset': 'Offset',
        'video_name': 'VideoName',
        'video_vid': 'VideoVid'
    }

    def __init__(self, index=None, offset=None, video_name=None, video_vid=None, _configuration=None):  # noqa: E501
        """LoopVideoForCreateActivityAPIV2Input - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._index = None
        self._offset = None
        self._video_name = None
        self._video_vid = None
        self.discriminator = None

        if index is not None:
            self.index = index
        if offset is not None:
            self.offset = offset
        if video_name is not None:
            self.video_name = video_name
        if video_vid is not None:
            self.video_vid = video_vid

    @property
    def index(self):
        """Gets the index of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501


        :return: The index of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :rtype: int
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this LoopVideoForCreateActivityAPIV2Input.


        :param index: The index of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :type: int
        """

        self._index = index

    @property
    def offset(self):
        """Gets the offset of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501


        :return: The offset of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this LoopVideoForCreateActivityAPIV2Input.


        :param offset: The offset of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def video_name(self):
        """Gets the video_name of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501


        :return: The video_name of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :rtype: str
        """
        return self._video_name

    @video_name.setter
    def video_name(self, video_name):
        """Sets the video_name of this LoopVideoForCreateActivityAPIV2Input.


        :param video_name: The video_name of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :type: str
        """

        self._video_name = video_name

    @property
    def video_vid(self):
        """Gets the video_vid of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501


        :return: The video_vid of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :rtype: str
        """
        return self._video_vid

    @video_vid.setter
    def video_vid(self, video_vid):
        """Sets the video_vid of this LoopVideoForCreateActivityAPIV2Input.


        :param video_vid: The video_vid of this LoopVideoForCreateActivityAPIV2Input.  # noqa: E501
        :type: str
        """

        self._video_vid = video_vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LoopVideoForCreateActivityAPIV2Input, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LoopVideoForCreateActivityAPIV2Input):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LoopVideoForCreateActivityAPIV2Input):
            return True

        return self.to_dict() != other.to_dict()
