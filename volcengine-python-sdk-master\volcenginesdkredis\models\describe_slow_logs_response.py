# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSlowLogsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'context': 'str',
        'instance_id': 'str',
        'list_over': 'bool',
        'slow_query': 'list[SlowQueryForDescribeSlowLogsOutput]',
        'total': 'int'
    }

    attribute_map = {
        'context': 'Context',
        'instance_id': 'InstanceId',
        'list_over': 'ListOver',
        'slow_query': 'SlowQuery',
        'total': 'Total'
    }

    def __init__(self, context=None, instance_id=None, list_over=None, slow_query=None, total=None, _configuration=None):  # noqa: E501
        """DescribeSlowLogsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._context = None
        self._instance_id = None
        self._list_over = None
        self._slow_query = None
        self._total = None
        self.discriminator = None

        if context is not None:
            self.context = context
        if instance_id is not None:
            self.instance_id = instance_id
        if list_over is not None:
            self.list_over = list_over
        if slow_query is not None:
            self.slow_query = slow_query
        if total is not None:
            self.total = total

    @property
    def context(self):
        """Gets the context of this DescribeSlowLogsResponse.  # noqa: E501


        :return: The context of this DescribeSlowLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._context

    @context.setter
    def context(self, context):
        """Sets the context of this DescribeSlowLogsResponse.


        :param context: The context of this DescribeSlowLogsResponse.  # noqa: E501
        :type: str
        """

        self._context = context

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeSlowLogsResponse.  # noqa: E501


        :return: The instance_id of this DescribeSlowLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeSlowLogsResponse.


        :param instance_id: The instance_id of this DescribeSlowLogsResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def list_over(self):
        """Gets the list_over of this DescribeSlowLogsResponse.  # noqa: E501


        :return: The list_over of this DescribeSlowLogsResponse.  # noqa: E501
        :rtype: bool
        """
        return self._list_over

    @list_over.setter
    def list_over(self, list_over):
        """Sets the list_over of this DescribeSlowLogsResponse.


        :param list_over: The list_over of this DescribeSlowLogsResponse.  # noqa: E501
        :type: bool
        """

        self._list_over = list_over

    @property
    def slow_query(self):
        """Gets the slow_query of this DescribeSlowLogsResponse.  # noqa: E501


        :return: The slow_query of this DescribeSlowLogsResponse.  # noqa: E501
        :rtype: list[SlowQueryForDescribeSlowLogsOutput]
        """
        return self._slow_query

    @slow_query.setter
    def slow_query(self, slow_query):
        """Sets the slow_query of this DescribeSlowLogsResponse.


        :param slow_query: The slow_query of this DescribeSlowLogsResponse.  # noqa: E501
        :type: list[SlowQueryForDescribeSlowLogsOutput]
        """

        self._slow_query = slow_query

    @property
    def total(self):
        """Gets the total of this DescribeSlowLogsResponse.  # noqa: E501


        :return: The total of this DescribeSlowLogsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeSlowLogsResponse.


        :param total: The total of this DescribeSlowLogsResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSlowLogsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSlowLogsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSlowLogsResponse):
            return True

        return self.to_dict() != other.to_dict()
