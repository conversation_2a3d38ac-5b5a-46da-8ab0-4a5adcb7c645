# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListProductQuotaDimensionsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'provider_code': 'str',
        'quota_type': 'str'
    }

    attribute_map = {
        'provider_code': 'ProviderCode',
        'quota_type': 'QuotaType'
    }

    def __init__(self, provider_code=None, quota_type=None, _configuration=None):  # noqa: E501
        """ListProductQuotaDimensionsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._provider_code = None
        self._quota_type = None
        self.discriminator = None

        self.provider_code = provider_code
        if quota_type is not None:
            self.quota_type = quota_type

    @property
    def provider_code(self):
        """Gets the provider_code of this ListProductQuotaDimensionsRequest.  # noqa: E501


        :return: The provider_code of this ListProductQuotaDimensionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this ListProductQuotaDimensionsRequest.


        :param provider_code: The provider_code of this ListProductQuotaDimensionsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and provider_code is None:
            raise ValueError("Invalid value for `provider_code`, must not be `None`")  # noqa: E501

        self._provider_code = provider_code

    @property
    def quota_type(self):
        """Gets the quota_type of this ListProductQuotaDimensionsRequest.  # noqa: E501


        :return: The quota_type of this ListProductQuotaDimensionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this ListProductQuotaDimensionsRequest.


        :param quota_type: The quota_type of this ListProductQuotaDimensionsRequest.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListProductQuotaDimensionsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListProductQuotaDimensionsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListProductQuotaDimensionsRequest):
            return True

        return self.to_dict() != other.to_dict()
