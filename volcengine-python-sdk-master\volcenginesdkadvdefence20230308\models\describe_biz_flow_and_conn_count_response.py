# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBizFlowAndConnCountResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'biz_in_kbps_flow': 'BizInKbpsFlowForDescribeBizFlowAndConnCountOutput',
        'biz_in_pps_flow': 'BizInPpsFlowForDescribeBizFlowAndConnCountOutput',
        'biz_out_kbps_flow': 'BizOutKbpsFlowForDescribeBizFlowAndConnCountOutput',
        'biz_out_pps_flow': 'BizOutPpsFlowForDescribeBizFlowAndConnCountOutput',
        'concurr_conn_flow': 'ConcurrConnFlowForDescribeBizFlowAndConnCountOutput',
        'new_conn_flow': 'NewConnFlowForDescribeBizFlowAndConnCountOutput'
    }

    attribute_map = {
        'biz_in_kbps_flow': 'BizInKbpsFlow',
        'biz_in_pps_flow': 'BizInPpsFlow',
        'biz_out_kbps_flow': 'BizOutKbpsFlow',
        'biz_out_pps_flow': 'BizOutPpsFlow',
        'concurr_conn_flow': 'ConcurrConnFlow',
        'new_conn_flow': 'NewConnFlow'
    }

    def __init__(self, biz_in_kbps_flow=None, biz_in_pps_flow=None, biz_out_kbps_flow=None, biz_out_pps_flow=None, concurr_conn_flow=None, new_conn_flow=None, _configuration=None):  # noqa: E501
        """DescribeBizFlowAndConnCountResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._biz_in_kbps_flow = None
        self._biz_in_pps_flow = None
        self._biz_out_kbps_flow = None
        self._biz_out_pps_flow = None
        self._concurr_conn_flow = None
        self._new_conn_flow = None
        self.discriminator = None

        if biz_in_kbps_flow is not None:
            self.biz_in_kbps_flow = biz_in_kbps_flow
        if biz_in_pps_flow is not None:
            self.biz_in_pps_flow = biz_in_pps_flow
        if biz_out_kbps_flow is not None:
            self.biz_out_kbps_flow = biz_out_kbps_flow
        if biz_out_pps_flow is not None:
            self.biz_out_pps_flow = biz_out_pps_flow
        if concurr_conn_flow is not None:
            self.concurr_conn_flow = concurr_conn_flow
        if new_conn_flow is not None:
            self.new_conn_flow = new_conn_flow

    @property
    def biz_in_kbps_flow(self):
        """Gets the biz_in_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The biz_in_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: BizInKbpsFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._biz_in_kbps_flow

    @biz_in_kbps_flow.setter
    def biz_in_kbps_flow(self, biz_in_kbps_flow):
        """Sets the biz_in_kbps_flow of this DescribeBizFlowAndConnCountResponse.


        :param biz_in_kbps_flow: The biz_in_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: BizInKbpsFlowForDescribeBizFlowAndConnCountOutput
        """

        self._biz_in_kbps_flow = biz_in_kbps_flow

    @property
    def biz_in_pps_flow(self):
        """Gets the biz_in_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The biz_in_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: BizInPpsFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._biz_in_pps_flow

    @biz_in_pps_flow.setter
    def biz_in_pps_flow(self, biz_in_pps_flow):
        """Sets the biz_in_pps_flow of this DescribeBizFlowAndConnCountResponse.


        :param biz_in_pps_flow: The biz_in_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: BizInPpsFlowForDescribeBizFlowAndConnCountOutput
        """

        self._biz_in_pps_flow = biz_in_pps_flow

    @property
    def biz_out_kbps_flow(self):
        """Gets the biz_out_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The biz_out_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: BizOutKbpsFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._biz_out_kbps_flow

    @biz_out_kbps_flow.setter
    def biz_out_kbps_flow(self, biz_out_kbps_flow):
        """Sets the biz_out_kbps_flow of this DescribeBizFlowAndConnCountResponse.


        :param biz_out_kbps_flow: The biz_out_kbps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: BizOutKbpsFlowForDescribeBizFlowAndConnCountOutput
        """

        self._biz_out_kbps_flow = biz_out_kbps_flow

    @property
    def biz_out_pps_flow(self):
        """Gets the biz_out_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The biz_out_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: BizOutPpsFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._biz_out_pps_flow

    @biz_out_pps_flow.setter
    def biz_out_pps_flow(self, biz_out_pps_flow):
        """Sets the biz_out_pps_flow of this DescribeBizFlowAndConnCountResponse.


        :param biz_out_pps_flow: The biz_out_pps_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: BizOutPpsFlowForDescribeBizFlowAndConnCountOutput
        """

        self._biz_out_pps_flow = biz_out_pps_flow

    @property
    def concurr_conn_flow(self):
        """Gets the concurr_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The concurr_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: ConcurrConnFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._concurr_conn_flow

    @concurr_conn_flow.setter
    def concurr_conn_flow(self, concurr_conn_flow):
        """Sets the concurr_conn_flow of this DescribeBizFlowAndConnCountResponse.


        :param concurr_conn_flow: The concurr_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: ConcurrConnFlowForDescribeBizFlowAndConnCountOutput
        """

        self._concurr_conn_flow = concurr_conn_flow

    @property
    def new_conn_flow(self):
        """Gets the new_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501


        :return: The new_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :rtype: NewConnFlowForDescribeBizFlowAndConnCountOutput
        """
        return self._new_conn_flow

    @new_conn_flow.setter
    def new_conn_flow(self, new_conn_flow):
        """Sets the new_conn_flow of this DescribeBizFlowAndConnCountResponse.


        :param new_conn_flow: The new_conn_flow of this DescribeBizFlowAndConnCountResponse.  # noqa: E501
        :type: NewConnFlowForDescribeBizFlowAndConnCountOutput
        """

        self._new_conn_flow = new_conn_flow

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBizFlowAndConnCountResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBizFlowAndConnCountResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBizFlowAndConnCountResponse):
            return True

        return self.to_dict() != other.to_dict()
