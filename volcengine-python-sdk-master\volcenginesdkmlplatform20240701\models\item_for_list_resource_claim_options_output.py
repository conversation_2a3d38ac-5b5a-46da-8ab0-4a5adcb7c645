# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListResourceClaimOptionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu': 'float',
        'family': 'str',
        'gpu_count': 'int',
        'gpu_memory_mi_b': 'int',
        'gpu_type': 'str',
        'id': 'str',
        'kind': 'str',
        'memory_mi_b': 'float',
        'payload_list': 'list[str]',
        'rdma_eni_count': 'int',
        'zone_info': 'list[ZoneInfoForListResourceClaimOptionsOutput]'
    }

    attribute_map = {
        'cpu': 'Cpu',
        'family': 'Family',
        'gpu_count': 'GpuCount',
        'gpu_memory_mi_b': 'GpuMemoryMiB',
        'gpu_type': 'GpuType',
        'id': 'Id',
        'kind': 'Kind',
        'memory_mi_b': 'MemoryMiB',
        'payload_list': 'PayloadList',
        'rdma_eni_count': 'RdmaEniCount',
        'zone_info': 'ZoneInfo'
    }

    def __init__(self, cpu=None, family=None, gpu_count=None, gpu_memory_mi_b=None, gpu_type=None, id=None, kind=None, memory_mi_b=None, payload_list=None, rdma_eni_count=None, zone_info=None, _configuration=None):  # noqa: E501
        """ItemForListResourceClaimOptionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu = None
        self._family = None
        self._gpu_count = None
        self._gpu_memory_mi_b = None
        self._gpu_type = None
        self._id = None
        self._kind = None
        self._memory_mi_b = None
        self._payload_list = None
        self._rdma_eni_count = None
        self._zone_info = None
        self.discriminator = None

        if cpu is not None:
            self.cpu = cpu
        if family is not None:
            self.family = family
        if gpu_count is not None:
            self.gpu_count = gpu_count
        if gpu_memory_mi_b is not None:
            self.gpu_memory_mi_b = gpu_memory_mi_b
        if gpu_type is not None:
            self.gpu_type = gpu_type
        if id is not None:
            self.id = id
        if kind is not None:
            self.kind = kind
        if memory_mi_b is not None:
            self.memory_mi_b = memory_mi_b
        if payload_list is not None:
            self.payload_list = payload_list
        if rdma_eni_count is not None:
            self.rdma_eni_count = rdma_eni_count
        if zone_info is not None:
            self.zone_info = zone_info

    @property
    def cpu(self):
        """Gets the cpu of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The cpu of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this ItemForListResourceClaimOptionsOutput.


        :param cpu: The cpu of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: float
        """

        self._cpu = cpu

    @property
    def family(self):
        """Gets the family of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The family of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._family

    @family.setter
    def family(self, family):
        """Sets the family of this ItemForListResourceClaimOptionsOutput.


        :param family: The family of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._family = family

    @property
    def gpu_count(self):
        """Gets the gpu_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The gpu_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._gpu_count

    @gpu_count.setter
    def gpu_count(self, gpu_count):
        """Sets the gpu_count of this ItemForListResourceClaimOptionsOutput.


        :param gpu_count: The gpu_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: int
        """

        self._gpu_count = gpu_count

    @property
    def gpu_memory_mi_b(self):
        """Gets the gpu_memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The gpu_memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._gpu_memory_mi_b

    @gpu_memory_mi_b.setter
    def gpu_memory_mi_b(self, gpu_memory_mi_b):
        """Sets the gpu_memory_mi_b of this ItemForListResourceClaimOptionsOutput.


        :param gpu_memory_mi_b: The gpu_memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: int
        """

        self._gpu_memory_mi_b = gpu_memory_mi_b

    @property
    def gpu_type(self):
        """Gets the gpu_type of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The gpu_type of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._gpu_type

    @gpu_type.setter
    def gpu_type(self, gpu_type):
        """Sets the gpu_type of this ItemForListResourceClaimOptionsOutput.


        :param gpu_type: The gpu_type of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._gpu_type = gpu_type

    @property
    def id(self):
        """Gets the id of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The id of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListResourceClaimOptionsOutput.


        :param id: The id of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def kind(self):
        """Gets the kind of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The kind of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this ItemForListResourceClaimOptionsOutput.


        :param kind: The kind of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def memory_mi_b(self):
        """Gets the memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: float
        """
        return self._memory_mi_b

    @memory_mi_b.setter
    def memory_mi_b(self, memory_mi_b):
        """Sets the memory_mi_b of this ItemForListResourceClaimOptionsOutput.


        :param memory_mi_b: The memory_mi_b of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: float
        """

        self._memory_mi_b = memory_mi_b

    @property
    def payload_list(self):
        """Gets the payload_list of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The payload_list of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._payload_list

    @payload_list.setter
    def payload_list(self, payload_list):
        """Sets the payload_list of this ItemForListResourceClaimOptionsOutput.


        :param payload_list: The payload_list of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: list[str]
        """

        self._payload_list = payload_list

    @property
    def rdma_eni_count(self):
        """Gets the rdma_eni_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The rdma_eni_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rdma_eni_count

    @rdma_eni_count.setter
    def rdma_eni_count(self, rdma_eni_count):
        """Sets the rdma_eni_count of this ItemForListResourceClaimOptionsOutput.


        :param rdma_eni_count: The rdma_eni_count of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: int
        """

        self._rdma_eni_count = rdma_eni_count

    @property
    def zone_info(self):
        """Gets the zone_info of this ItemForListResourceClaimOptionsOutput.  # noqa: E501


        :return: The zone_info of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :rtype: list[ZoneInfoForListResourceClaimOptionsOutput]
        """
        return self._zone_info

    @zone_info.setter
    def zone_info(self, zone_info):
        """Sets the zone_info of this ItemForListResourceClaimOptionsOutput.


        :param zone_info: The zone_info of this ItemForListResourceClaimOptionsOutput.  # noqa: E501
        :type: list[ZoneInfoForListResourceClaimOptionsOutput]
        """

        self._zone_info = zone_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListResourceClaimOptionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListResourceClaimOptionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListResourceClaimOptionsOutput):
            return True

        return self.to_dict() != other.to_dict()
