# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVipOrBlackListUserInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_user_info_list': 'list[AwardUserInfoListForCreateVipOrBlackListUserInfoInput]',
        'id': 'int',
        'role': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_user_info_list': 'AwardUserInfoList',
        'id': 'Id',
        'role': 'Role'
    }

    def __init__(self, activity_id=None, award_user_info_list=None, id=None, role=None, _configuration=None):  # noqa: E501
        """CreateVipOrBlackListUserInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_user_info_list = None
        self._id = None
        self._role = None
        self.discriminator = None

        self.activity_id = activity_id
        if award_user_info_list is not None:
            self.award_user_info_list = award_user_info_list
        if id is not None:
            self.id = id
        if role is not None:
            self.role = role

    @property
    def activity_id(self):
        """Gets the activity_id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501


        :return: The activity_id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this CreateVipOrBlackListUserInfoRequest.


        :param activity_id: The activity_id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def award_user_info_list(self):
        """Gets the award_user_info_list of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501


        :return: The award_user_info_list of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :rtype: list[AwardUserInfoListForCreateVipOrBlackListUserInfoInput]
        """
        return self._award_user_info_list

    @award_user_info_list.setter
    def award_user_info_list(self, award_user_info_list):
        """Sets the award_user_info_list of this CreateVipOrBlackListUserInfoRequest.


        :param award_user_info_list: The award_user_info_list of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :type: list[AwardUserInfoListForCreateVipOrBlackListUserInfoInput]
        """

        self._award_user_info_list = award_user_info_list

    @property
    def id(self):
        """Gets the id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501


        :return: The id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CreateVipOrBlackListUserInfoRequest.


        :param id: The id of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def role(self):
        """Gets the role of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501


        :return: The role of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this CreateVipOrBlackListUserInfoRequest.


        :param role: The role of this CreateVipOrBlackListUserInfoRequest.  # noqa: E501
        :type: int
        """

        self._role = role

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVipOrBlackListUserInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVipOrBlackListUserInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVipOrBlackListUserInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
