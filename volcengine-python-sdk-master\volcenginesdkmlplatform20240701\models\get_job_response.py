# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetJobResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'created_by': 'str',
        'description': 'str',
        'diagnose_config': 'list[DiagnoseConfigForGetJobOutput]',
        'id': 'str',
        'initial_id': 'str',
        'name': 'str',
        'observable_config': 'ObservableConfigForGetJobOutput',
        'private_network_config': 'PrivateNetworkConfigForGetJobOutput',
        'resource_config': 'ResourceConfigForGetJobOutput',
        'retry_config': 'RetryConfigForGetJobOutput',
        'runtime_config': 'RuntimeConfigForGetJobOutput',
        'status': 'StatusForGetJobOutput',
        'stop_reason': 'str',
        'storage_config': 'StorageConfigForGetJobOutput',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'created_by': 'CreatedBy',
        'description': 'Description',
        'diagnose_config': 'DiagnoseConfig',
        'id': 'Id',
        'initial_id': 'InitialId',
        'name': 'Name',
        'observable_config': 'ObservableConfig',
        'private_network_config': 'PrivateNetworkConfig',
        'resource_config': 'ResourceConfig',
        'retry_config': 'RetryConfig',
        'runtime_config': 'RuntimeConfig',
        'status': 'Status',
        'stop_reason': 'StopReason',
        'storage_config': 'StorageConfig',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, created_by=None, description=None, diagnose_config=None, id=None, initial_id=None, name=None, observable_config=None, private_network_config=None, resource_config=None, retry_config=None, runtime_config=None, status=None, stop_reason=None, storage_config=None, update_time=None, _configuration=None):  # noqa: E501
        """GetJobResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._created_by = None
        self._description = None
        self._diagnose_config = None
        self._id = None
        self._initial_id = None
        self._name = None
        self._observable_config = None
        self._private_network_config = None
        self._resource_config = None
        self._retry_config = None
        self._runtime_config = None
        self._status = None
        self._stop_reason = None
        self._storage_config = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if created_by is not None:
            self.created_by = created_by
        if description is not None:
            self.description = description
        if diagnose_config is not None:
            self.diagnose_config = diagnose_config
        if id is not None:
            self.id = id
        if initial_id is not None:
            self.initial_id = initial_id
        if name is not None:
            self.name = name
        if observable_config is not None:
            self.observable_config = observable_config
        if private_network_config is not None:
            self.private_network_config = private_network_config
        if resource_config is not None:
            self.resource_config = resource_config
        if retry_config is not None:
            self.retry_config = retry_config
        if runtime_config is not None:
            self.runtime_config = runtime_config
        if status is not None:
            self.status = status
        if stop_reason is not None:
            self.stop_reason = stop_reason
        if storage_config is not None:
            self.storage_config = storage_config
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this GetJobResponse.  # noqa: E501


        :return: The create_time of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetJobResponse.


        :param create_time: The create_time of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def created_by(self):
        """Gets the created_by of this GetJobResponse.  # noqa: E501


        :return: The created_by of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this GetJobResponse.


        :param created_by: The created_by of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def description(self):
        """Gets the description of this GetJobResponse.  # noqa: E501


        :return: The description of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetJobResponse.


        :param description: The description of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def diagnose_config(self):
        """Gets the diagnose_config of this GetJobResponse.  # noqa: E501


        :return: The diagnose_config of this GetJobResponse.  # noqa: E501
        :rtype: list[DiagnoseConfigForGetJobOutput]
        """
        return self._diagnose_config

    @diagnose_config.setter
    def diagnose_config(self, diagnose_config):
        """Sets the diagnose_config of this GetJobResponse.


        :param diagnose_config: The diagnose_config of this GetJobResponse.  # noqa: E501
        :type: list[DiagnoseConfigForGetJobOutput]
        """

        self._diagnose_config = diagnose_config

    @property
    def id(self):
        """Gets the id of this GetJobResponse.  # noqa: E501


        :return: The id of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetJobResponse.


        :param id: The id of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def initial_id(self):
        """Gets the initial_id of this GetJobResponse.  # noqa: E501


        :return: The initial_id of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._initial_id

    @initial_id.setter
    def initial_id(self, initial_id):
        """Sets the initial_id of this GetJobResponse.


        :param initial_id: The initial_id of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._initial_id = initial_id

    @property
    def name(self):
        """Gets the name of this GetJobResponse.  # noqa: E501


        :return: The name of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetJobResponse.


        :param name: The name of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def observable_config(self):
        """Gets the observable_config of this GetJobResponse.  # noqa: E501


        :return: The observable_config of this GetJobResponse.  # noqa: E501
        :rtype: ObservableConfigForGetJobOutput
        """
        return self._observable_config

    @observable_config.setter
    def observable_config(self, observable_config):
        """Sets the observable_config of this GetJobResponse.


        :param observable_config: The observable_config of this GetJobResponse.  # noqa: E501
        :type: ObservableConfigForGetJobOutput
        """

        self._observable_config = observable_config

    @property
    def private_network_config(self):
        """Gets the private_network_config of this GetJobResponse.  # noqa: E501


        :return: The private_network_config of this GetJobResponse.  # noqa: E501
        :rtype: PrivateNetworkConfigForGetJobOutput
        """
        return self._private_network_config

    @private_network_config.setter
    def private_network_config(self, private_network_config):
        """Sets the private_network_config of this GetJobResponse.


        :param private_network_config: The private_network_config of this GetJobResponse.  # noqa: E501
        :type: PrivateNetworkConfigForGetJobOutput
        """

        self._private_network_config = private_network_config

    @property
    def resource_config(self):
        """Gets the resource_config of this GetJobResponse.  # noqa: E501


        :return: The resource_config of this GetJobResponse.  # noqa: E501
        :rtype: ResourceConfigForGetJobOutput
        """
        return self._resource_config

    @resource_config.setter
    def resource_config(self, resource_config):
        """Sets the resource_config of this GetJobResponse.


        :param resource_config: The resource_config of this GetJobResponse.  # noqa: E501
        :type: ResourceConfigForGetJobOutput
        """

        self._resource_config = resource_config

    @property
    def retry_config(self):
        """Gets the retry_config of this GetJobResponse.  # noqa: E501


        :return: The retry_config of this GetJobResponse.  # noqa: E501
        :rtype: RetryConfigForGetJobOutput
        """
        return self._retry_config

    @retry_config.setter
    def retry_config(self, retry_config):
        """Sets the retry_config of this GetJobResponse.


        :param retry_config: The retry_config of this GetJobResponse.  # noqa: E501
        :type: RetryConfigForGetJobOutput
        """

        self._retry_config = retry_config

    @property
    def runtime_config(self):
        """Gets the runtime_config of this GetJobResponse.  # noqa: E501


        :return: The runtime_config of this GetJobResponse.  # noqa: E501
        :rtype: RuntimeConfigForGetJobOutput
        """
        return self._runtime_config

    @runtime_config.setter
    def runtime_config(self, runtime_config):
        """Sets the runtime_config of this GetJobResponse.


        :param runtime_config: The runtime_config of this GetJobResponse.  # noqa: E501
        :type: RuntimeConfigForGetJobOutput
        """

        self._runtime_config = runtime_config

    @property
    def status(self):
        """Gets the status of this GetJobResponse.  # noqa: E501


        :return: The status of this GetJobResponse.  # noqa: E501
        :rtype: StatusForGetJobOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetJobResponse.


        :param status: The status of this GetJobResponse.  # noqa: E501
        :type: StatusForGetJobOutput
        """

        self._status = status

    @property
    def stop_reason(self):
        """Gets the stop_reason of this GetJobResponse.  # noqa: E501


        :return: The stop_reason of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._stop_reason

    @stop_reason.setter
    def stop_reason(self, stop_reason):
        """Sets the stop_reason of this GetJobResponse.


        :param stop_reason: The stop_reason of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._stop_reason = stop_reason

    @property
    def storage_config(self):
        """Gets the storage_config of this GetJobResponse.  # noqa: E501


        :return: The storage_config of this GetJobResponse.  # noqa: E501
        :rtype: StorageConfigForGetJobOutput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this GetJobResponse.


        :param storage_config: The storage_config of this GetJobResponse.  # noqa: E501
        :type: StorageConfigForGetJobOutput
        """

        self._storage_config = storage_config

    @property
    def update_time(self):
        """Gets the update_time of this GetJobResponse.  # noqa: E501


        :return: The update_time of this GetJobResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetJobResponse.


        :param update_time: The update_time of this GetJobResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetJobResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetJobResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetJobResponse):
            return True

        return self.to_dict() != other.to_dict()
