# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityReservationAPIV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'channel': 'str',
        'external_id': 'str',
        'nick_name': 'str',
        'page': 'int',
        'page_size': 'int',
        'tel_reserve': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'channel': 'Channel',
        'external_id': 'ExternalId',
        'nick_name': 'NickName',
        'page': 'Page',
        'page_size': 'PageSize',
        'tel_reserve': 'TelReserve'
    }

    def __init__(self, activity_id=None, channel=None, external_id=None, nick_name=None, page=None, page_size=None, tel_reserve=None, _configuration=None):  # noqa: E501
        """GetActivityReservationAPIV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._channel = None
        self._external_id = None
        self._nick_name = None
        self._page = None
        self._page_size = None
        self._tel_reserve = None
        self.discriminator = None

        self.activity_id = activity_id
        if channel is not None:
            self.channel = channel
        if external_id is not None:
            self.external_id = external_id
        if nick_name is not None:
            self.nick_name = nick_name
        if page is not None:
            self.page = page
        if page_size is not None:
            self.page_size = page_size
        if tel_reserve is not None:
            self.tel_reserve = tel_reserve

    @property
    def activity_id(self):
        """Gets the activity_id of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The activity_id of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetActivityReservationAPIV2Request.


        :param activity_id: The activity_id of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def channel(self):
        """Gets the channel of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The channel of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._channel

    @channel.setter
    def channel(self, channel):
        """Sets the channel of this GetActivityReservationAPIV2Request.


        :param channel: The channel of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: str
        """

        self._channel = channel

    @property
    def external_id(self):
        """Gets the external_id of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The external_id of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this GetActivityReservationAPIV2Request.


        :param external_id: The external_id of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def nick_name(self):
        """Gets the nick_name of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The nick_name of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this GetActivityReservationAPIV2Request.


        :param nick_name: The nick_name of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def page(self):
        """Gets the page of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The page of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._page

    @page.setter
    def page(self, page):
        """Sets the page of this GetActivityReservationAPIV2Request.


        :param page: The page of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: int
        """

        self._page = page

    @property
    def page_size(self):
        """Gets the page_size of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The page_size of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetActivityReservationAPIV2Request.


        :param page_size: The page_size of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def tel_reserve(self):
        """Gets the tel_reserve of this GetActivityReservationAPIV2Request.  # noqa: E501


        :return: The tel_reserve of this GetActivityReservationAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._tel_reserve

    @tel_reserve.setter
    def tel_reserve(self, tel_reserve):
        """Sets the tel_reserve of this GetActivityReservationAPIV2Request.


        :param tel_reserve: The tel_reserve of this GetActivityReservationAPIV2Request.  # noqa: E501
        :type: str
        """

        self._tel_reserve = tel_reserve

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityReservationAPIV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityReservationAPIV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityReservationAPIV2Request):
            return True

        return self.to_dict() != other.to_dict()
