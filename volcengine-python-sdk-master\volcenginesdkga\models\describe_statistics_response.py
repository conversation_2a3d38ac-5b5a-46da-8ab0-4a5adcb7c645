# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'results': 'list[ResultForDescribeStatisticsOutput]',
        'total_statistic_results': 'list[TotalStatisticResultForDescribeStatisticsOutput]'
    }

    attribute_map = {
        'results': 'Results',
        'total_statistic_results': 'TotalStatisticResults'
    }

    def __init__(self, results=None, total_statistic_results=None, _configuration=None):  # noqa: E501
        """DescribeStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._results = None
        self._total_statistic_results = None
        self.discriminator = None

        if results is not None:
            self.results = results
        if total_statistic_results is not None:
            self.total_statistic_results = total_statistic_results

    @property
    def results(self):
        """Gets the results of this DescribeStatisticsResponse.  # noqa: E501


        :return: The results of this DescribeStatisticsResponse.  # noqa: E501
        :rtype: list[ResultForDescribeStatisticsOutput]
        """
        return self._results

    @results.setter
    def results(self, results):
        """Sets the results of this DescribeStatisticsResponse.


        :param results: The results of this DescribeStatisticsResponse.  # noqa: E501
        :type: list[ResultForDescribeStatisticsOutput]
        """

        self._results = results

    @property
    def total_statistic_results(self):
        """Gets the total_statistic_results of this DescribeStatisticsResponse.  # noqa: E501


        :return: The total_statistic_results of this DescribeStatisticsResponse.  # noqa: E501
        :rtype: list[TotalStatisticResultForDescribeStatisticsOutput]
        """
        return self._total_statistic_results

    @total_statistic_results.setter
    def total_statistic_results(self, total_statistic_results):
        """Sets the total_statistic_results of this DescribeStatisticsResponse.


        :param total_statistic_results: The total_statistic_results of this DescribeStatisticsResponse.  # noqa: E501
        :type: list[TotalStatisticResultForDescribeStatisticsOutput]
        """

        self._total_statistic_results = total_statistic_results

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
