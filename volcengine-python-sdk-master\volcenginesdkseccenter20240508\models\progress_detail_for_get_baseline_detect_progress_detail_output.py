# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProgressDetailForGetBaselineDetectProgressDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'progress': 'int',
        'time_left': 'int'
    }

    attribute_map = {
        'progress': 'Progress',
        'time_left': 'TimeLeft'
    }

    def __init__(self, progress=None, time_left=None, _configuration=None):  # noqa: E501
        """ProgressDetailForGetBaselineDetectProgressDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._progress = None
        self._time_left = None
        self.discriminator = None

        if progress is not None:
            self.progress = progress
        if time_left is not None:
            self.time_left = time_left

    @property
    def progress(self):
        """Gets the progress of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The progress of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._progress

    @progress.setter
    def progress(self, progress):
        """Sets the progress of this ProgressDetailForGetBaselineDetectProgressDetailOutput.


        :param progress: The progress of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: int
        """

        self._progress = progress

    @property
    def time_left(self):
        """Gets the time_left of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The time_left of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._time_left

    @time_left.setter
    def time_left(self, time_left):
        """Sets the time_left of this ProgressDetailForGetBaselineDetectProgressDetailOutput.


        :param time_left: The time_left of this ProgressDetailForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: int
        """

        self._time_left = time_left

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProgressDetailForGetBaselineDetectProgressDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProgressDetailForGetBaselineDetectProgressDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProgressDetailForGetBaselineDetectProgressDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
