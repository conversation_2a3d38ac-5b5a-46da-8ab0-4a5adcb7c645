# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BusinessAccountInfoForGetBusinessAccountInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_head_image': 'str',
        'account_head_redirect_url': 'str',
        'account_name': 'str',
        'host_account_id': 'int',
        'is_follow_enable': 'int'
    }

    attribute_map = {
        'account_head_image': 'AccountHeadImage',
        'account_head_redirect_url': 'AccountHeadRedirectUrl',
        'account_name': 'AccountName',
        'host_account_id': 'HostAccountId',
        'is_follow_enable': 'IsFollowEnable'
    }

    def __init__(self, account_head_image=None, account_head_redirect_url=None, account_name=None, host_account_id=None, is_follow_enable=None, _configuration=None):  # noqa: E501
        """BusinessAccountInfoForGetBusinessAccountInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_head_image = None
        self._account_head_redirect_url = None
        self._account_name = None
        self._host_account_id = None
        self._is_follow_enable = None
        self.discriminator = None

        if account_head_image is not None:
            self.account_head_image = account_head_image
        if account_head_redirect_url is not None:
            self.account_head_redirect_url = account_head_redirect_url
        if account_name is not None:
            self.account_name = account_name
        if host_account_id is not None:
            self.host_account_id = host_account_id
        if is_follow_enable is not None:
            self.is_follow_enable = is_follow_enable

    @property
    def account_head_image(self):
        """Gets the account_head_image of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501


        :return: The account_head_image of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_head_image

    @account_head_image.setter
    def account_head_image(self, account_head_image):
        """Sets the account_head_image of this BusinessAccountInfoForGetBusinessAccountInfoOutput.


        :param account_head_image: The account_head_image of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :type: str
        """

        self._account_head_image = account_head_image

    @property
    def account_head_redirect_url(self):
        """Gets the account_head_redirect_url of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501


        :return: The account_head_redirect_url of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_head_redirect_url

    @account_head_redirect_url.setter
    def account_head_redirect_url(self, account_head_redirect_url):
        """Sets the account_head_redirect_url of this BusinessAccountInfoForGetBusinessAccountInfoOutput.


        :param account_head_redirect_url: The account_head_redirect_url of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :type: str
        """

        self._account_head_redirect_url = account_head_redirect_url

    @property
    def account_name(self):
        """Gets the account_name of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501


        :return: The account_name of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this BusinessAccountInfoForGetBusinessAccountInfoOutput.


        :param account_name: The account_name of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def host_account_id(self):
        """Gets the host_account_id of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501


        :return: The host_account_id of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_account_id

    @host_account_id.setter
    def host_account_id(self, host_account_id):
        """Sets the host_account_id of this BusinessAccountInfoForGetBusinessAccountInfoOutput.


        :param host_account_id: The host_account_id of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :type: int
        """

        self._host_account_id = host_account_id

    @property
    def is_follow_enable(self):
        """Gets the is_follow_enable of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501


        :return: The is_follow_enable of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_follow_enable

    @is_follow_enable.setter
    def is_follow_enable(self, is_follow_enable):
        """Sets the is_follow_enable of this BusinessAccountInfoForGetBusinessAccountInfoOutput.


        :param is_follow_enable: The is_follow_enable of this BusinessAccountInfoForGetBusinessAccountInfoOutput.  # noqa: E501
        :type: int
        """

        self._is_follow_enable = is_follow_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BusinessAccountInfoForGetBusinessAccountInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BusinessAccountInfoForGetBusinessAccountInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BusinessAccountInfoForGetBusinessAccountInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
