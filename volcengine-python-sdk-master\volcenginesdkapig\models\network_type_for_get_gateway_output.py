# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkTypeForGetGatewayOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_private_network': 'bool',
        'enable_public_network': 'bool'
    }

    attribute_map = {
        'enable_private_network': 'EnablePrivateNetwork',
        'enable_public_network': 'EnablePublicNetwork'
    }

    def __init__(self, enable_private_network=None, enable_public_network=None, _configuration=None):  # noqa: E501
        """NetworkTypeForGetGatewayOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_private_network = None
        self._enable_public_network = None
        self.discriminator = None

        if enable_private_network is not None:
            self.enable_private_network = enable_private_network
        if enable_public_network is not None:
            self.enable_public_network = enable_public_network

    @property
    def enable_private_network(self):
        """Gets the enable_private_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501


        :return: The enable_private_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_private_network

    @enable_private_network.setter
    def enable_private_network(self, enable_private_network):
        """Sets the enable_private_network of this NetworkTypeForGetGatewayOutput.


        :param enable_private_network: The enable_private_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501
        :type: bool
        """

        self._enable_private_network = enable_private_network

    @property
    def enable_public_network(self):
        """Gets the enable_public_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501


        :return: The enable_public_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_public_network

    @enable_public_network.setter
    def enable_public_network(self, enable_public_network):
        """Sets the enable_public_network of this NetworkTypeForGetGatewayOutput.


        :param enable_public_network: The enable_public_network of this NetworkTypeForGetGatewayOutput.  # noqa: E501
        :type: bool
        """

        self._enable_public_network = enable_public_network

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkTypeForGetGatewayOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkTypeForGetGatewayOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkTypeForGetGatewayOutput):
            return True

        return self.to_dict() != other.to_dict()
