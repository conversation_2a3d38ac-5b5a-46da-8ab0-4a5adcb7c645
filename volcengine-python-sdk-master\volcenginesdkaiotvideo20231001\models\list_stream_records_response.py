# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListStreamRecordsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'path': 'str',
        'record': 'RecordForListStreamRecordsOutput',
        'record_meta': 'RecordMetaForListStreamRecordsOutput',
        'type': 'str'
    }

    attribute_map = {
        'path': 'Path',
        'record': 'Record',
        'record_meta': 'RecordMeta',
        'type': 'Type'
    }

    def __init__(self, path=None, record=None, record_meta=None, type=None, _configuration=None):  # noqa: E501
        """ListStreamRecordsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._path = None
        self._record = None
        self._record_meta = None
        self._type = None
        self.discriminator = None

        if path is not None:
            self.path = path
        if record is not None:
            self.record = record
        if record_meta is not None:
            self.record_meta = record_meta
        if type is not None:
            self.type = type

    @property
    def path(self):
        """Gets the path of this ListStreamRecordsResponse.  # noqa: E501


        :return: The path of this ListStreamRecordsResponse.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this ListStreamRecordsResponse.


        :param path: The path of this ListStreamRecordsResponse.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def record(self):
        """Gets the record of this ListStreamRecordsResponse.  # noqa: E501


        :return: The record of this ListStreamRecordsResponse.  # noqa: E501
        :rtype: RecordForListStreamRecordsOutput
        """
        return self._record

    @record.setter
    def record(self, record):
        """Sets the record of this ListStreamRecordsResponse.


        :param record: The record of this ListStreamRecordsResponse.  # noqa: E501
        :type: RecordForListStreamRecordsOutput
        """

        self._record = record

    @property
    def record_meta(self):
        """Gets the record_meta of this ListStreamRecordsResponse.  # noqa: E501


        :return: The record_meta of this ListStreamRecordsResponse.  # noqa: E501
        :rtype: RecordMetaForListStreamRecordsOutput
        """
        return self._record_meta

    @record_meta.setter
    def record_meta(self, record_meta):
        """Sets the record_meta of this ListStreamRecordsResponse.


        :param record_meta: The record_meta of this ListStreamRecordsResponse.  # noqa: E501
        :type: RecordMetaForListStreamRecordsOutput
        """

        self._record_meta = record_meta

    @property
    def type(self):
        """Gets the type of this ListStreamRecordsResponse.  # noqa: E501


        :return: The type of this ListStreamRecordsResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ListStreamRecordsResponse.


        :param type: The type of this ListStreamRecordsResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListStreamRecordsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListStreamRecordsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListStreamRecordsResponse):
            return True

        return self.to_dict() != other.to_dict()
