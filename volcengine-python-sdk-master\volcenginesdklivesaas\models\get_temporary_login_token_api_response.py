# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTemporaryLoginTokenAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'expire_time': 'int',
        'secret_key': 'str',
        'tmp_secret_key': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'expire_time': 'ExpireTime',
        'secret_key': 'SecretKey',
        'tmp_secret_key': 'TmpSecretKey'
    }

    def __init__(self, activity_id=None, expire_time=None, secret_key=None, tmp_secret_key=None, _configuration=None):  # noqa: E501
        """GetTemporaryLoginTokenAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._expire_time = None
        self._secret_key = None
        self._tmp_secret_key = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if expire_time is not None:
            self.expire_time = expire_time
        if secret_key is not None:
            self.secret_key = secret_key
        if tmp_secret_key is not None:
            self.tmp_secret_key = tmp_secret_key

    @property
    def activity_id(self):
        """Gets the activity_id of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501


        :return: The activity_id of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetTemporaryLoginTokenAPIResponse.


        :param activity_id: The activity_id of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def expire_time(self):
        """Gets the expire_time of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501


        :return: The expire_time of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this GetTemporaryLoginTokenAPIResponse.


        :param expire_time: The expire_time of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def secret_key(self):
        """Gets the secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501


        :return: The secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._secret_key

    @secret_key.setter
    def secret_key(self, secret_key):
        """Sets the secret_key of this GetTemporaryLoginTokenAPIResponse.


        :param secret_key: The secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :type: str
        """

        self._secret_key = secret_key

    @property
    def tmp_secret_key(self):
        """Gets the tmp_secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501


        :return: The tmp_secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._tmp_secret_key

    @tmp_secret_key.setter
    def tmp_secret_key(self, tmp_secret_key):
        """Sets the tmp_secret_key of this GetTemporaryLoginTokenAPIResponse.


        :param tmp_secret_key: The tmp_secret_key of this GetTemporaryLoginTokenAPIResponse.  # noqa: E501
        :type: str
        """

        self._tmp_secret_key = tmp_secret_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTemporaryLoginTokenAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTemporaryLoginTokenAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTemporaryLoginTokenAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
