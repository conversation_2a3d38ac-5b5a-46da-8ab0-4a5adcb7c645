# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InfectStatusForListVulByPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ignore': 'int',
        'processed': 'int',
        'un_processed': 'int'
    }

    attribute_map = {
        'ignore': 'Ignore',
        'processed': 'Processed',
        'un_processed': 'UnProcessed'
    }

    def __init__(self, ignore=None, processed=None, un_processed=None, _configuration=None):  # noqa: E501
        """InfectStatusForListVulByPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ignore = None
        self._processed = None
        self._un_processed = None
        self.discriminator = None

        if ignore is not None:
            self.ignore = ignore
        if processed is not None:
            self.processed = processed
        if un_processed is not None:
            self.un_processed = un_processed

    @property
    def ignore(self):
        """Gets the ignore of this InfectStatusForListVulByPodOutput.  # noqa: E501


        :return: The ignore of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._ignore

    @ignore.setter
    def ignore(self, ignore):
        """Sets the ignore of this InfectStatusForListVulByPodOutput.


        :param ignore: The ignore of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._ignore = ignore

    @property
    def processed(self):
        """Gets the processed of this InfectStatusForListVulByPodOutput.  # noqa: E501


        :return: The processed of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._processed

    @processed.setter
    def processed(self, processed):
        """Sets the processed of this InfectStatusForListVulByPodOutput.


        :param processed: The processed of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._processed = processed

    @property
    def un_processed(self):
        """Gets the un_processed of this InfectStatusForListVulByPodOutput.  # noqa: E501


        :return: The un_processed of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._un_processed

    @un_processed.setter
    def un_processed(self, un_processed):
        """Sets the un_processed of this InfectStatusForListVulByPodOutput.


        :param un_processed: The un_processed of this InfectStatusForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._un_processed = un_processed

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InfectStatusForListVulByPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InfectStatusForListVulByPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InfectStatusForListVulByPodOutput):
            return True

        return self.to_dict() != other.to_dict()
