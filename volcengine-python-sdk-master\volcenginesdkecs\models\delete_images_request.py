# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteImagesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_binded_snapshots': 'bool',
        'image_ids': 'list[str]'
    }

    attribute_map = {
        'delete_binded_snapshots': 'DeleteBindedSnapshots',
        'image_ids': 'ImageIds'
    }

    def __init__(self, delete_binded_snapshots=None, image_ids=None, _configuration=None):  # noqa: E501
        """DeleteImagesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_binded_snapshots = None
        self._image_ids = None
        self.discriminator = None

        if delete_binded_snapshots is not None:
            self.delete_binded_snapshots = delete_binded_snapshots
        if image_ids is not None:
            self.image_ids = image_ids

    @property
    def delete_binded_snapshots(self):
        """Gets the delete_binded_snapshots of this DeleteImagesRequest.  # noqa: E501


        :return: The delete_binded_snapshots of this DeleteImagesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_binded_snapshots

    @delete_binded_snapshots.setter
    def delete_binded_snapshots(self, delete_binded_snapshots):
        """Sets the delete_binded_snapshots of this DeleteImagesRequest.


        :param delete_binded_snapshots: The delete_binded_snapshots of this DeleteImagesRequest.  # noqa: E501
        :type: bool
        """

        self._delete_binded_snapshots = delete_binded_snapshots

    @property
    def image_ids(self):
        """Gets the image_ids of this DeleteImagesRequest.  # noqa: E501


        :return: The image_ids of this DeleteImagesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._image_ids

    @image_ids.setter
    def image_ids(self, image_ids):
        """Sets the image_ids of this DeleteImagesRequest.


        :param image_ids: The image_ids of this DeleteImagesRequest.  # noqa: E501
        :type: list[str]
        """

        self._image_ids = image_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteImagesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteImagesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteImagesRequest):
            return True

        return self.to_dict() != other.to_dict()
