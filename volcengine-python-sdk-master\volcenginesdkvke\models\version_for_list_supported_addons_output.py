# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VersionForListSupportedAddonsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'compatibilities': 'list[CompatibilityForListSupportedAddonsOutput]',
        'compatible_versions': 'list[str]',
        'version': 'str'
    }

    attribute_map = {
        'compatibilities': 'Compatibilities',
        'compatible_versions': 'CompatibleVersions',
        'version': 'Version'
    }

    def __init__(self, compatibilities=None, compatible_versions=None, version=None, _configuration=None):  # noqa: E501
        """VersionForListSupportedAddonsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._compatibilities = None
        self._compatible_versions = None
        self._version = None
        self.discriminator = None

        if compatibilities is not None:
            self.compatibilities = compatibilities
        if compatible_versions is not None:
            self.compatible_versions = compatible_versions
        if version is not None:
            self.version = version

    @property
    def compatibilities(self):
        """Gets the compatibilities of this VersionForListSupportedAddonsOutput.  # noqa: E501


        :return: The compatibilities of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :rtype: list[CompatibilityForListSupportedAddonsOutput]
        """
        return self._compatibilities

    @compatibilities.setter
    def compatibilities(self, compatibilities):
        """Sets the compatibilities of this VersionForListSupportedAddonsOutput.


        :param compatibilities: The compatibilities of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :type: list[CompatibilityForListSupportedAddonsOutput]
        """

        self._compatibilities = compatibilities

    @property
    def compatible_versions(self):
        """Gets the compatible_versions of this VersionForListSupportedAddonsOutput.  # noqa: E501


        :return: The compatible_versions of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._compatible_versions

    @compatible_versions.setter
    def compatible_versions(self, compatible_versions):
        """Sets the compatible_versions of this VersionForListSupportedAddonsOutput.


        :param compatible_versions: The compatible_versions of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :type: list[str]
        """

        self._compatible_versions = compatible_versions

    @property
    def version(self):
        """Gets the version of this VersionForListSupportedAddonsOutput.  # noqa: E501


        :return: The version of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this VersionForListSupportedAddonsOutput.


        :param version: The version of this VersionForListSupportedAddonsOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VersionForListSupportedAddonsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VersionForListSupportedAddonsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VersionForListSupportedAddonsOutput):
            return True

        return self.to_dict() != other.to_dict()
