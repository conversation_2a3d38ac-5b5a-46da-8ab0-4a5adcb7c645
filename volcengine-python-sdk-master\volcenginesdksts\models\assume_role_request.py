# coding: utf-8

"""
    sts

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssumeRoleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'duration_seconds': 'int',
        'policy': 'str',
        'role_session_name': 'str',
        'role_trn': 'str',
        'tags': 'list[TagForAssumeRoleInput]'
    }

    attribute_map = {
        'duration_seconds': 'DurationSeconds',
        'policy': 'Policy',
        'role_session_name': 'RoleSessionName',
        'role_trn': 'RoleTrn',
        'tags': 'Tags'
    }

    def __init__(self, duration_seconds=None, policy=None, role_session_name=None, role_trn=None, tags=None, _configuration=None):  # noqa: E501
        """AssumeRoleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._duration_seconds = None
        self._policy = None
        self._role_session_name = None
        self._role_trn = None
        self._tags = None
        self.discriminator = None

        if duration_seconds is not None:
            self.duration_seconds = duration_seconds
        if policy is not None:
            self.policy = policy
        self.role_session_name = role_session_name
        self.role_trn = role_trn
        if tags is not None:
            self.tags = tags

    @property
    def duration_seconds(self):
        """Gets the duration_seconds of this AssumeRoleRequest.  # noqa: E501


        :return: The duration_seconds of this AssumeRoleRequest.  # noqa: E501
        :rtype: int
        """
        return self._duration_seconds

    @duration_seconds.setter
    def duration_seconds(self, duration_seconds):
        """Sets the duration_seconds of this AssumeRoleRequest.


        :param duration_seconds: The duration_seconds of this AssumeRoleRequest.  # noqa: E501
        :type: int
        """

        self._duration_seconds = duration_seconds

    @property
    def policy(self):
        """Gets the policy of this AssumeRoleRequest.  # noqa: E501


        :return: The policy of this AssumeRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this AssumeRoleRequest.


        :param policy: The policy of this AssumeRoleRequest.  # noqa: E501
        :type: str
        """

        self._policy = policy

    @property
    def role_session_name(self):
        """Gets the role_session_name of this AssumeRoleRequest.  # noqa: E501


        :return: The role_session_name of this AssumeRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_session_name

    @role_session_name.setter
    def role_session_name(self, role_session_name):
        """Sets the role_session_name of this AssumeRoleRequest.


        :param role_session_name: The role_session_name of this AssumeRoleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and role_session_name is None:
            raise ValueError("Invalid value for `role_session_name`, must not be `None`")  # noqa: E501

        self._role_session_name = role_session_name

    @property
    def role_trn(self):
        """Gets the role_trn of this AssumeRoleRequest.  # noqa: E501


        :return: The role_trn of this AssumeRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_trn

    @role_trn.setter
    def role_trn(self, role_trn):
        """Sets the role_trn of this AssumeRoleRequest.


        :param role_trn: The role_trn of this AssumeRoleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and role_trn is None:
            raise ValueError("Invalid value for `role_trn`, must not be `None`")  # noqa: E501

        self._role_trn = role_trn

    @property
    def tags(self):
        """Gets the tags of this AssumeRoleRequest.  # noqa: E501


        :return: The tags of this AssumeRoleRequest.  # noqa: E501
        :rtype: list[TagForAssumeRoleInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this AssumeRoleRequest.


        :param tags: The tags of this AssumeRoleRequest.  # noqa: E501
        :type: list[TagForAssumeRoleInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssumeRoleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssumeRoleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssumeRoleRequest):
            return True

        return self.to_dict() != other.to_dict()
