# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class URLSetForUploadMediaByURLInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'folder_id': 'int',
        'name': 'str',
        'source_url': 'str'
    }

    attribute_map = {
        'folder_id': 'FolderId',
        'name': 'Name',
        'source_url': 'SourceUrl'
    }

    def __init__(self, folder_id=None, name=None, source_url=None, _configuration=None):  # noqa: E501
        """URLSetForUploadMediaByURLInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._folder_id = None
        self._name = None
        self._source_url = None
        self.discriminator = None

        if folder_id is not None:
            self.folder_id = folder_id
        if name is not None:
            self.name = name
        if source_url is not None:
            self.source_url = source_url

    @property
    def folder_id(self):
        """Gets the folder_id of this URLSetForUploadMediaByURLInput.  # noqa: E501


        :return: The folder_id of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :rtype: int
        """
        return self._folder_id

    @folder_id.setter
    def folder_id(self, folder_id):
        """Sets the folder_id of this URLSetForUploadMediaByURLInput.


        :param folder_id: The folder_id of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :type: int
        """

        self._folder_id = folder_id

    @property
    def name(self):
        """Gets the name of this URLSetForUploadMediaByURLInput.  # noqa: E501


        :return: The name of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this URLSetForUploadMediaByURLInput.


        :param name: The name of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def source_url(self):
        """Gets the source_url of this URLSetForUploadMediaByURLInput.  # noqa: E501


        :return: The source_url of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :rtype: str
        """
        return self._source_url

    @source_url.setter
    def source_url(self, source_url):
        """Sets the source_url of this URLSetForUploadMediaByURLInput.


        :param source_url: The source_url of this URLSetForUploadMediaByURLInput.  # noqa: E501
        :type: str
        """

        self._source_url = source_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(URLSetForUploadMediaByURLInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, URLSetForUploadMediaByURLInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, URLSetForUploadMediaByURLInput):
            return True

        return self.to_dict() != other.to_dict()
