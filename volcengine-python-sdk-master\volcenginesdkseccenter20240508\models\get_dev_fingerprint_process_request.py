# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDevFingerprintProcessRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'asset_ip': 'str',
        'asset_name': 'str',
        'checksum': 'str',
        'cmdline': 'str',
        'comm': 'str',
        'common': 'bool',
        'exe': 'str',
        'integrity': 'bool',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'start_time_end': 'int',
        'start_time_start': 'int',
        'username': 'str'
    }

    attribute_map = {
        'asset_id': 'AssetID',
        'asset_ip': 'AssetIP',
        'asset_name': 'AssetName',
        'checksum': 'Checksum',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'common': 'Common',
        'exe': 'Exe',
        'integrity': 'Integrity',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'start_time_end': 'StartTimeEnd',
        'start_time_start': 'StartTimeStart',
        'username': 'Username'
    }

    def __init__(self, asset_id=None, asset_ip=None, asset_name=None, checksum=None, cmdline=None, comm=None, common=None, exe=None, integrity=None, page_number=None, page_size=None, sort_by=None, sort_order=None, start_time_end=None, start_time_start=None, username=None, _configuration=None):  # noqa: E501
        """GetDevFingerprintProcessRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._asset_ip = None
        self._asset_name = None
        self._checksum = None
        self._cmdline = None
        self._comm = None
        self._common = None
        self._exe = None
        self._integrity = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._start_time_end = None
        self._start_time_start = None
        self._username = None
        self.discriminator = None

        if asset_id is not None:
            self.asset_id = asset_id
        if asset_ip is not None:
            self.asset_ip = asset_ip
        if asset_name is not None:
            self.asset_name = asset_name
        if checksum is not None:
            self.checksum = checksum
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if common is not None:
            self.common = common
        if exe is not None:
            self.exe = exe
        if integrity is not None:
            self.integrity = integrity
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if start_time_end is not None:
            self.start_time_end = start_time_end
        if start_time_start is not None:
            self.start_time_start = start_time_start
        if username is not None:
            self.username = username

    @property
    def asset_id(self):
        """Gets the asset_id of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The asset_id of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this GetDevFingerprintProcessRequest.


        :param asset_id: The asset_id of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_ip(self):
        """Gets the asset_ip of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The asset_ip of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_ip

    @asset_ip.setter
    def asset_ip(self, asset_ip):
        """Sets the asset_ip of this GetDevFingerprintProcessRequest.


        :param asset_ip: The asset_ip of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._asset_ip = asset_ip

    @property
    def asset_name(self):
        """Gets the asset_name of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The asset_name of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this GetDevFingerprintProcessRequest.


        :param asset_name: The asset_name of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def checksum(self):
        """Gets the checksum of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The checksum of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._checksum

    @checksum.setter
    def checksum(self, checksum):
        """Sets the checksum of this GetDevFingerprintProcessRequest.


        :param checksum: The checksum of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._checksum = checksum

    @property
    def cmdline(self):
        """Gets the cmdline of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The cmdline of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this GetDevFingerprintProcessRequest.


        :param cmdline: The cmdline of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The comm of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this GetDevFingerprintProcessRequest.


        :param comm: The comm of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def common(self):
        """Gets the common of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The common of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: bool
        """
        return self._common

    @common.setter
    def common(self, common):
        """Sets the common of this GetDevFingerprintProcessRequest.


        :param common: The common of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: bool
        """

        self._common = common

    @property
    def exe(self):
        """Gets the exe of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The exe of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this GetDevFingerprintProcessRequest.


        :param exe: The exe of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def integrity(self):
        """Gets the integrity of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The integrity of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: bool
        """
        return self._integrity

    @integrity.setter
    def integrity(self, integrity):
        """Sets the integrity of this GetDevFingerprintProcessRequest.


        :param integrity: The integrity of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: bool
        """

        self._integrity = integrity

    @property
    def page_number(self):
        """Gets the page_number of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The page_number of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetDevFingerprintProcessRequest.


        :param page_number: The page_number of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The page_size of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetDevFingerprintProcessRequest.


        :param page_size: The page_size of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The sort_by of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this GetDevFingerprintProcessRequest.


        :param sort_by: The sort_by of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The sort_order of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this GetDevFingerprintProcessRequest.


        :param sort_order: The sort_order of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Asc", " Desc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def start_time_end(self):
        """Gets the start_time_end of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The start_time_end of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time_end

    @start_time_end.setter
    def start_time_end(self, start_time_end):
        """Sets the start_time_end of this GetDevFingerprintProcessRequest.


        :param start_time_end: The start_time_end of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: int
        """

        self._start_time_end = start_time_end

    @property
    def start_time_start(self):
        """Gets the start_time_start of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The start_time_start of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time_start

    @start_time_start.setter
    def start_time_start(self, start_time_start):
        """Sets the start_time_start of this GetDevFingerprintProcessRequest.


        :param start_time_start: The start_time_start of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: int
        """

        self._start_time_start = start_time_start

    @property
    def username(self):
        """Gets the username of this GetDevFingerprintProcessRequest.  # noqa: E501


        :return: The username of this GetDevFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this GetDevFingerprintProcessRequest.


        :param username: The username of this GetDevFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDevFingerprintProcessRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDevFingerprintProcessRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDevFingerprintProcessRequest):
            return True

        return self.to_dict() != other.to_dict()
