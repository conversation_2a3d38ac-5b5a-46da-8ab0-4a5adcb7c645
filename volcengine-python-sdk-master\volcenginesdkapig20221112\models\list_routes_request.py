# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRoutesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter': 'FilterForListRoutesInput',
        'gateway_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'resource_type': 'str',
        'service_id': 'str',
        'upstream_id': 'str',
        'upstream_version': 'str'
    }

    attribute_map = {
        'filter': 'Filter',
        'gateway_id': 'GatewayId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'resource_type': 'ResourceType',
        'service_id': 'ServiceId',
        'upstream_id': 'UpstreamId',
        'upstream_version': 'UpstreamVersion'
    }

    def __init__(self, filter=None, gateway_id=None, page_number=None, page_size=None, resource_type=None, service_id=None, upstream_id=None, upstream_version=None, _configuration=None):  # noqa: E501
        """ListRoutesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter = None
        self._gateway_id = None
        self._page_number = None
        self._page_size = None
        self._resource_type = None
        self._service_id = None
        self._upstream_id = None
        self._upstream_version = None
        self.discriminator = None

        if filter is not None:
            self.filter = filter
        if gateway_id is not None:
            self.gateway_id = gateway_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if resource_type is not None:
            self.resource_type = resource_type
        if service_id is not None:
            self.service_id = service_id
        if upstream_id is not None:
            self.upstream_id = upstream_id
        if upstream_version is not None:
            self.upstream_version = upstream_version

    @property
    def filter(self):
        """Gets the filter of this ListRoutesRequest.  # noqa: E501


        :return: The filter of this ListRoutesRequest.  # noqa: E501
        :rtype: FilterForListRoutesInput
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this ListRoutesRequest.


        :param filter: The filter of this ListRoutesRequest.  # noqa: E501
        :type: FilterForListRoutesInput
        """

        self._filter = filter

    @property
    def gateway_id(self):
        """Gets the gateway_id of this ListRoutesRequest.  # noqa: E501


        :return: The gateway_id of this ListRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this ListRoutesRequest.


        :param gateway_id: The gateway_id of this ListRoutesRequest.  # noqa: E501
        :type: str
        """

        self._gateway_id = gateway_id

    @property
    def page_number(self):
        """Gets the page_number of this ListRoutesRequest.  # noqa: E501


        :return: The page_number of this ListRoutesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRoutesRequest.


        :param page_number: The page_number of this ListRoutesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRoutesRequest.  # noqa: E501


        :return: The page_size of this ListRoutesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRoutesRequest.


        :param page_size: The page_size of this ListRoutesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def resource_type(self):
        """Gets the resource_type of this ListRoutesRequest.  # noqa: E501


        :return: The resource_type of this ListRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListRoutesRequest.


        :param resource_type: The resource_type of this ListRoutesRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_id(self):
        """Gets the service_id of this ListRoutesRequest.  # noqa: E501


        :return: The service_id of this ListRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this ListRoutesRequest.


        :param service_id: The service_id of this ListRoutesRequest.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def upstream_id(self):
        """Gets the upstream_id of this ListRoutesRequest.  # noqa: E501


        :return: The upstream_id of this ListRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._upstream_id

    @upstream_id.setter
    def upstream_id(self, upstream_id):
        """Sets the upstream_id of this ListRoutesRequest.


        :param upstream_id: The upstream_id of this ListRoutesRequest.  # noqa: E501
        :type: str
        """

        self._upstream_id = upstream_id

    @property
    def upstream_version(self):
        """Gets the upstream_version of this ListRoutesRequest.  # noqa: E501


        :return: The upstream_version of this ListRoutesRequest.  # noqa: E501
        :rtype: str
        """
        return self._upstream_version

    @upstream_version.setter
    def upstream_version(self, upstream_version):
        """Sets the upstream_version of this ListRoutesRequest.


        :param upstream_version: The upstream_version of this ListRoutesRequest.  # noqa: E501
        :type: str
        """

        self._upstream_version = upstream_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRoutesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRoutesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRoutesRequest):
            return True

        return self.to_dict() != other.to_dict()
