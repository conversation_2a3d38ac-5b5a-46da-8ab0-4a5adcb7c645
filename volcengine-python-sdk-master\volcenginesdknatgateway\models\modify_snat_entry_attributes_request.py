# coding: utf-8

"""
    natgateway

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifySnatEntryAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eip_id': 'str',
        'snat_entry_id': 'str',
        'snat_entry_name': 'str'
    }

    attribute_map = {
        'eip_id': 'EipId',
        'snat_entry_id': 'SnatEntryId',
        'snat_entry_name': 'SnatEntryName'
    }

    def __init__(self, eip_id=None, snat_entry_id=None, snat_entry_name=None, _configuration=None):  # noqa: E501
        """ModifySnatEntryAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eip_id = None
        self._snat_entry_id = None
        self._snat_entry_name = None
        self.discriminator = None

        if eip_id is not None:
            self.eip_id = eip_id
        self.snat_entry_id = snat_entry_id
        if snat_entry_name is not None:
            self.snat_entry_name = snat_entry_name

    @property
    def eip_id(self):
        """Gets the eip_id of this ModifySnatEntryAttributesRequest.  # noqa: E501


        :return: The eip_id of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._eip_id

    @eip_id.setter
    def eip_id(self, eip_id):
        """Sets the eip_id of this ModifySnatEntryAttributesRequest.


        :param eip_id: The eip_id of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._eip_id = eip_id

    @property
    def snat_entry_id(self):
        """Gets the snat_entry_id of this ModifySnatEntryAttributesRequest.  # noqa: E501


        :return: The snat_entry_id of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._snat_entry_id

    @snat_entry_id.setter
    def snat_entry_id(self, snat_entry_id):
        """Sets the snat_entry_id of this ModifySnatEntryAttributesRequest.


        :param snat_entry_id: The snat_entry_id of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and snat_entry_id is None:
            raise ValueError("Invalid value for `snat_entry_id`, must not be `None`")  # noqa: E501

        self._snat_entry_id = snat_entry_id

    @property
    def snat_entry_name(self):
        """Gets the snat_entry_name of this ModifySnatEntryAttributesRequest.  # noqa: E501


        :return: The snat_entry_name of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._snat_entry_name

    @snat_entry_name.setter
    def snat_entry_name(self, snat_entry_name):
        """Sets the snat_entry_name of this ModifySnatEntryAttributesRequest.


        :param snat_entry_name: The snat_entry_name of this ModifySnatEntryAttributesRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                snat_entry_name is not None and len(snat_entry_name) > 128):
            raise ValueError("Invalid value for `snat_entry_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                snat_entry_name is not None and len(snat_entry_name) < 1):
            raise ValueError("Invalid value for `snat_entry_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._snat_entry_name = snat_entry_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifySnatEntryAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifySnatEntryAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifySnatEntryAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
