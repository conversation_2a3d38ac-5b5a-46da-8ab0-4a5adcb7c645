# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetParametersForImportResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'import_token': 'str',
        'key_id': 'str',
        'keyring_id': 'str',
        'public_key': 'str',
        'token_expire_time': 'str'
    }

    attribute_map = {
        'import_token': 'ImportToken',
        'key_id': 'KeyID',
        'keyring_id': 'KeyringID',
        'public_key': 'PublicKey',
        'token_expire_time': 'TokenExpireTime'
    }

    def __init__(self, import_token=None, key_id=None, keyring_id=None, public_key=None, token_expire_time=None, _configuration=None):  # noqa: E501
        """GetParametersForImportResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._import_token = None
        self._key_id = None
        self._keyring_id = None
        self._public_key = None
        self._token_expire_time = None
        self.discriminator = None

        if import_token is not None:
            self.import_token = import_token
        if key_id is not None:
            self.key_id = key_id
        if keyring_id is not None:
            self.keyring_id = keyring_id
        if public_key is not None:
            self.public_key = public_key
        if token_expire_time is not None:
            self.token_expire_time = token_expire_time

    @property
    def import_token(self):
        """Gets the import_token of this GetParametersForImportResponse.  # noqa: E501


        :return: The import_token of this GetParametersForImportResponse.  # noqa: E501
        :rtype: str
        """
        return self._import_token

    @import_token.setter
    def import_token(self, import_token):
        """Sets the import_token of this GetParametersForImportResponse.


        :param import_token: The import_token of this GetParametersForImportResponse.  # noqa: E501
        :type: str
        """

        self._import_token = import_token

    @property
    def key_id(self):
        """Gets the key_id of this GetParametersForImportResponse.  # noqa: E501


        :return: The key_id of this GetParametersForImportResponse.  # noqa: E501
        :rtype: str
        """
        return self._key_id

    @key_id.setter
    def key_id(self, key_id):
        """Sets the key_id of this GetParametersForImportResponse.


        :param key_id: The key_id of this GetParametersForImportResponse.  # noqa: E501
        :type: str
        """

        self._key_id = key_id

    @property
    def keyring_id(self):
        """Gets the keyring_id of this GetParametersForImportResponse.  # noqa: E501


        :return: The keyring_id of this GetParametersForImportResponse.  # noqa: E501
        :rtype: str
        """
        return self._keyring_id

    @keyring_id.setter
    def keyring_id(self, keyring_id):
        """Sets the keyring_id of this GetParametersForImportResponse.


        :param keyring_id: The keyring_id of this GetParametersForImportResponse.  # noqa: E501
        :type: str
        """

        self._keyring_id = keyring_id

    @property
    def public_key(self):
        """Gets the public_key of this GetParametersForImportResponse.  # noqa: E501


        :return: The public_key of this GetParametersForImportResponse.  # noqa: E501
        :rtype: str
        """
        return self._public_key

    @public_key.setter
    def public_key(self, public_key):
        """Sets the public_key of this GetParametersForImportResponse.


        :param public_key: The public_key of this GetParametersForImportResponse.  # noqa: E501
        :type: str
        """

        self._public_key = public_key

    @property
    def token_expire_time(self):
        """Gets the token_expire_time of this GetParametersForImportResponse.  # noqa: E501


        :return: The token_expire_time of this GetParametersForImportResponse.  # noqa: E501
        :rtype: str
        """
        return self._token_expire_time

    @token_expire_time.setter
    def token_expire_time(self, token_expire_time):
        """Sets the token_expire_time of this GetParametersForImportResponse.


        :param token_expire_time: The token_expire_time of this GetParametersForImportResponse.  # noqa: E501
        :type: str
        """

        self._token_expire_time = token_expire_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetParametersForImportResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetParametersForImportResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetParametersForImportResponse):
            return True

        return self.to_dict() != other.to_dict()
