# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListAssetClustersInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'external_cluster_id': 'str',
        'has_audit_risk_list': 'list[bool]',
        'has_cve_risk': 'bool',
        'has_cve_risk_list': 'list[bool]',
        'name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'external_cluster_id': 'ExternalClusterId',
        'has_audit_risk_list': 'HasAuditRiskList',
        'has_cve_risk': 'HasCveRisk',
        'has_cve_risk_list': 'HasCveRiskList',
        'name': 'Name',
        'status': 'Status'
    }

    def __init__(self, external_cluster_id=None, has_audit_risk_list=None, has_cve_risk=None, has_cve_risk_list=None, name=None, status=None, _configuration=None):  # noqa: E501
        """FilterForListAssetClustersInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._external_cluster_id = None
        self._has_audit_risk_list = None
        self._has_cve_risk = None
        self._has_cve_risk_list = None
        self._name = None
        self._status = None
        self.discriminator = None

        if external_cluster_id is not None:
            self.external_cluster_id = external_cluster_id
        if has_audit_risk_list is not None:
            self.has_audit_risk_list = has_audit_risk_list
        if has_cve_risk is not None:
            self.has_cve_risk = has_cve_risk
        if has_cve_risk_list is not None:
            self.has_cve_risk_list = has_cve_risk_list
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status

    @property
    def external_cluster_id(self):
        """Gets the external_cluster_id of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The external_cluster_id of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: str
        """
        return self._external_cluster_id

    @external_cluster_id.setter
    def external_cluster_id(self, external_cluster_id):
        """Sets the external_cluster_id of this FilterForListAssetClustersInput.


        :param external_cluster_id: The external_cluster_id of this FilterForListAssetClustersInput.  # noqa: E501
        :type: str
        """

        self._external_cluster_id = external_cluster_id

    @property
    def has_audit_risk_list(self):
        """Gets the has_audit_risk_list of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The has_audit_risk_list of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: list[bool]
        """
        return self._has_audit_risk_list

    @has_audit_risk_list.setter
    def has_audit_risk_list(self, has_audit_risk_list):
        """Sets the has_audit_risk_list of this FilterForListAssetClustersInput.


        :param has_audit_risk_list: The has_audit_risk_list of this FilterForListAssetClustersInput.  # noqa: E501
        :type: list[bool]
        """

        self._has_audit_risk_list = has_audit_risk_list

    @property
    def has_cve_risk(self):
        """Gets the has_cve_risk of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The has_cve_risk of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: bool
        """
        return self._has_cve_risk

    @has_cve_risk.setter
    def has_cve_risk(self, has_cve_risk):
        """Sets the has_cve_risk of this FilterForListAssetClustersInput.


        :param has_cve_risk: The has_cve_risk of this FilterForListAssetClustersInput.  # noqa: E501
        :type: bool
        """

        self._has_cve_risk = has_cve_risk

    @property
    def has_cve_risk_list(self):
        """Gets the has_cve_risk_list of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The has_cve_risk_list of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: list[bool]
        """
        return self._has_cve_risk_list

    @has_cve_risk_list.setter
    def has_cve_risk_list(self, has_cve_risk_list):
        """Sets the has_cve_risk_list of this FilterForListAssetClustersInput.


        :param has_cve_risk_list: The has_cve_risk_list of this FilterForListAssetClustersInput.  # noqa: E501
        :type: list[bool]
        """

        self._has_cve_risk_list = has_cve_risk_list

    @property
    def name(self):
        """Gets the name of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The name of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListAssetClustersInput.


        :param name: The name of this FilterForListAssetClustersInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this FilterForListAssetClustersInput.  # noqa: E501


        :return: The status of this FilterForListAssetClustersInput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListAssetClustersInput.


        :param status: The status of this FilterForListAssetClustersInput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListAssetClustersInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListAssetClustersInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListAssetClustersInput):
            return True

        return self.to_dict() != other.to_dict()
