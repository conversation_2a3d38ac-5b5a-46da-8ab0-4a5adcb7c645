# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CouponForCreateCouponsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'owner_sub_account_name': 'str',
        'pic': 'str',
        'third_party_id': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'owner_sub_account_name': 'OwnerSubAccountName',
        'pic': 'Pic',
        'third_party_id': 'ThirdPartyId'
    }

    def __init__(self, description=None, name=None, owner_sub_account_name=None, pic=None, third_party_id=None, _configuration=None):  # noqa: E501
        """CouponForCreateCouponsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._owner_sub_account_name = None
        self._pic = None
        self._third_party_id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if name is not None:
            self.name = name
        if owner_sub_account_name is not None:
            self.owner_sub_account_name = owner_sub_account_name
        if pic is not None:
            self.pic = pic
        if third_party_id is not None:
            self.third_party_id = third_party_id

    @property
    def description(self):
        """Gets the description of this CouponForCreateCouponsInput.  # noqa: E501


        :return: The description of this CouponForCreateCouponsInput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CouponForCreateCouponsInput.


        :param description: The description of this CouponForCreateCouponsInput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this CouponForCreateCouponsInput.  # noqa: E501


        :return: The name of this CouponForCreateCouponsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CouponForCreateCouponsInput.


        :param name: The name of this CouponForCreateCouponsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner_sub_account_name(self):
        """Gets the owner_sub_account_name of this CouponForCreateCouponsInput.  # noqa: E501


        :return: The owner_sub_account_name of this CouponForCreateCouponsInput.  # noqa: E501
        :rtype: str
        """
        return self._owner_sub_account_name

    @owner_sub_account_name.setter
    def owner_sub_account_name(self, owner_sub_account_name):
        """Sets the owner_sub_account_name of this CouponForCreateCouponsInput.


        :param owner_sub_account_name: The owner_sub_account_name of this CouponForCreateCouponsInput.  # noqa: E501
        :type: str
        """

        self._owner_sub_account_name = owner_sub_account_name

    @property
    def pic(self):
        """Gets the pic of this CouponForCreateCouponsInput.  # noqa: E501


        :return: The pic of this CouponForCreateCouponsInput.  # noqa: E501
        :rtype: str
        """
        return self._pic

    @pic.setter
    def pic(self, pic):
        """Sets the pic of this CouponForCreateCouponsInput.


        :param pic: The pic of this CouponForCreateCouponsInput.  # noqa: E501
        :type: str
        """

        self._pic = pic

    @property
    def third_party_id(self):
        """Gets the third_party_id of this CouponForCreateCouponsInput.  # noqa: E501


        :return: The third_party_id of this CouponForCreateCouponsInput.  # noqa: E501
        :rtype: str
        """
        return self._third_party_id

    @third_party_id.setter
    def third_party_id(self, third_party_id):
        """Sets the third_party_id of this CouponForCreateCouponsInput.


        :param third_party_id: The third_party_id of this CouponForCreateCouponsInput.  # noqa: E501
        :type: str
        """

        self._third_party_id = third_party_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CouponForCreateCouponsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CouponForCreateCouponsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CouponForCreateCouponsInput):
            return True

        return self.to_dict() != other.to_dict()
