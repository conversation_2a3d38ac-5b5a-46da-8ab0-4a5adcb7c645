# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopicsInfoForDescribeTopicsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'description': 'str',
        'message_type': 'int',
        'queue_number': 'int',
        'topic_name': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'message_type': 'MessageType',
        'queue_number': 'QueueNumber',
        'topic_name': 'TopicName'
    }

    def __init__(self, create_time=None, description=None, message_type=None, queue_number=None, topic_name=None, _configuration=None):  # noqa: E501
        """TopicsInfoForDescribeTopicsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._message_type = None
        self._queue_number = None
        self._topic_name = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if message_type is not None:
            self.message_type = message_type
        if queue_number is not None:
            self.queue_number = queue_number
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def create_time(self):
        """Gets the create_time of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501


        :return: The create_time of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this TopicsInfoForDescribeTopicsOutput.


        :param create_time: The create_time of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501


        :return: The description of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TopicsInfoForDescribeTopicsOutput.


        :param description: The description of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def message_type(self):
        """Gets the message_type of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501


        :return: The message_type of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_type

    @message_type.setter
    def message_type(self, message_type):
        """Sets the message_type of this TopicsInfoForDescribeTopicsOutput.


        :param message_type: The message_type of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :type: int
        """

        self._message_type = message_type

    @property
    def queue_number(self):
        """Gets the queue_number of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501


        :return: The queue_number of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :rtype: int
        """
        return self._queue_number

    @queue_number.setter
    def queue_number(self, queue_number):
        """Sets the queue_number of this TopicsInfoForDescribeTopicsOutput.


        :param queue_number: The queue_number of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :type: int
        """

        self._queue_number = queue_number

    @property
    def topic_name(self):
        """Gets the topic_name of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501


        :return: The topic_name of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this TopicsInfoForDescribeTopicsOutput.


        :param topic_name: The topic_name of this TopicsInfoForDescribeTopicsOutput.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopicsInfoForDescribeTopicsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopicsInfoForDescribeTopicsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopicsInfoForDescribeTopicsOutput):
            return True

        return self.to_dict() != other.to_dict()
