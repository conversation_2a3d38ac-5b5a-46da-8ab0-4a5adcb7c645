# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNLBListenerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_id': 'str',
        'connection_timeout': 'int',
        'description': 'str',
        'enabled': 'bool',
        'end_port': 'int',
        'listener_name': 'str',
        'load_balancer_id': 'str',
        'port': 'int',
        'protocol': 'str',
        'security_policy_id': 'str',
        'server_group_id': 'str',
        'start_port': 'int',
        'tags': 'list[TagForCreateNLBListenerInput]'
    }

    attribute_map = {
        'certificate_id': 'CertificateId',
        'connection_timeout': 'ConnectionTimeout',
        'description': 'Description',
        'enabled': 'Enabled',
        'end_port': 'EndPort',
        'listener_name': 'ListenerName',
        'load_balancer_id': 'LoadBalancerId',
        'port': 'Port',
        'protocol': 'Protocol',
        'security_policy_id': 'SecurityPolicyId',
        'server_group_id': 'ServerGroupId',
        'start_port': 'StartPort',
        'tags': 'Tags'
    }

    def __init__(self, certificate_id=None, connection_timeout=None, description=None, enabled=None, end_port=None, listener_name=None, load_balancer_id=None, port=None, protocol=None, security_policy_id=None, server_group_id=None, start_port=None, tags=None, _configuration=None):  # noqa: E501
        """CreateNLBListenerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_id = None
        self._connection_timeout = None
        self._description = None
        self._enabled = None
        self._end_port = None
        self._listener_name = None
        self._load_balancer_id = None
        self._port = None
        self._protocol = None
        self._security_policy_id = None
        self._server_group_id = None
        self._start_port = None
        self._tags = None
        self.discriminator = None

        if certificate_id is not None:
            self.certificate_id = certificate_id
        if connection_timeout is not None:
            self.connection_timeout = connection_timeout
        if description is not None:
            self.description = description
        if enabled is not None:
            self.enabled = enabled
        if end_port is not None:
            self.end_port = end_port
        if listener_name is not None:
            self.listener_name = listener_name
        self.load_balancer_id = load_balancer_id
        self.port = port
        self.protocol = protocol
        if security_policy_id is not None:
            self.security_policy_id = security_policy_id
        self.server_group_id = server_group_id
        if start_port is not None:
            self.start_port = start_port
        if tags is not None:
            self.tags = tags

    @property
    def certificate_id(self):
        """Gets the certificate_id of this CreateNLBListenerRequest.  # noqa: E501


        :return: The certificate_id of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this CreateNLBListenerRequest.


        :param certificate_id: The certificate_id of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def connection_timeout(self):
        """Gets the connection_timeout of this CreateNLBListenerRequest.  # noqa: E501


        :return: The connection_timeout of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: int
        """
        return self._connection_timeout

    @connection_timeout.setter
    def connection_timeout(self, connection_timeout):
        """Sets the connection_timeout of this CreateNLBListenerRequest.


        :param connection_timeout: The connection_timeout of this CreateNLBListenerRequest.  # noqa: E501
        :type: int
        """

        self._connection_timeout = connection_timeout

    @property
    def description(self):
        """Gets the description of this CreateNLBListenerRequest.  # noqa: E501


        :return: The description of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNLBListenerRequest.


        :param description: The description of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enabled(self):
        """Gets the enabled of this CreateNLBListenerRequest.  # noqa: E501


        :return: The enabled of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this CreateNLBListenerRequest.


        :param enabled: The enabled of this CreateNLBListenerRequest.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def end_port(self):
        """Gets the end_port of this CreateNLBListenerRequest.  # noqa: E501


        :return: The end_port of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_port

    @end_port.setter
    def end_port(self, end_port):
        """Sets the end_port of this CreateNLBListenerRequest.


        :param end_port: The end_port of this CreateNLBListenerRequest.  # noqa: E501
        :type: int
        """

        self._end_port = end_port

    @property
    def listener_name(self):
        """Gets the listener_name of this CreateNLBListenerRequest.  # noqa: E501


        :return: The listener_name of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._listener_name

    @listener_name.setter
    def listener_name(self, listener_name):
        """Sets the listener_name of this CreateNLBListenerRequest.


        :param listener_name: The listener_name of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """

        self._listener_name = listener_name

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this CreateNLBListenerRequest.  # noqa: E501


        :return: The load_balancer_id of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this CreateNLBListenerRequest.


        :param load_balancer_id: The load_balancer_id of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def port(self):
        """Gets the port of this CreateNLBListenerRequest.  # noqa: E501


        :return: The port of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this CreateNLBListenerRequest.


        :param port: The port of this CreateNLBListenerRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and port is None:
            raise ValueError("Invalid value for `port`, must not be `None`")  # noqa: E501

        self._port = port

    @property
    def protocol(self):
        """Gets the protocol of this CreateNLBListenerRequest.  # noqa: E501


        :return: The protocol of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateNLBListenerRequest.


        :param protocol: The protocol of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protocol is None:
            raise ValueError("Invalid value for `protocol`, must not be `None`")  # noqa: E501

        self._protocol = protocol

    @property
    def security_policy_id(self):
        """Gets the security_policy_id of this CreateNLBListenerRequest.  # noqa: E501


        :return: The security_policy_id of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_id

    @security_policy_id.setter
    def security_policy_id(self, security_policy_id):
        """Sets the security_policy_id of this CreateNLBListenerRequest.


        :param security_policy_id: The security_policy_id of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """

        self._security_policy_id = security_policy_id

    @property
    def server_group_id(self):
        """Gets the server_group_id of this CreateNLBListenerRequest.  # noqa: E501


        :return: The server_group_id of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this CreateNLBListenerRequest.


        :param server_group_id: The server_group_id of this CreateNLBListenerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and server_group_id is None:
            raise ValueError("Invalid value for `server_group_id`, must not be `None`")  # noqa: E501

        self._server_group_id = server_group_id

    @property
    def start_port(self):
        """Gets the start_port of this CreateNLBListenerRequest.  # noqa: E501


        :return: The start_port of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_port

    @start_port.setter
    def start_port(self, start_port):
        """Sets the start_port of this CreateNLBListenerRequest.


        :param start_port: The start_port of this CreateNLBListenerRequest.  # noqa: E501
        :type: int
        """

        self._start_port = start_port

    @property
    def tags(self):
        """Gets the tags of this CreateNLBListenerRequest.  # noqa: E501


        :return: The tags of this CreateNLBListenerRequest.  # noqa: E501
        :rtype: list[TagForCreateNLBListenerInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateNLBListenerRequest.


        :param tags: The tags of this CreateNLBListenerRequest.  # noqa: E501
        :type: list[TagForCreateNLBListenerInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNLBListenerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNLBListenerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNLBListenerRequest):
            return True

        return self.to_dict() != other.to_dict()
