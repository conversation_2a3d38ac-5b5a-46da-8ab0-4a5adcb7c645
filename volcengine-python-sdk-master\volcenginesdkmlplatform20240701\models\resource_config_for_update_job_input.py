# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceConfigForUpdateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'holding_time_seconds': 'int',
        'max_runtime_seconds': 'int',
        'priority': 'int'
    }

    attribute_map = {
        'holding_time_seconds': 'HoldingTimeSeconds',
        'max_runtime_seconds': 'MaxRuntimeSeconds',
        'priority': 'Priority'
    }

    def __init__(self, holding_time_seconds=None, max_runtime_seconds=None, priority=None, _configuration=None):  # noqa: E501
        """ResourceConfigForUpdateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._holding_time_seconds = None
        self._max_runtime_seconds = None
        self._priority = None
        self.discriminator = None

        if holding_time_seconds is not None:
            self.holding_time_seconds = holding_time_seconds
        if max_runtime_seconds is not None:
            self.max_runtime_seconds = max_runtime_seconds
        if priority is not None:
            self.priority = priority

    @property
    def holding_time_seconds(self):
        """Gets the holding_time_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501


        :return: The holding_time_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._holding_time_seconds

    @holding_time_seconds.setter
    def holding_time_seconds(self, holding_time_seconds):
        """Sets the holding_time_seconds of this ResourceConfigForUpdateJobInput.


        :param holding_time_seconds: The holding_time_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._holding_time_seconds = holding_time_seconds

    @property
    def max_runtime_seconds(self):
        """Gets the max_runtime_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501


        :return: The max_runtime_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._max_runtime_seconds

    @max_runtime_seconds.setter
    def max_runtime_seconds(self, max_runtime_seconds):
        """Sets the max_runtime_seconds of this ResourceConfigForUpdateJobInput.


        :param max_runtime_seconds: The max_runtime_seconds of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._max_runtime_seconds = max_runtime_seconds

    @property
    def priority(self):
        """Gets the priority of this ResourceConfigForUpdateJobInput.  # noqa: E501


        :return: The priority of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ResourceConfigForUpdateJobInput.


        :param priority: The priority of this ResourceConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceConfigForUpdateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceConfigForUpdateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceConfigForUpdateJobInput):
            return True

        return self.to_dict() != other.to_dict()
