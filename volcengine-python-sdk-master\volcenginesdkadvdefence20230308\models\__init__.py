# coding: utf-8

# flake8: noqa
"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkadvdefence20230308.models.accurate_group_for_add_web_def_cc_rule_input import AccurateGroupForAddWebDefCcRuleInput
from volcenginesdkadvdefence20230308.models.accurate_group_for_upd_web_def_cc_rule_input import AccurateGroupForUpdWebDefCcRuleInput
from volcenginesdkadvdefence20230308.models.accurate_rule_for_add_web_def_cc_rule_input import AccurateRuleForAddWebDefCcRuleInput
from volcenginesdkadvdefence20230308.models.accurate_rule_for_upd_web_def_cc_rule_input import AccurateRuleForUpdWebDefCcRuleInput
from volcenginesdkadvdefence20230308.models.add_rule_for_batch_add_fwd_rule_input import AddRuleForBatchAddFwdRuleInput
from volcenginesdkadvdefence20230308.models.add_web_def_cc_rule_request import AddWebDefCcRuleRequest
from volcenginesdkadvdefence20230308.models.add_web_def_cc_rule_response import AddWebDefCcRuleResponse
from volcenginesdkadvdefence20230308.models.avg_flow_for_describe_biz_flow_and_conn_count_output import AvgFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.batch_add_fwd_rule_request import BatchAddFwdRuleRequest
from volcenginesdkadvdefence20230308.models.batch_add_fwd_rule_response import BatchAddFwdRuleResponse
from volcenginesdkadvdefence20230308.models.biz_in_kbps_flow_for_describe_biz_flow_and_conn_count_output import BizInKbpsFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.biz_in_pps_flow_for_describe_biz_flow_and_conn_count_output import BizInPpsFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.biz_out_kbps_flow_for_describe_biz_flow_and_conn_count_output import BizOutKbpsFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.biz_out_pps_flow_for_describe_biz_flow_and_conn_count_output import BizOutPpsFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.concurr_conn_flow_for_describe_biz_flow_and_conn_count_output import ConcurrConnFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.del_web_def_cc_rule_request import DelWebDefCcRuleRequest
from volcenginesdkadvdefence20230308.models.del_web_def_cc_rule_response import DelWebDefCcRuleResponse
from volcenginesdkadvdefence20230308.models.describe_attack_flow_request import DescribeAttackFlowRequest
from volcenginesdkadvdefence20230308.models.describe_attack_flow_response import DescribeAttackFlowResponse
from volcenginesdkadvdefence20230308.models.describe_biz_flow_and_conn_count_request import DescribeBizFlowAndConnCountRequest
from volcenginesdkadvdefence20230308.models.describe_biz_flow_and_conn_count_response import DescribeBizFlowAndConnCountResponse
from volcenginesdkadvdefence20230308.models.drop_k_bps_flow_for_describe_attack_flow_output import DropKBpsFlowForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.drop_pkts_flow_for_describe_attack_flow_output import DropPktsFlowForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.in_k_bps_flow_for_describe_attack_flow_output import InKBpsFlowForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.in_pkts_flow_for_describe_attack_flow_output import InPktsFlowForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.instance_result_for_describe_attack_flow_output import InstanceResultForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.message_for_batch_add_fwd_rule_output import MessageForBatchAddFwdRuleOutput
from volcenginesdkadvdefence20230308.models.new_conn_flow_for_describe_biz_flow_and_conn_count_output import NewConnFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.overall_result_for_describe_attack_flow_output import OverallResultForDescribeAttackFlowOutput
from volcenginesdkadvdefence20230308.models.peak_flow_for_describe_biz_flow_and_conn_count_output import PeakFlowForDescribeBizFlowAndConnCountOutput
from volcenginesdkadvdefence20230308.models.upd_web_def_cc_rule_request import UpdWebDefCcRuleRequest
from volcenginesdkadvdefence20230308.models.upd_web_def_cc_rule_response import UpdWebDefCcRuleResponse
from volcenginesdkadvdefence20230308.models.update_fwd_rule_request import UpdateFwdRuleRequest
from volcenginesdkadvdefence20230308.models.update_fwd_rule_response import UpdateFwdRuleResponse
from volcenginesdkadvdefence20230308.models.val_for_describe_attack_flow_output import ValForDescribeAttackFlowOutput
