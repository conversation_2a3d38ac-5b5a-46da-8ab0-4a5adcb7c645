# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListActivityUsersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'search_audience_group_id': 'int',
        'search_name': 'str',
        'search_status': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'search_audience_group_id': 'SearchAudienceGroupId',
        'search_name': 'SearchName',
        'search_status': 'SearchStatus'
    }

    def __init__(self, activity_id=None, page_number=None, page_size=None, search_audience_group_id=None, search_name=None, search_status=None, _configuration=None):  # noqa: E501
        """ListActivityUsersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._page_number = None
        self._page_size = None
        self._search_audience_group_id = None
        self._search_name = None
        self._search_status = None
        self.discriminator = None

        self.activity_id = activity_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if search_audience_group_id is not None:
            self.search_audience_group_id = search_audience_group_id
        if search_name is not None:
            self.search_name = search_name
        self.search_status = search_status

    @property
    def activity_id(self):
        """Gets the activity_id of this ListActivityUsersRequest.  # noqa: E501


        :return: The activity_id of this ListActivityUsersRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListActivityUsersRequest.


        :param activity_id: The activity_id of this ListActivityUsersRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def page_number(self):
        """Gets the page_number of this ListActivityUsersRequest.  # noqa: E501


        :return: The page_number of this ListActivityUsersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListActivityUsersRequest.


        :param page_number: The page_number of this ListActivityUsersRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListActivityUsersRequest.  # noqa: E501


        :return: The page_size of this ListActivityUsersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListActivityUsersRequest.


        :param page_size: The page_size of this ListActivityUsersRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def search_audience_group_id(self):
        """Gets the search_audience_group_id of this ListActivityUsersRequest.  # noqa: E501


        :return: The search_audience_group_id of this ListActivityUsersRequest.  # noqa: E501
        :rtype: int
        """
        return self._search_audience_group_id

    @search_audience_group_id.setter
    def search_audience_group_id(self, search_audience_group_id):
        """Sets the search_audience_group_id of this ListActivityUsersRequest.


        :param search_audience_group_id: The search_audience_group_id of this ListActivityUsersRequest.  # noqa: E501
        :type: int
        """

        self._search_audience_group_id = search_audience_group_id

    @property
    def search_name(self):
        """Gets the search_name of this ListActivityUsersRequest.  # noqa: E501


        :return: The search_name of this ListActivityUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_name

    @search_name.setter
    def search_name(self, search_name):
        """Sets the search_name of this ListActivityUsersRequest.


        :param search_name: The search_name of this ListActivityUsersRequest.  # noqa: E501
        :type: str
        """

        self._search_name = search_name

    @property
    def search_status(self):
        """Gets the search_status of this ListActivityUsersRequest.  # noqa: E501


        :return: The search_status of this ListActivityUsersRequest.  # noqa: E501
        :rtype: int
        """
        return self._search_status

    @search_status.setter
    def search_status(self, search_status):
        """Sets the search_status of this ListActivityUsersRequest.


        :param search_status: The search_status of this ListActivityUsersRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and search_status is None:
            raise ValueError("Invalid value for `search_status`, must not be `None`")  # noqa: E501

        self._search_status = search_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListActivityUsersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListActivityUsersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListActivityUsersRequest):
            return True

        return self.to_dict() != other.to_dict()
