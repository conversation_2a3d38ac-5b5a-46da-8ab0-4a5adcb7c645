# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PingDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_cost': 'int',
        'diagnose_detail': 'DiagnoseDetailForGetTaskResultOutput',
        'mtr_hops': 'int',
        'ping_avg_cost': 'int',
        'ping_detail': 'str',
        'ping_loss_rate': 'float',
        'ping_max_cost': 'int',
        'ping_min_cost': 'int',
        'ping_receive_times': 'int',
        'ping_send_times': 'int',
        'resolve_cost': 'int'
    }

    attribute_map = {
        'dns_cost': 'DNSCost',
        'diagnose_detail': 'DiagnoseDetail',
        'mtr_hops': 'MtrHops',
        'ping_avg_cost': 'PingAvgCost',
        'ping_detail': 'PingDetail',
        'ping_loss_rate': 'PingLossRate',
        'ping_max_cost': 'PingMaxCost',
        'ping_min_cost': 'PingMinCost',
        'ping_receive_times': 'PingReceiveTimes',
        'ping_send_times': 'PingSendTimes',
        'resolve_cost': 'ResolveCost'
    }

    def __init__(self, dns_cost=None, diagnose_detail=None, mtr_hops=None, ping_avg_cost=None, ping_detail=None, ping_loss_rate=None, ping_max_cost=None, ping_min_cost=None, ping_receive_times=None, ping_send_times=None, resolve_cost=None, _configuration=None):  # noqa: E501
        """PingDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_cost = None
        self._diagnose_detail = None
        self._mtr_hops = None
        self._ping_avg_cost = None
        self._ping_detail = None
        self._ping_loss_rate = None
        self._ping_max_cost = None
        self._ping_min_cost = None
        self._ping_receive_times = None
        self._ping_send_times = None
        self._resolve_cost = None
        self.discriminator = None

        if dns_cost is not None:
            self.dns_cost = dns_cost
        if diagnose_detail is not None:
            self.diagnose_detail = diagnose_detail
        if mtr_hops is not None:
            self.mtr_hops = mtr_hops
        if ping_avg_cost is not None:
            self.ping_avg_cost = ping_avg_cost
        if ping_detail is not None:
            self.ping_detail = ping_detail
        if ping_loss_rate is not None:
            self.ping_loss_rate = ping_loss_rate
        if ping_max_cost is not None:
            self.ping_max_cost = ping_max_cost
        if ping_min_cost is not None:
            self.ping_min_cost = ping_min_cost
        if ping_receive_times is not None:
            self.ping_receive_times = ping_receive_times
        if ping_send_times is not None:
            self.ping_send_times = ping_send_times
        if resolve_cost is not None:
            self.resolve_cost = resolve_cost

    @property
    def dns_cost(self):
        """Gets the dns_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The dns_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._dns_cost

    @dns_cost.setter
    def dns_cost(self, dns_cost):
        """Sets the dns_cost of this PingDetailForGetTaskResultOutput.


        :param dns_cost: The dns_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._dns_cost = dns_cost

    @property
    def diagnose_detail(self):
        """Gets the diagnose_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The diagnose_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: DiagnoseDetailForGetTaskResultOutput
        """
        return self._diagnose_detail

    @diagnose_detail.setter
    def diagnose_detail(self, diagnose_detail):
        """Sets the diagnose_detail of this PingDetailForGetTaskResultOutput.


        :param diagnose_detail: The diagnose_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: DiagnoseDetailForGetTaskResultOutput
        """

        self._diagnose_detail = diagnose_detail

    @property
    def mtr_hops(self):
        """Gets the mtr_hops of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The mtr_hops of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._mtr_hops

    @mtr_hops.setter
    def mtr_hops(self, mtr_hops):
        """Sets the mtr_hops of this PingDetailForGetTaskResultOutput.


        :param mtr_hops: The mtr_hops of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._mtr_hops = mtr_hops

    @property
    def ping_avg_cost(self):
        """Gets the ping_avg_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_avg_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ping_avg_cost

    @ping_avg_cost.setter
    def ping_avg_cost(self, ping_avg_cost):
        """Sets the ping_avg_cost of this PingDetailForGetTaskResultOutput.


        :param ping_avg_cost: The ping_avg_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ping_avg_cost = ping_avg_cost

    @property
    def ping_detail(self):
        """Gets the ping_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._ping_detail

    @ping_detail.setter
    def ping_detail(self, ping_detail):
        """Sets the ping_detail of this PingDetailForGetTaskResultOutput.


        :param ping_detail: The ping_detail of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._ping_detail = ping_detail

    @property
    def ping_loss_rate(self):
        """Gets the ping_loss_rate of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_loss_rate of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: float
        """
        return self._ping_loss_rate

    @ping_loss_rate.setter
    def ping_loss_rate(self, ping_loss_rate):
        """Sets the ping_loss_rate of this PingDetailForGetTaskResultOutput.


        :param ping_loss_rate: The ping_loss_rate of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: float
        """

        self._ping_loss_rate = ping_loss_rate

    @property
    def ping_max_cost(self):
        """Gets the ping_max_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_max_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ping_max_cost

    @ping_max_cost.setter
    def ping_max_cost(self, ping_max_cost):
        """Sets the ping_max_cost of this PingDetailForGetTaskResultOutput.


        :param ping_max_cost: The ping_max_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ping_max_cost = ping_max_cost

    @property
    def ping_min_cost(self):
        """Gets the ping_min_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_min_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ping_min_cost

    @ping_min_cost.setter
    def ping_min_cost(self, ping_min_cost):
        """Sets the ping_min_cost of this PingDetailForGetTaskResultOutput.


        :param ping_min_cost: The ping_min_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ping_min_cost = ping_min_cost

    @property
    def ping_receive_times(self):
        """Gets the ping_receive_times of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_receive_times of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ping_receive_times

    @ping_receive_times.setter
    def ping_receive_times(self, ping_receive_times):
        """Sets the ping_receive_times of this PingDetailForGetTaskResultOutput.


        :param ping_receive_times: The ping_receive_times of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ping_receive_times = ping_receive_times

    @property
    def ping_send_times(self):
        """Gets the ping_send_times of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_send_times of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ping_send_times

    @ping_send_times.setter
    def ping_send_times(self, ping_send_times):
        """Sets the ping_send_times of this PingDetailForGetTaskResultOutput.


        :param ping_send_times: The ping_send_times of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ping_send_times = ping_send_times

    @property
    def resolve_cost(self):
        """Gets the resolve_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501


        :return: The resolve_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._resolve_cost

    @resolve_cost.setter
    def resolve_cost(self, resolve_cost):
        """Sets the resolve_cost of this PingDetailForGetTaskResultOutput.


        :param resolve_cost: The resolve_cost of this PingDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._resolve_cost = resolve_cost

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PingDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PingDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PingDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
