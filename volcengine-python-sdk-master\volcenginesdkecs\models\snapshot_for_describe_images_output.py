# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SnapshotForDescribeImagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'size': 'int',
        'snapshot_id': 'str',
        'volume_kind': 'str'
    }

    attribute_map = {
        'size': 'Size',
        'snapshot_id': 'SnapshotId',
        'volume_kind': 'VolumeKind'
    }

    def __init__(self, size=None, snapshot_id=None, volume_kind=None, _configuration=None):  # noqa: E501
        """SnapshotForDescribeImagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._size = None
        self._snapshot_id = None
        self._volume_kind = None
        self.discriminator = None

        if size is not None:
            self.size = size
        if snapshot_id is not None:
            self.snapshot_id = snapshot_id
        if volume_kind is not None:
            self.volume_kind = volume_kind

    @property
    def size(self):
        """Gets the size of this SnapshotForDescribeImagesOutput.  # noqa: E501


        :return: The size of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this SnapshotForDescribeImagesOutput.


        :param size: The size of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def snapshot_id(self):
        """Gets the snapshot_id of this SnapshotForDescribeImagesOutput.  # noqa: E501


        :return: The snapshot_id of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_id

    @snapshot_id.setter
    def snapshot_id(self, snapshot_id):
        """Sets the snapshot_id of this SnapshotForDescribeImagesOutput.


        :param snapshot_id: The snapshot_id of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._snapshot_id = snapshot_id

    @property
    def volume_kind(self):
        """Gets the volume_kind of this SnapshotForDescribeImagesOutput.  # noqa: E501


        :return: The volume_kind of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._volume_kind

    @volume_kind.setter
    def volume_kind(self, volume_kind):
        """Sets the volume_kind of this SnapshotForDescribeImagesOutput.


        :param volume_kind: The volume_kind of this SnapshotForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._volume_kind = volume_kind

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SnapshotForDescribeImagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SnapshotForDescribeImagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SnapshotForDescribeImagesOutput):
            return True

        return self.to_dict() != other.to_dict()
