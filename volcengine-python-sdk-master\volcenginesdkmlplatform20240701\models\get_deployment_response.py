# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDeploymentResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'created_by': 'str',
        'description': 'str',
        'history_versions': 'list[HistoryVersionForGetDeploymentOutput]',
        'id': 'str',
        'instance_items': 'list[InstanceItemForGetDeploymentOutput]',
        'name': 'str',
        'priority': 'int',
        'resource_queue_id': 'str',
        'roles': 'list[RoleForGetDeploymentOutput]',
        'service_id': 'str',
        'status': 'ConvertStatusForGetDeploymentOutput',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'created_by': 'CreatedBy',
        'description': 'Description',
        'history_versions': 'HistoryVersions',
        'id': 'Id',
        'instance_items': 'InstanceItems',
        'name': 'Name',
        'priority': 'Priority',
        'resource_queue_id': 'ResourceQueueId',
        'roles': 'Roles',
        'service_id': 'ServiceId',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, created_by=None, description=None, history_versions=None, id=None, instance_items=None, name=None, priority=None, resource_queue_id=None, roles=None, service_id=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """GetDeploymentResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._created_by = None
        self._description = None
        self._history_versions = None
        self._id = None
        self._instance_items = None
        self._name = None
        self._priority = None
        self._resource_queue_id = None
        self._roles = None
        self._service_id = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if created_by is not None:
            self.created_by = created_by
        if description is not None:
            self.description = description
        if history_versions is not None:
            self.history_versions = history_versions
        if id is not None:
            self.id = id
        if instance_items is not None:
            self.instance_items = instance_items
        if name is not None:
            self.name = name
        if priority is not None:
            self.priority = priority
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if roles is not None:
            self.roles = roles
        if service_id is not None:
            self.service_id = service_id
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this GetDeploymentResponse.  # noqa: E501


        :return: The create_time of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetDeploymentResponse.


        :param create_time: The create_time of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def created_by(self):
        """Gets the created_by of this GetDeploymentResponse.  # noqa: E501


        :return: The created_by of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this GetDeploymentResponse.


        :param created_by: The created_by of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def description(self):
        """Gets the description of this GetDeploymentResponse.  # noqa: E501


        :return: The description of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetDeploymentResponse.


        :param description: The description of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def history_versions(self):
        """Gets the history_versions of this GetDeploymentResponse.  # noqa: E501


        :return: The history_versions of this GetDeploymentResponse.  # noqa: E501
        :rtype: list[HistoryVersionForGetDeploymentOutput]
        """
        return self._history_versions

    @history_versions.setter
    def history_versions(self, history_versions):
        """Sets the history_versions of this GetDeploymentResponse.


        :param history_versions: The history_versions of this GetDeploymentResponse.  # noqa: E501
        :type: list[HistoryVersionForGetDeploymentOutput]
        """

        self._history_versions = history_versions

    @property
    def id(self):
        """Gets the id of this GetDeploymentResponse.  # noqa: E501


        :return: The id of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetDeploymentResponse.


        :param id: The id of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def instance_items(self):
        """Gets the instance_items of this GetDeploymentResponse.  # noqa: E501


        :return: The instance_items of this GetDeploymentResponse.  # noqa: E501
        :rtype: list[InstanceItemForGetDeploymentOutput]
        """
        return self._instance_items

    @instance_items.setter
    def instance_items(self, instance_items):
        """Sets the instance_items of this GetDeploymentResponse.


        :param instance_items: The instance_items of this GetDeploymentResponse.  # noqa: E501
        :type: list[InstanceItemForGetDeploymentOutput]
        """

        self._instance_items = instance_items

    @property
    def name(self):
        """Gets the name of this GetDeploymentResponse.  # noqa: E501


        :return: The name of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetDeploymentResponse.


        :param name: The name of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def priority(self):
        """Gets the priority of this GetDeploymentResponse.  # noqa: E501


        :return: The priority of this GetDeploymentResponse.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this GetDeploymentResponse.


        :param priority: The priority of this GetDeploymentResponse.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this GetDeploymentResponse.  # noqa: E501


        :return: The resource_queue_id of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this GetDeploymentResponse.


        :param resource_queue_id: The resource_queue_id of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def roles(self):
        """Gets the roles of this GetDeploymentResponse.  # noqa: E501


        :return: The roles of this GetDeploymentResponse.  # noqa: E501
        :rtype: list[RoleForGetDeploymentOutput]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this GetDeploymentResponse.


        :param roles: The roles of this GetDeploymentResponse.  # noqa: E501
        :type: list[RoleForGetDeploymentOutput]
        """

        self._roles = roles

    @property
    def service_id(self):
        """Gets the service_id of this GetDeploymentResponse.  # noqa: E501


        :return: The service_id of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this GetDeploymentResponse.


        :param service_id: The service_id of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def status(self):
        """Gets the status of this GetDeploymentResponse.  # noqa: E501


        :return: The status of this GetDeploymentResponse.  # noqa: E501
        :rtype: ConvertStatusForGetDeploymentOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetDeploymentResponse.


        :param status: The status of this GetDeploymentResponse.  # noqa: E501
        :type: ConvertStatusForGetDeploymentOutput
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this GetDeploymentResponse.  # noqa: E501


        :return: The update_time of this GetDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetDeploymentResponse.


        :param update_time: The update_time of this GetDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDeploymentResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDeploymentResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDeploymentResponse):
            return True

        return self.to_dict() != other.to_dict()
