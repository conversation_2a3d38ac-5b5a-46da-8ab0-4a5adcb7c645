# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityExportFileResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_type': 'int',
        'file_url': 'str',
        'status': 'int'
    }

    attribute_map = {
        'data_type': 'DataType',
        'file_url': 'FileUrl',
        'status': 'Status'
    }

    def __init__(self, data_type=None, file_url=None, status=None, _configuration=None):  # noqa: E501
        """GetActivityExportFileResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_type = None
        self._file_url = None
        self._status = None
        self.discriminator = None

        if data_type is not None:
            self.data_type = data_type
        if file_url is not None:
            self.file_url = file_url
        if status is not None:
            self.status = status

    @property
    def data_type(self):
        """Gets the data_type of this GetActivityExportFileResponse.  # noqa: E501


        :return: The data_type of this GetActivityExportFileResponse.  # noqa: E501
        :rtype: int
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this GetActivityExportFileResponse.


        :param data_type: The data_type of this GetActivityExportFileResponse.  # noqa: E501
        :type: int
        """

        self._data_type = data_type

    @property
    def file_url(self):
        """Gets the file_url of this GetActivityExportFileResponse.  # noqa: E501


        :return: The file_url of this GetActivityExportFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_url

    @file_url.setter
    def file_url(self, file_url):
        """Sets the file_url of this GetActivityExportFileResponse.


        :param file_url: The file_url of this GetActivityExportFileResponse.  # noqa: E501
        :type: str
        """

        self._file_url = file_url

    @property
    def status(self):
        """Gets the status of this GetActivityExportFileResponse.  # noqa: E501


        :return: The status of this GetActivityExportFileResponse.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetActivityExportFileResponse.


        :param status: The status of this GetActivityExportFileResponse.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityExportFileResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityExportFileResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityExportFileResponse):
            return True

        return self.to_dict() != other.to_dict()
