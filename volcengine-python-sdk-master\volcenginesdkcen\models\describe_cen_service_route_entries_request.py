# coding: utf-8

"""
    cen

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCenServiceRouteEntriesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cen_id': 'str',
        'cen_route_entry_ids': 'list[str]',
        'destination_cidr_block': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'service_region_id': 'str',
        'service_vpc_id': 'str'
    }

    attribute_map = {
        'cen_id': 'CenId',
        'cen_route_entry_ids': 'CenRouteEntryIds',
        'destination_cidr_block': 'DestinationCidrBlock',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'service_region_id': 'ServiceRegionId',
        'service_vpc_id': 'ServiceVpcId'
    }

    def __init__(self, cen_id=None, cen_route_entry_ids=None, destination_cidr_block=None, page_number=None, page_size=None, service_region_id=None, service_vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeCenServiceRouteEntriesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cen_id = None
        self._cen_route_entry_ids = None
        self._destination_cidr_block = None
        self._page_number = None
        self._page_size = None
        self._service_region_id = None
        self._service_vpc_id = None
        self.discriminator = None

        if cen_id is not None:
            self.cen_id = cen_id
        if cen_route_entry_ids is not None:
            self.cen_route_entry_ids = cen_route_entry_ids
        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if service_region_id is not None:
            self.service_region_id = service_region_id
        if service_vpc_id is not None:
            self.service_vpc_id = service_vpc_id

    @property
    def cen_id(self):
        """Gets the cen_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The cen_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cen_id

    @cen_id.setter
    def cen_id(self, cen_id):
        """Sets the cen_id of this DescribeCenServiceRouteEntriesRequest.


        :param cen_id: The cen_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._cen_id = cen_id

    @property
    def cen_route_entry_ids(self):
        """Gets the cen_route_entry_ids of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The cen_route_entry_ids of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cen_route_entry_ids

    @cen_route_entry_ids.setter
    def cen_route_entry_ids(self, cen_route_entry_ids):
        """Sets the cen_route_entry_ids of this DescribeCenServiceRouteEntriesRequest.


        :param cen_route_entry_ids: The cen_route_entry_ids of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: list[str]
        """

        self._cen_route_entry_ids = cen_route_entry_ids

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The destination_cidr_block of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this DescribeCenServiceRouteEntriesRequest.


        :param destination_cidr_block: The destination_cidr_block of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def page_number(self):
        """Gets the page_number of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The page_number of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeCenServiceRouteEntriesRequest.


        :param page_number: The page_number of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The page_size of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeCenServiceRouteEntriesRequest.


        :param page_size: The page_size of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def service_region_id(self):
        """Gets the service_region_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The service_region_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_region_id

    @service_region_id.setter
    def service_region_id(self, service_region_id):
        """Sets the service_region_id of this DescribeCenServiceRouteEntriesRequest.


        :param service_region_id: The service_region_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._service_region_id = service_region_id

    @property
    def service_vpc_id(self):
        """Gets the service_vpc_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501


        :return: The service_vpc_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_vpc_id

    @service_vpc_id.setter
    def service_vpc_id(self, service_vpc_id):
        """Sets the service_vpc_id of this DescribeCenServiceRouteEntriesRequest.


        :param service_vpc_id: The service_vpc_id of this DescribeCenServiceRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._service_vpc_id = service_vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCenServiceRouteEntriesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCenServiceRouteEntriesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCenServiceRouteEntriesRequest):
            return True

        return self.to_dict() != other.to_dict()
