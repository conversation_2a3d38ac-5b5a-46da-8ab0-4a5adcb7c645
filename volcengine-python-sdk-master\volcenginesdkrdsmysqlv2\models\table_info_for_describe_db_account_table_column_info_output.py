# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TableInfoForDescribeDbAccountTableColumnInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_privileges': 'list[str]',
        'column_infos': 'list[ColumnInfoForDescribeDbAccountTableColumnInfoOutput]',
        'table_name': 'str'
    }

    attribute_map = {
        'account_privileges': 'AccountPrivileges',
        'column_infos': 'ColumnInfos',
        'table_name': 'TableName'
    }

    def __init__(self, account_privileges=None, column_infos=None, table_name=None, _configuration=None):  # noqa: E501
        """TableInfoForDescribeDbAccountTableColumnInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_privileges = None
        self._column_infos = None
        self._table_name = None
        self.discriminator = None

        if account_privileges is not None:
            self.account_privileges = account_privileges
        if column_infos is not None:
            self.column_infos = column_infos
        if table_name is not None:
            self.table_name = table_name

    @property
    def account_privileges(self):
        """Gets the account_privileges of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501


        :return: The account_privileges of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._account_privileges

    @account_privileges.setter
    def account_privileges(self, account_privileges):
        """Sets the account_privileges of this TableInfoForDescribeDbAccountTableColumnInfoOutput.


        :param account_privileges: The account_privileges of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._account_privileges = account_privileges

    @property
    def column_infos(self):
        """Gets the column_infos of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501


        :return: The column_infos of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :rtype: list[ColumnInfoForDescribeDbAccountTableColumnInfoOutput]
        """
        return self._column_infos

    @column_infos.setter
    def column_infos(self, column_infos):
        """Sets the column_infos of this TableInfoForDescribeDbAccountTableColumnInfoOutput.


        :param column_infos: The column_infos of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :type: list[ColumnInfoForDescribeDbAccountTableColumnInfoOutput]
        """

        self._column_infos = column_infos

    @property
    def table_name(self):
        """Gets the table_name of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501


        :return: The table_name of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._table_name

    @table_name.setter
    def table_name(self, table_name):
        """Sets the table_name of this TableInfoForDescribeDbAccountTableColumnInfoOutput.


        :param table_name: The table_name of this TableInfoForDescribeDbAccountTableColumnInfoOutput.  # noqa: E501
        :type: str
        """

        self._table_name = table_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TableInfoForDescribeDbAccountTableColumnInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TableInfoForDescribeDbAccountTableColumnInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TableInfoForDescribeDbAccountTableColumnInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
