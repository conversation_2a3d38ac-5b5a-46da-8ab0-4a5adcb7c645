# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatePolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'policy_document': 'str',
        'policy_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'policy_document': 'PolicyDocument',
        'policy_name': 'PolicyName'
    }

    def __init__(self, description=None, policy_document=None, policy_name=None, _configuration=None):  # noqa: E501
        """CreatePolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._policy_document = None
        self._policy_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.policy_document = policy_document
        self.policy_name = policy_name

    @property
    def description(self):
        """Gets the description of this CreatePolicyRequest.  # noqa: E501


        :return: The description of this CreatePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreatePolicyRequest.


        :param description: The description of this CreatePolicyRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501

        self._description = description

    @property
    def policy_document(self):
        """Gets the policy_document of this CreatePolicyRequest.  # noqa: E501


        :return: The policy_document of this CreatePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_document

    @policy_document.setter
    def policy_document(self, policy_document):
        """Sets the policy_document of this CreatePolicyRequest.


        :param policy_document: The policy_document of this CreatePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_document is None:
            raise ValueError("Invalid value for `policy_document`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                policy_document is not None and len(policy_document) > 6144):
            raise ValueError("Invalid value for `policy_document`, length must be less than or equal to `6144`")  # noqa: E501
        if (self._configuration.client_side_validation and
                policy_document is not None and len(policy_document) < 1):
            raise ValueError("Invalid value for `policy_document`, length must be greater than or equal to `1`")  # noqa: E501

        self._policy_document = policy_document

    @property
    def policy_name(self):
        """Gets the policy_name of this CreatePolicyRequest.  # noqa: E501


        :return: The policy_name of this CreatePolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_name

    @policy_name.setter
    def policy_name(self, policy_name):
        """Sets the policy_name of this CreatePolicyRequest.


        :param policy_name: The policy_name of this CreatePolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_name is None:
            raise ValueError("Invalid value for `policy_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                policy_name is not None and len(policy_name) > 64):
            raise ValueError("Invalid value for `policy_name`, length must be less than or equal to `64`")  # noqa: E501
        if (self._configuration.client_side_validation and
                policy_name is not None and len(policy_name) < 1):
            raise ValueError("Invalid value for `policy_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._policy_name = policy_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatePolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatePolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatePolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
