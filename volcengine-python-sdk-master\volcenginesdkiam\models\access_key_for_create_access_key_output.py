# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccessKeyForCreateAccessKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key_id': 'str',
        'create_date': 'str',
        'secret_access_key': 'str',
        'status': 'str',
        'update_date': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'access_key_id': 'AccessKeyId',
        'create_date': 'CreateDate',
        'secret_access_key': 'SecretAccessKey',
        'status': 'Status',
        'update_date': 'UpdateDate',
        'user_name': 'UserName'
    }

    def __init__(self, access_key_id=None, create_date=None, secret_access_key=None, status=None, update_date=None, user_name=None, _configuration=None):  # noqa: E501
        """AccessKeyForCreateAccessKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key_id = None
        self._create_date = None
        self._secret_access_key = None
        self._status = None
        self._update_date = None
        self._user_name = None
        self.discriminator = None

        if access_key_id is not None:
            self.access_key_id = access_key_id
        if create_date is not None:
            self.create_date = create_date
        if secret_access_key is not None:
            self.secret_access_key = secret_access_key
        if status is not None:
            self.status = status
        if update_date is not None:
            self.update_date = update_date
        if user_name is not None:
            self.user_name = user_name

    @property
    def access_key_id(self):
        """Gets the access_key_id of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The access_key_id of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_key_id

    @access_key_id.setter
    def access_key_id(self, access_key_id):
        """Sets the access_key_id of this AccessKeyForCreateAccessKeyOutput.


        :param access_key_id: The access_key_id of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._access_key_id = access_key_id

    @property
    def create_date(self):
        """Gets the create_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The create_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_date

    @create_date.setter
    def create_date(self, create_date):
        """Sets the create_date of this AccessKeyForCreateAccessKeyOutput.


        :param create_date: The create_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._create_date = create_date

    @property
    def secret_access_key(self):
        """Gets the secret_access_key of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The secret_access_key of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._secret_access_key

    @secret_access_key.setter
    def secret_access_key(self, secret_access_key):
        """Sets the secret_access_key of this AccessKeyForCreateAccessKeyOutput.


        :param secret_access_key: The secret_access_key of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._secret_access_key = secret_access_key

    @property
    def status(self):
        """Gets the status of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The status of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AccessKeyForCreateAccessKeyOutput.


        :param status: The status of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_date(self):
        """Gets the update_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The update_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this AccessKeyForCreateAccessKeyOutput.


        :param update_date: The update_date of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    @property
    def user_name(self):
        """Gets the user_name of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501


        :return: The user_name of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this AccessKeyForCreateAccessKeyOutput.


        :param user_name: The user_name of this AccessKeyForCreateAccessKeyOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccessKeyForCreateAccessKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccessKeyForCreateAccessKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccessKeyForCreateAccessKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
