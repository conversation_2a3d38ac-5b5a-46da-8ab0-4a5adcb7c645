# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListContactsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contact_group_ids': 'list[str]',
        'email': 'str',
        'ids': 'list[str]',
        'name': 'str'
    }

    attribute_map = {
        'contact_group_ids': 'ContactGroupIds',
        'email': 'Email',
        'ids': 'Ids',
        'name': 'Name'
    }

    def __init__(self, contact_group_ids=None, email=None, ids=None, name=None, _configuration=None):  # noqa: E501
        """FilterForListContactsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._contact_group_ids = None
        self._email = None
        self._ids = None
        self._name = None
        self.discriminator = None

        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if email is not None:
            self.email = email
        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this FilterForListContactsInput.  # noqa: E501


        :return: The contact_group_ids of this FilterForListContactsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this FilterForListContactsInput.


        :param contact_group_ids: The contact_group_ids of this FilterForListContactsInput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def email(self):
        """Gets the email of this FilterForListContactsInput.  # noqa: E501


        :return: The email of this FilterForListContactsInput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this FilterForListContactsInput.


        :param email: The email of this FilterForListContactsInput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def ids(self):
        """Gets the ids of this FilterForListContactsInput.  # noqa: E501


        :return: The ids of this FilterForListContactsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListContactsInput.


        :param ids: The ids of this FilterForListContactsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListContactsInput.  # noqa: E501


        :return: The name of this FilterForListContactsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListContactsInput.


        :param name: The name of this FilterForListContactsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListContactsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListContactsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListContactsInput):
            return True

        return self.to_dict() != other.to_dict()
