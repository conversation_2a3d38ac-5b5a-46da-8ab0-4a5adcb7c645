# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UtteranceForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attribute': 'AttributeForGetExecutionOutput',
        'end': 'float',
        'start': 'float',
        'text': 'str'
    }

    attribute_map = {
        'attribute': 'Attribute',
        'end': 'End',
        'start': 'Start',
        'text': 'Text'
    }

    def __init__(self, attribute=None, end=None, start=None, text=None, _configuration=None):  # noqa: E501
        """UtteranceForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attribute = None
        self._end = None
        self._start = None
        self._text = None
        self.discriminator = None

        if attribute is not None:
            self.attribute = attribute
        if end is not None:
            self.end = end
        if start is not None:
            self.start = start
        if text is not None:
            self.text = text

    @property
    def attribute(self):
        """Gets the attribute of this UtteranceForGetExecutionOutput.  # noqa: E501


        :return: The attribute of this UtteranceForGetExecutionOutput.  # noqa: E501
        :rtype: AttributeForGetExecutionOutput
        """
        return self._attribute

    @attribute.setter
    def attribute(self, attribute):
        """Sets the attribute of this UtteranceForGetExecutionOutput.


        :param attribute: The attribute of this UtteranceForGetExecutionOutput.  # noqa: E501
        :type: AttributeForGetExecutionOutput
        """

        self._attribute = attribute

    @property
    def end(self):
        """Gets the end of this UtteranceForGetExecutionOutput.  # noqa: E501


        :return: The end of this UtteranceForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._end

    @end.setter
    def end(self, end):
        """Sets the end of this UtteranceForGetExecutionOutput.


        :param end: The end of this UtteranceForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._end = end

    @property
    def start(self):
        """Gets the start of this UtteranceForGetExecutionOutput.  # noqa: E501


        :return: The start of this UtteranceForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._start

    @start.setter
    def start(self, start):
        """Sets the start of this UtteranceForGetExecutionOutput.


        :param start: The start of this UtteranceForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._start = start

    @property
    def text(self):
        """Gets the text of this UtteranceForGetExecutionOutput.  # noqa: E501


        :return: The text of this UtteranceForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this UtteranceForGetExecutionOutput.


        :param text: The text of this UtteranceForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._text = text

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UtteranceForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UtteranceForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UtteranceForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
