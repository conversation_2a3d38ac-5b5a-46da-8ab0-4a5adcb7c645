# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListInvitationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth': 'AuthForListInvitationOutput',
        'invitation_type': 'int',
        'relation': 'RelationForListInvitationOutput'
    }

    attribute_map = {
        'auth': 'Auth',
        'invitation_type': 'InvitationType',
        'relation': 'Relation'
    }

    def __init__(self, auth=None, invitation_type=None, relation=None, _configuration=None):  # noqa: E501
        """ListForListInvitationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth = None
        self._invitation_type = None
        self._relation = None
        self.discriminator = None

        if auth is not None:
            self.auth = auth
        if invitation_type is not None:
            self.invitation_type = invitation_type
        if relation is not None:
            self.relation = relation

    @property
    def auth(self):
        """Gets the auth of this ListForListInvitationOutput.  # noqa: E501


        :return: The auth of this ListForListInvitationOutput.  # noqa: E501
        :rtype: AuthForListInvitationOutput
        """
        return self._auth

    @auth.setter
    def auth(self, auth):
        """Sets the auth of this ListForListInvitationOutput.


        :param auth: The auth of this ListForListInvitationOutput.  # noqa: E501
        :type: AuthForListInvitationOutput
        """

        self._auth = auth

    @property
    def invitation_type(self):
        """Gets the invitation_type of this ListForListInvitationOutput.  # noqa: E501


        :return: The invitation_type of this ListForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._invitation_type

    @invitation_type.setter
    def invitation_type(self, invitation_type):
        """Sets the invitation_type of this ListForListInvitationOutput.


        :param invitation_type: The invitation_type of this ListForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._invitation_type = invitation_type

    @property
    def relation(self):
        """Gets the relation of this ListForListInvitationOutput.  # noqa: E501


        :return: The relation of this ListForListInvitationOutput.  # noqa: E501
        :rtype: RelationForListInvitationOutput
        """
        return self._relation

    @relation.setter
    def relation(self, relation):
        """Sets the relation of this ListForListInvitationOutput.


        :param relation: The relation of this ListForListInvitationOutput.  # noqa: E501
        :type: RelationForListInvitationOutput
        """

        self._relation = relation

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListInvitationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListInvitationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListInvitationOutput):
            return True

        return self.to_dict() != other.to_dict()
