# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateForListCdnDomainsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'common_name': 'str',
        'expire_time': 'str',
        'fingerprint_sha1': 'str',
        'fingerprint_sha256': 'str',
        'id': 'str',
        'name': 'str',
        'start_time': 'str',
        'status': 'str',
        'subject_alternative_names': 'list[str]',
        'sync_detail': 'SyncDetailForListCdnDomainsOutput',
        'volc_ids': 'list[str]',
        'volc_ids_sync_detail': 'VolcIdsSyncDetailForListCdnDomainsOutput',
        'region': 'str'
    }

    attribute_map = {
        'common_name': 'CommonName',
        'expire_time': 'ExpireTime',
        'fingerprint_sha1': 'FingerprintSha1',
        'fingerprint_sha256': 'FingerprintSha256',
        'id': 'Id',
        'name': 'Name',
        'start_time': 'StartTime',
        'status': 'Status',
        'subject_alternative_names': 'SubjectAlternativeNames',
        'sync_detail': 'SyncDetail',
        'volc_ids': 'VolcIds',
        'volc_ids_sync_detail': 'VolcIdsSyncDetail',
        'region': 'region'
    }

    def __init__(self, common_name=None, expire_time=None, fingerprint_sha1=None, fingerprint_sha256=None, id=None, name=None, start_time=None, status=None, subject_alternative_names=None, sync_detail=None, volc_ids=None, volc_ids_sync_detail=None, region=None, _configuration=None):  # noqa: E501
        """CertificateForListCdnDomainsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._common_name = None
        self._expire_time = None
        self._fingerprint_sha1 = None
        self._fingerprint_sha256 = None
        self._id = None
        self._name = None
        self._start_time = None
        self._status = None
        self._subject_alternative_names = None
        self._sync_detail = None
        self._volc_ids = None
        self._volc_ids_sync_detail = None
        self._region = None
        self.discriminator = None

        if common_name is not None:
            self.common_name = common_name
        if expire_time is not None:
            self.expire_time = expire_time
        if fingerprint_sha1 is not None:
            self.fingerprint_sha1 = fingerprint_sha1
        if fingerprint_sha256 is not None:
            self.fingerprint_sha256 = fingerprint_sha256
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if subject_alternative_names is not None:
            self.subject_alternative_names = subject_alternative_names
        if sync_detail is not None:
            self.sync_detail = sync_detail
        if volc_ids is not None:
            self.volc_ids = volc_ids
        if volc_ids_sync_detail is not None:
            self.volc_ids_sync_detail = volc_ids_sync_detail
        if region is not None:
            self.region = region

    @property
    def common_name(self):
        """Gets the common_name of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The common_name of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this CertificateForListCdnDomainsOutput.


        :param common_name: The common_name of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def expire_time(self):
        """Gets the expire_time of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The expire_time of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this CertificateForListCdnDomainsOutput.


        :param expire_time: The expire_time of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def fingerprint_sha1(self):
        """Gets the fingerprint_sha1 of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The fingerprint_sha1 of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._fingerprint_sha1

    @fingerprint_sha1.setter
    def fingerprint_sha1(self, fingerprint_sha1):
        """Sets the fingerprint_sha1 of this CertificateForListCdnDomainsOutput.


        :param fingerprint_sha1: The fingerprint_sha1 of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._fingerprint_sha1 = fingerprint_sha1

    @property
    def fingerprint_sha256(self):
        """Gets the fingerprint_sha256 of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The fingerprint_sha256 of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._fingerprint_sha256

    @fingerprint_sha256.setter
    def fingerprint_sha256(self, fingerprint_sha256):
        """Sets the fingerprint_sha256 of this CertificateForListCdnDomainsOutput.


        :param fingerprint_sha256: The fingerprint_sha256 of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._fingerprint_sha256 = fingerprint_sha256

    @property
    def id(self):
        """Gets the id of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The id of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CertificateForListCdnDomainsOutput.


        :param id: The id of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The name of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CertificateForListCdnDomainsOutput.


        :param name: The name of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def start_time(self):
        """Gets the start_time of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The start_time of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this CertificateForListCdnDomainsOutput.


        :param start_time: The start_time of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The status of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CertificateForListCdnDomainsOutput.


        :param status: The status of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subject_alternative_names(self):
        """Gets the subject_alternative_names of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The subject_alternative_names of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subject_alternative_names

    @subject_alternative_names.setter
    def subject_alternative_names(self, subject_alternative_names):
        """Sets the subject_alternative_names of this CertificateForListCdnDomainsOutput.


        :param subject_alternative_names: The subject_alternative_names of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: list[str]
        """

        self._subject_alternative_names = subject_alternative_names

    @property
    def sync_detail(self):
        """Gets the sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: SyncDetailForListCdnDomainsOutput
        """
        return self._sync_detail

    @sync_detail.setter
    def sync_detail(self, sync_detail):
        """Sets the sync_detail of this CertificateForListCdnDomainsOutput.


        :param sync_detail: The sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: SyncDetailForListCdnDomainsOutput
        """

        self._sync_detail = sync_detail

    @property
    def volc_ids(self):
        """Gets the volc_ids of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The volc_ids of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._volc_ids

    @volc_ids.setter
    def volc_ids(self, volc_ids):
        """Sets the volc_ids of this CertificateForListCdnDomainsOutput.


        :param volc_ids: The volc_ids of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: list[str]
        """

        self._volc_ids = volc_ids

    @property
    def volc_ids_sync_detail(self):
        """Gets the volc_ids_sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The volc_ids_sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: VolcIdsSyncDetailForListCdnDomainsOutput
        """
        return self._volc_ids_sync_detail

    @volc_ids_sync_detail.setter
    def volc_ids_sync_detail(self, volc_ids_sync_detail):
        """Sets the volc_ids_sync_detail of this CertificateForListCdnDomainsOutput.


        :param volc_ids_sync_detail: The volc_ids_sync_detail of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: VolcIdsSyncDetailForListCdnDomainsOutput
        """

        self._volc_ids_sync_detail = volc_ids_sync_detail

    @property
    def region(self):
        """Gets the region of this CertificateForListCdnDomainsOutput.  # noqa: E501


        :return: The region of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this CertificateForListCdnDomainsOutput.


        :param region: The region of this CertificateForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateForListCdnDomainsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateForListCdnDomainsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateForListCdnDomainsOutput):
            return True

        return self.to_dict() != other.to_dict()
