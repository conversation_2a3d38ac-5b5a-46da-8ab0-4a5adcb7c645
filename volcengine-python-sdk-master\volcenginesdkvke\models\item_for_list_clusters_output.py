# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_config': 'ClusterConfigForListClustersOutput',
        'connector_config': 'ConnectorConfigForListClustersOutput',
        'create_client_token': 'str',
        'create_time': 'str',
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'id': 'str',
        'kubernetes_version': 'str',
        'logging_config': 'LoggingConfigForListClustersOutput',
        'message': 'str',
        'monitoring_config': 'MonitoringConfigForListClustersOutput',
        'name': 'str',
        'node_statistics': 'NodeStatisticsForListClustersOutput',
        'pods_config': 'PodsConfigForListClustersOutput',
        'project_name': 'str',
        'register_monitoring_config': 'RegisterMonitoringConfigForListClustersOutput',
        'services_config': 'ServicesConfigForListClustersOutput',
        'status': 'StatusForListClustersOutput',
        'tags': 'list[TagForListClustersOutput]',
        'type': 'str',
        'update_client_token': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'cluster_config': 'ClusterConfig',
        'connector_config': 'ConnectorConfig',
        'create_client_token': 'CreateClientToken',
        'create_time': 'CreateTime',
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'id': 'Id',
        'kubernetes_version': 'KubernetesVersion',
        'logging_config': 'LoggingConfig',
        'message': 'Message',
        'monitoring_config': 'MonitoringConfig',
        'name': 'Name',
        'node_statistics': 'NodeStatistics',
        'pods_config': 'PodsConfig',
        'project_name': 'ProjectName',
        'register_monitoring_config': 'RegisterMonitoringConfig',
        'services_config': 'ServicesConfig',
        'status': 'Status',
        'tags': 'Tags',
        'type': 'Type',
        'update_client_token': 'UpdateClientToken',
        'update_time': 'UpdateTime'
    }

    def __init__(self, cluster_config=None, connector_config=None, create_client_token=None, create_time=None, delete_protection_enabled=None, description=None, id=None, kubernetes_version=None, logging_config=None, message=None, monitoring_config=None, name=None, node_statistics=None, pods_config=None, project_name=None, register_monitoring_config=None, services_config=None, status=None, tags=None, type=None, update_client_token=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_config = None
        self._connector_config = None
        self._create_client_token = None
        self._create_time = None
        self._delete_protection_enabled = None
        self._description = None
        self._id = None
        self._kubernetes_version = None
        self._logging_config = None
        self._message = None
        self._monitoring_config = None
        self._name = None
        self._node_statistics = None
        self._pods_config = None
        self._project_name = None
        self._register_monitoring_config = None
        self._services_config = None
        self._status = None
        self._tags = None
        self._type = None
        self._update_client_token = None
        self._update_time = None
        self.discriminator = None

        if cluster_config is not None:
            self.cluster_config = cluster_config
        if connector_config is not None:
            self.connector_config = connector_config
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if create_time is not None:
            self.create_time = create_time
        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if kubernetes_version is not None:
            self.kubernetes_version = kubernetes_version
        if logging_config is not None:
            self.logging_config = logging_config
        if message is not None:
            self.message = message
        if monitoring_config is not None:
            self.monitoring_config = monitoring_config
        if name is not None:
            self.name = name
        if node_statistics is not None:
            self.node_statistics = node_statistics
        if pods_config is not None:
            self.pods_config = pods_config
        if project_name is not None:
            self.project_name = project_name
        if register_monitoring_config is not None:
            self.register_monitoring_config = register_monitoring_config
        if services_config is not None:
            self.services_config = services_config
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type
        if update_client_token is not None:
            self.update_client_token = update_client_token
        if update_time is not None:
            self.update_time = update_time

    @property
    def cluster_config(self):
        """Gets the cluster_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The cluster_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: ClusterConfigForListClustersOutput
        """
        return self._cluster_config

    @cluster_config.setter
    def cluster_config(self, cluster_config):
        """Sets the cluster_config of this ItemForListClustersOutput.


        :param cluster_config: The cluster_config of this ItemForListClustersOutput.  # noqa: E501
        :type: ClusterConfigForListClustersOutput
        """

        self._cluster_config = cluster_config

    @property
    def connector_config(self):
        """Gets the connector_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The connector_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: ConnectorConfigForListClustersOutput
        """
        return self._connector_config

    @connector_config.setter
    def connector_config(self, connector_config):
        """Sets the connector_config of this ItemForListClustersOutput.


        :param connector_config: The connector_config of this ItemForListClustersOutput.  # noqa: E501
        :type: ConnectorConfigForListClustersOutput
        """

        self._connector_config = connector_config

    @property
    def create_client_token(self):
        """Gets the create_client_token of this ItemForListClustersOutput.  # noqa: E501


        :return: The create_client_token of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this ItemForListClustersOutput.


        :param create_client_token: The create_client_token of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The create_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListClustersOutput.


        :param create_time: The create_time of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this ItemForListClustersOutput.  # noqa: E501


        :return: The delete_protection_enabled of this ItemForListClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this ItemForListClustersOutput.


        :param delete_protection_enabled: The delete_protection_enabled of this ItemForListClustersOutput.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this ItemForListClustersOutput.  # noqa: E501


        :return: The description of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListClustersOutput.


        :param description: The description of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this ItemForListClustersOutput.  # noqa: E501


        :return: The id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListClustersOutput.


        :param id: The id of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def kubernetes_version(self):
        """Gets the kubernetes_version of this ItemForListClustersOutput.  # noqa: E501


        :return: The kubernetes_version of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._kubernetes_version

    @kubernetes_version.setter
    def kubernetes_version(self, kubernetes_version):
        """Sets the kubernetes_version of this ItemForListClustersOutput.


        :param kubernetes_version: The kubernetes_version of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._kubernetes_version = kubernetes_version

    @property
    def logging_config(self):
        """Gets the logging_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The logging_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: LoggingConfigForListClustersOutput
        """
        return self._logging_config

    @logging_config.setter
    def logging_config(self, logging_config):
        """Sets the logging_config of this ItemForListClustersOutput.


        :param logging_config: The logging_config of this ItemForListClustersOutput.  # noqa: E501
        :type: LoggingConfigForListClustersOutput
        """

        self._logging_config = logging_config

    @property
    def message(self):
        """Gets the message of this ItemForListClustersOutput.  # noqa: E501


        :return: The message of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListClustersOutput.


        :param message: The message of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def monitoring_config(self):
        """Gets the monitoring_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The monitoring_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: MonitoringConfigForListClustersOutput
        """
        return self._monitoring_config

    @monitoring_config.setter
    def monitoring_config(self, monitoring_config):
        """Sets the monitoring_config of this ItemForListClustersOutput.


        :param monitoring_config: The monitoring_config of this ItemForListClustersOutput.  # noqa: E501
        :type: MonitoringConfigForListClustersOutput
        """

        self._monitoring_config = monitoring_config

    @property
    def name(self):
        """Gets the name of this ItemForListClustersOutput.  # noqa: E501


        :return: The name of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListClustersOutput.


        :param name: The name of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_statistics(self):
        """Gets the node_statistics of this ItemForListClustersOutput.  # noqa: E501


        :return: The node_statistics of this ItemForListClustersOutput.  # noqa: E501
        :rtype: NodeStatisticsForListClustersOutput
        """
        return self._node_statistics

    @node_statistics.setter
    def node_statistics(self, node_statistics):
        """Sets the node_statistics of this ItemForListClustersOutput.


        :param node_statistics: The node_statistics of this ItemForListClustersOutput.  # noqa: E501
        :type: NodeStatisticsForListClustersOutput
        """

        self._node_statistics = node_statistics

    @property
    def pods_config(self):
        """Gets the pods_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The pods_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: PodsConfigForListClustersOutput
        """
        return self._pods_config

    @pods_config.setter
    def pods_config(self, pods_config):
        """Sets the pods_config of this ItemForListClustersOutput.


        :param pods_config: The pods_config of this ItemForListClustersOutput.  # noqa: E501
        :type: PodsConfigForListClustersOutput
        """

        self._pods_config = pods_config

    @property
    def project_name(self):
        """Gets the project_name of this ItemForListClustersOutput.  # noqa: E501


        :return: The project_name of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ItemForListClustersOutput.


        :param project_name: The project_name of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def register_monitoring_config(self):
        """Gets the register_monitoring_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The register_monitoring_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: RegisterMonitoringConfigForListClustersOutput
        """
        return self._register_monitoring_config

    @register_monitoring_config.setter
    def register_monitoring_config(self, register_monitoring_config):
        """Sets the register_monitoring_config of this ItemForListClustersOutput.


        :param register_monitoring_config: The register_monitoring_config of this ItemForListClustersOutput.  # noqa: E501
        :type: RegisterMonitoringConfigForListClustersOutput
        """

        self._register_monitoring_config = register_monitoring_config

    @property
    def services_config(self):
        """Gets the services_config of this ItemForListClustersOutput.  # noqa: E501


        :return: The services_config of this ItemForListClustersOutput.  # noqa: E501
        :rtype: ServicesConfigForListClustersOutput
        """
        return self._services_config

    @services_config.setter
    def services_config(self, services_config):
        """Sets the services_config of this ItemForListClustersOutput.


        :param services_config: The services_config of this ItemForListClustersOutput.  # noqa: E501
        :type: ServicesConfigForListClustersOutput
        """

        self._services_config = services_config

    @property
    def status(self):
        """Gets the status of this ItemForListClustersOutput.  # noqa: E501


        :return: The status of this ItemForListClustersOutput.  # noqa: E501
        :rtype: StatusForListClustersOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListClustersOutput.


        :param status: The status of this ItemForListClustersOutput.  # noqa: E501
        :type: StatusForListClustersOutput
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ItemForListClustersOutput.  # noqa: E501


        :return: The tags of this ItemForListClustersOutput.  # noqa: E501
        :rtype: list[TagForListClustersOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListClustersOutput.


        :param tags: The tags of this ItemForListClustersOutput.  # noqa: E501
        :type: list[TagForListClustersOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this ItemForListClustersOutput.  # noqa: E501


        :return: The type of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListClustersOutput.


        :param type: The type of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_client_token(self):
        """Gets the update_client_token of this ItemForListClustersOutput.  # noqa: E501


        :return: The update_client_token of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_client_token

    @update_client_token.setter
    def update_client_token(self, update_client_token):
        """Sets the update_client_token of this ItemForListClustersOutput.


        :param update_client_token: The update_client_token of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._update_client_token = update_client_token

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The update_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListClustersOutput.


        :param update_time: The update_time of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
