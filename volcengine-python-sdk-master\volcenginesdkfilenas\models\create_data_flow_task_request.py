# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDataFlowTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bucket_name': 'str',
        'bucket_prefix': 'str',
        'data_flow_id': 'str',
        'entry_list_file_key': 'str',
        'entry_list_file_name': 'str',
        'evict_policy': 'EvictPolicyForCreateDataFlowTaskInput',
        'export_policy': 'ExportPolicyForCreateDataFlowTaskInput',
        'file_system_id': 'str',
        'file_system_path': 'str',
        'import_policy': 'ImportPolicyForCreateDataFlowTaskInput',
        'same_name_file_policy': 'str',
        'src_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'bucket_name': 'BucketName',
        'bucket_prefix': 'BucketPrefix',
        'data_flow_id': 'DataFlowId',
        'entry_list_file_key': 'EntryListFileKey',
        'entry_list_file_name': 'EntryListFileName',
        'evict_policy': 'EvictPolicy',
        'export_policy': 'ExportPolicy',
        'file_system_id': 'FileSystemId',
        'file_system_path': 'FileSystemPath',
        'import_policy': 'ImportPolicy',
        'same_name_file_policy': 'SameNameFilePolicy',
        'src_id': 'SrcId',
        'type': 'Type'
    }

    def __init__(self, bucket_name=None, bucket_prefix=None, data_flow_id=None, entry_list_file_key=None, entry_list_file_name=None, evict_policy=None, export_policy=None, file_system_id=None, file_system_path=None, import_policy=None, same_name_file_policy=None, src_id=None, type=None, _configuration=None):  # noqa: E501
        """CreateDataFlowTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bucket_name = None
        self._bucket_prefix = None
        self._data_flow_id = None
        self._entry_list_file_key = None
        self._entry_list_file_name = None
        self._evict_policy = None
        self._export_policy = None
        self._file_system_id = None
        self._file_system_path = None
        self._import_policy = None
        self._same_name_file_policy = None
        self._src_id = None
        self._type = None
        self.discriminator = None

        if bucket_name is not None:
            self.bucket_name = bucket_name
        if bucket_prefix is not None:
            self.bucket_prefix = bucket_prefix
        if data_flow_id is not None:
            self.data_flow_id = data_flow_id
        if entry_list_file_key is not None:
            self.entry_list_file_key = entry_list_file_key
        if entry_list_file_name is not None:
            self.entry_list_file_name = entry_list_file_name
        if evict_policy is not None:
            self.evict_policy = evict_policy
        if export_policy is not None:
            self.export_policy = export_policy
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if file_system_path is not None:
            self.file_system_path = file_system_path
        if import_policy is not None:
            self.import_policy = import_policy
        if same_name_file_policy is not None:
            self.same_name_file_policy = same_name_file_policy
        if src_id is not None:
            self.src_id = src_id
        if type is not None:
            self.type = type

    @property
    def bucket_name(self):
        """Gets the bucket_name of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The bucket_name of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._bucket_name

    @bucket_name.setter
    def bucket_name(self, bucket_name):
        """Sets the bucket_name of this CreateDataFlowTaskRequest.


        :param bucket_name: The bucket_name of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._bucket_name = bucket_name

    @property
    def bucket_prefix(self):
        """Gets the bucket_prefix of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The bucket_prefix of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._bucket_prefix

    @bucket_prefix.setter
    def bucket_prefix(self, bucket_prefix):
        """Sets the bucket_prefix of this CreateDataFlowTaskRequest.


        :param bucket_prefix: The bucket_prefix of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._bucket_prefix = bucket_prefix

    @property
    def data_flow_id(self):
        """Gets the data_flow_id of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The data_flow_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_id

    @data_flow_id.setter
    def data_flow_id(self, data_flow_id):
        """Sets the data_flow_id of this CreateDataFlowTaskRequest.


        :param data_flow_id: The data_flow_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._data_flow_id = data_flow_id

    @property
    def entry_list_file_key(self):
        """Gets the entry_list_file_key of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The entry_list_file_key of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_key

    @entry_list_file_key.setter
    def entry_list_file_key(self, entry_list_file_key):
        """Sets the entry_list_file_key of this CreateDataFlowTaskRequest.


        :param entry_list_file_key: The entry_list_file_key of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._entry_list_file_key = entry_list_file_key

    @property
    def entry_list_file_name(self):
        """Gets the entry_list_file_name of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The entry_list_file_name of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_name

    @entry_list_file_name.setter
    def entry_list_file_name(self, entry_list_file_name):
        """Sets the entry_list_file_name of this CreateDataFlowTaskRequest.


        :param entry_list_file_name: The entry_list_file_name of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._entry_list_file_name = entry_list_file_name

    @property
    def evict_policy(self):
        """Gets the evict_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The evict_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: EvictPolicyForCreateDataFlowTaskInput
        """
        return self._evict_policy

    @evict_policy.setter
    def evict_policy(self, evict_policy):
        """Sets the evict_policy of this CreateDataFlowTaskRequest.


        :param evict_policy: The evict_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: EvictPolicyForCreateDataFlowTaskInput
        """

        self._evict_policy = evict_policy

    @property
    def export_policy(self):
        """Gets the export_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The export_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: ExportPolicyForCreateDataFlowTaskInput
        """
        return self._export_policy

    @export_policy.setter
    def export_policy(self, export_policy):
        """Sets the export_policy of this CreateDataFlowTaskRequest.


        :param export_policy: The export_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: ExportPolicyForCreateDataFlowTaskInput
        """

        self._export_policy = export_policy

    @property
    def file_system_id(self):
        """Gets the file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this CreateDataFlowTaskRequest.


        :param file_system_id: The file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def file_system_path(self):
        """Gets the file_system_path of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The file_system_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_path

    @file_system_path.setter
    def file_system_path(self, file_system_path):
        """Sets the file_system_path of this CreateDataFlowTaskRequest.


        :param file_system_path: The file_system_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._file_system_path = file_system_path

    @property
    def import_policy(self):
        """Gets the import_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The import_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: ImportPolicyForCreateDataFlowTaskInput
        """
        return self._import_policy

    @import_policy.setter
    def import_policy(self, import_policy):
        """Sets the import_policy of this CreateDataFlowTaskRequest.


        :param import_policy: The import_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: ImportPolicyForCreateDataFlowTaskInput
        """

        self._import_policy = import_policy

    @property
    def same_name_file_policy(self):
        """Gets the same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._same_name_file_policy

    @same_name_file_policy.setter
    def same_name_file_policy(self, same_name_file_policy):
        """Sets the same_name_file_policy of this CreateDataFlowTaskRequest.


        :param same_name_file_policy: The same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Skip", "KeepLatest", "OverWrite"]  # noqa: E501
        if (self._configuration.client_side_validation and
                same_name_file_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `same_name_file_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(same_name_file_policy, allowed_values)
            )

        self._same_name_file_policy = same_name_file_policy

    @property
    def src_id(self):
        """Gets the src_id of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The src_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._src_id

    @src_id.setter
    def src_id(self, src_id):
        """Sets the src_id of this CreateDataFlowTaskRequest.


        :param src_id: The src_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._src_id = src_id

    @property
    def type(self):
        """Gets the type of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The type of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateDataFlowTaskRequest.


        :param type: The type of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Import", "Export", "Evict"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDataFlowTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDataFlowTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDataFlowTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
