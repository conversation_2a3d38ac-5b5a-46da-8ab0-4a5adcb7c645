# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_image': 'str',
        'end_time': 'int',
        'id': 'int',
        'is_auto_end_enable': 'int',
        'is_lock_preview': 'int',
        'is_page_view_enable': 'int',
        'live_layout': 'int',
        'live_mode': 'int',
        'live_review_status': 'int',
        'live_time': 'int',
        'name': 'str',
        'owner_sub_account': 'str',
        'status': 'int',
        'vertical_cover_image': 'str',
        'view_url': 'str'
    }

    attribute_map = {
        'cover_image': 'CoverImage',
        'end_time': 'EndTime',
        'id': 'Id',
        'is_auto_end_enable': 'IsAutoEndEnable',
        'is_lock_preview': 'IsLockPreview',
        'is_page_view_enable': 'IsPageViewEnable',
        'live_layout': 'LiveLayout',
        'live_mode': 'LiveMode',
        'live_review_status': 'LiveReviewStatus',
        'live_time': 'LiveTime',
        'name': 'Name',
        'owner_sub_account': 'OwnerSubAccount',
        'status': 'Status',
        'vertical_cover_image': 'VerticalCoverImage',
        'view_url': 'ViewUrl'
    }

    def __init__(self, cover_image=None, end_time=None, id=None, is_auto_end_enable=None, is_lock_preview=None, is_page_view_enable=None, live_layout=None, live_mode=None, live_review_status=None, live_time=None, name=None, owner_sub_account=None, status=None, vertical_cover_image=None, view_url=None, _configuration=None):  # noqa: E501
        """GetActivityAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_image = None
        self._end_time = None
        self._id = None
        self._is_auto_end_enable = None
        self._is_lock_preview = None
        self._is_page_view_enable = None
        self._live_layout = None
        self._live_mode = None
        self._live_review_status = None
        self._live_time = None
        self._name = None
        self._owner_sub_account = None
        self._status = None
        self._vertical_cover_image = None
        self._view_url = None
        self.discriminator = None

        if cover_image is not None:
            self.cover_image = cover_image
        if end_time is not None:
            self.end_time = end_time
        if id is not None:
            self.id = id
        if is_auto_end_enable is not None:
            self.is_auto_end_enable = is_auto_end_enable
        if is_lock_preview is not None:
            self.is_lock_preview = is_lock_preview
        if is_page_view_enable is not None:
            self.is_page_view_enable = is_page_view_enable
        if live_layout is not None:
            self.live_layout = live_layout
        if live_mode is not None:
            self.live_mode = live_mode
        if live_review_status is not None:
            self.live_review_status = live_review_status
        if live_time is not None:
            self.live_time = live_time
        if name is not None:
            self.name = name
        if owner_sub_account is not None:
            self.owner_sub_account = owner_sub_account
        if status is not None:
            self.status = status
        if vertical_cover_image is not None:
            self.vertical_cover_image = vertical_cover_image
        if view_url is not None:
            self.view_url = view_url

    @property
    def cover_image(self):
        """Gets the cover_image of this GetActivityAPIResponse.  # noqa: E501


        :return: The cover_image of this GetActivityAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this GetActivityAPIResponse.


        :param cover_image: The cover_image of this GetActivityAPIResponse.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def end_time(self):
        """Gets the end_time of this GetActivityAPIResponse.  # noqa: E501


        :return: The end_time of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetActivityAPIResponse.


        :param end_time: The end_time of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def id(self):
        """Gets the id of this GetActivityAPIResponse.  # noqa: E501


        :return: The id of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetActivityAPIResponse.


        :param id: The id of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_auto_end_enable(self):
        """Gets the is_auto_end_enable of this GetActivityAPIResponse.  # noqa: E501


        :return: The is_auto_end_enable of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_end_enable

    @is_auto_end_enable.setter
    def is_auto_end_enable(self, is_auto_end_enable):
        """Sets the is_auto_end_enable of this GetActivityAPIResponse.


        :param is_auto_end_enable: The is_auto_end_enable of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_auto_end_enable = is_auto_end_enable

    @property
    def is_lock_preview(self):
        """Gets the is_lock_preview of this GetActivityAPIResponse.  # noqa: E501


        :return: The is_lock_preview of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_lock_preview

    @is_lock_preview.setter
    def is_lock_preview(self, is_lock_preview):
        """Sets the is_lock_preview of this GetActivityAPIResponse.


        :param is_lock_preview: The is_lock_preview of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_lock_preview = is_lock_preview

    @property
    def is_page_view_enable(self):
        """Gets the is_page_view_enable of this GetActivityAPIResponse.  # noqa: E501


        :return: The is_page_view_enable of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_page_view_enable

    @is_page_view_enable.setter
    def is_page_view_enable(self, is_page_view_enable):
        """Sets the is_page_view_enable of this GetActivityAPIResponse.


        :param is_page_view_enable: The is_page_view_enable of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_page_view_enable = is_page_view_enable

    @property
    def live_layout(self):
        """Gets the live_layout of this GetActivityAPIResponse.  # noqa: E501


        :return: The live_layout of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_layout

    @live_layout.setter
    def live_layout(self, live_layout):
        """Sets the live_layout of this GetActivityAPIResponse.


        :param live_layout: The live_layout of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._live_layout = live_layout

    @property
    def live_mode(self):
        """Gets the live_mode of this GetActivityAPIResponse.  # noqa: E501


        :return: The live_mode of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_mode

    @live_mode.setter
    def live_mode(self, live_mode):
        """Sets the live_mode of this GetActivityAPIResponse.


        :param live_mode: The live_mode of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._live_mode = live_mode

    @property
    def live_review_status(self):
        """Gets the live_review_status of this GetActivityAPIResponse.  # noqa: E501


        :return: The live_review_status of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_review_status

    @live_review_status.setter
    def live_review_status(self, live_review_status):
        """Sets the live_review_status of this GetActivityAPIResponse.


        :param live_review_status: The live_review_status of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._live_review_status = live_review_status

    @property
    def live_time(self):
        """Gets the live_time of this GetActivityAPIResponse.  # noqa: E501


        :return: The live_time of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this GetActivityAPIResponse.


        :param live_time: The live_time of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def name(self):
        """Gets the name of this GetActivityAPIResponse.  # noqa: E501


        :return: The name of this GetActivityAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetActivityAPIResponse.


        :param name: The name of this GetActivityAPIResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner_sub_account(self):
        """Gets the owner_sub_account of this GetActivityAPIResponse.  # noqa: E501


        :return: The owner_sub_account of this GetActivityAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._owner_sub_account

    @owner_sub_account.setter
    def owner_sub_account(self, owner_sub_account):
        """Sets the owner_sub_account of this GetActivityAPIResponse.


        :param owner_sub_account: The owner_sub_account of this GetActivityAPIResponse.  # noqa: E501
        :type: str
        """

        self._owner_sub_account = owner_sub_account

    @property
    def status(self):
        """Gets the status of this GetActivityAPIResponse.  # noqa: E501


        :return: The status of this GetActivityAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetActivityAPIResponse.


        :param status: The status of this GetActivityAPIResponse.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def vertical_cover_image(self):
        """Gets the vertical_cover_image of this GetActivityAPIResponse.  # noqa: E501


        :return: The vertical_cover_image of this GetActivityAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._vertical_cover_image

    @vertical_cover_image.setter
    def vertical_cover_image(self, vertical_cover_image):
        """Sets the vertical_cover_image of this GetActivityAPIResponse.


        :param vertical_cover_image: The vertical_cover_image of this GetActivityAPIResponse.  # noqa: E501
        :type: str
        """

        self._vertical_cover_image = vertical_cover_image

    @property
    def view_url(self):
        """Gets the view_url of this GetActivityAPIResponse.  # noqa: E501


        :return: The view_url of this GetActivityAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._view_url

    @view_url.setter
    def view_url(self, view_url):
        """Sets the view_url of this GetActivityAPIResponse.


        :param view_url: The view_url of this GetActivityAPIResponse.  # noqa: E501
        :type: str
        """

        self._view_url = view_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
