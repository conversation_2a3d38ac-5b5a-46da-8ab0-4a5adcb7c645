# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRobotCommentsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'comment_content': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'robot_comment_repository_id': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'comment_content': 'CommentContent',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'robot_comment_repository_id': 'RobotCommentRepositoryId'
    }

    def __init__(self, activity_id=None, comment_content=None, page_number=None, page_size=None, robot_comment_repository_id=None, _configuration=None):  # noqa: E501
        """ListRobotCommentsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._comment_content = None
        self._page_number = None
        self._page_size = None
        self._robot_comment_repository_id = None
        self.discriminator = None

        self.activity_id = activity_id
        if comment_content is not None:
            self.comment_content = comment_content
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if robot_comment_repository_id is not None:
            self.robot_comment_repository_id = robot_comment_repository_id

    @property
    def activity_id(self):
        """Gets the activity_id of this ListRobotCommentsRequest.  # noqa: E501


        :return: The activity_id of this ListRobotCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListRobotCommentsRequest.


        :param activity_id: The activity_id of this ListRobotCommentsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def comment_content(self):
        """Gets the comment_content of this ListRobotCommentsRequest.  # noqa: E501


        :return: The comment_content of this ListRobotCommentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._comment_content

    @comment_content.setter
    def comment_content(self, comment_content):
        """Sets the comment_content of this ListRobotCommentsRequest.


        :param comment_content: The comment_content of this ListRobotCommentsRequest.  # noqa: E501
        :type: str
        """

        self._comment_content = comment_content

    @property
    def page_number(self):
        """Gets the page_number of this ListRobotCommentsRequest.  # noqa: E501


        :return: The page_number of this ListRobotCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRobotCommentsRequest.


        :param page_number: The page_number of this ListRobotCommentsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRobotCommentsRequest.  # noqa: E501


        :return: The page_size of this ListRobotCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRobotCommentsRequest.


        :param page_size: The page_size of this ListRobotCommentsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def robot_comment_repository_id(self):
        """Gets the robot_comment_repository_id of this ListRobotCommentsRequest.  # noqa: E501


        :return: The robot_comment_repository_id of this ListRobotCommentsRequest.  # noqa: E501
        :rtype: int
        """
        return self._robot_comment_repository_id

    @robot_comment_repository_id.setter
    def robot_comment_repository_id(self, robot_comment_repository_id):
        """Sets the robot_comment_repository_id of this ListRobotCommentsRequest.


        :param robot_comment_repository_id: The robot_comment_repository_id of this ListRobotCommentsRequest.  # noqa: E501
        :type: int
        """

        self._robot_comment_repository_id = robot_comment_repository_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRobotCommentsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRobotCommentsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRobotCommentsRequest):
            return True

        return self.to_dict() != other.to_dict()
