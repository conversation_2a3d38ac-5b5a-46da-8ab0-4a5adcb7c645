# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EDXListForListEDXOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'associated_bandwidth_pkg_count': 'int',
        'associated_vgw_count': 'int',
        'edx': 'EDXForListEDXOutput'
    }

    attribute_map = {
        'associated_bandwidth_pkg_count': 'AssociatedBandwidthPkgCount',
        'associated_vgw_count': 'AssociatedVGWCount',
        'edx': 'EDX'
    }

    def __init__(self, associated_bandwidth_pkg_count=None, associated_vgw_count=None, edx=None, _configuration=None):  # noqa: E501
        """EDXListForListEDXOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._associated_bandwidth_pkg_count = None
        self._associated_vgw_count = None
        self._edx = None
        self.discriminator = None

        if associated_bandwidth_pkg_count is not None:
            self.associated_bandwidth_pkg_count = associated_bandwidth_pkg_count
        if associated_vgw_count is not None:
            self.associated_vgw_count = associated_vgw_count
        if edx is not None:
            self.edx = edx

    @property
    def associated_bandwidth_pkg_count(self):
        """Gets the associated_bandwidth_pkg_count of this EDXListForListEDXOutput.  # noqa: E501


        :return: The associated_bandwidth_pkg_count of this EDXListForListEDXOutput.  # noqa: E501
        :rtype: int
        """
        return self._associated_bandwidth_pkg_count

    @associated_bandwidth_pkg_count.setter
    def associated_bandwidth_pkg_count(self, associated_bandwidth_pkg_count):
        """Sets the associated_bandwidth_pkg_count of this EDXListForListEDXOutput.


        :param associated_bandwidth_pkg_count: The associated_bandwidth_pkg_count of this EDXListForListEDXOutput.  # noqa: E501
        :type: int
        """

        self._associated_bandwidth_pkg_count = associated_bandwidth_pkg_count

    @property
    def associated_vgw_count(self):
        """Gets the associated_vgw_count of this EDXListForListEDXOutput.  # noqa: E501


        :return: The associated_vgw_count of this EDXListForListEDXOutput.  # noqa: E501
        :rtype: int
        """
        return self._associated_vgw_count

    @associated_vgw_count.setter
    def associated_vgw_count(self, associated_vgw_count):
        """Sets the associated_vgw_count of this EDXListForListEDXOutput.


        :param associated_vgw_count: The associated_vgw_count of this EDXListForListEDXOutput.  # noqa: E501
        :type: int
        """

        self._associated_vgw_count = associated_vgw_count

    @property
    def edx(self):
        """Gets the edx of this EDXListForListEDXOutput.  # noqa: E501


        :return: The edx of this EDXListForListEDXOutput.  # noqa: E501
        :rtype: EDXForListEDXOutput
        """
        return self._edx

    @edx.setter
    def edx(self, edx):
        """Sets the edx of this EDXListForListEDXOutput.


        :param edx: The edx of this EDXListForListEDXOutput.  # noqa: E501
        :type: EDXForListEDXOutput
        """

        self._edx = edx

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EDXListForListEDXOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EDXListForListEDXOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EDXListForListEDXOutput):
            return True

        return self.to_dict() != other.to_dict()
