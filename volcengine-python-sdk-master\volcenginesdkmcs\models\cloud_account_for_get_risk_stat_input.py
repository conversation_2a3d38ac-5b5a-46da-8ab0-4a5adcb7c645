# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CloudAccountForGetRiskStatInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_account_name': 'str',
        'cloud_account_uid': 'str',
        'cloud_vendor': 'str'
    }

    attribute_map = {
        'cloud_account_name': 'CloudAccountName',
        'cloud_account_uid': 'CloudAccountUID',
        'cloud_vendor': 'CloudVendor'
    }

    def __init__(self, cloud_account_name=None, cloud_account_uid=None, cloud_vendor=None, _configuration=None):  # noqa: E501
        """CloudAccountForGetRiskStatInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_account_name = None
        self._cloud_account_uid = None
        self._cloud_vendor = None
        self.discriminator = None

        if cloud_account_name is not None:
            self.cloud_account_name = cloud_account_name
        if cloud_account_uid is not None:
            self.cloud_account_uid = cloud_account_uid
        if cloud_vendor is not None:
            self.cloud_vendor = cloud_vendor

    @property
    def cloud_account_name(self):
        """Gets the cloud_account_name of this CloudAccountForGetRiskStatInput.  # noqa: E501


        :return: The cloud_account_name of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_name

    @cloud_account_name.setter
    def cloud_account_name(self, cloud_account_name):
        """Sets the cloud_account_name of this CloudAccountForGetRiskStatInput.


        :param cloud_account_name: The cloud_account_name of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :type: str
        """

        self._cloud_account_name = cloud_account_name

    @property
    def cloud_account_uid(self):
        """Gets the cloud_account_uid of this CloudAccountForGetRiskStatInput.  # noqa: E501


        :return: The cloud_account_uid of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_uid

    @cloud_account_uid.setter
    def cloud_account_uid(self, cloud_account_uid):
        """Sets the cloud_account_uid of this CloudAccountForGetRiskStatInput.


        :param cloud_account_uid: The cloud_account_uid of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :type: str
        """

        self._cloud_account_uid = cloud_account_uid

    @property
    def cloud_vendor(self):
        """Gets the cloud_vendor of this CloudAccountForGetRiskStatInput.  # noqa: E501


        :return: The cloud_vendor of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_vendor

    @cloud_vendor.setter
    def cloud_vendor(self, cloud_vendor):
        """Sets the cloud_vendor of this CloudAccountForGetRiskStatInput.


        :param cloud_vendor: The cloud_vendor of this CloudAccountForGetRiskStatInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["volcengine", "aliyun", "huaweicloud", "tencent"]  # noqa: E501
        if (self._configuration.client_side_validation and
                cloud_vendor not in allowed_values):
            raise ValueError(
                "Invalid value for `cloud_vendor` ({0}), must be one of {1}"  # noqa: E501
                .format(cloud_vendor, allowed_values)
            )

        self._cloud_vendor = cloud_vendor

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CloudAccountForGetRiskStatInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CloudAccountForGetRiskStatInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CloudAccountForGetRiskStatInput):
            return True

        return self.to_dict() != other.to_dict()
