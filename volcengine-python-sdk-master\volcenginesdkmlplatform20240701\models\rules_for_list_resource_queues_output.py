# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RulesForListResourceQueuesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_compact_gpu_resource_rule': 'AutoCompactGPUResourceRuleForListResourceQueuesOutput',
        'general_idle_rule': 'GeneralIdleRuleForListResourceQueuesOutput',
        'gpu_utilization_rule': 'GpuUtilizationRuleForListResourceQueuesOutput'
    }

    attribute_map = {
        'auto_compact_gpu_resource_rule': 'AutoCompactGPUResourceRule',
        'general_idle_rule': 'GeneralIdleRule',
        'gpu_utilization_rule': 'GpuUtilizationRule'
    }

    def __init__(self, auto_compact_gpu_resource_rule=None, general_idle_rule=None, gpu_utilization_rule=None, _configuration=None):  # noqa: E501
        """RulesForListResourceQueuesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_compact_gpu_resource_rule = None
        self._general_idle_rule = None
        self._gpu_utilization_rule = None
        self.discriminator = None

        if auto_compact_gpu_resource_rule is not None:
            self.auto_compact_gpu_resource_rule = auto_compact_gpu_resource_rule
        if general_idle_rule is not None:
            self.general_idle_rule = general_idle_rule
        if gpu_utilization_rule is not None:
            self.gpu_utilization_rule = gpu_utilization_rule

    @property
    def auto_compact_gpu_resource_rule(self):
        """Gets the auto_compact_gpu_resource_rule of this RulesForListResourceQueuesOutput.  # noqa: E501


        :return: The auto_compact_gpu_resource_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :rtype: AutoCompactGPUResourceRuleForListResourceQueuesOutput
        """
        return self._auto_compact_gpu_resource_rule

    @auto_compact_gpu_resource_rule.setter
    def auto_compact_gpu_resource_rule(self, auto_compact_gpu_resource_rule):
        """Sets the auto_compact_gpu_resource_rule of this RulesForListResourceQueuesOutput.


        :param auto_compact_gpu_resource_rule: The auto_compact_gpu_resource_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :type: AutoCompactGPUResourceRuleForListResourceQueuesOutput
        """

        self._auto_compact_gpu_resource_rule = auto_compact_gpu_resource_rule

    @property
    def general_idle_rule(self):
        """Gets the general_idle_rule of this RulesForListResourceQueuesOutput.  # noqa: E501


        :return: The general_idle_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :rtype: GeneralIdleRuleForListResourceQueuesOutput
        """
        return self._general_idle_rule

    @general_idle_rule.setter
    def general_idle_rule(self, general_idle_rule):
        """Sets the general_idle_rule of this RulesForListResourceQueuesOutput.


        :param general_idle_rule: The general_idle_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :type: GeneralIdleRuleForListResourceQueuesOutput
        """

        self._general_idle_rule = general_idle_rule

    @property
    def gpu_utilization_rule(self):
        """Gets the gpu_utilization_rule of this RulesForListResourceQueuesOutput.  # noqa: E501


        :return: The gpu_utilization_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :rtype: GpuUtilizationRuleForListResourceQueuesOutput
        """
        return self._gpu_utilization_rule

    @gpu_utilization_rule.setter
    def gpu_utilization_rule(self, gpu_utilization_rule):
        """Sets the gpu_utilization_rule of this RulesForListResourceQueuesOutput.


        :param gpu_utilization_rule: The gpu_utilization_rule of this RulesForListResourceQueuesOutput.  # noqa: E501
        :type: GpuUtilizationRuleForListResourceQueuesOutput
        """

        self._gpu_utilization_rule = gpu_utilization_rule

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RulesForListResourceQueuesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RulesForListResourceQueuesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RulesForListResourceQueuesOutput):
            return True

        return self.to_dict() != other.to_dict()
