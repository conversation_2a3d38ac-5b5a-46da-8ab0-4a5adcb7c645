# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetBaselineDetectProgressDetailRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'baseline_id': 'int',
        'group_id': 'int',
        'top_group_id': 'str'
    }

    attribute_map = {
        'baseline_id': 'BaselineID',
        'group_id': 'GroupID',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, baseline_id=None, group_id=None, top_group_id=None, _configuration=None):  # noqa: E501
        """GetBaselineDetectProgressDetailRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._baseline_id = None
        self._group_id = None
        self._top_group_id = None
        self.discriminator = None

        if baseline_id is not None:
            self.baseline_id = baseline_id
        if group_id is not None:
            self.group_id = group_id
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def baseline_id(self):
        """Gets the baseline_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501


        :return: The baseline_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this GetBaselineDetectProgressDetailRequest.


        :param baseline_id: The baseline_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def group_id(self):
        """Gets the group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501


        :return: The group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this GetBaselineDetectProgressDetailRequest.


        :param group_id: The group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :type: int
        """

        self._group_id = group_id

    @property
    def top_group_id(self):
        """Gets the top_group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501


        :return: The top_group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this GetBaselineDetectProgressDetailRequest.


        :param top_group_id: The top_group_id of this GetBaselineDetectProgressDetailRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetBaselineDetectProgressDetailRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetBaselineDetectProgressDetailRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetBaselineDetectProgressDetailRequest):
            return True

        return self.to_dict() != other.to_dict()
