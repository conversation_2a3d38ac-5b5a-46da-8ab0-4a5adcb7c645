# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstallAgentClientResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detail': 'list[DetailForInstallAgentClientOutput]',
        'failure_count': 'int',
        'success_count': 'int'
    }

    attribute_map = {
        'detail': 'Detail',
        'failure_count': 'FailureCount',
        'success_count': 'SuccessCount'
    }

    def __init__(self, detail=None, failure_count=None, success_count=None, _configuration=None):  # noqa: E501
        """InstallAgentClientResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detail = None
        self._failure_count = None
        self._success_count = None
        self.discriminator = None

        if detail is not None:
            self.detail = detail
        if failure_count is not None:
            self.failure_count = failure_count
        if success_count is not None:
            self.success_count = success_count

    @property
    def detail(self):
        """Gets the detail of this InstallAgentClientResponse.  # noqa: E501


        :return: The detail of this InstallAgentClientResponse.  # noqa: E501
        :rtype: list[DetailForInstallAgentClientOutput]
        """
        return self._detail

    @detail.setter
    def detail(self, detail):
        """Sets the detail of this InstallAgentClientResponse.


        :param detail: The detail of this InstallAgentClientResponse.  # noqa: E501
        :type: list[DetailForInstallAgentClientOutput]
        """

        self._detail = detail

    @property
    def failure_count(self):
        """Gets the failure_count of this InstallAgentClientResponse.  # noqa: E501


        :return: The failure_count of this InstallAgentClientResponse.  # noqa: E501
        :rtype: int
        """
        return self._failure_count

    @failure_count.setter
    def failure_count(self, failure_count):
        """Sets the failure_count of this InstallAgentClientResponse.


        :param failure_count: The failure_count of this InstallAgentClientResponse.  # noqa: E501
        :type: int
        """

        self._failure_count = failure_count

    @property
    def success_count(self):
        """Gets the success_count of this InstallAgentClientResponse.  # noqa: E501


        :return: The success_count of this InstallAgentClientResponse.  # noqa: E501
        :rtype: int
        """
        return self._success_count

    @success_count.setter
    def success_count(self, success_count):
        """Sets the success_count of this InstallAgentClientResponse.


        :param success_count: The success_count of this InstallAgentClientResponse.  # noqa: E501
        :type: int
        """

        self._success_count = success_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstallAgentClientResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstallAgentClientResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstallAgentClientResponse):
            return True

        return self.to_dict() != other.to_dict()
