# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDeploymentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'history_version_limit': 'int',
        'id': 'str',
        'include_instance_items': 'bool'
    }

    attribute_map = {
        'history_version_limit': 'HistoryVersionLimit',
        'id': 'Id',
        'include_instance_items': 'IncludeInstanceItems'
    }

    def __init__(self, history_version_limit=None, id=None, include_instance_items=None, _configuration=None):  # noqa: E501
        """GetDeploymentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._history_version_limit = None
        self._id = None
        self._include_instance_items = None
        self.discriminator = None

        if history_version_limit is not None:
            self.history_version_limit = history_version_limit
        self.id = id
        if include_instance_items is not None:
            self.include_instance_items = include_instance_items

    @property
    def history_version_limit(self):
        """Gets the history_version_limit of this GetDeploymentRequest.  # noqa: E501


        :return: The history_version_limit of this GetDeploymentRequest.  # noqa: E501
        :rtype: int
        """
        return self._history_version_limit

    @history_version_limit.setter
    def history_version_limit(self, history_version_limit):
        """Sets the history_version_limit of this GetDeploymentRequest.


        :param history_version_limit: The history_version_limit of this GetDeploymentRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                history_version_limit is not None and history_version_limit > 100):  # noqa: E501
            raise ValueError("Invalid value for `history_version_limit`, must be a value less than or equal to `100`")  # noqa: E501

        self._history_version_limit = history_version_limit

    @property
    def id(self):
        """Gets the id of this GetDeploymentRequest.  # noqa: E501


        :return: The id of this GetDeploymentRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetDeploymentRequest.


        :param id: The id of this GetDeploymentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def include_instance_items(self):
        """Gets the include_instance_items of this GetDeploymentRequest.  # noqa: E501


        :return: The include_instance_items of this GetDeploymentRequest.  # noqa: E501
        :rtype: bool
        """
        return self._include_instance_items

    @include_instance_items.setter
    def include_instance_items(self, include_instance_items):
        """Sets the include_instance_items of this GetDeploymentRequest.


        :param include_instance_items: The include_instance_items of this GetDeploymentRequest.  # noqa: E501
        :type: bool
        """

        self._include_instance_items = include_instance_items

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDeploymentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDeploymentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDeploymentRequest):
            return True

        return self.to_dict() != other.to_dict()
