# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplicationComponentLayoutForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'component_name': 'str',
        'effective_scope': 'EffectiveScopeForCreateClusterInput'
    }

    attribute_map = {
        'component_name': 'ComponentName',
        'effective_scope': 'EffectiveScope'
    }

    def __init__(self, component_name=None, effective_scope=None, _configuration=None):  # noqa: E501
        """ApplicationComponentLayoutForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._component_name = None
        self._effective_scope = None
        self.discriminator = None

        if component_name is not None:
            self.component_name = component_name
        if effective_scope is not None:
            self.effective_scope = effective_scope

    @property
    def component_name(self):
        """Gets the component_name of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501


        :return: The component_name of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._component_name

    @component_name.setter
    def component_name(self, component_name):
        """Sets the component_name of this ApplicationComponentLayoutForCreateClusterInput.


        :param component_name: The component_name of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._component_name = component_name

    @property
    def effective_scope(self):
        """Gets the effective_scope of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501


        :return: The effective_scope of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501
        :rtype: EffectiveScopeForCreateClusterInput
        """
        return self._effective_scope

    @effective_scope.setter
    def effective_scope(self, effective_scope):
        """Sets the effective_scope of this ApplicationComponentLayoutForCreateClusterInput.


        :param effective_scope: The effective_scope of this ApplicationComponentLayoutForCreateClusterInput.  # noqa: E501
        :type: EffectiveScopeForCreateClusterInput
        """

        self._effective_scope = effective_scope

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplicationComponentLayoutForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplicationComponentLayoutForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplicationComponentLayoutForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
