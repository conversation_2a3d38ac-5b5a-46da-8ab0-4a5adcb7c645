# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AutoRenewReservedStorageCapacityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'period': 'int',
        'period_unit': 'str',
        'rsc_auto_renew': 'bool',
        'rscid': 'str'
    }

    attribute_map = {
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'rsc_auto_renew': 'RSCAutoRenew',
        'rscid': 'RSCId'
    }

    def __init__(self, period=None, period_unit=None, rsc_auto_renew=None, rscid=None, _configuration=None):  # noqa: E501
        """AutoRenewReservedStorageCapacityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._period = None
        self._period_unit = None
        self._rsc_auto_renew = None
        self._rscid = None
        self.discriminator = None

        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if rsc_auto_renew is not None:
            self.rsc_auto_renew = rsc_auto_renew
        self.rscid = rscid

    @property
    def period(self):
        """Gets the period of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The period of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this AutoRenewReservedStorageCapacityRequest.


        :param period: The period of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The period_unit of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this AutoRenewReservedStorageCapacityRequest.


        :param period_unit: The period_unit of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def rsc_auto_renew(self):
        """Gets the rsc_auto_renew of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The rsc_auto_renew of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: bool
        """
        return self._rsc_auto_renew

    @rsc_auto_renew.setter
    def rsc_auto_renew(self, rsc_auto_renew):
        """Sets the rsc_auto_renew of this AutoRenewReservedStorageCapacityRequest.


        :param rsc_auto_renew: The rsc_auto_renew of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: bool
        """

        self._rsc_auto_renew = rsc_auto_renew

    @property
    def rscid(self):
        """Gets the rscid of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The rscid of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: str
        """
        return self._rscid

    @rscid.setter
    def rscid(self, rscid):
        """Sets the rscid of this AutoRenewReservedStorageCapacityRequest.


        :param rscid: The rscid of this AutoRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rscid is None:
            raise ValueError("Invalid value for `rscid`, must not be `None`")  # noqa: E501

        self._rscid = rscid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AutoRenewReservedStorageCapacityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AutoRenewReservedStorageCapacityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AutoRenewReservedStorageCapacityRequest):
            return True

        return self.to_dict() != other.to_dict()
