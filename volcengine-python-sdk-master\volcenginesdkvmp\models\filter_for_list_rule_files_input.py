# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRuleFilesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ids': 'list[str]',
        'name': 'str',
        'status': 'list[str]',
        'workspace_id': 'str'
    }

    attribute_map = {
        'ids': 'Ids',
        'name': 'Name',
        'status': 'Status',
        'workspace_id': 'WorkspaceId'
    }

    def __init__(self, ids=None, name=None, status=None, workspace_id=None, _configuration=None):  # noqa: E501
        """FilterForListRuleFilesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ids = None
        self._name = None
        self._status = None
        self._workspace_id = None
        self.discriminator = None

        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if workspace_id is not None:
            self.workspace_id = workspace_id

    @property
    def ids(self):
        """Gets the ids of this FilterForListRuleFilesInput.  # noqa: E501


        :return: The ids of this FilterForListRuleFilesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListRuleFilesInput.


        :param ids: The ids of this FilterForListRuleFilesInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListRuleFilesInput.  # noqa: E501


        :return: The name of this FilterForListRuleFilesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListRuleFilesInput.


        :param name: The name of this FilterForListRuleFilesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this FilterForListRuleFilesInput.  # noqa: E501


        :return: The status of this FilterForListRuleFilesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListRuleFilesInput.


        :param status: The status of this FilterForListRuleFilesInput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def workspace_id(self):
        """Gets the workspace_id of this FilterForListRuleFilesInput.  # noqa: E501


        :return: The workspace_id of this FilterForListRuleFilesInput.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this FilterForListRuleFilesInput.


        :param workspace_id: The workspace_id of this FilterForListRuleFilesInput.  # noqa: E501
        :type: str
        """

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRuleFilesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRuleFilesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRuleFilesInput):
            return True

        return self.to_dict() != other.to_dict()
