# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthorityListForListCrossAccountVIFAuthorityOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'state': 'str',
        'vgw_account_id': 'int',
        'vif_account_id': 'int',
        'vif_instance_id': 'str',
        'vif_instance_name': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'state': 'State',
        'vgw_account_id': 'VGWAccountId',
        'vif_account_id': 'VIFAccountId',
        'vif_instance_id': 'VIFInstanceId',
        'vif_instance_name': 'VIFInstanceName'
    }

    def __init__(self, create_time=None, state=None, vgw_account_id=None, vif_account_id=None, vif_instance_id=None, vif_instance_name=None, _configuration=None):  # noqa: E501
        """AuthorityListForListCrossAccountVIFAuthorityOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._state = None
        self._vgw_account_id = None
        self._vif_account_id = None
        self._vif_instance_id = None
        self._vif_instance_name = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if state is not None:
            self.state = state
        if vgw_account_id is not None:
            self.vgw_account_id = vgw_account_id
        if vif_account_id is not None:
            self.vif_account_id = vif_account_id
        if vif_instance_id is not None:
            self.vif_instance_id = vif_instance_id
        if vif_instance_name is not None:
            self.vif_instance_name = vif_instance_name

    @property
    def create_time(self):
        """Gets the create_time of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The create_time of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param create_time: The create_time of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def state(self):
        """Gets the state of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The state of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param state: The state of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def vgw_account_id(self):
        """Gets the vgw_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The vgw_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: int
        """
        return self._vgw_account_id

    @vgw_account_id.setter
    def vgw_account_id(self, vgw_account_id):
        """Sets the vgw_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param vgw_account_id: The vgw_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: int
        """

        self._vgw_account_id = vgw_account_id

    @property
    def vif_account_id(self):
        """Gets the vif_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The vif_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: int
        """
        return self._vif_account_id

    @vif_account_id.setter
    def vif_account_id(self, vif_account_id):
        """Sets the vif_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param vif_account_id: The vif_account_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: int
        """

        self._vif_account_id = vif_account_id

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The vif_instance_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param vif_instance_id: The vif_instance_id of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: str
        """

        self._vif_instance_id = vif_instance_id

    @property
    def vif_instance_name(self):
        """Gets the vif_instance_name of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501


        :return: The vif_instance_name of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_name

    @vif_instance_name.setter
    def vif_instance_name(self, vif_instance_name):
        """Sets the vif_instance_name of this AuthorityListForListCrossAccountVIFAuthorityOutput.


        :param vif_instance_name: The vif_instance_name of this AuthorityListForListCrossAccountVIFAuthorityOutput.  # noqa: E501
        :type: str
        """

        self._vif_instance_name = vif_instance_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthorityListForListCrossAccountVIFAuthorityOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthorityListForListCrossAccountVIFAuthorityOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthorityListForListCrossAccountVIFAuthorityOutput):
            return True

        return self.to_dict() != other.to_dict()
