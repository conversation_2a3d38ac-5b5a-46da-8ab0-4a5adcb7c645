# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccelerateIPForListBasicEndpointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerate_ip': 'str',
        'accelerate_ipid': 'str',
        'isp': 'str',
        'state': 'str'
    }

    attribute_map = {
        'accelerate_ip': 'AccelerateIP',
        'accelerate_ipid': 'AccelerateIPId',
        'isp': 'ISP',
        'state': 'State'
    }

    def __init__(self, accelerate_ip=None, accelerate_ipid=None, isp=None, state=None, _configuration=None):  # noqa: E501
        """AccelerateIPForListBasicEndpointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerate_ip = None
        self._accelerate_ipid = None
        self._isp = None
        self._state = None
        self.discriminator = None

        if accelerate_ip is not None:
            self.accelerate_ip = accelerate_ip
        if accelerate_ipid is not None:
            self.accelerate_ipid = accelerate_ipid
        if isp is not None:
            self.isp = isp
        if state is not None:
            self.state = state

    @property
    def accelerate_ip(self):
        """Gets the accelerate_ip of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501


        :return: The accelerate_ip of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_ip

    @accelerate_ip.setter
    def accelerate_ip(self, accelerate_ip):
        """Sets the accelerate_ip of this AccelerateIPForListBasicEndpointsOutput.


        :param accelerate_ip: The accelerate_ip of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._accelerate_ip = accelerate_ip

    @property
    def accelerate_ipid(self):
        """Gets the accelerate_ipid of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501


        :return: The accelerate_ipid of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_ipid

    @accelerate_ipid.setter
    def accelerate_ipid(self, accelerate_ipid):
        """Sets the accelerate_ipid of this AccelerateIPForListBasicEndpointsOutput.


        :param accelerate_ipid: The accelerate_ipid of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._accelerate_ipid = accelerate_ipid

    @property
    def isp(self):
        """Gets the isp of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501


        :return: The isp of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this AccelerateIPForListBasicEndpointsOutput.


        :param isp: The isp of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def state(self):
        """Gets the state of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501


        :return: The state of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this AccelerateIPForListBasicEndpointsOutput.


        :param state: The state of this AccelerateIPForListBasicEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccelerateIPForListBasicEndpointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccelerateIPForListBasicEndpointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccelerateIPForListBasicEndpointsOutput):
            return True

        return self.to_dict() != other.to_dict()
