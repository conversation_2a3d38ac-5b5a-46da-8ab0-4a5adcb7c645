# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CvssMetricForGetVulnDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'str',
        'kind': 'str',
        'name': 'str',
        'score': 'int'
    }

    attribute_map = {
        'code': 'Code',
        'kind': 'Kind',
        'name': 'Name',
        'score': 'Score'
    }

    def __init__(self, code=None, kind=None, name=None, score=None, _configuration=None):  # noqa: E501
        """CvssMetricForGetVulnDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code = None
        self._kind = None
        self._name = None
        self._score = None
        self.discriminator = None

        if code is not None:
            self.code = code
        if kind is not None:
            self.kind = kind
        if name is not None:
            self.name = name
        if score is not None:
            self.score = score

    @property
    def code(self):
        """Gets the code of this CvssMetricForGetVulnDetailOutput.  # noqa: E501


        :return: The code of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this CvssMetricForGetVulnDetailOutput.


        :param code: The code of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._code = code

    @property
    def kind(self):
        """Gets the kind of this CvssMetricForGetVulnDetailOutput.  # noqa: E501


        :return: The kind of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this CvssMetricForGetVulnDetailOutput.


        :param kind: The kind of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def name(self):
        """Gets the name of this CvssMetricForGetVulnDetailOutput.  # noqa: E501


        :return: The name of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CvssMetricForGetVulnDetailOutput.


        :param name: The name of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def score(self):
        """Gets the score of this CvssMetricForGetVulnDetailOutput.  # noqa: E501


        :return: The score of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._score

    @score.setter
    def score(self, score):
        """Sets the score of this CvssMetricForGetVulnDetailOutput.


        :param score: The score of this CvssMetricForGetVulnDetailOutput.  # noqa: E501
        :type: int
        """

        self._score = score

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CvssMetricForGetVulnDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CvssMetricForGetVulnDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CvssMetricForGetVulnDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
