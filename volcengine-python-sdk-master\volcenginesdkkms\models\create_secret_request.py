# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateSecretRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'automatic_rotation': 'bool',
        'description': 'str',
        'encryption_key': 'str',
        'extended_config': 'str',
        'project_name': 'str',
        'rotation_interval': 'str',
        'secret_name': 'str',
        'secret_type': 'str',
        'secret_value': 'str'
    }

    attribute_map = {
        'automatic_rotation': 'AutomaticRotation',
        'description': 'Description',
        'encryption_key': 'EncryptionKey',
        'extended_config': 'ExtendedConfig',
        'project_name': 'ProjectName',
        'rotation_interval': 'RotationInterval',
        'secret_name': 'SecretName',
        'secret_type': 'SecretType',
        'secret_value': 'SecretValue'
    }

    def __init__(self, automatic_rotation=None, description=None, encryption_key=None, extended_config=None, project_name=None, rotation_interval=None, secret_name=None, secret_type=None, secret_value=None, _configuration=None):  # noqa: E501
        """CreateSecretRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._automatic_rotation = None
        self._description = None
        self._encryption_key = None
        self._extended_config = None
        self._project_name = None
        self._rotation_interval = None
        self._secret_name = None
        self._secret_type = None
        self._secret_value = None
        self.discriminator = None

        if automatic_rotation is not None:
            self.automatic_rotation = automatic_rotation
        if description is not None:
            self.description = description
        if encryption_key is not None:
            self.encryption_key = encryption_key
        if extended_config is not None:
            self.extended_config = extended_config
        if project_name is not None:
            self.project_name = project_name
        if rotation_interval is not None:
            self.rotation_interval = rotation_interval
        self.secret_name = secret_name
        self.secret_type = secret_type
        self.secret_value = secret_value

    @property
    def automatic_rotation(self):
        """Gets the automatic_rotation of this CreateSecretRequest.  # noqa: E501


        :return: The automatic_rotation of this CreateSecretRequest.  # noqa: E501
        :rtype: bool
        """
        return self._automatic_rotation

    @automatic_rotation.setter
    def automatic_rotation(self, automatic_rotation):
        """Sets the automatic_rotation of this CreateSecretRequest.


        :param automatic_rotation: The automatic_rotation of this CreateSecretRequest.  # noqa: E501
        :type: bool
        """

        self._automatic_rotation = automatic_rotation

    @property
    def description(self):
        """Gets the description of this CreateSecretRequest.  # noqa: E501


        :return: The description of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateSecretRequest.


        :param description: The description of this CreateSecretRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 8192):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `8192`")  # noqa: E501

        self._description = description

    @property
    def encryption_key(self):
        """Gets the encryption_key of this CreateSecretRequest.  # noqa: E501


        :return: The encryption_key of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._encryption_key

    @encryption_key.setter
    def encryption_key(self, encryption_key):
        """Sets the encryption_key of this CreateSecretRequest.


        :param encryption_key: The encryption_key of this CreateSecretRequest.  # noqa: E501
        :type: str
        """

        self._encryption_key = encryption_key

    @property
    def extended_config(self):
        """Gets the extended_config of this CreateSecretRequest.  # noqa: E501


        :return: The extended_config of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._extended_config

    @extended_config.setter
    def extended_config(self, extended_config):
        """Sets the extended_config of this CreateSecretRequest.


        :param extended_config: The extended_config of this CreateSecretRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                extended_config is not None and len(extended_config) > 1024):
            raise ValueError("Invalid value for `extended_config`, length must be less than or equal to `1024`")  # noqa: E501

        self._extended_config = extended_config

    @property
    def project_name(self):
        """Gets the project_name of this CreateSecretRequest.  # noqa: E501


        :return: The project_name of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateSecretRequest.


        :param project_name: The project_name of this CreateSecretRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rotation_interval(self):
        """Gets the rotation_interval of this CreateSecretRequest.  # noqa: E501


        :return: The rotation_interval of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._rotation_interval

    @rotation_interval.setter
    def rotation_interval(self, rotation_interval):
        """Sets the rotation_interval of this CreateSecretRequest.


        :param rotation_interval: The rotation_interval of this CreateSecretRequest.  # noqa: E501
        :type: str
        """

        self._rotation_interval = rotation_interval

    @property
    def secret_name(self):
        """Gets the secret_name of this CreateSecretRequest.  # noqa: E501


        :return: The secret_name of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._secret_name

    @secret_name.setter
    def secret_name(self, secret_name):
        """Sets the secret_name of this CreateSecretRequest.


        :param secret_name: The secret_name of this CreateSecretRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_name is None:
            raise ValueError("Invalid value for `secret_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_name is not None and len(secret_name) > 128):
            raise ValueError("Invalid value for `secret_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_name is not None and len(secret_name) < 2):
            raise ValueError("Invalid value for `secret_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._secret_name = secret_name

    @property
    def secret_type(self):
        """Gets the secret_type of this CreateSecretRequest.  # noqa: E501


        :return: The secret_type of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._secret_type

    @secret_type.setter
    def secret_type(self, secret_type):
        """Sets the secret_type of this CreateSecretRequest.


        :param secret_type: The secret_type of this CreateSecretRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_type is None:
            raise ValueError("Invalid value for `secret_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Generic", "IAM", "RDS", "Redis", "ECS"]  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_type not in allowed_values):
            raise ValueError(
                "Invalid value for `secret_type` ({0}), must be one of {1}"  # noqa: E501
                .format(secret_type, allowed_values)
            )

        self._secret_type = secret_type

    @property
    def secret_value(self):
        """Gets the secret_value of this CreateSecretRequest.  # noqa: E501


        :return: The secret_value of this CreateSecretRequest.  # noqa: E501
        :rtype: str
        """
        return self._secret_value

    @secret_value.setter
    def secret_value(self, secret_value):
        """Sets the secret_value of this CreateSecretRequest.


        :param secret_value: The secret_value of this CreateSecretRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_value is None:
            raise ValueError("Invalid value for `secret_value`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_value is not None and len(secret_value) > 30720):
            raise ValueError("Invalid value for `secret_value`, length must be less than or equal to `30720`")  # noqa: E501

        self._secret_value = secret_value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateSecretRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateSecretRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateSecretRequest):
            return True

        return self.to_dict() != other.to_dict()
