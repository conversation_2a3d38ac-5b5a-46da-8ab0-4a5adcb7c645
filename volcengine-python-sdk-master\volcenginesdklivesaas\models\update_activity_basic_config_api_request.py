# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateActivityBasicConfigAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'activity_id': 'int',
        'announcement': 'str',
        'auto_reservation_window_enable': 'int',
        'auto_start_type': 'int',
        'background_color': 'str',
        'color_theme_index': 'str',
        'config_version': 'int',
        'cover_image_url': 'str',
        'default_subtitle_language': 'str',
        'end_time': 'int',
        'feedback_message': 'str',
        'font_color': 'str',
        'header_image_url': 'str',
        'interaction_color': 'str',
        'is_announcement_enable': 'int',
        'is_auto_end_enable': 'int',
        'is_auto_start_enable': 'int',
        'is_background_blur': 'int',
        'is_color_sync': 'int',
        'is_comment_translate_enable': 'int',
        'is_count_down_enable': 'int',
        'is_countdown_enable': 'int',
        'is_cover_image_enable': 'int',
        'is_feedback_enable': 'int',
        'is_header_image_enable': 'int',
        'is_language_enable': 'int',
        'is_live_bullet_chat': 'int',
        'is_mobile_back_image_enable': 'int',
        'is_pc_header_image_enable': 'int',
        'is_page_limit_enable': 'int',
        'is_pc_back_image_enable': 'int',
        'is_people_count_enable': 'int',
        'is_player_top_enable': 'int',
        'is_preview_prompt_enable': 'int',
        'is_preview_video_enable': 'int',
        'is_replay_auto_online_enable': 'int',
        'is_replay_bullet_chat': 'int',
        'is_reservation_enable': 'int',
        'is_reservation_sms_enable': 'int',
        'is_share_icon_enable': 'int',
        'is_thumb_up_enable': 'int',
        'is_thumb_up_number_enable': 'int',
        'is_time_shift': 'int',
        'is_watermark_image_enable': 'int',
        'language_type': 'list[int]',
        'live_time': 'int',
        'live_zone': 'int',
        'mobile_back_image_url': 'str',
        'mobile_back_image_url_default': 'str',
        'mobile_background_color': 'str',
        'mobile_chat_background_color': 'str',
        'mobile_login_background_image_url': 'str',
        'name': 'str',
        'open_live_avextractor_task': 'int',
        'pc_header_image_url': 'str',
        'page_limit_type': 'str',
        'pc_back_image_url': 'str',
        'pc_back_image_url_default': 'str',
        'pc_login_background_image_url': 'str',
        'player_top_type': 'list[int]',
        'presenter_chat_color': 'str',
        'preview_prompt': 'str',
        'preview_video_cover_image': 'str',
        'preview_video_id': 'int',
        'preview_video_media_name': 'str',
        'preview_video_review_status': 'int',
        'preview_video_url': 'str',
        'preview_video_vid': 'str',
        'preview_video_vid_default': 'str',
        'reservation_text': 'str',
        'reservation_time': 'int',
        'risk_warning_setting': 'RiskWarningSettingForUpdateActivityBasicConfigAPIInput',
        'share_icon_url': 'str',
        'share_icon_url_default': 'str',
        'site_tags': 'list[SiteTagForUpdateActivityBasicConfigAPIInput]',
        'sms_language': 'int',
        'source_subtitle_language': 'str',
        'text_site_tags': 'list[TextSiteTagForUpdateActivityBasicConfigAPIInput]',
        'thumb_up_effect_urls': 'list[str]',
        'thumb_up_url': 'str',
        'thumb_up_url_default': 'str',
        'use_default_thumb_up_effect': 'int',
        'vertical_cover_image_url': 'str',
        'watermark_image_url': 'str',
        'watermark_position': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'activity_id': 'ActivityId',
        'announcement': 'Announcement',
        'auto_reservation_window_enable': 'AutoReservationWindowEnable',
        'auto_start_type': 'AutoStartType',
        'background_color': 'BackgroundColor',
        'color_theme_index': 'ColorThemeIndex',
        'config_version': 'ConfigVersion',
        'cover_image_url': 'CoverImageUrl',
        'default_subtitle_language': 'DefaultSubtitleLanguage',
        'end_time': 'EndTime',
        'feedback_message': 'FeedbackMessage',
        'font_color': 'FontColor',
        'header_image_url': 'HeaderImageUrl',
        'interaction_color': 'InteractionColor',
        'is_announcement_enable': 'IsAnnouncementEnable',
        'is_auto_end_enable': 'IsAutoEndEnable',
        'is_auto_start_enable': 'IsAutoStartEnable',
        'is_background_blur': 'IsBackgroundBlur',
        'is_color_sync': 'IsColorSync',
        'is_comment_translate_enable': 'IsCommentTranslateEnable',
        'is_count_down_enable': 'IsCountDownEnable',
        'is_countdown_enable': 'IsCountdownEnable',
        'is_cover_image_enable': 'IsCoverImageEnable',
        'is_feedback_enable': 'IsFeedbackEnable',
        'is_header_image_enable': 'IsHeaderImageEnable',
        'is_language_enable': 'IsLanguageEnable',
        'is_live_bullet_chat': 'IsLiveBulletChat',
        'is_mobile_back_image_enable': 'IsMobileBackImageEnable',
        'is_pc_header_image_enable': 'IsPCHeaderImageEnable',
        'is_page_limit_enable': 'IsPageLimitEnable',
        'is_pc_back_image_enable': 'IsPcBackImageEnable',
        'is_people_count_enable': 'IsPeopleCountEnable',
        'is_player_top_enable': 'IsPlayerTopEnable',
        'is_preview_prompt_enable': 'IsPreviewPromptEnable',
        'is_preview_video_enable': 'IsPreviewVideoEnable',
        'is_replay_auto_online_enable': 'IsReplayAutoOnlineEnable',
        'is_replay_bullet_chat': 'IsReplayBulletChat',
        'is_reservation_enable': 'IsReservationEnable',
        'is_reservation_sms_enable': 'IsReservationSmsEnable',
        'is_share_icon_enable': 'IsShareIconEnable',
        'is_thumb_up_enable': 'IsThumbUpEnable',
        'is_thumb_up_number_enable': 'IsThumbUpNumberEnable',
        'is_time_shift': 'IsTimeShift',
        'is_watermark_image_enable': 'IsWatermarkImageEnable',
        'language_type': 'LanguageType',
        'live_time': 'LiveTime',
        'live_zone': 'LiveZone',
        'mobile_back_image_url': 'MobileBackImageUrl',
        'mobile_back_image_url_default': 'MobileBackImageUrlDefault',
        'mobile_background_color': 'MobileBackgroundColor',
        'mobile_chat_background_color': 'MobileChatBackgroundColor',
        'mobile_login_background_image_url': 'MobileLoginBackgroundImageUrl',
        'name': 'Name',
        'open_live_avextractor_task': 'OpenLiveAvextractorTask',
        'pc_header_image_url': 'PCHeaderImageUrl',
        'page_limit_type': 'PageLimitType',
        'pc_back_image_url': 'PcBackImageUrl',
        'pc_back_image_url_default': 'PcBackImageUrlDefault',
        'pc_login_background_image_url': 'PcLoginBackgroundImageUrl',
        'player_top_type': 'PlayerTopType',
        'presenter_chat_color': 'PresenterChatColor',
        'preview_prompt': 'PreviewPrompt',
        'preview_video_cover_image': 'PreviewVideoCoverImage',
        'preview_video_id': 'PreviewVideoId',
        'preview_video_media_name': 'PreviewVideoMediaName',
        'preview_video_review_status': 'PreviewVideoReviewStatus',
        'preview_video_url': 'PreviewVideoUrl',
        'preview_video_vid': 'PreviewVideoVid',
        'preview_video_vid_default': 'PreviewVideoVidDefault',
        'reservation_text': 'ReservationText',
        'reservation_time': 'ReservationTime',
        'risk_warning_setting': 'RiskWarningSetting',
        'share_icon_url': 'ShareIconUrl',
        'share_icon_url_default': 'ShareIconUrlDefault',
        'site_tags': 'SiteTags',
        'sms_language': 'SmsLanguage',
        'source_subtitle_language': 'SourceSubtitleLanguage',
        'text_site_tags': 'TextSiteTags',
        'thumb_up_effect_urls': 'ThumbUpEffectUrls',
        'thumb_up_url': 'ThumbUpUrl',
        'thumb_up_url_default': 'ThumbUpUrlDefault',
        'use_default_thumb_up_effect': 'UseDefaultThumbUpEffect',
        'vertical_cover_image_url': 'VerticalCoverImageUrl',
        'watermark_image_url': 'WatermarkImageUrl',
        'watermark_position': 'WatermarkPosition'
    }

    def __init__(self, account_id=None, activity_id=None, announcement=None, auto_reservation_window_enable=None, auto_start_type=None, background_color=None, color_theme_index=None, config_version=None, cover_image_url=None, default_subtitle_language=None, end_time=None, feedback_message=None, font_color=None, header_image_url=None, interaction_color=None, is_announcement_enable=None, is_auto_end_enable=None, is_auto_start_enable=None, is_background_blur=None, is_color_sync=None, is_comment_translate_enable=None, is_count_down_enable=None, is_countdown_enable=None, is_cover_image_enable=None, is_feedback_enable=None, is_header_image_enable=None, is_language_enable=None, is_live_bullet_chat=None, is_mobile_back_image_enable=None, is_pc_header_image_enable=None, is_page_limit_enable=None, is_pc_back_image_enable=None, is_people_count_enable=None, is_player_top_enable=None, is_preview_prompt_enable=None, is_preview_video_enable=None, is_replay_auto_online_enable=None, is_replay_bullet_chat=None, is_reservation_enable=None, is_reservation_sms_enable=None, is_share_icon_enable=None, is_thumb_up_enable=None, is_thumb_up_number_enable=None, is_time_shift=None, is_watermark_image_enable=None, language_type=None, live_time=None, live_zone=None, mobile_back_image_url=None, mobile_back_image_url_default=None, mobile_background_color=None, mobile_chat_background_color=None, mobile_login_background_image_url=None, name=None, open_live_avextractor_task=None, pc_header_image_url=None, page_limit_type=None, pc_back_image_url=None, pc_back_image_url_default=None, pc_login_background_image_url=None, player_top_type=None, presenter_chat_color=None, preview_prompt=None, preview_video_cover_image=None, preview_video_id=None, preview_video_media_name=None, preview_video_review_status=None, preview_video_url=None, preview_video_vid=None, preview_video_vid_default=None, reservation_text=None, reservation_time=None, risk_warning_setting=None, share_icon_url=None, share_icon_url_default=None, site_tags=None, sms_language=None, source_subtitle_language=None, text_site_tags=None, thumb_up_effect_urls=None, thumb_up_url=None, thumb_up_url_default=None, use_default_thumb_up_effect=None, vertical_cover_image_url=None, watermark_image_url=None, watermark_position=None, _configuration=None):  # noqa: E501
        """UpdateActivityBasicConfigAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._activity_id = None
        self._announcement = None
        self._auto_reservation_window_enable = None
        self._auto_start_type = None
        self._background_color = None
        self._color_theme_index = None
        self._config_version = None
        self._cover_image_url = None
        self._default_subtitle_language = None
        self._end_time = None
        self._feedback_message = None
        self._font_color = None
        self._header_image_url = None
        self._interaction_color = None
        self._is_announcement_enable = None
        self._is_auto_end_enable = None
        self._is_auto_start_enable = None
        self._is_background_blur = None
        self._is_color_sync = None
        self._is_comment_translate_enable = None
        self._is_count_down_enable = None
        self._is_countdown_enable = None
        self._is_cover_image_enable = None
        self._is_feedback_enable = None
        self._is_header_image_enable = None
        self._is_language_enable = None
        self._is_live_bullet_chat = None
        self._is_mobile_back_image_enable = None
        self._is_pc_header_image_enable = None
        self._is_page_limit_enable = None
        self._is_pc_back_image_enable = None
        self._is_people_count_enable = None
        self._is_player_top_enable = None
        self._is_preview_prompt_enable = None
        self._is_preview_video_enable = None
        self._is_replay_auto_online_enable = None
        self._is_replay_bullet_chat = None
        self._is_reservation_enable = None
        self._is_reservation_sms_enable = None
        self._is_share_icon_enable = None
        self._is_thumb_up_enable = None
        self._is_thumb_up_number_enable = None
        self._is_time_shift = None
        self._is_watermark_image_enable = None
        self._language_type = None
        self._live_time = None
        self._live_zone = None
        self._mobile_back_image_url = None
        self._mobile_back_image_url_default = None
        self._mobile_background_color = None
        self._mobile_chat_background_color = None
        self._mobile_login_background_image_url = None
        self._name = None
        self._open_live_avextractor_task = None
        self._pc_header_image_url = None
        self._page_limit_type = None
        self._pc_back_image_url = None
        self._pc_back_image_url_default = None
        self._pc_login_background_image_url = None
        self._player_top_type = None
        self._presenter_chat_color = None
        self._preview_prompt = None
        self._preview_video_cover_image = None
        self._preview_video_id = None
        self._preview_video_media_name = None
        self._preview_video_review_status = None
        self._preview_video_url = None
        self._preview_video_vid = None
        self._preview_video_vid_default = None
        self._reservation_text = None
        self._reservation_time = None
        self._risk_warning_setting = None
        self._share_icon_url = None
        self._share_icon_url_default = None
        self._site_tags = None
        self._sms_language = None
        self._source_subtitle_language = None
        self._text_site_tags = None
        self._thumb_up_effect_urls = None
        self._thumb_up_url = None
        self._thumb_up_url_default = None
        self._use_default_thumb_up_effect = None
        self._vertical_cover_image_url = None
        self._watermark_image_url = None
        self._watermark_position = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        self.activity_id = activity_id
        if announcement is not None:
            self.announcement = announcement
        if auto_reservation_window_enable is not None:
            self.auto_reservation_window_enable = auto_reservation_window_enable
        if auto_start_type is not None:
            self.auto_start_type = auto_start_type
        if background_color is not None:
            self.background_color = background_color
        if color_theme_index is not None:
            self.color_theme_index = color_theme_index
        if config_version is not None:
            self.config_version = config_version
        if cover_image_url is not None:
            self.cover_image_url = cover_image_url
        if default_subtitle_language is not None:
            self.default_subtitle_language = default_subtitle_language
        if end_time is not None:
            self.end_time = end_time
        if feedback_message is not None:
            self.feedback_message = feedback_message
        if font_color is not None:
            self.font_color = font_color
        if header_image_url is not None:
            self.header_image_url = header_image_url
        if interaction_color is not None:
            self.interaction_color = interaction_color
        if is_announcement_enable is not None:
            self.is_announcement_enable = is_announcement_enable
        if is_auto_end_enable is not None:
            self.is_auto_end_enable = is_auto_end_enable
        if is_auto_start_enable is not None:
            self.is_auto_start_enable = is_auto_start_enable
        if is_background_blur is not None:
            self.is_background_blur = is_background_blur
        if is_color_sync is not None:
            self.is_color_sync = is_color_sync
        if is_comment_translate_enable is not None:
            self.is_comment_translate_enable = is_comment_translate_enable
        if is_count_down_enable is not None:
            self.is_count_down_enable = is_count_down_enable
        if is_countdown_enable is not None:
            self.is_countdown_enable = is_countdown_enable
        if is_cover_image_enable is not None:
            self.is_cover_image_enable = is_cover_image_enable
        if is_feedback_enable is not None:
            self.is_feedback_enable = is_feedback_enable
        if is_header_image_enable is not None:
            self.is_header_image_enable = is_header_image_enable
        if is_language_enable is not None:
            self.is_language_enable = is_language_enable
        if is_live_bullet_chat is not None:
            self.is_live_bullet_chat = is_live_bullet_chat
        if is_mobile_back_image_enable is not None:
            self.is_mobile_back_image_enable = is_mobile_back_image_enable
        if is_pc_header_image_enable is not None:
            self.is_pc_header_image_enable = is_pc_header_image_enable
        if is_page_limit_enable is not None:
            self.is_page_limit_enable = is_page_limit_enable
        if is_pc_back_image_enable is not None:
            self.is_pc_back_image_enable = is_pc_back_image_enable
        if is_people_count_enable is not None:
            self.is_people_count_enable = is_people_count_enable
        if is_player_top_enable is not None:
            self.is_player_top_enable = is_player_top_enable
        if is_preview_prompt_enable is not None:
            self.is_preview_prompt_enable = is_preview_prompt_enable
        if is_preview_video_enable is not None:
            self.is_preview_video_enable = is_preview_video_enable
        if is_replay_auto_online_enable is not None:
            self.is_replay_auto_online_enable = is_replay_auto_online_enable
        if is_replay_bullet_chat is not None:
            self.is_replay_bullet_chat = is_replay_bullet_chat
        if is_reservation_enable is not None:
            self.is_reservation_enable = is_reservation_enable
        if is_reservation_sms_enable is not None:
            self.is_reservation_sms_enable = is_reservation_sms_enable
        if is_share_icon_enable is not None:
            self.is_share_icon_enable = is_share_icon_enable
        if is_thumb_up_enable is not None:
            self.is_thumb_up_enable = is_thumb_up_enable
        if is_thumb_up_number_enable is not None:
            self.is_thumb_up_number_enable = is_thumb_up_number_enable
        if is_time_shift is not None:
            self.is_time_shift = is_time_shift
        if is_watermark_image_enable is not None:
            self.is_watermark_image_enable = is_watermark_image_enable
        if language_type is not None:
            self.language_type = language_type
        if live_time is not None:
            self.live_time = live_time
        if live_zone is not None:
            self.live_zone = live_zone
        if mobile_back_image_url is not None:
            self.mobile_back_image_url = mobile_back_image_url
        if mobile_back_image_url_default is not None:
            self.mobile_back_image_url_default = mobile_back_image_url_default
        if mobile_background_color is not None:
            self.mobile_background_color = mobile_background_color
        if mobile_chat_background_color is not None:
            self.mobile_chat_background_color = mobile_chat_background_color
        if mobile_login_background_image_url is not None:
            self.mobile_login_background_image_url = mobile_login_background_image_url
        self.name = name
        if open_live_avextractor_task is not None:
            self.open_live_avextractor_task = open_live_avextractor_task
        if pc_header_image_url is not None:
            self.pc_header_image_url = pc_header_image_url
        if page_limit_type is not None:
            self.page_limit_type = page_limit_type
        if pc_back_image_url is not None:
            self.pc_back_image_url = pc_back_image_url
        if pc_back_image_url_default is not None:
            self.pc_back_image_url_default = pc_back_image_url_default
        if pc_login_background_image_url is not None:
            self.pc_login_background_image_url = pc_login_background_image_url
        if player_top_type is not None:
            self.player_top_type = player_top_type
        if presenter_chat_color is not None:
            self.presenter_chat_color = presenter_chat_color
        if preview_prompt is not None:
            self.preview_prompt = preview_prompt
        if preview_video_cover_image is not None:
            self.preview_video_cover_image = preview_video_cover_image
        if preview_video_id is not None:
            self.preview_video_id = preview_video_id
        if preview_video_media_name is not None:
            self.preview_video_media_name = preview_video_media_name
        if preview_video_review_status is not None:
            self.preview_video_review_status = preview_video_review_status
        if preview_video_url is not None:
            self.preview_video_url = preview_video_url
        if preview_video_vid is not None:
            self.preview_video_vid = preview_video_vid
        if preview_video_vid_default is not None:
            self.preview_video_vid_default = preview_video_vid_default
        if reservation_text is not None:
            self.reservation_text = reservation_text
        if reservation_time is not None:
            self.reservation_time = reservation_time
        if risk_warning_setting is not None:
            self.risk_warning_setting = risk_warning_setting
        if share_icon_url is not None:
            self.share_icon_url = share_icon_url
        if share_icon_url_default is not None:
            self.share_icon_url_default = share_icon_url_default
        if site_tags is not None:
            self.site_tags = site_tags
        if sms_language is not None:
            self.sms_language = sms_language
        if source_subtitle_language is not None:
            self.source_subtitle_language = source_subtitle_language
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags
        if thumb_up_effect_urls is not None:
            self.thumb_up_effect_urls = thumb_up_effect_urls
        if thumb_up_url is not None:
            self.thumb_up_url = thumb_up_url
        if thumb_up_url_default is not None:
            self.thumb_up_url_default = thumb_up_url_default
        if use_default_thumb_up_effect is not None:
            self.use_default_thumb_up_effect = use_default_thumb_up_effect
        if vertical_cover_image_url is not None:
            self.vertical_cover_image_url = vertical_cover_image_url
        if watermark_image_url is not None:
            self.watermark_image_url = watermark_image_url
        if watermark_position is not None:
            self.watermark_position = watermark_position

    @property
    def account_id(self):
        """Gets the account_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The account_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this UpdateActivityBasicConfigAPIRequest.


        :param account_id: The account_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The activity_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateActivityBasicConfigAPIRequest.


        :param activity_id: The activity_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def announcement(self):
        """Gets the announcement of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The announcement of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._announcement

    @announcement.setter
    def announcement(self, announcement):
        """Sets the announcement of this UpdateActivityBasicConfigAPIRequest.


        :param announcement: The announcement of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._announcement = announcement

    @property
    def auto_reservation_window_enable(self):
        """Gets the auto_reservation_window_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The auto_reservation_window_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._auto_reservation_window_enable

    @auto_reservation_window_enable.setter
    def auto_reservation_window_enable(self, auto_reservation_window_enable):
        """Sets the auto_reservation_window_enable of this UpdateActivityBasicConfigAPIRequest.


        :param auto_reservation_window_enable: The auto_reservation_window_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._auto_reservation_window_enable = auto_reservation_window_enable

    @property
    def auto_start_type(self):
        """Gets the auto_start_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The auto_start_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._auto_start_type

    @auto_start_type.setter
    def auto_start_type(self, auto_start_type):
        """Sets the auto_start_type of this UpdateActivityBasicConfigAPIRequest.


        :param auto_start_type: The auto_start_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._auto_start_type = auto_start_type

    @property
    def background_color(self):
        """Gets the background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._background_color

    @background_color.setter
    def background_color(self, background_color):
        """Sets the background_color of this UpdateActivityBasicConfigAPIRequest.


        :param background_color: The background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._background_color = background_color

    @property
    def color_theme_index(self):
        """Gets the color_theme_index of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The color_theme_index of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._color_theme_index

    @color_theme_index.setter
    def color_theme_index(self, color_theme_index):
        """Sets the color_theme_index of this UpdateActivityBasicConfigAPIRequest.


        :param color_theme_index: The color_theme_index of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._color_theme_index = color_theme_index

    @property
    def config_version(self):
        """Gets the config_version of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The config_version of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._config_version

    @config_version.setter
    def config_version(self, config_version):
        """Sets the config_version of this UpdateActivityBasicConfigAPIRequest.


        :param config_version: The config_version of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._config_version = config_version

    @property
    def cover_image_url(self):
        """Gets the cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._cover_image_url

    @cover_image_url.setter
    def cover_image_url(self, cover_image_url):
        """Sets the cover_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param cover_image_url: The cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._cover_image_url = cover_image_url

    @property
    def default_subtitle_language(self):
        """Gets the default_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The default_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._default_subtitle_language

    @default_subtitle_language.setter
    def default_subtitle_language(self, default_subtitle_language):
        """Sets the default_subtitle_language of this UpdateActivityBasicConfigAPIRequest.


        :param default_subtitle_language: The default_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._default_subtitle_language = default_subtitle_language

    @property
    def end_time(self):
        """Gets the end_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The end_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this UpdateActivityBasicConfigAPIRequest.


        :param end_time: The end_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def feedback_message(self):
        """Gets the feedback_message of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The feedback_message of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._feedback_message

    @feedback_message.setter
    def feedback_message(self, feedback_message):
        """Sets the feedback_message of this UpdateActivityBasicConfigAPIRequest.


        :param feedback_message: The feedback_message of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._feedback_message = feedback_message

    @property
    def font_color(self):
        """Gets the font_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The font_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._font_color

    @font_color.setter
    def font_color(self, font_color):
        """Sets the font_color of this UpdateActivityBasicConfigAPIRequest.


        :param font_color: The font_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._font_color = font_color

    @property
    def header_image_url(self):
        """Gets the header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._header_image_url

    @header_image_url.setter
    def header_image_url(self, header_image_url):
        """Sets the header_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param header_image_url: The header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._header_image_url = header_image_url

    @property
    def interaction_color(self):
        """Gets the interaction_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The interaction_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._interaction_color

    @interaction_color.setter
    def interaction_color(self, interaction_color):
        """Sets the interaction_color of this UpdateActivityBasicConfigAPIRequest.


        :param interaction_color: The interaction_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._interaction_color = interaction_color

    @property
    def is_announcement_enable(self):
        """Gets the is_announcement_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_announcement_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_announcement_enable

    @is_announcement_enable.setter
    def is_announcement_enable(self, is_announcement_enable):
        """Sets the is_announcement_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_announcement_enable: The is_announcement_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_announcement_enable = is_announcement_enable

    @property
    def is_auto_end_enable(self):
        """Gets the is_auto_end_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_auto_end_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_end_enable

    @is_auto_end_enable.setter
    def is_auto_end_enable(self, is_auto_end_enable):
        """Sets the is_auto_end_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_auto_end_enable: The is_auto_end_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_auto_end_enable = is_auto_end_enable

    @property
    def is_auto_start_enable(self):
        """Gets the is_auto_start_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_auto_start_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_start_enable

    @is_auto_start_enable.setter
    def is_auto_start_enable(self, is_auto_start_enable):
        """Sets the is_auto_start_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_auto_start_enable: The is_auto_start_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_auto_start_enable = is_auto_start_enable

    @property
    def is_background_blur(self):
        """Gets the is_background_blur of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_background_blur of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_background_blur

    @is_background_blur.setter
    def is_background_blur(self, is_background_blur):
        """Sets the is_background_blur of this UpdateActivityBasicConfigAPIRequest.


        :param is_background_blur: The is_background_blur of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_background_blur = is_background_blur

    @property
    def is_color_sync(self):
        """Gets the is_color_sync of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_color_sync of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_color_sync

    @is_color_sync.setter
    def is_color_sync(self, is_color_sync):
        """Sets the is_color_sync of this UpdateActivityBasicConfigAPIRequest.


        :param is_color_sync: The is_color_sync of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_color_sync = is_color_sync

    @property
    def is_comment_translate_enable(self):
        """Gets the is_comment_translate_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_comment_translate_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_comment_translate_enable

    @is_comment_translate_enable.setter
    def is_comment_translate_enable(self, is_comment_translate_enable):
        """Sets the is_comment_translate_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_comment_translate_enable: The is_comment_translate_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_comment_translate_enable = is_comment_translate_enable

    @property
    def is_count_down_enable(self):
        """Gets the is_count_down_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_count_down_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_count_down_enable

    @is_count_down_enable.setter
    def is_count_down_enable(self, is_count_down_enable):
        """Sets the is_count_down_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_count_down_enable: The is_count_down_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_count_down_enable = is_count_down_enable

    @property
    def is_countdown_enable(self):
        """Gets the is_countdown_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_countdown_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_countdown_enable

    @is_countdown_enable.setter
    def is_countdown_enable(self, is_countdown_enable):
        """Sets the is_countdown_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_countdown_enable: The is_countdown_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_countdown_enable = is_countdown_enable

    @property
    def is_cover_image_enable(self):
        """Gets the is_cover_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_cover_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_cover_image_enable

    @is_cover_image_enable.setter
    def is_cover_image_enable(self, is_cover_image_enable):
        """Sets the is_cover_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_cover_image_enable: The is_cover_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_cover_image_enable = is_cover_image_enable

    @property
    def is_feedback_enable(self):
        """Gets the is_feedback_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_feedback_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_feedback_enable

    @is_feedback_enable.setter
    def is_feedback_enable(self, is_feedback_enable):
        """Sets the is_feedback_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_feedback_enable: The is_feedback_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_feedback_enable = is_feedback_enable

    @property
    def is_header_image_enable(self):
        """Gets the is_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_header_image_enable

    @is_header_image_enable.setter
    def is_header_image_enable(self, is_header_image_enable):
        """Sets the is_header_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_header_image_enable: The is_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_header_image_enable = is_header_image_enable

    @property
    def is_language_enable(self):
        """Gets the is_language_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_language_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_language_enable

    @is_language_enable.setter
    def is_language_enable(self, is_language_enable):
        """Sets the is_language_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_language_enable: The is_language_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_language_enable = is_language_enable

    @property
    def is_live_bullet_chat(self):
        """Gets the is_live_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_live_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_live_bullet_chat

    @is_live_bullet_chat.setter
    def is_live_bullet_chat(self, is_live_bullet_chat):
        """Sets the is_live_bullet_chat of this UpdateActivityBasicConfigAPIRequest.


        :param is_live_bullet_chat: The is_live_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_live_bullet_chat = is_live_bullet_chat

    @property
    def is_mobile_back_image_enable(self):
        """Gets the is_mobile_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_mobile_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_mobile_back_image_enable

    @is_mobile_back_image_enable.setter
    def is_mobile_back_image_enable(self, is_mobile_back_image_enable):
        """Sets the is_mobile_back_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_mobile_back_image_enable: The is_mobile_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_mobile_back_image_enable = is_mobile_back_image_enable

    @property
    def is_pc_header_image_enable(self):
        """Gets the is_pc_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_pc_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_pc_header_image_enable

    @is_pc_header_image_enable.setter
    def is_pc_header_image_enable(self, is_pc_header_image_enable):
        """Sets the is_pc_header_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_pc_header_image_enable: The is_pc_header_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_pc_header_image_enable = is_pc_header_image_enable

    @property
    def is_page_limit_enable(self):
        """Gets the is_page_limit_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_page_limit_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_page_limit_enable

    @is_page_limit_enable.setter
    def is_page_limit_enable(self, is_page_limit_enable):
        """Sets the is_page_limit_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_page_limit_enable: The is_page_limit_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_page_limit_enable = is_page_limit_enable

    @property
    def is_pc_back_image_enable(self):
        """Gets the is_pc_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_pc_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_pc_back_image_enable

    @is_pc_back_image_enable.setter
    def is_pc_back_image_enable(self, is_pc_back_image_enable):
        """Sets the is_pc_back_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_pc_back_image_enable: The is_pc_back_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_pc_back_image_enable = is_pc_back_image_enable

    @property
    def is_people_count_enable(self):
        """Gets the is_people_count_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_people_count_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_people_count_enable

    @is_people_count_enable.setter
    def is_people_count_enable(self, is_people_count_enable):
        """Sets the is_people_count_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_people_count_enable: The is_people_count_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_people_count_enable = is_people_count_enable

    @property
    def is_player_top_enable(self):
        """Gets the is_player_top_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_player_top_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_player_top_enable

    @is_player_top_enable.setter
    def is_player_top_enable(self, is_player_top_enable):
        """Sets the is_player_top_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_player_top_enable: The is_player_top_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_player_top_enable = is_player_top_enable

    @property
    def is_preview_prompt_enable(self):
        """Gets the is_preview_prompt_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_preview_prompt_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_preview_prompt_enable

    @is_preview_prompt_enable.setter
    def is_preview_prompt_enable(self, is_preview_prompt_enable):
        """Sets the is_preview_prompt_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_preview_prompt_enable: The is_preview_prompt_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_preview_prompt_enable = is_preview_prompt_enable

    @property
    def is_preview_video_enable(self):
        """Gets the is_preview_video_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_preview_video_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_preview_video_enable

    @is_preview_video_enable.setter
    def is_preview_video_enable(self, is_preview_video_enable):
        """Sets the is_preview_video_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_preview_video_enable: The is_preview_video_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_preview_video_enable = is_preview_video_enable

    @property
    def is_replay_auto_online_enable(self):
        """Gets the is_replay_auto_online_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_replay_auto_online_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_replay_auto_online_enable

    @is_replay_auto_online_enable.setter
    def is_replay_auto_online_enable(self, is_replay_auto_online_enable):
        """Sets the is_replay_auto_online_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_replay_auto_online_enable: The is_replay_auto_online_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_replay_auto_online_enable = is_replay_auto_online_enable

    @property
    def is_replay_bullet_chat(self):
        """Gets the is_replay_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_replay_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_replay_bullet_chat

    @is_replay_bullet_chat.setter
    def is_replay_bullet_chat(self, is_replay_bullet_chat):
        """Sets the is_replay_bullet_chat of this UpdateActivityBasicConfigAPIRequest.


        :param is_replay_bullet_chat: The is_replay_bullet_chat of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_replay_bullet_chat = is_replay_bullet_chat

    @property
    def is_reservation_enable(self):
        """Gets the is_reservation_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_reservation_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_reservation_enable

    @is_reservation_enable.setter
    def is_reservation_enable(self, is_reservation_enable):
        """Sets the is_reservation_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_reservation_enable: The is_reservation_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_reservation_enable = is_reservation_enable

    @property
    def is_reservation_sms_enable(self):
        """Gets the is_reservation_sms_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_reservation_sms_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_reservation_sms_enable

    @is_reservation_sms_enable.setter
    def is_reservation_sms_enable(self, is_reservation_sms_enable):
        """Sets the is_reservation_sms_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_reservation_sms_enable: The is_reservation_sms_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_reservation_sms_enable = is_reservation_sms_enable

    @property
    def is_share_icon_enable(self):
        """Gets the is_share_icon_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_share_icon_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_share_icon_enable

    @is_share_icon_enable.setter
    def is_share_icon_enable(self, is_share_icon_enable):
        """Sets the is_share_icon_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_share_icon_enable: The is_share_icon_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_share_icon_enable = is_share_icon_enable

    @property
    def is_thumb_up_enable(self):
        """Gets the is_thumb_up_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_thumb_up_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_thumb_up_enable

    @is_thumb_up_enable.setter
    def is_thumb_up_enable(self, is_thumb_up_enable):
        """Sets the is_thumb_up_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_thumb_up_enable: The is_thumb_up_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_thumb_up_enable = is_thumb_up_enable

    @property
    def is_thumb_up_number_enable(self):
        """Gets the is_thumb_up_number_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_thumb_up_number_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_thumb_up_number_enable

    @is_thumb_up_number_enable.setter
    def is_thumb_up_number_enable(self, is_thumb_up_number_enable):
        """Sets the is_thumb_up_number_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_thumb_up_number_enable: The is_thumb_up_number_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_thumb_up_number_enable = is_thumb_up_number_enable

    @property
    def is_time_shift(self):
        """Gets the is_time_shift of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_time_shift of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_time_shift

    @is_time_shift.setter
    def is_time_shift(self, is_time_shift):
        """Sets the is_time_shift of this UpdateActivityBasicConfigAPIRequest.


        :param is_time_shift: The is_time_shift of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_time_shift = is_time_shift

    @property
    def is_watermark_image_enable(self):
        """Gets the is_watermark_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The is_watermark_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_watermark_image_enable

    @is_watermark_image_enable.setter
    def is_watermark_image_enable(self, is_watermark_image_enable):
        """Sets the is_watermark_image_enable of this UpdateActivityBasicConfigAPIRequest.


        :param is_watermark_image_enable: The is_watermark_image_enable of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_watermark_image_enable = is_watermark_image_enable

    @property
    def language_type(self):
        """Gets the language_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The language_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._language_type

    @language_type.setter
    def language_type(self, language_type):
        """Sets the language_type of this UpdateActivityBasicConfigAPIRequest.


        :param language_type: The language_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: list[int]
        """

        self._language_type = language_type

    @property
    def live_time(self):
        """Gets the live_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The live_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this UpdateActivityBasicConfigAPIRequest.


        :param live_time: The live_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def live_zone(self):
        """Gets the live_zone of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The live_zone of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_zone

    @live_zone.setter
    def live_zone(self, live_zone):
        """Sets the live_zone of this UpdateActivityBasicConfigAPIRequest.


        :param live_zone: The live_zone of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_zone = live_zone

    @property
    def mobile_back_image_url(self):
        """Gets the mobile_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The mobile_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_back_image_url

    @mobile_back_image_url.setter
    def mobile_back_image_url(self, mobile_back_image_url):
        """Sets the mobile_back_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param mobile_back_image_url: The mobile_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._mobile_back_image_url = mobile_back_image_url

    @property
    def mobile_back_image_url_default(self):
        """Gets the mobile_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The mobile_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_back_image_url_default

    @mobile_back_image_url_default.setter
    def mobile_back_image_url_default(self, mobile_back_image_url_default):
        """Sets the mobile_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.


        :param mobile_back_image_url_default: The mobile_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._mobile_back_image_url_default = mobile_back_image_url_default

    @property
    def mobile_background_color(self):
        """Gets the mobile_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The mobile_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_background_color

    @mobile_background_color.setter
    def mobile_background_color(self, mobile_background_color):
        """Sets the mobile_background_color of this UpdateActivityBasicConfigAPIRequest.


        :param mobile_background_color: The mobile_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._mobile_background_color = mobile_background_color

    @property
    def mobile_chat_background_color(self):
        """Gets the mobile_chat_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The mobile_chat_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_chat_background_color

    @mobile_chat_background_color.setter
    def mobile_chat_background_color(self, mobile_chat_background_color):
        """Sets the mobile_chat_background_color of this UpdateActivityBasicConfigAPIRequest.


        :param mobile_chat_background_color: The mobile_chat_background_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._mobile_chat_background_color = mobile_chat_background_color

    @property
    def mobile_login_background_image_url(self):
        """Gets the mobile_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The mobile_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_login_background_image_url

    @mobile_login_background_image_url.setter
    def mobile_login_background_image_url(self, mobile_login_background_image_url):
        """Sets the mobile_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param mobile_login_background_image_url: The mobile_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._mobile_login_background_image_url = mobile_login_background_image_url

    @property
    def name(self):
        """Gets the name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateActivityBasicConfigAPIRequest.


        :param name: The name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def open_live_avextractor_task(self):
        """Gets the open_live_avextractor_task of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The open_live_avextractor_task of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._open_live_avextractor_task

    @open_live_avextractor_task.setter
    def open_live_avextractor_task(self, open_live_avextractor_task):
        """Sets the open_live_avextractor_task of this UpdateActivityBasicConfigAPIRequest.


        :param open_live_avextractor_task: The open_live_avextractor_task of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._open_live_avextractor_task = open_live_avextractor_task

    @property
    def pc_header_image_url(self):
        """Gets the pc_header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The pc_header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._pc_header_image_url

    @pc_header_image_url.setter
    def pc_header_image_url(self, pc_header_image_url):
        """Sets the pc_header_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param pc_header_image_url: The pc_header_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._pc_header_image_url = pc_header_image_url

    @property
    def page_limit_type(self):
        """Gets the page_limit_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The page_limit_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_limit_type

    @page_limit_type.setter
    def page_limit_type(self, page_limit_type):
        """Sets the page_limit_type of this UpdateActivityBasicConfigAPIRequest.


        :param page_limit_type: The page_limit_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._page_limit_type = page_limit_type

    @property
    def pc_back_image_url(self):
        """Gets the pc_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The pc_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._pc_back_image_url

    @pc_back_image_url.setter
    def pc_back_image_url(self, pc_back_image_url):
        """Sets the pc_back_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param pc_back_image_url: The pc_back_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._pc_back_image_url = pc_back_image_url

    @property
    def pc_back_image_url_default(self):
        """Gets the pc_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The pc_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._pc_back_image_url_default

    @pc_back_image_url_default.setter
    def pc_back_image_url_default(self, pc_back_image_url_default):
        """Sets the pc_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.


        :param pc_back_image_url_default: The pc_back_image_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._pc_back_image_url_default = pc_back_image_url_default

    @property
    def pc_login_background_image_url(self):
        """Gets the pc_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The pc_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._pc_login_background_image_url

    @pc_login_background_image_url.setter
    def pc_login_background_image_url(self, pc_login_background_image_url):
        """Sets the pc_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param pc_login_background_image_url: The pc_login_background_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._pc_login_background_image_url = pc_login_background_image_url

    @property
    def player_top_type(self):
        """Gets the player_top_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The player_top_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._player_top_type

    @player_top_type.setter
    def player_top_type(self, player_top_type):
        """Sets the player_top_type of this UpdateActivityBasicConfigAPIRequest.


        :param player_top_type: The player_top_type of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: list[int]
        """

        self._player_top_type = player_top_type

    @property
    def presenter_chat_color(self):
        """Gets the presenter_chat_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The presenter_chat_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._presenter_chat_color

    @presenter_chat_color.setter
    def presenter_chat_color(self, presenter_chat_color):
        """Sets the presenter_chat_color of this UpdateActivityBasicConfigAPIRequest.


        :param presenter_chat_color: The presenter_chat_color of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._presenter_chat_color = presenter_chat_color

    @property
    def preview_prompt(self):
        """Gets the preview_prompt of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_prompt of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_prompt

    @preview_prompt.setter
    def preview_prompt(self, preview_prompt):
        """Sets the preview_prompt of this UpdateActivityBasicConfigAPIRequest.


        :param preview_prompt: The preview_prompt of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_prompt = preview_prompt

    @property
    def preview_video_cover_image(self):
        """Gets the preview_video_cover_image of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_cover_image of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_cover_image

    @preview_video_cover_image.setter
    def preview_video_cover_image(self, preview_video_cover_image):
        """Sets the preview_video_cover_image of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_cover_image: The preview_video_cover_image of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_video_cover_image = preview_video_cover_image

    @property
    def preview_video_id(self):
        """Gets the preview_video_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._preview_video_id

    @preview_video_id.setter
    def preview_video_id(self, preview_video_id):
        """Sets the preview_video_id of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_id: The preview_video_id of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._preview_video_id = preview_video_id

    @property
    def preview_video_media_name(self):
        """Gets the preview_video_media_name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_media_name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_media_name

    @preview_video_media_name.setter
    def preview_video_media_name(self, preview_video_media_name):
        """Sets the preview_video_media_name of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_media_name: The preview_video_media_name of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_video_media_name = preview_video_media_name

    @property
    def preview_video_review_status(self):
        """Gets the preview_video_review_status of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_review_status of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._preview_video_review_status

    @preview_video_review_status.setter
    def preview_video_review_status(self, preview_video_review_status):
        """Sets the preview_video_review_status of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_review_status: The preview_video_review_status of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._preview_video_review_status = preview_video_review_status

    @property
    def preview_video_url(self):
        """Gets the preview_video_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_url

    @preview_video_url.setter
    def preview_video_url(self, preview_video_url):
        """Sets the preview_video_url of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_url: The preview_video_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_video_url = preview_video_url

    @property
    def preview_video_vid(self):
        """Gets the preview_video_vid of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_vid of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_vid

    @preview_video_vid.setter
    def preview_video_vid(self, preview_video_vid):
        """Sets the preview_video_vid of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_vid: The preview_video_vid of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_video_vid = preview_video_vid

    @property
    def preview_video_vid_default(self):
        """Gets the preview_video_vid_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The preview_video_vid_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._preview_video_vid_default

    @preview_video_vid_default.setter
    def preview_video_vid_default(self, preview_video_vid_default):
        """Sets the preview_video_vid_default of this UpdateActivityBasicConfigAPIRequest.


        :param preview_video_vid_default: The preview_video_vid_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._preview_video_vid_default = preview_video_vid_default

    @property
    def reservation_text(self):
        """Gets the reservation_text of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The reservation_text of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._reservation_text

    @reservation_text.setter
    def reservation_text(self, reservation_text):
        """Sets the reservation_text of this UpdateActivityBasicConfigAPIRequest.


        :param reservation_text: The reservation_text of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._reservation_text = reservation_text

    @property
    def reservation_time(self):
        """Gets the reservation_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The reservation_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._reservation_time

    @reservation_time.setter
    def reservation_time(self, reservation_time):
        """Sets the reservation_time of this UpdateActivityBasicConfigAPIRequest.


        :param reservation_time: The reservation_time of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._reservation_time = reservation_time

    @property
    def risk_warning_setting(self):
        """Gets the risk_warning_setting of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The risk_warning_setting of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: RiskWarningSettingForUpdateActivityBasicConfigAPIInput
        """
        return self._risk_warning_setting

    @risk_warning_setting.setter
    def risk_warning_setting(self, risk_warning_setting):
        """Sets the risk_warning_setting of this UpdateActivityBasicConfigAPIRequest.


        :param risk_warning_setting: The risk_warning_setting of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: RiskWarningSettingForUpdateActivityBasicConfigAPIInput
        """

        self._risk_warning_setting = risk_warning_setting

    @property
    def share_icon_url(self):
        """Gets the share_icon_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The share_icon_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._share_icon_url

    @share_icon_url.setter
    def share_icon_url(self, share_icon_url):
        """Sets the share_icon_url of this UpdateActivityBasicConfigAPIRequest.


        :param share_icon_url: The share_icon_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._share_icon_url = share_icon_url

    @property
    def share_icon_url_default(self):
        """Gets the share_icon_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The share_icon_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._share_icon_url_default

    @share_icon_url_default.setter
    def share_icon_url_default(self, share_icon_url_default):
        """Sets the share_icon_url_default of this UpdateActivityBasicConfigAPIRequest.


        :param share_icon_url_default: The share_icon_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._share_icon_url_default = share_icon_url_default

    @property
    def site_tags(self):
        """Gets the site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: list[SiteTagForUpdateActivityBasicConfigAPIInput]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this UpdateActivityBasicConfigAPIRequest.


        :param site_tags: The site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: list[SiteTagForUpdateActivityBasicConfigAPIInput]
        """

        self._site_tags = site_tags

    @property
    def sms_language(self):
        """Gets the sms_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The sms_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._sms_language

    @sms_language.setter
    def sms_language(self, sms_language):
        """Sets the sms_language of this UpdateActivityBasicConfigAPIRequest.


        :param sms_language: The sms_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._sms_language = sms_language

    @property
    def source_subtitle_language(self):
        """Gets the source_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The source_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_subtitle_language

    @source_subtitle_language.setter
    def source_subtitle_language(self, source_subtitle_language):
        """Sets the source_subtitle_language of this UpdateActivityBasicConfigAPIRequest.


        :param source_subtitle_language: The source_subtitle_language of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._source_subtitle_language = source_subtitle_language

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The text_site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: list[TextSiteTagForUpdateActivityBasicConfigAPIInput]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this UpdateActivityBasicConfigAPIRequest.


        :param text_site_tags: The text_site_tags of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: list[TextSiteTagForUpdateActivityBasicConfigAPIInput]
        """

        self._text_site_tags = text_site_tags

    @property
    def thumb_up_effect_urls(self):
        """Gets the thumb_up_effect_urls of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The thumb_up_effect_urls of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._thumb_up_effect_urls

    @thumb_up_effect_urls.setter
    def thumb_up_effect_urls(self, thumb_up_effect_urls):
        """Sets the thumb_up_effect_urls of this UpdateActivityBasicConfigAPIRequest.


        :param thumb_up_effect_urls: The thumb_up_effect_urls of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: list[str]
        """

        self._thumb_up_effect_urls = thumb_up_effect_urls

    @property
    def thumb_up_url(self):
        """Gets the thumb_up_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The thumb_up_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._thumb_up_url

    @thumb_up_url.setter
    def thumb_up_url(self, thumb_up_url):
        """Sets the thumb_up_url of this UpdateActivityBasicConfigAPIRequest.


        :param thumb_up_url: The thumb_up_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._thumb_up_url = thumb_up_url

    @property
    def thumb_up_url_default(self):
        """Gets the thumb_up_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The thumb_up_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._thumb_up_url_default

    @thumb_up_url_default.setter
    def thumb_up_url_default(self, thumb_up_url_default):
        """Sets the thumb_up_url_default of this UpdateActivityBasicConfigAPIRequest.


        :param thumb_up_url_default: The thumb_up_url_default of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._thumb_up_url_default = thumb_up_url_default

    @property
    def use_default_thumb_up_effect(self):
        """Gets the use_default_thumb_up_effect of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The use_default_thumb_up_effect of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._use_default_thumb_up_effect

    @use_default_thumb_up_effect.setter
    def use_default_thumb_up_effect(self, use_default_thumb_up_effect):
        """Sets the use_default_thumb_up_effect of this UpdateActivityBasicConfigAPIRequest.


        :param use_default_thumb_up_effect: The use_default_thumb_up_effect of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._use_default_thumb_up_effect = use_default_thumb_up_effect

    @property
    def vertical_cover_image_url(self):
        """Gets the vertical_cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The vertical_cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._vertical_cover_image_url

    @vertical_cover_image_url.setter
    def vertical_cover_image_url(self, vertical_cover_image_url):
        """Sets the vertical_cover_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param vertical_cover_image_url: The vertical_cover_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._vertical_cover_image_url = vertical_cover_image_url

    @property
    def watermark_image_url(self):
        """Gets the watermark_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The watermark_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._watermark_image_url

    @watermark_image_url.setter
    def watermark_image_url(self, watermark_image_url):
        """Sets the watermark_image_url of this UpdateActivityBasicConfigAPIRequest.


        :param watermark_image_url: The watermark_image_url of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: str
        """

        self._watermark_image_url = watermark_image_url

    @property
    def watermark_position(self):
        """Gets the watermark_position of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501


        :return: The watermark_position of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._watermark_position

    @watermark_position.setter
    def watermark_position(self, watermark_position):
        """Sets the watermark_position of this UpdateActivityBasicConfigAPIRequest.


        :param watermark_position: The watermark_position of this UpdateActivityBasicConfigAPIRequest.  # noqa: E501
        :type: int
        """

        self._watermark_position = watermark_position

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateActivityBasicConfigAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateActivityBasicConfigAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateActivityBasicConfigAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
