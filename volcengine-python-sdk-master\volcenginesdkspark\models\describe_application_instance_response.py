# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeApplicationInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'app_draft_id': 'int',
        'application_name': 'str',
        'application_trn': 'str',
        'application_type': 'str',
        'args': 'str',
        'cluster': 'str',
        'conf': 'dict(str, str)',
        'dependency': 'DependencyFordescribeApplicationInstanceOutput',
        'deploy_request': 'DeployRequestFordescribeApplicationInstanceOutput',
        'deployment_id': 'str',
        'duration': 'int',
        'end_time': 'str',
        'engine_version': 'str',
        'image': 'str',
        'instance_id': 'str',
        'jar': 'str',
        'last_check_time': 'str',
        'main_class': 'str',
        'project_id': 'str',
        'rest_url': 'str',
        'sql_text': 'str',
        'start_time': 'str',
        'state': 'str',
        'unique_key': 'str',
        'user_id': 'str',
        'version_id': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'app_draft_id': 'AppDraftId',
        'application_name': 'ApplicationName',
        'application_trn': 'ApplicationTrn',
        'application_type': 'ApplicationType',
        'args': 'Args',
        'cluster': 'Cluster',
        'conf': 'Conf',
        'dependency': 'Dependency',
        'deploy_request': 'DeployRequest',
        'deployment_id': 'DeploymentId',
        'duration': 'Duration',
        'end_time': 'EndTime',
        'engine_version': 'EngineVersion',
        'image': 'Image',
        'instance_id': 'InstanceId',
        'jar': 'Jar',
        'last_check_time': 'LastCheckTime',
        'main_class': 'MainClass',
        'project_id': 'ProjectId',
        'rest_url': 'RestUrl',
        'sql_text': 'SqlText',
        'start_time': 'StartTime',
        'state': 'State',
        'unique_key': 'UniqueKey',
        'user_id': 'UserId',
        'version_id': 'VersionId'
    }

    def __init__(self, account_id=None, app_draft_id=None, application_name=None, application_trn=None, application_type=None, args=None, cluster=None, conf=None, dependency=None, deploy_request=None, deployment_id=None, duration=None, end_time=None, engine_version=None, image=None, instance_id=None, jar=None, last_check_time=None, main_class=None, project_id=None, rest_url=None, sql_text=None, start_time=None, state=None, unique_key=None, user_id=None, version_id=None, _configuration=None):  # noqa: E501
        """DescribeApplicationInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._app_draft_id = None
        self._application_name = None
        self._application_trn = None
        self._application_type = None
        self._args = None
        self._cluster = None
        self._conf = None
        self._dependency = None
        self._deploy_request = None
        self._deployment_id = None
        self._duration = None
        self._end_time = None
        self._engine_version = None
        self._image = None
        self._instance_id = None
        self._jar = None
        self._last_check_time = None
        self._main_class = None
        self._project_id = None
        self._rest_url = None
        self._sql_text = None
        self._start_time = None
        self._state = None
        self._unique_key = None
        self._user_id = None
        self._version_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if app_draft_id is not None:
            self.app_draft_id = app_draft_id
        if application_name is not None:
            self.application_name = application_name
        if application_trn is not None:
            self.application_trn = application_trn
        if application_type is not None:
            self.application_type = application_type
        if args is not None:
            self.args = args
        if cluster is not None:
            self.cluster = cluster
        if conf is not None:
            self.conf = conf
        if dependency is not None:
            self.dependency = dependency
        if deploy_request is not None:
            self.deploy_request = deploy_request
        if deployment_id is not None:
            self.deployment_id = deployment_id
        if duration is not None:
            self.duration = duration
        if end_time is not None:
            self.end_time = end_time
        if engine_version is not None:
            self.engine_version = engine_version
        if image is not None:
            self.image = image
        if instance_id is not None:
            self.instance_id = instance_id
        if jar is not None:
            self.jar = jar
        if last_check_time is not None:
            self.last_check_time = last_check_time
        if main_class is not None:
            self.main_class = main_class
        if project_id is not None:
            self.project_id = project_id
        if rest_url is not None:
            self.rest_url = rest_url
        if sql_text is not None:
            self.sql_text = sql_text
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state
        if unique_key is not None:
            self.unique_key = unique_key
        if user_id is not None:
            self.user_id = user_id
        if version_id is not None:
            self.version_id = version_id

    @property
    def account_id(self):
        """Gets the account_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The account_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeApplicationInstanceResponse.


        :param account_id: The account_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def app_draft_id(self):
        """Gets the app_draft_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The app_draft_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._app_draft_id

    @app_draft_id.setter
    def app_draft_id(self, app_draft_id):
        """Sets the app_draft_id of this DescribeApplicationInstanceResponse.


        :param app_draft_id: The app_draft_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: int
        """

        self._app_draft_id = app_draft_id

    @property
    def application_name(self):
        """Gets the application_name of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The application_name of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this DescribeApplicationInstanceResponse.


        :param application_name: The application_name of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def application_trn(self):
        """Gets the application_trn of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The application_trn of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._application_trn

    @application_trn.setter
    def application_trn(self, application_trn):
        """Sets the application_trn of this DescribeApplicationInstanceResponse.


        :param application_trn: The application_trn of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._application_trn = application_trn

    @property
    def application_type(self):
        """Gets the application_type of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The application_type of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._application_type

    @application_type.setter
    def application_type(self, application_type):
        """Sets the application_type of this DescribeApplicationInstanceResponse.


        :param application_type: The application_type of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._application_type = application_type

    @property
    def args(self):
        """Gets the args of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The args of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._args

    @args.setter
    def args(self, args):
        """Sets the args of this DescribeApplicationInstanceResponse.


        :param args: The args of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._args = args

    @property
    def cluster(self):
        """Gets the cluster of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The cluster of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._cluster

    @cluster.setter
    def cluster(self, cluster):
        """Sets the cluster of this DescribeApplicationInstanceResponse.


        :param cluster: The cluster of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._cluster = cluster

    @property
    def conf(self):
        """Gets the conf of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The conf of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._conf

    @conf.setter
    def conf(self, conf):
        """Sets the conf of this DescribeApplicationInstanceResponse.


        :param conf: The conf of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: dict(str, str)
        """

        self._conf = conf

    @property
    def dependency(self):
        """Gets the dependency of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The dependency of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: DependencyFordescribeApplicationInstanceOutput
        """
        return self._dependency

    @dependency.setter
    def dependency(self, dependency):
        """Sets the dependency of this DescribeApplicationInstanceResponse.


        :param dependency: The dependency of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: DependencyFordescribeApplicationInstanceOutput
        """

        self._dependency = dependency

    @property
    def deploy_request(self):
        """Gets the deploy_request of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The deploy_request of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: DeployRequestFordescribeApplicationInstanceOutput
        """
        return self._deploy_request

    @deploy_request.setter
    def deploy_request(self, deploy_request):
        """Sets the deploy_request of this DescribeApplicationInstanceResponse.


        :param deploy_request: The deploy_request of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: DeployRequestFordescribeApplicationInstanceOutput
        """

        self._deploy_request = deploy_request

    @property
    def deployment_id(self):
        """Gets the deployment_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The deployment_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._deployment_id

    @deployment_id.setter
    def deployment_id(self, deployment_id):
        """Sets the deployment_id of this DescribeApplicationInstanceResponse.


        :param deployment_id: The deployment_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._deployment_id = deployment_id

    @property
    def duration(self):
        """Gets the duration of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The duration of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this DescribeApplicationInstanceResponse.


        :param duration: The duration of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def end_time(self):
        """Gets the end_time of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The end_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeApplicationInstanceResponse.


        :param end_time: The end_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def engine_version(self):
        """Gets the engine_version of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The engine_version of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._engine_version

    @engine_version.setter
    def engine_version(self, engine_version):
        """Sets the engine_version of this DescribeApplicationInstanceResponse.


        :param engine_version: The engine_version of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._engine_version = engine_version

    @property
    def image(self):
        """Gets the image of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The image of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this DescribeApplicationInstanceResponse.


        :param image: The image of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._image = image

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The instance_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeApplicationInstanceResponse.


        :param instance_id: The instance_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def jar(self):
        """Gets the jar of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The jar of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._jar

    @jar.setter
    def jar(self, jar):
        """Sets the jar of this DescribeApplicationInstanceResponse.


        :param jar: The jar of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._jar = jar

    @property
    def last_check_time(self):
        """Gets the last_check_time of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The last_check_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_check_time

    @last_check_time.setter
    def last_check_time(self, last_check_time):
        """Sets the last_check_time of this DescribeApplicationInstanceResponse.


        :param last_check_time: The last_check_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._last_check_time = last_check_time

    @property
    def main_class(self):
        """Gets the main_class of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The main_class of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._main_class

    @main_class.setter
    def main_class(self, main_class):
        """Sets the main_class of this DescribeApplicationInstanceResponse.


        :param main_class: The main_class of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._main_class = main_class

    @property
    def project_id(self):
        """Gets the project_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The project_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this DescribeApplicationInstanceResponse.


        :param project_id: The project_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def rest_url(self):
        """Gets the rest_url of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The rest_url of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._rest_url

    @rest_url.setter
    def rest_url(self, rest_url):
        """Sets the rest_url of this DescribeApplicationInstanceResponse.


        :param rest_url: The rest_url of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._rest_url = rest_url

    @property
    def sql_text(self):
        """Gets the sql_text of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The sql_text of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._sql_text

    @sql_text.setter
    def sql_text(self, sql_text):
        """Sets the sql_text of this DescribeApplicationInstanceResponse.


        :param sql_text: The sql_text of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._sql_text = sql_text

    @property
    def start_time(self):
        """Gets the start_time of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The start_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeApplicationInstanceResponse.


        :param start_time: The start_time of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The state of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DescribeApplicationInstanceResponse.


        :param state: The state of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def unique_key(self):
        """Gets the unique_key of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The unique_key of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._unique_key

    @unique_key.setter
    def unique_key(self, unique_key):
        """Sets the unique_key of this DescribeApplicationInstanceResponse.


        :param unique_key: The unique_key of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._unique_key = unique_key

    @property
    def user_id(self):
        """Gets the user_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The user_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this DescribeApplicationInstanceResponse.


        :param user_id: The user_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    @property
    def version_id(self):
        """Gets the version_id of this DescribeApplicationInstanceResponse.  # noqa: E501


        :return: The version_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._version_id

    @version_id.setter
    def version_id(self, version_id):
        """Sets the version_id of this DescribeApplicationInstanceResponse.


        :param version_id: The version_id of this DescribeApplicationInstanceResponse.  # noqa: E501
        :type: int
        """

        self._version_id = version_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeApplicationInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeApplicationInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeApplicationInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
