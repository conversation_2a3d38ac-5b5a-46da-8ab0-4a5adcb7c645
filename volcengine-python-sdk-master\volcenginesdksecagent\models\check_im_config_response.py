# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckIMConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_pass': 'bool',
        'message': 'str'
    }

    attribute_map = {
        'is_pass': 'IsPass',
        'message': 'Message'
    }

    def __init__(self, is_pass=None, message=None, _configuration=None):  # noqa: E501
        """CheckIMConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_pass = None
        self._message = None
        self.discriminator = None

        if is_pass is not None:
            self.is_pass = is_pass
        if message is not None:
            self.message = message

    @property
    def is_pass(self):
        """Gets the is_pass of this CheckIMConfigResponse.  # noqa: E501


        :return: The is_pass of this CheckIMConfigResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_pass

    @is_pass.setter
    def is_pass(self, is_pass):
        """Sets the is_pass of this CheckIMConfigResponse.


        :param is_pass: The is_pass of this CheckIMConfigResponse.  # noqa: E501
        :type: bool
        """

        self._is_pass = is_pass

    @property
    def message(self):
        """Gets the message of this CheckIMConfigResponse.  # noqa: E501


        :return: The message of this CheckIMConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this CheckIMConfigResponse.


        :param message: The message of this CheckIMConfigResponse.  # noqa: E501
        :type: str
        """

        self._message = message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckIMConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckIMConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckIMConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
