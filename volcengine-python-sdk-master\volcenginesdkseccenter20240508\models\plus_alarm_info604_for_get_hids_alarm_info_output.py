# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo604ForGetHidsAlarmInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'old_uid': 'str',
        'old_username': 'str',
        'pid_tree': 'str'
    }

    attribute_map = {
        'old_uid': 'OldUid',
        'old_username': 'OldUsername',
        'pid_tree': 'PidTree'
    }

    def __init__(self, old_uid=None, old_username=None, pid_tree=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo604ForGetHidsAlarmInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._old_uid = None
        self._old_username = None
        self._pid_tree = None
        self.discriminator = None

        if old_uid is not None:
            self.old_uid = old_uid
        if old_username is not None:
            self.old_username = old_username
        if pid_tree is not None:
            self.pid_tree = pid_tree

    @property
    def old_uid(self):
        """Gets the old_uid of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The old_uid of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_uid

    @old_uid.setter
    def old_uid(self, old_uid):
        """Sets the old_uid of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.


        :param old_uid: The old_uid of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._old_uid = old_uid

    @property
    def old_username(self):
        """Gets the old_username of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The old_username of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_username

    @old_username.setter
    def old_username(self, old_username):
        """Sets the old_username of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.


        :param old_username: The old_username of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._old_username = old_username

    @property
    def pid_tree(self):
        """Gets the pid_tree of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The pid_tree of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_tree

    @pid_tree.setter
    def pid_tree(self, pid_tree):
        """Sets the pid_tree of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.


        :param pid_tree: The pid_tree of this PlusAlarmInfo604ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid_tree = pid_tree

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo604ForGetHidsAlarmInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo604ForGetHidsAlarmInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo604ForGetHidsAlarmInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
