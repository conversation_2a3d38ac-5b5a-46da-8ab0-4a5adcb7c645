# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateHostAccountRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'get_follow_data_url': 'str',
        'host_account': 'HostAccountForUpdateHostAccountInput',
        'update_follow_data_url': 'str'
    }

    attribute_map = {
        'get_follow_data_url': 'GetFollowDataUrl',
        'host_account': 'HostAccount',
        'update_follow_data_url': 'UpdateFollowDataUrl'
    }

    def __init__(self, get_follow_data_url=None, host_account=None, update_follow_data_url=None, _configuration=None):  # noqa: E501
        """UpdateHostAccountRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._get_follow_data_url = None
        self._host_account = None
        self._update_follow_data_url = None
        self.discriminator = None

        if get_follow_data_url is not None:
            self.get_follow_data_url = get_follow_data_url
        if host_account is not None:
            self.host_account = host_account
        if update_follow_data_url is not None:
            self.update_follow_data_url = update_follow_data_url

    @property
    def get_follow_data_url(self):
        """Gets the get_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501


        :return: The get_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._get_follow_data_url

    @get_follow_data_url.setter
    def get_follow_data_url(self, get_follow_data_url):
        """Sets the get_follow_data_url of this UpdateHostAccountRequest.


        :param get_follow_data_url: The get_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501
        :type: str
        """

        self._get_follow_data_url = get_follow_data_url

    @property
    def host_account(self):
        """Gets the host_account of this UpdateHostAccountRequest.  # noqa: E501


        :return: The host_account of this UpdateHostAccountRequest.  # noqa: E501
        :rtype: HostAccountForUpdateHostAccountInput
        """
        return self._host_account

    @host_account.setter
    def host_account(self, host_account):
        """Sets the host_account of this UpdateHostAccountRequest.


        :param host_account: The host_account of this UpdateHostAccountRequest.  # noqa: E501
        :type: HostAccountForUpdateHostAccountInput
        """

        self._host_account = host_account

    @property
    def update_follow_data_url(self):
        """Gets the update_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501


        :return: The update_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_follow_data_url

    @update_follow_data_url.setter
    def update_follow_data_url(self, update_follow_data_url):
        """Sets the update_follow_data_url of this UpdateHostAccountRequest.


        :param update_follow_data_url: The update_follow_data_url of this UpdateHostAccountRequest.  # noqa: E501
        :type: str
        """

        self._update_follow_data_url = update_follow_data_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateHostAccountRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateHostAccountRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateHostAccountRequest):
            return True

        return self.to_dict() != other.to_dict()
