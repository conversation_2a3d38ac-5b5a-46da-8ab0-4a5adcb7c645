# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CancelDataFlowTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_flow_task_id': 'str',
        'file_system_id': 'str'
    }

    attribute_map = {
        'data_flow_task_id': 'DataFlowTaskId',
        'file_system_id': 'FileSystemId'
    }

    def __init__(self, data_flow_task_id=None, file_system_id=None, _configuration=None):  # noqa: E501
        """CancelDataFlowTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_flow_task_id = None
        self._file_system_id = None
        self.discriminator = None

        self.data_flow_task_id = data_flow_task_id
        self.file_system_id = file_system_id

    @property
    def data_flow_task_id(self):
        """Gets the data_flow_task_id of this CancelDataFlowTaskRequest.  # noqa: E501


        :return: The data_flow_task_id of this CancelDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_task_id

    @data_flow_task_id.setter
    def data_flow_task_id(self, data_flow_task_id):
        """Sets the data_flow_task_id of this CancelDataFlowTaskRequest.


        :param data_flow_task_id: The data_flow_task_id of this CancelDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and data_flow_task_id is None:
            raise ValueError("Invalid value for `data_flow_task_id`, must not be `None`")  # noqa: E501

        self._data_flow_task_id = data_flow_task_id

    @property
    def file_system_id(self):
        """Gets the file_system_id of this CancelDataFlowTaskRequest.  # noqa: E501


        :return: The file_system_id of this CancelDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this CancelDataFlowTaskRequest.


        :param file_system_id: The file_system_id of this CancelDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and file_system_id is None:
            raise ValueError("Invalid value for `file_system_id`, must not be `None`")  # noqa: E501

        self._file_system_id = file_system_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CancelDataFlowTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CancelDataFlowTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CancelDataFlowTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
