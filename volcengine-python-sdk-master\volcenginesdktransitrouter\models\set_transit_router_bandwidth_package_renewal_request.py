# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetTransitRouterBandwidthPackageRenewalRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'remain_renew_times': 'int',
        'renew_period': 'int',
        'renew_type': 'str',
        'transit_router_bandwidth_package_id': 'str'
    }

    attribute_map = {
        'remain_renew_times': 'RemainRenewTimes',
        'renew_period': 'RenewPeriod',
        'renew_type': 'RenewType',
        'transit_router_bandwidth_package_id': 'TransitRouterBandwidthPackageId'
    }

    def __init__(self, remain_renew_times=None, renew_period=None, renew_type=None, transit_router_bandwidth_package_id=None, _configuration=None):  # noqa: E501
        """SetTransitRouterBandwidthPackageRenewalRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._remain_renew_times = None
        self._renew_period = None
        self._renew_type = None
        self._transit_router_bandwidth_package_id = None
        self.discriminator = None

        if remain_renew_times is not None:
            self.remain_renew_times = remain_renew_times
        if renew_period is not None:
            self.renew_period = renew_period
        self.renew_type = renew_type
        self.transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    @property
    def remain_renew_times(self):
        """Gets the remain_renew_times of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501


        :return: The remain_renew_times of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :rtype: int
        """
        return self._remain_renew_times

    @remain_renew_times.setter
    def remain_renew_times(self, remain_renew_times):
        """Sets the remain_renew_times of this SetTransitRouterBandwidthPackageRenewalRequest.


        :param remain_renew_times: The remain_renew_times of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :type: int
        """

        self._remain_renew_times = remain_renew_times

    @property
    def renew_period(self):
        """Gets the renew_period of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501


        :return: The renew_period of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :rtype: int
        """
        return self._renew_period

    @renew_period.setter
    def renew_period(self, renew_period):
        """Sets the renew_period of this SetTransitRouterBandwidthPackageRenewalRequest.


        :param renew_period: The renew_period of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :type: int
        """

        self._renew_period = renew_period

    @property
    def renew_type(self):
        """Gets the renew_type of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501


        :return: The renew_type of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :rtype: str
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this SetTransitRouterBandwidthPackageRenewalRequest.


        :param renew_type: The renew_type of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and renew_type is None:
            raise ValueError("Invalid value for `renew_type`, must not be `None`")  # noqa: E501

        self._renew_type = renew_type

    @property
    def transit_router_bandwidth_package_id(self):
        """Gets the transit_router_bandwidth_package_id of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501


        :return: The transit_router_bandwidth_package_id of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_bandwidth_package_id

    @transit_router_bandwidth_package_id.setter
    def transit_router_bandwidth_package_id(self, transit_router_bandwidth_package_id):
        """Sets the transit_router_bandwidth_package_id of this SetTransitRouterBandwidthPackageRenewalRequest.


        :param transit_router_bandwidth_package_id: The transit_router_bandwidth_package_id of this SetTransitRouterBandwidthPackageRenewalRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_bandwidth_package_id is None:
            raise ValueError("Invalid value for `transit_router_bandwidth_package_id`, must not be `None`")  # noqa: E501

        self._transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetTransitRouterBandwidthPackageRenewalRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetTransitRouterBandwidthPackageRenewalRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetTransitRouterBandwidthPackageRenewalRequest):
            return True

        return self.to_dict() != other.to_dict()
