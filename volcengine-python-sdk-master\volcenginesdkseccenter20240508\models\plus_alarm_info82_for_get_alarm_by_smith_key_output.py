# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo82ForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'new_name': 'str',
        'old_name': 'str',
        'pid_tree': 'str'
    }

    attribute_map = {
        'new_name': 'NewName',
        'old_name': 'OldName',
        'pid_tree': 'PidTree'
    }

    def __init__(self, new_name=None, old_name=None, pid_tree=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo82ForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._new_name = None
        self._old_name = None
        self._pid_tree = None
        self.discriminator = None

        if new_name is not None:
            self.new_name = new_name
        if old_name is not None:
            self.old_name = old_name
        if pid_tree is not None:
            self.pid_tree = pid_tree

    @property
    def new_name(self):
        """Gets the new_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The new_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._new_name

    @new_name.setter
    def new_name(self, new_name):
        """Sets the new_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.


        :param new_name: The new_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._new_name = new_name

    @property
    def old_name(self):
        """Gets the old_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The old_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_name

    @old_name.setter
    def old_name(self, old_name):
        """Sets the old_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.


        :param old_name: The old_name of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._old_name = old_name

    @property
    def pid_tree(self):
        """Gets the pid_tree of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The pid_tree of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_tree

    @pid_tree.setter
    def pid_tree(self, pid_tree):
        """Sets the pid_tree of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.


        :param pid_tree: The pid_tree of this PlusAlarmInfo82ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._pid_tree = pid_tree

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo82ForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo82ForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo82ForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
