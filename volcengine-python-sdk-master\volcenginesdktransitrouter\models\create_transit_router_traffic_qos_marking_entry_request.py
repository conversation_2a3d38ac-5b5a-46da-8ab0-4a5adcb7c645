# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTransitRouterTrafficQosMarkingEntryRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'destination_cidr_block': 'str',
        'destination_port_end': 'int',
        'destination_port_start': 'int',
        'match_dscp': 'int',
        'priority': 'int',
        'protocol': 'str',
        'remarking_dscp': 'int',
        'source_cidr_block': 'str',
        'source_port_end': 'int',
        'source_port_start': 'int',
        'transit_router_traffic_qos_marking_entry_name': 'str',
        'transit_router_traffic_qos_marking_policy_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'destination_cidr_block': 'DestinationCidrBlock',
        'destination_port_end': 'DestinationPortEnd',
        'destination_port_start': 'DestinationPortStart',
        'match_dscp': 'MatchDscp',
        'priority': 'Priority',
        'protocol': 'Protocol',
        'remarking_dscp': 'RemarkingDscp',
        'source_cidr_block': 'SourceCidrBlock',
        'source_port_end': 'SourcePortEnd',
        'source_port_start': 'SourcePortStart',
        'transit_router_traffic_qos_marking_entry_name': 'TransitRouterTrafficQosMarkingEntryName',
        'transit_router_traffic_qos_marking_policy_id': 'TransitRouterTrafficQosMarkingPolicyId'
    }

    def __init__(self, client_token=None, description=None, destination_cidr_block=None, destination_port_end=None, destination_port_start=None, match_dscp=None, priority=None, protocol=None, remarking_dscp=None, source_cidr_block=None, source_port_end=None, source_port_start=None, transit_router_traffic_qos_marking_entry_name=None, transit_router_traffic_qos_marking_policy_id=None, _configuration=None):  # noqa: E501
        """CreateTransitRouterTrafficQosMarkingEntryRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._destination_cidr_block = None
        self._destination_port_end = None
        self._destination_port_start = None
        self._match_dscp = None
        self._priority = None
        self._protocol = None
        self._remarking_dscp = None
        self._source_cidr_block = None
        self._source_port_end = None
        self._source_port_start = None
        self._transit_router_traffic_qos_marking_entry_name = None
        self._transit_router_traffic_qos_marking_policy_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.destination_cidr_block = destination_cidr_block
        self.destination_port_end = destination_port_end
        self.destination_port_start = destination_port_start
        if match_dscp is not None:
            self.match_dscp = match_dscp
        self.priority = priority
        if protocol is not None:
            self.protocol = protocol
        self.remarking_dscp = remarking_dscp
        self.source_cidr_block = source_cidr_block
        self.source_port_end = source_port_end
        self.source_port_start = source_port_start
        if transit_router_traffic_qos_marking_entry_name is not None:
            self.transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name
        self.transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The client_token of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param client_token: The client_token of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The description of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param description: The description of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The destination_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param destination_cidr_block: The destination_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and destination_cidr_block is None:
            raise ValueError("Invalid value for `destination_cidr_block`, must not be `None`")  # noqa: E501

        self._destination_cidr_block = destination_cidr_block

    @property
    def destination_port_end(self):
        """Gets the destination_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The destination_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._destination_port_end

    @destination_port_end.setter
    def destination_port_end(self, destination_port_end):
        """Sets the destination_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param destination_port_end: The destination_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and destination_port_end is None:
            raise ValueError("Invalid value for `destination_port_end`, must not be `None`")  # noqa: E501

        self._destination_port_end = destination_port_end

    @property
    def destination_port_start(self):
        """Gets the destination_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The destination_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._destination_port_start

    @destination_port_start.setter
    def destination_port_start(self, destination_port_start):
        """Sets the destination_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param destination_port_start: The destination_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and destination_port_start is None:
            raise ValueError("Invalid value for `destination_port_start`, must not be `None`")  # noqa: E501

        self._destination_port_start = destination_port_start

    @property
    def match_dscp(self):
        """Gets the match_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The match_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._match_dscp

    @match_dscp.setter
    def match_dscp(self, match_dscp):
        """Sets the match_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param match_dscp: The match_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """

        self._match_dscp = match_dscp

    @property
    def priority(self):
        """Gets the priority of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The priority of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param priority: The priority of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and priority is None:
            raise ValueError("Invalid value for `priority`, must not be `None`")  # noqa: E501

        self._priority = priority

    @property
    def protocol(self):
        """Gets the protocol of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The protocol of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param protocol: The protocol of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def remarking_dscp(self):
        """Gets the remarking_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The remarking_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._remarking_dscp

    @remarking_dscp.setter
    def remarking_dscp(self, remarking_dscp):
        """Sets the remarking_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param remarking_dscp: The remarking_dscp of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and remarking_dscp is None:
            raise ValueError("Invalid value for `remarking_dscp`, must not be `None`")  # noqa: E501

        self._remarking_dscp = remarking_dscp

    @property
    def source_cidr_block(self):
        """Gets the source_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The source_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_cidr_block

    @source_cidr_block.setter
    def source_cidr_block(self, source_cidr_block):
        """Sets the source_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param source_cidr_block: The source_cidr_block of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_cidr_block is None:
            raise ValueError("Invalid value for `source_cidr_block`, must not be `None`")  # noqa: E501

        self._source_cidr_block = source_cidr_block

    @property
    def source_port_end(self):
        """Gets the source_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The source_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._source_port_end

    @source_port_end.setter
    def source_port_end(self, source_port_end):
        """Sets the source_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param source_port_end: The source_port_end of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and source_port_end is None:
            raise ValueError("Invalid value for `source_port_end`, must not be `None`")  # noqa: E501

        self._source_port_end = source_port_end

    @property
    def source_port_start(self):
        """Gets the source_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The source_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: int
        """
        return self._source_port_start

    @source_port_start.setter
    def source_port_start(self, source_port_start):
        """Sets the source_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param source_port_start: The source_port_start of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and source_port_start is None:
            raise ValueError("Invalid value for `source_port_start`, must not be `None`")  # noqa: E501

        self._source_port_start = source_port_start

    @property
    def transit_router_traffic_qos_marking_entry_name(self):
        """Gets the transit_router_traffic_qos_marking_entry_name of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_entry_name of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_entry_name

    @transit_router_traffic_qos_marking_entry_name.setter
    def transit_router_traffic_qos_marking_entry_name(self, transit_router_traffic_qos_marking_entry_name):
        """Sets the transit_router_traffic_qos_marking_entry_name of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param transit_router_traffic_qos_marking_entry_name: The transit_router_traffic_qos_marking_entry_name of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name

    @property
    def transit_router_traffic_qos_marking_policy_id(self):
        """Gets the transit_router_traffic_qos_marking_policy_id of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_id of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_id

    @transit_router_traffic_qos_marking_policy_id.setter
    def transit_router_traffic_qos_marking_policy_id(self, transit_router_traffic_qos_marking_policy_id):
        """Sets the transit_router_traffic_qos_marking_policy_id of this CreateTransitRouterTrafficQosMarkingEntryRequest.


        :param transit_router_traffic_qos_marking_policy_id: The transit_router_traffic_qos_marking_policy_id of this CreateTransitRouterTrafficQosMarkingEntryRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_traffic_qos_marking_policy_id is None:
            raise ValueError("Invalid value for `transit_router_traffic_qos_marking_policy_id`, must not be `None`")  # noqa: E501

        self._transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTransitRouterTrafficQosMarkingEntryRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTransitRouterTrafficQosMarkingEntryRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTransitRouterTrafficQosMarkingEntryRequest):
            return True

        return self.to_dict() != other.to_dict()
