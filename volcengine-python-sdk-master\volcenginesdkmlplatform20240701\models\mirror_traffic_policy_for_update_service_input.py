# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MirrorTrafficPolicyForUpdateServiceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'percent': 'int',
        'source_deployment_id': 'str',
        'target_deployment_id': 'str'
    }

    attribute_map = {
        'percent': 'Percent',
        'source_deployment_id': 'SourceDeploymentId',
        'target_deployment_id': 'TargetDeploymentId'
    }

    def __init__(self, percent=None, source_deployment_id=None, target_deployment_id=None, _configuration=None):  # noqa: E501
        """MirrorTrafficPolicyForUpdateServiceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._percent = None
        self._source_deployment_id = None
        self._target_deployment_id = None
        self.discriminator = None

        if percent is not None:
            self.percent = percent
        if source_deployment_id is not None:
            self.source_deployment_id = source_deployment_id
        if target_deployment_id is not None:
            self.target_deployment_id = target_deployment_id

    @property
    def percent(self):
        """Gets the percent of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501


        :return: The percent of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :rtype: int
        """
        return self._percent

    @percent.setter
    def percent(self, percent):
        """Sets the percent of this MirrorTrafficPolicyForUpdateServiceInput.


        :param percent: The percent of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :type: int
        """

        self._percent = percent

    @property
    def source_deployment_id(self):
        """Gets the source_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501


        :return: The source_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._source_deployment_id

    @source_deployment_id.setter
    def source_deployment_id(self, source_deployment_id):
        """Sets the source_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.


        :param source_deployment_id: The source_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :type: str
        """

        self._source_deployment_id = source_deployment_id

    @property
    def target_deployment_id(self):
        """Gets the target_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501


        :return: The target_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._target_deployment_id

    @target_deployment_id.setter
    def target_deployment_id(self, target_deployment_id):
        """Sets the target_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.


        :param target_deployment_id: The target_deployment_id of this MirrorTrafficPolicyForUpdateServiceInput.  # noqa: E501
        :type: str
        """

        self._target_deployment_id = target_deployment_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MirrorTrafficPolicyForUpdateServiceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MirrorTrafficPolicyForUpdateServiceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MirrorTrafficPolicyForUpdateServiceInput):
            return True

        return self.to_dict() != other.to_dict()
