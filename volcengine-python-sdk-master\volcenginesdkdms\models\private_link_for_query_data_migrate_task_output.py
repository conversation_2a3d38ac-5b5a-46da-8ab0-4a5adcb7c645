# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrivateLinkForQueryDataMigrateTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eni_role': 'str',
        'idc_ip': 'str',
        'idc_port': 'int',
        'security_group_ids': 'list[str]',
        'subnet_id': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'eni_role': 'ENIRole',
        'idc_ip': 'IdcIP',
        'idc_port': 'IdcPort',
        'security_group_ids': 'SecurityGroupIDs',
        'subnet_id': 'SubnetID',
        'vpc_id': 'VpcID'
    }

    def __init__(self, eni_role=None, idc_ip=None, idc_port=None, security_group_ids=None, subnet_id=None, vpc_id=None, _configuration=None):  # noqa: E501
        """PrivateLinkForQueryDataMigrateTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eni_role = None
        self._idc_ip = None
        self._idc_port = None
        self._security_group_ids = None
        self._subnet_id = None
        self._vpc_id = None
        self.discriminator = None

        if eni_role is not None:
            self.eni_role = eni_role
        if idc_ip is not None:
            self.idc_ip = idc_ip
        if idc_port is not None:
            self.idc_port = idc_port
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def eni_role(self):
        """Gets the eni_role of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The eni_role of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._eni_role

    @eni_role.setter
    def eni_role(self, eni_role):
        """Sets the eni_role of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param eni_role: The eni_role of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._eni_role = eni_role

    @property
    def idc_ip(self):
        """Gets the idc_ip of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The idc_ip of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._idc_ip

    @idc_ip.setter
    def idc_ip(self, idc_ip):
        """Sets the idc_ip of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param idc_ip: The idc_ip of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._idc_ip = idc_ip

    @property
    def idc_port(self):
        """Gets the idc_port of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The idc_port of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._idc_port

    @idc_port.setter
    def idc_port(self, idc_port):
        """Sets the idc_port of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param idc_port: The idc_port of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._idc_port = idc_port

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The security_group_ids of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param security_group_ids: The security_group_ids of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def subnet_id(self):
        """Gets the subnet_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The subnet_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param subnet_id: The subnet_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The vpc_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this PrivateLinkForQueryDataMigrateTaskOutput.


        :param vpc_id: The vpc_id of this PrivateLinkForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrivateLinkForQueryDataMigrateTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrivateLinkForQueryDataMigrateTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrivateLinkForQueryDataMigrateTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
