# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OrganizationForCertificateGetOrganizationListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account': 'str',
        'address': 'str',
        'business_license_no': 'str',
        'city': 'str',
        'contact': 'ContactForCertificateGetOrganizationListOutput',
        'country': 'str',
        'created_time': 'str',
        'department': 'str',
        'email': 'str',
        'name': 'str',
        'organization_id': 'str',
        'postal_code': 'str',
        'project_name': 'str',
        'province': 'str',
        'tag': 'str',
        'tags': 'list[TagForCertificateGetOrganizationListOutput]',
        'type': 'str'
    }

    attribute_map = {
        'account': 'Account',
        'address': 'Address',
        'business_license_no': 'BusinessLicenseNo',
        'city': 'City',
        'contact': 'Contact',
        'country': 'Country',
        'created_time': 'CreatedTime',
        'department': 'Department',
        'email': 'Email',
        'name': 'Name',
        'organization_id': 'OrganizationId',
        'postal_code': 'PostalCode',
        'project_name': 'ProjectName',
        'province': 'Province',
        'tag': 'Tag',
        'tags': 'Tags',
        'type': 'Type'
    }

    def __init__(self, account=None, address=None, business_license_no=None, city=None, contact=None, country=None, created_time=None, department=None, email=None, name=None, organization_id=None, postal_code=None, project_name=None, province=None, tag=None, tags=None, type=None, _configuration=None):  # noqa: E501
        """OrganizationForCertificateGetOrganizationListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account = None
        self._address = None
        self._business_license_no = None
        self._city = None
        self._contact = None
        self._country = None
        self._created_time = None
        self._department = None
        self._email = None
        self._name = None
        self._organization_id = None
        self._postal_code = None
        self._project_name = None
        self._province = None
        self._tag = None
        self._tags = None
        self._type = None
        self.discriminator = None

        if account is not None:
            self.account = account
        if address is not None:
            self.address = address
        if business_license_no is not None:
            self.business_license_no = business_license_no
        if city is not None:
            self.city = city
        if contact is not None:
            self.contact = contact
        if country is not None:
            self.country = country
        if created_time is not None:
            self.created_time = created_time
        if department is not None:
            self.department = department
        if email is not None:
            self.email = email
        if name is not None:
            self.name = name
        if organization_id is not None:
            self.organization_id = organization_id
        if postal_code is not None:
            self.postal_code = postal_code
        if project_name is not None:
            self.project_name = project_name
        if province is not None:
            self.province = province
        if tag is not None:
            self.tag = tag
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type

    @property
    def account(self):
        """Gets the account of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The account of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._account

    @account.setter
    def account(self, account):
        """Sets the account of this OrganizationForCertificateGetOrganizationListOutput.


        :param account: The account of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._account = account

    @property
    def address(self):
        """Gets the address of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The address of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this OrganizationForCertificateGetOrganizationListOutput.


        :param address: The address of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def business_license_no(self):
        """Gets the business_license_no of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The business_license_no of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_license_no

    @business_license_no.setter
    def business_license_no(self, business_license_no):
        """Sets the business_license_no of this OrganizationForCertificateGetOrganizationListOutput.


        :param business_license_no: The business_license_no of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._business_license_no = business_license_no

    @property
    def city(self):
        """Gets the city of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The city of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this OrganizationForCertificateGetOrganizationListOutput.


        :param city: The city of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def contact(self):
        """Gets the contact of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The contact of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: ContactForCertificateGetOrganizationListOutput
        """
        return self._contact

    @contact.setter
    def contact(self, contact):
        """Sets the contact of this OrganizationForCertificateGetOrganizationListOutput.


        :param contact: The contact of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: ContactForCertificateGetOrganizationListOutput
        """

        self._contact = contact

    @property
    def country(self):
        """Gets the country of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The country of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this OrganizationForCertificateGetOrganizationListOutput.


        :param country: The country of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def created_time(self):
        """Gets the created_time of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The created_time of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this OrganizationForCertificateGetOrganizationListOutput.


        :param created_time: The created_time of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def department(self):
        """Gets the department of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The department of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._department

    @department.setter
    def department(self, department):
        """Sets the department of this OrganizationForCertificateGetOrganizationListOutput.


        :param department: The department of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._department = department

    @property
    def email(self):
        """Gets the email of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The email of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this OrganizationForCertificateGetOrganizationListOutput.


        :param email: The email of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def name(self):
        """Gets the name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this OrganizationForCertificateGetOrganizationListOutput.


        :param name: The name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def organization_id(self):
        """Gets the organization_id of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The organization_id of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._organization_id

    @organization_id.setter
    def organization_id(self, organization_id):
        """Sets the organization_id of this OrganizationForCertificateGetOrganizationListOutput.


        :param organization_id: The organization_id of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._organization_id = organization_id

    @property
    def postal_code(self):
        """Gets the postal_code of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The postal_code of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._postal_code

    @postal_code.setter
    def postal_code(self, postal_code):
        """Sets the postal_code of this OrganizationForCertificateGetOrganizationListOutput.


        :param postal_code: The postal_code of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._postal_code = postal_code

    @property
    def project_name(self):
        """Gets the project_name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The project_name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this OrganizationForCertificateGetOrganizationListOutput.


        :param project_name: The project_name of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def province(self):
        """Gets the province of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The province of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this OrganizationForCertificateGetOrganizationListOutput.


        :param province: The province of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._province = province

    @property
    def tag(self):
        """Gets the tag of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The tag of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this OrganizationForCertificateGetOrganizationListOutput.


        :param tag: The tag of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tags(self):
        """Gets the tags of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The tags of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: list[TagForCertificateGetOrganizationListOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this OrganizationForCertificateGetOrganizationListOutput.


        :param tags: The tags of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: list[TagForCertificateGetOrganizationListOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501


        :return: The type of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this OrganizationForCertificateGetOrganizationListOutput.


        :param type: The type of this OrganizationForCertificateGetOrganizationListOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OrganizationForCertificateGetOrganizationListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OrganizationForCertificateGetOrganizationListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OrganizationForCertificateGetOrganizationListOutput):
            return True

        return self.to_dict() != other.to_dict()
