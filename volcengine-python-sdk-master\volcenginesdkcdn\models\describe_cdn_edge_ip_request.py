# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnEdgeIpRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'ip_version': 'str',
        'isp': 'str',
        'region': 'str',
        'rs_ip': 'bool',
        'status': 'str'
    }

    attribute_map = {
        'domain': 'Domain',
        'ip_version': 'IpVersion',
        'isp': 'Isp',
        'region': 'Region',
        'rs_ip': 'RsIp',
        'status': 'Status'
    }

    def __init__(self, domain=None, ip_version=None, isp=None, region=None, rs_ip=None, status=None, _configuration=None):  # noqa: E501
        """DescribeCdnEdgeIpRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._ip_version = None
        self._isp = None
        self._region = None
        self._rs_ip = None
        self._status = None
        self.discriminator = None

        self.domain = domain
        if ip_version is not None:
            self.ip_version = ip_version
        if isp is not None:
            self.isp = isp
        if region is not None:
            self.region = region
        if rs_ip is not None:
            self.rs_ip = rs_ip
        if status is not None:
            self.status = status

    @property
    def domain(self):
        """Gets the domain of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The domain of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeCdnEdgeIpRequest.


        :param domain: The domain of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def ip_version(self):
        """Gets the ip_version of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The ip_version of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this DescribeCdnEdgeIpRequest.


        :param ip_version: The ip_version of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def isp(self):
        """Gets the isp of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The isp of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DescribeCdnEdgeIpRequest.


        :param isp: The isp of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def region(self):
        """Gets the region of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The region of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DescribeCdnEdgeIpRequest.


        :param region: The region of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def rs_ip(self):
        """Gets the rs_ip of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The rs_ip of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: bool
        """
        return self._rs_ip

    @rs_ip.setter
    def rs_ip(self, rs_ip):
        """Sets the rs_ip of this DescribeCdnEdgeIpRequest.


        :param rs_ip: The rs_ip of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: bool
        """

        self._rs_ip = rs_ip

    @property
    def status(self):
        """Gets the status of this DescribeCdnEdgeIpRequest.  # noqa: E501


        :return: The status of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeCdnEdgeIpRequest.


        :param status: The status of this DescribeCdnEdgeIpRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnEdgeIpRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnEdgeIpRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnEdgeIpRequest):
            return True

        return self.to_dict() != other.to_dict()
