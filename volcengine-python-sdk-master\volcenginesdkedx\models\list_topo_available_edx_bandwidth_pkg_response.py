# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListTopoAvailableEDXBandwidthPkgResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'topo_bandwidth_pkg_list': 'list[TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput]'
    }

    attribute_map = {
        'topo_bandwidth_pkg_list': 'TopoBandwidthPkgList'
    }

    def __init__(self, topo_bandwidth_pkg_list=None, _configuration=None):  # noqa: E501
        """ListTopoAvailableEDXBandwidthPkgResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._topo_bandwidth_pkg_list = None
        self.discriminator = None

        if topo_bandwidth_pkg_list is not None:
            self.topo_bandwidth_pkg_list = topo_bandwidth_pkg_list

    @property
    def topo_bandwidth_pkg_list(self):
        """Gets the topo_bandwidth_pkg_list of this ListTopoAvailableEDXBandwidthPkgResponse.  # noqa: E501


        :return: The topo_bandwidth_pkg_list of this ListTopoAvailableEDXBandwidthPkgResponse.  # noqa: E501
        :rtype: list[TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput]
        """
        return self._topo_bandwidth_pkg_list

    @topo_bandwidth_pkg_list.setter
    def topo_bandwidth_pkg_list(self, topo_bandwidth_pkg_list):
        """Sets the topo_bandwidth_pkg_list of this ListTopoAvailableEDXBandwidthPkgResponse.


        :param topo_bandwidth_pkg_list: The topo_bandwidth_pkg_list of this ListTopoAvailableEDXBandwidthPkgResponse.  # noqa: E501
        :type: list[TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput]
        """

        self._topo_bandwidth_pkg_list = topo_bandwidth_pkg_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListTopoAvailableEDXBandwidthPkgResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListTopoAvailableEDXBandwidthPkgResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListTopoAvailableEDXBandwidthPkgResponse):
            return True

        return self.to_dict() != other.to_dict()
