# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRegistryImagesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'image_id': 'str',
        'latest_flag': 'bool',
        'namespace': 'str',
        'push_time_from': 'int',
        'push_time_to': 'int',
        'registry_name': 'str',
        'repo': 'str',
        'risk': 'RiskForListRegistryImagesInput',
        'risk_flag': 'bool',
        'scan_status': 'list[str]',
        'scan_time_from': 'int',
        'scan_time_to': 'int',
        'tag': 'str'
    }

    attribute_map = {
        'image_id': 'ImageID',
        'latest_flag': 'LatestFlag',
        'namespace': 'Namespace',
        'push_time_from': 'PushTimeFrom',
        'push_time_to': 'PushTimeTo',
        'registry_name': 'RegistryName',
        'repo': 'Repo',
        'risk': 'Risk',
        'risk_flag': 'RiskFlag',
        'scan_status': 'ScanStatus',
        'scan_time_from': 'ScanTimeFrom',
        'scan_time_to': 'ScanTimeTo',
        'tag': 'Tag'
    }

    def __init__(self, image_id=None, latest_flag=None, namespace=None, push_time_from=None, push_time_to=None, registry_name=None, repo=None, risk=None, risk_flag=None, scan_status=None, scan_time_from=None, scan_time_to=None, tag=None, _configuration=None):  # noqa: E501
        """FilterForListRegistryImagesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._image_id = None
        self._latest_flag = None
        self._namespace = None
        self._push_time_from = None
        self._push_time_to = None
        self._registry_name = None
        self._repo = None
        self._risk = None
        self._risk_flag = None
        self._scan_status = None
        self._scan_time_from = None
        self._scan_time_to = None
        self._tag = None
        self.discriminator = None

        if image_id is not None:
            self.image_id = image_id
        if latest_flag is not None:
            self.latest_flag = latest_flag
        if namespace is not None:
            self.namespace = namespace
        if push_time_from is not None:
            self.push_time_from = push_time_from
        if push_time_to is not None:
            self.push_time_to = push_time_to
        if registry_name is not None:
            self.registry_name = registry_name
        if repo is not None:
            self.repo = repo
        if risk is not None:
            self.risk = risk
        if risk_flag is not None:
            self.risk_flag = risk_flag
        if scan_status is not None:
            self.scan_status = scan_status
        if scan_time_from is not None:
            self.scan_time_from = scan_time_from
        if scan_time_to is not None:
            self.scan_time_to = scan_time_to
        if tag is not None:
            self.tag = tag

    @property
    def image_id(self):
        """Gets the image_id of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The image_id of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this FilterForListRegistryImagesInput.


        :param image_id: The image_id of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def latest_flag(self):
        """Gets the latest_flag of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The latest_flag of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: bool
        """
        return self._latest_flag

    @latest_flag.setter
    def latest_flag(self, latest_flag):
        """Sets the latest_flag of this FilterForListRegistryImagesInput.


        :param latest_flag: The latest_flag of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: bool
        """

        self._latest_flag = latest_flag

    @property
    def namespace(self):
        """Gets the namespace of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The namespace of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this FilterForListRegistryImagesInput.


        :param namespace: The namespace of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def push_time_from(self):
        """Gets the push_time_from of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The push_time_from of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: int
        """
        return self._push_time_from

    @push_time_from.setter
    def push_time_from(self, push_time_from):
        """Sets the push_time_from of this FilterForListRegistryImagesInput.


        :param push_time_from: The push_time_from of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: int
        """

        self._push_time_from = push_time_from

    @property
    def push_time_to(self):
        """Gets the push_time_to of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The push_time_to of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: int
        """
        return self._push_time_to

    @push_time_to.setter
    def push_time_to(self, push_time_to):
        """Sets the push_time_to of this FilterForListRegistryImagesInput.


        :param push_time_to: The push_time_to of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: int
        """

        self._push_time_to = push_time_to

    @property
    def registry_name(self):
        """Gets the registry_name of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The registry_name of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: str
        """
        return self._registry_name

    @registry_name.setter
    def registry_name(self, registry_name):
        """Sets the registry_name of this FilterForListRegistryImagesInput.


        :param registry_name: The registry_name of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: str
        """

        self._registry_name = registry_name

    @property
    def repo(self):
        """Gets the repo of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The repo of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: str
        """
        return self._repo

    @repo.setter
    def repo(self, repo):
        """Sets the repo of this FilterForListRegistryImagesInput.


        :param repo: The repo of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: str
        """

        self._repo = repo

    @property
    def risk(self):
        """Gets the risk of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The risk of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: RiskForListRegistryImagesInput
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this FilterForListRegistryImagesInput.


        :param risk: The risk of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: RiskForListRegistryImagesInput
        """

        self._risk = risk

    @property
    def risk_flag(self):
        """Gets the risk_flag of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The risk_flag of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: bool
        """
        return self._risk_flag

    @risk_flag.setter
    def risk_flag(self, risk_flag):
        """Sets the risk_flag of this FilterForListRegistryImagesInput.


        :param risk_flag: The risk_flag of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: bool
        """

        self._risk_flag = risk_flag

    @property
    def scan_status(self):
        """Gets the scan_status of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The scan_status of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._scan_status

    @scan_status.setter
    def scan_status(self, scan_status):
        """Sets the scan_status of this FilterForListRegistryImagesInput.


        :param scan_status: The scan_status of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: list[str]
        """

        self._scan_status = scan_status

    @property
    def scan_time_from(self):
        """Gets the scan_time_from of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The scan_time_from of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: int
        """
        return self._scan_time_from

    @scan_time_from.setter
    def scan_time_from(self, scan_time_from):
        """Sets the scan_time_from of this FilterForListRegistryImagesInput.


        :param scan_time_from: The scan_time_from of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: int
        """

        self._scan_time_from = scan_time_from

    @property
    def scan_time_to(self):
        """Gets the scan_time_to of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The scan_time_to of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: int
        """
        return self._scan_time_to

    @scan_time_to.setter
    def scan_time_to(self, scan_time_to):
        """Sets the scan_time_to of this FilterForListRegistryImagesInput.


        :param scan_time_to: The scan_time_to of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: int
        """

        self._scan_time_to = scan_time_to

    @property
    def tag(self):
        """Gets the tag of this FilterForListRegistryImagesInput.  # noqa: E501


        :return: The tag of this FilterForListRegistryImagesInput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this FilterForListRegistryImagesInput.


        :param tag: The tag of this FilterForListRegistryImagesInput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRegistryImagesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRegistryImagesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRegistryImagesInput):
            return True

        return self.to_dict() != other.to_dict()
