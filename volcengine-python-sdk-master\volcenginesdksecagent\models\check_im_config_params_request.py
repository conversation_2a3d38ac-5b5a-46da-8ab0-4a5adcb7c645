# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckIMConfigParamsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'notify_platform': 'str',
        'signature': 'str',
        'webhook': 'str'
    }

    attribute_map = {
        'notify_platform': 'NotifyPlatform',
        'signature': 'Signature',
        'webhook': 'Webhook'
    }

    def __init__(self, notify_platform=None, signature=None, webhook=None, _configuration=None):  # noqa: E501
        """CheckIMConfigParamsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._notify_platform = None
        self._signature = None
        self._webhook = None
        self.discriminator = None

        self.notify_platform = notify_platform
        self.signature = signature
        self.webhook = webhook

    @property
    def notify_platform(self):
        """Gets the notify_platform of this CheckIMConfigParamsRequest.  # noqa: E501


        :return: The notify_platform of this CheckIMConfigParamsRequest.  # noqa: E501
        :rtype: str
        """
        return self._notify_platform

    @notify_platform.setter
    def notify_platform(self, notify_platform):
        """Sets the notify_platform of this CheckIMConfigParamsRequest.


        :param notify_platform: The notify_platform of this CheckIMConfigParamsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and notify_platform is None:
            raise ValueError("Invalid value for `notify_platform`, must not be `None`")  # noqa: E501

        self._notify_platform = notify_platform

    @property
    def signature(self):
        """Gets the signature of this CheckIMConfigParamsRequest.  # noqa: E501


        :return: The signature of this CheckIMConfigParamsRequest.  # noqa: E501
        :rtype: str
        """
        return self._signature

    @signature.setter
    def signature(self, signature):
        """Sets the signature of this CheckIMConfigParamsRequest.


        :param signature: The signature of this CheckIMConfigParamsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and signature is None:
            raise ValueError("Invalid value for `signature`, must not be `None`")  # noqa: E501

        self._signature = signature

    @property
    def webhook(self):
        """Gets the webhook of this CheckIMConfigParamsRequest.  # noqa: E501


        :return: The webhook of this CheckIMConfigParamsRequest.  # noqa: E501
        :rtype: str
        """
        return self._webhook

    @webhook.setter
    def webhook(self, webhook):
        """Sets the webhook of this CheckIMConfigParamsRequest.


        :param webhook: The webhook of this CheckIMConfigParamsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and webhook is None:
            raise ValueError("Invalid value for `webhook`, must not be `None`")  # noqa: E501

        self._webhook = webhook

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckIMConfigParamsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckIMConfigParamsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckIMConfigParamsRequest):
            return True

        return self.to_dict() != other.to_dict()
