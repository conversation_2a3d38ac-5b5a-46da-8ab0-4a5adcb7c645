# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WatchDataForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'live_count': 'int',
        'rtm_live_data': 'int',
        'watch_live_duration': 'int',
        'watch_page_duration': 'int',
        'watch_preview_duration': 'int',
        'watch_replay_duration': 'int',
        'watch_time': 'int'
    }

    attribute_map = {
        'live_count': 'LiveCount',
        'rtm_live_data': 'RtmLiveData',
        'watch_live_duration': 'WatchLiveDuration',
        'watch_page_duration': 'WatchPageDuration',
        'watch_preview_duration': 'WatchPreviewDuration',
        'watch_replay_duration': 'WatchReplayDuration',
        'watch_time': 'WatchTime'
    }

    def __init__(self, live_count=None, rtm_live_data=None, watch_live_duration=None, watch_page_duration=None, watch_preview_duration=None, watch_replay_duration=None, watch_time=None, _configuration=None):  # noqa: E501
        """WatchDataForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._live_count = None
        self._rtm_live_data = None
        self._watch_live_duration = None
        self._watch_page_duration = None
        self._watch_preview_duration = None
        self._watch_replay_duration = None
        self._watch_time = None
        self.discriminator = None

        if live_count is not None:
            self.live_count = live_count
        if rtm_live_data is not None:
            self.rtm_live_data = rtm_live_data
        if watch_live_duration is not None:
            self.watch_live_duration = watch_live_duration
        if watch_page_duration is not None:
            self.watch_page_duration = watch_page_duration
        if watch_preview_duration is not None:
            self.watch_preview_duration = watch_preview_duration
        if watch_replay_duration is not None:
            self.watch_replay_duration = watch_replay_duration
        if watch_time is not None:
            self.watch_time = watch_time

    @property
    def live_count(self):
        """Gets the live_count of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The live_count of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_count

    @live_count.setter
    def live_count(self, live_count):
        """Sets the live_count of this WatchDataForListAccountUserDataOutput.


        :param live_count: The live_count of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._live_count = live_count

    @property
    def rtm_live_data(self):
        """Gets the rtm_live_data of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The rtm_live_data of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._rtm_live_data

    @rtm_live_data.setter
    def rtm_live_data(self, rtm_live_data):
        """Sets the rtm_live_data of this WatchDataForListAccountUserDataOutput.


        :param rtm_live_data: The rtm_live_data of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._rtm_live_data = rtm_live_data

    @property
    def watch_live_duration(self):
        """Gets the watch_live_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_live_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_live_duration

    @watch_live_duration.setter
    def watch_live_duration(self, watch_live_duration):
        """Sets the watch_live_duration of this WatchDataForListAccountUserDataOutput.


        :param watch_live_duration: The watch_live_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_live_duration = watch_live_duration

    @property
    def watch_page_duration(self):
        """Gets the watch_page_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_page_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_page_duration

    @watch_page_duration.setter
    def watch_page_duration(self, watch_page_duration):
        """Sets the watch_page_duration of this WatchDataForListAccountUserDataOutput.


        :param watch_page_duration: The watch_page_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_page_duration = watch_page_duration

    @property
    def watch_preview_duration(self):
        """Gets the watch_preview_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_preview_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_preview_duration

    @watch_preview_duration.setter
    def watch_preview_duration(self, watch_preview_duration):
        """Sets the watch_preview_duration of this WatchDataForListAccountUserDataOutput.


        :param watch_preview_duration: The watch_preview_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_preview_duration = watch_preview_duration

    @property
    def watch_replay_duration(self):
        """Gets the watch_replay_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_replay_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_replay_duration

    @watch_replay_duration.setter
    def watch_replay_duration(self, watch_replay_duration):
        """Sets the watch_replay_duration of this WatchDataForListAccountUserDataOutput.


        :param watch_replay_duration: The watch_replay_duration of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_replay_duration = watch_replay_duration

    @property
    def watch_time(self):
        """Gets the watch_time of this WatchDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_time of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_time

    @watch_time.setter
    def watch_time(self, watch_time):
        """Sets the watch_time of this WatchDataForListAccountUserDataOutput.


        :param watch_time: The watch_time of this WatchDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_time = watch_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WatchDataForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WatchDataForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WatchDataForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
