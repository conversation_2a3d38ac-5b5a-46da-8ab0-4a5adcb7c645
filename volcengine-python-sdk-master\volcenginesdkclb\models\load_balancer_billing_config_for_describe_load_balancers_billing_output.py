# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_type': 'int',
        'expired_time': 'str',
        'instance_status': 'int',
        'load_balancer_id': 'str',
        'overdue_reclaim_time': 'str',
        'overdue_time': 'str',
        'reclaim_time': 'str',
        'remain_renew_times': 'int',
        'renew_period_times': 'int',
        'renew_type': 'int'
    }

    attribute_map = {
        'billing_type': 'BillingType',
        'expired_time': 'ExpiredTime',
        'instance_status': 'InstanceStatus',
        'load_balancer_id': 'LoadBalancerId',
        'overdue_reclaim_time': 'OverdueReclaimTime',
        'overdue_time': 'OverdueTime',
        'reclaim_time': 'ReclaimTime',
        'remain_renew_times': 'RemainRenewTimes',
        'renew_period_times': 'RenewPeriodTimes',
        'renew_type': 'RenewType'
    }

    def __init__(self, billing_type=None, expired_time=None, instance_status=None, load_balancer_id=None, overdue_reclaim_time=None, overdue_time=None, reclaim_time=None, remain_renew_times=None, renew_period_times=None, renew_type=None, _configuration=None):  # noqa: E501
        """LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_type = None
        self._expired_time = None
        self._instance_status = None
        self._load_balancer_id = None
        self._overdue_reclaim_time = None
        self._overdue_time = None
        self._reclaim_time = None
        self._remain_renew_times = None
        self._renew_period_times = None
        self._renew_type = None
        self.discriminator = None

        if billing_type is not None:
            self.billing_type = billing_type
        if expired_time is not None:
            self.expired_time = expired_time
        if instance_status is not None:
            self.instance_status = instance_status
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if overdue_reclaim_time is not None:
            self.overdue_reclaim_time = overdue_reclaim_time
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if reclaim_time is not None:
            self.reclaim_time = reclaim_time
        if remain_renew_times is not None:
            self.remain_renew_times = remain_renew_times
        if renew_period_times is not None:
            self.renew_period_times = renew_period_times
        if renew_type is not None:
            self.renew_type = renew_type

    @property
    def billing_type(self):
        """Gets the billing_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The billing_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param billing_type: The billing_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def expired_time(self):
        """Gets the expired_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The expired_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param expired_time: The expired_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def instance_status(self):
        """Gets the instance_status of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The instance_status of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param instance_status: The instance_status of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: int
        """

        self._instance_status = instance_status

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The load_balancer_id of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param load_balancer_id: The load_balancer_id of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def overdue_reclaim_time(self):
        """Gets the overdue_reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The overdue_reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._overdue_reclaim_time

    @overdue_reclaim_time.setter
    def overdue_reclaim_time(self, overdue_reclaim_time):
        """Sets the overdue_reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param overdue_reclaim_time: The overdue_reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: str
        """

        self._overdue_reclaim_time = overdue_reclaim_time

    @property
    def overdue_time(self):
        """Gets the overdue_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The overdue_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param overdue_time: The overdue_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def reclaim_time(self):
        """Gets the reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._reclaim_time

    @reclaim_time.setter
    def reclaim_time(self, reclaim_time):
        """Sets the reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param reclaim_time: The reclaim_time of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: str
        """

        self._reclaim_time = reclaim_time

    @property
    def remain_renew_times(self):
        """Gets the remain_renew_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The remain_renew_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._remain_renew_times

    @remain_renew_times.setter
    def remain_renew_times(self, remain_renew_times):
        """Sets the remain_renew_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param remain_renew_times: The remain_renew_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: int
        """

        self._remain_renew_times = remain_renew_times

    @property
    def renew_period_times(self):
        """Gets the renew_period_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The renew_period_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._renew_period_times

    @renew_period_times.setter
    def renew_period_times(self, renew_period_times):
        """Sets the renew_period_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param renew_period_times: The renew_period_times of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: int
        """

        self._renew_period_times = renew_period_times

    @property
    def renew_type(self):
        """Gets the renew_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501


        :return: The renew_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.


        :param renew_type: The renew_type of this LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LoadBalancerBillingConfigForDescribeLoadBalancersBillingOutput):
            return True

        return self.to_dict() != other.to_dict()
