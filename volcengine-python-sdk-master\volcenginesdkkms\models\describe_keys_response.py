# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeKeysResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'keys': 'list[KeyForDescribeKeysOutput]',
        'page_info': 'PageInfoForDescribeKeysOutput'
    }

    attribute_map = {
        'keys': 'Keys',
        'page_info': 'PageInfo'
    }

    def __init__(self, keys=None, page_info=None, _configuration=None):  # noqa: E501
        """DescribeKeysResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._keys = None
        self._page_info = None
        self.discriminator = None

        if keys is not None:
            self.keys = keys
        if page_info is not None:
            self.page_info = page_info

    @property
    def keys(self):
        """Gets the keys of this DescribeKeysResponse.  # noqa: E501


        :return: The keys of this DescribeKeysResponse.  # noqa: E501
        :rtype: list[KeyForDescribeKeysOutput]
        """
        return self._keys

    @keys.setter
    def keys(self, keys):
        """Sets the keys of this DescribeKeysResponse.


        :param keys: The keys of this DescribeKeysResponse.  # noqa: E501
        :type: list[KeyForDescribeKeysOutput]
        """

        self._keys = keys

    @property
    def page_info(self):
        """Gets the page_info of this DescribeKeysResponse.  # noqa: E501


        :return: The page_info of this DescribeKeysResponse.  # noqa: E501
        :rtype: PageInfoForDescribeKeysOutput
        """
        return self._page_info

    @page_info.setter
    def page_info(self, page_info):
        """Sets the page_info of this DescribeKeysResponse.


        :param page_info: The page_info of this DescribeKeysResponse.  # noqa: E501
        :type: PageInfoForDescribeKeysOutput
        """

        self._page_info = page_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeKeysResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeKeysResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeKeysResponse):
            return True

        return self.to_dict() != other.to_dict()
