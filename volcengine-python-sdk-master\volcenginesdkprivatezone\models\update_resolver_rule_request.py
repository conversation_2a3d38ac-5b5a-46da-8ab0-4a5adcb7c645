# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateResolverRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'forward_ips': 'list[ForwardIPForUpdateResolverRuleInput]',
        'line': 'str',
        'name': 'str',
        'rule_id': 'int',
        'rule_trn': 'str',
        'vpcs': 'list[VpcForUpdateResolverRuleInput]'
    }

    attribute_map = {
        'forward_ips': 'ForwardIPs',
        'line': 'Line',
        'name': 'Name',
        'rule_id': 'RuleID',
        'rule_trn': 'RuleTrn',
        'vpcs': 'Vpcs'
    }

    def __init__(self, forward_ips=None, line=None, name=None, rule_id=None, rule_trn=None, vpcs=None, _configuration=None):  # noqa: E501
        """UpdateResolverRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._forward_ips = None
        self._line = None
        self._name = None
        self._rule_id = None
        self._rule_trn = None
        self._vpcs = None
        self.discriminator = None

        if forward_ips is not None:
            self.forward_ips = forward_ips
        if line is not None:
            self.line = line
        if name is not None:
            self.name = name
        self.rule_id = rule_id
        if rule_trn is not None:
            self.rule_trn = rule_trn
        if vpcs is not None:
            self.vpcs = vpcs

    @property
    def forward_ips(self):
        """Gets the forward_ips of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The forward_ips of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: list[ForwardIPForUpdateResolverRuleInput]
        """
        return self._forward_ips

    @forward_ips.setter
    def forward_ips(self, forward_ips):
        """Sets the forward_ips of this UpdateResolverRuleRequest.


        :param forward_ips: The forward_ips of this UpdateResolverRuleRequest.  # noqa: E501
        :type: list[ForwardIPForUpdateResolverRuleInput]
        """

        self._forward_ips = forward_ips

    @property
    def line(self):
        """Gets the line of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The line of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this UpdateResolverRuleRequest.


        :param line: The line of this UpdateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def name(self):
        """Gets the name of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The name of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateResolverRuleRequest.


        :param name: The name of this UpdateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def rule_id(self):
        """Gets the rule_id of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The rule_id of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this UpdateResolverRuleRequest.


        :param rule_id: The rule_id of this UpdateResolverRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def rule_trn(self):
        """Gets the rule_trn of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The rule_trn of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_trn

    @rule_trn.setter
    def rule_trn(self, rule_trn):
        """Sets the rule_trn of this UpdateResolverRuleRequest.


        :param rule_trn: The rule_trn of this UpdateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._rule_trn = rule_trn

    @property
    def vpcs(self):
        """Gets the vpcs of this UpdateResolverRuleRequest.  # noqa: E501


        :return: The vpcs of this UpdateResolverRuleRequest.  # noqa: E501
        :rtype: list[VpcForUpdateResolverRuleInput]
        """
        return self._vpcs

    @vpcs.setter
    def vpcs(self, vpcs):
        """Sets the vpcs of this UpdateResolverRuleRequest.


        :param vpcs: The vpcs of this UpdateResolverRuleRequest.  # noqa: E501
        :type: list[VpcForUpdateResolverRuleInput]
        """

        self._vpcs = vpcs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateResolverRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateResolverRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateResolverRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
