# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExtensionsForCreateLeafInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_extensions': 'list[CustomExtensionForCreateLeafInstanceInput]',
        'extended_key_usages': 'ExtendedKeyUsagesForCreateLeafInstanceInput',
        'key_usages': 'KeyUsagesForCreateLeafInstanceInput'
    }

    attribute_map = {
        'custom_extensions': 'custom_extensions',
        'extended_key_usages': 'extended_key_usages',
        'key_usages': 'key_usages'
    }

    def __init__(self, custom_extensions=None, extended_key_usages=None, key_usages=None, _configuration=None):  # noqa: E501
        """ExtensionsForCreateLeafInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_extensions = None
        self._extended_key_usages = None
        self._key_usages = None
        self.discriminator = None

        if custom_extensions is not None:
            self.custom_extensions = custom_extensions
        if extended_key_usages is not None:
            self.extended_key_usages = extended_key_usages
        if key_usages is not None:
            self.key_usages = key_usages

    @property
    def custom_extensions(self):
        """Gets the custom_extensions of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501


        :return: The custom_extensions of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[CustomExtensionForCreateLeafInstanceInput]
        """
        return self._custom_extensions

    @custom_extensions.setter
    def custom_extensions(self, custom_extensions):
        """Sets the custom_extensions of this ExtensionsForCreateLeafInstanceInput.


        :param custom_extensions: The custom_extensions of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :type: list[CustomExtensionForCreateLeafInstanceInput]
        """

        self._custom_extensions = custom_extensions

    @property
    def extended_key_usages(self):
        """Gets the extended_key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501


        :return: The extended_key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :rtype: ExtendedKeyUsagesForCreateLeafInstanceInput
        """
        return self._extended_key_usages

    @extended_key_usages.setter
    def extended_key_usages(self, extended_key_usages):
        """Sets the extended_key_usages of this ExtensionsForCreateLeafInstanceInput.


        :param extended_key_usages: The extended_key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :type: ExtendedKeyUsagesForCreateLeafInstanceInput
        """

        self._extended_key_usages = extended_key_usages

    @property
    def key_usages(self):
        """Gets the key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501


        :return: The key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :rtype: KeyUsagesForCreateLeafInstanceInput
        """
        return self._key_usages

    @key_usages.setter
    def key_usages(self, key_usages):
        """Sets the key_usages of this ExtensionsForCreateLeafInstanceInput.


        :param key_usages: The key_usages of this ExtensionsForCreateLeafInstanceInput.  # noqa: E501
        :type: KeyUsagesForCreateLeafInstanceInput
        """

        self._key_usages = key_usages

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExtensionsForCreateLeafInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExtensionsForCreateLeafInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExtensionsForCreateLeafInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
