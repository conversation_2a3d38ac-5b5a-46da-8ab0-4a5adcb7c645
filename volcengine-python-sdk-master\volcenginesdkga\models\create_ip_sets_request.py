# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateIPSetsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'ip_sets': 'list[IPSetForCreateIPSetsInput]'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'ip_sets': 'IPSets'
    }

    def __init__(self, accelerator_id=None, ip_sets=None, _configuration=None):  # noqa: E501
        """CreateIPSetsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._ip_sets = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        if ip_sets is not None:
            self.ip_sets = ip_sets

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this CreateIPSetsRequest.  # noqa: E501


        :return: The accelerator_id of this CreateIPSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this CreateIPSetsRequest.


        :param accelerator_id: The accelerator_id of this CreateIPSetsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def ip_sets(self):
        """Gets the ip_sets of this CreateIPSetsRequest.  # noqa: E501


        :return: The ip_sets of this CreateIPSetsRequest.  # noqa: E501
        :rtype: list[IPSetForCreateIPSetsInput]
        """
        return self._ip_sets

    @ip_sets.setter
    def ip_sets(self, ip_sets):
        """Sets the ip_sets of this CreateIPSetsRequest.


        :param ip_sets: The ip_sets of this CreateIPSetsRequest.  # noqa: E501
        :type: list[IPSetForCreateIPSetsInput]
        """

        self._ip_sets = ip_sets

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateIPSetsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateIPSetsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateIPSetsRequest):
            return True

        return self.to_dict() != other.to_dict()
