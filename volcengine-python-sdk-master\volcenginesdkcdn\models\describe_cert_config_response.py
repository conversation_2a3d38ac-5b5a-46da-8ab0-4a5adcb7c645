# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCertConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_not_config': 'list[CertNotConfigForDescribeCertConfigOutput]',
        'other_cert_config': 'list[OtherCertConfigForDescribeCertConfigOutput]',
        'specified_cert_config': 'list[SpecifiedCertConfigForDescribeCertConfigOutput]'
    }

    attribute_map = {
        'cert_not_config': 'CertNotConfig',
        'other_cert_config': 'OtherCertConfig',
        'specified_cert_config': 'SpecifiedCertConfig'
    }

    def __init__(self, cert_not_config=None, other_cert_config=None, specified_cert_config=None, _configuration=None):  # noqa: E501
        """DescribeCertConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_not_config = None
        self._other_cert_config = None
        self._specified_cert_config = None
        self.discriminator = None

        if cert_not_config is not None:
            self.cert_not_config = cert_not_config
        if other_cert_config is not None:
            self.other_cert_config = other_cert_config
        if specified_cert_config is not None:
            self.specified_cert_config = specified_cert_config

    @property
    def cert_not_config(self):
        """Gets the cert_not_config of this DescribeCertConfigResponse.  # noqa: E501


        :return: The cert_not_config of this DescribeCertConfigResponse.  # noqa: E501
        :rtype: list[CertNotConfigForDescribeCertConfigOutput]
        """
        return self._cert_not_config

    @cert_not_config.setter
    def cert_not_config(self, cert_not_config):
        """Sets the cert_not_config of this DescribeCertConfigResponse.


        :param cert_not_config: The cert_not_config of this DescribeCertConfigResponse.  # noqa: E501
        :type: list[CertNotConfigForDescribeCertConfigOutput]
        """

        self._cert_not_config = cert_not_config

    @property
    def other_cert_config(self):
        """Gets the other_cert_config of this DescribeCertConfigResponse.  # noqa: E501


        :return: The other_cert_config of this DescribeCertConfigResponse.  # noqa: E501
        :rtype: list[OtherCertConfigForDescribeCertConfigOutput]
        """
        return self._other_cert_config

    @other_cert_config.setter
    def other_cert_config(self, other_cert_config):
        """Sets the other_cert_config of this DescribeCertConfigResponse.


        :param other_cert_config: The other_cert_config of this DescribeCertConfigResponse.  # noqa: E501
        :type: list[OtherCertConfigForDescribeCertConfigOutput]
        """

        self._other_cert_config = other_cert_config

    @property
    def specified_cert_config(self):
        """Gets the specified_cert_config of this DescribeCertConfigResponse.  # noqa: E501


        :return: The specified_cert_config of this DescribeCertConfigResponse.  # noqa: E501
        :rtype: list[SpecifiedCertConfigForDescribeCertConfigOutput]
        """
        return self._specified_cert_config

    @specified_cert_config.setter
    def specified_cert_config(self, specified_cert_config):
        """Sets the specified_cert_config of this DescribeCertConfigResponse.


        :param specified_cert_config: The specified_cert_config of this DescribeCertConfigResponse.  # noqa: E501
        :type: list[SpecifiedCertConfigForDescribeCertConfigOutput]
        """

        self._specified_cert_config = specified_cert_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCertConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCertConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCertConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
