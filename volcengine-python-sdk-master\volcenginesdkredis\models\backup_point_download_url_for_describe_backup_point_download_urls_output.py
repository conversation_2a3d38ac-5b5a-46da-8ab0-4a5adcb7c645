# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'private_download_url': 'str',
        'public_download_url': 'str',
        'rdb_file_size': 'int',
        'shard_id': 'str'
    }

    attribute_map = {
        'private_download_url': 'PrivateDownloadUrl',
        'public_download_url': 'PublicDownloadUrl',
        'rdb_file_size': 'RdbFileSize',
        'shard_id': 'ShardId'
    }

    def __init__(self, private_download_url=None, public_download_url=None, rdb_file_size=None, shard_id=None, _configuration=None):  # noqa: E501
        """BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._private_download_url = None
        self._public_download_url = None
        self._rdb_file_size = None
        self._shard_id = None
        self.discriminator = None

        if private_download_url is not None:
            self.private_download_url = private_download_url
        if public_download_url is not None:
            self.public_download_url = public_download_url
        if rdb_file_size is not None:
            self.rdb_file_size = rdb_file_size
        if shard_id is not None:
            self.shard_id = shard_id

    @property
    def private_download_url(self):
        """Gets the private_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501


        :return: The private_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_download_url

    @private_download_url.setter
    def private_download_url(self, private_download_url):
        """Sets the private_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.


        :param private_download_url: The private_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :type: str
        """

        self._private_download_url = private_download_url

    @property
    def public_download_url(self):
        """Gets the public_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501


        :return: The public_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_download_url

    @public_download_url.setter
    def public_download_url(self, public_download_url):
        """Sets the public_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.


        :param public_download_url: The public_download_url of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :type: str
        """

        self._public_download_url = public_download_url

    @property
    def rdb_file_size(self):
        """Gets the rdb_file_size of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501


        :return: The rdb_file_size of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rdb_file_size

    @rdb_file_size.setter
    def rdb_file_size(self, rdb_file_size):
        """Sets the rdb_file_size of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.


        :param rdb_file_size: The rdb_file_size of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :type: int
        """

        self._rdb_file_size = rdb_file_size

    @property
    def shard_id(self):
        """Gets the shard_id of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501


        :return: The shard_id of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :rtype: str
        """
        return self._shard_id

    @shard_id.setter
    def shard_id(self, shard_id):
        """Sets the shard_id of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.


        :param shard_id: The shard_id of this BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput.  # noqa: E501
        :type: str
        """

        self._shard_id = shard_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupPointDownloadUrlForDescribeBackupPointDownloadUrlsOutput):
            return True

        return self.to_dict() != other.to_dict()
