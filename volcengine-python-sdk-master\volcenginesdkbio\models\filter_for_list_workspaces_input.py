# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListWorkspacesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ids': 'list[str]',
        'is_public': 'bool',
        'keyword': 'str',
        'labels': 'list[str]'
    }

    attribute_map = {
        'ids': 'IDs',
        'is_public': 'IsPublic',
        'keyword': 'Keyword',
        'labels': 'Labels'
    }

    def __init__(self, ids=None, is_public=None, keyword=None, labels=None, _configuration=None):  # noqa: E501
        """FilterForListWorkspacesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ids = None
        self._is_public = None
        self._keyword = None
        self._labels = None
        self.discriminator = None

        if ids is not None:
            self.ids = ids
        if is_public is not None:
            self.is_public = is_public
        if keyword is not None:
            self.keyword = keyword
        if labels is not None:
            self.labels = labels

    @property
    def ids(self):
        """Gets the ids of this FilterForListWorkspacesInput.  # noqa: E501


        :return: The ids of this FilterForListWorkspacesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListWorkspacesInput.


        :param ids: The ids of this FilterForListWorkspacesInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def is_public(self):
        """Gets the is_public of this FilterForListWorkspacesInput.  # noqa: E501


        :return: The is_public of this FilterForListWorkspacesInput.  # noqa: E501
        :rtype: bool
        """
        return self._is_public

    @is_public.setter
    def is_public(self, is_public):
        """Sets the is_public of this FilterForListWorkspacesInput.


        :param is_public: The is_public of this FilterForListWorkspacesInput.  # noqa: E501
        :type: bool
        """

        self._is_public = is_public

    @property
    def keyword(self):
        """Gets the keyword of this FilterForListWorkspacesInput.  # noqa: E501


        :return: The keyword of this FilterForListWorkspacesInput.  # noqa: E501
        :rtype: str
        """
        return self._keyword

    @keyword.setter
    def keyword(self, keyword):
        """Sets the keyword of this FilterForListWorkspacesInput.


        :param keyword: The keyword of this FilterForListWorkspacesInput.  # noqa: E501
        :type: str
        """

        self._keyword = keyword

    @property
    def labels(self):
        """Gets the labels of this FilterForListWorkspacesInput.  # noqa: E501


        :return: The labels of this FilterForListWorkspacesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this FilterForListWorkspacesInput.


        :param labels: The labels of this FilterForListWorkspacesInput.  # noqa: E501
        :type: list[str]
        """

        self._labels = labels

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListWorkspacesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListWorkspacesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListWorkspacesInput):
            return True

        return self.to_dict() != other.to_dict()
