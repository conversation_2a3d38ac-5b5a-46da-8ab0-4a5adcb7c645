# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAuthorizedUserRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'auth_type': 'int',
        'verify_code': 'int'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'auth_type': 'AuthType',
        'verify_code': 'VerifyCode'
    }

    def __init__(self, account_id=None, auth_type=None, verify_code=None, _configuration=None):  # noqa: E501
        """CreateAuthorizedUserRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._auth_type = None
        self._verify_code = None
        self.discriminator = None

        self.account_id = account_id
        self.auth_type = auth_type
        if verify_code is not None:
            self.verify_code = verify_code

    @property
    def account_id(self):
        """Gets the account_id of this CreateAuthorizedUserRequest.  # noqa: E501


        :return: The account_id of this CreateAuthorizedUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this CreateAuthorizedUserRequest.


        :param account_id: The account_id of this CreateAuthorizedUserRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_id is None:
            raise ValueError("Invalid value for `account_id`, must not be `None`")  # noqa: E501

        self._account_id = account_id

    @property
    def auth_type(self):
        """Gets the auth_type of this CreateAuthorizedUserRequest.  # noqa: E501


        :return: The auth_type of this CreateAuthorizedUserRequest.  # noqa: E501
        :rtype: int
        """
        return self._auth_type

    @auth_type.setter
    def auth_type(self, auth_type):
        """Sets the auth_type of this CreateAuthorizedUserRequest.


        :param auth_type: The auth_type of this CreateAuthorizedUserRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and auth_type is None:
            raise ValueError("Invalid value for `auth_type`, must not be `None`")  # noqa: E501

        self._auth_type = auth_type

    @property
    def verify_code(self):
        """Gets the verify_code of this CreateAuthorizedUserRequest.  # noqa: E501


        :return: The verify_code of this CreateAuthorizedUserRequest.  # noqa: E501
        :rtype: int
        """
        return self._verify_code

    @verify_code.setter
    def verify_code(self, verify_code):
        """Sets the verify_code of this CreateAuthorizedUserRequest.


        :param verify_code: The verify_code of this CreateAuthorizedUserRequest.  # noqa: E501
        :type: int
        """

        self._verify_code = verify_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAuthorizedUserRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAuthorizedUserRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAuthorizedUserRequest):
            return True

        return self.to_dict() != other.to_dict()
