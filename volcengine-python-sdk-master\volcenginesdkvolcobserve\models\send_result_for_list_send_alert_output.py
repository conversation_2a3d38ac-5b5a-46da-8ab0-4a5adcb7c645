# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SendResultForListSendAlertOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_method': 'str',
        'alert_method_val': 'str',
        'err_msg': 'str',
        'receiver': 'str',
        'success': 'bool'
    }

    attribute_map = {
        'alert_method': 'AlertMethod',
        'alert_method_val': 'AlertMethodVal',
        'err_msg': 'ErrMsg',
        'receiver': 'Receiver',
        'success': 'Success'
    }

    def __init__(self, alert_method=None, alert_method_val=None, err_msg=None, receiver=None, success=None, _configuration=None):  # noqa: E501
        """SendResultForListSendAlertOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_method = None
        self._alert_method_val = None
        self._err_msg = None
        self._receiver = None
        self._success = None
        self.discriminator = None

        if alert_method is not None:
            self.alert_method = alert_method
        if alert_method_val is not None:
            self.alert_method_val = alert_method_val
        if err_msg is not None:
            self.err_msg = err_msg
        if receiver is not None:
            self.receiver = receiver
        if success is not None:
            self.success = success

    @property
    def alert_method(self):
        """Gets the alert_method of this SendResultForListSendAlertOutput.  # noqa: E501


        :return: The alert_method of this SendResultForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_method

    @alert_method.setter
    def alert_method(self, alert_method):
        """Sets the alert_method of this SendResultForListSendAlertOutput.


        :param alert_method: The alert_method of this SendResultForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._alert_method = alert_method

    @property
    def alert_method_val(self):
        """Gets the alert_method_val of this SendResultForListSendAlertOutput.  # noqa: E501


        :return: The alert_method_val of this SendResultForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_method_val

    @alert_method_val.setter
    def alert_method_val(self, alert_method_val):
        """Sets the alert_method_val of this SendResultForListSendAlertOutput.


        :param alert_method_val: The alert_method_val of this SendResultForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._alert_method_val = alert_method_val

    @property
    def err_msg(self):
        """Gets the err_msg of this SendResultForListSendAlertOutput.  # noqa: E501


        :return: The err_msg of this SendResultForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_msg

    @err_msg.setter
    def err_msg(self, err_msg):
        """Sets the err_msg of this SendResultForListSendAlertOutput.


        :param err_msg: The err_msg of this SendResultForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._err_msg = err_msg

    @property
    def receiver(self):
        """Gets the receiver of this SendResultForListSendAlertOutput.  # noqa: E501


        :return: The receiver of this SendResultForListSendAlertOutput.  # noqa: E501
        :rtype: str
        """
        return self._receiver

    @receiver.setter
    def receiver(self, receiver):
        """Sets the receiver of this SendResultForListSendAlertOutput.


        :param receiver: The receiver of this SendResultForListSendAlertOutput.  # noqa: E501
        :type: str
        """

        self._receiver = receiver

    @property
    def success(self):
        """Gets the success of this SendResultForListSendAlertOutput.  # noqa: E501


        :return: The success of this SendResultForListSendAlertOutput.  # noqa: E501
        :rtype: bool
        """
        return self._success

    @success.setter
    def success(self, success):
        """Sets the success of this SendResultForListSendAlertOutput.


        :param success: The success of this SendResultForListSendAlertOutput.  # noqa: E501
        :type: bool
        """

        self._success = success

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SendResultForListSendAlertOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SendResultForListSendAlertOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SendResultForListSendAlertOutput):
            return True

        return self.to_dict() != other.to_dict()
