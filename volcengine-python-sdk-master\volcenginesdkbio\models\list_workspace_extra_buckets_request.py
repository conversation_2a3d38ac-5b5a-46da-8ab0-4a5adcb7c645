# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListWorkspaceExtraBucketsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter': 'FilterForListWorkspaceExtraBucketsInput',
        'id': 'str',
        'sort_by': 'str',
        'sort_order': 'str'
    }

    attribute_map = {
        'filter': 'Filter',
        'id': 'ID',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder'
    }

    def __init__(self, filter=None, id=None, sort_by=None, sort_order=None, _configuration=None):  # noqa: E501
        """ListWorkspaceExtraBucketsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter = None
        self._id = None
        self._sort_by = None
        self._sort_order = None
        self.discriminator = None

        if filter is not None:
            self.filter = filter
        self.id = id
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order

    @property
    def filter(self):
        """Gets the filter of this ListWorkspaceExtraBucketsRequest.  # noqa: E501


        :return: The filter of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :rtype: FilterForListWorkspaceExtraBucketsInput
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this ListWorkspaceExtraBucketsRequest.


        :param filter: The filter of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :type: FilterForListWorkspaceExtraBucketsInput
        """

        self._filter = filter

    @property
    def id(self):
        """Gets the id of this ListWorkspaceExtraBucketsRequest.  # noqa: E501


        :return: The id of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ListWorkspaceExtraBucketsRequest.


        :param id: The id of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def sort_by(self):
        """Gets the sort_by of this ListWorkspaceExtraBucketsRequest.  # noqa: E501


        :return: The sort_by of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListWorkspaceExtraBucketsRequest.


        :param sort_by: The sort_by of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["BucketName", "AttachTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListWorkspaceExtraBucketsRequest.  # noqa: E501


        :return: The sort_order of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListWorkspaceExtraBucketsRequest.


        :param sort_order: The sort_order of this ListWorkspaceExtraBucketsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Asc", "Desc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListWorkspaceExtraBucketsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListWorkspaceExtraBucketsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListWorkspaceExtraBucketsRequest):
            return True

        return self.to_dict() != other.to_dict()
