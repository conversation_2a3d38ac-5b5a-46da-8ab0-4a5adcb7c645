# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatisticsQuestionnaireForListQuestionnaireDataAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_delete': 'bool',
        'is_real_name_enable': 'int',
        'publish_time': 'int',
        'question_count': 'int',
        'questionnaire_id': 'int',
        'title': 'str',
        'user_count': 'int'
    }

    attribute_map = {
        'is_delete': 'IsDelete',
        'is_real_name_enable': 'IsRealNameEnable',
        'publish_time': 'PublishTime',
        'question_count': 'QuestionCount',
        'questionnaire_id': 'QuestionnaireId',
        'title': 'Title',
        'user_count': 'UserCount'
    }

    def __init__(self, is_delete=None, is_real_name_enable=None, publish_time=None, question_count=None, questionnaire_id=None, title=None, user_count=None, _configuration=None):  # noqa: E501
        """StatisticsQuestionnaireForListQuestionnaireDataAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_delete = None
        self._is_real_name_enable = None
        self._publish_time = None
        self._question_count = None
        self._questionnaire_id = None
        self._title = None
        self._user_count = None
        self.discriminator = None

        if is_delete is not None:
            self.is_delete = is_delete
        if is_real_name_enable is not None:
            self.is_real_name_enable = is_real_name_enable
        if publish_time is not None:
            self.publish_time = publish_time
        if question_count is not None:
            self.question_count = question_count
        if questionnaire_id is not None:
            self.questionnaire_id = questionnaire_id
        if title is not None:
            self.title = title
        if user_count is not None:
            self.user_count = user_count

    @property
    def is_delete(self):
        """Gets the is_delete of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The is_delete of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_delete

    @is_delete.setter
    def is_delete(self, is_delete):
        """Sets the is_delete of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param is_delete: The is_delete of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_delete = is_delete

    @property
    def is_real_name_enable(self):
        """Gets the is_real_name_enable of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The is_real_name_enable of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_real_name_enable

    @is_real_name_enable.setter
    def is_real_name_enable(self, is_real_name_enable):
        """Sets the is_real_name_enable of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param is_real_name_enable: The is_real_name_enable of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_real_name_enable = is_real_name_enable

    @property
    def publish_time(self):
        """Gets the publish_time of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The publish_time of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._publish_time

    @publish_time.setter
    def publish_time(self, publish_time):
        """Sets the publish_time of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param publish_time: The publish_time of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._publish_time = publish_time

    @property
    def question_count(self):
        """Gets the question_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The question_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._question_count

    @question_count.setter
    def question_count(self, question_count):
        """Sets the question_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param question_count: The question_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._question_count = question_count

    @property
    def questionnaire_id(self):
        """Gets the questionnaire_id of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The questionnaire_id of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._questionnaire_id

    @questionnaire_id.setter
    def questionnaire_id(self, questionnaire_id):
        """Sets the questionnaire_id of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param questionnaire_id: The questionnaire_id of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._questionnaire_id = questionnaire_id

    @property
    def title(self):
        """Gets the title of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The title of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param title: The title of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def user_count(self):
        """Gets the user_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501


        :return: The user_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_count

    @user_count.setter
    def user_count(self, user_count):
        """Sets the user_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.


        :param user_count: The user_count of this StatisticsQuestionnaireForListQuestionnaireDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_count = user_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatisticsQuestionnaireForListQuestionnaireDataAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatisticsQuestionnaireForListQuestionnaireDataAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatisticsQuestionnaireForListQuestionnaireDataAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
