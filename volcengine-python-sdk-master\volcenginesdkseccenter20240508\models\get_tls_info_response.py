# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTLSInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authorized': 'bool',
        'open': 'bool',
        'project_id': 'str',
        'quota_total': 'int',
        'quota_used': 'int',
        'storage_days': 'int',
        'threshold': 'int',
        'topic_id': 'str',
        'vuln_topic_id': 'str'
    }

    attribute_map = {
        'authorized': 'Authorized',
        'open': 'Open',
        'project_id': 'ProjectId',
        'quota_total': 'QuotaTotal',
        'quota_used': 'QuotaUsed',
        'storage_days': 'StorageDays',
        'threshold': 'Threshold',
        'topic_id': 'TopicId',
        'vuln_topic_id': 'VulnTopicId'
    }

    def __init__(self, authorized=None, open=None, project_id=None, quota_total=None, quota_used=None, storage_days=None, threshold=None, topic_id=None, vuln_topic_id=None, _configuration=None):  # noqa: E501
        """GetTLSInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authorized = None
        self._open = None
        self._project_id = None
        self._quota_total = None
        self._quota_used = None
        self._storage_days = None
        self._threshold = None
        self._topic_id = None
        self._vuln_topic_id = None
        self.discriminator = None

        if authorized is not None:
            self.authorized = authorized
        if open is not None:
            self.open = open
        if project_id is not None:
            self.project_id = project_id
        if quota_total is not None:
            self.quota_total = quota_total
        if quota_used is not None:
            self.quota_used = quota_used
        if storage_days is not None:
            self.storage_days = storage_days
        if threshold is not None:
            self.threshold = threshold
        if topic_id is not None:
            self.topic_id = topic_id
        if vuln_topic_id is not None:
            self.vuln_topic_id = vuln_topic_id

    @property
    def authorized(self):
        """Gets the authorized of this GetTLSInfoResponse.  # noqa: E501


        :return: The authorized of this GetTLSInfoResponse.  # noqa: E501
        :rtype: bool
        """
        return self._authorized

    @authorized.setter
    def authorized(self, authorized):
        """Sets the authorized of this GetTLSInfoResponse.


        :param authorized: The authorized of this GetTLSInfoResponse.  # noqa: E501
        :type: bool
        """

        self._authorized = authorized

    @property
    def open(self):
        """Gets the open of this GetTLSInfoResponse.  # noqa: E501


        :return: The open of this GetTLSInfoResponse.  # noqa: E501
        :rtype: bool
        """
        return self._open

    @open.setter
    def open(self, open):
        """Sets the open of this GetTLSInfoResponse.


        :param open: The open of this GetTLSInfoResponse.  # noqa: E501
        :type: bool
        """

        self._open = open

    @property
    def project_id(self):
        """Gets the project_id of this GetTLSInfoResponse.  # noqa: E501


        :return: The project_id of this GetTLSInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this GetTLSInfoResponse.


        :param project_id: The project_id of this GetTLSInfoResponse.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def quota_total(self):
        """Gets the quota_total of this GetTLSInfoResponse.  # noqa: E501


        :return: The quota_total of this GetTLSInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._quota_total

    @quota_total.setter
    def quota_total(self, quota_total):
        """Sets the quota_total of this GetTLSInfoResponse.


        :param quota_total: The quota_total of this GetTLSInfoResponse.  # noqa: E501
        :type: int
        """

        self._quota_total = quota_total

    @property
    def quota_used(self):
        """Gets the quota_used of this GetTLSInfoResponse.  # noqa: E501


        :return: The quota_used of this GetTLSInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._quota_used

    @quota_used.setter
    def quota_used(self, quota_used):
        """Sets the quota_used of this GetTLSInfoResponse.


        :param quota_used: The quota_used of this GetTLSInfoResponse.  # noqa: E501
        :type: int
        """

        self._quota_used = quota_used

    @property
    def storage_days(self):
        """Gets the storage_days of this GetTLSInfoResponse.  # noqa: E501


        :return: The storage_days of this GetTLSInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_days

    @storage_days.setter
    def storage_days(self, storage_days):
        """Sets the storage_days of this GetTLSInfoResponse.


        :param storage_days: The storage_days of this GetTLSInfoResponse.  # noqa: E501
        :type: int
        """

        self._storage_days = storage_days

    @property
    def threshold(self):
        """Gets the threshold of this GetTLSInfoResponse.  # noqa: E501


        :return: The threshold of this GetTLSInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this GetTLSInfoResponse.


        :param threshold: The threshold of this GetTLSInfoResponse.  # noqa: E501
        :type: int
        """

        self._threshold = threshold

    @property
    def topic_id(self):
        """Gets the topic_id of this GetTLSInfoResponse.  # noqa: E501


        :return: The topic_id of this GetTLSInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._topic_id

    @topic_id.setter
    def topic_id(self, topic_id):
        """Sets the topic_id of this GetTLSInfoResponse.


        :param topic_id: The topic_id of this GetTLSInfoResponse.  # noqa: E501
        :type: str
        """

        self._topic_id = topic_id

    @property
    def vuln_topic_id(self):
        """Gets the vuln_topic_id of this GetTLSInfoResponse.  # noqa: E501


        :return: The vuln_topic_id of this GetTLSInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._vuln_topic_id

    @vuln_topic_id.setter
    def vuln_topic_id(self, vuln_topic_id):
        """Sets the vuln_topic_id of this GetTLSInfoResponse.


        :param vuln_topic_id: The vuln_topic_id of this GetTLSInfoResponse.  # noqa: E501
        :type: str
        """

        self._vuln_topic_id = vuln_topic_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTLSInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTLSInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTLSInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
