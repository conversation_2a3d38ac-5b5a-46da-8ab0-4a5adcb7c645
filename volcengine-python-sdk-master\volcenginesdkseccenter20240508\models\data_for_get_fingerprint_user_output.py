# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetFingerprintUserOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'eip_address': 'str',
        'gid': 'str',
        'group_name': 'str',
        'home_dir': 'str',
        'hostname': 'str',
        'id': 'str',
        'info': 'str',
        'last_login_ip': 'str',
        'last_login_time': 'int',
        'password': 'str',
        'primary_ip_address': 'str',
        'shell': 'str',
        'start_time': 'int',
        'sudoers': 'str',
        'uid': 'str',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'eip_address': 'EipAddress',
        'gid': 'Gid',
        'group_name': 'GroupName',
        'home_dir': 'HomeDir',
        'hostname': 'Hostname',
        'id': 'ID',
        'info': 'Info',
        'last_login_ip': 'LastLoginIP',
        'last_login_time': 'LastLoginTime',
        'password': 'Password',
        'primary_ip_address': 'PrimaryIpAddress',
        'shell': 'Shell',
        'start_time': 'StartTime',
        'sudoers': 'Sudoers',
        'uid': 'Uid',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, agent_id=None, agent_tags=None, eip_address=None, gid=None, group_name=None, home_dir=None, hostname=None, id=None, info=None, last_login_ip=None, last_login_time=None, password=None, primary_ip_address=None, shell=None, start_time=None, sudoers=None, uid=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetFingerprintUserOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._eip_address = None
        self._gid = None
        self._group_name = None
        self._home_dir = None
        self._hostname = None
        self._id = None
        self._info = None
        self._last_login_ip = None
        self._last_login_time = None
        self._password = None
        self._primary_ip_address = None
        self._shell = None
        self._start_time = None
        self._sudoers = None
        self._uid = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if eip_address is not None:
            self.eip_address = eip_address
        if gid is not None:
            self.gid = gid
        if group_name is not None:
            self.group_name = group_name
        if home_dir is not None:
            self.home_dir = home_dir
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if info is not None:
            self.info = info
        if last_login_ip is not None:
            self.last_login_ip = last_login_ip
        if last_login_time is not None:
            self.last_login_time = last_login_time
        if password is not None:
            self.password = password
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if shell is not None:
            self.shell = shell
        if start_time is not None:
            self.start_time = start_time
        if sudoers is not None:
            self.sudoers = sudoers
        if uid is not None:
            self.uid = uid
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The agent_id of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetFingerprintUserOutput.


        :param agent_id: The agent_id of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The agent_tags of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForGetFingerprintUserOutput.


        :param agent_tags: The agent_tags of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The eip_address of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForGetFingerprintUserOutput.


        :param eip_address: The eip_address of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def gid(self):
        """Gets the gid of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The gid of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._gid

    @gid.setter
    def gid(self, gid):
        """Sets the gid of this DataForGetFingerprintUserOutput.


        :param gid: The gid of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._gid = gid

    @property
    def group_name(self):
        """Gets the group_name of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The group_name of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this DataForGetFingerprintUserOutput.


        :param group_name: The group_name of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def home_dir(self):
        """Gets the home_dir of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The home_dir of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._home_dir

    @home_dir.setter
    def home_dir(self, home_dir):
        """Sets the home_dir of this DataForGetFingerprintUserOutput.


        :param home_dir: The home_dir of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._home_dir = home_dir

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The hostname of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetFingerprintUserOutput.


        :param hostname: The hostname of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The id of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetFingerprintUserOutput.


        :param id: The id of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def info(self):
        """Gets the info of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The info of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._info

    @info.setter
    def info(self, info):
        """Sets the info of this DataForGetFingerprintUserOutput.


        :param info: The info of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._info = info

    @property
    def last_login_ip(self):
        """Gets the last_login_ip of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The last_login_ip of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_login_ip

    @last_login_ip.setter
    def last_login_ip(self, last_login_ip):
        """Sets the last_login_ip of this DataForGetFingerprintUserOutput.


        :param last_login_ip: The last_login_ip of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._last_login_ip = last_login_ip

    @property
    def last_login_time(self):
        """Gets the last_login_time of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The last_login_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_login_time

    @last_login_time.setter
    def last_login_time(self, last_login_time):
        """Sets the last_login_time of this DataForGetFingerprintUserOutput.


        :param last_login_time: The last_login_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: int
        """

        self._last_login_time = last_login_time

    @property
    def password(self):
        """Gets the password of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The password of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this DataForGetFingerprintUserOutput.


        :param password: The password of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForGetFingerprintUserOutput.


        :param primary_ip_address: The primary_ip_address of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def shell(self):
        """Gets the shell of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The shell of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._shell

    @shell.setter
    def shell(self, shell):
        """Sets the shell of this DataForGetFingerprintUserOutput.


        :param shell: The shell of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._shell = shell

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The start_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetFingerprintUserOutput.


        :param start_time: The start_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def sudoers(self):
        """Gets the sudoers of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The sudoers of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._sudoers

    @sudoers.setter
    def sudoers(self, sudoers):
        """Sets the sudoers of this DataForGetFingerprintUserOutput.


        :param sudoers: The sudoers of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._sudoers = sudoers

    @property
    def uid(self):
        """Gets the uid of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The uid of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this DataForGetFingerprintUserOutput.


        :param uid: The uid of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The update_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetFingerprintUserOutput.


        :param update_time: The update_time of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetFingerprintUserOutput.  # noqa: E501


        :return: The username of this DataForGetFingerprintUserOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetFingerprintUserOutput.


        :param username: The username of this DataForGetFingerprintUserOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetFingerprintUserOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetFingerprintUserOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetFingerprintUserOutput):
            return True

        return self.to_dict() != other.to_dict()
