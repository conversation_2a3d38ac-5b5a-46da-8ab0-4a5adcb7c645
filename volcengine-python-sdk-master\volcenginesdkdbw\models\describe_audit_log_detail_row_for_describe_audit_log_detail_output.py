# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affect_row': 'int',
        'db': 'str',
        'duration': 'int',
        'exec_time': 'int',
        'log_level': 'str',
        'log_type': 'str',
        'node_id': 'str',
        'ori_sql': 'str',
        'return_rows': 'int',
        'scan_rows': 'int',
        'source_ip': 'str',
        'sql_fingerprint': 'str',
        'sql_method': 'str',
        'table': 'str',
        'user': 'str',
        'wait_lock_consuming_time': 'int'
    }

    attribute_map = {
        'affect_row': 'AffectRow',
        'db': 'DB',
        'duration': 'Duration',
        'exec_time': 'ExecTime',
        'log_level': 'LogLevel',
        'log_type': 'LogType',
        'node_id': 'NodeId',
        'ori_sql': 'OriSql',
        'return_rows': 'ReturnRows',
        'scan_rows': 'ScanRows',
        'source_ip': 'SourceIP',
        'sql_fingerprint': 'SqlFingerprint',
        'sql_method': 'SqlMethod',
        'table': 'Table',
        'user': 'User',
        'wait_lock_consuming_time': 'WaitLockConsumingTime'
    }

    def __init__(self, affect_row=None, db=None, duration=None, exec_time=None, log_level=None, log_type=None, node_id=None, ori_sql=None, return_rows=None, scan_rows=None, source_ip=None, sql_fingerprint=None, sql_method=None, table=None, user=None, wait_lock_consuming_time=None, _configuration=None):  # noqa: E501
        """DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affect_row = None
        self._db = None
        self._duration = None
        self._exec_time = None
        self._log_level = None
        self._log_type = None
        self._node_id = None
        self._ori_sql = None
        self._return_rows = None
        self._scan_rows = None
        self._source_ip = None
        self._sql_fingerprint = None
        self._sql_method = None
        self._table = None
        self._user = None
        self._wait_lock_consuming_time = None
        self.discriminator = None

        if affect_row is not None:
            self.affect_row = affect_row
        if db is not None:
            self.db = db
        if duration is not None:
            self.duration = duration
        if exec_time is not None:
            self.exec_time = exec_time
        if log_level is not None:
            self.log_level = log_level
        if log_type is not None:
            self.log_type = log_type
        if node_id is not None:
            self.node_id = node_id
        if ori_sql is not None:
            self.ori_sql = ori_sql
        if return_rows is not None:
            self.return_rows = return_rows
        if scan_rows is not None:
            self.scan_rows = scan_rows
        if source_ip is not None:
            self.source_ip = source_ip
        if sql_fingerprint is not None:
            self.sql_fingerprint = sql_fingerprint
        if sql_method is not None:
            self.sql_method = sql_method
        if table is not None:
            self.table = table
        if user is not None:
            self.user = user
        if wait_lock_consuming_time is not None:
            self.wait_lock_consuming_time = wait_lock_consuming_time

    @property
    def affect_row(self):
        """Gets the affect_row of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The affect_row of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._affect_row

    @affect_row.setter
    def affect_row(self, affect_row):
        """Sets the affect_row of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param affect_row: The affect_row of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._affect_row = affect_row

    @property
    def db(self):
        """Gets the db of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The db of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db

    @db.setter
    def db(self, db):
        """Sets the db of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param db: The db of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._db = db

    @property
    def duration(self):
        """Gets the duration of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The duration of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param duration: The duration of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def exec_time(self):
        """Gets the exec_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The exec_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._exec_time

    @exec_time.setter
    def exec_time(self, exec_time):
        """Sets the exec_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param exec_time: The exec_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._exec_time = exec_time

    @property
    def log_level(self):
        """Gets the log_level of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The log_level of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_level

    @log_level.setter
    def log_level(self, log_level):
        """Sets the log_level of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param log_level: The log_level of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._log_level = log_level

    @property
    def log_type(self):
        """Gets the log_type of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The log_type of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_type

    @log_type.setter
    def log_type(self, log_type):
        """Sets the log_type of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param log_type: The log_type of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._log_type = log_type

    @property
    def node_id(self):
        """Gets the node_id of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The node_id of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param node_id: The node_id of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def ori_sql(self):
        """Gets the ori_sql of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The ori_sql of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._ori_sql

    @ori_sql.setter
    def ori_sql(self, ori_sql):
        """Sets the ori_sql of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param ori_sql: The ori_sql of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._ori_sql = ori_sql

    @property
    def return_rows(self):
        """Gets the return_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The return_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._return_rows

    @return_rows.setter
    def return_rows(self, return_rows):
        """Sets the return_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param return_rows: The return_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._return_rows = return_rows

    @property
    def scan_rows(self):
        """Gets the scan_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The scan_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._scan_rows

    @scan_rows.setter
    def scan_rows(self, scan_rows):
        """Sets the scan_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param scan_rows: The scan_rows of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._scan_rows = scan_rows

    @property
    def source_ip(self):
        """Gets the source_ip of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The source_ip of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_ip

    @source_ip.setter
    def source_ip(self, source_ip):
        """Sets the source_ip of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param source_ip: The source_ip of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._source_ip = source_ip

    @property
    def sql_fingerprint(self):
        """Gets the sql_fingerprint of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The sql_fingerprint of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_fingerprint

    @sql_fingerprint.setter
    def sql_fingerprint(self, sql_fingerprint):
        """Sets the sql_fingerprint of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param sql_fingerprint: The sql_fingerprint of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._sql_fingerprint = sql_fingerprint

    @property
    def sql_method(self):
        """Gets the sql_method of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The sql_method of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql_method

    @sql_method.setter
    def sql_method(self, sql_method):
        """Sets the sql_method of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param sql_method: The sql_method of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._sql_method = sql_method

    @property
    def table(self):
        """Gets the table of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The table of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._table

    @table.setter
    def table(self, table):
        """Sets the table of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param table: The table of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._table = table

    @property
    def user(self):
        """Gets the user of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The user of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param user: The user of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    @property
    def wait_lock_consuming_time(self):
        """Gets the wait_lock_consuming_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501


        :return: The wait_lock_consuming_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._wait_lock_consuming_time

    @wait_lock_consuming_time.setter
    def wait_lock_consuming_time(self, wait_lock_consuming_time):
        """Sets the wait_lock_consuming_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.


        :param wait_lock_consuming_time: The wait_lock_consuming_time of this DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput.  # noqa: E501
        :type: int
        """

        self._wait_lock_consuming_time = wait_lock_consuming_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAuditLogDetailRowForDescribeAuditLogDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
