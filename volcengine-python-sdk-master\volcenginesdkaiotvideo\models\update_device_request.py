# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateDeviceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_notification': 'AlertNotificationForUpdateDeviceInput',
        'auto_pull_after_register': 'bool',
        'description': 'str',
        'device_id': 'str',
        'device_nsid': 'str',
        'device_name': 'str',
        'password': 'str',
        'space_id': 'str',
        'use_sub_stream': 'bool',
        'username': 'str'
    }

    attribute_map = {
        'alert_notification': 'AlertNotification',
        'auto_pull_after_register': 'AutoPullAfterRegister',
        'description': 'Description',
        'device_id': 'DeviceID',
        'device_nsid': 'DeviceNSID',
        'device_name': 'DeviceName',
        'password': 'Password',
        'space_id': 'SpaceID',
        'use_sub_stream': 'UseSubStream',
        'username': 'Username'
    }

    def __init__(self, alert_notification=None, auto_pull_after_register=None, description=None, device_id=None, device_nsid=None, device_name=None, password=None, space_id=None, use_sub_stream=None, username=None, _configuration=None):  # noqa: E501
        """UpdateDeviceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_notification = None
        self._auto_pull_after_register = None
        self._description = None
        self._device_id = None
        self._device_nsid = None
        self._device_name = None
        self._password = None
        self._space_id = None
        self._use_sub_stream = None
        self._username = None
        self.discriminator = None

        if alert_notification is not None:
            self.alert_notification = alert_notification
        if auto_pull_after_register is not None:
            self.auto_pull_after_register = auto_pull_after_register
        if description is not None:
            self.description = description
        self.device_id = device_id
        if device_nsid is not None:
            self.device_nsid = device_nsid
        if device_name is not None:
            self.device_name = device_name
        if password is not None:
            self.password = password
        if space_id is not None:
            self.space_id = space_id
        if use_sub_stream is not None:
            self.use_sub_stream = use_sub_stream
        if username is not None:
            self.username = username

    @property
    def alert_notification(self):
        """Gets the alert_notification of this UpdateDeviceRequest.  # noqa: E501


        :return: The alert_notification of this UpdateDeviceRequest.  # noqa: E501
        :rtype: AlertNotificationForUpdateDeviceInput
        """
        return self._alert_notification

    @alert_notification.setter
    def alert_notification(self, alert_notification):
        """Sets the alert_notification of this UpdateDeviceRequest.


        :param alert_notification: The alert_notification of this UpdateDeviceRequest.  # noqa: E501
        :type: AlertNotificationForUpdateDeviceInput
        """

        self._alert_notification = alert_notification

    @property
    def auto_pull_after_register(self):
        """Gets the auto_pull_after_register of this UpdateDeviceRequest.  # noqa: E501


        :return: The auto_pull_after_register of this UpdateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pull_after_register

    @auto_pull_after_register.setter
    def auto_pull_after_register(self, auto_pull_after_register):
        """Sets the auto_pull_after_register of this UpdateDeviceRequest.


        :param auto_pull_after_register: The auto_pull_after_register of this UpdateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._auto_pull_after_register = auto_pull_after_register

    @property
    def description(self):
        """Gets the description of this UpdateDeviceRequest.  # noqa: E501


        :return: The description of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateDeviceRequest.


        :param description: The description of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def device_id(self):
        """Gets the device_id of this UpdateDeviceRequest.  # noqa: E501


        :return: The device_id of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this UpdateDeviceRequest.


        :param device_id: The device_id of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and device_id is None:
            raise ValueError("Invalid value for `device_id`, must not be `None`")  # noqa: E501

        self._device_id = device_id

    @property
    def device_nsid(self):
        """Gets the device_nsid of this UpdateDeviceRequest.  # noqa: E501


        :return: The device_nsid of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this UpdateDeviceRequest.


        :param device_nsid: The device_nsid of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def device_name(self):
        """Gets the device_name of this UpdateDeviceRequest.  # noqa: E501


        :return: The device_name of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this UpdateDeviceRequest.


        :param device_name: The device_name of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._device_name = device_name

    @property
    def password(self):
        """Gets the password of this UpdateDeviceRequest.  # noqa: E501


        :return: The password of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this UpdateDeviceRequest.


        :param password: The password of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def space_id(self):
        """Gets the space_id of this UpdateDeviceRequest.  # noqa: E501


        :return: The space_id of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this UpdateDeviceRequest.


        :param space_id: The space_id of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def use_sub_stream(self):
        """Gets the use_sub_stream of this UpdateDeviceRequest.  # noqa: E501


        :return: The use_sub_stream of this UpdateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._use_sub_stream

    @use_sub_stream.setter
    def use_sub_stream(self, use_sub_stream):
        """Sets the use_sub_stream of this UpdateDeviceRequest.


        :param use_sub_stream: The use_sub_stream of this UpdateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._use_sub_stream = use_sub_stream

    @property
    def username(self):
        """Gets the username of this UpdateDeviceRequest.  # noqa: E501


        :return: The username of this UpdateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this UpdateDeviceRequest.


        :param username: The username of this UpdateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateDeviceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateDeviceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateDeviceRequest):
            return True

        return self.to_dict() != other.to_dict()
