# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForDeleteRaspConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cloud_providers': 'list[str]',
        'content': 'str',
        'hostname': 'str',
        'ip': 'str',
        'if_block': 'bool',
        'if_filter': 'bool',
        'if_protect': 'bool',
        'leaf_group_ids': 'list[str]',
        'name': 'str',
        'runtime': 'list[str]',
        'tags': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cloud_providers': 'CloudProviders',
        'content': 'Content',
        'hostname': 'Hostname',
        'ip': 'IP',
        'if_block': 'IfBlock',
        'if_filter': 'IfFilter',
        'if_protect': 'IfProtect',
        'leaf_group_ids': 'LeafGroupIDs',
        'name': 'Name',
        'runtime': 'Runtime',
        'tags': 'Tags',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, cloud_providers=None, content=None, hostname=None, ip=None, if_block=None, if_filter=None, if_protect=None, leaf_group_ids=None, name=None, runtime=None, tags=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ConditionsForDeleteRaspConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cloud_providers = None
        self._content = None
        self._hostname = None
        self._ip = None
        self._if_block = None
        self._if_filter = None
        self._if_protect = None
        self._leaf_group_ids = None
        self._name = None
        self._runtime = None
        self._tags = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if content is not None:
            self.content = content
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if if_block is not None:
            self.if_block = if_block
        if if_filter is not None:
            self.if_filter = if_filter
        if if_protect is not None:
            self.if_protect = if_protect
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if name is not None:
            self.name = name
        if runtime is not None:
            self.runtime = runtime
        if tags is not None:
            self.tags = tags
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The agent_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ConditionsForDeleteRaspConfigInput.


        :param agent_id: The agent_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The cloud_providers of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ConditionsForDeleteRaspConfigInput.


        :param cloud_providers: The cloud_providers of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def content(self):
        """Gets the content of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The content of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this ConditionsForDeleteRaspConfigInput.


        :param content: The content of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def hostname(self):
        """Gets the hostname of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The hostname of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ConditionsForDeleteRaspConfigInput.


        :param hostname: The hostname of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The ip of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ConditionsForDeleteRaspConfigInput.


        :param ip: The ip of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def if_block(self):
        """Gets the if_block of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The if_block of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._if_block

    @if_block.setter
    def if_block(self, if_block):
        """Sets the if_block of this ConditionsForDeleteRaspConfigInput.


        :param if_block: The if_block of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: bool
        """

        self._if_block = if_block

    @property
    def if_filter(self):
        """Gets the if_filter of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The if_filter of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._if_filter

    @if_filter.setter
    def if_filter(self, if_filter):
        """Sets the if_filter of this ConditionsForDeleteRaspConfigInput.


        :param if_filter: The if_filter of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: bool
        """

        self._if_filter = if_filter

    @property
    def if_protect(self):
        """Gets the if_protect of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The if_protect of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._if_protect

    @if_protect.setter
    def if_protect(self, if_protect):
        """Sets the if_protect of this ConditionsForDeleteRaspConfigInput.


        :param if_protect: The if_protect of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: bool
        """

        self._if_protect = if_protect

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The leaf_group_ids of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ConditionsForDeleteRaspConfigInput.


        :param leaf_group_ids: The leaf_group_ids of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def name(self):
        """Gets the name of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The name of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ConditionsForDeleteRaspConfigInput.


        :param name: The name of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def runtime(self):
        """Gets the runtime of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The runtime of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this ConditionsForDeleteRaspConfigInput.


        :param runtime: The runtime of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._runtime = runtime

    @property
    def tags(self):
        """Gets the tags of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The tags of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ConditionsForDeleteRaspConfigInput.


        :param tags: The tags of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501


        :return: The top_group_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ConditionsForDeleteRaspConfigInput.


        :param top_group_id: The top_group_id of this ConditionsForDeleteRaspConfigInput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForDeleteRaspConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForDeleteRaspConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForDeleteRaspConfigInput):
            return True

        return self.to_dict() != other.to_dict()
