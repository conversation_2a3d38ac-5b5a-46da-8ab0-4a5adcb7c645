# coding: utf-8

"""
    cen

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCenBandwidthPackagesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cen_bandwidth_package_ids': 'list[str]',
        'cen_bandwidth_package_name': 'str',
        'cen_id': 'str',
        'local_geographic_region_set_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'peer_geographic_region_set_id': 'str',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeCenBandwidthPackagesInput]'
    }

    attribute_map = {
        'cen_bandwidth_package_ids': 'CenBandwidthPackageIds',
        'cen_bandwidth_package_name': 'CenBandwidthPackageName',
        'cen_id': 'CenId',
        'local_geographic_region_set_id': 'LocalGeographicRegionSetId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'peer_geographic_region_set_id': 'PeerGeographicRegionSetId',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, cen_bandwidth_package_ids=None, cen_bandwidth_package_name=None, cen_id=None, local_geographic_region_set_id=None, page_number=None, page_size=None, peer_geographic_region_set_id=None, project_name=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeCenBandwidthPackagesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cen_bandwidth_package_ids = None
        self._cen_bandwidth_package_name = None
        self._cen_id = None
        self._local_geographic_region_set_id = None
        self._page_number = None
        self._page_size = None
        self._peer_geographic_region_set_id = None
        self._project_name = None
        self._tag_filters = None
        self.discriminator = None

        if cen_bandwidth_package_ids is not None:
            self.cen_bandwidth_package_ids = cen_bandwidth_package_ids
        if cen_bandwidth_package_name is not None:
            self.cen_bandwidth_package_name = cen_bandwidth_package_name
        if cen_id is not None:
            self.cen_id = cen_id
        if local_geographic_region_set_id is not None:
            self.local_geographic_region_set_id = local_geographic_region_set_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if peer_geographic_region_set_id is not None:
            self.peer_geographic_region_set_id = peer_geographic_region_set_id
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def cen_bandwidth_package_ids(self):
        """Gets the cen_bandwidth_package_ids of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The cen_bandwidth_package_ids of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cen_bandwidth_package_ids

    @cen_bandwidth_package_ids.setter
    def cen_bandwidth_package_ids(self, cen_bandwidth_package_ids):
        """Sets the cen_bandwidth_package_ids of this DescribeCenBandwidthPackagesRequest.


        :param cen_bandwidth_package_ids: The cen_bandwidth_package_ids of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: list[str]
        """

        self._cen_bandwidth_package_ids = cen_bandwidth_package_ids

    @property
    def cen_bandwidth_package_name(self):
        """Gets the cen_bandwidth_package_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The cen_bandwidth_package_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cen_bandwidth_package_name

    @cen_bandwidth_package_name.setter
    def cen_bandwidth_package_name(self, cen_bandwidth_package_name):
        """Sets the cen_bandwidth_package_name of this DescribeCenBandwidthPackagesRequest.


        :param cen_bandwidth_package_name: The cen_bandwidth_package_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._cen_bandwidth_package_name = cen_bandwidth_package_name

    @property
    def cen_id(self):
        """Gets the cen_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The cen_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cen_id

    @cen_id.setter
    def cen_id(self, cen_id):
        """Sets the cen_id of this DescribeCenBandwidthPackagesRequest.


        :param cen_id: The cen_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._cen_id = cen_id

    @property
    def local_geographic_region_set_id(self):
        """Gets the local_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The local_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._local_geographic_region_set_id

    @local_geographic_region_set_id.setter
    def local_geographic_region_set_id(self, local_geographic_region_set_id):
        """Sets the local_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.


        :param local_geographic_region_set_id: The local_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._local_geographic_region_set_id = local_geographic_region_set_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The page_number of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeCenBandwidthPackagesRequest.


        :param page_number: The page_number of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The page_size of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeCenBandwidthPackagesRequest.


        :param page_size: The page_size of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def peer_geographic_region_set_id(self):
        """Gets the peer_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The peer_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_geographic_region_set_id

    @peer_geographic_region_set_id.setter
    def peer_geographic_region_set_id(self, peer_geographic_region_set_id):
        """Sets the peer_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.


        :param peer_geographic_region_set_id: The peer_geographic_region_set_id of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._peer_geographic_region_set_id = peer_geographic_region_set_id

    @property
    def project_name(self):
        """Gets the project_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The project_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeCenBandwidthPackagesRequest.


        :param project_name: The project_name of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeCenBandwidthPackagesRequest.  # noqa: E501


        :return: The tag_filters of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeCenBandwidthPackagesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeCenBandwidthPackagesRequest.


        :param tag_filters: The tag_filters of this DescribeCenBandwidthPackagesRequest.  # noqa: E501
        :type: list[TagFilterForDescribeCenBandwidthPackagesInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCenBandwidthPackagesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCenBandwidthPackagesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCenBandwidthPackagesRequest):
            return True

        return self.to_dict() != other.to_dict()
