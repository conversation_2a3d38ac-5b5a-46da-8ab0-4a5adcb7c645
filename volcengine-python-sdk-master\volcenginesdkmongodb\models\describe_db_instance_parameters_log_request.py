# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceParametersLogRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'str',
        'instance_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'start_time': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'instance_id': 'InstanceId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'start_time': 'StartTime'
    }

    def __init__(self, end_time=None, instance_id=None, page_number=None, page_size=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceParametersLogRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._instance_id = None
        self._page_number = None
        self._page_size = None
        self._start_time = None
        self.discriminator = None

        self.end_time = end_time
        self.instance_id = instance_id
        self.page_number = page_number
        self.page_size = page_size
        self.start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501


        :return: The end_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeDBInstanceParametersLogRequest.


        :param end_time: The end_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBInstanceParametersLogRequest.  # noqa: E501


        :return: The instance_id of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBInstanceParametersLogRequest.


        :param instance_id: The instance_id of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDBInstanceParametersLogRequest.  # noqa: E501


        :return: The page_number of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDBInstanceParametersLogRequest.


        :param page_number: The page_number of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDBInstanceParametersLogRequest.  # noqa: E501


        :return: The page_size of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDBInstanceParametersLogRequest.


        :param page_size: The page_size of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def start_time(self):
        """Gets the start_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501


        :return: The start_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeDBInstanceParametersLogRequest.


        :param start_time: The start_time of this DescribeDBInstanceParametersLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceParametersLogRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceParametersLogRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceParametersLogRequest):
            return True

        return self.to_dict() != other.to_dict()
