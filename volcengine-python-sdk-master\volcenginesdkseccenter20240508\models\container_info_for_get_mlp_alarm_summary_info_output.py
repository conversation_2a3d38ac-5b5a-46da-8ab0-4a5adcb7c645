# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ContainerInfoForGetMlpAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'container_create_time': 'str',
        'container_create_timestamp': 'int',
        'container_host_name': 'str',
        'container_id': 'str',
        'container_ip': 'str',
        'container_name': 'str',
        'container_net_mode': 'str',
        'container_pns': 'str',
        'container_query_result': 'str',
        'container_runtime': 'str',
        'container_state': 'str',
        'image_id': 'str',
        'image_name': 'str',
        'pod_id': 'str',
        'pod_name': 'str'
    }

    attribute_map = {
        'container_create_time': 'ContainerCreateTime',
        'container_create_timestamp': 'ContainerCreateTimestamp',
        'container_host_name': 'ContainerHostName',
        'container_id': 'ContainerID',
        'container_ip': 'ContainerIP',
        'container_name': 'ContainerName',
        'container_net_mode': 'ContainerNetMode',
        'container_pns': 'ContainerPns',
        'container_query_result': 'ContainerQueryResult',
        'container_runtime': 'ContainerRuntime',
        'container_state': 'ContainerState',
        'image_id': 'ImageID',
        'image_name': 'ImageName',
        'pod_id': 'PodID',
        'pod_name': 'PodName'
    }

    def __init__(self, container_create_time=None, container_create_timestamp=None, container_host_name=None, container_id=None, container_ip=None, container_name=None, container_net_mode=None, container_pns=None, container_query_result=None, container_runtime=None, container_state=None, image_id=None, image_name=None, pod_id=None, pod_name=None, _configuration=None):  # noqa: E501
        """ContainerInfoForGetMlpAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._container_create_time = None
        self._container_create_timestamp = None
        self._container_host_name = None
        self._container_id = None
        self._container_ip = None
        self._container_name = None
        self._container_net_mode = None
        self._container_pns = None
        self._container_query_result = None
        self._container_runtime = None
        self._container_state = None
        self._image_id = None
        self._image_name = None
        self._pod_id = None
        self._pod_name = None
        self.discriminator = None

        if container_create_time is not None:
            self.container_create_time = container_create_time
        if container_create_timestamp is not None:
            self.container_create_timestamp = container_create_timestamp
        if container_host_name is not None:
            self.container_host_name = container_host_name
        if container_id is not None:
            self.container_id = container_id
        if container_ip is not None:
            self.container_ip = container_ip
        if container_name is not None:
            self.container_name = container_name
        if container_net_mode is not None:
            self.container_net_mode = container_net_mode
        if container_pns is not None:
            self.container_pns = container_pns
        if container_query_result is not None:
            self.container_query_result = container_query_result
        if container_runtime is not None:
            self.container_runtime = container_runtime
        if container_state is not None:
            self.container_state = container_state
        if image_id is not None:
            self.image_id = image_id
        if image_name is not None:
            self.image_name = image_name
        if pod_id is not None:
            self.pod_id = pod_id
        if pod_name is not None:
            self.pod_name = pod_name

    @property
    def container_create_time(self):
        """Gets the container_create_time of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_create_time of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_create_time

    @container_create_time.setter
    def container_create_time(self, container_create_time):
        """Sets the container_create_time of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_create_time: The container_create_time of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_create_time = container_create_time

    @property
    def container_create_timestamp(self):
        """Gets the container_create_timestamp of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_create_timestamp of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._container_create_timestamp

    @container_create_timestamp.setter
    def container_create_timestamp(self, container_create_timestamp):
        """Sets the container_create_timestamp of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_create_timestamp: The container_create_timestamp of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._container_create_timestamp = container_create_timestamp

    @property
    def container_host_name(self):
        """Gets the container_host_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_host_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_host_name

    @container_host_name.setter
    def container_host_name(self, container_host_name):
        """Sets the container_host_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_host_name: The container_host_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_host_name = container_host_name

    @property
    def container_id(self):
        """Gets the container_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_id: The container_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def container_ip(self):
        """Gets the container_ip of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_ip of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_ip

    @container_ip.setter
    def container_ip(self, container_ip):
        """Sets the container_ip of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_ip: The container_ip of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_ip = container_ip

    @property
    def container_name(self):
        """Gets the container_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_name: The container_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def container_net_mode(self):
        """Gets the container_net_mode of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_net_mode of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_net_mode

    @container_net_mode.setter
    def container_net_mode(self, container_net_mode):
        """Sets the container_net_mode of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_net_mode: The container_net_mode of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_net_mode = container_net_mode

    @property
    def container_pns(self):
        """Gets the container_pns of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_pns of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_pns

    @container_pns.setter
    def container_pns(self, container_pns):
        """Sets the container_pns of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_pns: The container_pns of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_pns = container_pns

    @property
    def container_query_result(self):
        """Gets the container_query_result of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_query_result of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_query_result

    @container_query_result.setter
    def container_query_result(self, container_query_result):
        """Sets the container_query_result of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_query_result: The container_query_result of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_query_result = container_query_result

    @property
    def container_runtime(self):
        """Gets the container_runtime of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_runtime of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_runtime

    @container_runtime.setter
    def container_runtime(self, container_runtime):
        """Sets the container_runtime of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_runtime: The container_runtime of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_runtime = container_runtime

    @property
    def container_state(self):
        """Gets the container_state of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The container_state of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_state

    @container_state.setter
    def container_state(self, container_state):
        """Sets the container_state of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param container_state: The container_state of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._container_state = container_state

    @property
    def image_id(self):
        """Gets the image_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The image_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param image_id: The image_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def image_name(self):
        """Gets the image_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The image_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param image_name: The image_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def pod_id(self):
        """Gets the pod_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pod_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_id

    @pod_id.setter
    def pod_id(self, pod_id):
        """Sets the pod_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param pod_id: The pod_id of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pod_id = pod_id

    @property
    def pod_name(self):
        """Gets the pod_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pod_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_name

    @pod_name.setter
    def pod_name(self, pod_name):
        """Sets the pod_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.


        :param pod_name: The pod_name of this ContainerInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pod_name = pod_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ContainerInfoForGetMlpAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ContainerInfoForGetMlpAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ContainerInfoForGetMlpAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
