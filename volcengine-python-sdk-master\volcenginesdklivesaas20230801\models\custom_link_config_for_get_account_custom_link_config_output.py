# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CustomLinkConfigForGetAccountCustomLinkConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'background_image_url': 'str',
        'background_music_url': 'str',
        'status': 'int'
    }

    attribute_map = {
        'background_image_url': 'BackgroundImageUrl',
        'background_music_url': 'BackgroundMusicUrl',
        'status': 'Status'
    }

    def __init__(self, background_image_url=None, background_music_url=None, status=None, _configuration=None):  # noqa: E501
        """CustomLinkConfigForGetAccountCustomLinkConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._background_image_url = None
        self._background_music_url = None
        self._status = None
        self.discriminator = None

        if background_image_url is not None:
            self.background_image_url = background_image_url
        if background_music_url is not None:
            self.background_music_url = background_music_url
        if status is not None:
            self.status = status

    @property
    def background_image_url(self):
        """Gets the background_image_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501


        :return: The background_image_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._background_image_url

    @background_image_url.setter
    def background_image_url(self, background_image_url):
        """Sets the background_image_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.


        :param background_image_url: The background_image_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :type: str
        """

        self._background_image_url = background_image_url

    @property
    def background_music_url(self):
        """Gets the background_music_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501


        :return: The background_music_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._background_music_url

    @background_music_url.setter
    def background_music_url(self, background_music_url):
        """Sets the background_music_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.


        :param background_music_url: The background_music_url of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :type: str
        """

        self._background_music_url = background_music_url

    @property
    def status(self):
        """Gets the status of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501


        :return: The status of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.


        :param status: The status of this CustomLinkConfigForGetAccountCustomLinkConfigOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomLinkConfigForGetAccountCustomLinkConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomLinkConfigForGetAccountCustomLinkConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CustomLinkConfigForGetAccountCustomLinkConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
