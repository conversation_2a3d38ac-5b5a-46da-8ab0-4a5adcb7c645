# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListPodRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'archive_status': 'str',
        'authority_status': 'int',
        'configuration_code_list': 'list[str]',
        'dnsid': 'str',
        'dc_list': 'list[str]',
        'host_id': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'online_list': 'list[int]',
        'pod_id_list': 'list[str]',
        'pod_name': 'str',
        'product_id': 'str',
        'region_list': 'list[str]',
        'server_type_code': 'str',
        'stream_status_list': 'list[int]',
        'tag_id_list': 'list[str]',
        'zone_id': 'str'
    }

    attribute_map = {
        'archive_status': 'ArchiveStatus',
        'authority_status': 'AuthorityStatus',
        'configuration_code_list': 'ConfigurationCodeList',
        'dnsid': 'DNSId',
        'dc_list': 'DcList',
        'host_id': 'HostId',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'online_list': 'OnlineList',
        'pod_id_list': 'PodIdList',
        'pod_name': 'PodName',
        'product_id': 'ProductId',
        'region_list': 'RegionList',
        'server_type_code': 'ServerTypeCode',
        'stream_status_list': 'StreamStatusList',
        'tag_id_list': 'TagIdList',
        'zone_id': 'ZoneId'
    }

    def __init__(self, archive_status=None, authority_status=None, configuration_code_list=None, dnsid=None, dc_list=None, host_id=None, max_results=None, next_token=None, online_list=None, pod_id_list=None, pod_name=None, product_id=None, region_list=None, server_type_code=None, stream_status_list=None, tag_id_list=None, zone_id=None, _configuration=None):  # noqa: E501
        """ListPodRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._archive_status = None
        self._authority_status = None
        self._configuration_code_list = None
        self._dnsid = None
        self._dc_list = None
        self._host_id = None
        self._max_results = None
        self._next_token = None
        self._online_list = None
        self._pod_id_list = None
        self._pod_name = None
        self._product_id = None
        self._region_list = None
        self._server_type_code = None
        self._stream_status_list = None
        self._tag_id_list = None
        self._zone_id = None
        self.discriminator = None

        if archive_status is not None:
            self.archive_status = archive_status
        if authority_status is not None:
            self.authority_status = authority_status
        if configuration_code_list is not None:
            self.configuration_code_list = configuration_code_list
        if dnsid is not None:
            self.dnsid = dnsid
        if dc_list is not None:
            self.dc_list = dc_list
        if host_id is not None:
            self.host_id = host_id
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if online_list is not None:
            self.online_list = online_list
        if pod_id_list is not None:
            self.pod_id_list = pod_id_list
        if pod_name is not None:
            self.pod_name = pod_name
        self.product_id = product_id
        if region_list is not None:
            self.region_list = region_list
        if server_type_code is not None:
            self.server_type_code = server_type_code
        if stream_status_list is not None:
            self.stream_status_list = stream_status_list
        if tag_id_list is not None:
            self.tag_id_list = tag_id_list
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def archive_status(self):
        """Gets the archive_status of this ListPodRequest.  # noqa: E501


        :return: The archive_status of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._archive_status

    @archive_status.setter
    def archive_status(self, archive_status):
        """Sets the archive_status of this ListPodRequest.


        :param archive_status: The archive_status of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._archive_status = archive_status

    @property
    def authority_status(self):
        """Gets the authority_status of this ListPodRequest.  # noqa: E501


        :return: The authority_status of this ListPodRequest.  # noqa: E501
        :rtype: int
        """
        return self._authority_status

    @authority_status.setter
    def authority_status(self, authority_status):
        """Sets the authority_status of this ListPodRequest.


        :param authority_status: The authority_status of this ListPodRequest.  # noqa: E501
        :type: int
        """

        self._authority_status = authority_status

    @property
    def configuration_code_list(self):
        """Gets the configuration_code_list of this ListPodRequest.  # noqa: E501


        :return: The configuration_code_list of this ListPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._configuration_code_list

    @configuration_code_list.setter
    def configuration_code_list(self, configuration_code_list):
        """Sets the configuration_code_list of this ListPodRequest.


        :param configuration_code_list: The configuration_code_list of this ListPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._configuration_code_list = configuration_code_list

    @property
    def dnsid(self):
        """Gets the dnsid of this ListPodRequest.  # noqa: E501


        :return: The dnsid of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._dnsid

    @dnsid.setter
    def dnsid(self, dnsid):
        """Sets the dnsid of this ListPodRequest.


        :param dnsid: The dnsid of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._dnsid = dnsid

    @property
    def dc_list(self):
        """Gets the dc_list of this ListPodRequest.  # noqa: E501


        :return: The dc_list of this ListPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._dc_list

    @dc_list.setter
    def dc_list(self, dc_list):
        """Sets the dc_list of this ListPodRequest.


        :param dc_list: The dc_list of this ListPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._dc_list = dc_list

    @property
    def host_id(self):
        """Gets the host_id of this ListPodRequest.  # noqa: E501


        :return: The host_id of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._host_id

    @host_id.setter
    def host_id(self, host_id):
        """Sets the host_id of this ListPodRequest.


        :param host_id: The host_id of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._host_id = host_id

    @property
    def max_results(self):
        """Gets the max_results of this ListPodRequest.  # noqa: E501


        :return: The max_results of this ListPodRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListPodRequest.


        :param max_results: The max_results of this ListPodRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListPodRequest.  # noqa: E501


        :return: The next_token of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListPodRequest.


        :param next_token: The next_token of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def online_list(self):
        """Gets the online_list of this ListPodRequest.  # noqa: E501


        :return: The online_list of this ListPodRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._online_list

    @online_list.setter
    def online_list(self, online_list):
        """Sets the online_list of this ListPodRequest.


        :param online_list: The online_list of this ListPodRequest.  # noqa: E501
        :type: list[int]
        """

        self._online_list = online_list

    @property
    def pod_id_list(self):
        """Gets the pod_id_list of this ListPodRequest.  # noqa: E501


        :return: The pod_id_list of this ListPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._pod_id_list

    @pod_id_list.setter
    def pod_id_list(self, pod_id_list):
        """Sets the pod_id_list of this ListPodRequest.


        :param pod_id_list: The pod_id_list of this ListPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._pod_id_list = pod_id_list

    @property
    def pod_name(self):
        """Gets the pod_name of this ListPodRequest.  # noqa: E501


        :return: The pod_name of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._pod_name

    @pod_name.setter
    def pod_name(self, pod_name):
        """Sets the pod_name of this ListPodRequest.


        :param pod_name: The pod_name of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._pod_name = pod_name

    @property
    def product_id(self):
        """Gets the product_id of this ListPodRequest.  # noqa: E501


        :return: The product_id of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this ListPodRequest.


        :param product_id: The product_id of this ListPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product_id is None:
            raise ValueError("Invalid value for `product_id`, must not be `None`")  # noqa: E501

        self._product_id = product_id

    @property
    def region_list(self):
        """Gets the region_list of this ListPodRequest.  # noqa: E501


        :return: The region_list of this ListPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._region_list

    @region_list.setter
    def region_list(self, region_list):
        """Sets the region_list of this ListPodRequest.


        :param region_list: The region_list of this ListPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._region_list = region_list

    @property
    def server_type_code(self):
        """Gets the server_type_code of this ListPodRequest.  # noqa: E501


        :return: The server_type_code of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_type_code

    @server_type_code.setter
    def server_type_code(self, server_type_code):
        """Sets the server_type_code of this ListPodRequest.


        :param server_type_code: The server_type_code of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._server_type_code = server_type_code

    @property
    def stream_status_list(self):
        """Gets the stream_status_list of this ListPodRequest.  # noqa: E501


        :return: The stream_status_list of this ListPodRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._stream_status_list

    @stream_status_list.setter
    def stream_status_list(self, stream_status_list):
        """Sets the stream_status_list of this ListPodRequest.


        :param stream_status_list: The stream_status_list of this ListPodRequest.  # noqa: E501
        :type: list[int]
        """

        self._stream_status_list = stream_status_list

    @property
    def tag_id_list(self):
        """Gets the tag_id_list of this ListPodRequest.  # noqa: E501


        :return: The tag_id_list of this ListPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag_id_list

    @tag_id_list.setter
    def tag_id_list(self, tag_id_list):
        """Sets the tag_id_list of this ListPodRequest.


        :param tag_id_list: The tag_id_list of this ListPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._tag_id_list = tag_id_list

    @property
    def zone_id(self):
        """Gets the zone_id of this ListPodRequest.  # noqa: E501


        :return: The zone_id of this ListPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ListPodRequest.


        :param zone_id: The zone_id of this ListPodRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListPodRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListPodRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListPodRequest):
            return True

        return self.to_dict() != other.to_dict()
