# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceStructureForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'nodes': 'list[NodeForDescribeDBInstanceDetailOutput]',
        'sub_instance_type': 'str',
        'zone_ids': 'str'
    }

    attribute_map = {
        'nodes': 'Nodes',
        'sub_instance_type': 'SubInstanceType',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, nodes=None, sub_instance_type=None, zone_ids=None, _configuration=None):  # noqa: E501
        """InstanceStructureForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._nodes = None
        self._sub_instance_type = None
        self._zone_ids = None
        self.discriminator = None

        if nodes is not None:
            self.nodes = nodes
        if sub_instance_type is not None:
            self.sub_instance_type = sub_instance_type
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def nodes(self):
        """Gets the nodes of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The nodes of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[NodeForDescribeDBInstanceDetailOutput]
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this InstanceStructureForDescribeDBInstanceDetailOutput.


        :param nodes: The nodes of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[NodeForDescribeDBInstanceDetailOutput]
        """

        self._nodes = nodes

    @property
    def sub_instance_type(self):
        """Gets the sub_instance_type of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The sub_instance_type of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_instance_type

    @sub_instance_type.setter
    def sub_instance_type(self, sub_instance_type):
        """Sets the sub_instance_type of this InstanceStructureForDescribeDBInstanceDetailOutput.


        :param sub_instance_type: The sub_instance_type of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._sub_instance_type = sub_instance_type

    @property
    def zone_ids(self):
        """Gets the zone_ids of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_ids of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this InstanceStructureForDescribeDBInstanceDetailOutput.


        :param zone_ids: The zone_ids of this InstanceStructureForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceStructureForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceStructureForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceStructureForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
