# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAutoDefenseRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'create_time': 'int',
        'create_user': 'str',
        'data_type': 'int',
        'enable': 'bool',
        'range': 'RangeForListAutoDefenseRulesOutput',
        'rule_desc': 'str',
        'rule_id': 'str',
        'rule_type': 'str',
        'type': 'str',
        'update_time': 'int',
        'update_user': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'create_time': 'CreateTime',
        'create_user': 'CreateUser',
        'data_type': 'DataType',
        'enable': 'Enable',
        'range': 'Range',
        'rule_desc': 'RuleDesc',
        'rule_id': 'RuleID',
        'rule_type': 'RuleType',
        'type': 'Type',
        'update_time': 'UpdateTime',
        'update_user': 'UpdateUser'
    }

    def __init__(self, action=None, create_time=None, create_user=None, data_type=None, enable=None, range=None, rule_desc=None, rule_id=None, rule_type=None, type=None, update_time=None, update_user=None, _configuration=None):  # noqa: E501
        """DataForListAutoDefenseRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._create_time = None
        self._create_user = None
        self._data_type = None
        self._enable = None
        self._range = None
        self._rule_desc = None
        self._rule_id = None
        self._rule_type = None
        self._type = None
        self._update_time = None
        self._update_user = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if create_time is not None:
            self.create_time = create_time
        if create_user is not None:
            self.create_user = create_user
        if data_type is not None:
            self.data_type = data_type
        if enable is not None:
            self.enable = enable
        if range is not None:
            self.range = range
        if rule_desc is not None:
            self.rule_desc = rule_desc
        if rule_id is not None:
            self.rule_id = rule_id
        if rule_type is not None:
            self.rule_type = rule_type
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time
        if update_user is not None:
            self.update_user = update_user

    @property
    def action(self):
        """Gets the action of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The action of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this DataForListAutoDefenseRulesOutput.


        :param action: The action of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def create_time(self):
        """Gets the create_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The create_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListAutoDefenseRulesOutput.


        :param create_time: The create_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def create_user(self):
        """Gets the create_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The create_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_user

    @create_user.setter
    def create_user(self, create_user):
        """Sets the create_user of this DataForListAutoDefenseRulesOutput.


        :param create_user: The create_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._create_user = create_user

    @property
    def data_type(self):
        """Gets the data_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The data_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this DataForListAutoDefenseRulesOutput.


        :param data_type: The data_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: int
        """

        self._data_type = data_type

    @property
    def enable(self):
        """Gets the enable of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The enable of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this DataForListAutoDefenseRulesOutput.


        :param enable: The enable of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def range(self):
        """Gets the range of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The range of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: RangeForListAutoDefenseRulesOutput
        """
        return self._range

    @range.setter
    def range(self, range):
        """Sets the range of this DataForListAutoDefenseRulesOutput.


        :param range: The range of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: RangeForListAutoDefenseRulesOutput
        """

        self._range = range

    @property
    def rule_desc(self):
        """Gets the rule_desc of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The rule_desc of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_desc

    @rule_desc.setter
    def rule_desc(self, rule_desc):
        """Sets the rule_desc of this DataForListAutoDefenseRulesOutput.


        :param rule_desc: The rule_desc of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_desc = rule_desc

    @property
    def rule_id(self):
        """Gets the rule_id of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The rule_id of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DataForListAutoDefenseRulesOutput.


        :param rule_id: The rule_id of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def rule_type(self):
        """Gets the rule_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The rule_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_type

    @rule_type.setter
    def rule_type(self, rule_type):
        """Sets the rule_type of this DataForListAutoDefenseRulesOutput.


        :param rule_type: The rule_type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_type = rule_type

    @property
    def type(self):
        """Gets the type of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForListAutoDefenseRulesOutput.


        :param type: The type of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The update_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListAutoDefenseRulesOutput.


        :param update_time: The update_time of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def update_user(self):
        """Gets the update_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The update_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_user

    @update_user.setter
    def update_user(self, update_user):
        """Sets the update_user of this DataForListAutoDefenseRulesOutput.


        :param update_user: The update_user of this DataForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._update_user = update_user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAutoDefenseRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAutoDefenseRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAutoDefenseRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
