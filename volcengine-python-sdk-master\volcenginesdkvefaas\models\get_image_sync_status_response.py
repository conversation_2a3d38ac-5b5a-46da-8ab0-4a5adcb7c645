# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetImageSyncStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'end_time': 'str',
        'image_cache_description': 'str',
        'image_cache_enabled': 'bool',
        'image_cache_expire_date_time': 'str',
        'image_cache_status': 'str',
        'image_preload_enabled': 'bool',
        'image_preload_status': 'str',
        'start_time': 'str',
        'status': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'end_time': 'EndTime',
        'image_cache_description': 'ImageCacheDescription',
        'image_cache_enabled': 'ImageCacheEnabled',
        'image_cache_expire_date_time': 'ImageCacheExpireDateTime',
        'image_cache_status': 'ImageCacheStatus',
        'image_preload_enabled': 'ImagePreloadEnabled',
        'image_preload_status': 'ImagePreloadStatus',
        'start_time': 'StartTime',
        'status': 'Status'
    }

    def __init__(self, description=None, end_time=None, image_cache_description=None, image_cache_enabled=None, image_cache_expire_date_time=None, image_cache_status=None, image_preload_enabled=None, image_preload_status=None, start_time=None, status=None, _configuration=None):  # noqa: E501
        """GetImageSyncStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._end_time = None
        self._image_cache_description = None
        self._image_cache_enabled = None
        self._image_cache_expire_date_time = None
        self._image_cache_status = None
        self._image_preload_enabled = None
        self._image_preload_status = None
        self._start_time = None
        self._status = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if end_time is not None:
            self.end_time = end_time
        if image_cache_description is not None:
            self.image_cache_description = image_cache_description
        if image_cache_enabled is not None:
            self.image_cache_enabled = image_cache_enabled
        if image_cache_expire_date_time is not None:
            self.image_cache_expire_date_time = image_cache_expire_date_time
        if image_cache_status is not None:
            self.image_cache_status = image_cache_status
        if image_preload_enabled is not None:
            self.image_preload_enabled = image_preload_enabled
        if image_preload_status is not None:
            self.image_preload_status = image_preload_status
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status

    @property
    def description(self):
        """Gets the description of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The description of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetImageSyncStatusResponse.


        :param description: The description of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def end_time(self):
        """Gets the end_time of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The end_time of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetImageSyncStatusResponse.


        :param end_time: The end_time of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def image_cache_description(self):
        """Gets the image_cache_description of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_cache_description of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_cache_description

    @image_cache_description.setter
    def image_cache_description(self, image_cache_description):
        """Sets the image_cache_description of this GetImageSyncStatusResponse.


        :param image_cache_description: The image_cache_description of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._image_cache_description = image_cache_description

    @property
    def image_cache_enabled(self):
        """Gets the image_cache_enabled of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_cache_enabled of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: bool
        """
        return self._image_cache_enabled

    @image_cache_enabled.setter
    def image_cache_enabled(self, image_cache_enabled):
        """Sets the image_cache_enabled of this GetImageSyncStatusResponse.


        :param image_cache_enabled: The image_cache_enabled of this GetImageSyncStatusResponse.  # noqa: E501
        :type: bool
        """

        self._image_cache_enabled = image_cache_enabled

    @property
    def image_cache_expire_date_time(self):
        """Gets the image_cache_expire_date_time of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_cache_expire_date_time of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_cache_expire_date_time

    @image_cache_expire_date_time.setter
    def image_cache_expire_date_time(self, image_cache_expire_date_time):
        """Sets the image_cache_expire_date_time of this GetImageSyncStatusResponse.


        :param image_cache_expire_date_time: The image_cache_expire_date_time of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._image_cache_expire_date_time = image_cache_expire_date_time

    @property
    def image_cache_status(self):
        """Gets the image_cache_status of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_cache_status of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_cache_status

    @image_cache_status.setter
    def image_cache_status(self, image_cache_status):
        """Sets the image_cache_status of this GetImageSyncStatusResponse.


        :param image_cache_status: The image_cache_status of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._image_cache_status = image_cache_status

    @property
    def image_preload_enabled(self):
        """Gets the image_preload_enabled of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_preload_enabled of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: bool
        """
        return self._image_preload_enabled

    @image_preload_enabled.setter
    def image_preload_enabled(self, image_preload_enabled):
        """Sets the image_preload_enabled of this GetImageSyncStatusResponse.


        :param image_preload_enabled: The image_preload_enabled of this GetImageSyncStatusResponse.  # noqa: E501
        :type: bool
        """

        self._image_preload_enabled = image_preload_enabled

    @property
    def image_preload_status(self):
        """Gets the image_preload_status of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The image_preload_status of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_preload_status

    @image_preload_status.setter
    def image_preload_status(self, image_preload_status):
        """Sets the image_preload_status of this GetImageSyncStatusResponse.


        :param image_preload_status: The image_preload_status of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._image_preload_status = image_preload_status

    @property
    def start_time(self):
        """Gets the start_time of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The start_time of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GetImageSyncStatusResponse.


        :param start_time: The start_time of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this GetImageSyncStatusResponse.  # noqa: E501


        :return: The status of this GetImageSyncStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetImageSyncStatusResponse.


        :param status: The status of this GetImageSyncStatusResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetImageSyncStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetImageSyncStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetImageSyncStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
