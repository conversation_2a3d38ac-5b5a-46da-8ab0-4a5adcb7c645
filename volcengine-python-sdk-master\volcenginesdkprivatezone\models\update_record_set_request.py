# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateRecordSetRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'record_set_id': 'str',
        'weight_enabled': 'bool',
        'zid': 'int'
    }

    attribute_map = {
        'record_set_id': 'RecordSetID',
        'weight_enabled': 'WeightEnabled',
        'zid': 'ZID'
    }

    def __init__(self, record_set_id=None, weight_enabled=None, zid=None, _configuration=None):  # noqa: E501
        """UpdateRecordSetRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._record_set_id = None
        self._weight_enabled = None
        self._zid = None
        self.discriminator = None

        self.record_set_id = record_set_id
        self.weight_enabled = weight_enabled
        if zid is not None:
            self.zid = zid

    @property
    def record_set_id(self):
        """Gets the record_set_id of this UpdateRecordSetRequest.  # noqa: E501


        :return: The record_set_id of this UpdateRecordSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._record_set_id

    @record_set_id.setter
    def record_set_id(self, record_set_id):
        """Sets the record_set_id of this UpdateRecordSetRequest.


        :param record_set_id: The record_set_id of this UpdateRecordSetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and record_set_id is None:
            raise ValueError("Invalid value for `record_set_id`, must not be `None`")  # noqa: E501

        self._record_set_id = record_set_id

    @property
    def weight_enabled(self):
        """Gets the weight_enabled of this UpdateRecordSetRequest.  # noqa: E501


        :return: The weight_enabled of this UpdateRecordSetRequest.  # noqa: E501
        :rtype: bool
        """
        return self._weight_enabled

    @weight_enabled.setter
    def weight_enabled(self, weight_enabled):
        """Sets the weight_enabled of this UpdateRecordSetRequest.


        :param weight_enabled: The weight_enabled of this UpdateRecordSetRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and weight_enabled is None:
            raise ValueError("Invalid value for `weight_enabled`, must not be `None`")  # noqa: E501

        self._weight_enabled = weight_enabled

    @property
    def zid(self):
        """Gets the zid of this UpdateRecordSetRequest.  # noqa: E501


        :return: The zid of this UpdateRecordSetRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this UpdateRecordSetRequest.


        :param zid: The zid of this UpdateRecordSetRequest.  # noqa: E501
        :type: int
        """

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateRecordSetRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateRecordSetRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateRecordSetRequest):
            return True

        return self.to_dict() != other.to_dict()
