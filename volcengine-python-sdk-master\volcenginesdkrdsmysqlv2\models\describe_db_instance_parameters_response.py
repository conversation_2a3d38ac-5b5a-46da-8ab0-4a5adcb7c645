# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceParametersResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_engine': 'str',
        'db_engine_version': 'str',
        'instance_id': 'str',
        'parameter_count': 'int',
        'parameters': 'list[ParameterForDescribeDBInstanceParametersOutput]'
    }

    attribute_map = {
        'db_engine': 'DBEngine',
        'db_engine_version': 'DBEngineVersion',
        'instance_id': 'InstanceId',
        'parameter_count': 'ParameterCount',
        'parameters': 'Parameters'
    }

    def __init__(self, db_engine=None, db_engine_version=None, instance_id=None, parameter_count=None, parameters=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceParametersResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_engine = None
        self._db_engine_version = None
        self._instance_id = None
        self._parameter_count = None
        self._parameters = None
        self.discriminator = None

        if db_engine is not None:
            self.db_engine = db_engine
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if instance_id is not None:
            self.instance_id = instance_id
        if parameter_count is not None:
            self.parameter_count = parameter_count
        if parameters is not None:
            self.parameters = parameters

    @property
    def db_engine(self):
        """Gets the db_engine of this DescribeDBInstanceParametersResponse.  # noqa: E501


        :return: The db_engine of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :rtype: str
        """
        return self._db_engine

    @db_engine.setter
    def db_engine(self, db_engine):
        """Sets the db_engine of this DescribeDBInstanceParametersResponse.


        :param db_engine: The db_engine of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :type: str
        """

        self._db_engine = db_engine

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this DescribeDBInstanceParametersResponse.  # noqa: E501


        :return: The db_engine_version of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this DescribeDBInstanceParametersResponse.


        :param db_engine_version: The db_engine_version of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBInstanceParametersResponse.  # noqa: E501


        :return: The instance_id of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBInstanceParametersResponse.


        :param instance_id: The instance_id of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def parameter_count(self):
        """Gets the parameter_count of this DescribeDBInstanceParametersResponse.  # noqa: E501


        :return: The parameter_count of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :rtype: int
        """
        return self._parameter_count

    @parameter_count.setter
    def parameter_count(self, parameter_count):
        """Sets the parameter_count of this DescribeDBInstanceParametersResponse.


        :param parameter_count: The parameter_count of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :type: int
        """

        self._parameter_count = parameter_count

    @property
    def parameters(self):
        """Gets the parameters of this DescribeDBInstanceParametersResponse.  # noqa: E501


        :return: The parameters of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :rtype: list[ParameterForDescribeDBInstanceParametersOutput]
        """
        return self._parameters

    @parameters.setter
    def parameters(self, parameters):
        """Sets the parameters of this DescribeDBInstanceParametersResponse.


        :param parameters: The parameters of this DescribeDBInstanceParametersResponse.  # noqa: E501
        :type: list[ParameterForDescribeDBInstanceParametersOutput]
        """

        self._parameters = parameters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceParametersResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceParametersResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceParametersResponse):
            return True

        return self.to_dict() != other.to_dict()
