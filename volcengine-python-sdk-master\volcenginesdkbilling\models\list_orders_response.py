# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListOrdersResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'order_infos': 'list[OrderInfoForListOrdersOutput]'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'order_infos': 'OrderInfos'
    }

    def __init__(self, max_results=None, next_token=None, order_infos=None, _configuration=None):  # noqa: E501
        """ListOrdersResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._order_infos = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if order_infos is not None:
            self.order_infos = order_infos

    @property
    def max_results(self):
        """Gets the max_results of this ListOrdersResponse.  # noqa: E501


        :return: The max_results of this ListOrdersResponse.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListOrdersResponse.


        :param max_results: The max_results of this ListOrdersResponse.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListOrdersResponse.  # noqa: E501


        :return: The next_token of this ListOrdersResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListOrdersResponse.


        :param next_token: The next_token of this ListOrdersResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def order_infos(self):
        """Gets the order_infos of this ListOrdersResponse.  # noqa: E501


        :return: The order_infos of this ListOrdersResponse.  # noqa: E501
        :rtype: list[OrderInfoForListOrdersOutput]
        """
        return self._order_infos

    @order_infos.setter
    def order_infos(self, order_infos):
        """Sets the order_infos of this ListOrdersResponse.


        :param order_infos: The order_infos of this ListOrdersResponse.  # noqa: E501
        :type: list[OrderInfoForListOrdersOutput]
        """

        self._order_infos = order_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListOrdersResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListOrdersResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListOrdersResponse):
            return True

        return self.to_dict() != other.to_dict()
