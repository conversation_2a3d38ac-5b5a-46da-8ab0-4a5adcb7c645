# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstallAgentProxyServerCommandResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'install_command': 'str',
        'uninstall_command': 'str'
    }

    attribute_map = {
        'install_command': 'InstallCommand',
        'uninstall_command': 'UninstallCommand'
    }

    def __init__(self, install_command=None, uninstall_command=None, _configuration=None):  # noqa: E501
        """InstallAgentProxyServerCommandResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._install_command = None
        self._uninstall_command = None
        self.discriminator = None

        if install_command is not None:
            self.install_command = install_command
        if uninstall_command is not None:
            self.uninstall_command = uninstall_command

    @property
    def install_command(self):
        """Gets the install_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501


        :return: The install_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501
        :rtype: str
        """
        return self._install_command

    @install_command.setter
    def install_command(self, install_command):
        """Sets the install_command of this InstallAgentProxyServerCommandResponse.


        :param install_command: The install_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501
        :type: str
        """

        self._install_command = install_command

    @property
    def uninstall_command(self):
        """Gets the uninstall_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501


        :return: The uninstall_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501
        :rtype: str
        """
        return self._uninstall_command

    @uninstall_command.setter
    def uninstall_command(self, uninstall_command):
        """Sets the uninstall_command of this InstallAgentProxyServerCommandResponse.


        :param uninstall_command: The uninstall_command of this InstallAgentProxyServerCommandResponse.  # noqa: E501
        :type: str
        """

        self._uninstall_command = uninstall_command

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstallAgentProxyServerCommandResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstallAgentProxyServerCommandResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstallAgentProxyServerCommandResponse):
            return True

        return self.to_dict() != other.to_dict()
