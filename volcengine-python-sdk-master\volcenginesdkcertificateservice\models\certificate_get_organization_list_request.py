# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateGetOrganizationListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'organization_ids': 'list[str]',
        'organization_type': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag': 'str',
        'tag_filters': 'list[TagFilterForCertificateGetOrganizationListInput]'
    }

    attribute_map = {
        'organization_ids': 'OrganizationIds',
        'organization_type': 'OrganizationType',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag': 'Tag',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, organization_ids=None, organization_type=None, page_number=None, page_size=None, project_name=None, tag=None, tag_filters=None, _configuration=None):  # noqa: E501
        """CertificateGetOrganizationListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._organization_ids = None
        self._organization_type = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag = None
        self._tag_filters = None
        self.discriminator = None

        if organization_ids is not None:
            self.organization_ids = organization_ids
        if organization_type is not None:
            self.organization_type = organization_type
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag is not None:
            self.tag = tag
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def organization_ids(self):
        """Gets the organization_ids of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The organization_ids of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._organization_ids

    @organization_ids.setter
    def organization_ids(self, organization_ids):
        """Sets the organization_ids of this CertificateGetOrganizationListRequest.


        :param organization_ids: The organization_ids of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: list[str]
        """

        self._organization_ids = organization_ids

    @property
    def organization_type(self):
        """Gets the organization_type of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The organization_type of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: str
        """
        return self._organization_type

    @organization_type.setter
    def organization_type(self, organization_type):
        """Sets the organization_type of this CertificateGetOrganizationListRequest.


        :param organization_type: The organization_type of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Enterprise", "Individual"]  # noqa: E501
        if (self._configuration.client_side_validation and
                organization_type not in allowed_values):
            raise ValueError(
                "Invalid value for `organization_type` ({0}), must be one of {1}"  # noqa: E501
                .format(organization_type, allowed_values)
            )

        self._organization_type = organization_type

    @property
    def page_number(self):
        """Gets the page_number of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The page_number of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this CertificateGetOrganizationListRequest.


        :param page_number: The page_number of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The page_size of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this CertificateGetOrganizationListRequest.


        :param page_size: The page_size of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The project_name of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CertificateGetOrganizationListRequest.


        :param project_name: The project_name of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag(self):
        """Gets the tag of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The tag of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this CertificateGetOrganizationListRequest.


        :param tag: The tag of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tag_filters(self):
        """Gets the tag_filters of this CertificateGetOrganizationListRequest.  # noqa: E501


        :return: The tag_filters of this CertificateGetOrganizationListRequest.  # noqa: E501
        :rtype: list[TagFilterForCertificateGetOrganizationListInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this CertificateGetOrganizationListRequest.


        :param tag_filters: The tag_filters of this CertificateGetOrganizationListRequest.  # noqa: E501
        :type: list[TagFilterForCertificateGetOrganizationListInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateGetOrganizationListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateGetOrganizationListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateGetOrganizationListRequest):
            return True

        return self.to_dict() != other.to_dict()
