# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetApiV1OverviewSecurityScoresResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_vendors': 'list[CloudVendorForGetApiV1OverviewSecurityScoresOutput]',
        'overview': 'str'
    }

    attribute_map = {
        'cloud_vendors': 'cloud_vendors',
        'overview': 'overview'
    }

    def __init__(self, cloud_vendors=None, overview=None, _configuration=None):  # noqa: E501
        """GetApiV1OverviewSecurityScoresResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_vendors = None
        self._overview = None
        self.discriminator = None

        if cloud_vendors is not None:
            self.cloud_vendors = cloud_vendors
        if overview is not None:
            self.overview = overview

    @property
    def cloud_vendors(self):
        """Gets the cloud_vendors of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501


        :return: The cloud_vendors of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501
        :rtype: list[CloudVendorForGetApiV1OverviewSecurityScoresOutput]
        """
        return self._cloud_vendors

    @cloud_vendors.setter
    def cloud_vendors(self, cloud_vendors):
        """Sets the cloud_vendors of this GetApiV1OverviewSecurityScoresResponse.


        :param cloud_vendors: The cloud_vendors of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501
        :type: list[CloudVendorForGetApiV1OverviewSecurityScoresOutput]
        """

        self._cloud_vendors = cloud_vendors

    @property
    def overview(self):
        """Gets the overview of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501


        :return: The overview of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501
        :rtype: str
        """
        return self._overview

    @overview.setter
    def overview(self, overview):
        """Sets the overview of this GetApiV1OverviewSecurityScoresResponse.


        :param overview: The overview of this GetApiV1OverviewSecurityScoresResponse.  # noqa: E501
        :type: str
        """

        self._overview = overview

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetApiV1OverviewSecurityScoresResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetApiV1OverviewSecurityScoresResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetApiV1OverviewSecurityScoresResponse):
            return True

        return self.to_dict() != other.to_dict()
