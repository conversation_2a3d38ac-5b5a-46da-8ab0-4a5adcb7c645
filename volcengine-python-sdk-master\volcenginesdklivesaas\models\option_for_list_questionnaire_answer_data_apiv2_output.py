# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OptionForListQuestionnaireAnswerDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'option_id': 'int',
        'option_name': 'str'
    }

    attribute_map = {
        'option_id': 'OptionId',
        'option_name': 'OptionName'
    }

    def __init__(self, option_id=None, option_name=None, _configuration=None):  # noqa: E501
        """OptionForListQuestionnaireAnswerDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._option_id = None
        self._option_name = None
        self.discriminator = None

        if option_id is not None:
            self.option_id = option_id
        if option_name is not None:
            self.option_name = option_name

    @property
    def option_id(self):
        """Gets the option_id of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The option_id of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._option_id

    @option_id.setter
    def option_id(self, option_id):
        """Sets the option_id of this OptionForListQuestionnaireAnswerDataAPIV2Output.


        :param option_id: The option_id of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._option_id = option_id

    @property
    def option_name(self):
        """Gets the option_name of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The option_name of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._option_name

    @option_name.setter
    def option_name(self, option_name):
        """Sets the option_name of this OptionForListQuestionnaireAnswerDataAPIV2Output.


        :param option_name: The option_name of this OptionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._option_name = option_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OptionForListQuestionnaireAnswerDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionForListQuestionnaireAnswerDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionForListQuestionnaireAnswerDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
