# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusForListBatchInferenceJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message': 'str',
        'phase': 'str',
        'phase_time': 'str'
    }

    attribute_map = {
        'message': 'Message',
        'phase': 'Phase',
        'phase_time': 'PhaseTime'
    }

    def __init__(self, message=None, phase=None, phase_time=None, _configuration=None):  # noqa: E501
        """StatusForListBatchInferenceJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message = None
        self._phase = None
        self._phase_time = None
        self.discriminator = None

        if message is not None:
            self.message = message
        if phase is not None:
            self.phase = phase
        if phase_time is not None:
            self.phase_time = phase_time

    @property
    def message(self):
        """Gets the message of this StatusForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The message of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this StatusForListBatchInferenceJobsOutput.


        :param message: The message of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def phase(self):
        """Gets the phase of this StatusForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The phase of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._phase

    @phase.setter
    def phase(self, phase):
        """Sets the phase of this StatusForListBatchInferenceJobsOutput.


        :param phase: The phase of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._phase = phase

    @property
    def phase_time(self):
        """Gets the phase_time of this StatusForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The phase_time of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._phase_time

    @phase_time.setter
    def phase_time(self, phase_time):
        """Sets the phase_time of this StatusForListBatchInferenceJobsOutput.


        :param phase_time: The phase_time of this StatusForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._phase_time = phase_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusForListBatchInferenceJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusForListBatchInferenceJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusForListBatchInferenceJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
