# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAvailableResourceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_zones': 'list[AvailableZoneForDescribeAvailableResourceOutput]'
    }

    attribute_map = {
        'available_zones': 'AvailableZones'
    }

    def __init__(self, available_zones=None, _configuration=None):  # noqa: E501
        """DescribeAvailableResourceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_zones = None
        self.discriminator = None

        if available_zones is not None:
            self.available_zones = available_zones

    @property
    def available_zones(self):
        """Gets the available_zones of this DescribeAvailableResourceResponse.  # noqa: E501


        :return: The available_zones of this DescribeAvailableResourceResponse.  # noqa: E501
        :rtype: list[AvailableZoneForDescribeAvailableResourceOutput]
        """
        return self._available_zones

    @available_zones.setter
    def available_zones(self, available_zones):
        """Sets the available_zones of this DescribeAvailableResourceResponse.


        :param available_zones: The available_zones of this DescribeAvailableResourceResponse.  # noqa: E501
        :type: list[AvailableZoneForDescribeAvailableResourceOutput]
        """

        self._available_zones = available_zones

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAvailableResourceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAvailableResourceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAvailableResourceResponse):
            return True

        return self.to_dict() != other.to_dict()
