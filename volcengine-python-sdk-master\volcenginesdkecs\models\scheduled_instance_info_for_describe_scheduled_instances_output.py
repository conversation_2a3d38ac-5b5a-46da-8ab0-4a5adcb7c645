# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ScheduledInstanceInfoForDescribeScheduledInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_release_at': 'str',
        'count': 'int',
        'created_at': 'str',
        'delivery_count': 'int',
        'delivery_type': 'str',
        'elastic_scheduled_instance_type': 'str',
        'end_delivery_at': 'str',
        'instance_config': 'InstanceConfigForDescribeScheduledInstancesOutput',
        'invalid_at': 'str',
        'project_name': 'str',
        'release_count': 'int',
        'scheduled_instance_description': 'str',
        'scheduled_instance_id': 'str',
        'scheduled_instance_name': 'str',
        'start_delivery_at': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeScheduledInstancesOutput]',
        'updated_at': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'auto_release_at': 'AutoReleaseAt',
        'count': 'Count',
        'created_at': 'CreatedAt',
        'delivery_count': 'DeliveryCount',
        'delivery_type': 'DeliveryType',
        'elastic_scheduled_instance_type': 'ElasticScheduledInstanceType',
        'end_delivery_at': 'EndDeliveryAt',
        'instance_config': 'InstanceConfig',
        'invalid_at': 'InvalidAt',
        'project_name': 'ProjectName',
        'release_count': 'ReleaseCount',
        'scheduled_instance_description': 'ScheduledInstanceDescription',
        'scheduled_instance_id': 'ScheduledInstanceId',
        'scheduled_instance_name': 'ScheduledInstanceName',
        'start_delivery_at': 'StartDeliveryAt',
        'status': 'Status',
        'tags': 'Tags',
        'updated_at': 'UpdatedAt',
        'zone_id': 'ZoneId'
    }

    def __init__(self, auto_release_at=None, count=None, created_at=None, delivery_count=None, delivery_type=None, elastic_scheduled_instance_type=None, end_delivery_at=None, instance_config=None, invalid_at=None, project_name=None, release_count=None, scheduled_instance_description=None, scheduled_instance_id=None, scheduled_instance_name=None, start_delivery_at=None, status=None, tags=None, updated_at=None, zone_id=None, _configuration=None):  # noqa: E501
        """ScheduledInstanceInfoForDescribeScheduledInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_release_at = None
        self._count = None
        self._created_at = None
        self._delivery_count = None
        self._delivery_type = None
        self._elastic_scheduled_instance_type = None
        self._end_delivery_at = None
        self._instance_config = None
        self._invalid_at = None
        self._project_name = None
        self._release_count = None
        self._scheduled_instance_description = None
        self._scheduled_instance_id = None
        self._scheduled_instance_name = None
        self._start_delivery_at = None
        self._status = None
        self._tags = None
        self._updated_at = None
        self._zone_id = None
        self.discriminator = None

        if auto_release_at is not None:
            self.auto_release_at = auto_release_at
        if count is not None:
            self.count = count
        if created_at is not None:
            self.created_at = created_at
        if delivery_count is not None:
            self.delivery_count = delivery_count
        if delivery_type is not None:
            self.delivery_type = delivery_type
        if elastic_scheduled_instance_type is not None:
            self.elastic_scheduled_instance_type = elastic_scheduled_instance_type
        if end_delivery_at is not None:
            self.end_delivery_at = end_delivery_at
        if instance_config is not None:
            self.instance_config = instance_config
        if invalid_at is not None:
            self.invalid_at = invalid_at
        if project_name is not None:
            self.project_name = project_name
        if release_count is not None:
            self.release_count = release_count
        if scheduled_instance_description is not None:
            self.scheduled_instance_description = scheduled_instance_description
        if scheduled_instance_id is not None:
            self.scheduled_instance_id = scheduled_instance_id
        if scheduled_instance_name is not None:
            self.scheduled_instance_name = scheduled_instance_name
        if start_delivery_at is not None:
            self.start_delivery_at = start_delivery_at
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if updated_at is not None:
            self.updated_at = updated_at
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def auto_release_at(self):
        """Gets the auto_release_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The auto_release_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_release_at

    @auto_release_at.setter
    def auto_release_at(self, auto_release_at):
        """Sets the auto_release_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param auto_release_at: The auto_release_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._auto_release_at = auto_release_at

    @property
    def count(self):
        """Gets the count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param count: The count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def created_at(self):
        """Gets the created_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The created_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param created_at: The created_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def delivery_count(self):
        """Gets the delivery_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The delivery_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._delivery_count

    @delivery_count.setter
    def delivery_count(self, delivery_count):
        """Sets the delivery_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param delivery_count: The delivery_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: int
        """

        self._delivery_count = delivery_count

    @property
    def delivery_type(self):
        """Gets the delivery_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The delivery_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._delivery_type

    @delivery_type.setter
    def delivery_type(self, delivery_type):
        """Sets the delivery_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param delivery_type: The delivery_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._delivery_type = delivery_type

    @property
    def elastic_scheduled_instance_type(self):
        """Gets the elastic_scheduled_instance_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The elastic_scheduled_instance_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._elastic_scheduled_instance_type

    @elastic_scheduled_instance_type.setter
    def elastic_scheduled_instance_type(self, elastic_scheduled_instance_type):
        """Sets the elastic_scheduled_instance_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param elastic_scheduled_instance_type: The elastic_scheduled_instance_type of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._elastic_scheduled_instance_type = elastic_scheduled_instance_type

    @property
    def end_delivery_at(self):
        """Gets the end_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The end_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_delivery_at

    @end_delivery_at.setter
    def end_delivery_at(self, end_delivery_at):
        """Sets the end_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param end_delivery_at: The end_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._end_delivery_at = end_delivery_at

    @property
    def instance_config(self):
        """Gets the instance_config of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The instance_config of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: InstanceConfigForDescribeScheduledInstancesOutput
        """
        return self._instance_config

    @instance_config.setter
    def instance_config(self, instance_config):
        """Sets the instance_config of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param instance_config: The instance_config of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: InstanceConfigForDescribeScheduledInstancesOutput
        """

        self._instance_config = instance_config

    @property
    def invalid_at(self):
        """Gets the invalid_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The invalid_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._invalid_at

    @invalid_at.setter
    def invalid_at(self, invalid_at):
        """Sets the invalid_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param invalid_at: The invalid_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._invalid_at = invalid_at

    @property
    def project_name(self):
        """Gets the project_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The project_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param project_name: The project_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def release_count(self):
        """Gets the release_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The release_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._release_count

    @release_count.setter
    def release_count(self, release_count):
        """Sets the release_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param release_count: The release_count of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: int
        """

        self._release_count = release_count

    @property
    def scheduled_instance_description(self):
        """Gets the scheduled_instance_description of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The scheduled_instance_description of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scheduled_instance_description

    @scheduled_instance_description.setter
    def scheduled_instance_description(self, scheduled_instance_description):
        """Sets the scheduled_instance_description of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param scheduled_instance_description: The scheduled_instance_description of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._scheduled_instance_description = scheduled_instance_description

    @property
    def scheduled_instance_id(self):
        """Gets the scheduled_instance_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The scheduled_instance_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scheduled_instance_id

    @scheduled_instance_id.setter
    def scheduled_instance_id(self, scheduled_instance_id):
        """Sets the scheduled_instance_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param scheduled_instance_id: The scheduled_instance_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._scheduled_instance_id = scheduled_instance_id

    @property
    def scheduled_instance_name(self):
        """Gets the scheduled_instance_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The scheduled_instance_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scheduled_instance_name

    @scheduled_instance_name.setter
    def scheduled_instance_name(self, scheduled_instance_name):
        """Sets the scheduled_instance_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param scheduled_instance_name: The scheduled_instance_name of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._scheduled_instance_name = scheduled_instance_name

    @property
    def start_delivery_at(self):
        """Gets the start_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The start_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_delivery_at

    @start_delivery_at.setter
    def start_delivery_at(self, start_delivery_at):
        """Sets the start_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param start_delivery_at: The start_delivery_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._start_delivery_at = start_delivery_at

    @property
    def status(self):
        """Gets the status of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The status of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param status: The status of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The tags of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: list[TagForDescribeScheduledInstancesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param tags: The tags of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: list[TagForDescribeScheduledInstancesOutput]
        """

        self._tags = tags

    @property
    def updated_at(self):
        """Gets the updated_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The updated_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param updated_at: The updated_at of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def zone_id(self):
        """Gets the zone_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501


        :return: The zone_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.


        :param zone_id: The zone_id of this ScheduledInstanceInfoForDescribeScheduledInstancesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ScheduledInstanceInfoForDescribeScheduledInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ScheduledInstanceInfoForDescribeScheduledInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ScheduledInstanceInfoForDescribeScheduledInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
