# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrincipalForListPrincipalsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'external': 'bool',
        'id': 'str',
        'resource_shares': 'list[ResourceShareForListPrincipalsOutput]'
    }

    attribute_map = {
        'external': 'External',
        'id': 'Id',
        'resource_shares': 'ResourceShares'
    }

    def __init__(self, external=None, id=None, resource_shares=None, _configuration=None):  # noqa: E501
        """PrincipalForListPrincipalsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._external = None
        self._id = None
        self._resource_shares = None
        self.discriminator = None

        if external is not None:
            self.external = external
        if id is not None:
            self.id = id
        if resource_shares is not None:
            self.resource_shares = resource_shares

    @property
    def external(self):
        """Gets the external of this PrincipalForListPrincipalsOutput.  # noqa: E501


        :return: The external of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._external

    @external.setter
    def external(self, external):
        """Sets the external of this PrincipalForListPrincipalsOutput.


        :param external: The external of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :type: bool
        """

        self._external = external

    @property
    def id(self):
        """Gets the id of this PrincipalForListPrincipalsOutput.  # noqa: E501


        :return: The id of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this PrincipalForListPrincipalsOutput.


        :param id: The id of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def resource_shares(self):
        """Gets the resource_shares of this PrincipalForListPrincipalsOutput.  # noqa: E501


        :return: The resource_shares of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :rtype: list[ResourceShareForListPrincipalsOutput]
        """
        return self._resource_shares

    @resource_shares.setter
    def resource_shares(self, resource_shares):
        """Sets the resource_shares of this PrincipalForListPrincipalsOutput.


        :param resource_shares: The resource_shares of this PrincipalForListPrincipalsOutput.  # noqa: E501
        :type: list[ResourceShareForListPrincipalsOutput]
        """

        self._resource_shares = resource_shares

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrincipalForListPrincipalsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrincipalForListPrincipalsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrincipalForListPrincipalsOutput):
            return True

        return self.to_dict() != other.to_dict()
