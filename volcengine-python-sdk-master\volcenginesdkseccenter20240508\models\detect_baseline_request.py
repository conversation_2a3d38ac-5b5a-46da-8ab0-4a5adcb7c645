# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DetectBaselineRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_ids': 'list[str]',
        'asset_type': 'str',
        'baseline_list': 'list[int]',
        'check_list': 'list[int]',
        'conditions': 'ConditionsForDetectBaselineInput',
        'group_id': 'int',
        'host_list': 'list[str]',
        'if_all_host': 'bool',
        'if_all_mlp': 'bool',
        'leaf_group_ids': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'asset_ids': 'AssetIDs',
        'asset_type': 'AssetType',
        'baseline_list': 'BaselineList',
        'check_list': 'CheckList',
        'conditions': 'Conditions',
        'group_id': 'GroupID',
        'host_list': 'HostList',
        'if_all_host': 'IfAllHost',
        'if_all_mlp': 'IfAllMlp',
        'leaf_group_ids': 'LeafGroupIDs',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, asset_ids=None, asset_type=None, baseline_list=None, check_list=None, conditions=None, group_id=None, host_list=None, if_all_host=None, if_all_mlp=None, leaf_group_ids=None, top_group_id=None, _configuration=None):  # noqa: E501
        """DetectBaselineRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_ids = None
        self._asset_type = None
        self._baseline_list = None
        self._check_list = None
        self._conditions = None
        self._group_id = None
        self._host_list = None
        self._if_all_host = None
        self._if_all_mlp = None
        self._leaf_group_ids = None
        self._top_group_id = None
        self.discriminator = None

        if asset_ids is not None:
            self.asset_ids = asset_ids
        if asset_type is not None:
            self.asset_type = asset_type
        if baseline_list is not None:
            self.baseline_list = baseline_list
        if check_list is not None:
            self.check_list = check_list
        if conditions is not None:
            self.conditions = conditions
        if group_id is not None:
            self.group_id = group_id
        if host_list is not None:
            self.host_list = host_list
        if if_all_host is not None:
            self.if_all_host = if_all_host
        if if_all_mlp is not None:
            self.if_all_mlp = if_all_mlp
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def asset_ids(self):
        """Gets the asset_ids of this DetectBaselineRequest.  # noqa: E501


        :return: The asset_ids of this DetectBaselineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_ids

    @asset_ids.setter
    def asset_ids(self, asset_ids):
        """Sets the asset_ids of this DetectBaselineRequest.


        :param asset_ids: The asset_ids of this DetectBaselineRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_ids = asset_ids

    @property
    def asset_type(self):
        """Gets the asset_type of this DetectBaselineRequest.  # noqa: E501


        :return: The asset_type of this DetectBaselineRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this DetectBaselineRequest.


        :param asset_type: The asset_type of this DetectBaselineRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def baseline_list(self):
        """Gets the baseline_list of this DetectBaselineRequest.  # noqa: E501


        :return: The baseline_list of this DetectBaselineRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._baseline_list

    @baseline_list.setter
    def baseline_list(self, baseline_list):
        """Sets the baseline_list of this DetectBaselineRequest.


        :param baseline_list: The baseline_list of this DetectBaselineRequest.  # noqa: E501
        :type: list[int]
        """

        self._baseline_list = baseline_list

    @property
    def check_list(self):
        """Gets the check_list of this DetectBaselineRequest.  # noqa: E501


        :return: The check_list of this DetectBaselineRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._check_list

    @check_list.setter
    def check_list(self, check_list):
        """Sets the check_list of this DetectBaselineRequest.


        :param check_list: The check_list of this DetectBaselineRequest.  # noqa: E501
        :type: list[int]
        """

        self._check_list = check_list

    @property
    def conditions(self):
        """Gets the conditions of this DetectBaselineRequest.  # noqa: E501


        :return: The conditions of this DetectBaselineRequest.  # noqa: E501
        :rtype: ConditionsForDetectBaselineInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this DetectBaselineRequest.


        :param conditions: The conditions of this DetectBaselineRequest.  # noqa: E501
        :type: ConditionsForDetectBaselineInput
        """

        self._conditions = conditions

    @property
    def group_id(self):
        """Gets the group_id of this DetectBaselineRequest.  # noqa: E501


        :return: The group_id of this DetectBaselineRequest.  # noqa: E501
        :rtype: int
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this DetectBaselineRequest.


        :param group_id: The group_id of this DetectBaselineRequest.  # noqa: E501
        :type: int
        """

        self._group_id = group_id

    @property
    def host_list(self):
        """Gets the host_list of this DetectBaselineRequest.  # noqa: E501


        :return: The host_list of this DetectBaselineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._host_list

    @host_list.setter
    def host_list(self, host_list):
        """Sets the host_list of this DetectBaselineRequest.


        :param host_list: The host_list of this DetectBaselineRequest.  # noqa: E501
        :type: list[str]
        """

        self._host_list = host_list

    @property
    def if_all_host(self):
        """Gets the if_all_host of this DetectBaselineRequest.  # noqa: E501


        :return: The if_all_host of this DetectBaselineRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_all_host

    @if_all_host.setter
    def if_all_host(self, if_all_host):
        """Sets the if_all_host of this DetectBaselineRequest.


        :param if_all_host: The if_all_host of this DetectBaselineRequest.  # noqa: E501
        :type: bool
        """

        self._if_all_host = if_all_host

    @property
    def if_all_mlp(self):
        """Gets the if_all_mlp of this DetectBaselineRequest.  # noqa: E501


        :return: The if_all_mlp of this DetectBaselineRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_all_mlp

    @if_all_mlp.setter
    def if_all_mlp(self, if_all_mlp):
        """Sets the if_all_mlp of this DetectBaselineRequest.


        :param if_all_mlp: The if_all_mlp of this DetectBaselineRequest.  # noqa: E501
        :type: bool
        """

        self._if_all_mlp = if_all_mlp

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this DetectBaselineRequest.  # noqa: E501


        :return: The leaf_group_ids of this DetectBaselineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this DetectBaselineRequest.


        :param leaf_group_ids: The leaf_group_ids of this DetectBaselineRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def top_group_id(self):
        """Gets the top_group_id of this DetectBaselineRequest.  # noqa: E501


        :return: The top_group_id of this DetectBaselineRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this DetectBaselineRequest.


        :param top_group_id: The top_group_id of this DetectBaselineRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DetectBaselineRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DetectBaselineRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DetectBaselineRequest):
            return True

        return self.to_dict() != other.to_dict()
