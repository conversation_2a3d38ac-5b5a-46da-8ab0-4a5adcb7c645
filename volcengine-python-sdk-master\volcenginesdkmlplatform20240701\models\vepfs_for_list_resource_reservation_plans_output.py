# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VepfsForListResourceReservationPlansOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_ids': 'list[str]',
        'prefetch_enabled': 'bool'
    }

    attribute_map = {
        'file_system_ids': 'FileSystemIds',
        'prefetch_enabled': 'PrefetchEnabled'
    }

    def __init__(self, file_system_ids=None, prefetch_enabled=None, _configuration=None):  # noqa: E501
        """VepfsForListResourceReservationPlansOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_ids = None
        self._prefetch_enabled = None
        self.discriminator = None

        if file_system_ids is not None:
            self.file_system_ids = file_system_ids
        if prefetch_enabled is not None:
            self.prefetch_enabled = prefetch_enabled

    @property
    def file_system_ids(self):
        """Gets the file_system_ids of this VepfsForListResourceReservationPlansOutput.  # noqa: E501


        :return: The file_system_ids of this VepfsForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._file_system_ids

    @file_system_ids.setter
    def file_system_ids(self, file_system_ids):
        """Sets the file_system_ids of this VepfsForListResourceReservationPlansOutput.


        :param file_system_ids: The file_system_ids of this VepfsForListResourceReservationPlansOutput.  # noqa: E501
        :type: list[str]
        """

        self._file_system_ids = file_system_ids

    @property
    def prefetch_enabled(self):
        """Gets the prefetch_enabled of this VepfsForListResourceReservationPlansOutput.  # noqa: E501


        :return: The prefetch_enabled of this VepfsForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: bool
        """
        return self._prefetch_enabled

    @prefetch_enabled.setter
    def prefetch_enabled(self, prefetch_enabled):
        """Sets the prefetch_enabled of this VepfsForListResourceReservationPlansOutput.


        :param prefetch_enabled: The prefetch_enabled of this VepfsForListResourceReservationPlansOutput.  # noqa: E501
        :type: bool
        """

        self._prefetch_enabled = prefetch_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VepfsForListResourceReservationPlansOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VepfsForListResourceReservationPlansOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VepfsForListResourceReservationPlansOutput):
            return True

        return self.to_dict() != other.to_dict()
