# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WeakPasswordForListWeakPasswordCheckDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'check_name_cn': 'str',
        'eip_address': 'str',
        'hostname': 'str',
        'id': 'str',
        'primary_ip_address': 'str',
        'weak_password': 'str',
        'weak_username': 'str',
        'check_id': 'int',
        'description_cn': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'check_name_cn': 'CheckNameCn',
        'eip_address': 'EipAddress',
        'hostname': 'Hostname',
        'id': 'ID',
        'primary_ip_address': 'PrimaryIpAddress',
        'weak_password': 'WeakPassword',
        'weak_username': 'WeakUsername',
        'check_id': 'check_id',
        'description_cn': 'description_cn'
    }

    def __init__(self, agent_id=None, check_name_cn=None, eip_address=None, hostname=None, id=None, primary_ip_address=None, weak_password=None, weak_username=None, check_id=None, description_cn=None, _configuration=None):  # noqa: E501
        """WeakPasswordForListWeakPasswordCheckDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._check_name_cn = None
        self._eip_address = None
        self._hostname = None
        self._id = None
        self._primary_ip_address = None
        self._weak_password = None
        self._weak_username = None
        self._check_id = None
        self._description_cn = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if check_name_cn is not None:
            self.check_name_cn = check_name_cn
        if eip_address is not None:
            self.eip_address = eip_address
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if weak_password is not None:
            self.weak_password = weak_password
        if weak_username is not None:
            self.weak_username = weak_username
        if check_id is not None:
            self.check_id = check_id
        if description_cn is not None:
            self.description_cn = description_cn

    @property
    def agent_id(self):
        """Gets the agent_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The agent_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param agent_id: The agent_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def check_name_cn(self):
        """Gets the check_name_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The check_name_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_name_cn

    @check_name_cn.setter
    def check_name_cn(self, check_name_cn):
        """Sets the check_name_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param check_name_cn: The check_name_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._check_name_cn = check_name_cn

    @property
    def eip_address(self):
        """Gets the eip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The eip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param eip_address: The eip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def hostname(self):
        """Gets the hostname of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The hostname of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param hostname: The hostname of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param id: The id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The primary_ip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param primary_ip_address: The primary_ip_address of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def weak_password(self):
        """Gets the weak_password of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The weak_password of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._weak_password

    @weak_password.setter
    def weak_password(self, weak_password):
        """Sets the weak_password of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param weak_password: The weak_password of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._weak_password = weak_password

    @property
    def weak_username(self):
        """Gets the weak_username of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The weak_username of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._weak_username

    @weak_username.setter
    def weak_username(self, weak_username):
        """Sets the weak_username of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param weak_username: The weak_username of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._weak_username = weak_username

    @property
    def check_id(self):
        """Gets the check_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The check_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_id

    @check_id.setter
    def check_id(self, check_id):
        """Sets the check_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param check_id: The check_id of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: int
        """

        self._check_id = check_id

    @property
    def description_cn(self):
        """Gets the description_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501


        :return: The description_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._description_cn

    @description_cn.setter
    def description_cn(self, description_cn):
        """Sets the description_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.


        :param description_cn: The description_cn of this WeakPasswordForListWeakPasswordCheckDetailOutput.  # noqa: E501
        :type: str
        """

        self._description_cn = description_cn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WeakPasswordForListWeakPasswordCheckDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WeakPasswordForListWeakPasswordCheckDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WeakPasswordForListWeakPasswordCheckDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
