# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyVolumeChargeTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_pay': 'bool',
        'disk_charge_type': 'str',
        'instance_id': 'str',
        'volume_ids': 'list[str]'
    }

    attribute_map = {
        'auto_pay': 'AutoPay',
        'disk_charge_type': 'DiskChargeType',
        'instance_id': 'InstanceId',
        'volume_ids': 'VolumeIds'
    }

    def __init__(self, auto_pay=None, disk_charge_type=None, instance_id=None, volume_ids=None, _configuration=None):  # noqa: E501
        """ModifyVolumeChargeTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_pay = None
        self._disk_charge_type = None
        self._instance_id = None
        self._volume_ids = None
        self.discriminator = None

        if auto_pay is not None:
            self.auto_pay = auto_pay
        self.disk_charge_type = disk_charge_type
        self.instance_id = instance_id
        if volume_ids is not None:
            self.volume_ids = volume_ids

    @property
    def auto_pay(self):
        """Gets the auto_pay of this ModifyVolumeChargeTypeRequest.  # noqa: E501


        :return: The auto_pay of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pay

    @auto_pay.setter
    def auto_pay(self, auto_pay):
        """Sets the auto_pay of this ModifyVolumeChargeTypeRequest.


        :param auto_pay: The auto_pay of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :type: bool
        """

        self._auto_pay = auto_pay

    @property
    def disk_charge_type(self):
        """Gets the disk_charge_type of this ModifyVolumeChargeTypeRequest.  # noqa: E501


        :return: The disk_charge_type of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._disk_charge_type

    @disk_charge_type.setter
    def disk_charge_type(self, disk_charge_type):
        """Sets the disk_charge_type of this ModifyVolumeChargeTypeRequest.


        :param disk_charge_type: The disk_charge_type of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and disk_charge_type is None:
            raise ValueError("Invalid value for `disk_charge_type`, must not be `None`")  # noqa: E501

        self._disk_charge_type = disk_charge_type

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyVolumeChargeTypeRequest.  # noqa: E501


        :return: The instance_id of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyVolumeChargeTypeRequest.


        :param instance_id: The instance_id of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def volume_ids(self):
        """Gets the volume_ids of this ModifyVolumeChargeTypeRequest.  # noqa: E501


        :return: The volume_ids of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._volume_ids

    @volume_ids.setter
    def volume_ids(self, volume_ids):
        """Sets the volume_ids of this ModifyVolumeChargeTypeRequest.


        :param volume_ids: The volume_ids of this ModifyVolumeChargeTypeRequest.  # noqa: E501
        :type: list[str]
        """

        self._volume_ids = volume_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyVolumeChargeTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyVolumeChargeTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyVolumeChargeTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
