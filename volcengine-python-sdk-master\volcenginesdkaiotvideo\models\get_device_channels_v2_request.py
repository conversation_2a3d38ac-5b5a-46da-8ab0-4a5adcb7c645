# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDeviceChannelsV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_id': 'str',
        'channel_status': 'str',
        'device_id': 'str',
        'order': 'str',
        'query_mode': 'str',
        'space_id': 'str'
    }

    attribute_map = {
        'channel_id': 'ChannelID',
        'channel_status': 'ChannelStatus',
        'device_id': 'DeviceID',
        'order': 'Order',
        'query_mode': 'QueryMode',
        'space_id': 'SpaceID'
    }

    def __init__(self, channel_id=None, channel_status=None, device_id=None, order=None, query_mode=None, space_id=None, _configuration=None):  # noqa: E501
        """GetDeviceChannelsV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_id = None
        self._channel_status = None
        self._device_id = None
        self._order = None
        self._query_mode = None
        self._space_id = None
        self.discriminator = None

        if channel_id is not None:
            self.channel_id = channel_id
        if channel_status is not None:
            self.channel_status = channel_status
        self.device_id = device_id
        if order is not None:
            self.order = order
        if query_mode is not None:
            self.query_mode = query_mode
        if space_id is not None:
            self.space_id = space_id

    @property
    def channel_id(self):
        """Gets the channel_id of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The channel_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._channel_id

    @channel_id.setter
    def channel_id(self, channel_id):
        """Sets the channel_id of this GetDeviceChannelsV2Request.


        :param channel_id: The channel_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """

        self._channel_id = channel_id

    @property
    def channel_status(self):
        """Gets the channel_status of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The channel_status of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._channel_status

    @channel_status.setter
    def channel_status(self, channel_status):
        """Sets the channel_status of this GetDeviceChannelsV2Request.


        :param channel_status: The channel_status of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """

        self._channel_status = channel_status

    @property
    def device_id(self):
        """Gets the device_id of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The device_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this GetDeviceChannelsV2Request.


        :param device_id: The device_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and device_id is None:
            raise ValueError("Invalid value for `device_id`, must not be `None`")  # noqa: E501

        self._device_id = device_id

    @property
    def order(self):
        """Gets the order of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The order of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._order

    @order.setter
    def order(self, order):
        """Sets the order of this GetDeviceChannelsV2Request.


        :param order: The order of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """

        self._order = order

    @property
    def query_mode(self):
        """Gets the query_mode of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The query_mode of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._query_mode

    @query_mode.setter
    def query_mode(self, query_mode):
        """Sets the query_mode of this GetDeviceChannelsV2Request.


        :param query_mode: The query_mode of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """

        self._query_mode = query_mode

    @property
    def space_id(self):
        """Gets the space_id of this GetDeviceChannelsV2Request.  # noqa: E501


        :return: The space_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this GetDeviceChannelsV2Request.


        :param space_id: The space_id of this GetDeviceChannelsV2Request.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDeviceChannelsV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDeviceChannelsV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDeviceChannelsV2Request):
            return True

        return self.to_dict() != other.to_dict()
