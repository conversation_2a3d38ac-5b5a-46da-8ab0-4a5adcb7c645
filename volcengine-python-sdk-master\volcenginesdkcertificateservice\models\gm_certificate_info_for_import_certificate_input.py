# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GmCertificateInfoForImportCertificateInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'encrypt_certificate_chain': 'str',
        'encrypt_private_key': 'str',
        'sign_certificate_chain': 'str',
        'sign_private_key': 'str'
    }

    attribute_map = {
        'encrypt_certificate_chain': 'EncryptCertificateChain',
        'encrypt_private_key': 'EncryptPrivateKey',
        'sign_certificate_chain': 'SignCertificateChain',
        'sign_private_key': 'SignPrivateKey'
    }

    def __init__(self, encrypt_certificate_chain=None, encrypt_private_key=None, sign_certificate_chain=None, sign_private_key=None, _configuration=None):  # noqa: E501
        """GmCertificateInfoForImportCertificateInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._encrypt_certificate_chain = None
        self._encrypt_private_key = None
        self._sign_certificate_chain = None
        self._sign_private_key = None
        self.discriminator = None

        if encrypt_certificate_chain is not None:
            self.encrypt_certificate_chain = encrypt_certificate_chain
        if encrypt_private_key is not None:
            self.encrypt_private_key = encrypt_private_key
        if sign_certificate_chain is not None:
            self.sign_certificate_chain = sign_certificate_chain
        if sign_private_key is not None:
            self.sign_private_key = sign_private_key

    @property
    def encrypt_certificate_chain(self):
        """Gets the encrypt_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The encrypt_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._encrypt_certificate_chain

    @encrypt_certificate_chain.setter
    def encrypt_certificate_chain(self, encrypt_certificate_chain):
        """Sets the encrypt_certificate_chain of this GmCertificateInfoForImportCertificateInput.


        :param encrypt_certificate_chain: The encrypt_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._encrypt_certificate_chain = encrypt_certificate_chain

    @property
    def encrypt_private_key(self):
        """Gets the encrypt_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The encrypt_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._encrypt_private_key

    @encrypt_private_key.setter
    def encrypt_private_key(self, encrypt_private_key):
        """Sets the encrypt_private_key of this GmCertificateInfoForImportCertificateInput.


        :param encrypt_private_key: The encrypt_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._encrypt_private_key = encrypt_private_key

    @property
    def sign_certificate_chain(self):
        """Gets the sign_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The sign_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._sign_certificate_chain

    @sign_certificate_chain.setter
    def sign_certificate_chain(self, sign_certificate_chain):
        """Sets the sign_certificate_chain of this GmCertificateInfoForImportCertificateInput.


        :param sign_certificate_chain: The sign_certificate_chain of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._sign_certificate_chain = sign_certificate_chain

    @property
    def sign_private_key(self):
        """Gets the sign_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The sign_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._sign_private_key

    @sign_private_key.setter
    def sign_private_key(self, sign_private_key):
        """Sets the sign_private_key of this GmCertificateInfoForImportCertificateInput.


        :param sign_private_key: The sign_private_key of this GmCertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._sign_private_key = sign_private_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GmCertificateInfoForImportCertificateInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GmCertificateInfoForImportCertificateInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GmCertificateInfoForImportCertificateInput):
            return True

        return self.to_dict() != other.to_dict()
