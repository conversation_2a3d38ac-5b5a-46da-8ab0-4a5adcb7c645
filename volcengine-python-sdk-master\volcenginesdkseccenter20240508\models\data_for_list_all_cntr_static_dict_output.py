# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAllCntrStaticDictOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dict_name': 'str',
        'mapping': 'list[MappingForListAllCntrStaticDictOutput]'
    }

    attribute_map = {
        'dict_name': 'DictName',
        'mapping': 'Mapping'
    }

    def __init__(self, dict_name=None, mapping=None, _configuration=None):  # noqa: E501
        """DataForListAllCntrStaticDictOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dict_name = None
        self._mapping = None
        self.discriminator = None

        if dict_name is not None:
            self.dict_name = dict_name
        if mapping is not None:
            self.mapping = mapping

    @property
    def dict_name(self):
        """Gets the dict_name of this DataForListAllCntrStaticDictOutput.  # noqa: E501


        :return: The dict_name of this DataForListAllCntrStaticDictOutput.  # noqa: E501
        :rtype: str
        """
        return self._dict_name

    @dict_name.setter
    def dict_name(self, dict_name):
        """Sets the dict_name of this DataForListAllCntrStaticDictOutput.


        :param dict_name: The dict_name of this DataForListAllCntrStaticDictOutput.  # noqa: E501
        :type: str
        """

        self._dict_name = dict_name

    @property
    def mapping(self):
        """Gets the mapping of this DataForListAllCntrStaticDictOutput.  # noqa: E501


        :return: The mapping of this DataForListAllCntrStaticDictOutput.  # noqa: E501
        :rtype: list[MappingForListAllCntrStaticDictOutput]
        """
        return self._mapping

    @mapping.setter
    def mapping(self, mapping):
        """Sets the mapping of this DataForListAllCntrStaticDictOutput.


        :param mapping: The mapping of this DataForListAllCntrStaticDictOutput.  # noqa: E501
        :type: list[MappingForListAllCntrStaticDictOutput]
        """

        self._mapping = mapping

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAllCntrStaticDictOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAllCntrStaticDictOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAllCntrStaticDictOutput):
            return True

        return self.to_dict() != other.to_dict()
