# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CloneLoadBalancerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_package_id': 'str',
        'delete_protection': 'str',
        'description': 'str',
        'eip_billing_config': 'EipBillingConfigForCloneLoadBalancerInput',
        'ipv6_eip_billing_config': 'Ipv6EipBillingConfigForCloneLoadBalancerInput',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'project_name': 'str',
        'region_id': 'str',
        'zone_mappings': 'list[ZoneMappingForCloneLoadBalancerInput]'
    }

    attribute_map = {
        'bandwidth_package_id': 'BandwidthPackageId',
        'delete_protection': 'DeleteProtection',
        'description': 'Description',
        'eip_billing_config': 'EipBillingConfig',
        'ipv6_eip_billing_config': 'Ipv6EipBillingConfig',
        'load_balancer_id': 'LoadBalancerID',
        'load_balancer_name': 'LoadBalancerName',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'zone_mappings': 'ZoneMappings'
    }

    def __init__(self, bandwidth_package_id=None, delete_protection=None, description=None, eip_billing_config=None, ipv6_eip_billing_config=None, load_balancer_id=None, load_balancer_name=None, project_name=None, region_id=None, zone_mappings=None, _configuration=None):  # noqa: E501
        """CloneLoadBalancerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_package_id = None
        self._delete_protection = None
        self._description = None
        self._eip_billing_config = None
        self._ipv6_eip_billing_config = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._project_name = None
        self._region_id = None
        self._zone_mappings = None
        self.discriminator = None

        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if delete_protection is not None:
            self.delete_protection = delete_protection
        if description is not None:
            self.description = description
        if eip_billing_config is not None:
            self.eip_billing_config = eip_billing_config
        if ipv6_eip_billing_config is not None:
            self.ipv6_eip_billing_config = ipv6_eip_billing_config
        self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if project_name is not None:
            self.project_name = project_name
        self.region_id = region_id
        if zone_mappings is not None:
            self.zone_mappings = zone_mappings

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The bandwidth_package_id of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this CloneLoadBalancerRequest.


        :param bandwidth_package_id: The bandwidth_package_id of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def delete_protection(self):
        """Gets the delete_protection of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The delete_protection of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._delete_protection

    @delete_protection.setter
    def delete_protection(self, delete_protection):
        """Sets the delete_protection of this CloneLoadBalancerRequest.


        :param delete_protection: The delete_protection of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._delete_protection = delete_protection

    @property
    def description(self):
        """Gets the description of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The description of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CloneLoadBalancerRequest.


        :param description: The description of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def eip_billing_config(self):
        """Gets the eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: EipBillingConfigForCloneLoadBalancerInput
        """
        return self._eip_billing_config

    @eip_billing_config.setter
    def eip_billing_config(self, eip_billing_config):
        """Sets the eip_billing_config of this CloneLoadBalancerRequest.


        :param eip_billing_config: The eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501
        :type: EipBillingConfigForCloneLoadBalancerInput
        """

        self._eip_billing_config = eip_billing_config

    @property
    def ipv6_eip_billing_config(self):
        """Gets the ipv6_eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The ipv6_eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: Ipv6EipBillingConfigForCloneLoadBalancerInput
        """
        return self._ipv6_eip_billing_config

    @ipv6_eip_billing_config.setter
    def ipv6_eip_billing_config(self, ipv6_eip_billing_config):
        """Sets the ipv6_eip_billing_config of this CloneLoadBalancerRequest.


        :param ipv6_eip_billing_config: The ipv6_eip_billing_config of this CloneLoadBalancerRequest.  # noqa: E501
        :type: Ipv6EipBillingConfigForCloneLoadBalancerInput
        """

        self._ipv6_eip_billing_config = ipv6_eip_billing_config

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The load_balancer_id of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this CloneLoadBalancerRequest.


        :param load_balancer_id: The load_balancer_id of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The load_balancer_name of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this CloneLoadBalancerRequest.


        :param load_balancer_name: The load_balancer_name of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def project_name(self):
        """Gets the project_name of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The project_name of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CloneLoadBalancerRequest.


        :param project_name: The project_name of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The region_id of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this CloneLoadBalancerRequest.


        :param region_id: The region_id of this CloneLoadBalancerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region_id is None:
            raise ValueError("Invalid value for `region_id`, must not be `None`")  # noqa: E501

        self._region_id = region_id

    @property
    def zone_mappings(self):
        """Gets the zone_mappings of this CloneLoadBalancerRequest.  # noqa: E501


        :return: The zone_mappings of this CloneLoadBalancerRequest.  # noqa: E501
        :rtype: list[ZoneMappingForCloneLoadBalancerInput]
        """
        return self._zone_mappings

    @zone_mappings.setter
    def zone_mappings(self, zone_mappings):
        """Sets the zone_mappings of this CloneLoadBalancerRequest.


        :param zone_mappings: The zone_mappings of this CloneLoadBalancerRequest.  # noqa: E501
        :type: list[ZoneMappingForCloneLoadBalancerInput]
        """

        self._zone_mappings = zone_mappings

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CloneLoadBalancerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CloneLoadBalancerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CloneLoadBalancerRequest):
            return True

        return self.to_dict() != other.to_dict()
