# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate': 'str',
        'encryption_cert': 'str',
        'encryption_key': 'str',
        'private_key': 'str'
    }

    attribute_map = {
        'certificate': 'Certificate',
        'encryption_cert': 'EncryptionCert',
        'encryption_key': 'EncryptionKey',
        'private_key': 'PrivateKey'
    }

    def __init__(self, certificate=None, encryption_cert=None, encryption_key=None, private_key=None, _configuration=None):  # noqa: E501
        """CertificateForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate = None
        self._encryption_cert = None
        self._encryption_key = None
        self._private_key = None
        self.discriminator = None

        if certificate is not None:
            self.certificate = certificate
        if encryption_cert is not None:
            self.encryption_cert = encryption_cert
        if encryption_key is not None:
            self.encryption_key = encryption_key
        if private_key is not None:
            self.private_key = private_key

    @property
    def certificate(self):
        """Gets the certificate of this CertificateForAddCdnDomainInput.  # noqa: E501


        :return: The certificate of this CertificateForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._certificate

    @certificate.setter
    def certificate(self, certificate):
        """Sets the certificate of this CertificateForAddCdnDomainInput.


        :param certificate: The certificate of this CertificateForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._certificate = certificate

    @property
    def encryption_cert(self):
        """Gets the encryption_cert of this CertificateForAddCdnDomainInput.  # noqa: E501


        :return: The encryption_cert of this CertificateForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._encryption_cert

    @encryption_cert.setter
    def encryption_cert(self, encryption_cert):
        """Sets the encryption_cert of this CertificateForAddCdnDomainInput.


        :param encryption_cert: The encryption_cert of this CertificateForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._encryption_cert = encryption_cert

    @property
    def encryption_key(self):
        """Gets the encryption_key of this CertificateForAddCdnDomainInput.  # noqa: E501


        :return: The encryption_key of this CertificateForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._encryption_key

    @encryption_key.setter
    def encryption_key(self, encryption_key):
        """Sets the encryption_key of this CertificateForAddCdnDomainInput.


        :param encryption_key: The encryption_key of this CertificateForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._encryption_key = encryption_key

    @property
    def private_key(self):
        """Gets the private_key of this CertificateForAddCdnDomainInput.  # noqa: E501


        :return: The private_key of this CertificateForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this CertificateForAddCdnDomainInput.


        :param private_key: The private_key of this CertificateForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
