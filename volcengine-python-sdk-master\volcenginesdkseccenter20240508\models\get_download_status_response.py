# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDownloadStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'export_count': 'int',
        'export_total': 'int',
        'file_name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'export_count': 'ExportCount',
        'export_total': 'ExportTotal',
        'file_name': 'FileName',
        'status': 'Status'
    }

    def __init__(self, export_count=None, export_total=None, file_name=None, status=None, _configuration=None):  # noqa: E501
        """GetDownloadStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._export_count = None
        self._export_total = None
        self._file_name = None
        self._status = None
        self.discriminator = None

        if export_count is not None:
            self.export_count = export_count
        if export_total is not None:
            self.export_total = export_total
        if file_name is not None:
            self.file_name = file_name
        if status is not None:
            self.status = status

    @property
    def export_count(self):
        """Gets the export_count of this GetDownloadStatusResponse.  # noqa: E501


        :return: The export_count of this GetDownloadStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._export_count

    @export_count.setter
    def export_count(self, export_count):
        """Sets the export_count of this GetDownloadStatusResponse.


        :param export_count: The export_count of this GetDownloadStatusResponse.  # noqa: E501
        :type: int
        """

        self._export_count = export_count

    @property
    def export_total(self):
        """Gets the export_total of this GetDownloadStatusResponse.  # noqa: E501


        :return: The export_total of this GetDownloadStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._export_total

    @export_total.setter
    def export_total(self, export_total):
        """Sets the export_total of this GetDownloadStatusResponse.


        :param export_total: The export_total of this GetDownloadStatusResponse.  # noqa: E501
        :type: int
        """

        self._export_total = export_total

    @property
    def file_name(self):
        """Gets the file_name of this GetDownloadStatusResponse.  # noqa: E501


        :return: The file_name of this GetDownloadStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this GetDownloadStatusResponse.


        :param file_name: The file_name of this GetDownloadStatusResponse.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def status(self):
        """Gets the status of this GetDownloadStatusResponse.  # noqa: E501


        :return: The status of this GetDownloadStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetDownloadStatusResponse.


        :param status: The status of this GetDownloadStatusResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDownloadStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDownloadStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDownloadStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
