# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListActivityUsersResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'online_user_count': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'total_count': 'int',
        'total_user_count': 'int',
        'user_msgs': 'list[UserMsgForListActivityUsersOutput]'
    }

    attribute_map = {
        'online_user_count': 'OnlineUserCount',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'total_count': 'TotalCount',
        'total_user_count': 'TotalUserCount',
        'user_msgs': 'UserMsgs'
    }

    def __init__(self, online_user_count=None, page_number=None, page_size=None, total_count=None, total_user_count=None, user_msgs=None, _configuration=None):  # noqa: E501
        """ListActivityUsersResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._online_user_count = None
        self._page_number = None
        self._page_size = None
        self._total_count = None
        self._total_user_count = None
        self._user_msgs = None
        self.discriminator = None

        if online_user_count is not None:
            self.online_user_count = online_user_count
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if total_count is not None:
            self.total_count = total_count
        if total_user_count is not None:
            self.total_user_count = total_user_count
        if user_msgs is not None:
            self.user_msgs = user_msgs

    @property
    def online_user_count(self):
        """Gets the online_user_count of this ListActivityUsersResponse.  # noqa: E501


        :return: The online_user_count of this ListActivityUsersResponse.  # noqa: E501
        :rtype: int
        """
        return self._online_user_count

    @online_user_count.setter
    def online_user_count(self, online_user_count):
        """Sets the online_user_count of this ListActivityUsersResponse.


        :param online_user_count: The online_user_count of this ListActivityUsersResponse.  # noqa: E501
        :type: int
        """

        self._online_user_count = online_user_count

    @property
    def page_number(self):
        """Gets the page_number of this ListActivityUsersResponse.  # noqa: E501


        :return: The page_number of this ListActivityUsersResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListActivityUsersResponse.


        :param page_number: The page_number of this ListActivityUsersResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListActivityUsersResponse.  # noqa: E501


        :return: The page_size of this ListActivityUsersResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListActivityUsersResponse.


        :param page_size: The page_size of this ListActivityUsersResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def total_count(self):
        """Gets the total_count of this ListActivityUsersResponse.  # noqa: E501


        :return: The total_count of this ListActivityUsersResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ListActivityUsersResponse.


        :param total_count: The total_count of this ListActivityUsersResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def total_user_count(self):
        """Gets the total_user_count of this ListActivityUsersResponse.  # noqa: E501


        :return: The total_user_count of this ListActivityUsersResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_user_count

    @total_user_count.setter
    def total_user_count(self, total_user_count):
        """Sets the total_user_count of this ListActivityUsersResponse.


        :param total_user_count: The total_user_count of this ListActivityUsersResponse.  # noqa: E501
        :type: int
        """

        self._total_user_count = total_user_count

    @property
    def user_msgs(self):
        """Gets the user_msgs of this ListActivityUsersResponse.  # noqa: E501


        :return: The user_msgs of this ListActivityUsersResponse.  # noqa: E501
        :rtype: list[UserMsgForListActivityUsersOutput]
        """
        return self._user_msgs

    @user_msgs.setter
    def user_msgs(self, user_msgs):
        """Sets the user_msgs of this ListActivityUsersResponse.


        :param user_msgs: The user_msgs of this ListActivityUsersResponse.  # noqa: E501
        :type: list[UserMsgForListActivityUsersOutput]
        """

        self._user_msgs = user_msgs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListActivityUsersResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListActivityUsersResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListActivityUsersResponse):
            return True

        return self.to_dict() != other.to_dict()
