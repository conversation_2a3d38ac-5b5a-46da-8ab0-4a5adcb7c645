# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OSProcForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cmd_line': 'str',
        'create_time_nano': 'int',
        'file': 'FileForGetRiskOutput',
        'os_parent_process_id': 'str',
        'os_process_cred': 'str',
        'os_process_exec_dir': 'str',
        'os_process_group_id': 'str',
        'os_process_id': 'str',
        'os_user': 'OSUserForGetRiskOutput'
    }

    attribute_map = {
        'cmd_line': 'CmdLine',
        'create_time_nano': 'CreateTimeNano',
        'file': 'File',
        'os_parent_process_id': 'OSParentProcessID',
        'os_process_cred': 'OSProcessCred',
        'os_process_exec_dir': 'OSProcessExecDir',
        'os_process_group_id': 'OSProcessGroupID',
        'os_process_id': 'OSProcessID',
        'os_user': 'OSUser'
    }

    def __init__(self, cmd_line=None, create_time_nano=None, file=None, os_parent_process_id=None, os_process_cred=None, os_process_exec_dir=None, os_process_group_id=None, os_process_id=None, os_user=None, _configuration=None):  # noqa: E501
        """OSProcForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cmd_line = None
        self._create_time_nano = None
        self._file = None
        self._os_parent_process_id = None
        self._os_process_cred = None
        self._os_process_exec_dir = None
        self._os_process_group_id = None
        self._os_process_id = None
        self._os_user = None
        self.discriminator = None

        if cmd_line is not None:
            self.cmd_line = cmd_line
        if create_time_nano is not None:
            self.create_time_nano = create_time_nano
        if file is not None:
            self.file = file
        if os_parent_process_id is not None:
            self.os_parent_process_id = os_parent_process_id
        if os_process_cred is not None:
            self.os_process_cred = os_process_cred
        if os_process_exec_dir is not None:
            self.os_process_exec_dir = os_process_exec_dir
        if os_process_group_id is not None:
            self.os_process_group_id = os_process_group_id
        if os_process_id is not None:
            self.os_process_id = os_process_id
        if os_user is not None:
            self.os_user = os_user

    @property
    def cmd_line(self):
        """Gets the cmd_line of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The cmd_line of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmd_line

    @cmd_line.setter
    def cmd_line(self, cmd_line):
        """Sets the cmd_line of this OSProcForGetRiskOutput.


        :param cmd_line: The cmd_line of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cmd_line = cmd_line

    @property
    def create_time_nano(self):
        """Gets the create_time_nano of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The create_time_nano of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time_nano

    @create_time_nano.setter
    def create_time_nano(self, create_time_nano):
        """Sets the create_time_nano of this OSProcForGetRiskOutput.


        :param create_time_nano: The create_time_nano of this OSProcForGetRiskOutput.  # noqa: E501
        :type: int
        """

        self._create_time_nano = create_time_nano

    @property
    def file(self):
        """Gets the file of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The file of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: FileForGetRiskOutput
        """
        return self._file

    @file.setter
    def file(self, file):
        """Sets the file of this OSProcForGetRiskOutput.


        :param file: The file of this OSProcForGetRiskOutput.  # noqa: E501
        :type: FileForGetRiskOutput
        """

        self._file = file

    @property
    def os_parent_process_id(self):
        """Gets the os_parent_process_id of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_parent_process_id of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_parent_process_id

    @os_parent_process_id.setter
    def os_parent_process_id(self, os_parent_process_id):
        """Sets the os_parent_process_id of this OSProcForGetRiskOutput.


        :param os_parent_process_id: The os_parent_process_id of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._os_parent_process_id = os_parent_process_id

    @property
    def os_process_cred(self):
        """Gets the os_process_cred of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_process_cred of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_process_cred

    @os_process_cred.setter
    def os_process_cred(self, os_process_cred):
        """Sets the os_process_cred of this OSProcForGetRiskOutput.


        :param os_process_cred: The os_process_cred of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._os_process_cred = os_process_cred

    @property
    def os_process_exec_dir(self):
        """Gets the os_process_exec_dir of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_process_exec_dir of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_process_exec_dir

    @os_process_exec_dir.setter
    def os_process_exec_dir(self, os_process_exec_dir):
        """Sets the os_process_exec_dir of this OSProcForGetRiskOutput.


        :param os_process_exec_dir: The os_process_exec_dir of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._os_process_exec_dir = os_process_exec_dir

    @property
    def os_process_group_id(self):
        """Gets the os_process_group_id of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_process_group_id of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_process_group_id

    @os_process_group_id.setter
    def os_process_group_id(self, os_process_group_id):
        """Sets the os_process_group_id of this OSProcForGetRiskOutput.


        :param os_process_group_id: The os_process_group_id of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._os_process_group_id = os_process_group_id

    @property
    def os_process_id(self):
        """Gets the os_process_id of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_process_id of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_process_id

    @os_process_id.setter
    def os_process_id(self, os_process_id):
        """Sets the os_process_id of this OSProcForGetRiskOutput.


        :param os_process_id: The os_process_id of this OSProcForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._os_process_id = os_process_id

    @property
    def os_user(self):
        """Gets the os_user of this OSProcForGetRiskOutput.  # noqa: E501


        :return: The os_user of this OSProcForGetRiskOutput.  # noqa: E501
        :rtype: OSUserForGetRiskOutput
        """
        return self._os_user

    @os_user.setter
    def os_user(self, os_user):
        """Sets the os_user of this OSProcForGetRiskOutput.


        :param os_user: The os_user of this OSProcForGetRiskOutput.  # noqa: E501
        :type: OSUserForGetRiskOutput
        """

        self._os_user = os_user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OSProcForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OSProcForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OSProcForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
