# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetSecurityOverviewResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'baseline_risk': 'BaselineRiskForGetSecurityOverviewOutput',
        'container_intrusion': 'ContainerIntrusionForGetSecurityOverviewOutput',
        'host_intrusion': 'HostIntrusionForGetSecurityOverviewOutput',
        'rasp_intrusion': 'RaspIntrusionForGetSecurityOverviewOutput',
        'virus_file': 'VirusFileForGetSecurityOverviewOutput',
        'vuln_risk': 'VulnRiskForGetSecurityOverviewOutput'
    }

    attribute_map = {
        'baseline_risk': 'BaselineRisk',
        'container_intrusion': 'ContainerIntrusion',
        'host_intrusion': 'HostIntrusion',
        'rasp_intrusion': 'RaspIntrusion',
        'virus_file': 'VirusFile',
        'vuln_risk': 'VulnRisk'
    }

    def __init__(self, baseline_risk=None, container_intrusion=None, host_intrusion=None, rasp_intrusion=None, virus_file=None, vuln_risk=None, _configuration=None):  # noqa: E501
        """GetSecurityOverviewResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._baseline_risk = None
        self._container_intrusion = None
        self._host_intrusion = None
        self._rasp_intrusion = None
        self._virus_file = None
        self._vuln_risk = None
        self.discriminator = None

        if baseline_risk is not None:
            self.baseline_risk = baseline_risk
        if container_intrusion is not None:
            self.container_intrusion = container_intrusion
        if host_intrusion is not None:
            self.host_intrusion = host_intrusion
        if rasp_intrusion is not None:
            self.rasp_intrusion = rasp_intrusion
        if virus_file is not None:
            self.virus_file = virus_file
        if vuln_risk is not None:
            self.vuln_risk = vuln_risk

    @property
    def baseline_risk(self):
        """Gets the baseline_risk of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The baseline_risk of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: BaselineRiskForGetSecurityOverviewOutput
        """
        return self._baseline_risk

    @baseline_risk.setter
    def baseline_risk(self, baseline_risk):
        """Sets the baseline_risk of this GetSecurityOverviewResponse.


        :param baseline_risk: The baseline_risk of this GetSecurityOverviewResponse.  # noqa: E501
        :type: BaselineRiskForGetSecurityOverviewOutput
        """

        self._baseline_risk = baseline_risk

    @property
    def container_intrusion(self):
        """Gets the container_intrusion of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The container_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: ContainerIntrusionForGetSecurityOverviewOutput
        """
        return self._container_intrusion

    @container_intrusion.setter
    def container_intrusion(self, container_intrusion):
        """Sets the container_intrusion of this GetSecurityOverviewResponse.


        :param container_intrusion: The container_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :type: ContainerIntrusionForGetSecurityOverviewOutput
        """

        self._container_intrusion = container_intrusion

    @property
    def host_intrusion(self):
        """Gets the host_intrusion of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The host_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: HostIntrusionForGetSecurityOverviewOutput
        """
        return self._host_intrusion

    @host_intrusion.setter
    def host_intrusion(self, host_intrusion):
        """Sets the host_intrusion of this GetSecurityOverviewResponse.


        :param host_intrusion: The host_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :type: HostIntrusionForGetSecurityOverviewOutput
        """

        self._host_intrusion = host_intrusion

    @property
    def rasp_intrusion(self):
        """Gets the rasp_intrusion of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The rasp_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: RaspIntrusionForGetSecurityOverviewOutput
        """
        return self._rasp_intrusion

    @rasp_intrusion.setter
    def rasp_intrusion(self, rasp_intrusion):
        """Sets the rasp_intrusion of this GetSecurityOverviewResponse.


        :param rasp_intrusion: The rasp_intrusion of this GetSecurityOverviewResponse.  # noqa: E501
        :type: RaspIntrusionForGetSecurityOverviewOutput
        """

        self._rasp_intrusion = rasp_intrusion

    @property
    def virus_file(self):
        """Gets the virus_file of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The virus_file of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: VirusFileForGetSecurityOverviewOutput
        """
        return self._virus_file

    @virus_file.setter
    def virus_file(self, virus_file):
        """Sets the virus_file of this GetSecurityOverviewResponse.


        :param virus_file: The virus_file of this GetSecurityOverviewResponse.  # noqa: E501
        :type: VirusFileForGetSecurityOverviewOutput
        """

        self._virus_file = virus_file

    @property
    def vuln_risk(self):
        """Gets the vuln_risk of this GetSecurityOverviewResponse.  # noqa: E501


        :return: The vuln_risk of this GetSecurityOverviewResponse.  # noqa: E501
        :rtype: VulnRiskForGetSecurityOverviewOutput
        """
        return self._vuln_risk

    @vuln_risk.setter
    def vuln_risk(self, vuln_risk):
        """Sets the vuln_risk of this GetSecurityOverviewResponse.


        :param vuln_risk: The vuln_risk of this GetSecurityOverviewResponse.  # noqa: E501
        :type: VulnRiskForGetSecurityOverviewOutput
        """

        self._vuln_risk = vuln_risk

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetSecurityOverviewResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetSecurityOverviewResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetSecurityOverviewResponse):
            return True

        return self.to_dict() != other.to_dict()
