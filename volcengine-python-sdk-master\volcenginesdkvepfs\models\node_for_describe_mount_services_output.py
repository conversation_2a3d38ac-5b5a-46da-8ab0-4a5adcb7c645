# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeForDescribeMountServicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_password': 'str',
        'node_id': 'str'
    }

    attribute_map = {
        'default_password': 'DefaultPassword',
        'node_id': 'NodeId'
    }

    def __init__(self, default_password=None, node_id=None, _configuration=None):  # noqa: E501
        """NodeForDescribeMountServicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_password = None
        self._node_id = None
        self.discriminator = None

        if default_password is not None:
            self.default_password = default_password
        if node_id is not None:
            self.node_id = node_id

    @property
    def default_password(self):
        """Gets the default_password of this NodeForDescribeMountServicesOutput.  # noqa: E501


        :return: The default_password of this NodeForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._default_password

    @default_password.setter
    def default_password(self, default_password):
        """Sets the default_password of this NodeForDescribeMountServicesOutput.


        :param default_password: The default_password of this NodeForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._default_password = default_password

    @property
    def node_id(self):
        """Gets the node_id of this NodeForDescribeMountServicesOutput.  # noqa: E501


        :return: The node_id of this NodeForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this NodeForDescribeMountServicesOutput.


        :param node_id: The node_id of this NodeForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeForDescribeMountServicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeForDescribeMountServicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeForDescribeMountServicesOutput):
            return True

        return self.to_dict() != other.to_dict()
