# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSubscriptionsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'str',
        'next_token': 'str',
        'subscription_ids': 'list[str]',
        'type': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'subscription_ids': 'SubscriptionIds',
        'type': 'Type'
    }

    def __init__(self, max_results=None, next_token=None, subscription_ids=None, type=None, _configuration=None):  # noqa: E501
        """DescribeSubscriptionsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._subscription_ids = None
        self._type = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if subscription_ids is not None:
            self.subscription_ids = subscription_ids
        if type is not None:
            self.type = type

    @property
    def max_results(self):
        """Gets the max_results of this DescribeSubscriptionsRequest.  # noqa: E501


        :return: The max_results of this DescribeSubscriptionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeSubscriptionsRequest.


        :param max_results: The max_results of this DescribeSubscriptionsRequest.  # noqa: E501
        :type: str
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeSubscriptionsRequest.  # noqa: E501


        :return: The next_token of this DescribeSubscriptionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeSubscriptionsRequest.


        :param next_token: The next_token of this DescribeSubscriptionsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def subscription_ids(self):
        """Gets the subscription_ids of this DescribeSubscriptionsRequest.  # noqa: E501


        :return: The subscription_ids of this DescribeSubscriptionsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._subscription_ids

    @subscription_ids.setter
    def subscription_ids(self, subscription_ids):
        """Sets the subscription_ids of this DescribeSubscriptionsRequest.


        :param subscription_ids: The subscription_ids of this DescribeSubscriptionsRequest.  # noqa: E501
        :type: list[str]
        """

        self._subscription_ids = subscription_ids

    @property
    def type(self):
        """Gets the type of this DescribeSubscriptionsRequest.  # noqa: E501


        :return: The type of this DescribeSubscriptionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DescribeSubscriptionsRequest.


        :param type: The type of this DescribeSubscriptionsRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSubscriptionsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSubscriptionsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSubscriptionsRequest):
            return True

        return self.to_dict() != other.to_dict()
