# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListEndpointHandleMethodsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_list': 'list[str]',
        'not_support_reason': 'str',
        'proc_list': 'list[ProcListForListEndpointHandleMethodsOutput]',
        'support': 'bool'
    }

    attribute_map = {
        'file_list': 'FileList',
        'not_support_reason': 'NotSupportReason',
        'proc_list': 'ProcList',
        'support': 'Support'
    }

    def __init__(self, file_list=None, not_support_reason=None, proc_list=None, support=None, _configuration=None):  # noqa: E501
        """ListEndpointHandleMethodsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_list = None
        self._not_support_reason = None
        self._proc_list = None
        self._support = None
        self.discriminator = None

        if file_list is not None:
            self.file_list = file_list
        if not_support_reason is not None:
            self.not_support_reason = not_support_reason
        if proc_list is not None:
            self.proc_list = proc_list
        if support is not None:
            self.support = support

    @property
    def file_list(self):
        """Gets the file_list of this ListEndpointHandleMethodsResponse.  # noqa: E501


        :return: The file_list of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._file_list

    @file_list.setter
    def file_list(self, file_list):
        """Sets the file_list of this ListEndpointHandleMethodsResponse.


        :param file_list: The file_list of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :type: list[str]
        """

        self._file_list = file_list

    @property
    def not_support_reason(self):
        """Gets the not_support_reason of this ListEndpointHandleMethodsResponse.  # noqa: E501


        :return: The not_support_reason of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :rtype: str
        """
        return self._not_support_reason

    @not_support_reason.setter
    def not_support_reason(self, not_support_reason):
        """Sets the not_support_reason of this ListEndpointHandleMethodsResponse.


        :param not_support_reason: The not_support_reason of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :type: str
        """

        self._not_support_reason = not_support_reason

    @property
    def proc_list(self):
        """Gets the proc_list of this ListEndpointHandleMethodsResponse.  # noqa: E501


        :return: The proc_list of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :rtype: list[ProcListForListEndpointHandleMethodsOutput]
        """
        return self._proc_list

    @proc_list.setter
    def proc_list(self, proc_list):
        """Sets the proc_list of this ListEndpointHandleMethodsResponse.


        :param proc_list: The proc_list of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :type: list[ProcListForListEndpointHandleMethodsOutput]
        """

        self._proc_list = proc_list

    @property
    def support(self):
        """Gets the support of this ListEndpointHandleMethodsResponse.  # noqa: E501


        :return: The support of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :rtype: bool
        """
        return self._support

    @support.setter
    def support(self, support):
        """Sets the support of this ListEndpointHandleMethodsResponse.


        :param support: The support of this ListEndpointHandleMethodsResponse.  # noqa: E501
        :type: bool
        """

        self._support = support

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListEndpointHandleMethodsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListEndpointHandleMethodsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListEndpointHandleMethodsResponse):
            return True

        return self.to_dict() != other.to_dict()
