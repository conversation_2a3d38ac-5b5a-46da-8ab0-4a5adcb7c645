# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TargetForUpdateGrantsForUserInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'database': 'str',
        'table': 'str'
    }

    attribute_map = {
        'database': 'Database',
        'table': 'Table'
    }

    def __init__(self, database=None, table=None, _configuration=None):  # noqa: E501
        """TargetForUpdateGrantsForUserInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._database = None
        self._table = None
        self.discriminator = None

        if database is not None:
            self.database = database
        if table is not None:
            self.table = table

    @property
    def database(self):
        """Gets the database of this TargetForUpdateGrantsForUserInput.  # noqa: E501


        :return: The database of this TargetForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._database

    @database.setter
    def database(self, database):
        """Sets the database of this TargetForUpdateGrantsForUserInput.


        :param database: The database of this TargetForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._database = database

    @property
    def table(self):
        """Gets the table of this TargetForUpdateGrantsForUserInput.  # noqa: E501


        :return: The table of this TargetForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._table

    @table.setter
    def table(self, table):
        """Sets the table of this TargetForUpdateGrantsForUserInput.


        :param table: The table of this TargetForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._table = table

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TargetForUpdateGrantsForUserInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TargetForUpdateGrantsForUserInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TargetForUpdateGrantsForUserInput):
            return True

        return self.to_dict() != other.to_dict()
