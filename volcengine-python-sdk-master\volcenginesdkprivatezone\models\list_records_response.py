# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRecordsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'records': 'list[RecordForListRecordsOutput]',
        'total': 'int'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'records': 'Records',
        'total': 'Total'
    }

    def __init__(self, page_number=None, page_size=None, records=None, total=None, _configuration=None):  # noqa: E501
        """ListRecordsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._records = None
        self._total = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if records is not None:
            self.records = records
        if total is not None:
            self.total = total

    @property
    def page_number(self):
        """Gets the page_number of this ListRecordsResponse.  # noqa: E501


        :return: The page_number of this ListRecordsResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRecordsResponse.


        :param page_number: The page_number of this ListRecordsResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRecordsResponse.  # noqa: E501


        :return: The page_size of this ListRecordsResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRecordsResponse.


        :param page_size: The page_size of this ListRecordsResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def records(self):
        """Gets the records of this ListRecordsResponse.  # noqa: E501


        :return: The records of this ListRecordsResponse.  # noqa: E501
        :rtype: list[RecordForListRecordsOutput]
        """
        return self._records

    @records.setter
    def records(self, records):
        """Sets the records of this ListRecordsResponse.


        :param records: The records of this ListRecordsResponse.  # noqa: E501
        :type: list[RecordForListRecordsOutput]
        """

        self._records = records

    @property
    def total(self):
        """Gets the total of this ListRecordsResponse.  # noqa: E501


        :return: The total of this ListRecordsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListRecordsResponse.


        :param total: The total of this ListRecordsResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRecordsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRecordsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRecordsResponse):
            return True

        return self.to_dict() != other.to_dict()
