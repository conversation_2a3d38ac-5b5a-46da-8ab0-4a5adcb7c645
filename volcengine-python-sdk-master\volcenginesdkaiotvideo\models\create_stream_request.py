# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateStreamRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'destination': 'str',
        'pull_source': 'str',
        'space_id': 'str',
        'stream_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'destination': 'Destination',
        'pull_source': 'PullSource',
        'space_id': 'SpaceID',
        'stream_name': 'StreamName'
    }

    def __init__(self, description=None, destination=None, pull_source=None, space_id=None, stream_name=None, _configuration=None):  # noqa: E501
        """CreateStreamRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._destination = None
        self._pull_source = None
        self._space_id = None
        self._stream_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if destination is not None:
            self.destination = destination
        if pull_source is not None:
            self.pull_source = pull_source
        self.space_id = space_id
        self.stream_name = stream_name

    @property
    def description(self):
        """Gets the description of this CreateStreamRequest.  # noqa: E501


        :return: The description of this CreateStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateStreamRequest.


        :param description: The description of this CreateStreamRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination(self):
        """Gets the destination of this CreateStreamRequest.  # noqa: E501


        :return: The destination of this CreateStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination

    @destination.setter
    def destination(self, destination):
        """Sets the destination of this CreateStreamRequest.


        :param destination: The destination of this CreateStreamRequest.  # noqa: E501
        :type: str
        """

        self._destination = destination

    @property
    def pull_source(self):
        """Gets the pull_source of this CreateStreamRequest.  # noqa: E501


        :return: The pull_source of this CreateStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._pull_source

    @pull_source.setter
    def pull_source(self, pull_source):
        """Sets the pull_source of this CreateStreamRequest.


        :param pull_source: The pull_source of this CreateStreamRequest.  # noqa: E501
        :type: str
        """

        self._pull_source = pull_source

    @property
    def space_id(self):
        """Gets the space_id of this CreateStreamRequest.  # noqa: E501


        :return: The space_id of this CreateStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this CreateStreamRequest.


        :param space_id: The space_id of this CreateStreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_id is None:
            raise ValueError("Invalid value for `space_id`, must not be `None`")  # noqa: E501

        self._space_id = space_id

    @property
    def stream_name(self):
        """Gets the stream_name of this CreateStreamRequest.  # noqa: E501


        :return: The stream_name of this CreateStreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_name

    @stream_name.setter
    def stream_name(self, stream_name):
        """Sets the stream_name of this CreateStreamRequest.


        :param stream_name: The stream_name of this CreateStreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_name is None:
            raise ValueError("Invalid value for `stream_name`, must not be `None`")  # noqa: E501

        self._stream_name = stream_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateStreamRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateStreamRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateStreamRequest):
            return True

        return self.to_dict() != other.to_dict()
