# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FailoverQueryInfoForDescribeFailoverLogsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'change_time': 'str',
        'details': 'str',
        'ha_change_type': 'str',
        'new_master': 'str',
        'old_master': 'str',
        'reason': 'str'
    }

    attribute_map = {
        'change_time': 'ChangeTime',
        'details': 'Details',
        'ha_change_type': 'HAChangeType',
        'new_master': 'NewMaster',
        'old_master': 'OldMaster',
        'reason': 'Reason'
    }

    def __init__(self, change_time=None, details=None, ha_change_type=None, new_master=None, old_master=None, reason=None, _configuration=None):  # noqa: E501
        """FailoverQueryInfoForDescribeFailoverLogsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._change_time = None
        self._details = None
        self._ha_change_type = None
        self._new_master = None
        self._old_master = None
        self._reason = None
        self.discriminator = None

        if change_time is not None:
            self.change_time = change_time
        if details is not None:
            self.details = details
        if ha_change_type is not None:
            self.ha_change_type = ha_change_type
        if new_master is not None:
            self.new_master = new_master
        if old_master is not None:
            self.old_master = old_master
        if reason is not None:
            self.reason = reason

    @property
    def change_time(self):
        """Gets the change_time of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The change_time of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._change_time

    @change_time.setter
    def change_time(self, change_time):
        """Sets the change_time of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param change_time: The change_time of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._change_time = change_time

    @property
    def details(self):
        """Gets the details of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The details of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._details

    @details.setter
    def details(self, details):
        """Sets the details of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param details: The details of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._details = details

    @property
    def ha_change_type(self):
        """Gets the ha_change_type of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The ha_change_type of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ha_change_type

    @ha_change_type.setter
    def ha_change_type(self, ha_change_type):
        """Sets the ha_change_type of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param ha_change_type: The ha_change_type of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._ha_change_type = ha_change_type

    @property
    def new_master(self):
        """Gets the new_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The new_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._new_master

    @new_master.setter
    def new_master(self, new_master):
        """Sets the new_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param new_master: The new_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._new_master = new_master

    @property
    def old_master(self):
        """Gets the old_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The old_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_master

    @old_master.setter
    def old_master(self, old_master):
        """Sets the old_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param old_master: The old_master of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._old_master = old_master

    @property
    def reason(self):
        """Gets the reason of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501


        :return: The reason of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this FailoverQueryInfoForDescribeFailoverLogsOutput.


        :param reason: The reason of this FailoverQueryInfoForDescribeFailoverLogsOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FailoverQueryInfoForDescribeFailoverLogsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FailoverQueryInfoForDescribeFailoverLogsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FailoverQueryInfoForDescribeFailoverLogsOutput):
            return True

        return self.to_dict() != other.to_dict()
