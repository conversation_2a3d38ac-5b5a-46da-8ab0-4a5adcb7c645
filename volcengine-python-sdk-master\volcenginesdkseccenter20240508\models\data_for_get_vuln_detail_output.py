# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetVulnDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affect_packs': 'list[AffectPackForGetVulnDetailOutput]',
        'cnnvd_id': 'str',
        'cve_id': 'str',
        'cvss_metric': 'list[CvssMetricForGetVulnDetailOutput]',
        'cvss_score': 'str',
        'cwe_id': 'list[str]',
        'descrip': 'str',
        'publish_date': 'int',
        'refer_link': 'list[str]',
        'remediation': 'str',
        'severity': 'str',
        'title': 'str',
        'vuln_id': 'str',
        'vuln_tag': 'list[str]',
        'vuln_tag_text': 'list[str]',
        'vuln_type': 'str'
    }

    attribute_map = {
        'affect_packs': 'AffectPacks',
        'cnnvd_id': 'CnnvdID',
        'cve_id': 'CveID',
        'cvss_metric': 'CvssMetric',
        'cvss_score': 'CvssScore',
        'cwe_id': 'CweID',
        'descrip': 'Descrip',
        'publish_date': 'PublishDate',
        'refer_link': 'ReferLink',
        'remediation': 'Remediation',
        'severity': 'Severity',
        'title': 'Title',
        'vuln_id': 'VulnID',
        'vuln_tag': 'VulnTag',
        'vuln_tag_text': 'VulnTagText',
        'vuln_type': 'VulnType'
    }

    def __init__(self, affect_packs=None, cnnvd_id=None, cve_id=None, cvss_metric=None, cvss_score=None, cwe_id=None, descrip=None, publish_date=None, refer_link=None, remediation=None, severity=None, title=None, vuln_id=None, vuln_tag=None, vuln_tag_text=None, vuln_type=None, _configuration=None):  # noqa: E501
        """DataForGetVulnDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affect_packs = None
        self._cnnvd_id = None
        self._cve_id = None
        self._cvss_metric = None
        self._cvss_score = None
        self._cwe_id = None
        self._descrip = None
        self._publish_date = None
        self._refer_link = None
        self._remediation = None
        self._severity = None
        self._title = None
        self._vuln_id = None
        self._vuln_tag = None
        self._vuln_tag_text = None
        self._vuln_type = None
        self.discriminator = None

        if affect_packs is not None:
            self.affect_packs = affect_packs
        if cnnvd_id is not None:
            self.cnnvd_id = cnnvd_id
        if cve_id is not None:
            self.cve_id = cve_id
        if cvss_metric is not None:
            self.cvss_metric = cvss_metric
        if cvss_score is not None:
            self.cvss_score = cvss_score
        if cwe_id is not None:
            self.cwe_id = cwe_id
        if descrip is not None:
            self.descrip = descrip
        if publish_date is not None:
            self.publish_date = publish_date
        if refer_link is not None:
            self.refer_link = refer_link
        if remediation is not None:
            self.remediation = remediation
        if severity is not None:
            self.severity = severity
        if title is not None:
            self.title = title
        if vuln_id is not None:
            self.vuln_id = vuln_id
        if vuln_tag is not None:
            self.vuln_tag = vuln_tag
        if vuln_tag_text is not None:
            self.vuln_tag_text = vuln_tag_text
        if vuln_type is not None:
            self.vuln_type = vuln_type

    @property
    def affect_packs(self):
        """Gets the affect_packs of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The affect_packs of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[AffectPackForGetVulnDetailOutput]
        """
        return self._affect_packs

    @affect_packs.setter
    def affect_packs(self, affect_packs):
        """Sets the affect_packs of this DataForGetVulnDetailOutput.


        :param affect_packs: The affect_packs of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[AffectPackForGetVulnDetailOutput]
        """

        self._affect_packs = affect_packs

    @property
    def cnnvd_id(self):
        """Gets the cnnvd_id of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The cnnvd_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._cnnvd_id

    @cnnvd_id.setter
    def cnnvd_id(self, cnnvd_id):
        """Sets the cnnvd_id of this DataForGetVulnDetailOutput.


        :param cnnvd_id: The cnnvd_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._cnnvd_id = cnnvd_id

    @property
    def cve_id(self):
        """Gets the cve_id of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The cve_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this DataForGetVulnDetailOutput.


        :param cve_id: The cve_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def cvss_metric(self):
        """Gets the cvss_metric of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The cvss_metric of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[CvssMetricForGetVulnDetailOutput]
        """
        return self._cvss_metric

    @cvss_metric.setter
    def cvss_metric(self, cvss_metric):
        """Sets the cvss_metric of this DataForGetVulnDetailOutput.


        :param cvss_metric: The cvss_metric of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[CvssMetricForGetVulnDetailOutput]
        """

        self._cvss_metric = cvss_metric

    @property
    def cvss_score(self):
        """Gets the cvss_score of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The cvss_score of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._cvss_score

    @cvss_score.setter
    def cvss_score(self, cvss_score):
        """Sets the cvss_score of this DataForGetVulnDetailOutput.


        :param cvss_score: The cvss_score of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._cvss_score = cvss_score

    @property
    def cwe_id(self):
        """Gets the cwe_id of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The cwe_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cwe_id

    @cwe_id.setter
    def cwe_id(self, cwe_id):
        """Sets the cwe_id of this DataForGetVulnDetailOutput.


        :param cwe_id: The cwe_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._cwe_id = cwe_id

    @property
    def descrip(self):
        """Gets the descrip of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The descrip of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._descrip

    @descrip.setter
    def descrip(self, descrip):
        """Sets the descrip of this DataForGetVulnDetailOutput.


        :param descrip: The descrip of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._descrip = descrip

    @property
    def publish_date(self):
        """Gets the publish_date of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The publish_date of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._publish_date

    @publish_date.setter
    def publish_date(self, publish_date):
        """Sets the publish_date of this DataForGetVulnDetailOutput.


        :param publish_date: The publish_date of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: int
        """

        self._publish_date = publish_date

    @property
    def refer_link(self):
        """Gets the refer_link of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The refer_link of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._refer_link

    @refer_link.setter
    def refer_link(self, refer_link):
        """Sets the refer_link of this DataForGetVulnDetailOutput.


        :param refer_link: The refer_link of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._refer_link = refer_link

    @property
    def remediation(self):
        """Gets the remediation of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The remediation of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._remediation

    @remediation.setter
    def remediation(self, remediation):
        """Sets the remediation of this DataForGetVulnDetailOutput.


        :param remediation: The remediation of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._remediation = remediation

    @property
    def severity(self):
        """Gets the severity of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The severity of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._severity

    @severity.setter
    def severity(self, severity):
        """Sets the severity of this DataForGetVulnDetailOutput.


        :param severity: The severity of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._severity = severity

    @property
    def title(self):
        """Gets the title of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The title of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this DataForGetVulnDetailOutput.


        :param title: The title of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def vuln_id(self):
        """Gets the vuln_id of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The vuln_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_id

    @vuln_id.setter
    def vuln_id(self, vuln_id):
        """Sets the vuln_id of this DataForGetVulnDetailOutput.


        :param vuln_id: The vuln_id of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._vuln_id = vuln_id

    @property
    def vuln_tag(self):
        """Gets the vuln_tag of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The vuln_tag of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_tag

    @vuln_tag.setter
    def vuln_tag(self, vuln_tag):
        """Sets the vuln_tag of this DataForGetVulnDetailOutput.


        :param vuln_tag: The vuln_tag of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._vuln_tag = vuln_tag

    @property
    def vuln_tag_text(self):
        """Gets the vuln_tag_text of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The vuln_tag_text of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_tag_text

    @vuln_tag_text.setter
    def vuln_tag_text(self, vuln_tag_text):
        """Sets the vuln_tag_text of this DataForGetVulnDetailOutput.


        :param vuln_tag_text: The vuln_tag_text of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._vuln_tag_text = vuln_tag_text

    @property
    def vuln_type(self):
        """Gets the vuln_type of this DataForGetVulnDetailOutput.  # noqa: E501


        :return: The vuln_type of this DataForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_type

    @vuln_type.setter
    def vuln_type(self, vuln_type):
        """Sets the vuln_type of this DataForGetVulnDetailOutput.


        :param vuln_type: The vuln_type of this DataForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._vuln_type = vuln_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetVulnDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetVulnDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetVulnDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
