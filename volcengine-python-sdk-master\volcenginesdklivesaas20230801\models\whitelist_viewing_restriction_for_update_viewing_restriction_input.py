# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WhitelistViewingRestrictionForUpdateViewingRestrictionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_trial': 'bool',
        'input_prompt': 'str',
        'trial_time': 'int',
        'welcome_title': 'str'
    }

    attribute_map = {
        'enable_trial': 'EnableTrial',
        'input_prompt': 'InputPrompt',
        'trial_time': 'TrialTime',
        'welcome_title': 'WelcomeTitle'
    }

    def __init__(self, enable_trial=None, input_prompt=None, trial_time=None, welcome_title=None, _configuration=None):  # noqa: E501
        """WhitelistViewingRestrictionForUpdateViewingRestrictionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_trial = None
        self._input_prompt = None
        self._trial_time = None
        self._welcome_title = None
        self.discriminator = None

        if enable_trial is not None:
            self.enable_trial = enable_trial
        if input_prompt is not None:
            self.input_prompt = input_prompt
        if trial_time is not None:
            self.trial_time = trial_time
        if welcome_title is not None:
            self.welcome_title = welcome_title

    @property
    def enable_trial(self):
        """Gets the enable_trial of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The enable_trial of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_trial

    @enable_trial.setter
    def enable_trial(self, enable_trial):
        """Sets the enable_trial of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.


        :param enable_trial: The enable_trial of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: bool
        """

        self._enable_trial = enable_trial

    @property
    def input_prompt(self):
        """Gets the input_prompt of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The input_prompt of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: str
        """
        return self._input_prompt

    @input_prompt.setter
    def input_prompt(self, input_prompt):
        """Sets the input_prompt of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.


        :param input_prompt: The input_prompt of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: str
        """

        self._input_prompt = input_prompt

    @property
    def trial_time(self):
        """Gets the trial_time of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The trial_time of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: int
        """
        return self._trial_time

    @trial_time.setter
    def trial_time(self, trial_time):
        """Sets the trial_time of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.


        :param trial_time: The trial_time of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: int
        """

        self._trial_time = trial_time

    @property
    def welcome_title(self):
        """Gets the welcome_title of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501


        :return: The welcome_title of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :rtype: str
        """
        return self._welcome_title

    @welcome_title.setter
    def welcome_title(self, welcome_title):
        """Sets the welcome_title of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.


        :param welcome_title: The welcome_title of this WhitelistViewingRestrictionForUpdateViewingRestrictionInput.  # noqa: E501
        :type: str
        """

        self._welcome_title = welcome_title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WhitelistViewingRestrictionForUpdateViewingRestrictionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WhitelistViewingRestrictionForUpdateViewingRestrictionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WhitelistViewingRestrictionForUpdateViewingRestrictionInput):
            return True

        return self.to_dict() != other.to_dict()
