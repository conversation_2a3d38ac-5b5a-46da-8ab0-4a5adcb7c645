# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DetailForMigratePodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'err_code': 'int',
        'err_msg': 'str',
        'pod_id': 'str',
        'success': 'bool'
    }

    attribute_map = {
        'err_code': 'ErrCode',
        'err_msg': 'ErrMsg',
        'pod_id': 'PodId',
        'success': 'Success'
    }

    def __init__(self, err_code=None, err_msg=None, pod_id=None, success=None, _configuration=None):  # noqa: E501
        """DetailForMigratePodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._err_code = None
        self._err_msg = None
        self._pod_id = None
        self._success = None
        self.discriminator = None

        if err_code is not None:
            self.err_code = err_code
        if err_msg is not None:
            self.err_msg = err_msg
        if pod_id is not None:
            self.pod_id = pod_id
        if success is not None:
            self.success = success

    @property
    def err_code(self):
        """Gets the err_code of this DetailForMigratePodOutput.  # noqa: E501


        :return: The err_code of this DetailForMigratePodOutput.  # noqa: E501
        :rtype: int
        """
        return self._err_code

    @err_code.setter
    def err_code(self, err_code):
        """Sets the err_code of this DetailForMigratePodOutput.


        :param err_code: The err_code of this DetailForMigratePodOutput.  # noqa: E501
        :type: int
        """

        self._err_code = err_code

    @property
    def err_msg(self):
        """Gets the err_msg of this DetailForMigratePodOutput.  # noqa: E501


        :return: The err_msg of this DetailForMigratePodOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_msg

    @err_msg.setter
    def err_msg(self, err_msg):
        """Sets the err_msg of this DetailForMigratePodOutput.


        :param err_msg: The err_msg of this DetailForMigratePodOutput.  # noqa: E501
        :type: str
        """

        self._err_msg = err_msg

    @property
    def pod_id(self):
        """Gets the pod_id of this DetailForMigratePodOutput.  # noqa: E501


        :return: The pod_id of this DetailForMigratePodOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_id

    @pod_id.setter
    def pod_id(self, pod_id):
        """Sets the pod_id of this DetailForMigratePodOutput.


        :param pod_id: The pod_id of this DetailForMigratePodOutput.  # noqa: E501
        :type: str
        """

        self._pod_id = pod_id

    @property
    def success(self):
        """Gets the success of this DetailForMigratePodOutput.  # noqa: E501


        :return: The success of this DetailForMigratePodOutput.  # noqa: E501
        :rtype: bool
        """
        return self._success

    @success.setter
    def success(self, success):
        """Sets the success of this DetailForMigratePodOutput.


        :param success: The success of this DetailForMigratePodOutput.  # noqa: E501
        :type: bool
        """

        self._success = success

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DetailForMigratePodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DetailForMigratePodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DetailForMigratePodOutput):
            return True

        return self.to_dict() != other.to_dict()
