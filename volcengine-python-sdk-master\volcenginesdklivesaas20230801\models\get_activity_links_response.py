# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityLinksResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audience_link': 'AudienceLinkForGetActivityLinksOutput',
        'live_client_link': 'LiveClientLinkForGetActivityLinksOutput',
        'teach_assistant_login_links': 'list[TeachAssistantLoginLinkForGetActivityLinksOutput]',
        'web_push_guest_login_link': 'WebPushGuestLoginLinkForGetActivityLinksOutput',
        'web_push_host_link': 'WebPushHostLinkForGetActivityLinksOutput'
    }

    attribute_map = {
        'audience_link': 'AudienceLink',
        'live_client_link': 'LiveClientLink',
        'teach_assistant_login_links': 'TeachAssistantLoginLinks',
        'web_push_guest_login_link': 'WebPushGuestLoginLink',
        'web_push_host_link': 'WebPushHostLink'
    }

    def __init__(self, audience_link=None, live_client_link=None, teach_assistant_login_links=None, web_push_guest_login_link=None, web_push_host_link=None, _configuration=None):  # noqa: E501
        """GetActivityLinksResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audience_link = None
        self._live_client_link = None
        self._teach_assistant_login_links = None
        self._web_push_guest_login_link = None
        self._web_push_host_link = None
        self.discriminator = None

        if audience_link is not None:
            self.audience_link = audience_link
        if live_client_link is not None:
            self.live_client_link = live_client_link
        if teach_assistant_login_links is not None:
            self.teach_assistant_login_links = teach_assistant_login_links
        if web_push_guest_login_link is not None:
            self.web_push_guest_login_link = web_push_guest_login_link
        if web_push_host_link is not None:
            self.web_push_host_link = web_push_host_link

    @property
    def audience_link(self):
        """Gets the audience_link of this GetActivityLinksResponse.  # noqa: E501


        :return: The audience_link of this GetActivityLinksResponse.  # noqa: E501
        :rtype: AudienceLinkForGetActivityLinksOutput
        """
        return self._audience_link

    @audience_link.setter
    def audience_link(self, audience_link):
        """Sets the audience_link of this GetActivityLinksResponse.


        :param audience_link: The audience_link of this GetActivityLinksResponse.  # noqa: E501
        :type: AudienceLinkForGetActivityLinksOutput
        """

        self._audience_link = audience_link

    @property
    def live_client_link(self):
        """Gets the live_client_link of this GetActivityLinksResponse.  # noqa: E501


        :return: The live_client_link of this GetActivityLinksResponse.  # noqa: E501
        :rtype: LiveClientLinkForGetActivityLinksOutput
        """
        return self._live_client_link

    @live_client_link.setter
    def live_client_link(self, live_client_link):
        """Sets the live_client_link of this GetActivityLinksResponse.


        :param live_client_link: The live_client_link of this GetActivityLinksResponse.  # noqa: E501
        :type: LiveClientLinkForGetActivityLinksOutput
        """

        self._live_client_link = live_client_link

    @property
    def teach_assistant_login_links(self):
        """Gets the teach_assistant_login_links of this GetActivityLinksResponse.  # noqa: E501


        :return: The teach_assistant_login_links of this GetActivityLinksResponse.  # noqa: E501
        :rtype: list[TeachAssistantLoginLinkForGetActivityLinksOutput]
        """
        return self._teach_assistant_login_links

    @teach_assistant_login_links.setter
    def teach_assistant_login_links(self, teach_assistant_login_links):
        """Sets the teach_assistant_login_links of this GetActivityLinksResponse.


        :param teach_assistant_login_links: The teach_assistant_login_links of this GetActivityLinksResponse.  # noqa: E501
        :type: list[TeachAssistantLoginLinkForGetActivityLinksOutput]
        """

        self._teach_assistant_login_links = teach_assistant_login_links

    @property
    def web_push_guest_login_link(self):
        """Gets the web_push_guest_login_link of this GetActivityLinksResponse.  # noqa: E501


        :return: The web_push_guest_login_link of this GetActivityLinksResponse.  # noqa: E501
        :rtype: WebPushGuestLoginLinkForGetActivityLinksOutput
        """
        return self._web_push_guest_login_link

    @web_push_guest_login_link.setter
    def web_push_guest_login_link(self, web_push_guest_login_link):
        """Sets the web_push_guest_login_link of this GetActivityLinksResponse.


        :param web_push_guest_login_link: The web_push_guest_login_link of this GetActivityLinksResponse.  # noqa: E501
        :type: WebPushGuestLoginLinkForGetActivityLinksOutput
        """

        self._web_push_guest_login_link = web_push_guest_login_link

    @property
    def web_push_host_link(self):
        """Gets the web_push_host_link of this GetActivityLinksResponse.  # noqa: E501


        :return: The web_push_host_link of this GetActivityLinksResponse.  # noqa: E501
        :rtype: WebPushHostLinkForGetActivityLinksOutput
        """
        return self._web_push_host_link

    @web_push_host_link.setter
    def web_push_host_link(self, web_push_host_link):
        """Sets the web_push_host_link of this GetActivityLinksResponse.


        :param web_push_host_link: The web_push_host_link of this GetActivityLinksResponse.  # noqa: E501
        :type: WebPushHostLinkForGetActivityLinksOutput
        """

        self._web_push_host_link = web_push_host_link

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityLinksResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityLinksResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityLinksResponse):
            return True

        return self.to_dict() != other.to_dict()
