# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_name': 'str',
        'endpoint_type': 'str',
        'instance_id': 'str',
        'nodes': 'str',
        'read_write_mode': 'str'
    }

    attribute_map = {
        'endpoint_name': 'EndpointName',
        'endpoint_type': 'EndpointType',
        'instance_id': 'InstanceId',
        'nodes': 'Nodes',
        'read_write_mode': 'ReadWriteMode'
    }

    def __init__(self, endpoint_name=None, endpoint_type=None, instance_id=None, nodes=None, read_write_mode=None, _configuration=None):  # noqa: E501
        """CreateDBEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_name = None
        self._endpoint_type = None
        self._instance_id = None
        self._nodes = None
        self._read_write_mode = None
        self.discriminator = None

        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        self.endpoint_type = endpoint_type
        self.instance_id = instance_id
        if nodes is not None:
            self.nodes = nodes
        if read_write_mode is not None:
            self.read_write_mode = read_write_mode

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this CreateDBEndpointRequest.  # noqa: E501


        :return: The endpoint_name of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this CreateDBEndpointRequest.


        :param endpoint_name: The endpoint_name of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this CreateDBEndpointRequest.  # noqa: E501


        :return: The endpoint_type of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this CreateDBEndpointRequest.


        :param endpoint_type: The endpoint_type of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and endpoint_type is None:
            raise ValueError("Invalid value for `endpoint_type`, must not be `None`")  # noqa: E501

        self._endpoint_type = endpoint_type

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDBEndpointRequest.  # noqa: E501


        :return: The instance_id of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDBEndpointRequest.


        :param instance_id: The instance_id of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def nodes(self):
        """Gets the nodes of this CreateDBEndpointRequest.  # noqa: E501


        :return: The nodes of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this CreateDBEndpointRequest.


        :param nodes: The nodes of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """

        self._nodes = nodes

    @property
    def read_write_mode(self):
        """Gets the read_write_mode of this CreateDBEndpointRequest.  # noqa: E501


        :return: The read_write_mode of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._read_write_mode

    @read_write_mode.setter
    def read_write_mode(self, read_write_mode):
        """Sets the read_write_mode of this CreateDBEndpointRequest.


        :param read_write_mode: The read_write_mode of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """

        self._read_write_mode = read_write_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
