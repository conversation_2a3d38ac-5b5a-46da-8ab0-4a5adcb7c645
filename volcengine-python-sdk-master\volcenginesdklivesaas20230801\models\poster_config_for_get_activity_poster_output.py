# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PosterConfigForGetActivityPosterOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_address': 'str',
        'activity_desc': 'str',
        'activity_id': 'int',
        'can_invite': 'int',
        'can_share': 'int',
        'cover_image': 'str',
        'custom_address': 'str',
        'custom_desc': 'str',
        'custom_image': 'str',
        'custom_title': 'str',
        'enable_mini_app': 'bool',
        'invite_content': 'str',
        'invite_list_enable': 'int',
        'invite_show_people': 'int',
        'is_force_login': 'int',
        'live_start_time': 'int',
        'live_type': 'int',
        'mini_app_id': 'str',
        'mini_app_redirect_url': 'str',
        'sponsor': 'str',
        'title': 'str'
    }

    attribute_map = {
        'activity_address': 'ActivityAddress',
        'activity_desc': 'ActivityDesc',
        'activity_id': 'ActivityId',
        'can_invite': 'CanInvite',
        'can_share': 'CanShare',
        'cover_image': 'CoverImage',
        'custom_address': 'CustomAddress',
        'custom_desc': 'CustomDesc',
        'custom_image': 'CustomImage',
        'custom_title': 'CustomTitle',
        'enable_mini_app': 'EnableMiniApp',
        'invite_content': 'InviteContent',
        'invite_list_enable': 'InviteListEnable',
        'invite_show_people': 'InviteShowPeople',
        'is_force_login': 'IsForceLogin',
        'live_start_time': 'LiveStartTime',
        'live_type': 'LiveType',
        'mini_app_id': 'MiniAppId',
        'mini_app_redirect_url': 'MiniAppRedirectUrl',
        'sponsor': 'Sponsor',
        'title': 'Title'
    }

    def __init__(self, activity_address=None, activity_desc=None, activity_id=None, can_invite=None, can_share=None, cover_image=None, custom_address=None, custom_desc=None, custom_image=None, custom_title=None, enable_mini_app=None, invite_content=None, invite_list_enable=None, invite_show_people=None, is_force_login=None, live_start_time=None, live_type=None, mini_app_id=None, mini_app_redirect_url=None, sponsor=None, title=None, _configuration=None):  # noqa: E501
        """PosterConfigForGetActivityPosterOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_address = None
        self._activity_desc = None
        self._activity_id = None
        self._can_invite = None
        self._can_share = None
        self._cover_image = None
        self._custom_address = None
        self._custom_desc = None
        self._custom_image = None
        self._custom_title = None
        self._enable_mini_app = None
        self._invite_content = None
        self._invite_list_enable = None
        self._invite_show_people = None
        self._is_force_login = None
        self._live_start_time = None
        self._live_type = None
        self._mini_app_id = None
        self._mini_app_redirect_url = None
        self._sponsor = None
        self._title = None
        self.discriminator = None

        if activity_address is not None:
            self.activity_address = activity_address
        if activity_desc is not None:
            self.activity_desc = activity_desc
        if activity_id is not None:
            self.activity_id = activity_id
        if can_invite is not None:
            self.can_invite = can_invite
        if can_share is not None:
            self.can_share = can_share
        if cover_image is not None:
            self.cover_image = cover_image
        if custom_address is not None:
            self.custom_address = custom_address
        if custom_desc is not None:
            self.custom_desc = custom_desc
        if custom_image is not None:
            self.custom_image = custom_image
        if custom_title is not None:
            self.custom_title = custom_title
        if enable_mini_app is not None:
            self.enable_mini_app = enable_mini_app
        if invite_content is not None:
            self.invite_content = invite_content
        if invite_list_enable is not None:
            self.invite_list_enable = invite_list_enable
        if invite_show_people is not None:
            self.invite_show_people = invite_show_people
        if is_force_login is not None:
            self.is_force_login = is_force_login
        if live_start_time is not None:
            self.live_start_time = live_start_time
        if live_type is not None:
            self.live_type = live_type
        if mini_app_id is not None:
            self.mini_app_id = mini_app_id
        if mini_app_redirect_url is not None:
            self.mini_app_redirect_url = mini_app_redirect_url
        if sponsor is not None:
            self.sponsor = sponsor
        if title is not None:
            self.title = title

    @property
    def activity_address(self):
        """Gets the activity_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The activity_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._activity_address

    @activity_address.setter
    def activity_address(self, activity_address):
        """Sets the activity_address of this PosterConfigForGetActivityPosterOutput.


        :param activity_address: The activity_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._activity_address = activity_address

    @property
    def activity_desc(self):
        """Gets the activity_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The activity_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._activity_desc

    @activity_desc.setter
    def activity_desc(self, activity_desc):
        """Sets the activity_desc of this PosterConfigForGetActivityPosterOutput.


        :param activity_desc: The activity_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._activity_desc = activity_desc

    @property
    def activity_id(self):
        """Gets the activity_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The activity_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this PosterConfigForGetActivityPosterOutput.


        :param activity_id: The activity_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def can_invite(self):
        """Gets the can_invite of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The can_invite of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._can_invite

    @can_invite.setter
    def can_invite(self, can_invite):
        """Sets the can_invite of this PosterConfigForGetActivityPosterOutput.


        :param can_invite: The can_invite of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._can_invite = can_invite

    @property
    def can_share(self):
        """Gets the can_share of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The can_share of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._can_share

    @can_share.setter
    def can_share(self, can_share):
        """Sets the can_share of this PosterConfigForGetActivityPosterOutput.


        :param can_share: The can_share of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._can_share = can_share

    @property
    def cover_image(self):
        """Gets the cover_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The cover_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this PosterConfigForGetActivityPosterOutput.


        :param cover_image: The cover_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def custom_address(self):
        """Gets the custom_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The custom_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_address

    @custom_address.setter
    def custom_address(self, custom_address):
        """Sets the custom_address of this PosterConfigForGetActivityPosterOutput.


        :param custom_address: The custom_address of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._custom_address = custom_address

    @property
    def custom_desc(self):
        """Gets the custom_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The custom_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_desc

    @custom_desc.setter
    def custom_desc(self, custom_desc):
        """Sets the custom_desc of this PosterConfigForGetActivityPosterOutput.


        :param custom_desc: The custom_desc of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._custom_desc = custom_desc

    @property
    def custom_image(self):
        """Gets the custom_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The custom_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_image

    @custom_image.setter
    def custom_image(self, custom_image):
        """Sets the custom_image of this PosterConfigForGetActivityPosterOutput.


        :param custom_image: The custom_image of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._custom_image = custom_image

    @property
    def custom_title(self):
        """Gets the custom_title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The custom_title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_title

    @custom_title.setter
    def custom_title(self, custom_title):
        """Sets the custom_title of this PosterConfigForGetActivityPosterOutput.


        :param custom_title: The custom_title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._custom_title = custom_title

    @property
    def enable_mini_app(self):
        """Gets the enable_mini_app of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The enable_mini_app of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_mini_app

    @enable_mini_app.setter
    def enable_mini_app(self, enable_mini_app):
        """Sets the enable_mini_app of this PosterConfigForGetActivityPosterOutput.


        :param enable_mini_app: The enable_mini_app of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: bool
        """

        self._enable_mini_app = enable_mini_app

    @property
    def invite_content(self):
        """Gets the invite_content of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The invite_content of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._invite_content

    @invite_content.setter
    def invite_content(self, invite_content):
        """Sets the invite_content of this PosterConfigForGetActivityPosterOutput.


        :param invite_content: The invite_content of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._invite_content = invite_content

    @property
    def invite_list_enable(self):
        """Gets the invite_list_enable of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The invite_list_enable of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._invite_list_enable

    @invite_list_enable.setter
    def invite_list_enable(self, invite_list_enable):
        """Sets the invite_list_enable of this PosterConfigForGetActivityPosterOutput.


        :param invite_list_enable: The invite_list_enable of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._invite_list_enable = invite_list_enable

    @property
    def invite_show_people(self):
        """Gets the invite_show_people of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The invite_show_people of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._invite_show_people

    @invite_show_people.setter
    def invite_show_people(self, invite_show_people):
        """Sets the invite_show_people of this PosterConfigForGetActivityPosterOutput.


        :param invite_show_people: The invite_show_people of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._invite_show_people = invite_show_people

    @property
    def is_force_login(self):
        """Gets the is_force_login of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The is_force_login of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_force_login

    @is_force_login.setter
    def is_force_login(self, is_force_login):
        """Sets the is_force_login of this PosterConfigForGetActivityPosterOutput.


        :param is_force_login: The is_force_login of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._is_force_login = is_force_login

    @property
    def live_start_time(self):
        """Gets the live_start_time of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The live_start_time of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_start_time

    @live_start_time.setter
    def live_start_time(self, live_start_time):
        """Sets the live_start_time of this PosterConfigForGetActivityPosterOutput.


        :param live_start_time: The live_start_time of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._live_start_time = live_start_time

    @property
    def live_type(self):
        """Gets the live_type of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The live_type of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_type

    @live_type.setter
    def live_type(self, live_type):
        """Sets the live_type of this PosterConfigForGetActivityPosterOutput.


        :param live_type: The live_type of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: int
        """

        self._live_type = live_type

    @property
    def mini_app_id(self):
        """Gets the mini_app_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The mini_app_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_id

    @mini_app_id.setter
    def mini_app_id(self, mini_app_id):
        """Sets the mini_app_id of this PosterConfigForGetActivityPosterOutput.


        :param mini_app_id: The mini_app_id of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._mini_app_id = mini_app_id

    @property
    def mini_app_redirect_url(self):
        """Gets the mini_app_redirect_url of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The mini_app_redirect_url of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_redirect_url

    @mini_app_redirect_url.setter
    def mini_app_redirect_url(self, mini_app_redirect_url):
        """Sets the mini_app_redirect_url of this PosterConfigForGetActivityPosterOutput.


        :param mini_app_redirect_url: The mini_app_redirect_url of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._mini_app_redirect_url = mini_app_redirect_url

    @property
    def sponsor(self):
        """Gets the sponsor of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The sponsor of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._sponsor

    @sponsor.setter
    def sponsor(self, sponsor):
        """Sets the sponsor of this PosterConfigForGetActivityPosterOutput.


        :param sponsor: The sponsor of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._sponsor = sponsor

    @property
    def title(self):
        """Gets the title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501


        :return: The title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this PosterConfigForGetActivityPosterOutput.


        :param title: The title of this PosterConfigForGetActivityPosterOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PosterConfigForGetActivityPosterOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PosterConfigForGetActivityPosterOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PosterConfigForGetActivityPosterOutput):
            return True

        return self.to_dict() != other.to_dict()
