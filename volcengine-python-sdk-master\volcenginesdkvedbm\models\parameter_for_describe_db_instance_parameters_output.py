# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ParameterForDescribeDBInstanceParametersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'checking_code': 'str',
        'data_type': 'str',
        'modifiable': 'bool',
        'need_restart': 'bool',
        'parameter_default_value': 'str',
        'parameter_description': 'str',
        'parameter_display_value': 'str',
        'parameter_name': 'str',
        'parameter_value': 'str',
        'status': 'str'
    }

    attribute_map = {
        'checking_code': 'CheckingCode',
        'data_type': 'DataType',
        'modifiable': 'Modifiable',
        'need_restart': 'NeedRestart',
        'parameter_default_value': 'ParameterDefaultValue',
        'parameter_description': 'ParameterDescription',
        'parameter_display_value': 'ParameterDisplayValue',
        'parameter_name': 'ParameterName',
        'parameter_value': 'ParameterValue',
        'status': 'Status'
    }

    def __init__(self, checking_code=None, data_type=None, modifiable=None, need_restart=None, parameter_default_value=None, parameter_description=None, parameter_display_value=None, parameter_name=None, parameter_value=None, status=None, _configuration=None):  # noqa: E501
        """ParameterForDescribeDBInstanceParametersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._checking_code = None
        self._data_type = None
        self._modifiable = None
        self._need_restart = None
        self._parameter_default_value = None
        self._parameter_description = None
        self._parameter_display_value = None
        self._parameter_name = None
        self._parameter_value = None
        self._status = None
        self.discriminator = None

        if checking_code is not None:
            self.checking_code = checking_code
        if data_type is not None:
            self.data_type = data_type
        if modifiable is not None:
            self.modifiable = modifiable
        if need_restart is not None:
            self.need_restart = need_restart
        if parameter_default_value is not None:
            self.parameter_default_value = parameter_default_value
        if parameter_description is not None:
            self.parameter_description = parameter_description
        if parameter_display_value is not None:
            self.parameter_display_value = parameter_display_value
        if parameter_name is not None:
            self.parameter_name = parameter_name
        if parameter_value is not None:
            self.parameter_value = parameter_value
        if status is not None:
            self.status = status

    @property
    def checking_code(self):
        """Gets the checking_code of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The checking_code of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._checking_code

    @checking_code.setter
    def checking_code(self, checking_code):
        """Sets the checking_code of this ParameterForDescribeDBInstanceParametersOutput.


        :param checking_code: The checking_code of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._checking_code = checking_code

    @property
    def data_type(self):
        """Gets the data_type of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The data_type of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this ParameterForDescribeDBInstanceParametersOutput.


        :param data_type: The data_type of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def modifiable(self):
        """Gets the modifiable of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The modifiable of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._modifiable

    @modifiable.setter
    def modifiable(self, modifiable):
        """Sets the modifiable of this ParameterForDescribeDBInstanceParametersOutput.


        :param modifiable: The modifiable of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: bool
        """

        self._modifiable = modifiable

    @property
    def need_restart(self):
        """Gets the need_restart of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The need_restart of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._need_restart

    @need_restart.setter
    def need_restart(self, need_restart):
        """Sets the need_restart of this ParameterForDescribeDBInstanceParametersOutput.


        :param need_restart: The need_restart of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: bool
        """

        self._need_restart = need_restart

    @property
    def parameter_default_value(self):
        """Gets the parameter_default_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The parameter_default_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_default_value

    @parameter_default_value.setter
    def parameter_default_value(self, parameter_default_value):
        """Sets the parameter_default_value of this ParameterForDescribeDBInstanceParametersOutput.


        :param parameter_default_value: The parameter_default_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._parameter_default_value = parameter_default_value

    @property
    def parameter_description(self):
        """Gets the parameter_description of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The parameter_description of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_description

    @parameter_description.setter
    def parameter_description(self, parameter_description):
        """Sets the parameter_description of this ParameterForDescribeDBInstanceParametersOutput.


        :param parameter_description: The parameter_description of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._parameter_description = parameter_description

    @property
    def parameter_display_value(self):
        """Gets the parameter_display_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The parameter_display_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_display_value

    @parameter_display_value.setter
    def parameter_display_value(self, parameter_display_value):
        """Sets the parameter_display_value of this ParameterForDescribeDBInstanceParametersOutput.


        :param parameter_display_value: The parameter_display_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._parameter_display_value = parameter_display_value

    @property
    def parameter_name(self):
        """Gets the parameter_name of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The parameter_name of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_name

    @parameter_name.setter
    def parameter_name(self, parameter_name):
        """Sets the parameter_name of this ParameterForDescribeDBInstanceParametersOutput.


        :param parameter_name: The parameter_name of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._parameter_name = parameter_name

    @property
    def parameter_value(self):
        """Gets the parameter_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The parameter_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_value

    @parameter_value.setter
    def parameter_value(self, parameter_value):
        """Sets the parameter_value of this ParameterForDescribeDBInstanceParametersOutput.


        :param parameter_value: The parameter_value of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._parameter_value = parameter_value

    @property
    def status(self):
        """Gets the status of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501


        :return: The status of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ParameterForDescribeDBInstanceParametersOutput.


        :param status: The status of this ParameterForDescribeDBInstanceParametersOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ParameterForDescribeDBInstanceParametersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ParameterForDescribeDBInstanceParametersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ParameterForDescribeDBInstanceParametersOutput):
            return True

        return self.to_dict() != other.to_dict()
