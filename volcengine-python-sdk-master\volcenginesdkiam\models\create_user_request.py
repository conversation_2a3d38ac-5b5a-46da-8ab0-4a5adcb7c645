# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateUserRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'display_name': 'str',
        'email': 'str',
        'mobile_phone': 'str',
        'tags': 'list[TagForCreateUserInput]',
        'user_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'display_name': 'DisplayName',
        'email': 'Email',
        'mobile_phone': 'MobilePhone',
        'tags': 'Tags',
        'user_name': 'UserName'
    }

    def __init__(self, description=None, display_name=None, email=None, mobile_phone=None, tags=None, user_name=None, _configuration=None):  # noqa: E501
        """CreateUserRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._display_name = None
        self._email = None
        self._mobile_phone = None
        self._tags = None
        self._user_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if display_name is not None:
            self.display_name = display_name
        if email is not None:
            self.email = email
        if mobile_phone is not None:
            self.mobile_phone = mobile_phone
        if tags is not None:
            self.tags = tags
        self.user_name = user_name

    @property
    def description(self):
        """Gets the description of this CreateUserRequest.  # noqa: E501


        :return: The description of this CreateUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateUserRequest.


        :param description: The description of this CreateUserRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def display_name(self):
        """Gets the display_name of this CreateUserRequest.  # noqa: E501


        :return: The display_name of this CreateUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this CreateUserRequest.


        :param display_name: The display_name of this CreateUserRequest.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def email(self):
        """Gets the email of this CreateUserRequest.  # noqa: E501


        :return: The email of this CreateUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this CreateUserRequest.


        :param email: The email of this CreateUserRequest.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def mobile_phone(self):
        """Gets the mobile_phone of this CreateUserRequest.  # noqa: E501


        :return: The mobile_phone of this CreateUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._mobile_phone

    @mobile_phone.setter
    def mobile_phone(self, mobile_phone):
        """Sets the mobile_phone of this CreateUserRequest.


        :param mobile_phone: The mobile_phone of this CreateUserRequest.  # noqa: E501
        :type: str
        """

        self._mobile_phone = mobile_phone

    @property
    def tags(self):
        """Gets the tags of this CreateUserRequest.  # noqa: E501


        :return: The tags of this CreateUserRequest.  # noqa: E501
        :rtype: list[TagForCreateUserInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateUserRequest.


        :param tags: The tags of this CreateUserRequest.  # noqa: E501
        :type: list[TagForCreateUserInput]
        """

        self._tags = tags

    @property
    def user_name(self):
        """Gets the user_name of this CreateUserRequest.  # noqa: E501


        :return: The user_name of this CreateUserRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this CreateUserRequest.


        :param user_name: The user_name of this CreateUserRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user_name is None:
            raise ValueError("Invalid value for `user_name`, must not be `None`")  # noqa: E501

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateUserRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateUserRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateUserRequest):
            return True

        return self.to_dict() != other.to_dict()
