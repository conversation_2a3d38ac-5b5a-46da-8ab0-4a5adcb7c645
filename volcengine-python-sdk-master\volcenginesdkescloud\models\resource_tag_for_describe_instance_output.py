# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceTagForDescribeInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'tag_keys': 'list[str]',
        'tag_kvs': 'TagKvsForDescribeInstanceOutput',
        'type': 'str'
    }

    attribute_map = {
        'tag_keys': 'TagKeys',
        'tag_kvs': 'TagKvs',
        'type': 'Type'
    }

    def __init__(self, tag_keys=None, tag_kvs=None, type=None, _configuration=None):  # noqa: E501
        """ResourceTagForDescribeInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._tag_keys = None
        self._tag_kvs = None
        self._type = None
        self.discriminator = None

        if tag_keys is not None:
            self.tag_keys = tag_keys
        if tag_kvs is not None:
            self.tag_kvs = tag_kvs
        if type is not None:
            self.type = type

    @property
    def tag_keys(self):
        """Gets the tag_keys of this ResourceTagForDescribeInstanceOutput.  # noqa: E501


        :return: The tag_keys of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag_keys

    @tag_keys.setter
    def tag_keys(self, tag_keys):
        """Sets the tag_keys of this ResourceTagForDescribeInstanceOutput.


        :param tag_keys: The tag_keys of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag_keys = tag_keys

    @property
    def tag_kvs(self):
        """Gets the tag_kvs of this ResourceTagForDescribeInstanceOutput.  # noqa: E501


        :return: The tag_kvs of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :rtype: TagKvsForDescribeInstanceOutput
        """
        return self._tag_kvs

    @tag_kvs.setter
    def tag_kvs(self, tag_kvs):
        """Sets the tag_kvs of this ResourceTagForDescribeInstanceOutput.


        :param tag_kvs: The tag_kvs of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :type: TagKvsForDescribeInstanceOutput
        """

        self._tag_kvs = tag_kvs

    @property
    def type(self):
        """Gets the type of this ResourceTagForDescribeInstanceOutput.  # noqa: E501


        :return: The type of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ResourceTagForDescribeInstanceOutput.


        :param type: The type of this ResourceTagForDescribeInstanceOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceTagForDescribeInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceTagForDescribeInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceTagForDescribeInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
