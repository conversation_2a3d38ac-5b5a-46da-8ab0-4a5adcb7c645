# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dts_task_id': 'str',
        'dts_task_name': 'str',
        'dts_task_status': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'region': 'str',
        'seconds_behind_master': 'int'
    }

    attribute_map = {
        'dts_task_id': 'DtsTaskId',
        'dts_task_name': 'DtsTaskName',
        'dts_task_status': 'DtsTaskStatus',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'region': 'Region',
        'seconds_behind_master': 'SecondsBehindMaster'
    }

    def __init__(self, dts_task_id=None, dts_task_name=None, dts_task_status=None, instance_id=None, instance_name=None, region=None, seconds_behind_master=None, _configuration=None):  # noqa: E501
        """DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dts_task_id = None
        self._dts_task_name = None
        self._dts_task_status = None
        self._instance_id = None
        self._instance_name = None
        self._region = None
        self._seconds_behind_master = None
        self.discriminator = None

        if dts_task_id is not None:
            self.dts_task_id = dts_task_id
        if dts_task_name is not None:
            self.dts_task_name = dts_task_name
        if dts_task_status is not None:
            self.dts_task_status = dts_task_status
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if region is not None:
            self.region = region
        if seconds_behind_master is not None:
            self.seconds_behind_master = seconds_behind_master

    @property
    def dts_task_id(self):
        """Gets the dts_task_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dts_task_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dts_task_id

    @dts_task_id.setter
    def dts_task_id(self, dts_task_id):
        """Sets the dts_task_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param dts_task_id: The dts_task_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dts_task_id = dts_task_id

    @property
    def dts_task_name(self):
        """Gets the dts_task_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dts_task_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dts_task_name

    @dts_task_name.setter
    def dts_task_name(self, dts_task_name):
        """Sets the dts_task_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param dts_task_name: The dts_task_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dts_task_name = dts_task_name

    @property
    def dts_task_status(self):
        """Gets the dts_task_status of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The dts_task_status of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._dts_task_status

    @dts_task_status.setter
    def dts_task_status(self, dts_task_status):
        """Sets the dts_task_status of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param dts_task_status: The dts_task_status of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._dts_task_status = dts_task_status

    @property
    def instance_id(self):
        """Gets the instance_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def region(self):
        """Gets the region of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param region: The region of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def seconds_behind_master(self):
        """Gets the seconds_behind_master of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The seconds_behind_master of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._seconds_behind_master

    @seconds_behind_master.setter
    def seconds_behind_master(self, seconds_behind_master):
        """Sets the seconds_behind_master of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.


        :param seconds_behind_master: The seconds_behind_master of this DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._seconds_behind_master = seconds_behind_master

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
