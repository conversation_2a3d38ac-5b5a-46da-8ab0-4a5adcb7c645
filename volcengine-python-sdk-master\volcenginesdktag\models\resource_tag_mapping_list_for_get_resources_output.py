# coding: utf-8

"""
    tag

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceTagMappingListForGetResourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'region': 'str',
        'resource_id': 'str',
        'resource_trn': 'str',
        'resource_type': 'str',
        'service_name': 'str',
        'tags': 'list[TagForGetResourcesOutput]'
    }

    attribute_map = {
        'region': 'Region',
        'resource_id': 'ResourceID',
        'resource_trn': 'ResourceTrn',
        'resource_type': 'ResourceType',
        'service_name': 'ServiceName',
        'tags': 'Tags'
    }

    def __init__(self, region=None, resource_id=None, resource_trn=None, resource_type=None, service_name=None, tags=None, _configuration=None):  # noqa: E501
        """ResourceTagMappingListForGetResourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._region = None
        self._resource_id = None
        self._resource_trn = None
        self._resource_type = None
        self._service_name = None
        self._tags = None
        self.discriminator = None

        if region is not None:
            self.region = region
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_trn is not None:
            self.resource_trn = resource_trn
        if resource_type is not None:
            self.resource_type = resource_type
        if service_name is not None:
            self.service_name = service_name
        if tags is not None:
            self.tags = tags

    @property
    def region(self):
        """Gets the region of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The region of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ResourceTagMappingListForGetResourcesOutput.


        :param region: The region of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def resource_id(self):
        """Gets the resource_id of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The resource_id of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this ResourceTagMappingListForGetResourcesOutput.


        :param resource_id: The resource_id of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_trn(self):
        """Gets the resource_trn of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The resource_trn of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_trn

    @resource_trn.setter
    def resource_trn(self, resource_trn):
        """Sets the resource_trn of this ResourceTagMappingListForGetResourcesOutput.


        :param resource_trn: The resource_trn of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_trn = resource_trn

    @property
    def resource_type(self):
        """Gets the resource_type of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The resource_type of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ResourceTagMappingListForGetResourcesOutput.


        :param resource_type: The resource_type of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_name(self):
        """Gets the service_name of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The service_name of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this ResourceTagMappingListForGetResourcesOutput.


        :param service_name: The service_name of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def tags(self):
        """Gets the tags of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501


        :return: The tags of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :rtype: list[TagForGetResourcesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ResourceTagMappingListForGetResourcesOutput.


        :param tags: The tags of this ResourceTagMappingListForGetResourcesOutput.  # noqa: E501
        :type: list[TagForGetResourcesOutput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceTagMappingListForGetResourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceTagMappingListForGetResourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceTagMappingListForGetResourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
