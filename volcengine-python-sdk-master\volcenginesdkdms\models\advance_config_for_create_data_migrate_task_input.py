# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AdvanceConfigForCreateDataMigrateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'increase_setting': 'IncreaseSettingForCreateDataMigrateTaskInput',
        'private_link': 'PrivateLinkForCreateDataMigrateTaskInput',
        'rename_setting': 'RenameSettingForCreateDataMigrateTaskInput',
        'time_bandwidth_setting': 'list[TimeBandwidthSettingForCreateDataMigrateTaskInput]'
    }

    attribute_map = {
        'increase_setting': 'IncreaseSetting',
        'private_link': 'PrivateLink',
        'rename_setting': 'RenameSetting',
        'time_bandwidth_setting': 'TimeBandwidthSetting'
    }

    def __init__(self, increase_setting=None, private_link=None, rename_setting=None, time_bandwidth_setting=None, _configuration=None):  # noqa: E501
        """AdvanceConfigForCreateDataMigrateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._increase_setting = None
        self._private_link = None
        self._rename_setting = None
        self._time_bandwidth_setting = None
        self.discriminator = None

        if increase_setting is not None:
            self.increase_setting = increase_setting
        if private_link is not None:
            self.private_link = private_link
        if rename_setting is not None:
            self.rename_setting = rename_setting
        if time_bandwidth_setting is not None:
            self.time_bandwidth_setting = time_bandwidth_setting

    @property
    def increase_setting(self):
        """Gets the increase_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The increase_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: IncreaseSettingForCreateDataMigrateTaskInput
        """
        return self._increase_setting

    @increase_setting.setter
    def increase_setting(self, increase_setting):
        """Sets the increase_setting of this AdvanceConfigForCreateDataMigrateTaskInput.


        :param increase_setting: The increase_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: IncreaseSettingForCreateDataMigrateTaskInput
        """

        self._increase_setting = increase_setting

    @property
    def private_link(self):
        """Gets the private_link of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The private_link of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: PrivateLinkForCreateDataMigrateTaskInput
        """
        return self._private_link

    @private_link.setter
    def private_link(self, private_link):
        """Sets the private_link of this AdvanceConfigForCreateDataMigrateTaskInput.


        :param private_link: The private_link of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: PrivateLinkForCreateDataMigrateTaskInput
        """

        self._private_link = private_link

    @property
    def rename_setting(self):
        """Gets the rename_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The rename_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: RenameSettingForCreateDataMigrateTaskInput
        """
        return self._rename_setting

    @rename_setting.setter
    def rename_setting(self, rename_setting):
        """Sets the rename_setting of this AdvanceConfigForCreateDataMigrateTaskInput.


        :param rename_setting: The rename_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: RenameSettingForCreateDataMigrateTaskInput
        """

        self._rename_setting = rename_setting

    @property
    def time_bandwidth_setting(self):
        """Gets the time_bandwidth_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The time_bandwidth_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: list[TimeBandwidthSettingForCreateDataMigrateTaskInput]
        """
        return self._time_bandwidth_setting

    @time_bandwidth_setting.setter
    def time_bandwidth_setting(self, time_bandwidth_setting):
        """Sets the time_bandwidth_setting of this AdvanceConfigForCreateDataMigrateTaskInput.


        :param time_bandwidth_setting: The time_bandwidth_setting of this AdvanceConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: list[TimeBandwidthSettingForCreateDataMigrateTaskInput]
        """

        self._time_bandwidth_setting = time_bandwidth_setting

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AdvanceConfigForCreateDataMigrateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AdvanceConfigForCreateDataMigrateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AdvanceConfigForCreateDataMigrateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
