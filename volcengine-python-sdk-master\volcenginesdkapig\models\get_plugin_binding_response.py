# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetPluginBindingResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'enable': 'bool',
        'gateway_id': 'str',
        'gateway_name': 'str',
        'id': 'str',
        'plugin_config': 'str',
        'plugin_name': 'str',
        'route_id': 'str',
        'route_name': 'str',
        'scope': 'str',
        'service_id': 'str',
        'service_name': 'str',
        'target': 'str',
        'target_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'enable': 'Enable',
        'gateway_id': 'GatewayId',
        'gateway_name': 'GatewayName',
        'id': 'Id',
        'plugin_config': 'PluginConfig',
        'plugin_name': 'PluginName',
        'route_id': 'RouteId',
        'route_name': 'RouteName',
        'scope': 'Scope',
        'service_id': 'ServiceId',
        'service_name': 'ServiceName',
        'target': 'Target',
        'target_name': 'TargetName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, description=None, enable=None, gateway_id=None, gateway_name=None, id=None, plugin_config=None, plugin_name=None, route_id=None, route_name=None, scope=None, service_id=None, service_name=None, target=None, target_name=None, update_time=None, _configuration=None):  # noqa: E501
        """GetPluginBindingResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._enable = None
        self._gateway_id = None
        self._gateway_name = None
        self._id = None
        self._plugin_config = None
        self._plugin_name = None
        self._route_id = None
        self._route_name = None
        self._scope = None
        self._service_id = None
        self._service_name = None
        self._target = None
        self._target_name = None
        self._update_time = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if enable is not None:
            self.enable = enable
        if gateway_id is not None:
            self.gateway_id = gateway_id
        if gateway_name is not None:
            self.gateway_name = gateway_name
        if id is not None:
            self.id = id
        if plugin_config is not None:
            self.plugin_config = plugin_config
        if plugin_name is not None:
            self.plugin_name = plugin_name
        if route_id is not None:
            self.route_id = route_id
        if route_name is not None:
            self.route_name = route_name
        if scope is not None:
            self.scope = scope
        if service_id is not None:
            self.service_id = service_id
        if service_name is not None:
            self.service_name = service_name
        if target is not None:
            self.target = target
        if target_name is not None:
            self.target_name = target_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def description(self):
        """Gets the description of this GetPluginBindingResponse.  # noqa: E501


        :return: The description of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetPluginBindingResponse.


        :param description: The description of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable(self):
        """Gets the enable of this GetPluginBindingResponse.  # noqa: E501


        :return: The enable of this GetPluginBindingResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this GetPluginBindingResponse.


        :param enable: The enable of this GetPluginBindingResponse.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def gateway_id(self):
        """Gets the gateway_id of this GetPluginBindingResponse.  # noqa: E501


        :return: The gateway_id of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this GetPluginBindingResponse.


        :param gateway_id: The gateway_id of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._gateway_id = gateway_id

    @property
    def gateway_name(self):
        """Gets the gateway_name of this GetPluginBindingResponse.  # noqa: E501


        :return: The gateway_name of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._gateway_name

    @gateway_name.setter
    def gateway_name(self, gateway_name):
        """Sets the gateway_name of this GetPluginBindingResponse.


        :param gateway_name: The gateway_name of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._gateway_name = gateway_name

    @property
    def id(self):
        """Gets the id of this GetPluginBindingResponse.  # noqa: E501


        :return: The id of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetPluginBindingResponse.


        :param id: The id of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def plugin_config(self):
        """Gets the plugin_config of this GetPluginBindingResponse.  # noqa: E501


        :return: The plugin_config of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._plugin_config

    @plugin_config.setter
    def plugin_config(self, plugin_config):
        """Sets the plugin_config of this GetPluginBindingResponse.


        :param plugin_config: The plugin_config of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._plugin_config = plugin_config

    @property
    def plugin_name(self):
        """Gets the plugin_name of this GetPluginBindingResponse.  # noqa: E501


        :return: The plugin_name of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._plugin_name

    @plugin_name.setter
    def plugin_name(self, plugin_name):
        """Sets the plugin_name of this GetPluginBindingResponse.


        :param plugin_name: The plugin_name of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._plugin_name = plugin_name

    @property
    def route_id(self):
        """Gets the route_id of this GetPluginBindingResponse.  # noqa: E501


        :return: The route_id of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._route_id

    @route_id.setter
    def route_id(self, route_id):
        """Sets the route_id of this GetPluginBindingResponse.


        :param route_id: The route_id of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._route_id = route_id

    @property
    def route_name(self):
        """Gets the route_name of this GetPluginBindingResponse.  # noqa: E501


        :return: The route_name of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._route_name

    @route_name.setter
    def route_name(self, route_name):
        """Sets the route_name of this GetPluginBindingResponse.


        :param route_name: The route_name of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._route_name = route_name

    @property
    def scope(self):
        """Gets the scope of this GetPluginBindingResponse.  # noqa: E501


        :return: The scope of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._scope

    @scope.setter
    def scope(self, scope):
        """Sets the scope of this GetPluginBindingResponse.


        :param scope: The scope of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._scope = scope

    @property
    def service_id(self):
        """Gets the service_id of this GetPluginBindingResponse.  # noqa: E501


        :return: The service_id of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this GetPluginBindingResponse.


        :param service_id: The service_id of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def service_name(self):
        """Gets the service_name of this GetPluginBindingResponse.  # noqa: E501


        :return: The service_name of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this GetPluginBindingResponse.


        :param service_name: The service_name of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def target(self):
        """Gets the target of this GetPluginBindingResponse.  # noqa: E501


        :return: The target of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this GetPluginBindingResponse.


        :param target: The target of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._target = target

    @property
    def target_name(self):
        """Gets the target_name of this GetPluginBindingResponse.  # noqa: E501


        :return: The target_name of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._target_name

    @target_name.setter
    def target_name(self, target_name):
        """Sets the target_name of this GetPluginBindingResponse.


        :param target_name: The target_name of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._target_name = target_name

    @property
    def update_time(self):
        """Gets the update_time of this GetPluginBindingResponse.  # noqa: E501


        :return: The update_time of this GetPluginBindingResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetPluginBindingResponse.


        :param update_time: The update_time of this GetPluginBindingResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetPluginBindingResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetPluginBindingResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetPluginBindingResponse):
            return True

        return self.to_dict() != other.to_dict()
