# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDevDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'create_time': 'int',
        'flavor_id': 'str',
        'image_id': 'str',
        'platform': 'str',
        'ports': 'list[PortForGetDevDetailOutput]',
        'private_ip': 'str',
        'public_ip': 'str',
        'region': 'str',
        'status': 'str',
        'storages': 'list[StorageForGetDevDetailOutput]',
        'volume': 'VolumeForGetDevDetailOutput',
        'vpc_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'create_time': 'CreateTime',
        'flavor_id': 'FlavorId',
        'image_id': 'ImageID',
        'platform': 'Platform',
        'ports': 'Ports',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'region': 'Region',
        'status': 'Status',
        'storages': 'Storages',
        'volume': 'Volume',
        'vpc_id': 'VpcId'
    }

    def __init__(self, account_id=None, asset_id=None, asset_name=None, create_time=None, flavor_id=None, image_id=None, platform=None, ports=None, private_ip=None, public_ip=None, region=None, status=None, storages=None, volume=None, vpc_id=None, _configuration=None):  # noqa: E501
        """GetDevDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asset_id = None
        self._asset_name = None
        self._create_time = None
        self._flavor_id = None
        self._image_id = None
        self._platform = None
        self._ports = None
        self._private_ip = None
        self._public_ip = None
        self._region = None
        self._status = None
        self._storages = None
        self._volume = None
        self._vpc_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if create_time is not None:
            self.create_time = create_time
        if flavor_id is not None:
            self.flavor_id = flavor_id
        if image_id is not None:
            self.image_id = image_id
        if platform is not None:
            self.platform = platform
        if ports is not None:
            self.ports = ports
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if region is not None:
            self.region = region
        if status is not None:
            self.status = status
        if storages is not None:
            self.storages = storages
        if volume is not None:
            self.volume = volume
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def account_id(self):
        """Gets the account_id of this GetDevDetailResponse.  # noqa: E501


        :return: The account_id of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this GetDevDetailResponse.


        :param account_id: The account_id of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asset_id(self):
        """Gets the asset_id of this GetDevDetailResponse.  # noqa: E501


        :return: The asset_id of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this GetDevDetailResponse.


        :param asset_id: The asset_id of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this GetDevDetailResponse.  # noqa: E501


        :return: The asset_name of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this GetDevDetailResponse.


        :param asset_name: The asset_name of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def create_time(self):
        """Gets the create_time of this GetDevDetailResponse.  # noqa: E501


        :return: The create_time of this GetDevDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetDevDetailResponse.


        :param create_time: The create_time of this GetDevDetailResponse.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def flavor_id(self):
        """Gets the flavor_id of this GetDevDetailResponse.  # noqa: E501


        :return: The flavor_id of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._flavor_id

    @flavor_id.setter
    def flavor_id(self, flavor_id):
        """Sets the flavor_id of this GetDevDetailResponse.


        :param flavor_id: The flavor_id of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._flavor_id = flavor_id

    @property
    def image_id(self):
        """Gets the image_id of this GetDevDetailResponse.  # noqa: E501


        :return: The image_id of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this GetDevDetailResponse.


        :param image_id: The image_id of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def platform(self):
        """Gets the platform of this GetDevDetailResponse.  # noqa: E501


        :return: The platform of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this GetDevDetailResponse.


        :param platform: The platform of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def ports(self):
        """Gets the ports of this GetDevDetailResponse.  # noqa: E501


        :return: The ports of this GetDevDetailResponse.  # noqa: E501
        :rtype: list[PortForGetDevDetailOutput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this GetDevDetailResponse.


        :param ports: The ports of this GetDevDetailResponse.  # noqa: E501
        :type: list[PortForGetDevDetailOutput]
        """

        self._ports = ports

    @property
    def private_ip(self):
        """Gets the private_ip of this GetDevDetailResponse.  # noqa: E501


        :return: The private_ip of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this GetDevDetailResponse.


        :param private_ip: The private_ip of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this GetDevDetailResponse.  # noqa: E501


        :return: The public_ip of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this GetDevDetailResponse.


        :param public_ip: The public_ip of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def region(self):
        """Gets the region of this GetDevDetailResponse.  # noqa: E501


        :return: The region of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this GetDevDetailResponse.


        :param region: The region of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def status(self):
        """Gets the status of this GetDevDetailResponse.  # noqa: E501


        :return: The status of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetDevDetailResponse.


        :param status: The status of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def storages(self):
        """Gets the storages of this GetDevDetailResponse.  # noqa: E501


        :return: The storages of this GetDevDetailResponse.  # noqa: E501
        :rtype: list[StorageForGetDevDetailOutput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this GetDevDetailResponse.


        :param storages: The storages of this GetDevDetailResponse.  # noqa: E501
        :type: list[StorageForGetDevDetailOutput]
        """

        self._storages = storages

    @property
    def volume(self):
        """Gets the volume of this GetDevDetailResponse.  # noqa: E501


        :return: The volume of this GetDevDetailResponse.  # noqa: E501
        :rtype: VolumeForGetDevDetailOutput
        """
        return self._volume

    @volume.setter
    def volume(self, volume):
        """Sets the volume of this GetDevDetailResponse.


        :param volume: The volume of this GetDevDetailResponse.  # noqa: E501
        :type: VolumeForGetDevDetailOutput
        """

        self._volume = volume

    @property
    def vpc_id(self):
        """Gets the vpc_id of this GetDevDetailResponse.  # noqa: E501


        :return: The vpc_id of this GetDevDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this GetDevDetailResponse.


        :param vpc_id: The vpc_id of this GetDevDetailResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDevDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDevDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDevDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
