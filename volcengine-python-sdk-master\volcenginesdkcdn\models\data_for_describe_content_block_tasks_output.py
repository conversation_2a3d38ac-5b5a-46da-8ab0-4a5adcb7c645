# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeContentBlockTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'block_reason': 'str',
        'create_time': 'int',
        'status': 'str',
        'task_id': 'str',
        'task_type': 'str',
        'url': 'str'
    }

    attribute_map = {
        'block_reason': 'BlockReason',
        'create_time': 'CreateTime',
        'status': 'Status',
        'task_id': 'TaskID',
        'task_type': 'TaskType',
        'url': 'Url'
    }

    def __init__(self, block_reason=None, create_time=None, status=None, task_id=None, task_type=None, url=None, _configuration=None):  # noqa: E501
        """DataForDescribeContentBlockTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._block_reason = None
        self._create_time = None
        self._status = None
        self._task_id = None
        self._task_type = None
        self._url = None
        self.discriminator = None

        if block_reason is not None:
            self.block_reason = block_reason
        if create_time is not None:
            self.create_time = create_time
        if status is not None:
            self.status = status
        if task_id is not None:
            self.task_id = task_id
        if task_type is not None:
            self.task_type = task_type
        if url is not None:
            self.url = url

    @property
    def block_reason(self):
        """Gets the block_reason of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The block_reason of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._block_reason

    @block_reason.setter
    def block_reason(self, block_reason):
        """Sets the block_reason of this DataForDescribeContentBlockTasksOutput.


        :param block_reason: The block_reason of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: str
        """

        self._block_reason = block_reason

    @property
    def create_time(self):
        """Gets the create_time of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The create_time of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForDescribeContentBlockTasksOutput.


        :param create_time: The create_time of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def status(self):
        """Gets the status of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The status of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForDescribeContentBlockTasksOutput.


        :param status: The status of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def task_id(self):
        """Gets the task_id of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The task_id of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DataForDescribeContentBlockTasksOutput.


        :param task_id: The task_id of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_type(self):
        """Gets the task_type of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The task_type of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this DataForDescribeContentBlockTasksOutput.


        :param task_type: The task_type of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    @property
    def url(self):
        """Gets the url of this DataForDescribeContentBlockTasksOutput.  # noqa: E501


        :return: The url of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this DataForDescribeContentBlockTasksOutput.


        :param url: The url of this DataForDescribeContentBlockTasksOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeContentBlockTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeContentBlockTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeContentBlockTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
