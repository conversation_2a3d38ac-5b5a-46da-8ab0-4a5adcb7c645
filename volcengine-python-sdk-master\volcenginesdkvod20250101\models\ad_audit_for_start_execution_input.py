# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AdAuditForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'advertiser_id': 'str',
        'business_type': 'str'
    }

    attribute_map = {
        'advertiser_id': 'AdvertiserId',
        'business_type': 'BusinessType'
    }

    def __init__(self, advertiser_id=None, business_type=None, _configuration=None):  # noqa: E501
        """AdAuditForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._advertiser_id = None
        self._business_type = None
        self.discriminator = None

        if advertiser_id is not None:
            self.advertiser_id = advertiser_id
        if business_type is not None:
            self.business_type = business_type

    @property
    def advertiser_id(self):
        """Gets the advertiser_id of this AdAuditForStartExecutionInput.  # noqa: E501


        :return: The advertiser_id of this AdAuditForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._advertiser_id

    @advertiser_id.setter
    def advertiser_id(self, advertiser_id):
        """Sets the advertiser_id of this AdAuditForStartExecutionInput.


        :param advertiser_id: The advertiser_id of this AdAuditForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._advertiser_id = advertiser_id

    @property
    def business_type(self):
        """Gets the business_type of this AdAuditForStartExecutionInput.  # noqa: E501


        :return: The business_type of this AdAuditForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._business_type

    @business_type.setter
    def business_type(self, business_type):
        """Sets the business_type of this AdAuditForStartExecutionInput.


        :param business_type: The business_type of this AdAuditForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._business_type = business_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AdAuditForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AdAuditForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AdAuditForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
