# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetStreamDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'b_audio': 'list[BAudioForGetStreamDataOutput]',
        'b_video': 'list[BVideoForGetStreamDataOutput]',
        'fps': 'list[FPForGetStreamDataOutput]',
        'frame': 'list[FrameForGetStreamDataOutput]',
        'height': 'int',
        'v_codec': 'str',
        'width': 'int'
    }

    attribute_map = {
        'b_audio': 'BAudio',
        'b_video': 'BVideo',
        'fps': 'FPS',
        'frame': 'Frame',
        'height': 'Height',
        'v_codec': 'VCodec',
        'width': 'Width'
    }

    def __init__(self, b_audio=None, b_video=None, fps=None, frame=None, height=None, v_codec=None, width=None, _configuration=None):  # noqa: E501
        """GetStreamDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._b_audio = None
        self._b_video = None
        self._fps = None
        self._frame = None
        self._height = None
        self._v_codec = None
        self._width = None
        self.discriminator = None

        if b_audio is not None:
            self.b_audio = b_audio
        if b_video is not None:
            self.b_video = b_video
        if fps is not None:
            self.fps = fps
        if frame is not None:
            self.frame = frame
        if height is not None:
            self.height = height
        if v_codec is not None:
            self.v_codec = v_codec
        if width is not None:
            self.width = width

    @property
    def b_audio(self):
        """Gets the b_audio of this GetStreamDataResponse.  # noqa: E501


        :return: The b_audio of this GetStreamDataResponse.  # noqa: E501
        :rtype: list[BAudioForGetStreamDataOutput]
        """
        return self._b_audio

    @b_audio.setter
    def b_audio(self, b_audio):
        """Sets the b_audio of this GetStreamDataResponse.


        :param b_audio: The b_audio of this GetStreamDataResponse.  # noqa: E501
        :type: list[BAudioForGetStreamDataOutput]
        """

        self._b_audio = b_audio

    @property
    def b_video(self):
        """Gets the b_video of this GetStreamDataResponse.  # noqa: E501


        :return: The b_video of this GetStreamDataResponse.  # noqa: E501
        :rtype: list[BVideoForGetStreamDataOutput]
        """
        return self._b_video

    @b_video.setter
    def b_video(self, b_video):
        """Sets the b_video of this GetStreamDataResponse.


        :param b_video: The b_video of this GetStreamDataResponse.  # noqa: E501
        :type: list[BVideoForGetStreamDataOutput]
        """

        self._b_video = b_video

    @property
    def fps(self):
        """Gets the fps of this GetStreamDataResponse.  # noqa: E501


        :return: The fps of this GetStreamDataResponse.  # noqa: E501
        :rtype: list[FPForGetStreamDataOutput]
        """
        return self._fps

    @fps.setter
    def fps(self, fps):
        """Sets the fps of this GetStreamDataResponse.


        :param fps: The fps of this GetStreamDataResponse.  # noqa: E501
        :type: list[FPForGetStreamDataOutput]
        """

        self._fps = fps

    @property
    def frame(self):
        """Gets the frame of this GetStreamDataResponse.  # noqa: E501


        :return: The frame of this GetStreamDataResponse.  # noqa: E501
        :rtype: list[FrameForGetStreamDataOutput]
        """
        return self._frame

    @frame.setter
    def frame(self, frame):
        """Sets the frame of this GetStreamDataResponse.


        :param frame: The frame of this GetStreamDataResponse.  # noqa: E501
        :type: list[FrameForGetStreamDataOutput]
        """

        self._frame = frame

    @property
    def height(self):
        """Gets the height of this GetStreamDataResponse.  # noqa: E501


        :return: The height of this GetStreamDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._height

    @height.setter
    def height(self, height):
        """Sets the height of this GetStreamDataResponse.


        :param height: The height of this GetStreamDataResponse.  # noqa: E501
        :type: int
        """

        self._height = height

    @property
    def v_codec(self):
        """Gets the v_codec of this GetStreamDataResponse.  # noqa: E501


        :return: The v_codec of this GetStreamDataResponse.  # noqa: E501
        :rtype: str
        """
        return self._v_codec

    @v_codec.setter
    def v_codec(self, v_codec):
        """Sets the v_codec of this GetStreamDataResponse.


        :param v_codec: The v_codec of this GetStreamDataResponse.  # noqa: E501
        :type: str
        """

        self._v_codec = v_codec

    @property
    def width(self):
        """Gets the width of this GetStreamDataResponse.  # noqa: E501


        :return: The width of this GetStreamDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._width

    @width.setter
    def width(self, width):
        """Sets the width of this GetStreamDataResponse.


        :param width: The width of this GetStreamDataResponse.  # noqa: E501
        :type: int
        """

        self._width = width

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetStreamDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetStreamDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetStreamDataResponse):
            return True

        return self.to_dict() != other.to_dict()
