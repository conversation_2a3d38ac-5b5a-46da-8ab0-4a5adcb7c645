# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PostApiV1OverviewDescribeAssetInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'protect_stats': 'ProtectStatsForPostApiV1OverviewDescribeAssetInfoOutput',
        'resource_info': 'list[ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput]',
        'risk': 'int',
        'risk_top': 'list[RiskTopForPostApiV1OverviewDescribeAssetInfoOutput]',
        'riskless': 'int',
        'total': 'int'
    }

    attribute_map = {
        'protect_stats': 'protect_stats',
        'resource_info': 'resource_info',
        'risk': 'risk',
        'risk_top': 'risk_top',
        'riskless': 'riskless',
        'total': 'total'
    }

    def __init__(self, protect_stats=None, resource_info=None, risk=None, risk_top=None, riskless=None, total=None, _configuration=None):  # noqa: E501
        """PostApiV1OverviewDescribeAssetInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._protect_stats = None
        self._resource_info = None
        self._risk = None
        self._risk_top = None
        self._riskless = None
        self._total = None
        self.discriminator = None

        if protect_stats is not None:
            self.protect_stats = protect_stats
        if resource_info is not None:
            self.resource_info = resource_info
        if risk is not None:
            self.risk = risk
        if risk_top is not None:
            self.risk_top = risk_top
        if riskless is not None:
            self.riskless = riskless
        if total is not None:
            self.total = total

    @property
    def protect_stats(self):
        """Gets the protect_stats of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The protect_stats of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: ProtectStatsForPostApiV1OverviewDescribeAssetInfoOutput
        """
        return self._protect_stats

    @protect_stats.setter
    def protect_stats(self, protect_stats):
        """Sets the protect_stats of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param protect_stats: The protect_stats of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: ProtectStatsForPostApiV1OverviewDescribeAssetInfoOutput
        """

        self._protect_stats = protect_stats

    @property
    def resource_info(self):
        """Gets the resource_info of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The resource_info of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: list[ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput]
        """
        return self._resource_info

    @resource_info.setter
    def resource_info(self, resource_info):
        """Sets the resource_info of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param resource_info: The resource_info of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: list[ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput]
        """

        self._resource_info = resource_info

    @property
    def risk(self):
        """Gets the risk of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The risk of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param risk: The risk of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: int
        """

        self._risk = risk

    @property
    def risk_top(self):
        """Gets the risk_top of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The risk_top of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: list[RiskTopForPostApiV1OverviewDescribeAssetInfoOutput]
        """
        return self._risk_top

    @risk_top.setter
    def risk_top(self, risk_top):
        """Sets the risk_top of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param risk_top: The risk_top of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: list[RiskTopForPostApiV1OverviewDescribeAssetInfoOutput]
        """

        self._risk_top = risk_top

    @property
    def riskless(self):
        """Gets the riskless of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The riskless of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._riskless

    @riskless.setter
    def riskless(self, riskless):
        """Sets the riskless of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param riskless: The riskless of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: int
        """

        self._riskless = riskless

    @property
    def total(self):
        """Gets the total of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501


        :return: The total of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this PostApiV1OverviewDescribeAssetInfoResponse.


        :param total: The total of this PostApiV1OverviewDescribeAssetInfoResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PostApiV1OverviewDescribeAssetInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PostApiV1OverviewDescribeAssetInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PostApiV1OverviewDescribeAssetInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
