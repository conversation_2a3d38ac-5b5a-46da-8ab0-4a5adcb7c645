# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListPosterInviteAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'invite_list': 'list[InviteListForListPosterInviteAPIOutput]',
        'is_avatar_show_enable': 'int',
        'total_item_count': 'int'
    }

    attribute_map = {
        'invite_list': 'InviteList',
        'is_avatar_show_enable': 'IsAvatarShowEnable',
        'total_item_count': 'TotalItemCount'
    }

    def __init__(self, invite_list=None, is_avatar_show_enable=None, total_item_count=None, _configuration=None):  # noqa: E501
        """ListPosterInviteAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._invite_list = None
        self._is_avatar_show_enable = None
        self._total_item_count = None
        self.discriminator = None

        if invite_list is not None:
            self.invite_list = invite_list
        if is_avatar_show_enable is not None:
            self.is_avatar_show_enable = is_avatar_show_enable
        if total_item_count is not None:
            self.total_item_count = total_item_count

    @property
    def invite_list(self):
        """Gets the invite_list of this ListPosterInviteAPIResponse.  # noqa: E501


        :return: The invite_list of this ListPosterInviteAPIResponse.  # noqa: E501
        :rtype: list[InviteListForListPosterInviteAPIOutput]
        """
        return self._invite_list

    @invite_list.setter
    def invite_list(self, invite_list):
        """Sets the invite_list of this ListPosterInviteAPIResponse.


        :param invite_list: The invite_list of this ListPosterInviteAPIResponse.  # noqa: E501
        :type: list[InviteListForListPosterInviteAPIOutput]
        """

        self._invite_list = invite_list

    @property
    def is_avatar_show_enable(self):
        """Gets the is_avatar_show_enable of this ListPosterInviteAPIResponse.  # noqa: E501


        :return: The is_avatar_show_enable of this ListPosterInviteAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_avatar_show_enable

    @is_avatar_show_enable.setter
    def is_avatar_show_enable(self, is_avatar_show_enable):
        """Sets the is_avatar_show_enable of this ListPosterInviteAPIResponse.


        :param is_avatar_show_enable: The is_avatar_show_enable of this ListPosterInviteAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_avatar_show_enable = is_avatar_show_enable

    @property
    def total_item_count(self):
        """Gets the total_item_count of this ListPosterInviteAPIResponse.  # noqa: E501


        :return: The total_item_count of this ListPosterInviteAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_item_count

    @total_item_count.setter
    def total_item_count(self, total_item_count):
        """Sets the total_item_count of this ListPosterInviteAPIResponse.


        :param total_item_count: The total_item_count of this ListPosterInviteAPIResponse.  # noqa: E501
        :type: int
        """

        self._total_item_count = total_item_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListPosterInviteAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListPosterInviteAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListPosterInviteAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
