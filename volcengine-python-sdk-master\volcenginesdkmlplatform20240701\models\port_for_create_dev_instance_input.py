# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PortForCreateDevInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_public_network_access': 'bool',
        'external_port': 'int',
        'internal_port': 'int',
        'name': 'str',
        'type': 'str'
    }

    attribute_map = {
        'enable_public_network_access': 'EnablePublicNetworkAccess',
        'external_port': 'ExternalPort',
        'internal_port': 'InternalPort',
        'name': 'Name',
        'type': 'Type'
    }

    def __init__(self, enable_public_network_access=None, external_port=None, internal_port=None, name=None, type=None, _configuration=None):  # noqa: E501
        """PortForCreateDevInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_public_network_access = None
        self._external_port = None
        self._internal_port = None
        self._name = None
        self._type = None
        self.discriminator = None

        if enable_public_network_access is not None:
            self.enable_public_network_access = enable_public_network_access
        if external_port is not None:
            self.external_port = external_port
        if internal_port is not None:
            self.internal_port = internal_port
        if name is not None:
            self.name = name
        if type is not None:
            self.type = type

    @property
    def enable_public_network_access(self):
        """Gets the enable_public_network_access of this PortForCreateDevInstanceInput.  # noqa: E501


        :return: The enable_public_network_access of this PortForCreateDevInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_public_network_access

    @enable_public_network_access.setter
    def enable_public_network_access(self, enable_public_network_access):
        """Sets the enable_public_network_access of this PortForCreateDevInstanceInput.


        :param enable_public_network_access: The enable_public_network_access of this PortForCreateDevInstanceInput.  # noqa: E501
        :type: bool
        """

        self._enable_public_network_access = enable_public_network_access

    @property
    def external_port(self):
        """Gets the external_port of this PortForCreateDevInstanceInput.  # noqa: E501


        :return: The external_port of this PortForCreateDevInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._external_port

    @external_port.setter
    def external_port(self, external_port):
        """Sets the external_port of this PortForCreateDevInstanceInput.


        :param external_port: The external_port of this PortForCreateDevInstanceInput.  # noqa: E501
        :type: int
        """

        self._external_port = external_port

    @property
    def internal_port(self):
        """Gets the internal_port of this PortForCreateDevInstanceInput.  # noqa: E501


        :return: The internal_port of this PortForCreateDevInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._internal_port

    @internal_port.setter
    def internal_port(self, internal_port):
        """Sets the internal_port of this PortForCreateDevInstanceInput.


        :param internal_port: The internal_port of this PortForCreateDevInstanceInput.  # noqa: E501
        :type: int
        """

        self._internal_port = internal_port

    @property
    def name(self):
        """Gets the name of this PortForCreateDevInstanceInput.  # noqa: E501


        :return: The name of this PortForCreateDevInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this PortForCreateDevInstanceInput.


        :param name: The name of this PortForCreateDevInstanceInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def type(self):
        """Gets the type of this PortForCreateDevInstanceInput.  # noqa: E501


        :return: The type of this PortForCreateDevInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this PortForCreateDevInstanceInput.


        :param type: The type of this PortForCreateDevInstanceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["system", "custom"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PortForCreateDevInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PortForCreateDevInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PortForCreateDevInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
