# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeHotKeysResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hot_key': 'list[HotKeyForDescribeHotKeysOutput]',
        'instance_id': 'str',
        'total': 'int'
    }

    attribute_map = {
        'hot_key': 'HotKey',
        'instance_id': 'InstanceId',
        'total': 'Total'
    }

    def __init__(self, hot_key=None, instance_id=None, total=None, _configuration=None):  # noqa: E501
        """DescribeHotKeysResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hot_key = None
        self._instance_id = None
        self._total = None
        self.discriminator = None

        if hot_key is not None:
            self.hot_key = hot_key
        if instance_id is not None:
            self.instance_id = instance_id
        if total is not None:
            self.total = total

    @property
    def hot_key(self):
        """Gets the hot_key of this DescribeHotKeysResponse.  # noqa: E501


        :return: The hot_key of this DescribeHotKeysResponse.  # noqa: E501
        :rtype: list[HotKeyForDescribeHotKeysOutput]
        """
        return self._hot_key

    @hot_key.setter
    def hot_key(self, hot_key):
        """Sets the hot_key of this DescribeHotKeysResponse.


        :param hot_key: The hot_key of this DescribeHotKeysResponse.  # noqa: E501
        :type: list[HotKeyForDescribeHotKeysOutput]
        """

        self._hot_key = hot_key

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeHotKeysResponse.  # noqa: E501


        :return: The instance_id of this DescribeHotKeysResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeHotKeysResponse.


        :param instance_id: The instance_id of this DescribeHotKeysResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def total(self):
        """Gets the total of this DescribeHotKeysResponse.  # noqa: E501


        :return: The total of this DescribeHotKeysResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeHotKeysResponse.


        :param total: The total of this DescribeHotKeysResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeHotKeysResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeHotKeysResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeHotKeysResponse):
            return True

        return self.to_dict() != other.to_dict()
