# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConnectorConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'provider': 'str',
        'proxy_config': 'ProxyConfigForListClustersOutput',
        'type': 'str'
    }

    attribute_map = {
        'provider': 'Provider',
        'proxy_config': 'ProxyConfig',
        'type': 'Type'
    }

    def __init__(self, provider=None, proxy_config=None, type=None, _configuration=None):  # noqa: E501
        """ConnectorConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._provider = None
        self._proxy_config = None
        self._type = None
        self.discriminator = None

        if provider is not None:
            self.provider = provider
        if proxy_config is not None:
            self.proxy_config = proxy_config
        if type is not None:
            self.type = type

    @property
    def provider(self):
        """Gets the provider of this ConnectorConfigForListClustersOutput.  # noqa: E501


        :return: The provider of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider

    @provider.setter
    def provider(self, provider):
        """Sets the provider of this ConnectorConfigForListClustersOutput.


        :param provider: The provider of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._provider = provider

    @property
    def proxy_config(self):
        """Gets the proxy_config of this ConnectorConfigForListClustersOutput.  # noqa: E501


        :return: The proxy_config of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :rtype: ProxyConfigForListClustersOutput
        """
        return self._proxy_config

    @proxy_config.setter
    def proxy_config(self, proxy_config):
        """Sets the proxy_config of this ConnectorConfigForListClustersOutput.


        :param proxy_config: The proxy_config of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :type: ProxyConfigForListClustersOutput
        """

        self._proxy_config = proxy_config

    @property
    def type(self):
        """Gets the type of this ConnectorConfigForListClustersOutput.  # noqa: E501


        :return: The type of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ConnectorConfigForListClustersOutput.


        :param type: The type of this ConnectorConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConnectorConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConnectorConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConnectorConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
