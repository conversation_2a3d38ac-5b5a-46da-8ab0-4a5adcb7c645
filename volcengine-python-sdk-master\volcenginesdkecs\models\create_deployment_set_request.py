# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDeploymentSetRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'deployment_set_name': 'str',
        'description': 'str',
        'granularity': 'str',
        'group_count': 'int',
        'strategy': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'deployment_set_name': 'DeploymentSetName',
        'description': 'Description',
        'granularity': 'Granularity',
        'group_count': 'GroupCount',
        'strategy': 'Strategy'
    }

    def __init__(self, client_token=None, deployment_set_name=None, description=None, granularity=None, group_count=None, strategy=None, _configuration=None):  # noqa: E501
        """CreateDeploymentSetRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._deployment_set_name = None
        self._description = None
        self._granularity = None
        self._group_count = None
        self._strategy = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.deployment_set_name = deployment_set_name
        if description is not None:
            self.description = description
        if granularity is not None:
            self.granularity = granularity
        if group_count is not None:
            self.group_count = group_count
        if strategy is not None:
            self.strategy = strategy

    @property
    def client_token(self):
        """Gets the client_token of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The client_token of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateDeploymentSetRequest.


        :param client_token: The client_token of this CreateDeploymentSetRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def deployment_set_name(self):
        """Gets the deployment_set_name of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The deployment_set_name of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._deployment_set_name

    @deployment_set_name.setter
    def deployment_set_name(self, deployment_set_name):
        """Sets the deployment_set_name of this CreateDeploymentSetRequest.


        :param deployment_set_name: The deployment_set_name of this CreateDeploymentSetRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and deployment_set_name is None:
            raise ValueError("Invalid value for `deployment_set_name`, must not be `None`")  # noqa: E501

        self._deployment_set_name = deployment_set_name

    @property
    def description(self):
        """Gets the description of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The description of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDeploymentSetRequest.


        :param description: The description of this CreateDeploymentSetRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def granularity(self):
        """Gets the granularity of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The granularity of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._granularity

    @granularity.setter
    def granularity(self, granularity):
        """Sets the granularity of this CreateDeploymentSetRequest.


        :param granularity: The granularity of this CreateDeploymentSetRequest.  # noqa: E501
        :type: str
        """

        self._granularity = granularity

    @property
    def group_count(self):
        """Gets the group_count of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The group_count of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: int
        """
        return self._group_count

    @group_count.setter
    def group_count(self, group_count):
        """Sets the group_count of this CreateDeploymentSetRequest.


        :param group_count: The group_count of this CreateDeploymentSetRequest.  # noqa: E501
        :type: int
        """

        self._group_count = group_count

    @property
    def strategy(self):
        """Gets the strategy of this CreateDeploymentSetRequest.  # noqa: E501


        :return: The strategy of this CreateDeploymentSetRequest.  # noqa: E501
        :rtype: str
        """
        return self._strategy

    @strategy.setter
    def strategy(self, strategy):
        """Sets the strategy of this CreateDeploymentSetRequest.


        :param strategy: The strategy of this CreateDeploymentSetRequest.  # noqa: E501
        :type: str
        """

        self._strategy = strategy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDeploymentSetRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDeploymentSetRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDeploymentSetRequest):
            return True

        return self.to_dict() != other.to_dict()
