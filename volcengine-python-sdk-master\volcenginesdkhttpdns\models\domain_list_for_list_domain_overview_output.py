# coding: utf-8

"""
    httpdns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DomainListForListDomainOverviewOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'domain_name': 'str',
        'id': 'str',
        'number': 'int'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'domain_name': 'DomainName',
        'id': 'Id',
        'number': 'Number'
    }

    def __init__(self, create_time=None, domain_name=None, id=None, number=None, _configuration=None):  # noqa: E501
        """DomainListForListDomainOverviewOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._domain_name = None
        self._id = None
        self._number = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if domain_name is not None:
            self.domain_name = domain_name
        if id is not None:
            self.id = id
        if number is not None:
            self.number = number

    @property
    def create_time(self):
        """Gets the create_time of this DomainListForListDomainOverviewOutput.  # noqa: E501


        :return: The create_time of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DomainListForListDomainOverviewOutput.


        :param create_time: The create_time of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def domain_name(self):
        """Gets the domain_name of this DomainListForListDomainOverviewOutput.  # noqa: E501


        :return: The domain_name of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain_name

    @domain_name.setter
    def domain_name(self, domain_name):
        """Sets the domain_name of this DomainListForListDomainOverviewOutput.


        :param domain_name: The domain_name of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :type: str
        """

        self._domain_name = domain_name

    @property
    def id(self):
        """Gets the id of this DomainListForListDomainOverviewOutput.  # noqa: E501


        :return: The id of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DomainListForListDomainOverviewOutput.


        :param id: The id of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def number(self):
        """Gets the number of this DomainListForListDomainOverviewOutput.  # noqa: E501


        :return: The number of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._number

    @number.setter
    def number(self, number):
        """Sets the number of this DomainListForListDomainOverviewOutput.


        :param number: The number of this DomainListForListDomainOverviewOutput.  # noqa: E501
        :type: int
        """

        self._number = number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DomainListForListDomainOverviewOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DomainListForListDomainOverviewOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DomainListForListDomainOverviewOutput):
            return True

        return self.to_dict() != other.to_dict()
