# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotaAlarmRuleListForListQuotaAlarmRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_type': 'str',
        'created_time': 'str',
        'description': 'str',
        'dimensions': 'str',
        'enable_state': 'str',
        'metric_unit': 'str',
        'product_name': 'str',
        'provider_code': 'str',
        'quota_code': 'str',
        'quota_type': 'str',
        'rule_id': 'str',
        'rule_name': 'str',
        'silence_time': 'int',
        'statistics': 'str',
        'threshold': 'str',
        'updated_time': 'str'
    }

    attribute_map = {
        'alarm_type': 'AlarmType',
        'created_time': 'CreatedTime',
        'description': 'Description',
        'dimensions': 'Dimensions',
        'enable_state': 'EnableState',
        'metric_unit': 'MetricUnit',
        'product_name': 'ProductName',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode',
        'quota_type': 'QuotaType',
        'rule_id': 'RuleID',
        'rule_name': 'RuleName',
        'silence_time': 'SilenceTime',
        'statistics': 'Statistics',
        'threshold': 'Threshold',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, alarm_type=None, created_time=None, description=None, dimensions=None, enable_state=None, metric_unit=None, product_name=None, provider_code=None, quota_code=None, quota_type=None, rule_id=None, rule_name=None, silence_time=None, statistics=None, threshold=None, updated_time=None, _configuration=None):  # noqa: E501
        """QuotaAlarmRuleListForListQuotaAlarmRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_type = None
        self._created_time = None
        self._description = None
        self._dimensions = None
        self._enable_state = None
        self._metric_unit = None
        self._product_name = None
        self._provider_code = None
        self._quota_code = None
        self._quota_type = None
        self._rule_id = None
        self._rule_name = None
        self._silence_time = None
        self._statistics = None
        self._threshold = None
        self._updated_time = None
        self.discriminator = None

        if alarm_type is not None:
            self.alarm_type = alarm_type
        if created_time is not None:
            self.created_time = created_time
        if description is not None:
            self.description = description
        if dimensions is not None:
            self.dimensions = dimensions
        if enable_state is not None:
            self.enable_state = enable_state
        if metric_unit is not None:
            self.metric_unit = metric_unit
        if product_name is not None:
            self.product_name = product_name
        if provider_code is not None:
            self.provider_code = provider_code
        if quota_code is not None:
            self.quota_code = quota_code
        if quota_type is not None:
            self.quota_type = quota_type
        if rule_id is not None:
            self.rule_id = rule_id
        if rule_name is not None:
            self.rule_name = rule_name
        if silence_time is not None:
            self.silence_time = silence_time
        if statistics is not None:
            self.statistics = statistics
        if threshold is not None:
            self.threshold = threshold
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def alarm_type(self):
        """Gets the alarm_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The alarm_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param alarm_type: The alarm_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def created_time(self):
        """Gets the created_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The created_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param created_time: The created_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def description(self):
        """Gets the description of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The description of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param description: The description of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dimensions(self):
        """Gets the dimensions of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The dimensions of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param dimensions: The dimensions of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._dimensions = dimensions

    @property
    def enable_state(self):
        """Gets the enable_state of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The enable_state of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_state

    @enable_state.setter
    def enable_state(self, enable_state):
        """Sets the enable_state of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param enable_state: The enable_state of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._enable_state = enable_state

    @property
    def metric_unit(self):
        """Gets the metric_unit of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The metric_unit of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_unit

    @metric_unit.setter
    def metric_unit(self, metric_unit):
        """Sets the metric_unit of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param metric_unit: The metric_unit of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._metric_unit = metric_unit

    @property
    def product_name(self):
        """Gets the product_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The product_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param product_name: The product_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def provider_code(self):
        """Gets the provider_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The provider_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param provider_code: The provider_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The quota_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param quota_code: The quota_code of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._quota_code = quota_code

    @property
    def quota_type(self):
        """Gets the quota_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The quota_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param quota_type: The quota_type of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    @property
    def rule_id(self):
        """Gets the rule_id of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The rule_id of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param rule_id: The rule_id of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The rule_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param rule_name: The rule_name of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    @property
    def silence_time(self):
        """Gets the silence_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The silence_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param silence_time: The silence_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: int
        """

        self._silence_time = silence_time

    @property
    def statistics(self):
        """Gets the statistics of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The statistics of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._statistics

    @statistics.setter
    def statistics(self, statistics):
        """Sets the statistics of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param statistics: The statistics of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._statistics = statistics

    @property
    def threshold(self):
        """Gets the threshold of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The threshold of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param threshold: The threshold of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._threshold = threshold

    @property
    def updated_time(self):
        """Gets the updated_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501


        :return: The updated_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.


        :param updated_time: The updated_time of this QuotaAlarmRuleListForListQuotaAlarmRulesOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotaAlarmRuleListForListQuotaAlarmRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotaAlarmRuleListForListQuotaAlarmRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotaAlarmRuleListForListQuotaAlarmRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
