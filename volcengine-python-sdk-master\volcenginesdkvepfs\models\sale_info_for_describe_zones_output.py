# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SaleInfoForDescribeZonesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_type': 'str',
        'protocol_type': 'str',
        'region_id': 'str',
        'status': 'str',
        'store_type': 'str',
        'store_type_cn': 'str',
        'store_type_en': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'file_system_type': 'FileSystemType',
        'protocol_type': 'ProtocolType',
        'region_id': 'RegionId',
        'status': 'Status',
        'store_type': 'StoreType',
        'store_type_cn': 'StoreTypeCN',
        'store_type_en': 'StoreTypeEN',
        'zone_id': 'ZoneId'
    }

    def __init__(self, file_system_type=None, protocol_type=None, region_id=None, status=None, store_type=None, store_type_cn=None, store_type_en=None, zone_id=None, _configuration=None):  # noqa: E501
        """SaleInfoForDescribeZonesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_type = None
        self._protocol_type = None
        self._region_id = None
        self._status = None
        self._store_type = None
        self._store_type_cn = None
        self._store_type_en = None
        self._zone_id = None
        self.discriminator = None

        if file_system_type is not None:
            self.file_system_type = file_system_type
        if protocol_type is not None:
            self.protocol_type = protocol_type
        if region_id is not None:
            self.region_id = region_id
        if status is not None:
            self.status = status
        if store_type is not None:
            self.store_type = store_type
        if store_type_cn is not None:
            self.store_type_cn = store_type_cn
        if store_type_en is not None:
            self.store_type_en = store_type_en
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def file_system_type(self):
        """Gets the file_system_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The file_system_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_type

    @file_system_type.setter
    def file_system_type(self, file_system_type):
        """Sets the file_system_type of this SaleInfoForDescribeZonesOutput.


        :param file_system_type: The file_system_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_type = file_system_type

    @property
    def protocol_type(self):
        """Gets the protocol_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The protocol_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this SaleInfoForDescribeZonesOutput.


        :param protocol_type: The protocol_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._protocol_type = protocol_type

    @property
    def region_id(self):
        """Gets the region_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The region_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this SaleInfoForDescribeZonesOutput.


        :param region_id: The region_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def status(self):
        """Gets the status of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The status of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SaleInfoForDescribeZonesOutput.


        :param status: The status of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def store_type(self):
        """Gets the store_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The store_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type

    @store_type.setter
    def store_type(self, store_type):
        """Sets the store_type of this SaleInfoForDescribeZonesOutput.


        :param store_type: The store_type of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._store_type = store_type

    @property
    def store_type_cn(self):
        """Gets the store_type_cn of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The store_type_cn of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type_cn

    @store_type_cn.setter
    def store_type_cn(self, store_type_cn):
        """Sets the store_type_cn of this SaleInfoForDescribeZonesOutput.


        :param store_type_cn: The store_type_cn of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._store_type_cn = store_type_cn

    @property
    def store_type_en(self):
        """Gets the store_type_en of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The store_type_en of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type_en

    @store_type_en.setter
    def store_type_en(self, store_type_en):
        """Sets the store_type_en of this SaleInfoForDescribeZonesOutput.


        :param store_type_en: The store_type_en of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._store_type_en = store_type_en

    @property
    def zone_id(self):
        """Gets the zone_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501


        :return: The zone_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this SaleInfoForDescribeZonesOutput.


        :param zone_id: The zone_id of this SaleInfoForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SaleInfoForDescribeZonesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SaleInfoForDescribeZonesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SaleInfoForDescribeZonesOutput):
            return True

        return self.to_dict() != other.to_dict()
