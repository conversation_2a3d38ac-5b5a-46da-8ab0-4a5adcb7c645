# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAppInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'current': 'int',
        'records': 'list[object]',
        'size': 'int',
        'total': 'int'
    }

    attribute_map = {
        'current': 'Current',
        'records': 'Records',
        'size': 'Size',
        'total': 'Total'
    }

    def __init__(self, current=None, records=None, size=None, total=None, _configuration=None):  # noqa: E501
        """ListAppInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._current = None
        self._records = None
        self._size = None
        self._total = None
        self.discriminator = None

        if current is not None:
            self.current = current
        if records is not None:
            self.records = records
        if size is not None:
            self.size = size
        if total is not None:
            self.total = total

    @property
    def current(self):
        """Gets the current of this ListAppInstanceResponse.  # noqa: E501


        :return: The current of this ListAppInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._current

    @current.setter
    def current(self, current):
        """Sets the current of this ListAppInstanceResponse.


        :param current: The current of this ListAppInstanceResponse.  # noqa: E501
        :type: int
        """

        self._current = current

    @property
    def records(self):
        """Gets the records of this ListAppInstanceResponse.  # noqa: E501


        :return: The records of this ListAppInstanceResponse.  # noqa: E501
        :rtype: list[object]
        """
        return self._records

    @records.setter
    def records(self, records):
        """Sets the records of this ListAppInstanceResponse.


        :param records: The records of this ListAppInstanceResponse.  # noqa: E501
        :type: list[object]
        """

        self._records = records

    @property
    def size(self):
        """Gets the size of this ListAppInstanceResponse.  # noqa: E501


        :return: The size of this ListAppInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ListAppInstanceResponse.


        :param size: The size of this ListAppInstanceResponse.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def total(self):
        """Gets the total of this ListAppInstanceResponse.  # noqa: E501


        :return: The total of this ListAppInstanceResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListAppInstanceResponse.


        :param total: The total of this ListAppInstanceResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAppInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAppInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAppInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
