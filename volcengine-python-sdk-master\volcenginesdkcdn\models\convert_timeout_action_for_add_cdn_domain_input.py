# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertTimeoutActionForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'http_timeout': 'int',
        'tcp_timeout': 'int'
    }

    attribute_map = {
        'http_timeout': 'HttpTimeout',
        'tcp_timeout': 'TcpTimeout'
    }

    def __init__(self, http_timeout=None, tcp_timeout=None, _configuration=None):  # noqa: E501
        """ConvertTimeoutActionForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._http_timeout = None
        self._tcp_timeout = None
        self.discriminator = None

        if http_timeout is not None:
            self.http_timeout = http_timeout
        if tcp_timeout is not None:
            self.tcp_timeout = tcp_timeout

    @property
    def http_timeout(self):
        """Gets the http_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501


        :return: The http_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501
        :rtype: int
        """
        return self._http_timeout

    @http_timeout.setter
    def http_timeout(self, http_timeout):
        """Sets the http_timeout of this ConvertTimeoutActionForAddCdnDomainInput.


        :param http_timeout: The http_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501
        :type: int
        """

        self._http_timeout = http_timeout

    @property
    def tcp_timeout(self):
        """Gets the tcp_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501


        :return: The tcp_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_timeout

    @tcp_timeout.setter
    def tcp_timeout(self, tcp_timeout):
        """Sets the tcp_timeout of this ConvertTimeoutActionForAddCdnDomainInput.


        :param tcp_timeout: The tcp_timeout of this ConvertTimeoutActionForAddCdnDomainInput.  # noqa: E501
        :type: int
        """

        self._tcp_timeout = tcp_timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertTimeoutActionForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertTimeoutActionForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertTimeoutActionForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
