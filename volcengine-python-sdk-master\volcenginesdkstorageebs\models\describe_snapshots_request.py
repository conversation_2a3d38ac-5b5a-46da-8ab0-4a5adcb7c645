# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSnapshotsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter': 'list[FilterForDescribeSnapshotsInput]',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'snapshot_ids': 'list[str]',
        'snapshot_name': 'str',
        'snapshot_status': 'list[str]',
        'snapshot_types': 'list[str]',
        'volume_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'filter': 'Filter',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'snapshot_ids': 'SnapshotIds',
        'snapshot_name': 'SnapshotName',
        'snapshot_status': 'SnapshotStatus',
        'snapshot_types': 'SnapshotTypes',
        'volume_id': 'VolumeId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, filter=None, page_number=None, page_size=None, project_name=None, snapshot_ids=None, snapshot_name=None, snapshot_status=None, snapshot_types=None, volume_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeSnapshotsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._snapshot_ids = None
        self._snapshot_name = None
        self._snapshot_status = None
        self._snapshot_types = None
        self._volume_id = None
        self._zone_id = None
        self.discriminator = None

        if filter is not None:
            self.filter = filter
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if snapshot_ids is not None:
            self.snapshot_ids = snapshot_ids
        if snapshot_name is not None:
            self.snapshot_name = snapshot_name
        if snapshot_status is not None:
            self.snapshot_status = snapshot_status
        if snapshot_types is not None:
            self.snapshot_types = snapshot_types
        if volume_id is not None:
            self.volume_id = volume_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def filter(self):
        """Gets the filter of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The filter of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: list[FilterForDescribeSnapshotsInput]
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this DescribeSnapshotsRequest.


        :param filter: The filter of this DescribeSnapshotsRequest.  # noqa: E501
        :type: list[FilterForDescribeSnapshotsInput]
        """

        self._filter = filter

    @property
    def page_number(self):
        """Gets the page_number of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The page_number of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeSnapshotsRequest.


        :param page_number: The page_number of this DescribeSnapshotsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The page_size of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeSnapshotsRequest.


        :param page_size: The page_size of this DescribeSnapshotsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The project_name of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeSnapshotsRequest.


        :param project_name: The project_name of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def snapshot_ids(self):
        """Gets the snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._snapshot_ids

    @snapshot_ids.setter
    def snapshot_ids(self, snapshot_ids):
        """Sets the snapshot_ids of this DescribeSnapshotsRequest.


        :param snapshot_ids: The snapshot_ids of this DescribeSnapshotsRequest.  # noqa: E501
        :type: list[str]
        """

        self._snapshot_ids = snapshot_ids

    @property
    def snapshot_name(self):
        """Gets the snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_name

    @snapshot_name.setter
    def snapshot_name(self, snapshot_name):
        """Sets the snapshot_name of this DescribeSnapshotsRequest.


        :param snapshot_name: The snapshot_name of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._snapshot_name = snapshot_name

    @property
    def snapshot_status(self):
        """Gets the snapshot_status of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_status of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._snapshot_status

    @snapshot_status.setter
    def snapshot_status(self, snapshot_status):
        """Sets the snapshot_status of this DescribeSnapshotsRequest.


        :param snapshot_status: The snapshot_status of this DescribeSnapshotsRequest.  # noqa: E501
        :type: list[str]
        """

        self._snapshot_status = snapshot_status

    @property
    def snapshot_types(self):
        """Gets the snapshot_types of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The snapshot_types of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._snapshot_types

    @snapshot_types.setter
    def snapshot_types(self, snapshot_types):
        """Sets the snapshot_types of this DescribeSnapshotsRequest.


        :param snapshot_types: The snapshot_types of this DescribeSnapshotsRequest.  # noqa: E501
        :type: list[str]
        """

        self._snapshot_types = snapshot_types

    @property
    def volume_id(self):
        """Gets the volume_id of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The volume_id of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_id

    @volume_id.setter
    def volume_id(self, volume_id):
        """Sets the volume_id of this DescribeSnapshotsRequest.


        :param volume_id: The volume_id of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._volume_id = volume_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeSnapshotsRequest.  # noqa: E501


        :return: The zone_id of this DescribeSnapshotsRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeSnapshotsRequest.


        :param zone_id: The zone_id of this DescribeSnapshotsRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSnapshotsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSnapshotsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSnapshotsRequest):
            return True

        return self.to_dict() != other.to_dict()
