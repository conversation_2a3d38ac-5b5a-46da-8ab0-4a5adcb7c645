# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourceClaimOptionsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'preemptible': 'bool',
        'resource_queue_id': 'str',
        'support_status': 'str'
    }

    attribute_map = {
        'preemptible': 'Preemptible',
        'resource_queue_id': 'ResourceQueueId',
        'support_status': 'SupportStatus'
    }

    def __init__(self, preemptible=None, resource_queue_id=None, support_status=None, _configuration=None):  # noqa: E501
        """ListResourceClaimOptionsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._preemptible = None
        self._resource_queue_id = None
        self._support_status = None
        self.discriminator = None

        if preemptible is not None:
            self.preemptible = preemptible
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if support_status is not None:
            self.support_status = support_status

    @property
    def preemptible(self):
        """Gets the preemptible of this ListResourceClaimOptionsRequest.  # noqa: E501


        :return: The preemptible of this ListResourceClaimOptionsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._preemptible

    @preemptible.setter
    def preemptible(self, preemptible):
        """Sets the preemptible of this ListResourceClaimOptionsRequest.


        :param preemptible: The preemptible of this ListResourceClaimOptionsRequest.  # noqa: E501
        :type: bool
        """

        self._preemptible = preemptible

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this ListResourceClaimOptionsRequest.  # noqa: E501


        :return: The resource_queue_id of this ListResourceClaimOptionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this ListResourceClaimOptionsRequest.


        :param resource_queue_id: The resource_queue_id of this ListResourceClaimOptionsRequest.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def support_status(self):
        """Gets the support_status of this ListResourceClaimOptionsRequest.  # noqa: E501


        :return: The support_status of this ListResourceClaimOptionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._support_status

    @support_status.setter
    def support_status(self, support_status):
        """Sets the support_status of this ListResourceClaimOptionsRequest.


        :param support_status: The support_status of this ListResourceClaimOptionsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Valid", "Deprecated"]  # noqa: E501
        if (self._configuration.client_side_validation and
                support_status not in allowed_values):
            raise ValueError(
                "Invalid value for `support_status` ({0}), must be one of {1}"  # noqa: E501
                .format(support_status, allowed_values)
            )

        self._support_status = support_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourceClaimOptionsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourceClaimOptionsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourceClaimOptionsRequest):
            return True

        return self.to_dict() != other.to_dict()
