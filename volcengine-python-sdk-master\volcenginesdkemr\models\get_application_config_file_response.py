# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetApplicationConfigFileResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'description': 'str',
        'file_content': 'str',
        'file_group': 'str',
        'file_name': 'str',
        'file_path': 'str',
        'file_user': 'str',
        'support_custom_config_item': 'bool'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'description': 'Description',
        'file_content': 'FileContent',
        'file_group': 'FileGroup',
        'file_name': 'FileName',
        'file_path': 'FilePath',
        'file_user': 'FileUser',
        'support_custom_config_item': 'SupportCustomConfigItem'
    }

    def __init__(self, application_name=None, description=None, file_content=None, file_group=None, file_name=None, file_path=None, file_user=None, support_custom_config_item=None, _configuration=None):  # noqa: E501
        """GetApplicationConfigFileResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._description = None
        self._file_content = None
        self._file_group = None
        self._file_name = None
        self._file_path = None
        self._file_user = None
        self._support_custom_config_item = None
        self.discriminator = None

        if application_name is not None:
            self.application_name = application_name
        if description is not None:
            self.description = description
        if file_content is not None:
            self.file_content = file_content
        if file_group is not None:
            self.file_group = file_group
        if file_name is not None:
            self.file_name = file_name
        if file_path is not None:
            self.file_path = file_path
        if file_user is not None:
            self.file_user = file_user
        if support_custom_config_item is not None:
            self.support_custom_config_item = support_custom_config_item

    @property
    def application_name(self):
        """Gets the application_name of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The application_name of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this GetApplicationConfigFileResponse.


        :param application_name: The application_name of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def description(self):
        """Gets the description of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The description of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetApplicationConfigFileResponse.


        :param description: The description of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def file_content(self):
        """Gets the file_content of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The file_content of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_content

    @file_content.setter
    def file_content(self, file_content):
        """Sets the file_content of this GetApplicationConfigFileResponse.


        :param file_content: The file_content of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._file_content = file_content

    @property
    def file_group(self):
        """Gets the file_group of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The file_group of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_group

    @file_group.setter
    def file_group(self, file_group):
        """Sets the file_group of this GetApplicationConfigFileResponse.


        :param file_group: The file_group of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._file_group = file_group

    @property
    def file_name(self):
        """Gets the file_name of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The file_name of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this GetApplicationConfigFileResponse.


        :param file_name: The file_name of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def file_path(self):
        """Gets the file_path of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The file_path of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this GetApplicationConfigFileResponse.


        :param file_path: The file_path of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def file_user(self):
        """Gets the file_user of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The file_user of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_user

    @file_user.setter
    def file_user(self, file_user):
        """Sets the file_user of this GetApplicationConfigFileResponse.


        :param file_user: The file_user of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: str
        """

        self._file_user = file_user

    @property
    def support_custom_config_item(self):
        """Gets the support_custom_config_item of this GetApplicationConfigFileResponse.  # noqa: E501


        :return: The support_custom_config_item of this GetApplicationConfigFileResponse.  # noqa: E501
        :rtype: bool
        """
        return self._support_custom_config_item

    @support_custom_config_item.setter
    def support_custom_config_item(self, support_custom_config_item):
        """Sets the support_custom_config_item of this GetApplicationConfigFileResponse.


        :param support_custom_config_item: The support_custom_config_item of this GetApplicationConfigFileResponse.  # noqa: E501
        :type: bool
        """

        self._support_custom_config_item = support_custom_config_item

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetApplicationConfigFileResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetApplicationConfigFileResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetApplicationConfigFileResponse):
            return True

        return self.to_dict() != other.to_dict()
