# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiagnoseConfigForUpdateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detection_interval_seconds': 'int',
        'first_detection_wait_seconds': 'int',
        'name': 'str',
        'triggers': 'list[str]'
    }

    attribute_map = {
        'detection_interval_seconds': 'DetectionIntervalSeconds',
        'first_detection_wait_seconds': 'FirstDetectionWaitSeconds',
        'name': 'Name',
        'triggers': 'Triggers'
    }

    def __init__(self, detection_interval_seconds=None, first_detection_wait_seconds=None, name=None, triggers=None, _configuration=None):  # noqa: E501
        """DiagnoseConfigForUpdateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detection_interval_seconds = None
        self._first_detection_wait_seconds = None
        self._name = None
        self._triggers = None
        self.discriminator = None

        if detection_interval_seconds is not None:
            self.detection_interval_seconds = detection_interval_seconds
        if first_detection_wait_seconds is not None:
            self.first_detection_wait_seconds = first_detection_wait_seconds
        if name is not None:
            self.name = name
        if triggers is not None:
            self.triggers = triggers

    @property
    def detection_interval_seconds(self):
        """Gets the detection_interval_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501


        :return: The detection_interval_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._detection_interval_seconds

    @detection_interval_seconds.setter
    def detection_interval_seconds(self, detection_interval_seconds):
        """Sets the detection_interval_seconds of this DiagnoseConfigForUpdateJobInput.


        :param detection_interval_seconds: The detection_interval_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._detection_interval_seconds = detection_interval_seconds

    @property
    def first_detection_wait_seconds(self):
        """Gets the first_detection_wait_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501


        :return: The first_detection_wait_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._first_detection_wait_seconds

    @first_detection_wait_seconds.setter
    def first_detection_wait_seconds(self, first_detection_wait_seconds):
        """Sets the first_detection_wait_seconds of this DiagnoseConfigForUpdateJobInput.


        :param first_detection_wait_seconds: The first_detection_wait_seconds of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._first_detection_wait_seconds = first_detection_wait_seconds

    @property
    def name(self):
        """Gets the name of this DiagnoseConfigForUpdateJobInput.  # noqa: E501


        :return: The name of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DiagnoseConfigForUpdateJobInput.


        :param name: The name of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["EnvironmentalDiagnosis", "PythonDetection", "LogDetection"]  # noqa: E501
        if (self._configuration.client_side_validation and
                name not in allowed_values):
            raise ValueError(
                "Invalid value for `name` ({0}), must be one of {1}"  # noqa: E501
                .format(name, allowed_values)
            )

        self._name = name

    @property
    def triggers(self):
        """Gets the triggers of this DiagnoseConfigForUpdateJobInput.  # noqa: E501


        :return: The triggers of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._triggers

    @triggers.setter
    def triggers(self, triggers):
        """Sets the triggers of this DiagnoseConfigForUpdateJobInput.


        :param triggers: The triggers of this DiagnoseConfigForUpdateJobInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["BeforeStart", "JobRunning", "JobFailed", "StopByUser"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(triggers).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `triggers` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(triggers) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._triggers = triggers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiagnoseConfigForUpdateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiagnoseConfigForUpdateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiagnoseConfigForUpdateJobInput):
            return True

        return self.to_dict() != other.to_dict()
