# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'answer': 'str',
        'question': 'str',
        'question_id': 'int'
    }

    attribute_map = {
        'answer': 'Answer',
        'question': 'Question',
        'question_id': 'QuestionId'
    }

    def __init__(self, answer=None, question=None, question_id=None, _configuration=None):  # noqa: E501
        """QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._answer = None
        self._question = None
        self._question_id = None
        self.discriminator = None

        if answer is not None:
            self.answer = answer
        if question is not None:
            self.question = question
        if question_id is not None:
            self.question_id = question_id

    @property
    def answer(self):
        """Gets the answer of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The answer of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._answer

    @answer.setter
    def answer(self, answer):
        """Sets the answer of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.


        :param answer: The answer of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._answer = answer

    @property
    def question(self):
        """Gets the question of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._question

    @question.setter
    def question(self, question):
        """Sets the question of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.


        :param question: The question of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._question = question

    @property
    def question_id(self):
        """Gets the question_id of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question_id of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._question_id

    @question_id.setter
    def question_id(self, question_id):
        """Sets the question_id of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.


        :param question_id: The question_id of this QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._question_id = question_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
