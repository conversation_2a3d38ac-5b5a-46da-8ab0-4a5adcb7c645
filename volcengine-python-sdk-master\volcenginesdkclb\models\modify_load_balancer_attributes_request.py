# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyLoadBalancerAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address_ip_version': 'str',
        'allowed_ports': 'list[str]',
        'bypass_security_group_enabled': 'str',
        'description': 'str',
        'eni_ipv6_address': 'str',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'load_balancer_spec': 'str',
        'modification_protection_reason': 'str',
        'modification_protection_status': 'str',
        'type': 'str'
    }

    attribute_map = {
        'address_ip_version': 'AddressIpVersion',
        'allowed_ports': 'AllowedPorts',
        'bypass_security_group_enabled': 'BypassSecurityGroupEnabled',
        'description': 'Description',
        'eni_ipv6_address': 'EniIpv6Address',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_name': 'LoadBalancerName',
        'load_balancer_spec': 'LoadBalancerSpec',
        'modification_protection_reason': 'ModificationProtectionReason',
        'modification_protection_status': 'ModificationProtectionStatus',
        'type': 'Type'
    }

    def __init__(self, address_ip_version=None, allowed_ports=None, bypass_security_group_enabled=None, description=None, eni_ipv6_address=None, load_balancer_id=None, load_balancer_name=None, load_balancer_spec=None, modification_protection_reason=None, modification_protection_status=None, type=None, _configuration=None):  # noqa: E501
        """ModifyLoadBalancerAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address_ip_version = None
        self._allowed_ports = None
        self._bypass_security_group_enabled = None
        self._description = None
        self._eni_ipv6_address = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._load_balancer_spec = None
        self._modification_protection_reason = None
        self._modification_protection_status = None
        self._type = None
        self.discriminator = None

        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if allowed_ports is not None:
            self.allowed_ports = allowed_ports
        if bypass_security_group_enabled is not None:
            self.bypass_security_group_enabled = bypass_security_group_enabled
        if description is not None:
            self.description = description
        if eni_ipv6_address is not None:
            self.eni_ipv6_address = eni_ipv6_address
        self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if load_balancer_spec is not None:
            self.load_balancer_spec = load_balancer_spec
        if modification_protection_reason is not None:
            self.modification_protection_reason = modification_protection_reason
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status
        if type is not None:
            self.type = type

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The address_ip_version of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this ModifyLoadBalancerAttributesRequest.


        :param address_ip_version: The address_ip_version of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def allowed_ports(self):
        """Gets the allowed_ports of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The allowed_ports of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_ports

    @allowed_ports.setter
    def allowed_ports(self, allowed_ports):
        """Sets the allowed_ports of this ModifyLoadBalancerAttributesRequest.


        :param allowed_ports: The allowed_ports of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: list[str]
        """

        self._allowed_ports = allowed_ports

    @property
    def bypass_security_group_enabled(self):
        """Gets the bypass_security_group_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The bypass_security_group_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._bypass_security_group_enabled

    @bypass_security_group_enabled.setter
    def bypass_security_group_enabled(self, bypass_security_group_enabled):
        """Sets the bypass_security_group_enabled of this ModifyLoadBalancerAttributesRequest.


        :param bypass_security_group_enabled: The bypass_security_group_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._bypass_security_group_enabled = bypass_security_group_enabled

    @property
    def description(self):
        """Gets the description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyLoadBalancerAttributesRequest.


        :param description: The description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def eni_ipv6_address(self):
        """Gets the eni_ipv6_address of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The eni_ipv6_address of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._eni_ipv6_address

    @eni_ipv6_address.setter
    def eni_ipv6_address(self, eni_ipv6_address):
        """Sets the eni_ipv6_address of this ModifyLoadBalancerAttributesRequest.


        :param eni_ipv6_address: The eni_ipv6_address of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._eni_ipv6_address = eni_ipv6_address

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this ModifyLoadBalancerAttributesRequest.


        :param load_balancer_id: The load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this ModifyLoadBalancerAttributesRequest.


        :param load_balancer_name: The load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def load_balancer_spec(self):
        """Gets the load_balancer_spec of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_spec of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_spec

    @load_balancer_spec.setter
    def load_balancer_spec(self, load_balancer_spec):
        """Sets the load_balancer_spec of this ModifyLoadBalancerAttributesRequest.


        :param load_balancer_spec: The load_balancer_spec of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_spec = load_balancer_spec

    @property
    def modification_protection_reason(self):
        """Gets the modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_reason

    @modification_protection_reason.setter
    def modification_protection_reason(self, modification_protection_reason):
        """Sets the modification_protection_reason of this ModifyLoadBalancerAttributesRequest.


        :param modification_protection_reason: The modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_reason = modification_protection_reason

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this ModifyLoadBalancerAttributesRequest.


        :param modification_protection_status: The modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    @property
    def type(self):
        """Gets the type of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The type of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ModifyLoadBalancerAttributesRequest.


        :param type: The type of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyLoadBalancerAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyLoadBalancerAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyLoadBalancerAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
