# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImportKeyPairResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'finger_print': 'str',
        'key_pair_id': 'str',
        'key_pair_name': 'str'
    }

    attribute_map = {
        'finger_print': 'FingerPrint',
        'key_pair_id': 'KeyPairId',
        'key_pair_name': 'KeyPairName'
    }

    def __init__(self, finger_print=None, key_pair_id=None, key_pair_name=None, _configuration=None):  # noqa: E501
        """ImportKeyPairResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._finger_print = None
        self._key_pair_id = None
        self._key_pair_name = None
        self.discriminator = None

        if finger_print is not None:
            self.finger_print = finger_print
        if key_pair_id is not None:
            self.key_pair_id = key_pair_id
        if key_pair_name is not None:
            self.key_pair_name = key_pair_name

    @property
    def finger_print(self):
        """Gets the finger_print of this ImportKeyPairResponse.  # noqa: E501


        :return: The finger_print of this ImportKeyPairResponse.  # noqa: E501
        :rtype: str
        """
        return self._finger_print

    @finger_print.setter
    def finger_print(self, finger_print):
        """Sets the finger_print of this ImportKeyPairResponse.


        :param finger_print: The finger_print of this ImportKeyPairResponse.  # noqa: E501
        :type: str
        """

        self._finger_print = finger_print

    @property
    def key_pair_id(self):
        """Gets the key_pair_id of this ImportKeyPairResponse.  # noqa: E501


        :return: The key_pair_id of this ImportKeyPairResponse.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_id

    @key_pair_id.setter
    def key_pair_id(self, key_pair_id):
        """Sets the key_pair_id of this ImportKeyPairResponse.


        :param key_pair_id: The key_pair_id of this ImportKeyPairResponse.  # noqa: E501
        :type: str
        """

        self._key_pair_id = key_pair_id

    @property
    def key_pair_name(self):
        """Gets the key_pair_name of this ImportKeyPairResponse.  # noqa: E501


        :return: The key_pair_name of this ImportKeyPairResponse.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_name

    @key_pair_name.setter
    def key_pair_name(self, key_pair_name):
        """Sets the key_pair_name of this ImportKeyPairResponse.


        :param key_pair_name: The key_pair_name of this ImportKeyPairResponse.  # noqa: E501
        :type: str
        """

        self._key_pair_name = key_pair_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImportKeyPairResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImportKeyPairResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImportKeyPairResponse):
            return True

        return self.to_dict() != other.to_dict()
