# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResultListForGetHidsAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'argv': 'str',
        'attr': 'str',
        'comm': 'str',
        'create_at': 'str',
        'cron': 'str',
        'data': 'str',
        'data_type': 'str',
        'exe': 'str',
        'exe_hash': 'str',
        'exe_size': 'str',
        'file_path': 'str',
        'ip': 'str',
        'keywords': 'str',
        'line_number': 'str',
        'login_at': 'str',
        'logout_at': 'str',
        'matches': 'str',
        'md5sum': 'str',
        'modify_at': 'str',
        'name': 'str',
        'permission': 'str',
        'pgid': 'str',
        'pid': 'int',
        'port': 'str',
        'ppid': 'str',
        'restart': 'str',
        'sha256sum': 'str',
        'stype': 'str',
        'trace_id': 'str',
        'types': 'str',
        'user': 'str'
    }

    attribute_map = {
        'argv': 'Argv',
        'attr': 'Attr',
        'comm': 'Comm',
        'create_at': 'CreateAt',
        'cron': 'Cron',
        'data': 'Data',
        'data_type': 'DataType',
        'exe': 'Exe',
        'exe_hash': 'ExeHash',
        'exe_size': 'ExeSize',
        'file_path': 'FilePath',
        'ip': 'IP',
        'keywords': 'Keywords',
        'line_number': 'LineNumber',
        'login_at': 'LoginAt',
        'logout_at': 'LogoutAt',
        'matches': 'Matches',
        'md5sum': 'Md5sum',
        'modify_at': 'ModifyAt',
        'name': 'Name',
        'permission': 'Permission',
        'pgid': 'Pgid',
        'pid': 'Pid',
        'port': 'Port',
        'ppid': 'Ppid',
        'restart': 'Restart',
        'sha256sum': 'Sha256sum',
        'stype': 'Stype',
        'trace_id': 'TraceID',
        'types': 'Types',
        'user': 'User'
    }

    def __init__(self, argv=None, attr=None, comm=None, create_at=None, cron=None, data=None, data_type=None, exe=None, exe_hash=None, exe_size=None, file_path=None, ip=None, keywords=None, line_number=None, login_at=None, logout_at=None, matches=None, md5sum=None, modify_at=None, name=None, permission=None, pgid=None, pid=None, port=None, ppid=None, restart=None, sha256sum=None, stype=None, trace_id=None, types=None, user=None, _configuration=None):  # noqa: E501
        """ResultListForGetHidsAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._argv = None
        self._attr = None
        self._comm = None
        self._create_at = None
        self._cron = None
        self._data = None
        self._data_type = None
        self._exe = None
        self._exe_hash = None
        self._exe_size = None
        self._file_path = None
        self._ip = None
        self._keywords = None
        self._line_number = None
        self._login_at = None
        self._logout_at = None
        self._matches = None
        self._md5sum = None
        self._modify_at = None
        self._name = None
        self._permission = None
        self._pgid = None
        self._pid = None
        self._port = None
        self._ppid = None
        self._restart = None
        self._sha256sum = None
        self._stype = None
        self._trace_id = None
        self._types = None
        self._user = None
        self.discriminator = None

        if argv is not None:
            self.argv = argv
        if attr is not None:
            self.attr = attr
        if comm is not None:
            self.comm = comm
        if create_at is not None:
            self.create_at = create_at
        if cron is not None:
            self.cron = cron
        if data is not None:
            self.data = data
        if data_type is not None:
            self.data_type = data_type
        if exe is not None:
            self.exe = exe
        if exe_hash is not None:
            self.exe_hash = exe_hash
        if exe_size is not None:
            self.exe_size = exe_size
        if file_path is not None:
            self.file_path = file_path
        if ip is not None:
            self.ip = ip
        if keywords is not None:
            self.keywords = keywords
        if line_number is not None:
            self.line_number = line_number
        if login_at is not None:
            self.login_at = login_at
        if logout_at is not None:
            self.logout_at = logout_at
        if matches is not None:
            self.matches = matches
        if md5sum is not None:
            self.md5sum = md5sum
        if modify_at is not None:
            self.modify_at = modify_at
        if name is not None:
            self.name = name
        if permission is not None:
            self.permission = permission
        if pgid is not None:
            self.pgid = pgid
        if pid is not None:
            self.pid = pid
        if port is not None:
            self.port = port
        if ppid is not None:
            self.ppid = ppid
        if restart is not None:
            self.restart = restart
        if sha256sum is not None:
            self.sha256sum = sha256sum
        if stype is not None:
            self.stype = stype
        if trace_id is not None:
            self.trace_id = trace_id
        if types is not None:
            self.types = types
        if user is not None:
            self.user = user

    @property
    def argv(self):
        """Gets the argv of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The argv of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._argv

    @argv.setter
    def argv(self, argv):
        """Sets the argv of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param argv: The argv of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._argv = argv

    @property
    def attr(self):
        """Gets the attr of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The attr of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._attr

    @attr.setter
    def attr(self, attr):
        """Sets the attr of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param attr: The attr of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._attr = attr

    @property
    def comm(self):
        """Gets the comm of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The comm of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param comm: The comm of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def create_at(self):
        """Gets the create_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The create_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param create_at: The create_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._create_at = create_at

    @property
    def cron(self):
        """Gets the cron of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cron of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cron

    @cron.setter
    def cron(self, cron):
        """Sets the cron of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param cron: The cron of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._cron = cron

    @property
    def data(self):
        """Gets the data of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The data of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param data: The data of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._data = data

    @property
    def data_type(self):
        """Gets the data_type of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The data_type of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param data_type: The data_type of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def exe(self):
        """Gets the exe of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exe of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param exe: The exe of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def exe_hash(self):
        """Gets the exe_hash of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exe_hash of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe_hash

    @exe_hash.setter
    def exe_hash(self, exe_hash):
        """Sets the exe_hash of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param exe_hash: The exe_hash of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exe_hash = exe_hash

    @property
    def exe_size(self):
        """Gets the exe_size of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exe_size of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe_size

    @exe_size.setter
    def exe_size(self, exe_size):
        """Sets the exe_size of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param exe_size: The exe_size of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exe_size = exe_size

    @property
    def file_path(self):
        """Gets the file_path of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The file_path of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param file_path: The file_path of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def ip(self):
        """Gets the ip of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ip of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param ip: The ip of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def keywords(self):
        """Gets the keywords of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The keywords of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._keywords

    @keywords.setter
    def keywords(self, keywords):
        """Sets the keywords of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param keywords: The keywords of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._keywords = keywords

    @property
    def line_number(self):
        """Gets the line_number of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The line_number of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._line_number

    @line_number.setter
    def line_number(self, line_number):
        """Sets the line_number of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param line_number: The line_number of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._line_number = line_number

    @property
    def login_at(self):
        """Gets the login_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The login_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._login_at

    @login_at.setter
    def login_at(self, login_at):
        """Sets the login_at of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param login_at: The login_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._login_at = login_at

    @property
    def logout_at(self):
        """Gets the logout_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The logout_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._logout_at

    @logout_at.setter
    def logout_at(self, logout_at):
        """Sets the logout_at of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param logout_at: The logout_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._logout_at = logout_at

    @property
    def matches(self):
        """Gets the matches of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The matches of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._matches

    @matches.setter
    def matches(self, matches):
        """Sets the matches of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param matches: The matches of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._matches = matches

    @property
    def md5sum(self):
        """Gets the md5sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The md5sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._md5sum

    @md5sum.setter
    def md5sum(self, md5sum):
        """Sets the md5sum of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param md5sum: The md5sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._md5sum = md5sum

    @property
    def modify_at(self):
        """Gets the modify_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The modify_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._modify_at

    @modify_at.setter
    def modify_at(self, modify_at):
        """Sets the modify_at of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param modify_at: The modify_at of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._modify_at = modify_at

    @property
    def name(self):
        """Gets the name of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The name of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param name: The name of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def permission(self):
        """Gets the permission of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The permission of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._permission

    @permission.setter
    def permission(self, permission):
        """Sets the permission of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param permission: The permission of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._permission = permission

    @property
    def pgid(self):
        """Gets the pgid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pgid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pgid

    @pgid.setter
    def pgid(self, pgid):
        """Sets the pgid of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param pgid: The pgid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pgid = pgid

    @property
    def pid(self):
        """Gets the pid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param pid: The pid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._pid = pid

    @property
    def port(self):
        """Gets the port of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The port of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param port: The port of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._port = port

    @property
    def ppid(self):
        """Gets the ppid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ppid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ppid

    @ppid.setter
    def ppid(self, ppid):
        """Sets the ppid of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param ppid: The ppid of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ppid = ppid

    @property
    def restart(self):
        """Gets the restart of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The restart of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._restart

    @restart.setter
    def restart(self, restart):
        """Sets the restart of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param restart: The restart of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._restart = restart

    @property
    def sha256sum(self):
        """Gets the sha256sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The sha256sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sha256sum

    @sha256sum.setter
    def sha256sum(self, sha256sum):
        """Sets the sha256sum of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param sha256sum: The sha256sum of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._sha256sum = sha256sum

    @property
    def stype(self):
        """Gets the stype of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The stype of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stype

    @stype.setter
    def stype(self, stype):
        """Sets the stype of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param stype: The stype of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._stype = stype

    @property
    def trace_id(self):
        """Gets the trace_id of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The trace_id of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param trace_id: The trace_id of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    @property
    def types(self):
        """Gets the types of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The types of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param types: The types of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._types = types

    @property
    def user(self):
        """Gets the user of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this ResultListForGetHidsAlarmSummaryInfoOutput.


        :param user: The user of this ResultListForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResultListForGetHidsAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResultListForGetHidsAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResultListForGetHidsAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
