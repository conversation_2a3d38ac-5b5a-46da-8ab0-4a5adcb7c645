# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ManualRenewReservedStorageCapacityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'effective_at': 'str',
        'period': 'int',
        'period_unit': 'str',
        'rscid': 'str'
    }

    attribute_map = {
        'effective_at': 'EffectiveAt',
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'rscid': 'RSCId'
    }

    def __init__(self, effective_at=None, period=None, period_unit=None, rscid=None, _configuration=None):  # noqa: E501
        """ManualRenewReservedStorageCapacityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._effective_at = None
        self._period = None
        self._period_unit = None
        self._rscid = None
        self.discriminator = None

        if effective_at is not None:
            self.effective_at = effective_at
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if rscid is not None:
            self.rscid = rscid

    @property
    def effective_at(self):
        """Gets the effective_at of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The effective_at of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: str
        """
        return self._effective_at

    @effective_at.setter
    def effective_at(self, effective_at):
        """Sets the effective_at of this ManualRenewReservedStorageCapacityRequest.


        :param effective_at: The effective_at of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: str
        """

        self._effective_at = effective_at

    @property
    def period(self):
        """Gets the period of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The period of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this ManualRenewReservedStorageCapacityRequest.


        :param period: The period of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The period_unit of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this ManualRenewReservedStorageCapacityRequest.


        :param period_unit: The period_unit of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def rscid(self):
        """Gets the rscid of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501


        :return: The rscid of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :rtype: str
        """
        return self._rscid

    @rscid.setter
    def rscid(self, rscid):
        """Sets the rscid of this ManualRenewReservedStorageCapacityRequest.


        :param rscid: The rscid of this ManualRenewReservedStorageCapacityRequest.  # noqa: E501
        :type: str
        """

        self._rscid = rscid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ManualRenewReservedStorageCapacityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ManualRenewReservedStorageCapacityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ManualRenewReservedStorageCapacityRequest):
            return True

        return self.to_dict() != other.to_dict()
