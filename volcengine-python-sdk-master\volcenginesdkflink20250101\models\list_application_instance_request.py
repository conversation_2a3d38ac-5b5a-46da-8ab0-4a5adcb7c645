# coding: utf-8

"""
    flink20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListApplicationInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'job_id': 'int',
        'job_name': 'str',
        'page_num': 'str',
        'page_size': 'str',
        'project_id': 'str',
        'resource_pool': 'str',
        'sort_field': 'str',
        'sort_order': 'str',
        'state': 'str'
    }

    attribute_map = {
        'job_id': 'JobId',
        'job_name': 'JobName',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_id': 'ProjectId',
        'resource_pool': 'ResourcePool',
        'sort_field': 'SortField',
        'sort_order': 'SortOrder',
        'state': 'State'
    }

    def __init__(self, job_id=None, job_name=None, page_num=None, page_size=None, project_id=None, resource_pool=None, sort_field=None, sort_order=None, state=None, _configuration=None):  # noqa: E501
        """ListApplicationInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._job_id = None
        self._job_name = None
        self._page_num = None
        self._page_size = None
        self._project_id = None
        self._resource_pool = None
        self._sort_field = None
        self._sort_order = None
        self._state = None
        self.discriminator = None

        self.job_id = job_id
        if job_name is not None:
            self.job_name = job_name
        self.page_num = page_num
        self.page_size = page_size
        self.project_id = project_id
        if resource_pool is not None:
            self.resource_pool = resource_pool
        if sort_field is not None:
            self.sort_field = sort_field
        if sort_order is not None:
            self.sort_order = sort_order
        self.state = state

    @property
    def job_id(self):
        """Gets the job_id of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The job_id of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._job_id

    @job_id.setter
    def job_id(self, job_id):
        """Sets the job_id of this ListApplicationInstanceRequest.


        :param job_id: The job_id of this ListApplicationInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and job_id is None:
            raise ValueError("Invalid value for `job_id`, must not be `None`")  # noqa: E501

        self._job_id = job_id

    @property
    def job_name(self):
        """Gets the job_name of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The job_name of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._job_name

    @job_name.setter
    def job_name(self, job_name):
        """Sets the job_name of this ListApplicationInstanceRequest.


        :param job_name: The job_name of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._job_name = job_name

    @property
    def page_num(self):
        """Gets the page_num of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The page_num of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListApplicationInstanceRequest.


        :param page_num: The page_num of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and page_num is None:
            raise ValueError("Invalid value for `page_num`, must not be `None`")  # noqa: E501

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The page_size of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListApplicationInstanceRequest.


        :param page_size: The page_size of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_id(self):
        """Gets the project_id of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The project_id of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this ListApplicationInstanceRequest.


        :param project_id: The project_id of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_id is None:
            raise ValueError("Invalid value for `project_id`, must not be `None`")  # noqa: E501

        self._project_id = project_id

    @property
    def resource_pool(self):
        """Gets the resource_pool of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The resource_pool of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_pool

    @resource_pool.setter
    def resource_pool(self, resource_pool):
        """Sets the resource_pool of this ListApplicationInstanceRequest.


        :param resource_pool: The resource_pool of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._resource_pool = resource_pool

    @property
    def sort_field(self):
        """Gets the sort_field of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The sort_field of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_field

    @sort_field.setter
    def sort_field(self, sort_field):
        """Sets the sort_field of this ListApplicationInstanceRequest.


        :param sort_field: The sort_field of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._sort_field = sort_field

    @property
    def sort_order(self):
        """Gets the sort_order of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The sort_order of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListApplicationInstanceRequest.


        :param sort_order: The sort_order of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def state(self):
        """Gets the state of this ListApplicationInstanceRequest.  # noqa: E501


        :return: The state of this ListApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListApplicationInstanceRequest.


        :param state: The state of this ListApplicationInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and state is None:
            raise ValueError("Invalid value for `state`, must not be `None`")  # noqa: E501

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListApplicationInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListApplicationInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListApplicationInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
