# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetRegularVirusScanConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'action': 'str',
        'agent_id_list': 'list[str]',
        'all_host': 'bool',
        'cloud_providers': 'list[str]',
        'cpu_i_dle': 'str',
        'file_path': 'str',
        'leaf_group_ids': 'list[str]',
        'operate_user': 'str',
        'scan_interval': 'int',
        'scan_switch_on': 'bool',
        'task_end_time': 'str',
        'task_start_time': 'str',
        'timeout': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'action': 'Action',
        'agent_id_list': 'AgentIDList',
        'all_host': 'AllHost',
        'cloud_providers': 'CloudProviders',
        'cpu_i_dle': 'CpuIDle',
        'file_path': 'FilePath',
        'leaf_group_ids': 'LeafGroupIDs',
        'operate_user': 'OperateUser',
        'scan_interval': 'ScanInterval',
        'scan_switch_on': 'ScanSwitchOn',
        'task_end_time': 'TaskEndTime',
        'task_start_time': 'TaskStartTime',
        'timeout': 'Timeout',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, account_id=None, action=None, agent_id_list=None, all_host=None, cloud_providers=None, cpu_i_dle=None, file_path=None, leaf_group_ids=None, operate_user=None, scan_interval=None, scan_switch_on=None, task_end_time=None, task_start_time=None, timeout=None, top_group_id=None, _configuration=None):  # noqa: E501
        """SetRegularVirusScanConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._action = None
        self._agent_id_list = None
        self._all_host = None
        self._cloud_providers = None
        self._cpu_i_dle = None
        self._file_path = None
        self._leaf_group_ids = None
        self._operate_user = None
        self._scan_interval = None
        self._scan_switch_on = None
        self._task_end_time = None
        self._task_start_time = None
        self._timeout = None
        self._top_group_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        self.action = action
        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if all_host is not None:
            self.all_host = all_host
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cpu_i_dle is not None:
            self.cpu_i_dle = cpu_i_dle
        if file_path is not None:
            self.file_path = file_path
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if operate_user is not None:
            self.operate_user = operate_user
        if scan_interval is not None:
            self.scan_interval = scan_interval
        if scan_switch_on is not None:
            self.scan_switch_on = scan_switch_on
        if task_end_time is not None:
            self.task_end_time = task_end_time
        if task_start_time is not None:
            self.task_start_time = task_start_time
        if timeout is not None:
            self.timeout = timeout
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def account_id(self):
        """Gets the account_id of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The account_id of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this SetRegularVirusScanConfigRequest.


        :param account_id: The account_id of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def action(self):
        """Gets the action of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The action of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this SetRegularVirusScanConfigRequest.


        :param action: The action of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and action is None:
            raise ValueError("Invalid value for `action`, must not be `None`")  # noqa: E501

        self._action = action

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The agent_id_list of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this SetRegularVirusScanConfigRequest.


        :param agent_id_list: The agent_id_list of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def all_host(self):
        """Gets the all_host of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The all_host of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_host

    @all_host.setter
    def all_host(self, all_host):
        """Sets the all_host of this SetRegularVirusScanConfigRequest.


        :param all_host: The all_host of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: bool
        """

        self._all_host = all_host

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The cloud_providers of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this SetRegularVirusScanConfigRequest.


        :param cloud_providers: The cloud_providers of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cpu_i_dle(self):
        """Gets the cpu_i_dle of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The cpu_i_dle of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._cpu_i_dle

    @cpu_i_dle.setter
    def cpu_i_dle(self, cpu_i_dle):
        """Sets the cpu_i_dle of this SetRegularVirusScanConfigRequest.


        :param cpu_i_dle: The cpu_i_dle of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._cpu_i_dle = cpu_i_dle

    @property
    def file_path(self):
        """Gets the file_path of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The file_path of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this SetRegularVirusScanConfigRequest.


        :param file_path: The file_path of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The leaf_group_ids of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this SetRegularVirusScanConfigRequest.


        :param leaf_group_ids: The leaf_group_ids of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def operate_user(self):
        """Gets the operate_user of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The operate_user of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._operate_user

    @operate_user.setter
    def operate_user(self, operate_user):
        """Sets the operate_user of this SetRegularVirusScanConfigRequest.


        :param operate_user: The operate_user of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._operate_user = operate_user

    @property
    def scan_interval(self):
        """Gets the scan_interval of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The scan_interval of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._scan_interval

    @scan_interval.setter
    def scan_interval(self, scan_interval):
        """Sets the scan_interval of this SetRegularVirusScanConfigRequest.


        :param scan_interval: The scan_interval of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: int
        """

        self._scan_interval = scan_interval

    @property
    def scan_switch_on(self):
        """Gets the scan_switch_on of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The scan_switch_on of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._scan_switch_on

    @scan_switch_on.setter
    def scan_switch_on(self, scan_switch_on):
        """Sets the scan_switch_on of this SetRegularVirusScanConfigRequest.


        :param scan_switch_on: The scan_switch_on of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: bool
        """

        self._scan_switch_on = scan_switch_on

    @property
    def task_end_time(self):
        """Gets the task_end_time of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The task_end_time of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_end_time

    @task_end_time.setter
    def task_end_time(self, task_end_time):
        """Sets the task_end_time of this SetRegularVirusScanConfigRequest.


        :param task_end_time: The task_end_time of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._task_end_time = task_end_time

    @property
    def task_start_time(self):
        """Gets the task_start_time of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The task_start_time of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_start_time

    @task_start_time.setter
    def task_start_time(self, task_start_time):
        """Sets the task_start_time of this SetRegularVirusScanConfigRequest.


        :param task_start_time: The task_start_time of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._task_start_time = task_start_time

    @property
    def timeout(self):
        """Gets the timeout of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The timeout of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this SetRegularVirusScanConfigRequest.


        :param timeout: The timeout of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._timeout = timeout

    @property
    def top_group_id(self):
        """Gets the top_group_id of this SetRegularVirusScanConfigRequest.  # noqa: E501


        :return: The top_group_id of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this SetRegularVirusScanConfigRequest.


        :param top_group_id: The top_group_id of this SetRegularVirusScanConfigRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetRegularVirusScanConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetRegularVirusScanConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetRegularVirusScanConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
