# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'expr': 'str',
        'kind': 'str',
        'labels': 'list[LabelForListRulesOutput]',
        'last_evaluation': 'str',
        'name': 'str',
        'reason': 'str',
        'rule_file_name': 'str',
        'rule_group_name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'expr': 'Expr',
        'kind': 'Kind',
        'labels': 'Labels',
        'last_evaluation': 'LastEvaluation',
        'name': 'Name',
        'reason': 'Reason',
        'rule_file_name': 'RuleFileName',
        'rule_group_name': 'RuleGroupName',
        'status': 'Status'
    }

    def __init__(self, expr=None, kind=None, labels=None, last_evaluation=None, name=None, reason=None, rule_file_name=None, rule_group_name=None, status=None, _configuration=None):  # noqa: E501
        """ItemForListRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._expr = None
        self._kind = None
        self._labels = None
        self._last_evaluation = None
        self._name = None
        self._reason = None
        self._rule_file_name = None
        self._rule_group_name = None
        self._status = None
        self.discriminator = None

        if expr is not None:
            self.expr = expr
        if kind is not None:
            self.kind = kind
        if labels is not None:
            self.labels = labels
        if last_evaluation is not None:
            self.last_evaluation = last_evaluation
        if name is not None:
            self.name = name
        if reason is not None:
            self.reason = reason
        if rule_file_name is not None:
            self.rule_file_name = rule_file_name
        if rule_group_name is not None:
            self.rule_group_name = rule_group_name
        if status is not None:
            self.status = status

    @property
    def expr(self):
        """Gets the expr of this ItemForListRulesOutput.  # noqa: E501


        :return: The expr of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expr

    @expr.setter
    def expr(self, expr):
        """Sets the expr of this ItemForListRulesOutput.


        :param expr: The expr of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._expr = expr

    @property
    def kind(self):
        """Gets the kind of this ItemForListRulesOutput.  # noqa: E501


        :return: The kind of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this ItemForListRulesOutput.


        :param kind: The kind of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def labels(self):
        """Gets the labels of this ItemForListRulesOutput.  # noqa: E501


        :return: The labels of this ItemForListRulesOutput.  # noqa: E501
        :rtype: list[LabelForListRulesOutput]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this ItemForListRulesOutput.


        :param labels: The labels of this ItemForListRulesOutput.  # noqa: E501
        :type: list[LabelForListRulesOutput]
        """

        self._labels = labels

    @property
    def last_evaluation(self):
        """Gets the last_evaluation of this ItemForListRulesOutput.  # noqa: E501


        :return: The last_evaluation of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_evaluation

    @last_evaluation.setter
    def last_evaluation(self, last_evaluation):
        """Sets the last_evaluation of this ItemForListRulesOutput.


        :param last_evaluation: The last_evaluation of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._last_evaluation = last_evaluation

    @property
    def name(self):
        """Gets the name of this ItemForListRulesOutput.  # noqa: E501


        :return: The name of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListRulesOutput.


        :param name: The name of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def reason(self):
        """Gets the reason of this ItemForListRulesOutput.  # noqa: E501


        :return: The reason of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this ItemForListRulesOutput.


        :param reason: The reason of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def rule_file_name(self):
        """Gets the rule_file_name of this ItemForListRulesOutput.  # noqa: E501


        :return: The rule_file_name of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_file_name

    @rule_file_name.setter
    def rule_file_name(self, rule_file_name):
        """Sets the rule_file_name of this ItemForListRulesOutput.


        :param rule_file_name: The rule_file_name of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_file_name = rule_file_name

    @property
    def rule_group_name(self):
        """Gets the rule_group_name of this ItemForListRulesOutput.  # noqa: E501


        :return: The rule_group_name of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_group_name

    @rule_group_name.setter
    def rule_group_name(self, rule_group_name):
        """Sets the rule_group_name of this ItemForListRulesOutput.


        :param rule_group_name: The rule_group_name of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._rule_group_name = rule_group_name

    @property
    def status(self):
        """Gets the status of this ItemForListRulesOutput.  # noqa: E501


        :return: The status of this ItemForListRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListRulesOutput.


        :param status: The status of this ItemForListRulesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
