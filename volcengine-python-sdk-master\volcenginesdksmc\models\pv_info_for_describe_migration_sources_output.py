# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PvInfoForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pv_name': 'str',
        'pv_size': 'int',
        'pv_uuid': 'str',
        'vg_name': 'str'
    }

    attribute_map = {
        'pv_name': 'PvName',
        'pv_size': 'PvSize',
        'pv_uuid': 'PvUUID',
        'vg_name': 'VgName'
    }

    def __init__(self, pv_name=None, pv_size=None, pv_uuid=None, vg_name=None, _configuration=None):  # noqa: E501
        """PvInfoForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pv_name = None
        self._pv_size = None
        self._pv_uuid = None
        self._vg_name = None
        self.discriminator = None

        if pv_name is not None:
            self.pv_name = pv_name
        if pv_size is not None:
            self.pv_size = pv_size
        if pv_uuid is not None:
            self.pv_uuid = pv_uuid
        if vg_name is not None:
            self.vg_name = vg_name

    @property
    def pv_name(self):
        """Gets the pv_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The pv_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._pv_name

    @pv_name.setter
    def pv_name(self, pv_name):
        """Sets the pv_name of this PvInfoForDescribeMigrationSourcesOutput.


        :param pv_name: The pv_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._pv_name = pv_name

    @property
    def pv_size(self):
        """Gets the pv_size of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The pv_size of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._pv_size

    @pv_size.setter
    def pv_size(self, pv_size):
        """Sets the pv_size of this PvInfoForDescribeMigrationSourcesOutput.


        :param pv_size: The pv_size of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._pv_size = pv_size

    @property
    def pv_uuid(self):
        """Gets the pv_uuid of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The pv_uuid of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._pv_uuid

    @pv_uuid.setter
    def pv_uuid(self, pv_uuid):
        """Sets the pv_uuid of this PvInfoForDescribeMigrationSourcesOutput.


        :param pv_uuid: The pv_uuid of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._pv_uuid = pv_uuid

    @property
    def vg_name(self):
        """Gets the vg_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The vg_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vg_name

    @vg_name.setter
    def vg_name(self, vg_name):
        """Sets the vg_name of this PvInfoForDescribeMigrationSourcesOutput.


        :param vg_name: The vg_name of this PvInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._vg_name = vg_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PvInfoForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PvInfoForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PvInfoForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
