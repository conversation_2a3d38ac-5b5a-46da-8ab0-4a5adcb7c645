# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SSHKeyPairInfoForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time_milli': 'int',
        'finger_print': 'str',
        'name': 'str',
        'uid': 'str'
    }

    attribute_map = {
        'create_time_milli': 'CreateTimeMilli',
        'finger_print': 'FingerPrint',
        'name': 'Name',
        'uid': 'UID'
    }

    def __init__(self, create_time_milli=None, finger_print=None, name=None, uid=None, _configuration=None):  # noqa: E501
        """SSHKeyPairInfoForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time_milli = None
        self._finger_print = None
        self._name = None
        self._uid = None
        self.discriminator = None

        if create_time_milli is not None:
            self.create_time_milli = create_time_milli
        if finger_print is not None:
            self.finger_print = finger_print
        if name is not None:
            self.name = name
        if uid is not None:
            self.uid = uid

    @property
    def create_time_milli(self):
        """Gets the create_time_milli of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501


        :return: The create_time_milli of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time_milli

    @create_time_milli.setter
    def create_time_milli(self, create_time_milli):
        """Sets the create_time_milli of this SSHKeyPairInfoForGetRiskOutput.


        :param create_time_milli: The create_time_milli of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :type: int
        """

        self._create_time_milli = create_time_milli

    @property
    def finger_print(self):
        """Gets the finger_print of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501


        :return: The finger_print of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._finger_print

    @finger_print.setter
    def finger_print(self, finger_print):
        """Sets the finger_print of this SSHKeyPairInfoForGetRiskOutput.


        :param finger_print: The finger_print of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._finger_print = finger_print

    @property
    def name(self):
        """Gets the name of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501


        :return: The name of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SSHKeyPairInfoForGetRiskOutput.


        :param name: The name of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def uid(self):
        """Gets the uid of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501


        :return: The uid of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this SSHKeyPairInfoForGetRiskOutput.


        :param uid: The uid of this SSHKeyPairInfoForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SSHKeyPairInfoForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SSHKeyPairInfoForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SSHKeyPairInfoForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
