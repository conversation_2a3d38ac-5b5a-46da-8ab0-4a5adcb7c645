# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VpcLinkForDescribeVpcLinksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'chain_role_trn': 'list[str]',
        'creation_time': 'str',
        'customer_account_id': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'network_interface_id': 'str',
        'primary_ip_address': 'str',
        'private_ip_addresses': 'list[str]',
        'private_link_gateway_id': 'str',
        'resource_id': 'str',
        'security_group_ids': 'list[str]',
        'status': 'str',
        'subnet_id': 'str',
        'update_time': 'str',
        'vpc_id': 'str',
        'vpc_link_id': 'str',
        'vpc_link_index': 'int',
        'vpc_link_name': 'str'
    }

    attribute_map = {
        'chain_role_trn': 'ChainRoleTrn',
        'creation_time': 'CreationTime',
        'customer_account_id': 'CustomerAccountId',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'network_interface_id': 'NetworkInterfaceId',
        'primary_ip_address': 'PrimaryIpAddress',
        'private_ip_addresses': 'PrivateIpAddresses',
        'private_link_gateway_id': 'PrivateLinkGatewayId',
        'resource_id': 'ResourceId',
        'security_group_ids': 'SecurityGroupIds',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId',
        'vpc_link_id': 'VpcLinkId',
        'vpc_link_index': 'VpcLinkIndex',
        'vpc_link_name': 'VpcLinkName'
    }

    def __init__(self, chain_role_trn=None, creation_time=None, customer_account_id=None, deleted_time=None, description=None, network_interface_id=None, primary_ip_address=None, private_ip_addresses=None, private_link_gateway_id=None, resource_id=None, security_group_ids=None, status=None, subnet_id=None, update_time=None, vpc_id=None, vpc_link_id=None, vpc_link_index=None, vpc_link_name=None, _configuration=None):  # noqa: E501
        """VpcLinkForDescribeVpcLinksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._chain_role_trn = None
        self._creation_time = None
        self._customer_account_id = None
        self._deleted_time = None
        self._description = None
        self._network_interface_id = None
        self._primary_ip_address = None
        self._private_ip_addresses = None
        self._private_link_gateway_id = None
        self._resource_id = None
        self._security_group_ids = None
        self._status = None
        self._subnet_id = None
        self._update_time = None
        self._vpc_id = None
        self._vpc_link_id = None
        self._vpc_link_index = None
        self._vpc_link_name = None
        self.discriminator = None

        if chain_role_trn is not None:
            self.chain_role_trn = chain_role_trn
        if creation_time is not None:
            self.creation_time = creation_time
        if customer_account_id is not None:
            self.customer_account_id = customer_account_id
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if network_interface_id is not None:
            self.network_interface_id = network_interface_id
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if private_ip_addresses is not None:
            self.private_ip_addresses = private_ip_addresses
        if private_link_gateway_id is not None:
            self.private_link_gateway_id = private_link_gateway_id
        if resource_id is not None:
            self.resource_id = resource_id
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_link_id is not None:
            self.vpc_link_id = vpc_link_id
        if vpc_link_index is not None:
            self.vpc_link_index = vpc_link_index
        if vpc_link_name is not None:
            self.vpc_link_name = vpc_link_name

    @property
    def chain_role_trn(self):
        """Gets the chain_role_trn of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The chain_role_trn of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._chain_role_trn

    @chain_role_trn.setter
    def chain_role_trn(self, chain_role_trn):
        """Sets the chain_role_trn of this VpcLinkForDescribeVpcLinksOutput.


        :param chain_role_trn: The chain_role_trn of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: list[str]
        """

        self._chain_role_trn = chain_role_trn

    @property
    def creation_time(self):
        """Gets the creation_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The creation_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this VpcLinkForDescribeVpcLinksOutput.


        :param creation_time: The creation_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def customer_account_id(self):
        """Gets the customer_account_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The customer_account_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_account_id

    @customer_account_id.setter
    def customer_account_id(self, customer_account_id):
        """Sets the customer_account_id of this VpcLinkForDescribeVpcLinksOutput.


        :param customer_account_id: The customer_account_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._customer_account_id = customer_account_id

    @property
    def deleted_time(self):
        """Gets the deleted_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The deleted_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this VpcLinkForDescribeVpcLinksOutput.


        :param deleted_time: The deleted_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The description of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this VpcLinkForDescribeVpcLinksOutput.


        :param description: The description of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def network_interface_id(self):
        """Gets the network_interface_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The network_interface_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_interface_id

    @network_interface_id.setter
    def network_interface_id(self, network_interface_id):
        """Sets the network_interface_id of this VpcLinkForDescribeVpcLinksOutput.


        :param network_interface_id: The network_interface_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._network_interface_id = network_interface_id

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The primary_ip_address of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this VpcLinkForDescribeVpcLinksOutput.


        :param primary_ip_address: The primary_ip_address of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def private_ip_addresses(self):
        """Gets the private_ip_addresses of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The private_ip_addresses of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ip_addresses

    @private_ip_addresses.setter
    def private_ip_addresses(self, private_ip_addresses):
        """Sets the private_ip_addresses of this VpcLinkForDescribeVpcLinksOutput.


        :param private_ip_addresses: The private_ip_addresses of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: list[str]
        """

        self._private_ip_addresses = private_ip_addresses

    @property
    def private_link_gateway_id(self):
        """Gets the private_link_gateway_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The private_link_gateway_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_id

    @private_link_gateway_id.setter
    def private_link_gateway_id(self, private_link_gateway_id):
        """Sets the private_link_gateway_id of this VpcLinkForDescribeVpcLinksOutput.


        :param private_link_gateway_id: The private_link_gateway_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_id = private_link_gateway_id

    @property
    def resource_id(self):
        """Gets the resource_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The resource_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this VpcLinkForDescribeVpcLinksOutput.


        :param resource_id: The resource_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The security_group_ids of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this VpcLinkForDescribeVpcLinksOutput.


        :param security_group_ids: The security_group_ids of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def status(self):
        """Gets the status of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The status of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VpcLinkForDescribeVpcLinksOutput.


        :param status: The status of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The subnet_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this VpcLinkForDescribeVpcLinksOutput.


        :param subnet_id: The subnet_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def update_time(self):
        """Gets the update_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The update_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this VpcLinkForDescribeVpcLinksOutput.


        :param update_time: The update_time of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The vpc_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this VpcLinkForDescribeVpcLinksOutput.


        :param vpc_id: The vpc_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_link_id(self):
        """Gets the vpc_link_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The vpc_link_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_link_id

    @vpc_link_id.setter
    def vpc_link_id(self, vpc_link_id):
        """Sets the vpc_link_id of this VpcLinkForDescribeVpcLinksOutput.


        :param vpc_link_id: The vpc_link_id of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._vpc_link_id = vpc_link_id

    @property
    def vpc_link_index(self):
        """Gets the vpc_link_index of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The vpc_link_index of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: int
        """
        return self._vpc_link_index

    @vpc_link_index.setter
    def vpc_link_index(self, vpc_link_index):
        """Sets the vpc_link_index of this VpcLinkForDescribeVpcLinksOutput.


        :param vpc_link_index: The vpc_link_index of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: int
        """

        self._vpc_link_index = vpc_link_index

    @property
    def vpc_link_name(self):
        """Gets the vpc_link_name of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501


        :return: The vpc_link_name of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_link_name

    @vpc_link_name.setter
    def vpc_link_name(self, vpc_link_name):
        """Sets the vpc_link_name of this VpcLinkForDescribeVpcLinksOutput.


        :param vpc_link_name: The vpc_link_name of this VpcLinkForDescribeVpcLinksOutput.  # noqa: E501
        :type: str
        """

        self._vpc_link_name = vpc_link_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VpcLinkForDescribeVpcLinksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VpcLinkForDescribeVpcLinksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VpcLinkForDescribeVpcLinksOutput):
            return True

        return self.to_dict() != other.to_dict()
