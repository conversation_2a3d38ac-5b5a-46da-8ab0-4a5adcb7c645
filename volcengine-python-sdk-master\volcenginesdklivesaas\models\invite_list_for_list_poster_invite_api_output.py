# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InviteListForListPosterInviteAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'external_user_id': 'str',
        'invite_count': 'int',
        'inviter_avatar': 'str',
        'inviter_name': 'str',
        'rank': 'int',
        'user_id': 'int',
        'user_tel': 'str'
    }

    attribute_map = {
        'external_user_id': 'ExternalUserId',
        'invite_count': 'InviteCount',
        'inviter_avatar': 'InviterAvatar',
        'inviter_name': 'InviterName',
        'rank': 'Rank',
        'user_id': 'UserId',
        'user_tel': 'UserTel'
    }

    def __init__(self, external_user_id=None, invite_count=None, inviter_avatar=None, inviter_name=None, rank=None, user_id=None, user_tel=None, _configuration=None):  # noqa: E501
        """InviteListForListPosterInviteAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._external_user_id = None
        self._invite_count = None
        self._inviter_avatar = None
        self._inviter_name = None
        self._rank = None
        self._user_id = None
        self._user_tel = None
        self.discriminator = None

        if external_user_id is not None:
            self.external_user_id = external_user_id
        if invite_count is not None:
            self.invite_count = invite_count
        if inviter_avatar is not None:
            self.inviter_avatar = inviter_avatar
        if inviter_name is not None:
            self.inviter_name = inviter_name
        if rank is not None:
            self.rank = rank
        if user_id is not None:
            self.user_id = user_id
        if user_tel is not None:
            self.user_tel = user_tel

    @property
    def external_user_id(self):
        """Gets the external_user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The external_user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this InviteListForListPosterInviteAPIOutput.


        :param external_user_id: The external_user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def invite_count(self):
        """Gets the invite_count of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The invite_count of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._invite_count

    @invite_count.setter
    def invite_count(self, invite_count):
        """Sets the invite_count of this InviteListForListPosterInviteAPIOutput.


        :param invite_count: The invite_count of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: int
        """

        self._invite_count = invite_count

    @property
    def inviter_avatar(self):
        """Gets the inviter_avatar of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The inviter_avatar of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._inviter_avatar

    @inviter_avatar.setter
    def inviter_avatar(self, inviter_avatar):
        """Sets the inviter_avatar of this InviteListForListPosterInviteAPIOutput.


        :param inviter_avatar: The inviter_avatar of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: str
        """

        self._inviter_avatar = inviter_avatar

    @property
    def inviter_name(self):
        """Gets the inviter_name of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The inviter_name of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._inviter_name

    @inviter_name.setter
    def inviter_name(self, inviter_name):
        """Sets the inviter_name of this InviteListForListPosterInviteAPIOutput.


        :param inviter_name: The inviter_name of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: str
        """

        self._inviter_name = inviter_name

    @property
    def rank(self):
        """Gets the rank of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The rank of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._rank

    @rank.setter
    def rank(self, rank):
        """Sets the rank of this InviteListForListPosterInviteAPIOutput.


        :param rank: The rank of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: int
        """

        self._rank = rank

    @property
    def user_id(self):
        """Gets the user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this InviteListForListPosterInviteAPIOutput.


        :param user_id: The user_id of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_tel(self):
        """Gets the user_tel of this InviteListForListPosterInviteAPIOutput.  # noqa: E501


        :return: The user_tel of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this InviteListForListPosterInviteAPIOutput.


        :param user_tel: The user_tel of this InviteListForListPosterInviteAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InviteListForListPosterInviteAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InviteListForListPosterInviteAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InviteListForListPosterInviteAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
