# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRepoImageVirusInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_name': 'str',
        'md5': 'str',
        'type': 'str',
        'virus_name': 'str'
    }

    attribute_map = {
        'file_name': 'FileName',
        'md5': 'Md5',
        'type': 'Type',
        'virus_name': 'VirusName'
    }

    def __init__(self, file_name=None, md5=None, type=None, virus_name=None, _configuration=None):  # noqa: E501
        """FilterForListRepoImageVirusInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_name = None
        self._md5 = None
        self._type = None
        self._virus_name = None
        self.discriminator = None

        if file_name is not None:
            self.file_name = file_name
        if md5 is not None:
            self.md5 = md5
        if type is not None:
            self.type = type
        if virus_name is not None:
            self.virus_name = virus_name

    @property
    def file_name(self):
        """Gets the file_name of this FilterForListRepoImageVirusInput.  # noqa: E501


        :return: The file_name of this FilterForListRepoImageVirusInput.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this FilterForListRepoImageVirusInput.


        :param file_name: The file_name of this FilterForListRepoImageVirusInput.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def md5(self):
        """Gets the md5 of this FilterForListRepoImageVirusInput.  # noqa: E501


        :return: The md5 of this FilterForListRepoImageVirusInput.  # noqa: E501
        :rtype: str
        """
        return self._md5

    @md5.setter
    def md5(self, md5):
        """Sets the md5 of this FilterForListRepoImageVirusInput.


        :param md5: The md5 of this FilterForListRepoImageVirusInput.  # noqa: E501
        :type: str
        """

        self._md5 = md5

    @property
    def type(self):
        """Gets the type of this FilterForListRepoImageVirusInput.  # noqa: E501


        :return: The type of this FilterForListRepoImageVirusInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this FilterForListRepoImageVirusInput.


        :param type: The type of this FilterForListRepoImageVirusInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def virus_name(self):
        """Gets the virus_name of this FilterForListRepoImageVirusInput.  # noqa: E501


        :return: The virus_name of this FilterForListRepoImageVirusInput.  # noqa: E501
        :rtype: str
        """
        return self._virus_name

    @virus_name.setter
    def virus_name(self, virus_name):
        """Sets the virus_name of this FilterForListRepoImageVirusInput.


        :param virus_name: The virus_name of this FilterForListRepoImageVirusInput.  # noqa: E501
        :type: str
        """

        self._virus_name = virus_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRepoImageVirusInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRepoImageVirusInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRepoImageVirusInput):
            return True

        return self.to_dict() != other.to_dict()
