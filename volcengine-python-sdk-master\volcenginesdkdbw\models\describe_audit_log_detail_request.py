# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAuditLogDetailRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'instance_id': 'str',
        'instance_type': 'str',
        'log_version': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'region_id': 'str',
        'search_param': 'SearchParamForDescribeAuditLogDetailInput',
        'start_time': 'int'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'instance_id': 'InstanceId',
        'instance_type': 'InstanceType',
        'log_version': 'LogVersion',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'region_id': 'RegionId',
        'search_param': 'SearchParam',
        'start_time': 'StartTime'
    }

    def __init__(self, end_time=None, instance_id=None, instance_type=None, log_version=None, max_results=None, next_token=None, region_id=None, search_param=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeAuditLogDetailRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._instance_id = None
        self._instance_type = None
        self._log_version = None
        self._max_results = None
        self._next_token = None
        self._region_id = None
        self._search_param = None
        self._start_time = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        self.instance_id = instance_id
        self.instance_type = instance_type
        if log_version is not None:
            self.log_version = log_version
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        self.region_id = region_id
        if search_param is not None:
            self.search_param = search_param
        if start_time is not None:
            self.start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The end_time of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeAuditLogDetailRequest.


        :param end_time: The end_time of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The instance_id of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeAuditLogDetailRequest.


        :param instance_id: The instance_id of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def instance_type(self):
        """Gets the instance_type of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The instance_type of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DescribeAuditLogDetailRequest.


        :param instance_type: The instance_type of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_type is None:
            raise ValueError("Invalid value for `instance_type`, must not be `None`")  # noqa: E501
        allowed_values = ["MySQL", "Postgres", "Mongo", "Redis", "VeDBMySQL", "MetaRDS", "MSSQL", "ByteRDS", "MySQLSharding", "MetaMySQL"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def log_version(self):
        """Gets the log_version of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The log_version of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._log_version

    @log_version.setter
    def log_version(self, log_version):
        """Sets the log_version of this DescribeAuditLogDetailRequest.


        :param log_version: The log_version of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: str
        """

        self._log_version = log_version

    @property
    def max_results(self):
        """Gets the max_results of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The max_results of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeAuditLogDetailRequest.


        :param max_results: The max_results of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The next_token of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeAuditLogDetailRequest.


        :param next_token: The next_token of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def region_id(self):
        """Gets the region_id of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The region_id of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this DescribeAuditLogDetailRequest.


        :param region_id: The region_id of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region_id is None:
            raise ValueError("Invalid value for `region_id`, must not be `None`")  # noqa: E501

        self._region_id = region_id

    @property
    def search_param(self):
        """Gets the search_param of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The search_param of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: SearchParamForDescribeAuditLogDetailInput
        """
        return self._search_param

    @search_param.setter
    def search_param(self, search_param):
        """Sets the search_param of this DescribeAuditLogDetailRequest.


        :param search_param: The search_param of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: SearchParamForDescribeAuditLogDetailInput
        """

        self._search_param = search_param

    @property
    def start_time(self):
        """Gets the start_time of this DescribeAuditLogDetailRequest.  # noqa: E501


        :return: The start_time of this DescribeAuditLogDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeAuditLogDetailRequest.


        :param start_time: The start_time of this DescribeAuditLogDetailRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAuditLogDetailRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAuditLogDetailRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAuditLogDetailRequest):
            return True

        return self.to_dict() != other.to_dict()
