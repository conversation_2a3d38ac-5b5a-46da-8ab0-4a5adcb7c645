# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OptionForDescribeBackupsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'brief_result_only': 'bool',
        'result_filter': 'str',
        'result_sorter': 'str'
    }

    attribute_map = {
        'brief_result_only': 'BriefResultOnly',
        'result_filter': 'ResultFilter',
        'result_sorter': 'ResultSorter'
    }

    def __init__(self, brief_result_only=None, result_filter=None, result_sorter=None, _configuration=None):  # noqa: E501
        """OptionForDescribeBackupsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._brief_result_only = None
        self._result_filter = None
        self._result_sorter = None
        self.discriminator = None

        if brief_result_only is not None:
            self.brief_result_only = brief_result_only
        if result_filter is not None:
            self.result_filter = result_filter
        if result_sorter is not None:
            self.result_sorter = result_sorter

    @property
    def brief_result_only(self):
        """Gets the brief_result_only of this OptionForDescribeBackupsInput.  # noqa: E501


        :return: The brief_result_only of this OptionForDescribeBackupsInput.  # noqa: E501
        :rtype: bool
        """
        return self._brief_result_only

    @brief_result_only.setter
    def brief_result_only(self, brief_result_only):
        """Sets the brief_result_only of this OptionForDescribeBackupsInput.


        :param brief_result_only: The brief_result_only of this OptionForDescribeBackupsInput.  # noqa: E501
        :type: bool
        """

        self._brief_result_only = brief_result_only

    @property
    def result_filter(self):
        """Gets the result_filter of this OptionForDescribeBackupsInput.  # noqa: E501


        :return: The result_filter of this OptionForDescribeBackupsInput.  # noqa: E501
        :rtype: str
        """
        return self._result_filter

    @result_filter.setter
    def result_filter(self, result_filter):
        """Sets the result_filter of this OptionForDescribeBackupsInput.


        :param result_filter: The result_filter of this OptionForDescribeBackupsInput.  # noqa: E501
        :type: str
        """

        self._result_filter = result_filter

    @property
    def result_sorter(self):
        """Gets the result_sorter of this OptionForDescribeBackupsInput.  # noqa: E501


        :return: The result_sorter of this OptionForDescribeBackupsInput.  # noqa: E501
        :rtype: str
        """
        return self._result_sorter

    @result_sorter.setter
    def result_sorter(self, result_sorter):
        """Sets the result_sorter of this OptionForDescribeBackupsInput.


        :param result_sorter: The result_sorter of this OptionForDescribeBackupsInput.  # noqa: E501
        :type: str
        """

        self._result_sorter = result_sorter

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OptionForDescribeBackupsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionForDescribeBackupsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionForDescribeBackupsInput):
            return True

        return self.to_dict() != other.to_dict()
