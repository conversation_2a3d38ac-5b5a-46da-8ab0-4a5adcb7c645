# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetSDKTokenAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'avatar_url': 'str',
        'custom_comment': 'str',
        'invite_token': 'str',
        'mode': 'int',
        'nickname': 'str',
        'user_id_str': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'avatar_url': 'AvatarUrl',
        'custom_comment': 'CustomComment',
        'invite_token': 'InviteToken',
        'mode': 'Mode',
        'nickname': 'Nickname',
        'user_id_str': 'UserIdStr'
    }

    def __init__(self, activity_id=None, avatar_url=None, custom_comment=None, invite_token=None, mode=None, nickname=None, user_id_str=None, _configuration=None):  # noqa: E501
        """GetSDKTokenAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._avatar_url = None
        self._custom_comment = None
        self._invite_token = None
        self._mode = None
        self._nickname = None
        self._user_id_str = None
        self.discriminator = None

        self.activity_id = activity_id
        if avatar_url is not None:
            self.avatar_url = avatar_url
        if custom_comment is not None:
            self.custom_comment = custom_comment
        if invite_token is not None:
            self.invite_token = invite_token
        self.mode = mode
        if nickname is not None:
            self.nickname = nickname
        if user_id_str is not None:
            self.user_id_str = user_id_str

    @property
    def activity_id(self):
        """Gets the activity_id of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The activity_id of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetSDKTokenAPIRequest.


        :param activity_id: The activity_id of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def avatar_url(self):
        """Gets the avatar_url of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The avatar_url of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._avatar_url

    @avatar_url.setter
    def avatar_url(self, avatar_url):
        """Sets the avatar_url of this GetSDKTokenAPIRequest.


        :param avatar_url: The avatar_url of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: str
        """

        self._avatar_url = avatar_url

    @property
    def custom_comment(self):
        """Gets the custom_comment of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The custom_comment of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._custom_comment

    @custom_comment.setter
    def custom_comment(self, custom_comment):
        """Sets the custom_comment of this GetSDKTokenAPIRequest.


        :param custom_comment: The custom_comment of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: str
        """

        self._custom_comment = custom_comment

    @property
    def invite_token(self):
        """Gets the invite_token of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The invite_token of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._invite_token

    @invite_token.setter
    def invite_token(self, invite_token):
        """Sets the invite_token of this GetSDKTokenAPIRequest.


        :param invite_token: The invite_token of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: str
        """

        self._invite_token = invite_token

    @property
    def mode(self):
        """Gets the mode of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The mode of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this GetSDKTokenAPIRequest.


        :param mode: The mode of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and mode is None:
            raise ValueError("Invalid value for `mode`, must not be `None`")  # noqa: E501

        self._mode = mode

    @property
    def nickname(self):
        """Gets the nickname of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The nickname of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._nickname

    @nickname.setter
    def nickname(self, nickname):
        """Sets the nickname of this GetSDKTokenAPIRequest.


        :param nickname: The nickname of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: str
        """

        self._nickname = nickname

    @property
    def user_id_str(self):
        """Gets the user_id_str of this GetSDKTokenAPIRequest.  # noqa: E501


        :return: The user_id_str of this GetSDKTokenAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_id_str

    @user_id_str.setter
    def user_id_str(self, user_id_str):
        """Sets the user_id_str of this GetSDKTokenAPIRequest.


        :param user_id_str: The user_id_str of this GetSDKTokenAPIRequest.  # noqa: E501
        :type: str
        """

        self._user_id_str = user_id_str

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetSDKTokenAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetSDKTokenAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetSDKTokenAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
