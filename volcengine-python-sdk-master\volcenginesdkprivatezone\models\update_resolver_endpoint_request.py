# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateResolverEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_id': 'int',
        'endpoint_trn': 'str',
        'endpoint_type': 'str',
        'ip_configs': 'IpConfigsForUpdateResolverEndpointInput',
        'name': 'str'
    }

    attribute_map = {
        'endpoint_id': 'EndpointID',
        'endpoint_trn': 'EndpointTrn',
        'endpoint_type': 'EndpointType',
        'ip_configs': 'IpConfigs',
        'name': 'Name'
    }

    def __init__(self, endpoint_id=None, endpoint_trn=None, endpoint_type=None, ip_configs=None, name=None, _configuration=None):  # noqa: E501
        """UpdateResolverEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_id = None
        self._endpoint_trn = None
        self._endpoint_type = None
        self._ip_configs = None
        self._name = None
        self.discriminator = None

        self.endpoint_id = endpoint_id
        if endpoint_trn is not None:
            self.endpoint_trn = endpoint_trn
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if ip_configs is not None:
            self.ip_configs = ip_configs
        if name is not None:
            self.name = name

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this UpdateResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_id of this UpdateResolverEndpointRequest.  # noqa: E501
        :rtype: int
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this UpdateResolverEndpointRequest.


        :param endpoint_id: The endpoint_id of this UpdateResolverEndpointRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and endpoint_id is None:
            raise ValueError("Invalid value for `endpoint_id`, must not be `None`")  # noqa: E501

        self._endpoint_id = endpoint_id

    @property
    def endpoint_trn(self):
        """Gets the endpoint_trn of this UpdateResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_trn of this UpdateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_trn

    @endpoint_trn.setter
    def endpoint_trn(self, endpoint_trn):
        """Sets the endpoint_trn of this UpdateResolverEndpointRequest.


        :param endpoint_trn: The endpoint_trn of this UpdateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_trn = endpoint_trn

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this UpdateResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_type of this UpdateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this UpdateResolverEndpointRequest.


        :param endpoint_type: The endpoint_type of this UpdateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def ip_configs(self):
        """Gets the ip_configs of this UpdateResolverEndpointRequest.  # noqa: E501


        :return: The ip_configs of this UpdateResolverEndpointRequest.  # noqa: E501
        :rtype: IpConfigsForUpdateResolverEndpointInput
        """
        return self._ip_configs

    @ip_configs.setter
    def ip_configs(self, ip_configs):
        """Sets the ip_configs of this UpdateResolverEndpointRequest.


        :param ip_configs: The ip_configs of this UpdateResolverEndpointRequest.  # noqa: E501
        :type: IpConfigsForUpdateResolverEndpointInput
        """

        self._ip_configs = ip_configs

    @property
    def name(self):
        """Gets the name of this UpdateResolverEndpointRequest.  # noqa: E501


        :return: The name of this UpdateResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateResolverEndpointRequest.


        :param name: The name of this UpdateResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateResolverEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateResolverEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateResolverEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
