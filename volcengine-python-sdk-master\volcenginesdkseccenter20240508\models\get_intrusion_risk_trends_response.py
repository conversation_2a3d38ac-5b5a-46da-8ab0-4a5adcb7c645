# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetIntrusionRiskTrendsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'critical_trends': 'list[CriticalTrendForGetIntrusionRiskTrendsOutput]',
        'high_trends': 'list[HighTrendForGetIntrusionRiskTrendsOutput]',
        'low_trends': 'list[LowTrendForGetIntrusionRiskTrendsOutput]',
        'medium_trends': 'list[MediumTrendForGetIntrusionRiskTrendsOutput]'
    }

    attribute_map = {
        'critical_trends': 'CriticalTrends',
        'high_trends': 'HighTrends',
        'low_trends': 'LowTrends',
        'medium_trends': 'MediumTrends'
    }

    def __init__(self, critical_trends=None, high_trends=None, low_trends=None, medium_trends=None, _configuration=None):  # noqa: E501
        """GetIntrusionRiskTrendsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._critical_trends = None
        self._high_trends = None
        self._low_trends = None
        self._medium_trends = None
        self.discriminator = None

        if critical_trends is not None:
            self.critical_trends = critical_trends
        if high_trends is not None:
            self.high_trends = high_trends
        if low_trends is not None:
            self.low_trends = low_trends
        if medium_trends is not None:
            self.medium_trends = medium_trends

    @property
    def critical_trends(self):
        """Gets the critical_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501


        :return: The critical_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :rtype: list[CriticalTrendForGetIntrusionRiskTrendsOutput]
        """
        return self._critical_trends

    @critical_trends.setter
    def critical_trends(self, critical_trends):
        """Sets the critical_trends of this GetIntrusionRiskTrendsResponse.


        :param critical_trends: The critical_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :type: list[CriticalTrendForGetIntrusionRiskTrendsOutput]
        """

        self._critical_trends = critical_trends

    @property
    def high_trends(self):
        """Gets the high_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501


        :return: The high_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :rtype: list[HighTrendForGetIntrusionRiskTrendsOutput]
        """
        return self._high_trends

    @high_trends.setter
    def high_trends(self, high_trends):
        """Sets the high_trends of this GetIntrusionRiskTrendsResponse.


        :param high_trends: The high_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :type: list[HighTrendForGetIntrusionRiskTrendsOutput]
        """

        self._high_trends = high_trends

    @property
    def low_trends(self):
        """Gets the low_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501


        :return: The low_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :rtype: list[LowTrendForGetIntrusionRiskTrendsOutput]
        """
        return self._low_trends

    @low_trends.setter
    def low_trends(self, low_trends):
        """Sets the low_trends of this GetIntrusionRiskTrendsResponse.


        :param low_trends: The low_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :type: list[LowTrendForGetIntrusionRiskTrendsOutput]
        """

        self._low_trends = low_trends

    @property
    def medium_trends(self):
        """Gets the medium_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501


        :return: The medium_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :rtype: list[MediumTrendForGetIntrusionRiskTrendsOutput]
        """
        return self._medium_trends

    @medium_trends.setter
    def medium_trends(self, medium_trends):
        """Sets the medium_trends of this GetIntrusionRiskTrendsResponse.


        :param medium_trends: The medium_trends of this GetIntrusionRiskTrendsResponse.  # noqa: E501
        :type: list[MediumTrendForGetIntrusionRiskTrendsOutput]
        """

        self._medium_trends = medium_trends

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetIntrusionRiskTrendsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetIntrusionRiskTrendsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetIntrusionRiskTrendsResponse):
            return True

        return self.to_dict() != other.to_dict()
