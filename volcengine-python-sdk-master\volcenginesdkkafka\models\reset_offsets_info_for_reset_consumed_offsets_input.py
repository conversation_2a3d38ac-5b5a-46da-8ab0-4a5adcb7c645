# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResetOffsetsInfoForResetConsumedOffsetsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'partition_id': 'int',
        'reset_offset': 'int',
        'reset_timestamp': 'int',
        'reset_type': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'partition_id': 'PartitionId',
        'reset_offset': 'ResetOffset',
        'reset_timestamp': 'ResetTimestamp',
        'reset_type': 'ResetType',
        'topic_name': 'TopicName'
    }

    def __init__(self, partition_id=None, reset_offset=None, reset_timestamp=None, reset_type=None, topic_name=None, _configuration=None):  # noqa: E501
        """ResetOffsetsInfoForResetConsumedOffsetsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._partition_id = None
        self._reset_offset = None
        self._reset_timestamp = None
        self._reset_type = None
        self._topic_name = None
        self.discriminator = None

        if partition_id is not None:
            self.partition_id = partition_id
        if reset_offset is not None:
            self.reset_offset = reset_offset
        if reset_timestamp is not None:
            self.reset_timestamp = reset_timestamp
        if reset_type is not None:
            self.reset_type = reset_type
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def partition_id(self):
        """Gets the partition_id of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501


        :return: The partition_id of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :rtype: int
        """
        return self._partition_id

    @partition_id.setter
    def partition_id(self, partition_id):
        """Sets the partition_id of this ResetOffsetsInfoForResetConsumedOffsetsInput.


        :param partition_id: The partition_id of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :type: int
        """

        self._partition_id = partition_id

    @property
    def reset_offset(self):
        """Gets the reset_offset of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501


        :return: The reset_offset of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :rtype: int
        """
        return self._reset_offset

    @reset_offset.setter
    def reset_offset(self, reset_offset):
        """Sets the reset_offset of this ResetOffsetsInfoForResetConsumedOffsetsInput.


        :param reset_offset: The reset_offset of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :type: int
        """

        self._reset_offset = reset_offset

    @property
    def reset_timestamp(self):
        """Gets the reset_timestamp of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501


        :return: The reset_timestamp of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :rtype: int
        """
        return self._reset_timestamp

    @reset_timestamp.setter
    def reset_timestamp(self, reset_timestamp):
        """Sets the reset_timestamp of this ResetOffsetsInfoForResetConsumedOffsetsInput.


        :param reset_timestamp: The reset_timestamp of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :type: int
        """

        self._reset_timestamp = reset_timestamp

    @property
    def reset_type(self):
        """Gets the reset_type of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501


        :return: The reset_type of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :rtype: str
        """
        return self._reset_type

    @reset_type.setter
    def reset_type(self, reset_type):
        """Sets the reset_type of this ResetOffsetsInfoForResetConsumedOffsetsInput.


        :param reset_type: The reset_type of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :type: str
        """

        self._reset_type = reset_type

    @property
    def topic_name(self):
        """Gets the topic_name of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501


        :return: The topic_name of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this ResetOffsetsInfoForResetConsumedOffsetsInput.


        :param topic_name: The topic_name of this ResetOffsetsInfoForResetConsumedOffsetsInput.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResetOffsetsInfoForResetConsumedOffsetsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResetOffsetsInfoForResetConsumedOffsetsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResetOffsetsInfoForResetConsumedOffsetsInput):
            return True

        return self.to_dict() != other.to_dict()
