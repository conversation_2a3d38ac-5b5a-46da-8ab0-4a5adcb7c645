# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityReplayPlayerConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'replay_player_config': 'ReplayPlayerConfigForGetActivityReplayPlayerConfigOutput'
    }

    attribute_map = {
        'replay_player_config': 'ReplayPlayerConfig'
    }

    def __init__(self, replay_player_config=None, _configuration=None):  # noqa: E501
        """GetActivityReplayPlayerConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._replay_player_config = None
        self.discriminator = None

        if replay_player_config is not None:
            self.replay_player_config = replay_player_config

    @property
    def replay_player_config(self):
        """Gets the replay_player_config of this GetActivityReplayPlayerConfigResponse.  # noqa: E501


        :return: The replay_player_config of this GetActivityReplayPlayerConfigResponse.  # noqa: E501
        :rtype: ReplayPlayerConfigForGetActivityReplayPlayerConfigOutput
        """
        return self._replay_player_config

    @replay_player_config.setter
    def replay_player_config(self, replay_player_config):
        """Sets the replay_player_config of this GetActivityReplayPlayerConfigResponse.


        :param replay_player_config: The replay_player_config of this GetActivityReplayPlayerConfigResponse.  # noqa: E501
        :type: ReplayPlayerConfigForGetActivityReplayPlayerConfigOutput
        """

        self._replay_player_config = replay_player_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityReplayPlayerConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityReplayPlayerConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityReplayPlayerConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
