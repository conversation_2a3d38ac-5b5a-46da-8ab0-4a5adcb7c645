# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstancesDistributionForModifyScalingGroupInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'compensate_with_on_demand': 'bool',
        'on_demand_base_capacity': 'int',
        'on_demand_percentage_above_base_capacity': 'int',
        'spot_instance_remedy': 'bool'
    }

    attribute_map = {
        'compensate_with_on_demand': 'CompensateWithOnDemand',
        'on_demand_base_capacity': 'OnDemandBaseCapacity',
        'on_demand_percentage_above_base_capacity': 'OnDemandPercentageAboveBaseCapacity',
        'spot_instance_remedy': 'SpotInstanceRemedy'
    }

    def __init__(self, compensate_with_on_demand=None, on_demand_base_capacity=None, on_demand_percentage_above_base_capacity=None, spot_instance_remedy=None, _configuration=None):  # noqa: E501
        """InstancesDistributionForModifyScalingGroupInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._compensate_with_on_demand = None
        self._on_demand_base_capacity = None
        self._on_demand_percentage_above_base_capacity = None
        self._spot_instance_remedy = None
        self.discriminator = None

        if compensate_with_on_demand is not None:
            self.compensate_with_on_demand = compensate_with_on_demand
        if on_demand_base_capacity is not None:
            self.on_demand_base_capacity = on_demand_base_capacity
        if on_demand_percentage_above_base_capacity is not None:
            self.on_demand_percentage_above_base_capacity = on_demand_percentage_above_base_capacity
        if spot_instance_remedy is not None:
            self.spot_instance_remedy = spot_instance_remedy

    @property
    def compensate_with_on_demand(self):
        """Gets the compensate_with_on_demand of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501


        :return: The compensate_with_on_demand of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :rtype: bool
        """
        return self._compensate_with_on_demand

    @compensate_with_on_demand.setter
    def compensate_with_on_demand(self, compensate_with_on_demand):
        """Sets the compensate_with_on_demand of this InstancesDistributionForModifyScalingGroupInput.


        :param compensate_with_on_demand: The compensate_with_on_demand of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :type: bool
        """

        self._compensate_with_on_demand = compensate_with_on_demand

    @property
    def on_demand_base_capacity(self):
        """Gets the on_demand_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501


        :return: The on_demand_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._on_demand_base_capacity

    @on_demand_base_capacity.setter
    def on_demand_base_capacity(self, on_demand_base_capacity):
        """Sets the on_demand_base_capacity of this InstancesDistributionForModifyScalingGroupInput.


        :param on_demand_base_capacity: The on_demand_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                on_demand_base_capacity is not None and on_demand_base_capacity > 2000):  # noqa: E501
            raise ValueError("Invalid value for `on_demand_base_capacity`, must be a value less than or equal to `2000`")  # noqa: E501
        if (self._configuration.client_side_validation and
                on_demand_base_capacity is not None and on_demand_base_capacity < 0):  # noqa: E501
            raise ValueError("Invalid value for `on_demand_base_capacity`, must be a value greater than or equal to `0`")  # noqa: E501

        self._on_demand_base_capacity = on_demand_base_capacity

    @property
    def on_demand_percentage_above_base_capacity(self):
        """Gets the on_demand_percentage_above_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501


        :return: The on_demand_percentage_above_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._on_demand_percentage_above_base_capacity

    @on_demand_percentage_above_base_capacity.setter
    def on_demand_percentage_above_base_capacity(self, on_demand_percentage_above_base_capacity):
        """Sets the on_demand_percentage_above_base_capacity of this InstancesDistributionForModifyScalingGroupInput.


        :param on_demand_percentage_above_base_capacity: The on_demand_percentage_above_base_capacity of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                on_demand_percentage_above_base_capacity is not None and on_demand_percentage_above_base_capacity > 100):  # noqa: E501
            raise ValueError("Invalid value for `on_demand_percentage_above_base_capacity`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                on_demand_percentage_above_base_capacity is not None and on_demand_percentage_above_base_capacity < 0):  # noqa: E501
            raise ValueError("Invalid value for `on_demand_percentage_above_base_capacity`, must be a value greater than or equal to `0`")  # noqa: E501

        self._on_demand_percentage_above_base_capacity = on_demand_percentage_above_base_capacity

    @property
    def spot_instance_remedy(self):
        """Gets the spot_instance_remedy of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501


        :return: The spot_instance_remedy of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :rtype: bool
        """
        return self._spot_instance_remedy

    @spot_instance_remedy.setter
    def spot_instance_remedy(self, spot_instance_remedy):
        """Sets the spot_instance_remedy of this InstancesDistributionForModifyScalingGroupInput.


        :param spot_instance_remedy: The spot_instance_remedy of this InstancesDistributionForModifyScalingGroupInput.  # noqa: E501
        :type: bool
        """

        self._spot_instance_remedy = spot_instance_remedy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstancesDistributionForModifyScalingGroupInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstancesDistributionForModifyScalingGroupInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstancesDistributionForModifyScalingGroupInput):
            return True

        return self.to_dict() != other.to_dict()
