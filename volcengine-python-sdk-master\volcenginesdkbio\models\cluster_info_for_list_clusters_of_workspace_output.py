# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ClusterInfoForListClustersOfWorkspaceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bound': 'bool',
        'description': 'str',
        'error_message': 'str',
        'external_config': 'ExternalConfigForListClustersOfWorkspaceOutput',
        'id': 'str',
        'name': 'str',
        'public': 'bool',
        'shared_config': 'SharedConfigForListClustersOfWorkspaceOutput',
        'start_time': 'int',
        'status': 'str',
        'stopped_time': 'int',
        'vke_config': 'VKEConfigForListClustersOfWorkspaceOutput'
    }

    attribute_map = {
        'bound': 'Bound',
        'description': 'Description',
        'error_message': 'ErrorMessage',
        'external_config': 'ExternalConfig',
        'id': 'ID',
        'name': 'Name',
        'public': 'Public',
        'shared_config': 'SharedConfig',
        'start_time': 'StartTime',
        'status': 'Status',
        'stopped_time': 'StoppedTime',
        'vke_config': 'VKEConfig'
    }

    def __init__(self, bound=None, description=None, error_message=None, external_config=None, id=None, name=None, public=None, shared_config=None, start_time=None, status=None, stopped_time=None, vke_config=None, _configuration=None):  # noqa: E501
        """ClusterInfoForListClustersOfWorkspaceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bound = None
        self._description = None
        self._error_message = None
        self._external_config = None
        self._id = None
        self._name = None
        self._public = None
        self._shared_config = None
        self._start_time = None
        self._status = None
        self._stopped_time = None
        self._vke_config = None
        self.discriminator = None

        if bound is not None:
            self.bound = bound
        if description is not None:
            self.description = description
        if error_message is not None:
            self.error_message = error_message
        if external_config is not None:
            self.external_config = external_config
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if public is not None:
            self.public = public
        if shared_config is not None:
            self.shared_config = shared_config
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if stopped_time is not None:
            self.stopped_time = stopped_time
        if vke_config is not None:
            self.vke_config = vke_config

    @property
    def bound(self):
        """Gets the bound of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The bound of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._bound

    @bound.setter
    def bound(self, bound):
        """Sets the bound of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param bound: The bound of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: bool
        """

        self._bound = bound

    @property
    def description(self):
        """Gets the description of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The description of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param description: The description of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def error_message(self):
        """Gets the error_message of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The error_message of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_message

    @error_message.setter
    def error_message(self, error_message):
        """Sets the error_message of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param error_message: The error_message of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._error_message = error_message

    @property
    def external_config(self):
        """Gets the external_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The external_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: ExternalConfigForListClustersOfWorkspaceOutput
        """
        return self._external_config

    @external_config.setter
    def external_config(self, external_config):
        """Sets the external_config of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param external_config: The external_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: ExternalConfigForListClustersOfWorkspaceOutput
        """

        self._external_config = external_config

    @property
    def id(self):
        """Gets the id of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The id of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param id: The id of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The name of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param name: The name of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def public(self):
        """Gets the public of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The public of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._public

    @public.setter
    def public(self, public):
        """Sets the public of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param public: The public of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: bool
        """

        self._public = public

    @property
    def shared_config(self):
        """Gets the shared_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The shared_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: SharedConfigForListClustersOfWorkspaceOutput
        """
        return self._shared_config

    @shared_config.setter
    def shared_config(self, shared_config):
        """Sets the shared_config of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param shared_config: The shared_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: SharedConfigForListClustersOfWorkspaceOutput
        """

        self._shared_config = shared_config

    @property
    def start_time(self):
        """Gets the start_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The start_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param start_time: The start_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The status of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param status: The status of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stopped_time(self):
        """Gets the stopped_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The stopped_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: int
        """
        return self._stopped_time

    @stopped_time.setter
    def stopped_time(self, stopped_time):
        """Sets the stopped_time of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param stopped_time: The stopped_time of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: int
        """

        self._stopped_time = stopped_time

    @property
    def vke_config(self):
        """Gets the vke_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501


        :return: The vke_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :rtype: VKEConfigForListClustersOfWorkspaceOutput
        """
        return self._vke_config

    @vke_config.setter
    def vke_config(self, vke_config):
        """Sets the vke_config of this ClusterInfoForListClustersOfWorkspaceOutput.


        :param vke_config: The vke_config of this ClusterInfoForListClustersOfWorkspaceOutput.  # noqa: E501
        :type: VKEConfigForListClustersOfWorkspaceOutput
        """

        self._vke_config = vke_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ClusterInfoForListClustersOfWorkspaceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ClusterInfoForListClustersOfWorkspaceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ClusterInfoForListClustersOfWorkspaceOutput):
            return True

        return self.to_dict() != other.to_dict()
