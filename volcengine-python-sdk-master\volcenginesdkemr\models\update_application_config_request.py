# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateApplicationConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'client_token': 'str',
        'cluster_id': 'str',
        'configs': 'list[ConfigForUpdateApplicationConfigInput]',
        'remark': 'str'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'client_token': 'ClientToken',
        'cluster_id': 'ClusterId',
        'configs': 'Configs',
        'remark': 'Remark'
    }

    def __init__(self, application_name=None, client_token=None, cluster_id=None, configs=None, remark=None, _configuration=None):  # noqa: E501
        """UpdateApplicationConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._client_token = None
        self._cluster_id = None
        self._configs = None
        self._remark = None
        self.discriminator = None

        self.application_name = application_name
        if client_token is not None:
            self.client_token = client_token
        self.cluster_id = cluster_id
        if configs is not None:
            self.configs = configs
        self.remark = remark

    @property
    def application_name(self):
        """Gets the application_name of this UpdateApplicationConfigRequest.  # noqa: E501


        :return: The application_name of this UpdateApplicationConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this UpdateApplicationConfigRequest.


        :param application_name: The application_name of this UpdateApplicationConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and application_name is None:
            raise ValueError("Invalid value for `application_name`, must not be `None`")  # noqa: E501

        self._application_name = application_name

    @property
    def client_token(self):
        """Gets the client_token of this UpdateApplicationConfigRequest.  # noqa: E501


        :return: The client_token of this UpdateApplicationConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this UpdateApplicationConfigRequest.


        :param client_token: The client_token of this UpdateApplicationConfigRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_id(self):
        """Gets the cluster_id of this UpdateApplicationConfigRequest.  # noqa: E501


        :return: The cluster_id of this UpdateApplicationConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this UpdateApplicationConfigRequest.


        :param cluster_id: The cluster_id of this UpdateApplicationConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def configs(self):
        """Gets the configs of this UpdateApplicationConfigRequest.  # noqa: E501


        :return: The configs of this UpdateApplicationConfigRequest.  # noqa: E501
        :rtype: list[ConfigForUpdateApplicationConfigInput]
        """
        return self._configs

    @configs.setter
    def configs(self, configs):
        """Sets the configs of this UpdateApplicationConfigRequest.


        :param configs: The configs of this UpdateApplicationConfigRequest.  # noqa: E501
        :type: list[ConfigForUpdateApplicationConfigInput]
        """

        self._configs = configs

    @property
    def remark(self):
        """Gets the remark of this UpdateApplicationConfigRequest.  # noqa: E501


        :return: The remark of this UpdateApplicationConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this UpdateApplicationConfigRequest.


        :param remark: The remark of this UpdateApplicationConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and remark is None:
            raise ValueError("Invalid value for `remark`, must not be `None`")  # noqa: E501

        self._remark = remark

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateApplicationConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateApplicationConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateApplicationConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
