# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateProductReminderInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'hot_sale': 'str',
        'product_id': 'int',
        'reminder_type': 'int',
        'stock': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'hot_sale': 'HotSale',
        'product_id': 'ProductId',
        'reminder_type': 'ReminderType',
        'stock': 'Stock'
    }

    def __init__(self, activity_id=None, hot_sale=None, product_id=None, reminder_type=None, stock=None, _configuration=None):  # noqa: E501
        """UpdateProductReminderInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._hot_sale = None
        self._product_id = None
        self._reminder_type = None
        self._stock = None
        self.discriminator = None

        self.activity_id = activity_id
        if hot_sale is not None:
            self.hot_sale = hot_sale
        self.product_id = product_id
        if reminder_type is not None:
            self.reminder_type = reminder_type
        if stock is not None:
            self.stock = stock

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateProductReminderInfoRequest.  # noqa: E501


        :return: The activity_id of this UpdateProductReminderInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateProductReminderInfoRequest.


        :param activity_id: The activity_id of this UpdateProductReminderInfoRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def hot_sale(self):
        """Gets the hot_sale of this UpdateProductReminderInfoRequest.  # noqa: E501


        :return: The hot_sale of this UpdateProductReminderInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._hot_sale

    @hot_sale.setter
    def hot_sale(self, hot_sale):
        """Sets the hot_sale of this UpdateProductReminderInfoRequest.


        :param hot_sale: The hot_sale of this UpdateProductReminderInfoRequest.  # noqa: E501
        :type: str
        """

        self._hot_sale = hot_sale

    @property
    def product_id(self):
        """Gets the product_id of this UpdateProductReminderInfoRequest.  # noqa: E501


        :return: The product_id of this UpdateProductReminderInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this UpdateProductReminderInfoRequest.


        :param product_id: The product_id of this UpdateProductReminderInfoRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and product_id is None:
            raise ValueError("Invalid value for `product_id`, must not be `None`")  # noqa: E501

        self._product_id = product_id

    @property
    def reminder_type(self):
        """Gets the reminder_type of this UpdateProductReminderInfoRequest.  # noqa: E501


        :return: The reminder_type of this UpdateProductReminderInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._reminder_type

    @reminder_type.setter
    def reminder_type(self, reminder_type):
        """Sets the reminder_type of this UpdateProductReminderInfoRequest.


        :param reminder_type: The reminder_type of this UpdateProductReminderInfoRequest.  # noqa: E501
        :type: int
        """

        self._reminder_type = reminder_type

    @property
    def stock(self):
        """Gets the stock of this UpdateProductReminderInfoRequest.  # noqa: E501


        :return: The stock of this UpdateProductReminderInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._stock

    @stock.setter
    def stock(self, stock):
        """Sets the stock of this UpdateProductReminderInfoRequest.


        :param stock: The stock of this UpdateProductReminderInfoRequest.  # noqa: E501
        :type: str
        """

        self._stock = stock

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateProductReminderInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateProductReminderInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateProductReminderInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
