# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListComponentInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_names': 'list[str]',
        'cluster_id': 'str',
        'component_states': 'list[str]',
        'node_ids': 'list[str]',
        'node_names': 'list[str]'
    }

    attribute_map = {
        'application_names': 'ApplicationNames',
        'cluster_id': 'ClusterId',
        'component_states': 'ComponentStates',
        'node_ids': 'NodeIds',
        'node_names': 'NodeNames'
    }

    def __init__(self, application_names=None, cluster_id=None, component_states=None, node_ids=None, node_names=None, _configuration=None):  # noqa: E501
        """ListComponentInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_names = None
        self._cluster_id = None
        self._component_states = None
        self._node_ids = None
        self._node_names = None
        self.discriminator = None

        if application_names is not None:
            self.application_names = application_names
        self.cluster_id = cluster_id
        if component_states is not None:
            self.component_states = component_states
        if node_ids is not None:
            self.node_ids = node_ids
        if node_names is not None:
            self.node_names = node_names

    @property
    def application_names(self):
        """Gets the application_names of this ListComponentInstancesRequest.  # noqa: E501


        :return: The application_names of this ListComponentInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._application_names

    @application_names.setter
    def application_names(self, application_names):
        """Sets the application_names of this ListComponentInstancesRequest.


        :param application_names: The application_names of this ListComponentInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._application_names = application_names

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListComponentInstancesRequest.  # noqa: E501


        :return: The cluster_id of this ListComponentInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListComponentInstancesRequest.


        :param cluster_id: The cluster_id of this ListComponentInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def component_states(self):
        """Gets the component_states of this ListComponentInstancesRequest.  # noqa: E501


        :return: The component_states of this ListComponentInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._component_states

    @component_states.setter
    def component_states(self, component_states):
        """Sets the component_states of this ListComponentInstancesRequest.


        :param component_states: The component_states of this ListComponentInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._component_states = component_states

    @property
    def node_ids(self):
        """Gets the node_ids of this ListComponentInstancesRequest.  # noqa: E501


        :return: The node_ids of this ListComponentInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_ids

    @node_ids.setter
    def node_ids(self, node_ids):
        """Sets the node_ids of this ListComponentInstancesRequest.


        :param node_ids: The node_ids of this ListComponentInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_ids = node_ids

    @property
    def node_names(self):
        """Gets the node_names of this ListComponentInstancesRequest.  # noqa: E501


        :return: The node_names of this ListComponentInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_names

    @node_names.setter
    def node_names(self, node_names):
        """Sets the node_names of this ListComponentInstancesRequest.


        :param node_names: The node_names of this ListComponentInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_names = node_names

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListComponentInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListComponentInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListComponentInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
