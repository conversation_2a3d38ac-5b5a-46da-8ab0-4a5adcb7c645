# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateBasicAccelerateIPEndpointRelationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerate_ipid': 'str',
        'accelerator_id': 'str',
        'endpoint_ids': 'list[str]',
        'ip_set_id': 'str'
    }

    attribute_map = {
        'accelerate_ipid': 'AccelerateIPId',
        'accelerator_id': 'AcceleratorId',
        'endpoint_ids': 'EndpointIds',
        'ip_set_id': 'IPSetId'
    }

    def __init__(self, accelerate_ipid=None, accelerator_id=None, endpoint_ids=None, ip_set_id=None, _configuration=None):  # noqa: E501
        """CreateBasicAccelerateIPEndpointRelationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerate_ipid = None
        self._accelerator_id = None
        self._endpoint_ids = None
        self._ip_set_id = None
        self.discriminator = None

        self.accelerate_ipid = accelerate_ipid
        self.accelerator_id = accelerator_id
        if endpoint_ids is not None:
            self.endpoint_ids = endpoint_ids
        self.ip_set_id = ip_set_id

    @property
    def accelerate_ipid(self):
        """Gets the accelerate_ipid of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501


        :return: The accelerate_ipid of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_ipid

    @accelerate_ipid.setter
    def accelerate_ipid(self, accelerate_ipid):
        """Sets the accelerate_ipid of this CreateBasicAccelerateIPEndpointRelationRequest.


        :param accelerate_ipid: The accelerate_ipid of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerate_ipid is None:
            raise ValueError("Invalid value for `accelerate_ipid`, must not be `None`")  # noqa: E501

        self._accelerate_ipid = accelerate_ipid

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501


        :return: The accelerator_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this CreateBasicAccelerateIPEndpointRelationRequest.


        :param accelerator_id: The accelerator_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def endpoint_ids(self):
        """Gets the endpoint_ids of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501


        :return: The endpoint_ids of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_ids

    @endpoint_ids.setter
    def endpoint_ids(self, endpoint_ids):
        """Sets the endpoint_ids of this CreateBasicAccelerateIPEndpointRelationRequest.


        :param endpoint_ids: The endpoint_ids of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_ids = endpoint_ids

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501


        :return: The ip_set_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this CreateBasicAccelerateIPEndpointRelationRequest.


        :param ip_set_id: The ip_set_id of this CreateBasicAccelerateIPEndpointRelationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_set_id is None:
            raise ValueError("Invalid value for `ip_set_id`, must not be `None`")  # noqa: E501

        self._ip_set_id = ip_set_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateBasicAccelerateIPEndpointRelationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateBasicAccelerateIPEndpointRelationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateBasicAccelerateIPEndpointRelationRequest):
            return True

        return self.to_dict() != other.to_dict()
