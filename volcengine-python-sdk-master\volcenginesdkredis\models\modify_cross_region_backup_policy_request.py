# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyCrossRegionBackupPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'active': 'bool',
        'client_token': 'str',
        'cross_backup_region': 'str',
        'instance_id': 'str'
    }

    attribute_map = {
        'active': 'Active',
        'client_token': 'ClientToken',
        'cross_backup_region': 'CrossBackupRegion',
        'instance_id': 'InstanceId'
    }

    def __init__(self, active=None, client_token=None, cross_backup_region=None, instance_id=None, _configuration=None):  # noqa: E501
        """ModifyCrossRegionBackupPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._active = None
        self._client_token = None
        self._cross_backup_region = None
        self._instance_id = None
        self.discriminator = None

        self.active = active
        if client_token is not None:
            self.client_token = client_token
        if cross_backup_region is not None:
            self.cross_backup_region = cross_backup_region
        self.instance_id = instance_id

    @property
    def active(self):
        """Gets the active of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501


        :return: The active of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._active

    @active.setter
    def active(self, active):
        """Sets the active of this ModifyCrossRegionBackupPolicyRequest.


        :param active: The active of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and active is None:
            raise ValueError("Invalid value for `active`, must not be `None`")  # noqa: E501

        self._active = active

    @property
    def client_token(self):
        """Gets the client_token of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501


        :return: The client_token of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyCrossRegionBackupPolicyRequest.


        :param client_token: The client_token of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cross_backup_region(self):
        """Gets the cross_backup_region of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501


        :return: The cross_backup_region of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._cross_backup_region

    @cross_backup_region.setter
    def cross_backup_region(self, cross_backup_region):
        """Sets the cross_backup_region of this ModifyCrossRegionBackupPolicyRequest.


        :param cross_backup_region: The cross_backup_region of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :type: str
        """

        self._cross_backup_region = cross_backup_region

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501


        :return: The instance_id of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyCrossRegionBackupPolicyRequest.


        :param instance_id: The instance_id of this ModifyCrossRegionBackupPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyCrossRegionBackupPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyCrossRegionBackupPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyCrossRegionBackupPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
