# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAllStreamPullInfoAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup': 'list[BackupForGetAllStreamPullInfoAPIOutput]',
        'line_name': 'str',
        'main': 'list[MainForGetAllStreamPullInfoAPIOutput]'
    }

    attribute_map = {
        'backup': 'Backup',
        'line_name': 'LineName',
        'main': 'Main'
    }

    def __init__(self, backup=None, line_name=None, main=None, _configuration=None):  # noqa: E501
        """GetAllStreamPullInfoAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup = None
        self._line_name = None
        self._main = None
        self.discriminator = None

        if backup is not None:
            self.backup = backup
        if line_name is not None:
            self.line_name = line_name
        if main is not None:
            self.main = main

    @property
    def backup(self):
        """Gets the backup of this GetAllStreamPullInfoAPIResponse.  # noqa: E501


        :return: The backup of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :rtype: list[BackupForGetAllStreamPullInfoAPIOutput]
        """
        return self._backup

    @backup.setter
    def backup(self, backup):
        """Sets the backup of this GetAllStreamPullInfoAPIResponse.


        :param backup: The backup of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :type: list[BackupForGetAllStreamPullInfoAPIOutput]
        """

        self._backup = backup

    @property
    def line_name(self):
        """Gets the line_name of this GetAllStreamPullInfoAPIResponse.  # noqa: E501


        :return: The line_name of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._line_name

    @line_name.setter
    def line_name(self, line_name):
        """Sets the line_name of this GetAllStreamPullInfoAPIResponse.


        :param line_name: The line_name of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :type: str
        """

        self._line_name = line_name

    @property
    def main(self):
        """Gets the main of this GetAllStreamPullInfoAPIResponse.  # noqa: E501


        :return: The main of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :rtype: list[MainForGetAllStreamPullInfoAPIOutput]
        """
        return self._main

    @main.setter
    def main(self, main):
        """Sets the main of this GetAllStreamPullInfoAPIResponse.


        :param main: The main of this GetAllStreamPullInfoAPIResponse.  # noqa: E501
        :type: list[MainForGetAllStreamPullInfoAPIOutput]
        """

        self._main = main

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAllStreamPullInfoAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAllStreamPullInfoAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAllStreamPullInfoAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
