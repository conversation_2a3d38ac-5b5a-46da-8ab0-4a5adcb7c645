# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'basic_detail': 'BasicDetailForGetTaskResultOutput',
        'dns_detail': 'DNSDetailForGetTaskResultOutput',
        'download_detail': 'DownloadDetailForGetTaskResultOutput',
        'http_detail': 'HTTPDetailForGetTaskResultOutput',
        'page_detail': 'PageDetailForGetTaskResultOutput',
        'ping_detail': 'PingDetailForGetTaskResultOutput',
        'tcp_detail': 'TCPDetailForGetTaskResultOutput',
        'task_id': 'int',
        'udp_detail': 'UDPDetailForGetTaskResultOutput',
        'upload_detail': 'UploadDetailForGetTaskResultOutput'
    }

    attribute_map = {
        'basic_detail': 'BasicDetail',
        'dns_detail': 'DNSDetail',
        'download_detail': 'DownloadDetail',
        'http_detail': 'HTTPDetail',
        'page_detail': 'PageDetail',
        'ping_detail': 'PingDetail',
        'tcp_detail': 'TCPDetail',
        'task_id': 'TaskID',
        'udp_detail': 'UDPDetail',
        'upload_detail': 'UploadDetail'
    }

    def __init__(self, basic_detail=None, dns_detail=None, download_detail=None, http_detail=None, page_detail=None, ping_detail=None, tcp_detail=None, task_id=None, udp_detail=None, upload_detail=None, _configuration=None):  # noqa: E501
        """DataForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._basic_detail = None
        self._dns_detail = None
        self._download_detail = None
        self._http_detail = None
        self._page_detail = None
        self._ping_detail = None
        self._tcp_detail = None
        self._task_id = None
        self._udp_detail = None
        self._upload_detail = None
        self.discriminator = None

        if basic_detail is not None:
            self.basic_detail = basic_detail
        if dns_detail is not None:
            self.dns_detail = dns_detail
        if download_detail is not None:
            self.download_detail = download_detail
        if http_detail is not None:
            self.http_detail = http_detail
        if page_detail is not None:
            self.page_detail = page_detail
        if ping_detail is not None:
            self.ping_detail = ping_detail
        if tcp_detail is not None:
            self.tcp_detail = tcp_detail
        if task_id is not None:
            self.task_id = task_id
        if udp_detail is not None:
            self.udp_detail = udp_detail
        if upload_detail is not None:
            self.upload_detail = upload_detail

    @property
    def basic_detail(self):
        """Gets the basic_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The basic_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: BasicDetailForGetTaskResultOutput
        """
        return self._basic_detail

    @basic_detail.setter
    def basic_detail(self, basic_detail):
        """Sets the basic_detail of this DataForGetTaskResultOutput.


        :param basic_detail: The basic_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: BasicDetailForGetTaskResultOutput
        """

        self._basic_detail = basic_detail

    @property
    def dns_detail(self):
        """Gets the dns_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The dns_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: DNSDetailForGetTaskResultOutput
        """
        return self._dns_detail

    @dns_detail.setter
    def dns_detail(self, dns_detail):
        """Sets the dns_detail of this DataForGetTaskResultOutput.


        :param dns_detail: The dns_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: DNSDetailForGetTaskResultOutput
        """

        self._dns_detail = dns_detail

    @property
    def download_detail(self):
        """Gets the download_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The download_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: DownloadDetailForGetTaskResultOutput
        """
        return self._download_detail

    @download_detail.setter
    def download_detail(self, download_detail):
        """Sets the download_detail of this DataForGetTaskResultOutput.


        :param download_detail: The download_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: DownloadDetailForGetTaskResultOutput
        """

        self._download_detail = download_detail

    @property
    def http_detail(self):
        """Gets the http_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The http_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: HTTPDetailForGetTaskResultOutput
        """
        return self._http_detail

    @http_detail.setter
    def http_detail(self, http_detail):
        """Sets the http_detail of this DataForGetTaskResultOutput.


        :param http_detail: The http_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: HTTPDetailForGetTaskResultOutput
        """

        self._http_detail = http_detail

    @property
    def page_detail(self):
        """Gets the page_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The page_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: PageDetailForGetTaskResultOutput
        """
        return self._page_detail

    @page_detail.setter
    def page_detail(self, page_detail):
        """Sets the page_detail of this DataForGetTaskResultOutput.


        :param page_detail: The page_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: PageDetailForGetTaskResultOutput
        """

        self._page_detail = page_detail

    @property
    def ping_detail(self):
        """Gets the ping_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The ping_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: PingDetailForGetTaskResultOutput
        """
        return self._ping_detail

    @ping_detail.setter
    def ping_detail(self, ping_detail):
        """Sets the ping_detail of this DataForGetTaskResultOutput.


        :param ping_detail: The ping_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: PingDetailForGetTaskResultOutput
        """

        self._ping_detail = ping_detail

    @property
    def tcp_detail(self):
        """Gets the tcp_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: TCPDetailForGetTaskResultOutput
        """
        return self._tcp_detail

    @tcp_detail.setter
    def tcp_detail(self, tcp_detail):
        """Sets the tcp_detail of this DataForGetTaskResultOutput.


        :param tcp_detail: The tcp_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: TCPDetailForGetTaskResultOutput
        """

        self._tcp_detail = tcp_detail

    @property
    def task_id(self):
        """Gets the task_id of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The task_id of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DataForGetTaskResultOutput.


        :param task_id: The task_id of this DataForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._task_id = task_id

    @property
    def udp_detail(self):
        """Gets the udp_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The udp_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: UDPDetailForGetTaskResultOutput
        """
        return self._udp_detail

    @udp_detail.setter
    def udp_detail(self, udp_detail):
        """Sets the udp_detail of this DataForGetTaskResultOutput.


        :param udp_detail: The udp_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: UDPDetailForGetTaskResultOutput
        """

        self._udp_detail = udp_detail

    @property
    def upload_detail(self):
        """Gets the upload_detail of this DataForGetTaskResultOutput.  # noqa: E501


        :return: The upload_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :rtype: UploadDetailForGetTaskResultOutput
        """
        return self._upload_detail

    @upload_detail.setter
    def upload_detail(self, upload_detail):
        """Sets the upload_detail of this DataForGetTaskResultOutput.


        :param upload_detail: The upload_detail of this DataForGetTaskResultOutput.  # noqa: E501
        :type: UploadDetailForGetTaskResultOutput
        """

        self._upload_detail = upload_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
