# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModelForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asr_app_id': 'str',
        'asr_app_type': 'str',
        'doubao_vision_endpoint': 'str'
    }

    attribute_map = {
        'asr_app_id': 'AsrAppId',
        'asr_app_type': 'AsrAppType',
        'doubao_vision_endpoint': 'DoubaoVisionEndpoint'
    }

    def __init__(self, asr_app_id=None, asr_app_type=None, doubao_vision_endpoint=None, _configuration=None):  # noqa: E501
        """ModelForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asr_app_id = None
        self._asr_app_type = None
        self._doubao_vision_endpoint = None
        self.discriminator = None

        if asr_app_id is not None:
            self.asr_app_id = asr_app_id
        if asr_app_type is not None:
            self.asr_app_type = asr_app_type
        if doubao_vision_endpoint is not None:
            self.doubao_vision_endpoint = doubao_vision_endpoint

    @property
    def asr_app_id(self):
        """Gets the asr_app_id of this ModelForStartExecutionInput.  # noqa: E501


        :return: The asr_app_id of this ModelForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._asr_app_id

    @asr_app_id.setter
    def asr_app_id(self, asr_app_id):
        """Sets the asr_app_id of this ModelForStartExecutionInput.


        :param asr_app_id: The asr_app_id of this ModelForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._asr_app_id = asr_app_id

    @property
    def asr_app_type(self):
        """Gets the asr_app_type of this ModelForStartExecutionInput.  # noqa: E501


        :return: The asr_app_type of this ModelForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._asr_app_type

    @asr_app_type.setter
    def asr_app_type(self, asr_app_type):
        """Sets the asr_app_type of this ModelForStartExecutionInput.


        :param asr_app_type: The asr_app_type of this ModelForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._asr_app_type = asr_app_type

    @property
    def doubao_vision_endpoint(self):
        """Gets the doubao_vision_endpoint of this ModelForStartExecutionInput.  # noqa: E501


        :return: The doubao_vision_endpoint of this ModelForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._doubao_vision_endpoint

    @doubao_vision_endpoint.setter
    def doubao_vision_endpoint(self, doubao_vision_endpoint):
        """Sets the doubao_vision_endpoint of this ModelForStartExecutionInput.


        :param doubao_vision_endpoint: The doubao_vision_endpoint of this ModelForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._doubao_vision_endpoint = doubao_vision_endpoint

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModelForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModelForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModelForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
