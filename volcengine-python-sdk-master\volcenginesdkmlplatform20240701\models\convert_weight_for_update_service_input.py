# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertWeightForUpdateServiceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deployment_id': 'str',
        'weight': 'int'
    }

    attribute_map = {
        'deployment_id': 'DeploymentId',
        'weight': 'Weight'
    }

    def __init__(self, deployment_id=None, weight=None, _configuration=None):  # noqa: E501
        """ConvertWeightForUpdateServiceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deployment_id = None
        self._weight = None
        self.discriminator = None

        if deployment_id is not None:
            self.deployment_id = deployment_id
        if weight is not None:
            self.weight = weight

    @property
    def deployment_id(self):
        """Gets the deployment_id of this ConvertWeightForUpdateServiceInput.  # noqa: E501


        :return: The deployment_id of this ConvertWeightForUpdateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._deployment_id

    @deployment_id.setter
    def deployment_id(self, deployment_id):
        """Sets the deployment_id of this ConvertWeightForUpdateServiceInput.


        :param deployment_id: The deployment_id of this ConvertWeightForUpdateServiceInput.  # noqa: E501
        :type: str
        """

        self._deployment_id = deployment_id

    @property
    def weight(self):
        """Gets the weight of this ConvertWeightForUpdateServiceInput.  # noqa: E501


        :return: The weight of this ConvertWeightForUpdateServiceInput.  # noqa: E501
        :rtype: int
        """
        return self._weight

    @weight.setter
    def weight(self, weight):
        """Sets the weight of this ConvertWeightForUpdateServiceInput.


        :param weight: The weight of this ConvertWeightForUpdateServiceInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                weight is not None and weight > 10):  # noqa: E501
            raise ValueError("Invalid value for `weight`, must be a value less than or equal to `10`")  # noqa: E501
        if (self._configuration.client_side_validation and
                weight is not None and weight < 0):  # noqa: E501
            raise ValueError("Invalid value for `weight`, must be a value greater than or equal to `0`")  # noqa: E501

        self._weight = weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertWeightForUpdateServiceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertWeightForUpdateServiceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertWeightForUpdateServiceInput):
            return True

        return self.to_dict() != other.to_dict()
