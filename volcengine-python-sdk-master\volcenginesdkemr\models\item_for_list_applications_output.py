# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListApplicationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_config_home': 'str',
        'application_home': 'str',
        'application_name': 'str',
        'application_state': 'str',
        'application_version': 'str',
        'group': 'str',
        'support_client': 'bool',
        'user': 'str'
    }

    attribute_map = {
        'application_config_home': 'ApplicationConfigHome',
        'application_home': 'ApplicationHome',
        'application_name': 'ApplicationName',
        'application_state': 'ApplicationState',
        'application_version': 'ApplicationVersion',
        'group': 'Group',
        'support_client': 'SupportClient',
        'user': 'User'
    }

    def __init__(self, application_config_home=None, application_home=None, application_name=None, application_state=None, application_version=None, group=None, support_client=None, user=None, _configuration=None):  # noqa: E501
        """ItemForListApplicationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_config_home = None
        self._application_home = None
        self._application_name = None
        self._application_state = None
        self._application_version = None
        self._group = None
        self._support_client = None
        self._user = None
        self.discriminator = None

        if application_config_home is not None:
            self.application_config_home = application_config_home
        if application_home is not None:
            self.application_home = application_home
        if application_name is not None:
            self.application_name = application_name
        if application_state is not None:
            self.application_state = application_state
        if application_version is not None:
            self.application_version = application_version
        if group is not None:
            self.group = group
        if support_client is not None:
            self.support_client = support_client
        if user is not None:
            self.user = user

    @property
    def application_config_home(self):
        """Gets the application_config_home of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The application_config_home of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_config_home

    @application_config_home.setter
    def application_config_home(self, application_config_home):
        """Sets the application_config_home of this ItemForListApplicationsOutput.


        :param application_config_home: The application_config_home of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._application_config_home = application_config_home

    @property
    def application_home(self):
        """Gets the application_home of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The application_home of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_home

    @application_home.setter
    def application_home(self, application_home):
        """Sets the application_home of this ItemForListApplicationsOutput.


        :param application_home: The application_home of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._application_home = application_home

    @property
    def application_name(self):
        """Gets the application_name of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The application_name of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ItemForListApplicationsOutput.


        :param application_name: The application_name of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def application_state(self):
        """Gets the application_state of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The application_state of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_state

    @application_state.setter
    def application_state(self, application_state):
        """Sets the application_state of this ItemForListApplicationsOutput.


        :param application_state: The application_state of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._application_state = application_state

    @property
    def application_version(self):
        """Gets the application_version of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The application_version of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_version

    @application_version.setter
    def application_version(self, application_version):
        """Sets the application_version of this ItemForListApplicationsOutput.


        :param application_version: The application_version of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._application_version = application_version

    @property
    def group(self):
        """Gets the group of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The group of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group

    @group.setter
    def group(self, group):
        """Sets the group of this ItemForListApplicationsOutput.


        :param group: The group of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._group = group

    @property
    def support_client(self):
        """Gets the support_client of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The support_client of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._support_client

    @support_client.setter
    def support_client(self, support_client):
        """Sets the support_client of this ItemForListApplicationsOutput.


        :param support_client: The support_client of this ItemForListApplicationsOutput.  # noqa: E501
        :type: bool
        """

        self._support_client = support_client

    @property
    def user(self):
        """Gets the user of this ItemForListApplicationsOutput.  # noqa: E501


        :return: The user of this ItemForListApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this ItemForListApplicationsOutput.


        :param user: The user of this ItemForListApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListApplicationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListApplicationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListApplicationsOutput):
            return True

        return self.to_dict() != other.to_dict()
