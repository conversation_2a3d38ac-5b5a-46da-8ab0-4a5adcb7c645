# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LogForDescribeMigrationLogsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'log_level': 'str',
        'log_message': 'str',
        'log_time': 'str'
    }

    attribute_map = {
        'log_level': 'LogLevel',
        'log_message': 'LogMessage',
        'log_time': 'LogTime'
    }

    def __init__(self, log_level=None, log_message=None, log_time=None, _configuration=None):  # noqa: E501
        """LogForDescribeMigrationLogsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._log_level = None
        self._log_message = None
        self._log_time = None
        self.discriminator = None

        if log_level is not None:
            self.log_level = log_level
        if log_message is not None:
            self.log_message = log_message
        if log_time is not None:
            self.log_time = log_time

    @property
    def log_level(self):
        """Gets the log_level of this LogForDescribeMigrationLogsOutput.  # noqa: E501


        :return: The log_level of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_level

    @log_level.setter
    def log_level(self, log_level):
        """Sets the log_level of this LogForDescribeMigrationLogsOutput.


        :param log_level: The log_level of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :type: str
        """

        self._log_level = log_level

    @property
    def log_message(self):
        """Gets the log_message of this LogForDescribeMigrationLogsOutput.  # noqa: E501


        :return: The log_message of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_message

    @log_message.setter
    def log_message(self, log_message):
        """Sets the log_message of this LogForDescribeMigrationLogsOutput.


        :param log_message: The log_message of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :type: str
        """

        self._log_message = log_message

    @property
    def log_time(self):
        """Gets the log_time of this LogForDescribeMigrationLogsOutput.  # noqa: E501


        :return: The log_time of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_time

    @log_time.setter
    def log_time(self, log_time):
        """Sets the log_time of this LogForDescribeMigrationLogsOutput.


        :param log_time: The log_time of this LogForDescribeMigrationLogsOutput.  # noqa: E501
        :type: str
        """

        self._log_time = log_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LogForDescribeMigrationLogsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LogForDescribeMigrationLogsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LogForDescribeMigrationLogsOutput):
            return True

        return self.to_dict() != other.to_dict()
