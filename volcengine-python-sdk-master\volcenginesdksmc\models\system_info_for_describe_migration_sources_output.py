# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SystemInfoForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'architecture': 'str',
        'cpu': 'int',
        'cpu_usage': 'float',
        'memory': 'int',
        'memory_usage': 'float'
    }

    attribute_map = {
        'architecture': 'Architecture',
        'cpu': 'CPU',
        'cpu_usage': 'CPUUsage',
        'memory': 'Memory',
        'memory_usage': 'MemoryUsage'
    }

    def __init__(self, architecture=None, cpu=None, cpu_usage=None, memory=None, memory_usage=None, _configuration=None):  # noqa: E501
        """SystemInfoForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._architecture = None
        self._cpu = None
        self._cpu_usage = None
        self._memory = None
        self._memory_usage = None
        self.discriminator = None

        if architecture is not None:
            self.architecture = architecture
        if cpu is not None:
            self.cpu = cpu
        if cpu_usage is not None:
            self.cpu_usage = cpu_usage
        if memory is not None:
            self.memory = memory
        if memory_usage is not None:
            self.memory_usage = memory_usage

    @property
    def architecture(self):
        """Gets the architecture of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The architecture of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._architecture

    @architecture.setter
    def architecture(self, architecture):
        """Sets the architecture of this SystemInfoForDescribeMigrationSourcesOutput.


        :param architecture: The architecture of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._architecture = architecture

    @property
    def cpu(self):
        """Gets the cpu of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The cpu of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this SystemInfoForDescribeMigrationSourcesOutput.


        :param cpu: The cpu of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._cpu = cpu

    @property
    def cpu_usage(self):
        """Gets the cpu_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The cpu_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu_usage

    @cpu_usage.setter
    def cpu_usage(self, cpu_usage):
        """Sets the cpu_usage of this SystemInfoForDescribeMigrationSourcesOutput.


        :param cpu_usage: The cpu_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: float
        """

        self._cpu_usage = cpu_usage

    @property
    def memory(self):
        """Gets the memory of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The memory of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this SystemInfoForDescribeMigrationSourcesOutput.


        :param memory: The memory of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def memory_usage(self):
        """Gets the memory_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The memory_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: float
        """
        return self._memory_usage

    @memory_usage.setter
    def memory_usage(self, memory_usage):
        """Sets the memory_usage of this SystemInfoForDescribeMigrationSourcesOutput.


        :param memory_usage: The memory_usage of this SystemInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: float
        """

        self._memory_usage = memory_usage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SystemInfoForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SystemInfoForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SystemInfoForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
