# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebAtkTopSrcIpResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attack_count': 'int',
        'attack_ip': 'str',
        'percentage': 'float',
        'region': 'str'
    }

    attribute_map = {
        'attack_count': 'AttackCount',
        'attack_ip': 'AttackIP',
        'percentage': 'Percentage',
        'region': 'Region'
    }

    def __init__(self, attack_count=None, attack_ip=None, percentage=None, region=None, _configuration=None):  # noqa: E501
        """DescWebAtkTopSrcIpResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attack_count = None
        self._attack_ip = None
        self._percentage = None
        self._region = None
        self.discriminator = None

        if attack_count is not None:
            self.attack_count = attack_count
        if attack_ip is not None:
            self.attack_ip = attack_ip
        if percentage is not None:
            self.percentage = percentage
        if region is not None:
            self.region = region

    @property
    def attack_count(self):
        """Gets the attack_count of this DescWebAtkTopSrcIpResponse.  # noqa: E501


        :return: The attack_count of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :rtype: int
        """
        return self._attack_count

    @attack_count.setter
    def attack_count(self, attack_count):
        """Sets the attack_count of this DescWebAtkTopSrcIpResponse.


        :param attack_count: The attack_count of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :type: int
        """

        self._attack_count = attack_count

    @property
    def attack_ip(self):
        """Gets the attack_ip of this DescWebAtkTopSrcIpResponse.  # noqa: E501


        :return: The attack_ip of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :rtype: str
        """
        return self._attack_ip

    @attack_ip.setter
    def attack_ip(self, attack_ip):
        """Sets the attack_ip of this DescWebAtkTopSrcIpResponse.


        :param attack_ip: The attack_ip of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :type: str
        """

        self._attack_ip = attack_ip

    @property
    def percentage(self):
        """Gets the percentage of this DescWebAtkTopSrcIpResponse.  # noqa: E501


        :return: The percentage of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :rtype: float
        """
        return self._percentage

    @percentage.setter
    def percentage(self, percentage):
        """Sets the percentage of this DescWebAtkTopSrcIpResponse.


        :param percentage: The percentage of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :type: float
        """

        self._percentage = percentage

    @property
    def region(self):
        """Gets the region of this DescWebAtkTopSrcIpResponse.  # noqa: E501


        :return: The region of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DescWebAtkTopSrcIpResponse.


        :param region: The region of this DescWebAtkTopSrcIpResponse.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebAtkTopSrcIpResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebAtkTopSrcIpResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebAtkTopSrcIpResponse):
            return True

        return self.to_dict() != other.to_dict()
