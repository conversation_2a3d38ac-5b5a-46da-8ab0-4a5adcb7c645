# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaselineInfoForListBaselineCheckItemsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'baseline_check': 'str',
        'baseline_id': 'int',
        'check_id': 'int',
        'description': 'str',
        'description_cn': 'str',
        'pass_rate': 'int',
        'security': 'str',
        'solution': 'str',
        'solution_cn': 'str',
        'status': 'str',
        'title': 'str',
        'title_cn': 'str',
        'type': 'str',
        'type_cn': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'baseline_check': 'BaselineCheck',
        'baseline_id': 'BaselineID',
        'check_id': 'CheckID',
        'description': 'Description',
        'description_cn': 'DescriptionCn',
        'pass_rate': 'PassRate',
        'security': 'Security',
        'solution': 'Solution',
        'solution_cn': 'SolutionCn',
        'status': 'Status',
        'title': 'Title',
        'title_cn': 'TitleCn',
        'type': 'Type',
        'type_cn': 'TypeCn',
        'update_time': 'UpdateTime'
    }

    def __init__(self, baseline_check=None, baseline_id=None, check_id=None, description=None, description_cn=None, pass_rate=None, security=None, solution=None, solution_cn=None, status=None, title=None, title_cn=None, type=None, type_cn=None, update_time=None, _configuration=None):  # noqa: E501
        """BaselineInfoForListBaselineCheckItemsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._baseline_check = None
        self._baseline_id = None
        self._check_id = None
        self._description = None
        self._description_cn = None
        self._pass_rate = None
        self._security = None
        self._solution = None
        self._solution_cn = None
        self._status = None
        self._title = None
        self._title_cn = None
        self._type = None
        self._type_cn = None
        self._update_time = None
        self.discriminator = None

        if baseline_check is not None:
            self.baseline_check = baseline_check
        if baseline_id is not None:
            self.baseline_id = baseline_id
        if check_id is not None:
            self.check_id = check_id
        if description is not None:
            self.description = description
        if description_cn is not None:
            self.description_cn = description_cn
        if pass_rate is not None:
            self.pass_rate = pass_rate
        if security is not None:
            self.security = security
        if solution is not None:
            self.solution = solution
        if solution_cn is not None:
            self.solution_cn = solution_cn
        if status is not None:
            self.status = status
        if title is not None:
            self.title = title
        if title_cn is not None:
            self.title_cn = title_cn
        if type is not None:
            self.type = type
        if type_cn is not None:
            self.type_cn = type_cn
        if update_time is not None:
            self.update_time = update_time

    @property
    def baseline_check(self):
        """Gets the baseline_check of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The baseline_check of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._baseline_check

    @baseline_check.setter
    def baseline_check(self, baseline_check):
        """Sets the baseline_check of this BaselineInfoForListBaselineCheckItemsOutput.


        :param baseline_check: The baseline_check of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._baseline_check = baseline_check

    @property
    def baseline_id(self):
        """Gets the baseline_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The baseline_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this BaselineInfoForListBaselineCheckItemsOutput.


        :param baseline_id: The baseline_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def check_id(self):
        """Gets the check_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The check_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_id

    @check_id.setter
    def check_id(self, check_id):
        """Sets the check_id of this BaselineInfoForListBaselineCheckItemsOutput.


        :param check_id: The check_id of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: int
        """

        self._check_id = check_id

    @property
    def description(self):
        """Gets the description of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The description of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this BaselineInfoForListBaselineCheckItemsOutput.


        :param description: The description of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def description_cn(self):
        """Gets the description_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The description_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description_cn

    @description_cn.setter
    def description_cn(self, description_cn):
        """Sets the description_cn of this BaselineInfoForListBaselineCheckItemsOutput.


        :param description_cn: The description_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._description_cn = description_cn

    @property
    def pass_rate(self):
        """Gets the pass_rate of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The pass_rate of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._pass_rate

    @pass_rate.setter
    def pass_rate(self, pass_rate):
        """Sets the pass_rate of this BaselineInfoForListBaselineCheckItemsOutput.


        :param pass_rate: The pass_rate of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: int
        """

        self._pass_rate = pass_rate

    @property
    def security(self):
        """Gets the security of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The security of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._security

    @security.setter
    def security(self, security):
        """Sets the security of this BaselineInfoForListBaselineCheckItemsOutput.


        :param security: The security of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._security = security

    @property
    def solution(self):
        """Gets the solution of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The solution of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._solution

    @solution.setter
    def solution(self, solution):
        """Sets the solution of this BaselineInfoForListBaselineCheckItemsOutput.


        :param solution: The solution of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._solution = solution

    @property
    def solution_cn(self):
        """Gets the solution_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The solution_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._solution_cn

    @solution_cn.setter
    def solution_cn(self, solution_cn):
        """Sets the solution_cn of this BaselineInfoForListBaselineCheckItemsOutput.


        :param solution_cn: The solution_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._solution_cn = solution_cn

    @property
    def status(self):
        """Gets the status of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The status of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this BaselineInfoForListBaselineCheckItemsOutput.


        :param status: The status of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def title(self):
        """Gets the title of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The title of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this BaselineInfoForListBaselineCheckItemsOutput.


        :param title: The title of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def title_cn(self):
        """Gets the title_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The title_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._title_cn

    @title_cn.setter
    def title_cn(self, title_cn):
        """Sets the title_cn of this BaselineInfoForListBaselineCheckItemsOutput.


        :param title_cn: The title_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._title_cn = title_cn

    @property
    def type(self):
        """Gets the type of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The type of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this BaselineInfoForListBaselineCheckItemsOutput.


        :param type: The type of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def type_cn(self):
        """Gets the type_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The type_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type_cn

    @type_cn.setter
    def type_cn(self, type_cn):
        """Sets the type_cn of this BaselineInfoForListBaselineCheckItemsOutput.


        :param type_cn: The type_cn of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: str
        """

        self._type_cn = type_cn

    @property
    def update_time(self):
        """Gets the update_time of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501


        :return: The update_time of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BaselineInfoForListBaselineCheckItemsOutput.


        :param update_time: The update_time of this BaselineInfoForListBaselineCheckItemsOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaselineInfoForListBaselineCheckItemsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaselineInfoForListBaselineCheckItemsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaselineInfoForListBaselineCheckItemsOutput):
            return True

        return self.to_dict() != other.to_dict()
