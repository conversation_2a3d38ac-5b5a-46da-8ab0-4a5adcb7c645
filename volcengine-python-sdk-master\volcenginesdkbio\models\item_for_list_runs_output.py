# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListRunsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_entity_row_id': 'str',
        'duration': 'int',
        'engine_run_id': 'str',
        'engine_workflow_name': 'str',
        'finish_time': 'int',
        'id': 'str',
        'inputs': 'str',
        'log': 'str',
        'message': 'str',
        'outputs': 'str',
        'start_time': 'int',
        'status': 'str',
        'submission_id': 'str',
        'task_status': 'TaskStatusForListRunsOutput'
    }

    attribute_map = {
        'data_entity_row_id': 'DataEntityRowID',
        'duration': 'Duration',
        'engine_run_id': 'EngineRunID',
        'engine_workflow_name': 'EngineWorkflowName',
        'finish_time': 'FinishTime',
        'id': 'ID',
        'inputs': 'Inputs',
        'log': 'Log',
        'message': 'Message',
        'outputs': 'Outputs',
        'start_time': 'StartTime',
        'status': 'Status',
        'submission_id': 'SubmissionID',
        'task_status': 'TaskStatus'
    }

    def __init__(self, data_entity_row_id=None, duration=None, engine_run_id=None, engine_workflow_name=None, finish_time=None, id=None, inputs=None, log=None, message=None, outputs=None, start_time=None, status=None, submission_id=None, task_status=None, _configuration=None):  # noqa: E501
        """ItemForListRunsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_entity_row_id = None
        self._duration = None
        self._engine_run_id = None
        self._engine_workflow_name = None
        self._finish_time = None
        self._id = None
        self._inputs = None
        self._log = None
        self._message = None
        self._outputs = None
        self._start_time = None
        self._status = None
        self._submission_id = None
        self._task_status = None
        self.discriminator = None

        if data_entity_row_id is not None:
            self.data_entity_row_id = data_entity_row_id
        if duration is not None:
            self.duration = duration
        if engine_run_id is not None:
            self.engine_run_id = engine_run_id
        if engine_workflow_name is not None:
            self.engine_workflow_name = engine_workflow_name
        if finish_time is not None:
            self.finish_time = finish_time
        if id is not None:
            self.id = id
        if inputs is not None:
            self.inputs = inputs
        if log is not None:
            self.log = log
        if message is not None:
            self.message = message
        if outputs is not None:
            self.outputs = outputs
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if submission_id is not None:
            self.submission_id = submission_id
        if task_status is not None:
            self.task_status = task_status

    @property
    def data_entity_row_id(self):
        """Gets the data_entity_row_id of this ItemForListRunsOutput.  # noqa: E501


        :return: The data_entity_row_id of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_entity_row_id

    @data_entity_row_id.setter
    def data_entity_row_id(self, data_entity_row_id):
        """Sets the data_entity_row_id of this ItemForListRunsOutput.


        :param data_entity_row_id: The data_entity_row_id of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._data_entity_row_id = data_entity_row_id

    @property
    def duration(self):
        """Gets the duration of this ItemForListRunsOutput.  # noqa: E501


        :return: The duration of this ItemForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ItemForListRunsOutput.


        :param duration: The duration of this ItemForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def engine_run_id(self):
        """Gets the engine_run_id of this ItemForListRunsOutput.  # noqa: E501


        :return: The engine_run_id of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._engine_run_id

    @engine_run_id.setter
    def engine_run_id(self, engine_run_id):
        """Sets the engine_run_id of this ItemForListRunsOutput.


        :param engine_run_id: The engine_run_id of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._engine_run_id = engine_run_id

    @property
    def engine_workflow_name(self):
        """Gets the engine_workflow_name of this ItemForListRunsOutput.  # noqa: E501


        :return: The engine_workflow_name of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._engine_workflow_name

    @engine_workflow_name.setter
    def engine_workflow_name(self, engine_workflow_name):
        """Sets the engine_workflow_name of this ItemForListRunsOutput.


        :param engine_workflow_name: The engine_workflow_name of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._engine_workflow_name = engine_workflow_name

    @property
    def finish_time(self):
        """Gets the finish_time of this ItemForListRunsOutput.  # noqa: E501


        :return: The finish_time of this ItemForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this ItemForListRunsOutput.


        :param finish_time: The finish_time of this ItemForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._finish_time = finish_time

    @property
    def id(self):
        """Gets the id of this ItemForListRunsOutput.  # noqa: E501


        :return: The id of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListRunsOutput.


        :param id: The id of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def inputs(self):
        """Gets the inputs of this ItemForListRunsOutput.  # noqa: E501


        :return: The inputs of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._inputs

    @inputs.setter
    def inputs(self, inputs):
        """Sets the inputs of this ItemForListRunsOutput.


        :param inputs: The inputs of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._inputs = inputs

    @property
    def log(self):
        """Gets the log of this ItemForListRunsOutput.  # noqa: E501


        :return: The log of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._log

    @log.setter
    def log(self, log):
        """Sets the log of this ItemForListRunsOutput.


        :param log: The log of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._log = log

    @property
    def message(self):
        """Gets the message of this ItemForListRunsOutput.  # noqa: E501


        :return: The message of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListRunsOutput.


        :param message: The message of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def outputs(self):
        """Gets the outputs of this ItemForListRunsOutput.  # noqa: E501


        :return: The outputs of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._outputs

    @outputs.setter
    def outputs(self, outputs):
        """Sets the outputs of this ItemForListRunsOutput.


        :param outputs: The outputs of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._outputs = outputs

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListRunsOutput.  # noqa: E501


        :return: The start_time of this ItemForListRunsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListRunsOutput.


        :param start_time: The start_time of this ItemForListRunsOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ItemForListRunsOutput.  # noqa: E501


        :return: The status of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListRunsOutput.


        :param status: The status of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def submission_id(self):
        """Gets the submission_id of this ItemForListRunsOutput.  # noqa: E501


        :return: The submission_id of this ItemForListRunsOutput.  # noqa: E501
        :rtype: str
        """
        return self._submission_id

    @submission_id.setter
    def submission_id(self, submission_id):
        """Sets the submission_id of this ItemForListRunsOutput.


        :param submission_id: The submission_id of this ItemForListRunsOutput.  # noqa: E501
        :type: str
        """

        self._submission_id = submission_id

    @property
    def task_status(self):
        """Gets the task_status of this ItemForListRunsOutput.  # noqa: E501


        :return: The task_status of this ItemForListRunsOutput.  # noqa: E501
        :rtype: TaskStatusForListRunsOutput
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this ItemForListRunsOutput.


        :param task_status: The task_status of this ItemForListRunsOutput.  # noqa: E501
        :type: TaskStatusForListRunsOutput
        """

        self._task_status = task_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListRunsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListRunsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListRunsOutput):
            return True

        return self.to_dict() != other.to_dict()
