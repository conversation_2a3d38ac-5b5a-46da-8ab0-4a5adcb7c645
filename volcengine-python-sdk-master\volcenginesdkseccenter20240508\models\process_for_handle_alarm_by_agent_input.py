# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProcessForHandleAlarmByAgentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cmdline': 'str',
        'exe': 'str',
        'pid': 'int'
    }

    attribute_map = {
        'cmdline': 'Cmdline',
        'exe': 'Exe',
        'pid': 'Pid'
    }

    def __init__(self, cmdline=None, exe=None, pid=None, _configuration=None):  # noqa: E501
        """ProcessForHandleAlarmByAgentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cmdline = None
        self._exe = None
        self._pid = None
        self.discriminator = None

        if cmdline is not None:
            self.cmdline = cmdline
        if exe is not None:
            self.exe = exe
        if pid is not None:
            self.pid = pid

    @property
    def cmdline(self):
        """Gets the cmdline of this ProcessForHandleAlarmByAgentInput.  # noqa: E501


        :return: The cmdline of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this ProcessForHandleAlarmByAgentInput.


        :param cmdline: The cmdline of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def exe(self):
        """Gets the exe of this ProcessForHandleAlarmByAgentInput.  # noqa: E501


        :return: The exe of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this ProcessForHandleAlarmByAgentInput.


        :param exe: The exe of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def pid(self):
        """Gets the pid of this ProcessForHandleAlarmByAgentInput.  # noqa: E501


        :return: The pid of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :rtype: int
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this ProcessForHandleAlarmByAgentInput.


        :param pid: The pid of this ProcessForHandleAlarmByAgentInput.  # noqa: E501
        :type: int
        """

        self._pid = pid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProcessForHandleAlarmByAgentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProcessForHandleAlarmByAgentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProcessForHandleAlarmByAgentInput):
            return True

        return self.to_dict() != other.to_dict()
