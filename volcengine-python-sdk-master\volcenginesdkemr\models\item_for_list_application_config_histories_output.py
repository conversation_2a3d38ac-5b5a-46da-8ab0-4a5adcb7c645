# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListApplicationConfigHistoriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'before_config_item_value': 'str',
        'config_file_name': 'str',
        'config_item_key': 'str',
        'config_item_value': 'str',
        'config_version': 'str',
        'description': 'str',
        'effective_scope': 'EffectiveScopeForListApplicationConfigHistoriesOutput',
        'operator_id': 'str',
        'remark': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'before_config_item_value': 'BeforeConfigItemValue',
        'config_file_name': 'ConfigFileName',
        'config_item_key': 'ConfigItemKey',
        'config_item_value': 'ConfigItemValue',
        'config_version': 'ConfigVersion',
        'description': 'Description',
        'effective_scope': 'EffectiveScope',
        'operator_id': 'OperatorId',
        'remark': 'Remark',
        'update_time': 'updateTime'
    }

    def __init__(self, application_name=None, before_config_item_value=None, config_file_name=None, config_item_key=None, config_item_value=None, config_version=None, description=None, effective_scope=None, operator_id=None, remark=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListApplicationConfigHistoriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._before_config_item_value = None
        self._config_file_name = None
        self._config_item_key = None
        self._config_item_value = None
        self._config_version = None
        self._description = None
        self._effective_scope = None
        self._operator_id = None
        self._remark = None
        self._update_time = None
        self.discriminator = None

        if application_name is not None:
            self.application_name = application_name
        if before_config_item_value is not None:
            self.before_config_item_value = before_config_item_value
        if config_file_name is not None:
            self.config_file_name = config_file_name
        if config_item_key is not None:
            self.config_item_key = config_item_key
        if config_item_value is not None:
            self.config_item_value = config_item_value
        if config_version is not None:
            self.config_version = config_version
        if description is not None:
            self.description = description
        if effective_scope is not None:
            self.effective_scope = effective_scope
        if operator_id is not None:
            self.operator_id = operator_id
        if remark is not None:
            self.remark = remark
        if update_time is not None:
            self.update_time = update_time

    @property
    def application_name(self):
        """Gets the application_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The application_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ItemForListApplicationConfigHistoriesOutput.


        :param application_name: The application_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def before_config_item_value(self):
        """Gets the before_config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The before_config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._before_config_item_value

    @before_config_item_value.setter
    def before_config_item_value(self, before_config_item_value):
        """Sets the before_config_item_value of this ItemForListApplicationConfigHistoriesOutput.


        :param before_config_item_value: The before_config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._before_config_item_value = before_config_item_value

    @property
    def config_file_name(self):
        """Gets the config_file_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The config_file_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_file_name

    @config_file_name.setter
    def config_file_name(self, config_file_name):
        """Sets the config_file_name of this ItemForListApplicationConfigHistoriesOutput.


        :param config_file_name: The config_file_name of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._config_file_name = config_file_name

    @property
    def config_item_key(self):
        """Gets the config_item_key of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The config_item_key of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_item_key

    @config_item_key.setter
    def config_item_key(self, config_item_key):
        """Sets the config_item_key of this ItemForListApplicationConfigHistoriesOutput.


        :param config_item_key: The config_item_key of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._config_item_key = config_item_key

    @property
    def config_item_value(self):
        """Gets the config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_item_value

    @config_item_value.setter
    def config_item_value(self, config_item_value):
        """Sets the config_item_value of this ItemForListApplicationConfigHistoriesOutput.


        :param config_item_value: The config_item_value of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._config_item_value = config_item_value

    @property
    def config_version(self):
        """Gets the config_version of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The config_version of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_version

    @config_version.setter
    def config_version(self, config_version):
        """Sets the config_version of this ItemForListApplicationConfigHistoriesOutput.


        :param config_version: The config_version of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._config_version = config_version

    @property
    def description(self):
        """Gets the description of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The description of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListApplicationConfigHistoriesOutput.


        :param description: The description of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def effective_scope(self):
        """Gets the effective_scope of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The effective_scope of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: EffectiveScopeForListApplicationConfigHistoriesOutput
        """
        return self._effective_scope

    @effective_scope.setter
    def effective_scope(self, effective_scope):
        """Sets the effective_scope of this ItemForListApplicationConfigHistoriesOutput.


        :param effective_scope: The effective_scope of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: EffectiveScopeForListApplicationConfigHistoriesOutput
        """

        self._effective_scope = effective_scope

    @property
    def operator_id(self):
        """Gets the operator_id of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The operator_id of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._operator_id

    @operator_id.setter
    def operator_id(self, operator_id):
        """Sets the operator_id of this ItemForListApplicationConfigHistoriesOutput.


        :param operator_id: The operator_id of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._operator_id = operator_id

    @property
    def remark(self):
        """Gets the remark of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The remark of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this ItemForListApplicationConfigHistoriesOutput.


        :param remark: The remark of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501


        :return: The update_time of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListApplicationConfigHistoriesOutput.


        :param update_time: The update_time of this ItemForListApplicationConfigHistoriesOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListApplicationConfigHistoriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListApplicationConfigHistoriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListApplicationConfigHistoriesOutput):
            return True

        return self.to_dict() != other.to_dict()
