# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DBInstanceForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'charge_status': 'str',
        'charge_type': 'str',
        'closed_time': 'str',
        'config_servers': 'list[ConfigServerForDescribeDBInstanceDetailOutput]',
        'config_servers_id': 'str',
        'create_time': 'str',
        'db_engine': 'str',
        'db_engine_version': 'str',
        'db_engine_version_str': 'str',
        'expired_time': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'mongos': 'list[MongoForDescribeDBInstanceDetailOutput]',
        'mongos_id': 'str',
        'nodes': 'list[NodeForDescribeDBInstanceDetailOutput]',
        'private_endpoint': 'str',
        'project_name': 'str',
        'reclaim_time': 'str',
        'shards': 'list[ShardForDescribeDBInstanceDetailOutput]',
        'storage_type': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForDescribeDBInstanceDetailOutput]',
        'update_time': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'charge_status': 'ChargeStatus',
        'charge_type': 'ChargeType',
        'closed_time': 'ClosedTime',
        'config_servers': 'ConfigServers',
        'config_servers_id': 'ConfigServersId',
        'create_time': 'CreateTime',
        'db_engine': 'DBEngine',
        'db_engine_version': 'DBEngineVersion',
        'db_engine_version_str': 'DBEngineVersionStr',
        'expired_time': 'ExpiredTime',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'mongos': 'Mongos',
        'mongos_id': 'MongosId',
        'nodes': 'Nodes',
        'private_endpoint': 'PrivateEndpoint',
        'project_name': 'ProjectName',
        'reclaim_time': 'ReclaimTime',
        'shards': 'Shards',
        'storage_type': 'StorageType',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, auto_renew=None, charge_status=None, charge_type=None, closed_time=None, config_servers=None, config_servers_id=None, create_time=None, db_engine=None, db_engine_version=None, db_engine_version_str=None, expired_time=None, instance_id=None, instance_name=None, instance_status=None, instance_type=None, mongos=None, mongos_id=None, nodes=None, private_endpoint=None, project_name=None, reclaim_time=None, shards=None, storage_type=None, subnet_id=None, tags=None, update_time=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DBInstanceForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._charge_status = None
        self._charge_type = None
        self._closed_time = None
        self._config_servers = None
        self._config_servers_id = None
        self._create_time = None
        self._db_engine = None
        self._db_engine_version = None
        self._db_engine_version_str = None
        self._expired_time = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_type = None
        self._mongos = None
        self._mongos_id = None
        self._nodes = None
        self._private_endpoint = None
        self._project_name = None
        self._reclaim_time = None
        self._shards = None
        self._storage_type = None
        self._subnet_id = None
        self._tags = None
        self._update_time = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if auto_renew is not None:
            self.auto_renew = auto_renew
        if charge_status is not None:
            self.charge_status = charge_status
        if charge_type is not None:
            self.charge_type = charge_type
        if closed_time is not None:
            self.closed_time = closed_time
        if config_servers is not None:
            self.config_servers = config_servers
        if config_servers_id is not None:
            self.config_servers_id = config_servers_id
        if create_time is not None:
            self.create_time = create_time
        if db_engine is not None:
            self.db_engine = db_engine
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if db_engine_version_str is not None:
            self.db_engine_version_str = db_engine_version_str
        if expired_time is not None:
            self.expired_time = expired_time
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if mongos is not None:
            self.mongos = mongos
        if mongos_id is not None:
            self.mongos_id = mongos_id
        if nodes is not None:
            self.nodes = nodes
        if private_endpoint is not None:
            self.private_endpoint = private_endpoint
        if project_name is not None:
            self.project_name = project_name
        if reclaim_time is not None:
            self.reclaim_time = reclaim_time
        if shards is not None:
            self.shards = shards
        if storage_type is not None:
            self.storage_type = storage_type
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def auto_renew(self):
        """Gets the auto_renew of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The auto_renew of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param auto_renew: The auto_renew of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def charge_status(self):
        """Gets the charge_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The charge_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_status

    @charge_status.setter
    def charge_status(self, charge_status):
        """Sets the charge_status of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param charge_status: The charge_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._charge_status = charge_status

    @property
    def charge_type(self):
        """Gets the charge_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The charge_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param charge_type: The charge_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def closed_time(self):
        """Gets the closed_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The closed_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._closed_time

    @closed_time.setter
    def closed_time(self, closed_time):
        """Sets the closed_time of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param closed_time: The closed_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._closed_time = closed_time

    @property
    def config_servers(self):
        """Gets the config_servers of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The config_servers of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[ConfigServerForDescribeDBInstanceDetailOutput]
        """
        return self._config_servers

    @config_servers.setter
    def config_servers(self, config_servers):
        """Sets the config_servers of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param config_servers: The config_servers of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[ConfigServerForDescribeDBInstanceDetailOutput]
        """

        self._config_servers = config_servers

    @property
    def config_servers_id(self):
        """Gets the config_servers_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The config_servers_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_servers_id

    @config_servers_id.setter
    def config_servers_id(self, config_servers_id):
        """Sets the config_servers_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param config_servers_id: The config_servers_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._config_servers_id = config_servers_id

    @property
    def create_time(self):
        """Gets the create_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def db_engine(self):
        """Gets the db_engine of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine

    @db_engine.setter
    def db_engine(self, db_engine):
        """Sets the db_engine of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param db_engine: The db_engine of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine = db_engine

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param db_engine_version: The db_engine_version of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def db_engine_version_str(self):
        """Gets the db_engine_version_str of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version_str of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version_str

    @db_engine_version_str.setter
    def db_engine_version_str(self, db_engine_version_str):
        """Sets the db_engine_version_str of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param db_engine_version_str: The db_engine_version_str of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version_str = db_engine_version_str

    @property
    def expired_time(self):
        """Gets the expired_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The expired_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param expired_time: The expired_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param instance_status: The instance_status of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param instance_type: The instance_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def mongos(self):
        """Gets the mongos of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The mongos of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[MongoForDescribeDBInstanceDetailOutput]
        """
        return self._mongos

    @mongos.setter
    def mongos(self, mongos):
        """Sets the mongos of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param mongos: The mongos of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[MongoForDescribeDBInstanceDetailOutput]
        """

        self._mongos = mongos

    @property
    def mongos_id(self):
        """Gets the mongos_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The mongos_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._mongos_id

    @mongos_id.setter
    def mongos_id(self, mongos_id):
        """Sets the mongos_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param mongos_id: The mongos_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._mongos_id = mongos_id

    @property
    def nodes(self):
        """Gets the nodes of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The nodes of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[NodeForDescribeDBInstanceDetailOutput]
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param nodes: The nodes of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[NodeForDescribeDBInstanceDetailOutput]
        """

        self._nodes = nodes

    @property
    def private_endpoint(self):
        """Gets the private_endpoint of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The private_endpoint of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_endpoint

    @private_endpoint.setter
    def private_endpoint(self, private_endpoint):
        """Sets the private_endpoint of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param private_endpoint: The private_endpoint of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._private_endpoint = private_endpoint

    @property
    def project_name(self):
        """Gets the project_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The project_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param project_name: The project_name of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def reclaim_time(self):
        """Gets the reclaim_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The reclaim_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._reclaim_time

    @reclaim_time.setter
    def reclaim_time(self, reclaim_time):
        """Sets the reclaim_time of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param reclaim_time: The reclaim_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._reclaim_time = reclaim_time

    @property
    def shards(self):
        """Gets the shards of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The shards of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[ShardForDescribeDBInstanceDetailOutput]
        """
        return self._shards

    @shards.setter
    def shards(self, shards):
        """Sets the shards of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param shards: The shards of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[ShardForDescribeDBInstanceDetailOutput]
        """

        self._shards = shards

    @property
    def storage_type(self):
        """Gets the storage_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param storage_type: The storage_type of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The subnet_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param subnet_id: The subnet_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The tags of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[TagForDescribeDBInstanceDetailOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param tags: The tags of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[TagForDescribeDBInstanceDetailOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The update_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param update_time: The update_time of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vpc_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param vpc_id: The vpc_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DBInstanceForDescribeDBInstanceDetailOutput.


        :param zone_id: The zone_id of this DBInstanceForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DBInstanceForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DBInstanceForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DBInstanceForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
