# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GroupsInfoForDescribeGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'balance_algorithm': 'str',
        'description': 'str',
        'group_id': 'str',
        'protocol_type': 'str',
        'state': 'str',
        'tags': 'TagsForDescribeGroupsOutput'
    }

    attribute_map = {
        'balance_algorithm': 'BalanceAlgorithm',
        'description': 'Description',
        'group_id': 'GroupId',
        'protocol_type': 'ProtocolType',
        'state': 'State',
        'tags': 'Tags'
    }

    def __init__(self, balance_algorithm=None, description=None, group_id=None, protocol_type=None, state=None, tags=None, _configuration=None):  # noqa: E501
        """GroupsInfoForDescribeGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._balance_algorithm = None
        self._description = None
        self._group_id = None
        self._protocol_type = None
        self._state = None
        self._tags = None
        self.discriminator = None

        if balance_algorithm is not None:
            self.balance_algorithm = balance_algorithm
        if description is not None:
            self.description = description
        if group_id is not None:
            self.group_id = group_id
        if protocol_type is not None:
            self.protocol_type = protocol_type
        if state is not None:
            self.state = state
        if tags is not None:
            self.tags = tags

    @property
    def balance_algorithm(self):
        """Gets the balance_algorithm of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The balance_algorithm of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._balance_algorithm

    @balance_algorithm.setter
    def balance_algorithm(self, balance_algorithm):
        """Sets the balance_algorithm of this GroupsInfoForDescribeGroupsOutput.


        :param balance_algorithm: The balance_algorithm of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._balance_algorithm = balance_algorithm

    @property
    def description(self):
        """Gets the description of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The description of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GroupsInfoForDescribeGroupsOutput.


        :param description: The description of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def group_id(self):
        """Gets the group_id of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The group_id of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this GroupsInfoForDescribeGroupsOutput.


        :param group_id: The group_id of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def protocol_type(self):
        """Gets the protocol_type of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The protocol_type of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this GroupsInfoForDescribeGroupsOutput.


        :param protocol_type: The protocol_type of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._protocol_type = protocol_type

    @property
    def state(self):
        """Gets the state of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The state of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this GroupsInfoForDescribeGroupsOutput.


        :param state: The state of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def tags(self):
        """Gets the tags of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501


        :return: The tags of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :rtype: TagsForDescribeGroupsOutput
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this GroupsInfoForDescribeGroupsOutput.


        :param tags: The tags of this GroupsInfoForDescribeGroupsOutput.  # noqa: E501
        :type: TagsForDescribeGroupsOutput
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GroupsInfoForDescribeGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GroupsInfoForDescribeGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GroupsInfoForDescribeGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
