# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpstreamSpecForListUpstreamsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ai_provider': 'AIProviderForListUpstreamsOutput',
        'ecs_list': 'list[EcsListForListUpstreamsOutput]',
        'k8_s_service': 'K8SServiceForListUpstreamsOutput',
        'nacos_service': 'NacosServiceForListUpstreamsOutput',
        've_faas': 'VeFaasForListUpstreamsOutput'
    }

    attribute_map = {
        'ai_provider': 'AIProvider',
        'ecs_list': 'EcsList',
        'k8_s_service': 'K8SService',
        'nacos_service': 'NacosService',
        've_faas': 'VeFaas'
    }

    def __init__(self, ai_provider=None, ecs_list=None, k8_s_service=None, nacos_service=None, ve_faas=None, _configuration=None):  # noqa: E501
        """UpstreamSpecForListUpstreamsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ai_provider = None
        self._ecs_list = None
        self._k8_s_service = None
        self._nacos_service = None
        self._ve_faas = None
        self.discriminator = None

        if ai_provider is not None:
            self.ai_provider = ai_provider
        if ecs_list is not None:
            self.ecs_list = ecs_list
        if k8_s_service is not None:
            self.k8_s_service = k8_s_service
        if nacos_service is not None:
            self.nacos_service = nacos_service
        if ve_faas is not None:
            self.ve_faas = ve_faas

    @property
    def ai_provider(self):
        """Gets the ai_provider of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501


        :return: The ai_provider of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :rtype: AIProviderForListUpstreamsOutput
        """
        return self._ai_provider

    @ai_provider.setter
    def ai_provider(self, ai_provider):
        """Sets the ai_provider of this UpstreamSpecForListUpstreamsOutput.


        :param ai_provider: The ai_provider of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :type: AIProviderForListUpstreamsOutput
        """

        self._ai_provider = ai_provider

    @property
    def ecs_list(self):
        """Gets the ecs_list of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501


        :return: The ecs_list of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :rtype: list[EcsListForListUpstreamsOutput]
        """
        return self._ecs_list

    @ecs_list.setter
    def ecs_list(self, ecs_list):
        """Sets the ecs_list of this UpstreamSpecForListUpstreamsOutput.


        :param ecs_list: The ecs_list of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :type: list[EcsListForListUpstreamsOutput]
        """

        self._ecs_list = ecs_list

    @property
    def k8_s_service(self):
        """Gets the k8_s_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501


        :return: The k8_s_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :rtype: K8SServiceForListUpstreamsOutput
        """
        return self._k8_s_service

    @k8_s_service.setter
    def k8_s_service(self, k8_s_service):
        """Sets the k8_s_service of this UpstreamSpecForListUpstreamsOutput.


        :param k8_s_service: The k8_s_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :type: K8SServiceForListUpstreamsOutput
        """

        self._k8_s_service = k8_s_service

    @property
    def nacos_service(self):
        """Gets the nacos_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501


        :return: The nacos_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :rtype: NacosServiceForListUpstreamsOutput
        """
        return self._nacos_service

    @nacos_service.setter
    def nacos_service(self, nacos_service):
        """Sets the nacos_service of this UpstreamSpecForListUpstreamsOutput.


        :param nacos_service: The nacos_service of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :type: NacosServiceForListUpstreamsOutput
        """

        self._nacos_service = nacos_service

    @property
    def ve_faas(self):
        """Gets the ve_faas of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501


        :return: The ve_faas of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :rtype: VeFaasForListUpstreamsOutput
        """
        return self._ve_faas

    @ve_faas.setter
    def ve_faas(self, ve_faas):
        """Sets the ve_faas of this UpstreamSpecForListUpstreamsOutput.


        :param ve_faas: The ve_faas of this UpstreamSpecForListUpstreamsOutput.  # noqa: E501
        :type: VeFaasForListUpstreamsOutput
        """

        self._ve_faas = ve_faas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpstreamSpecForListUpstreamsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpstreamSpecForListUpstreamsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpstreamSpecForListUpstreamsOutput):
            return True

        return self.to_dict() != other.to_dict()
