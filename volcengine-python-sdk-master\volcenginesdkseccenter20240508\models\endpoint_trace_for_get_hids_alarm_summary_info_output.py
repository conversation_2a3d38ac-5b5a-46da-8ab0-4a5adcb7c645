# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointTraceForGetHidsAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_id': 'str',
        'result_list': 'list[ResultListForGetHidsAlarmSummaryInfoOutput]',
        'trace_id': 'str'
    }

    attribute_map = {
        'alarm_id': 'AlarmID',
        'result_list': 'ResultList',
        'trace_id': 'TraceID'
    }

    def __init__(self, alarm_id=None, result_list=None, trace_id=None, _configuration=None):  # noqa: E501
        """EndpointTraceForGetHidsAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_id = None
        self._result_list = None
        self._trace_id = None
        self.discriminator = None

        if alarm_id is not None:
            self.alarm_id = alarm_id
        if result_list is not None:
            self.result_list = result_list
        if trace_id is not None:
            self.trace_id = trace_id

    @property
    def alarm_id(self):
        """Gets the alarm_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alarm_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.


        :param alarm_id: The alarm_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def result_list(self):
        """Gets the result_list of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The result_list of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[ResultListForGetHidsAlarmSummaryInfoOutput]
        """
        return self._result_list

    @result_list.setter
    def result_list(self, result_list):
        """Sets the result_list of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.


        :param result_list: The result_list of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[ResultListForGetHidsAlarmSummaryInfoOutput]
        """

        self._result_list = result_list

    @property
    def trace_id(self):
        """Gets the trace_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The trace_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.


        :param trace_id: The trace_id of this EndpointTraceForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointTraceForGetHidsAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointTraceForGetHidsAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointTraceForGetHidsAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
