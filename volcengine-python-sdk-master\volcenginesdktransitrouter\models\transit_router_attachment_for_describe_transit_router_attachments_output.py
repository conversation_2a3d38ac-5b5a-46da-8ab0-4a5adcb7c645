# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'auto_publish_route_enabled': 'bool',
        'business_status': 'str',
        'creation_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'ipv6_enabled': 'bool',
        'overdue_time': 'str',
        'resource_id': 'str',
        'resource_type': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeTransitRouterAttachmentsOutput]',
        'transit_router_attachment_id': 'str',
        'transit_router_attachment_name': 'str',
        'transit_router_forward_policy_table_id': 'str',
        'transit_router_id': 'str',
        'transit_router_route_table_id': 'str',
        'transit_router_traffic_qos_marking_policy_id': 'str',
        'transit_router_traffic_qos_queue_policy_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'auto_publish_route_enabled': 'AutoPublishRouteEnabled',
        'business_status': 'BusinessStatus',
        'creation_time': 'CreationTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'ipv6_enabled': 'Ipv6Enabled',
        'overdue_time': 'OverdueTime',
        'resource_id': 'ResourceId',
        'resource_type': 'ResourceType',
        'status': 'Status',
        'tags': 'Tags',
        'transit_router_attachment_id': 'TransitRouterAttachmentId',
        'transit_router_attachment_name': 'TransitRouterAttachmentName',
        'transit_router_forward_policy_table_id': 'TransitRouterForwardPolicyTableId',
        'transit_router_id': 'TransitRouterId',
        'transit_router_route_table_id': 'TransitRouterRouteTableId',
        'transit_router_traffic_qos_marking_policy_id': 'TransitRouterTrafficQosMarkingPolicyId',
        'transit_router_traffic_qos_queue_policy_id': 'TransitRouterTrafficQosQueuePolicyId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, auto_publish_route_enabled=None, business_status=None, creation_time=None, deleted_time=None, description=None, ipv6_enabled=None, overdue_time=None, resource_id=None, resource_type=None, status=None, tags=None, transit_router_attachment_id=None, transit_router_attachment_name=None, transit_router_forward_policy_table_id=None, transit_router_id=None, transit_router_route_table_id=None, transit_router_traffic_qos_marking_policy_id=None, transit_router_traffic_qos_queue_policy_id=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._auto_publish_route_enabled = None
        self._business_status = None
        self._creation_time = None
        self._deleted_time = None
        self._description = None
        self._ipv6_enabled = None
        self._overdue_time = None
        self._resource_id = None
        self._resource_type = None
        self._status = None
        self._tags = None
        self._transit_router_attachment_id = None
        self._transit_router_attachment_name = None
        self._transit_router_forward_policy_table_id = None
        self._transit_router_id = None
        self._transit_router_route_table_id = None
        self._transit_router_traffic_qos_marking_policy_id = None
        self._transit_router_traffic_qos_queue_policy_id = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if auto_publish_route_enabled is not None:
            self.auto_publish_route_enabled = auto_publish_route_enabled
        if business_status is not None:
            self.business_status = business_status
        if creation_time is not None:
            self.creation_time = creation_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if ipv6_enabled is not None:
            self.ipv6_enabled = ipv6_enabled
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_type is not None:
            self.resource_type = resource_type
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if transit_router_attachment_id is not None:
            self.transit_router_attachment_id = transit_router_attachment_id
        if transit_router_attachment_name is not None:
            self.transit_router_attachment_name = transit_router_attachment_name
        if transit_router_forward_policy_table_id is not None:
            self.transit_router_forward_policy_table_id = transit_router_forward_policy_table_id
        if transit_router_id is not None:
            self.transit_router_id = transit_router_id
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id
        if transit_router_traffic_qos_marking_policy_id is not None:
            self.transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id
        if transit_router_traffic_qos_queue_policy_id is not None:
            self.transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The account_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param account_id: The account_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def auto_publish_route_enabled(self):
        """Gets the auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_publish_route_enabled

    @auto_publish_route_enabled.setter
    def auto_publish_route_enabled(self, auto_publish_route_enabled):
        """Sets the auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param auto_publish_route_enabled: The auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: bool
        """

        self._auto_publish_route_enabled = auto_publish_route_enabled

    @property
    def business_status(self):
        """Gets the business_status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The business_status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param business_status: The business_status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param creation_time: The creation_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The deleted_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param deleted_time: The deleted_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The description of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param description: The description of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ipv6_enabled(self):
        """Gets the ipv6_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The ipv6_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._ipv6_enabled

    @ipv6_enabled.setter
    def ipv6_enabled(self, ipv6_enabled):
        """Sets the ipv6_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param ipv6_enabled: The ipv6_enabled of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: bool
        """

        self._ipv6_enabled = ipv6_enabled

    @property
    def overdue_time(self):
        """Gets the overdue_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The overdue_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param overdue_time: The overdue_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def resource_id(self):
        """Gets the resource_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The resource_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param resource_id: The resource_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_type(self):
        """Gets the resource_type of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The resource_type of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param resource_type: The resource_type of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def status(self):
        """Gets the status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param status: The status of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The tags of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: list[TagForDescribeTransitRouterAttachmentsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param tags: The tags of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: list[TagForDescribeTransitRouterAttachmentsOutput]
        """

        self._tags = tags

    @property
    def transit_router_attachment_id(self):
        """Gets the transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_id

    @transit_router_attachment_id.setter
    def transit_router_attachment_id(self, transit_router_attachment_id):
        """Sets the transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_attachment_id: The transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_id = transit_router_attachment_id

    @property
    def transit_router_attachment_name(self):
        """Gets the transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_name

    @transit_router_attachment_name.setter
    def transit_router_attachment_name(self, transit_router_attachment_name):
        """Sets the transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_attachment_name: The transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_name = transit_router_attachment_name

    @property
    def transit_router_forward_policy_table_id(self):
        """Gets the transit_router_forward_policy_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_forward_policy_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_table_id

    @transit_router_forward_policy_table_id.setter
    def transit_router_forward_policy_table_id(self, transit_router_forward_policy_table_id):
        """Sets the transit_router_forward_policy_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_forward_policy_table_id: The transit_router_forward_policy_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_forward_policy_table_id = transit_router_forward_policy_table_id

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_id: The transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_id = transit_router_id

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_route_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_route_table_id: The transit_router_route_table_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    @property
    def transit_router_traffic_qos_marking_policy_id(self):
        """Gets the transit_router_traffic_qos_marking_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_id

    @transit_router_traffic_qos_marking_policy_id.setter
    def transit_router_traffic_qos_marking_policy_id(self, transit_router_traffic_qos_marking_policy_id):
        """Sets the transit_router_traffic_qos_marking_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_traffic_qos_marking_policy_id: The transit_router_traffic_qos_marking_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    @property
    def transit_router_traffic_qos_queue_policy_id(self):
        """Gets the transit_router_traffic_qos_queue_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_policy_id

    @transit_router_traffic_qos_queue_policy_id.setter
    def transit_router_traffic_qos_queue_policy_id(self, transit_router_traffic_qos_queue_policy_id):
        """Sets the transit_router_traffic_qos_queue_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param transit_router_traffic_qos_queue_policy_id: The transit_router_traffic_qos_queue_policy_id of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501


        :return: The update_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.


        :param update_time: The update_time of this TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterAttachmentForDescribeTransitRouterAttachmentsOutput):
            return True

        return self.to_dict() != other.to_dict()
