# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UAInfoForUpdateActivityProductInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ua_address': 'str',
        'ua_name': 'str'
    }

    attribute_map = {
        'ua_address': 'UAAddress',
        'ua_name': 'UAName'
    }

    def __init__(self, ua_address=None, ua_name=None, _configuration=None):  # noqa: E501
        """UAInfoForUpdateActivityProductInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ua_address = None
        self._ua_name = None
        self.discriminator = None

        if ua_address is not None:
            self.ua_address = ua_address
        if ua_name is not None:
            self.ua_name = ua_name

    @property
    def ua_address(self):
        """Gets the ua_address of this UAInfoForUpdateActivityProductInput.  # noqa: E501


        :return: The ua_address of this UAInfoForUpdateActivityProductInput.  # noqa: E501
        :rtype: str
        """
        return self._ua_address

    @ua_address.setter
    def ua_address(self, ua_address):
        """Sets the ua_address of this UAInfoForUpdateActivityProductInput.


        :param ua_address: The ua_address of this UAInfoForUpdateActivityProductInput.  # noqa: E501
        :type: str
        """

        self._ua_address = ua_address

    @property
    def ua_name(self):
        """Gets the ua_name of this UAInfoForUpdateActivityProductInput.  # noqa: E501


        :return: The ua_name of this UAInfoForUpdateActivityProductInput.  # noqa: E501
        :rtype: str
        """
        return self._ua_name

    @ua_name.setter
    def ua_name(self, ua_name):
        """Sets the ua_name of this UAInfoForUpdateActivityProductInput.


        :param ua_name: The ua_name of this UAInfoForUpdateActivityProductInput.  # noqa: E501
        :type: str
        """

        self._ua_name = ua_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UAInfoForUpdateActivityProductInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UAInfoForUpdateActivityProductInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UAInfoForUpdateActivityProductInput):
            return True

        return self.to_dict() != other.to_dict()
