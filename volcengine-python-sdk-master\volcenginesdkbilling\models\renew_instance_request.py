# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RenewInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'instance_id': 'str',
        'product': 'str',
        'renew_related_instance': 'bool',
        'renewal_duration': 'int',
        'renewal_duration_unit': 'str',
        'united_expire_day': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'instance_id': 'InstanceID',
        'product': 'Product',
        'renew_related_instance': 'RenewRelatedInstance',
        'renewal_duration': 'RenewalDuration',
        'renewal_duration_unit': 'RenewalDurationUnit',
        'united_expire_day': 'UnitedExpireDay'
    }

    def __init__(self, client_token=None, instance_id=None, product=None, renew_related_instance=None, renewal_duration=None, renewal_duration_unit=None, united_expire_day=None, _configuration=None):  # noqa: E501
        """RenewInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._instance_id = None
        self._product = None
        self._renew_related_instance = None
        self._renewal_duration = None
        self._renewal_duration_unit = None
        self._united_expire_day = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.instance_id = instance_id
        self.product = product
        if renew_related_instance is not None:
            self.renew_related_instance = renew_related_instance
        if renewal_duration is not None:
            self.renewal_duration = renewal_duration
        self.renewal_duration_unit = renewal_duration_unit
        if united_expire_day is not None:
            self.united_expire_day = united_expire_day

    @property
    def client_token(self):
        """Gets the client_token of this RenewInstanceRequest.  # noqa: E501


        :return: The client_token of this RenewInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this RenewInstanceRequest.


        :param client_token: The client_token of this RenewInstanceRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this RenewInstanceRequest.  # noqa: E501


        :return: The instance_id of this RenewInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this RenewInstanceRequest.


        :param instance_id: The instance_id of this RenewInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def product(self):
        """Gets the product of this RenewInstanceRequest.  # noqa: E501


        :return: The product of this RenewInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this RenewInstanceRequest.


        :param product: The product of this RenewInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product is None:
            raise ValueError("Invalid value for `product`, must not be `None`")  # noqa: E501

        self._product = product

    @property
    def renew_related_instance(self):
        """Gets the renew_related_instance of this RenewInstanceRequest.  # noqa: E501


        :return: The renew_related_instance of this RenewInstanceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._renew_related_instance

    @renew_related_instance.setter
    def renew_related_instance(self, renew_related_instance):
        """Sets the renew_related_instance of this RenewInstanceRequest.


        :param renew_related_instance: The renew_related_instance of this RenewInstanceRequest.  # noqa: E501
        :type: bool
        """

        self._renew_related_instance = renew_related_instance

    @property
    def renewal_duration(self):
        """Gets the renewal_duration of this RenewInstanceRequest.  # noqa: E501


        :return: The renewal_duration of this RenewInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._renewal_duration

    @renewal_duration.setter
    def renewal_duration(self, renewal_duration):
        """Sets the renewal_duration of this RenewInstanceRequest.


        :param renewal_duration: The renewal_duration of this RenewInstanceRequest.  # noqa: E501
        :type: int
        """

        self._renewal_duration = renewal_duration

    @property
    def renewal_duration_unit(self):
        """Gets the renewal_duration_unit of this RenewInstanceRequest.  # noqa: E501


        :return: The renewal_duration_unit of this RenewInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._renewal_duration_unit

    @renewal_duration_unit.setter
    def renewal_duration_unit(self, renewal_duration_unit):
        """Sets the renewal_duration_unit of this RenewInstanceRequest.


        :param renewal_duration_unit: The renewal_duration_unit of this RenewInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and renewal_duration_unit is None:
            raise ValueError("Invalid value for `renewal_duration_unit`, must not be `None`")  # noqa: E501

        self._renewal_duration_unit = renewal_duration_unit

    @property
    def united_expire_day(self):
        """Gets the united_expire_day of this RenewInstanceRequest.  # noqa: E501


        :return: The united_expire_day of this RenewInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._united_expire_day

    @united_expire_day.setter
    def united_expire_day(self, united_expire_day):
        """Sets the united_expire_day of this RenewInstanceRequest.


        :param united_expire_day: The united_expire_day of this RenewInstanceRequest.  # noqa: E501
        :type: str
        """

        self._united_expire_day = united_expire_day

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RenewInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RenewInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RenewInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
