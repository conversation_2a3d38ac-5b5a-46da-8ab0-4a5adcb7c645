# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNetworkLoadBalancerAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cross_zone_enabled': 'bool',
        'description': 'str',
        'ip_address_version': 'str',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'modification_protection_status': 'str'
    }

    attribute_map = {
        'cross_zone_enabled': 'CrossZoneEnabled',
        'description': 'Description',
        'ip_address_version': 'IpAddressVersion',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_name': 'LoadBalancerName',
        'modification_protection_status': 'ModificationProtectionStatus'
    }

    def __init__(self, cross_zone_enabled=None, description=None, ip_address_version=None, load_balancer_id=None, load_balancer_name=None, modification_protection_status=None, _configuration=None):  # noqa: E501
        """ModifyNetworkLoadBalancerAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cross_zone_enabled = None
        self._description = None
        self._ip_address_version = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._modification_protection_status = None
        self.discriminator = None

        if cross_zone_enabled is not None:
            self.cross_zone_enabled = cross_zone_enabled
        if description is not None:
            self.description = description
        if ip_address_version is not None:
            self.ip_address_version = ip_address_version
        self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status

    @property
    def cross_zone_enabled(self):
        """Gets the cross_zone_enabled of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The cross_zone_enabled of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._cross_zone_enabled

    @cross_zone_enabled.setter
    def cross_zone_enabled(self, cross_zone_enabled):
        """Sets the cross_zone_enabled of this ModifyNetworkLoadBalancerAttributesRequest.


        :param cross_zone_enabled: The cross_zone_enabled of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._cross_zone_enabled = cross_zone_enabled

    @property
    def description(self):
        """Gets the description of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The description of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyNetworkLoadBalancerAttributesRequest.


        :param description: The description of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def ip_address_version(self):
        """Gets the ip_address_version of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The ip_address_version of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_version

    @ip_address_version.setter
    def ip_address_version(self, ip_address_version):
        """Sets the ip_address_version of this ModifyNetworkLoadBalancerAttributesRequest.


        :param ip_address_version: The ip_address_version of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._ip_address_version = ip_address_version

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_id of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this ModifyNetworkLoadBalancerAttributesRequest.


        :param load_balancer_id: The load_balancer_id of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_name of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this ModifyNetworkLoadBalancerAttributesRequest.


        :param load_balancer_name: The load_balancer_name of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501


        :return: The modification_protection_status of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this ModifyNetworkLoadBalancerAttributesRequest.


        :param modification_protection_status: The modification_protection_status of this ModifyNetworkLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNetworkLoadBalancerAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNetworkLoadBalancerAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNetworkLoadBalancerAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
