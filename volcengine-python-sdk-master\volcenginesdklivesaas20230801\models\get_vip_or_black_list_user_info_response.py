# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVipOrBlackListUserInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'award_user_info_list': 'list[AwardUserInfoListForGetVipOrBlackListUserInfoOutput]',
        'black_user_info_id': 'int',
        'role': 'int',
        'user_count': 'int',
        'vip_user_info_id': 'int'
    }

    attribute_map = {
        'award_user_info_list': 'AwardUserInfoList',
        'black_user_info_id': 'BlackUserInfoId',
        'role': 'Role',
        'user_count': 'UserCount',
        'vip_user_info_id': 'VipUserInfoId'
    }

    def __init__(self, award_user_info_list=None, black_user_info_id=None, role=None, user_count=None, vip_user_info_id=None, _configuration=None):  # noqa: E501
        """GetVipOrBlackListUserInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._award_user_info_list = None
        self._black_user_info_id = None
        self._role = None
        self._user_count = None
        self._vip_user_info_id = None
        self.discriminator = None

        if award_user_info_list is not None:
            self.award_user_info_list = award_user_info_list
        if black_user_info_id is not None:
            self.black_user_info_id = black_user_info_id
        if role is not None:
            self.role = role
        if user_count is not None:
            self.user_count = user_count
        if vip_user_info_id is not None:
            self.vip_user_info_id = vip_user_info_id

    @property
    def award_user_info_list(self):
        """Gets the award_user_info_list of this GetVipOrBlackListUserInfoResponse.  # noqa: E501


        :return: The award_user_info_list of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :rtype: list[AwardUserInfoListForGetVipOrBlackListUserInfoOutput]
        """
        return self._award_user_info_list

    @award_user_info_list.setter
    def award_user_info_list(self, award_user_info_list):
        """Sets the award_user_info_list of this GetVipOrBlackListUserInfoResponse.


        :param award_user_info_list: The award_user_info_list of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :type: list[AwardUserInfoListForGetVipOrBlackListUserInfoOutput]
        """

        self._award_user_info_list = award_user_info_list

    @property
    def black_user_info_id(self):
        """Gets the black_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501


        :return: The black_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._black_user_info_id

    @black_user_info_id.setter
    def black_user_info_id(self, black_user_info_id):
        """Sets the black_user_info_id of this GetVipOrBlackListUserInfoResponse.


        :param black_user_info_id: The black_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :type: int
        """

        self._black_user_info_id = black_user_info_id

    @property
    def role(self):
        """Gets the role of this GetVipOrBlackListUserInfoResponse.  # noqa: E501


        :return: The role of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this GetVipOrBlackListUserInfoResponse.


        :param role: The role of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :type: int
        """

        self._role = role

    @property
    def user_count(self):
        """Gets the user_count of this GetVipOrBlackListUserInfoResponse.  # noqa: E501


        :return: The user_count of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._user_count

    @user_count.setter
    def user_count(self, user_count):
        """Sets the user_count of this GetVipOrBlackListUserInfoResponse.


        :param user_count: The user_count of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :type: int
        """

        self._user_count = user_count

    @property
    def vip_user_info_id(self):
        """Gets the vip_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501


        :return: The vip_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._vip_user_info_id

    @vip_user_info_id.setter
    def vip_user_info_id(self, vip_user_info_id):
        """Sets the vip_user_info_id of this GetVipOrBlackListUserInfoResponse.


        :param vip_user_info_id: The vip_user_info_id of this GetVipOrBlackListUserInfoResponse.  # noqa: E501
        :type: int
        """

        self._vip_user_info_id = vip_user_info_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVipOrBlackListUserInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVipOrBlackListUserInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVipOrBlackListUserInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
