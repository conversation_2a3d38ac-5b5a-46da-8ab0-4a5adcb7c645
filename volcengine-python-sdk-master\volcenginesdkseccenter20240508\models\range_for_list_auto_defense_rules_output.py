# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RangeForListAutoDefenseRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host_list': 'list[HostListForListAutoDefenseRulesOutput]',
        'host_num': 'int',
        'if_all_host': 'bool',
        'leaf_group_id_list': 'list[str]',
        'single_group_path': 'str'
    }

    attribute_map = {
        'host_list': 'HostList',
        'host_num': 'HostNum',
        'if_all_host': 'IfAllHost',
        'leaf_group_id_list': 'LeafGroupIDList',
        'single_group_path': 'SingleGroupPath'
    }

    def __init__(self, host_list=None, host_num=None, if_all_host=None, leaf_group_id_list=None, single_group_path=None, _configuration=None):  # noqa: E501
        """RangeForListAutoDefenseRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host_list = None
        self._host_num = None
        self._if_all_host = None
        self._leaf_group_id_list = None
        self._single_group_path = None
        self.discriminator = None

        if host_list is not None:
            self.host_list = host_list
        if host_num is not None:
            self.host_num = host_num
        if if_all_host is not None:
            self.if_all_host = if_all_host
        if leaf_group_id_list is not None:
            self.leaf_group_id_list = leaf_group_id_list
        if single_group_path is not None:
            self.single_group_path = single_group_path

    @property
    def host_list(self):
        """Gets the host_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The host_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: list[HostListForListAutoDefenseRulesOutput]
        """
        return self._host_list

    @host_list.setter
    def host_list(self, host_list):
        """Sets the host_list of this RangeForListAutoDefenseRulesOutput.


        :param host_list: The host_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :type: list[HostListForListAutoDefenseRulesOutput]
        """

        self._host_list = host_list

    @property
    def host_num(self):
        """Gets the host_num of this RangeForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The host_num of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_num

    @host_num.setter
    def host_num(self, host_num):
        """Sets the host_num of this RangeForListAutoDefenseRulesOutput.


        :param host_num: The host_num of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :type: int
        """

        self._host_num = host_num

    @property
    def if_all_host(self):
        """Gets the if_all_host of this RangeForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The if_all_host of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._if_all_host

    @if_all_host.setter
    def if_all_host(self, if_all_host):
        """Sets the if_all_host of this RangeForListAutoDefenseRulesOutput.


        :param if_all_host: The if_all_host of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :type: bool
        """

        self._if_all_host = if_all_host

    @property
    def leaf_group_id_list(self):
        """Gets the leaf_group_id_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The leaf_group_id_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_id_list

    @leaf_group_id_list.setter
    def leaf_group_id_list(self, leaf_group_id_list):
        """Sets the leaf_group_id_list of this RangeForListAutoDefenseRulesOutput.


        :param leaf_group_id_list: The leaf_group_id_list of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_id_list = leaf_group_id_list

    @property
    def single_group_path(self):
        """Gets the single_group_path of this RangeForListAutoDefenseRulesOutput.  # noqa: E501


        :return: The single_group_path of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._single_group_path

    @single_group_path.setter
    def single_group_path(self, single_group_path):
        """Sets the single_group_path of this RangeForListAutoDefenseRulesOutput.


        :param single_group_path: The single_group_path of this RangeForListAutoDefenseRulesOutput.  # noqa: E501
        :type: str
        """

        self._single_group_path = single_group_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RangeForListAutoDefenseRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RangeForListAutoDefenseRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RangeForListAutoDefenseRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
