# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RemoveTagsFromResourceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all': 'bool',
        'instance_ids': 'list[str]',
        'tag_keys': 'list[str]'
    }

    attribute_map = {
        'all': 'All',
        'instance_ids': 'InstanceIds',
        'tag_keys': 'TagKeys'
    }

    def __init__(self, all=None, instance_ids=None, tag_keys=None, _configuration=None):  # noqa: E501
        """RemoveTagsFromResourceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all = None
        self._instance_ids = None
        self._tag_keys = None
        self.discriminator = None

        if all is not None:
            self.all = all
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if tag_keys is not None:
            self.tag_keys = tag_keys

    @property
    def all(self):
        """Gets the all of this RemoveTagsFromResourceRequest.  # noqa: E501


        :return: The all of this RemoveTagsFromResourceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all

    @all.setter
    def all(self, all):
        """Sets the all of this RemoveTagsFromResourceRequest.


        :param all: The all of this RemoveTagsFromResourceRequest.  # noqa: E501
        :type: bool
        """

        self._all = all

    @property
    def instance_ids(self):
        """Gets the instance_ids of this RemoveTagsFromResourceRequest.  # noqa: E501


        :return: The instance_ids of this RemoveTagsFromResourceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this RemoveTagsFromResourceRequest.


        :param instance_ids: The instance_ids of this RemoveTagsFromResourceRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def tag_keys(self):
        """Gets the tag_keys of this RemoveTagsFromResourceRequest.  # noqa: E501


        :return: The tag_keys of this RemoveTagsFromResourceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag_keys

    @tag_keys.setter
    def tag_keys(self, tag_keys):
        """Sets the tag_keys of this RemoveTagsFromResourceRequest.


        :param tag_keys: The tag_keys of this RemoveTagsFromResourceRequest.  # noqa: E501
        :type: list[str]
        """

        self._tag_keys = tag_keys

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RemoveTagsFromResourceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RemoveTagsFromResourceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RemoveTagsFromResourceRequest):
            return True

        return self.to_dict() != other.to_dict()
