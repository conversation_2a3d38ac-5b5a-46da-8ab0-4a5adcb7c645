# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListVulByPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'action': 'str',
        'control_time': 'int',
        'create_time': 'int',
        'cve_id': 'list[str]',
        'cwpp_id': 'str',
        'infect_num': 'int',
        'infect_status': 'InfectStatusForListVulByPodOutput',
        'level': 'str',
        'operate_reason': 'str',
        'status': 'str',
        'tag': 'list[str]',
        'update_time': 'int',
        'vuln_name': 'str',
        'vuln_name_en': 'str',
        'vuln_type': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'action': 'Action',
        'control_time': 'ControlTime',
        'create_time': 'CreateTime',
        'cve_id': 'CveID',
        'cwpp_id': 'CwppID',
        'infect_num': 'InfectNum',
        'infect_status': 'InfectStatus',
        'level': 'Level',
        'operate_reason': 'OperateReason',
        'status': 'Status',
        'tag': 'Tag',
        'update_time': 'UpdateTime',
        'vuln_name': 'VulnName',
        'vuln_name_en': 'VulnNameEn',
        'vuln_type': 'VulnType'
    }

    def __init__(self, account_id=None, action=None, control_time=None, create_time=None, cve_id=None, cwpp_id=None, infect_num=None, infect_status=None, level=None, operate_reason=None, status=None, tag=None, update_time=None, vuln_name=None, vuln_name_en=None, vuln_type=None, _configuration=None):  # noqa: E501
        """DataForListVulByPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._action = None
        self._control_time = None
        self._create_time = None
        self._cve_id = None
        self._cwpp_id = None
        self._infect_num = None
        self._infect_status = None
        self._level = None
        self._operate_reason = None
        self._status = None
        self._tag = None
        self._update_time = None
        self._vuln_name = None
        self._vuln_name_en = None
        self._vuln_type = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if action is not None:
            self.action = action
        if control_time is not None:
            self.control_time = control_time
        if create_time is not None:
            self.create_time = create_time
        if cve_id is not None:
            self.cve_id = cve_id
        if cwpp_id is not None:
            self.cwpp_id = cwpp_id
        if infect_num is not None:
            self.infect_num = infect_num
        if infect_status is not None:
            self.infect_status = infect_status
        if level is not None:
            self.level = level
        if operate_reason is not None:
            self.operate_reason = operate_reason
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if update_time is not None:
            self.update_time = update_time
        if vuln_name is not None:
            self.vuln_name = vuln_name
        if vuln_name_en is not None:
            self.vuln_name_en = vuln_name_en
        if vuln_type is not None:
            self.vuln_type = vuln_type

    @property
    def account_id(self):
        """Gets the account_id of this DataForListVulByPodOutput.  # noqa: E501


        :return: The account_id of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListVulByPodOutput.


        :param account_id: The account_id of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def action(self):
        """Gets the action of this DataForListVulByPodOutput.  # noqa: E501


        :return: The action of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this DataForListVulByPodOutput.


        :param action: The action of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def control_time(self):
        """Gets the control_time of this DataForListVulByPodOutput.  # noqa: E501


        :return: The control_time of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._control_time

    @control_time.setter
    def control_time(self, control_time):
        """Sets the control_time of this DataForListVulByPodOutput.


        :param control_time: The control_time of this DataForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._control_time = control_time

    @property
    def create_time(self):
        """Gets the create_time of this DataForListVulByPodOutput.  # noqa: E501


        :return: The create_time of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListVulByPodOutput.


        :param create_time: The create_time of this DataForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def cve_id(self):
        """Gets the cve_id of this DataForListVulByPodOutput.  # noqa: E501


        :return: The cve_id of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this DataForListVulByPodOutput.


        :param cve_id: The cve_id of this DataForListVulByPodOutput.  # noqa: E501
        :type: list[str]
        """

        self._cve_id = cve_id

    @property
    def cwpp_id(self):
        """Gets the cwpp_id of this DataForListVulByPodOutput.  # noqa: E501


        :return: The cwpp_id of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._cwpp_id

    @cwpp_id.setter
    def cwpp_id(self, cwpp_id):
        """Sets the cwpp_id of this DataForListVulByPodOutput.


        :param cwpp_id: The cwpp_id of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._cwpp_id = cwpp_id

    @property
    def infect_num(self):
        """Gets the infect_num of this DataForListVulByPodOutput.  # noqa: E501


        :return: The infect_num of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._infect_num

    @infect_num.setter
    def infect_num(self, infect_num):
        """Sets the infect_num of this DataForListVulByPodOutput.


        :param infect_num: The infect_num of this DataForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._infect_num = infect_num

    @property
    def infect_status(self):
        """Gets the infect_status of this DataForListVulByPodOutput.  # noqa: E501


        :return: The infect_status of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: InfectStatusForListVulByPodOutput
        """
        return self._infect_status

    @infect_status.setter
    def infect_status(self, infect_status):
        """Sets the infect_status of this DataForListVulByPodOutput.


        :param infect_status: The infect_status of this DataForListVulByPodOutput.  # noqa: E501
        :type: InfectStatusForListVulByPodOutput
        """

        self._infect_status = infect_status

    @property
    def level(self):
        """Gets the level of this DataForListVulByPodOutput.  # noqa: E501


        :return: The level of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this DataForListVulByPodOutput.


        :param level: The level of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def operate_reason(self):
        """Gets the operate_reason of this DataForListVulByPodOutput.  # noqa: E501


        :return: The operate_reason of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_reason

    @operate_reason.setter
    def operate_reason(self, operate_reason):
        """Sets the operate_reason of this DataForListVulByPodOutput.


        :param operate_reason: The operate_reason of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._operate_reason = operate_reason

    @property
    def status(self):
        """Gets the status of this DataForListVulByPodOutput.  # noqa: E501


        :return: The status of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListVulByPodOutput.


        :param status: The status of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this DataForListVulByPodOutput.  # noqa: E501


        :return: The tag of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this DataForListVulByPodOutput.


        :param tag: The tag of this DataForListVulByPodOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def update_time(self):
        """Gets the update_time of this DataForListVulByPodOutput.  # noqa: E501


        :return: The update_time of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListVulByPodOutput.


        :param update_time: The update_time of this DataForListVulByPodOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def vuln_name(self):
        """Gets the vuln_name of this DataForListVulByPodOutput.  # noqa: E501


        :return: The vuln_name of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this DataForListVulByPodOutput.


        :param vuln_name: The vuln_name of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    @property
    def vuln_name_en(self):
        """Gets the vuln_name_en of this DataForListVulByPodOutput.  # noqa: E501


        :return: The vuln_name_en of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name_en

    @vuln_name_en.setter
    def vuln_name_en(self, vuln_name_en):
        """Sets the vuln_name_en of this DataForListVulByPodOutput.


        :param vuln_name_en: The vuln_name_en of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._vuln_name_en = vuln_name_en

    @property
    def vuln_type(self):
        """Gets the vuln_type of this DataForListVulByPodOutput.  # noqa: E501


        :return: The vuln_type of this DataForListVulByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_type

    @vuln_type.setter
    def vuln_type(self, vuln_type):
        """Sets the vuln_type of this DataForListVulByPodOutput.


        :param vuln_type: The vuln_type of this DataForListVulByPodOutput.  # noqa: E501
        :type: str
        """

        self._vuln_type = vuln_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListVulByPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListVulByPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListVulByPodOutput):
            return True

        return self.to_dict() != other.to_dict()
