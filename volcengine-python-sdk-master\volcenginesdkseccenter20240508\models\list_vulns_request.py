# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListVulnsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'cloud_providers': 'list[str]',
        'create_time_end': 'int',
        'create_time_start': 'int',
        'cve_id': 'str',
        'if_high_availability': 'bool',
        'leaf_group_ids': 'list[str]',
        'level': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[str]',
        'tag': 'list[str]',
        'top_group_id': 'str',
        'vuln_name': 'str',
        'vuln_type_list': 'list[str]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'cloud_providers': 'CloudProviders',
        'create_time_end': 'CreateTimeEnd',
        'create_time_start': 'CreateTimeStart',
        'cve_id': 'CveID',
        'if_high_availability': 'IfHighAvailability',
        'leaf_group_ids': 'LeafGroupIDs',
        'level': 'Level',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'tag': 'Tag',
        'top_group_id': 'TopGroupID',
        'vuln_name': 'VulnName',
        'vuln_type_list': 'VulnTypeList'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, cloud_providers=None, create_time_end=None, create_time_start=None, cve_id=None, if_high_availability=None, leaf_group_ids=None, level=None, page_number=None, page_size=None, sort_by=None, sort_order=None, status=None, tag=None, top_group_id=None, vuln_name=None, vuln_type_list=None, _configuration=None):  # noqa: E501
        """ListVulnsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._cloud_providers = None
        self._create_time_end = None
        self._create_time_start = None
        self._cve_id = None
        self._if_high_availability = None
        self._leaf_group_ids = None
        self._level = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._tag = None
        self._top_group_id = None
        self._vuln_name = None
        self._vuln_type_list = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if create_time_end is not None:
            self.create_time_end = create_time_end
        if create_time_start is not None:
            self.create_time_start = create_time_start
        if cve_id is not None:
            self.cve_id = cve_id
        if if_high_availability is not None:
            self.if_high_availability = if_high_availability
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if level is not None:
            self.level = level
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if vuln_name is not None:
            self.vuln_name = vuln_name
        if vuln_type_list is not None:
            self.vuln_type_list = vuln_type_list

    @property
    def agent_id(self):
        """Gets the agent_id of this ListVulnsRequest.  # noqa: E501


        :return: The agent_id of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListVulnsRequest.


        :param agent_id: The agent_id of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this ListVulnsRequest.  # noqa: E501


        :return: The asset_id of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListVulnsRequest.


        :param asset_id: The asset_id of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this ListVulnsRequest.  # noqa: E501


        :return: The asset_type of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this ListVulnsRequest.


        :param asset_type: The asset_type of this ListVulnsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListVulnsRequest.  # noqa: E501


        :return: The cloud_providers of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListVulnsRequest.


        :param cloud_providers: The cloud_providers of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def create_time_end(self):
        """Gets the create_time_end of this ListVulnsRequest.  # noqa: E501


        :return: The create_time_end of this ListVulnsRequest.  # noqa: E501
        :rtype: int
        """
        return self._create_time_end

    @create_time_end.setter
    def create_time_end(self, create_time_end):
        """Sets the create_time_end of this ListVulnsRequest.


        :param create_time_end: The create_time_end of this ListVulnsRequest.  # noqa: E501
        :type: int
        """

        self._create_time_end = create_time_end

    @property
    def create_time_start(self):
        """Gets the create_time_start of this ListVulnsRequest.  # noqa: E501


        :return: The create_time_start of this ListVulnsRequest.  # noqa: E501
        :rtype: int
        """
        return self._create_time_start

    @create_time_start.setter
    def create_time_start(self, create_time_start):
        """Sets the create_time_start of this ListVulnsRequest.


        :param create_time_start: The create_time_start of this ListVulnsRequest.  # noqa: E501
        :type: int
        """

        self._create_time_start = create_time_start

    @property
    def cve_id(self):
        """Gets the cve_id of this ListVulnsRequest.  # noqa: E501


        :return: The cve_id of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this ListVulnsRequest.


        :param cve_id: The cve_id of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def if_high_availability(self):
        """Gets the if_high_availability of this ListVulnsRequest.  # noqa: E501


        :return: The if_high_availability of this ListVulnsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_high_availability

    @if_high_availability.setter
    def if_high_availability(self, if_high_availability):
        """Sets the if_high_availability of this ListVulnsRequest.


        :param if_high_availability: The if_high_availability of this ListVulnsRequest.  # noqa: E501
        :type: bool
        """

        self._if_high_availability = if_high_availability

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListVulnsRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListVulnsRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def level(self):
        """Gets the level of this ListVulnsRequest.  # noqa: E501


        :return: The level of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ListVulnsRequest.


        :param level: The level of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._level = level

    @property
    def page_number(self):
        """Gets the page_number of this ListVulnsRequest.  # noqa: E501


        :return: The page_number of this ListVulnsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListVulnsRequest.


        :param page_number: The page_number of this ListVulnsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListVulnsRequest.  # noqa: E501


        :return: The page_size of this ListVulnsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListVulnsRequest.


        :param page_size: The page_size of this ListVulnsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListVulnsRequest.  # noqa: E501


        :return: The sort_by of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListVulnsRequest.


        :param sort_by: The sort_by of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListVulnsRequest.  # noqa: E501


        :return: The sort_order of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListVulnsRequest.


        :param sort_order: The sort_order of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListVulnsRequest.  # noqa: E501


        :return: The status of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListVulnsRequest.


        :param status: The status of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this ListVulnsRequest.  # noqa: E501


        :return: The tag of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ListVulnsRequest.


        :param tag: The tag of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListVulnsRequest.  # noqa: E501


        :return: The top_group_id of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListVulnsRequest.


        :param top_group_id: The top_group_id of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def vuln_name(self):
        """Gets the vuln_name of this ListVulnsRequest.  # noqa: E501


        :return: The vuln_name of this ListVulnsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this ListVulnsRequest.


        :param vuln_name: The vuln_name of this ListVulnsRequest.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    @property
    def vuln_type_list(self):
        """Gets the vuln_type_list of this ListVulnsRequest.  # noqa: E501


        :return: The vuln_type_list of this ListVulnsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_type_list

    @vuln_type_list.setter
    def vuln_type_list(self, vuln_type_list):
        """Sets the vuln_type_list of this ListVulnsRequest.


        :param vuln_type_list: The vuln_type_list of this ListVulnsRequest.  # noqa: E501
        :type: list[str]
        """

        self._vuln_type_list = vuln_type_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListVulnsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListVulnsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListVulnsRequest):
            return True

        return self.to_dict() != other.to_dict()
