# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VoteStatisticsListForGetVoteStatisticsAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'user_agent': 'str',
        'user_chose': 'list[UserChoseForGetVoteStatisticsAPIOutput]',
        'user_id': 'int',
        'user_name': 'str',
        'user_tel': 'str',
        'vote_time': 'int'
    }

    attribute_map = {
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'IP',
        'user_agent': 'UserAgent',
        'user_chose': 'UserChose',
        'user_id': 'UserID',
        'user_name': 'UserName',
        'user_tel': 'UserTel',
        'vote_time': 'VoteTime'
    }

    def __init__(self, email=None, external_id=None, extra=None, ip=None, user_agent=None, user_chose=None, user_id=None, user_name=None, user_tel=None, vote_time=None, _configuration=None):  # noqa: E501
        """VoteStatisticsListForGetVoteStatisticsAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._user_agent = None
        self._user_chose = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self._vote_time = None
        self.discriminator = None

        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if user_agent is not None:
            self.user_agent = user_agent
        if user_chose is not None:
            self.user_chose = user_chose
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel
        if vote_time is not None:
            self.vote_time = vote_time

    @property
    def email(self):
        """Gets the email of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The email of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param email: The email of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The external_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param external_id: The external_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The extra of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param extra: The extra of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The ip of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param ip: The ip of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def user_agent(self):
        """Gets the user_agent of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The user_agent of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param user_agent: The user_agent of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_chose(self):
        """Gets the user_chose of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The user_chose of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: list[UserChoseForGetVoteStatisticsAPIOutput]
        """
        return self._user_chose

    @user_chose.setter
    def user_chose(self, user_chose):
        """Sets the user_chose of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param user_chose: The user_chose of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: list[UserChoseForGetVoteStatisticsAPIOutput]
        """

        self._user_chose = user_chose

    @property
    def user_id(self):
        """Gets the user_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The user_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param user_id: The user_id of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The user_name of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param user_name: The user_name of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The user_tel of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param user_tel: The user_tel of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    @property
    def vote_time(self):
        """Gets the vote_time of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501


        :return: The vote_time of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._vote_time

    @vote_time.setter
    def vote_time(self, vote_time):
        """Sets the vote_time of this VoteStatisticsListForGetVoteStatisticsAPIOutput.


        :param vote_time: The vote_time of this VoteStatisticsListForGetVoteStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._vote_time = vote_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VoteStatisticsListForGetVoteStatisticsAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VoteStatisticsListForGetVoteStatisticsAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VoteStatisticsListForGetVoteStatisticsAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
