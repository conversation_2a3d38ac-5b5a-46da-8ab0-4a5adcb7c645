# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AnalysisUserBehaviorPeopleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'end_time': 'int',
        'extra': 'str',
        'is_merge': 'int',
        'is_merge_user_id': 'int',
        'percentage': 'int',
        'play_status': 'int',
        'source': 'int',
        'start_time': 'int',
        'total_time': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityID',
        'end_time': 'EndTime',
        'extra': 'Extra',
        'is_merge': 'IsMerge',
        'is_merge_user_id': 'IsMergeUserId',
        'percentage': 'Percentage',
        'play_status': 'PlayStatus',
        'source': 'Source',
        'start_time': 'StartTime',
        'total_time': 'TotalTime'
    }

    def __init__(self, activity_id=None, end_time=None, extra=None, is_merge=None, is_merge_user_id=None, percentage=None, play_status=None, source=None, start_time=None, total_time=None, _configuration=None):  # noqa: E501
        """AnalysisUserBehaviorPeopleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._end_time = None
        self._extra = None
        self._is_merge = None
        self._is_merge_user_id = None
        self._percentage = None
        self._play_status = None
        self._source = None
        self._start_time = None
        self._total_time = None
        self.discriminator = None

        self.activity_id = activity_id
        if end_time is not None:
            self.end_time = end_time
        if extra is not None:
            self.extra = extra
        self.is_merge = is_merge
        if is_merge_user_id is not None:
            self.is_merge_user_id = is_merge_user_id
        self.percentage = percentage
        self.play_status = play_status
        if source is not None:
            self.source = source
        if start_time is not None:
            self.start_time = start_time
        self.total_time = total_time

    @property
    def activity_id(self):
        """Gets the activity_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The activity_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this AnalysisUserBehaviorPeopleRequest.


        :param activity_id: The activity_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def end_time(self):
        """Gets the end_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The end_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this AnalysisUserBehaviorPeopleRequest.


        :param end_time: The end_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def extra(self):
        """Gets the extra of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The extra of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this AnalysisUserBehaviorPeopleRequest.


        :param extra: The extra of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def is_merge(self):
        """Gets the is_merge of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The is_merge of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_merge

    @is_merge.setter
    def is_merge(self, is_merge):
        """Sets the is_merge of this AnalysisUserBehaviorPeopleRequest.


        :param is_merge: The is_merge of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and is_merge is None:
            raise ValueError("Invalid value for `is_merge`, must not be `None`")  # noqa: E501

        self._is_merge = is_merge

    @property
    def is_merge_user_id(self):
        """Gets the is_merge_user_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The is_merge_user_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_merge_user_id

    @is_merge_user_id.setter
    def is_merge_user_id(self, is_merge_user_id):
        """Sets the is_merge_user_id of this AnalysisUserBehaviorPeopleRequest.


        :param is_merge_user_id: The is_merge_user_id of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """

        self._is_merge_user_id = is_merge_user_id

    @property
    def percentage(self):
        """Gets the percentage of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The percentage of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._percentage

    @percentage.setter
    def percentage(self, percentage):
        """Sets the percentage of this AnalysisUserBehaviorPeopleRequest.


        :param percentage: The percentage of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and percentage is None:
            raise ValueError("Invalid value for `percentage`, must not be `None`")  # noqa: E501

        self._percentage = percentage

    @property
    def play_status(self):
        """Gets the play_status of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The play_status of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._play_status

    @play_status.setter
    def play_status(self, play_status):
        """Sets the play_status of this AnalysisUserBehaviorPeopleRequest.


        :param play_status: The play_status of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and play_status is None:
            raise ValueError("Invalid value for `play_status`, must not be `None`")  # noqa: E501

        self._play_status = play_status

    @property
    def source(self):
        """Gets the source of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The source of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this AnalysisUserBehaviorPeopleRequest.


        :param source: The source of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """

        self._source = source

    @property
    def start_time(self):
        """Gets the start_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The start_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this AnalysisUserBehaviorPeopleRequest.


        :param start_time: The start_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def total_time(self):
        """Gets the total_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501


        :return: The total_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :rtype: str
        """
        return self._total_time

    @total_time.setter
    def total_time(self, total_time):
        """Sets the total_time of this AnalysisUserBehaviorPeopleRequest.


        :param total_time: The total_time of this AnalysisUserBehaviorPeopleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and total_time is None:
            raise ValueError("Invalid value for `total_time`, must not be `None`")  # noqa: E501

        self._total_time = total_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AnalysisUserBehaviorPeopleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AnalysisUserBehaviorPeopleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AnalysisUserBehaviorPeopleRequest):
            return True

        return self.to_dict() != other.to_dict()
