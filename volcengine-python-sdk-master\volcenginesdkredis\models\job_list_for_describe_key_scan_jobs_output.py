# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class JobListForDescribeKeyScanJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'completed_count': 'int',
        'create_time': 'str',
        'finish_time': 'str',
        'job_id': 'str',
        'start_time': 'str',
        'status': 'str',
        'total_count': 'int'
    }

    attribute_map = {
        'completed_count': 'CompletedCount',
        'create_time': 'CreateTime',
        'finish_time': 'FinishTime',
        'job_id': 'JobId',
        'start_time': 'StartTime',
        'status': 'Status',
        'total_count': 'TotalCount'
    }

    def __init__(self, completed_count=None, create_time=None, finish_time=None, job_id=None, start_time=None, status=None, total_count=None, _configuration=None):  # noqa: E501
        """JobListForDescribeKeyScanJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._completed_count = None
        self._create_time = None
        self._finish_time = None
        self._job_id = None
        self._start_time = None
        self._status = None
        self._total_count = None
        self.discriminator = None

        if completed_count is not None:
            self.completed_count = completed_count
        if create_time is not None:
            self.create_time = create_time
        if finish_time is not None:
            self.finish_time = finish_time
        if job_id is not None:
            self.job_id = job_id
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if total_count is not None:
            self.total_count = total_count

    @property
    def completed_count(self):
        """Gets the completed_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The completed_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._completed_count

    @completed_count.setter
    def completed_count(self, completed_count):
        """Sets the completed_count of this JobListForDescribeKeyScanJobsOutput.


        :param completed_count: The completed_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: int
        """

        self._completed_count = completed_count

    @property
    def create_time(self):
        """Gets the create_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The create_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this JobListForDescribeKeyScanJobsOutput.


        :param create_time: The create_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def finish_time(self):
        """Gets the finish_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The finish_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this JobListForDescribeKeyScanJobsOutput.


        :param finish_time: The finish_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: str
        """

        self._finish_time = finish_time

    @property
    def job_id(self):
        """Gets the job_id of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The job_id of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._job_id

    @job_id.setter
    def job_id(self, job_id):
        """Sets the job_id of this JobListForDescribeKeyScanJobsOutput.


        :param job_id: The job_id of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: str
        """

        self._job_id = job_id

    @property
    def start_time(self):
        """Gets the start_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The start_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this JobListForDescribeKeyScanJobsOutput.


        :param start_time: The start_time of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The status of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this JobListForDescribeKeyScanJobsOutput.


        :param status: The status of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def total_count(self):
        """Gets the total_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501


        :return: The total_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this JobListForDescribeKeyScanJobsOutput.


        :param total_count: The total_count of this JobListForDescribeKeyScanJobsOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(JobListForDescribeKeyScanJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, JobListForDescribeKeyScanJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, JobListForDescribeKeyScanJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
