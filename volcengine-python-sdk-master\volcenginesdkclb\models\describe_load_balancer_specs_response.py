# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeLoadBalancerSpecsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'load_balancer_specs': 'list[LoadBalancerSpecForDescribeLoadBalancerSpecsOutput]',
        'request_id': 'str'
    }

    attribute_map = {
        'load_balancer_specs': 'LoadBalancerSpecs',
        'request_id': 'RequestId'
    }

    def __init__(self, load_balancer_specs=None, request_id=None, _configuration=None):  # noqa: E501
        """DescribeLoadBalancerSpecsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._load_balancer_specs = None
        self._request_id = None
        self.discriminator = None

        if load_balancer_specs is not None:
            self.load_balancer_specs = load_balancer_specs
        if request_id is not None:
            self.request_id = request_id

    @property
    def load_balancer_specs(self):
        """Gets the load_balancer_specs of this DescribeLoadBalancerSpecsResponse.  # noqa: E501


        :return: The load_balancer_specs of this DescribeLoadBalancerSpecsResponse.  # noqa: E501
        :rtype: list[LoadBalancerSpecForDescribeLoadBalancerSpecsOutput]
        """
        return self._load_balancer_specs

    @load_balancer_specs.setter
    def load_balancer_specs(self, load_balancer_specs):
        """Sets the load_balancer_specs of this DescribeLoadBalancerSpecsResponse.


        :param load_balancer_specs: The load_balancer_specs of this DescribeLoadBalancerSpecsResponse.  # noqa: E501
        :type: list[LoadBalancerSpecForDescribeLoadBalancerSpecsOutput]
        """

        self._load_balancer_specs = load_balancer_specs

    @property
    def request_id(self):
        """Gets the request_id of this DescribeLoadBalancerSpecsResponse.  # noqa: E501


        :return: The request_id of this DescribeLoadBalancerSpecsResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeLoadBalancerSpecsResponse.


        :param request_id: The request_id of this DescribeLoadBalancerSpecsResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeLoadBalancerSpecsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeLoadBalancerSpecsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeLoadBalancerSpecsResponse):
            return True

        return self.to_dict() != other.to_dict()
