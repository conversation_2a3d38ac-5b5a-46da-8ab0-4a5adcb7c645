# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListNodesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_ids': 'list[str]',
        'create_client_token': 'str',
        'ids': 'list[str]',
        'instance_ids': 'list[str]',
        'name': 'str',
        'node_pool_ids': 'list[str]',
        'statuses': 'list[StatusForListNodesInput]',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'cluster_ids': 'ClusterIds',
        'create_client_token': 'CreateClientToken',
        'ids': 'Ids',
        'instance_ids': 'InstanceIds',
        'name': 'Name',
        'node_pool_ids': 'NodePoolIds',
        'statuses': 'Statuses',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, cluster_ids=None, create_client_token=None, ids=None, instance_ids=None, name=None, node_pool_ids=None, statuses=None, zone_ids=None, _configuration=None):  # noqa: E501
        """FilterForListNodesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_ids = None
        self._create_client_token = None
        self._ids = None
        self._instance_ids = None
        self._name = None
        self._node_pool_ids = None
        self._statuses = None
        self._zone_ids = None
        self.discriminator = None

        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if ids is not None:
            self.ids = ids
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if name is not None:
            self.name = name
        if node_pool_ids is not None:
            self.node_pool_ids = node_pool_ids
        if statuses is not None:
            self.statuses = statuses
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this FilterForListNodesInput.  # noqa: E501


        :return: The cluster_ids of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this FilterForListNodesInput.


        :param cluster_ids: The cluster_ids of this FilterForListNodesInput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def create_client_token(self):
        """Gets the create_client_token of this FilterForListNodesInput.  # noqa: E501


        :return: The create_client_token of this FilterForListNodesInput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this FilterForListNodesInput.


        :param create_client_token: The create_client_token of this FilterForListNodesInput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def ids(self):
        """Gets the ids of this FilterForListNodesInput.  # noqa: E501


        :return: The ids of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListNodesInput.


        :param ids: The ids of this FilterForListNodesInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def instance_ids(self):
        """Gets the instance_ids of this FilterForListNodesInput.  # noqa: E501


        :return: The instance_ids of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this FilterForListNodesInput.


        :param instance_ids: The instance_ids of this FilterForListNodesInput.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def name(self):
        """Gets the name of this FilterForListNodesInput.  # noqa: E501


        :return: The name of this FilterForListNodesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListNodesInput.


        :param name: The name of this FilterForListNodesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_pool_ids(self):
        """Gets the node_pool_ids of this FilterForListNodesInput.  # noqa: E501


        :return: The node_pool_ids of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_pool_ids

    @node_pool_ids.setter
    def node_pool_ids(self, node_pool_ids):
        """Sets the node_pool_ids of this FilterForListNodesInput.


        :param node_pool_ids: The node_pool_ids of this FilterForListNodesInput.  # noqa: E501
        :type: list[str]
        """

        self._node_pool_ids = node_pool_ids

    @property
    def statuses(self):
        """Gets the statuses of this FilterForListNodesInput.  # noqa: E501


        :return: The statuses of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[StatusForListNodesInput]
        """
        return self._statuses

    @statuses.setter
    def statuses(self, statuses):
        """Sets the statuses of this FilterForListNodesInput.


        :param statuses: The statuses of this FilterForListNodesInput.  # noqa: E501
        :type: list[StatusForListNodesInput]
        """

        self._statuses = statuses

    @property
    def zone_ids(self):
        """Gets the zone_ids of this FilterForListNodesInput.  # noqa: E501


        :return: The zone_ids of this FilterForListNodesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this FilterForListNodesInput.


        :param zone_ids: The zone_ids of this FilterForListNodesInput.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListNodesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListNodesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListNodesInput):
            return True

        return self.to_dict() != other.to_dict()
