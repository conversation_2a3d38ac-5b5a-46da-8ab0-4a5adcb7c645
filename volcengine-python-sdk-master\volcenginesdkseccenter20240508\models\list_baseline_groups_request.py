# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBaselineGroupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_type': 'str',
        'if_container': 'bool'
    }

    attribute_map = {
        'asset_type': 'AssetType',
        'if_container': 'IfContainer'
    }

    def __init__(self, asset_type=None, if_container=None, _configuration=None):  # noqa: E501
        """ListBaselineGroupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_type = None
        self._if_container = None
        self.discriminator = None

        if asset_type is not None:
            self.asset_type = asset_type
        if if_container is not None:
            self.if_container = if_container

    @property
    def asset_type(self):
        """Gets the asset_type of this ListBaselineGroupsRequest.  # noqa: E501


        :return: The asset_type of this ListBaselineGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this ListBaselineGroupsRequest.


        :param asset_type: The asset_type of this ListBaselineGroupsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def if_container(self):
        """Gets the if_container of this ListBaselineGroupsRequest.  # noqa: E501


        :return: The if_container of this ListBaselineGroupsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_container

    @if_container.setter
    def if_container(self, if_container):
        """Sets the if_container of this ListBaselineGroupsRequest.


        :param if_container: The if_container of this ListBaselineGroupsRequest.  # noqa: E501
        :type: bool
        """

        self._if_container = if_container

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBaselineGroupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBaselineGroupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBaselineGroupsRequest):
            return True

        return self.to_dict() != other.to_dict()
