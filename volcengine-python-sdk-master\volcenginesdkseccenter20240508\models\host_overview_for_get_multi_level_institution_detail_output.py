# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HostOverviewForGetMultiLevelInstitutionDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host_num': 'int',
        'offline': 'int',
        'protecting': 'int',
        'protection_exception': 'int',
        'unprotected': 'int'
    }

    attribute_map = {
        'host_num': 'HostNum',
        'offline': 'Offline',
        'protecting': 'Protecting',
        'protection_exception': 'ProtectionException',
        'unprotected': 'Unprotected'
    }

    def __init__(self, host_num=None, offline=None, protecting=None, protection_exception=None, unprotected=None, _configuration=None):  # noqa: E501
        """HostOverviewForGetMultiLevelInstitutionDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host_num = None
        self._offline = None
        self._protecting = None
        self._protection_exception = None
        self._unprotected = None
        self.discriminator = None

        if host_num is not None:
            self.host_num = host_num
        if offline is not None:
            self.offline = offline
        if protecting is not None:
            self.protecting = protecting
        if protection_exception is not None:
            self.protection_exception = protection_exception
        if unprotected is not None:
            self.unprotected = unprotected

    @property
    def host_num(self):
        """Gets the host_num of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The host_num of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_num

    @host_num.setter
    def host_num(self, host_num):
        """Sets the host_num of this HostOverviewForGetMultiLevelInstitutionDetailOutput.


        :param host_num: The host_num of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._host_num = host_num

    @property
    def offline(self):
        """Gets the offline of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The offline of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._offline

    @offline.setter
    def offline(self, offline):
        """Sets the offline of this HostOverviewForGetMultiLevelInstitutionDetailOutput.


        :param offline: The offline of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._offline = offline

    @property
    def protecting(self):
        """Gets the protecting of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The protecting of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._protecting

    @protecting.setter
    def protecting(self, protecting):
        """Sets the protecting of this HostOverviewForGetMultiLevelInstitutionDetailOutput.


        :param protecting: The protecting of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._protecting = protecting

    @property
    def protection_exception(self):
        """Gets the protection_exception of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The protection_exception of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._protection_exception

    @protection_exception.setter
    def protection_exception(self, protection_exception):
        """Sets the protection_exception of this HostOverviewForGetMultiLevelInstitutionDetailOutput.


        :param protection_exception: The protection_exception of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._protection_exception = protection_exception

    @property
    def unprotected(self):
        """Gets the unprotected of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The unprotected of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._unprotected

    @unprotected.setter
    def unprotected(self, unprotected):
        """Sets the unprotected of this HostOverviewForGetMultiLevelInstitutionDetailOutput.


        :param unprotected: The unprotected of this HostOverviewForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._unprotected = unprotected

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HostOverviewForGetMultiLevelInstitutionDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HostOverviewForGetMultiLevelInstitutionDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HostOverviewForGetMultiLevelInstitutionDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
