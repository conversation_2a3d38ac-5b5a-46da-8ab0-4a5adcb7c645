# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AwardItemListForGetAwardItemListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'award_item_amounts': 'int',
        'award_item_icon': 'str',
        'award_item_name': 'str',
        'award_item_num': 'str',
        'award_item_stock': 'int',
        'award_item_type': 'int',
        'award_lottery_ticket_addr': 'str',
        'delivery_method': 'int',
        'id': 'int',
        'winner_info_type': 'list[int]'
    }

    attribute_map = {
        'award_item_amounts': 'AwardItemAmounts',
        'award_item_icon': 'AwardItemIcon',
        'award_item_name': 'AwardItemName',
        'award_item_num': 'AwardItemNum',
        'award_item_stock': 'AwardItemStock',
        'award_item_type': 'AwardItemType',
        'award_lottery_ticket_addr': 'AwardLotteryTicketAddr',
        'delivery_method': 'DeliveryMethod',
        'id': 'Id',
        'winner_info_type': 'WinnerInfoType'
    }

    def __init__(self, award_item_amounts=None, award_item_icon=None, award_item_name=None, award_item_num=None, award_item_stock=None, award_item_type=None, award_lottery_ticket_addr=None, delivery_method=None, id=None, winner_info_type=None, _configuration=None):  # noqa: E501
        """AwardItemListForGetAwardItemListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._award_item_amounts = None
        self._award_item_icon = None
        self._award_item_name = None
        self._award_item_num = None
        self._award_item_stock = None
        self._award_item_type = None
        self._award_lottery_ticket_addr = None
        self._delivery_method = None
        self._id = None
        self._winner_info_type = None
        self.discriminator = None

        if award_item_amounts is not None:
            self.award_item_amounts = award_item_amounts
        if award_item_icon is not None:
            self.award_item_icon = award_item_icon
        if award_item_name is not None:
            self.award_item_name = award_item_name
        if award_item_num is not None:
            self.award_item_num = award_item_num
        if award_item_stock is not None:
            self.award_item_stock = award_item_stock
        if award_item_type is not None:
            self.award_item_type = award_item_type
        if award_lottery_ticket_addr is not None:
            self.award_lottery_ticket_addr = award_lottery_ticket_addr
        if delivery_method is not None:
            self.delivery_method = delivery_method
        if id is not None:
            self.id = id
        if winner_info_type is not None:
            self.winner_info_type = winner_info_type

    @property
    def award_item_amounts(self):
        """Gets the award_item_amounts of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_amounts of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_amounts

    @award_item_amounts.setter
    def award_item_amounts(self, award_item_amounts):
        """Sets the award_item_amounts of this AwardItemListForGetAwardItemListOutput.


        :param award_item_amounts: The award_item_amounts of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: int
        """

        self._award_item_amounts = award_item_amounts

    @property
    def award_item_icon(self):
        """Gets the award_item_icon of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_icon of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_icon

    @award_item_icon.setter
    def award_item_icon(self, award_item_icon):
        """Sets the award_item_icon of this AwardItemListForGetAwardItemListOutput.


        :param award_item_icon: The award_item_icon of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: str
        """

        self._award_item_icon = award_item_icon

    @property
    def award_item_name(self):
        """Gets the award_item_name of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_name of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_name

    @award_item_name.setter
    def award_item_name(self, award_item_name):
        """Sets the award_item_name of this AwardItemListForGetAwardItemListOutput.


        :param award_item_name: The award_item_name of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: str
        """

        self._award_item_name = award_item_name

    @property
    def award_item_num(self):
        """Gets the award_item_num of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_num of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_num

    @award_item_num.setter
    def award_item_num(self, award_item_num):
        """Sets the award_item_num of this AwardItemListForGetAwardItemListOutput.


        :param award_item_num: The award_item_num of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: str
        """

        self._award_item_num = award_item_num

    @property
    def award_item_stock(self):
        """Gets the award_item_stock of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_stock of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_stock

    @award_item_stock.setter
    def award_item_stock(self, award_item_stock):
        """Sets the award_item_stock of this AwardItemListForGetAwardItemListOutput.


        :param award_item_stock: The award_item_stock of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: int
        """

        self._award_item_stock = award_item_stock

    @property
    def award_item_type(self):
        """Gets the award_item_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_item_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_type

    @award_item_type.setter
    def award_item_type(self, award_item_type):
        """Sets the award_item_type of this AwardItemListForGetAwardItemListOutput.


        :param award_item_type: The award_item_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: int
        """

        self._award_item_type = award_item_type

    @property
    def award_lottery_ticket_addr(self):
        """Gets the award_lottery_ticket_addr of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The award_lottery_ticket_addr of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_lottery_ticket_addr

    @award_lottery_ticket_addr.setter
    def award_lottery_ticket_addr(self, award_lottery_ticket_addr):
        """Sets the award_lottery_ticket_addr of this AwardItemListForGetAwardItemListOutput.


        :param award_lottery_ticket_addr: The award_lottery_ticket_addr of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: str
        """

        self._award_lottery_ticket_addr = award_lottery_ticket_addr

    @property
    def delivery_method(self):
        """Gets the delivery_method of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The delivery_method of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: int
        """
        return self._delivery_method

    @delivery_method.setter
    def delivery_method(self, delivery_method):
        """Sets the delivery_method of this AwardItemListForGetAwardItemListOutput.


        :param delivery_method: The delivery_method of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: int
        """

        self._delivery_method = delivery_method

    @property
    def id(self):
        """Gets the id of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The id of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this AwardItemListForGetAwardItemListOutput.


        :param id: The id of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def winner_info_type(self):
        """Gets the winner_info_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501


        :return: The winner_info_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._winner_info_type

    @winner_info_type.setter
    def winner_info_type(self, winner_info_type):
        """Sets the winner_info_type of this AwardItemListForGetAwardItemListOutput.


        :param winner_info_type: The winner_info_type of this AwardItemListForGetAwardItemListOutput.  # noqa: E501
        :type: list[int]
        """

        self._winner_info_type = winner_info_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AwardItemListForGetAwardItemListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AwardItemListForGetAwardItemListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AwardItemListForGetAwardItemListOutput):
            return True

        return self.to_dict() != other.to_dict()
