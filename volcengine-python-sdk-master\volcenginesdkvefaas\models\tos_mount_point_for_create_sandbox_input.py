# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TosMountPointForCreateSandboxInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bucket_path': 'str',
        'local_mount_path': 'str'
    }

    attribute_map = {
        'bucket_path': 'BucketPath',
        'local_mount_path': 'LocalMountPath'
    }

    def __init__(self, bucket_path=None, local_mount_path=None, _configuration=None):  # noqa: E501
        """TosMountPointForCreateSandboxInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bucket_path = None
        self._local_mount_path = None
        self.discriminator = None

        if bucket_path is not None:
            self.bucket_path = bucket_path
        if local_mount_path is not None:
            self.local_mount_path = local_mount_path

    @property
    def bucket_path(self):
        """Gets the bucket_path of this TosMountPointForCreateSandboxInput.  # noqa: E501


        :return: The bucket_path of this TosMountPointForCreateSandboxInput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_path

    @bucket_path.setter
    def bucket_path(self, bucket_path):
        """Sets the bucket_path of this TosMountPointForCreateSandboxInput.


        :param bucket_path: The bucket_path of this TosMountPointForCreateSandboxInput.  # noqa: E501
        :type: str
        """

        self._bucket_path = bucket_path

    @property
    def local_mount_path(self):
        """Gets the local_mount_path of this TosMountPointForCreateSandboxInput.  # noqa: E501


        :return: The local_mount_path of this TosMountPointForCreateSandboxInput.  # noqa: E501
        :rtype: str
        """
        return self._local_mount_path

    @local_mount_path.setter
    def local_mount_path(self, local_mount_path):
        """Sets the local_mount_path of this TosMountPointForCreateSandboxInput.


        :param local_mount_path: The local_mount_path of this TosMountPointForCreateSandboxInput.  # noqa: E501
        :type: str
        """

        self._local_mount_path = local_mount_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TosMountPointForCreateSandboxInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TosMountPointForCreateSandboxInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TosMountPointForCreateSandboxInput):
            return True

        return self.to_dict() != other.to_dict()
