# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TargetForCreateDataMigrateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ak': 'str',
        'bucket_name': 'str',
        'role_trn': 'str',
        'sk': 'str'
    }

    attribute_map = {
        'ak': 'AK',
        'bucket_name': 'BucketName',
        'role_trn': 'RoleTrn',
        'sk': 'SK'
    }

    def __init__(self, ak=None, bucket_name=None, role_trn=None, sk=None, _configuration=None):  # noqa: E501
        """TargetForCreateDataMigrateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ak = None
        self._bucket_name = None
        self._role_trn = None
        self._sk = None
        self.discriminator = None

        if ak is not None:
            self.ak = ak
        if bucket_name is not None:
            self.bucket_name = bucket_name
        if role_trn is not None:
            self.role_trn = role_trn
        if sk is not None:
            self.sk = sk

    @property
    def ak(self):
        """Gets the ak of this TargetForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The ak of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._ak

    @ak.setter
    def ak(self, ak):
        """Sets the ak of this TargetForCreateDataMigrateTaskInput.


        :param ak: The ak of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """

        self._ak = ak

    @property
    def bucket_name(self):
        """Gets the bucket_name of this TargetForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The bucket_name of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_name

    @bucket_name.setter
    def bucket_name(self, bucket_name):
        """Sets the bucket_name of this TargetForCreateDataMigrateTaskInput.


        :param bucket_name: The bucket_name of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """

        self._bucket_name = bucket_name

    @property
    def role_trn(self):
        """Gets the role_trn of this TargetForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The role_trn of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._role_trn

    @role_trn.setter
    def role_trn(self, role_trn):
        """Sets the role_trn of this TargetForCreateDataMigrateTaskInput.


        :param role_trn: The role_trn of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """

        self._role_trn = role_trn

    @property
    def sk(self):
        """Gets the sk of this TargetForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The sk of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._sk

    @sk.setter
    def sk(self, sk):
        """Sets the sk of this TargetForCreateDataMigrateTaskInput.


        :param sk: The sk of this TargetForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """

        self._sk = sk

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TargetForCreateDataMigrateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TargetForCreateDataMigrateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TargetForCreateDataMigrateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
