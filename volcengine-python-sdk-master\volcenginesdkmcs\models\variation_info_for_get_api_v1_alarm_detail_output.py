# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VariationInfoForGetApiV1AlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_feature_info_malware': 'AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput'
    }

    attribute_map = {
        'alarm_feature_info_malware': 'alarm_feature_info_malware'
    }

    def __init__(self, alarm_feature_info_malware=None, _configuration=None):  # noqa: E501
        """VariationInfoForGetApiV1AlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_feature_info_malware = None
        self.discriminator = None

        if alarm_feature_info_malware is not None:
            self.alarm_feature_info_malware = alarm_feature_info_malware

    @property
    def alarm_feature_info_malware(self):
        """Gets the alarm_feature_info_malware of this VariationInfoForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_feature_info_malware of this VariationInfoForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput
        """
        return self._alarm_feature_info_malware

    @alarm_feature_info_malware.setter
    def alarm_feature_info_malware(self, alarm_feature_info_malware):
        """Sets the alarm_feature_info_malware of this VariationInfoForGetApiV1AlarmDetailOutput.


        :param alarm_feature_info_malware: The alarm_feature_info_malware of this VariationInfoForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput
        """

        self._alarm_feature_info_malware = alarm_feature_info_malware

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VariationInfoForGetApiV1AlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VariationInfoForGetApiV1AlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VariationInfoForGetApiV1AlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
