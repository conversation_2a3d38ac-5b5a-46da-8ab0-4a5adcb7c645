# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AttachPolicyInProjectRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'policy_name': 'str',
        'policy_type': 'str',
        'principal_name': 'str',
        'principal_type': 'str',
        'project_name': 'list[str]'
    }

    attribute_map = {
        'policy_name': 'PolicyName',
        'policy_type': 'PolicyType',
        'principal_name': 'PrincipalName',
        'principal_type': 'PrincipalType',
        'project_name': 'ProjectName'
    }

    def __init__(self, policy_name=None, policy_type=None, principal_name=None, principal_type=None, project_name=None, _configuration=None):  # noqa: E501
        """AttachPolicyInProjectRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._policy_name = None
        self._policy_type = None
        self._principal_name = None
        self._principal_type = None
        self._project_name = None
        self.discriminator = None

        self.policy_name = policy_name
        self.policy_type = policy_type
        self.principal_name = principal_name
        self.principal_type = principal_type
        if project_name is not None:
            self.project_name = project_name

    @property
    def policy_name(self):
        """Gets the policy_name of this AttachPolicyInProjectRequest.  # noqa: E501


        :return: The policy_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_name

    @policy_name.setter
    def policy_name(self, policy_name):
        """Sets the policy_name of this AttachPolicyInProjectRequest.


        :param policy_name: The policy_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_name is None:
            raise ValueError("Invalid value for `policy_name`, must not be `None`")  # noqa: E501

        self._policy_name = policy_name

    @property
    def policy_type(self):
        """Gets the policy_type of this AttachPolicyInProjectRequest.  # noqa: E501


        :return: The policy_type of this AttachPolicyInProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_type

    @policy_type.setter
    def policy_type(self, policy_type):
        """Sets the policy_type of this AttachPolicyInProjectRequest.


        :param policy_type: The policy_type of this AttachPolicyInProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_type is None:
            raise ValueError("Invalid value for `policy_type`, must not be `None`")  # noqa: E501
        allowed_values = ["System", "Custom"]  # noqa: E501
        if (self._configuration.client_side_validation and
                policy_type not in allowed_values):
            raise ValueError(
                "Invalid value for `policy_type` ({0}), must be one of {1}"  # noqa: E501
                .format(policy_type, allowed_values)
            )

        self._policy_type = policy_type

    @property
    def principal_name(self):
        """Gets the principal_name of this AttachPolicyInProjectRequest.  # noqa: E501


        :return: The principal_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._principal_name

    @principal_name.setter
    def principal_name(self, principal_name):
        """Sets the principal_name of this AttachPolicyInProjectRequest.


        :param principal_name: The principal_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and principal_name is None:
            raise ValueError("Invalid value for `principal_name`, must not be `None`")  # noqa: E501

        self._principal_name = principal_name

    @property
    def principal_type(self):
        """Gets the principal_type of this AttachPolicyInProjectRequest.  # noqa: E501


        :return: The principal_type of this AttachPolicyInProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._principal_type

    @principal_type.setter
    def principal_type(self, principal_type):
        """Sets the principal_type of this AttachPolicyInProjectRequest.


        :param principal_type: The principal_type of this AttachPolicyInProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and principal_type is None:
            raise ValueError("Invalid value for `principal_type`, must not be `None`")  # noqa: E501
        allowed_values = ["User", "Role", "UserGroup"]  # noqa: E501
        if (self._configuration.client_side_validation and
                principal_type not in allowed_values):
            raise ValueError(
                "Invalid value for `principal_type` ({0}), must be one of {1}"  # noqa: E501
                .format(principal_type, allowed_values)
            )

        self._principal_type = principal_type

    @property
    def project_name(self):
        """Gets the project_name of this AttachPolicyInProjectRequest.  # noqa: E501


        :return: The project_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this AttachPolicyInProjectRequest.


        :param project_name: The project_name of this AttachPolicyInProjectRequest.  # noqa: E501
        :type: list[str]
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AttachPolicyInProjectRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AttachPolicyInProjectRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AttachPolicyInProjectRequest):
            return True

        return self.to_dict() != other.to_dict()
