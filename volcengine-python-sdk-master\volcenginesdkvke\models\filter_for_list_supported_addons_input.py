# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListSupportedAddonsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'categories': 'list[str]',
        'deploy_modes': 'list[str]',
        'deploy_node_types': 'list[str]',
        'name': 'str',
        'necessaries': 'list[str]',
        'pod_network_modes': 'list[str]',
        'versions_compatibilities_kubernetes_versions': 'list[str]'
    }

    attribute_map = {
        'categories': 'Categories',
        'deploy_modes': 'DeployModes',
        'deploy_node_types': 'DeployNodeTypes',
        'name': 'Name',
        'necessaries': 'Necessaries',
        'pod_network_modes': 'PodNetworkModes',
        'versions_compatibilities_kubernetes_versions': 'Versions.Compatibilities.KubernetesVersions'
    }

    def __init__(self, categories=None, deploy_modes=None, deploy_node_types=None, name=None, necessaries=None, pod_network_modes=None, versions_compatibilities_kubernetes_versions=None, _configuration=None):  # noqa: E501
        """FilterForListSupportedAddonsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._categories = None
        self._deploy_modes = None
        self._deploy_node_types = None
        self._name = None
        self._necessaries = None
        self._pod_network_modes = None
        self._versions_compatibilities_kubernetes_versions = None
        self.discriminator = None

        if categories is not None:
            self.categories = categories
        if deploy_modes is not None:
            self.deploy_modes = deploy_modes
        if deploy_node_types is not None:
            self.deploy_node_types = deploy_node_types
        if name is not None:
            self.name = name
        if necessaries is not None:
            self.necessaries = necessaries
        if pod_network_modes is not None:
            self.pod_network_modes = pod_network_modes
        if versions_compatibilities_kubernetes_versions is not None:
            self.versions_compatibilities_kubernetes_versions = versions_compatibilities_kubernetes_versions

    @property
    def categories(self):
        """Gets the categories of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The categories of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._categories

    @categories.setter
    def categories(self, categories):
        """Sets the categories of this FilterForListSupportedAddonsInput.


        :param categories: The categories of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Monitor", "Dns", "Storage", "Scheduler", "Network", "Security", "Gpu", "Image"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(categories).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `categories` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(categories) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._categories = categories

    @property
    def deploy_modes(self):
        """Gets the deploy_modes of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The deploy_modes of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._deploy_modes

    @deploy_modes.setter
    def deploy_modes(self, deploy_modes):
        """Sets the deploy_modes of this FilterForListSupportedAddonsInput.


        :param deploy_modes: The deploy_modes of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Managed", "Unmanaged"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(deploy_modes).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `deploy_modes` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(deploy_modes) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._deploy_modes = deploy_modes

    @property
    def deploy_node_types(self):
        """Gets the deploy_node_types of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The deploy_node_types of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._deploy_node_types

    @deploy_node_types.setter
    def deploy_node_types(self, deploy_node_types):
        """Sets the deploy_node_types of this FilterForListSupportedAddonsInput.


        :param deploy_node_types: The deploy_node_types of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Node", "VirtualNode", "EdgeNode"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(deploy_node_types).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `deploy_node_types` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(deploy_node_types) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._deploy_node_types = deploy_node_types

    @property
    def name(self):
        """Gets the name of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The name of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListSupportedAddonsInput.


        :param name: The name of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def necessaries(self):
        """Gets the necessaries of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The necessaries of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._necessaries

    @necessaries.setter
    def necessaries(self, necessaries):
        """Sets the necessaries of this FilterForListSupportedAddonsInput.


        :param necessaries: The necessaries of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Required", "Recommended", "OnDemand"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(necessaries).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `necessaries` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(necessaries) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._necessaries = necessaries

    @property
    def pod_network_modes(self):
        """Gets the pod_network_modes of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The pod_network_modes of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._pod_network_modes

    @pod_network_modes.setter
    def pod_network_modes(self, pod_network_modes):
        """Sets the pod_network_modes of this FilterForListSupportedAddonsInput.


        :param pod_network_modes: The pod_network_modes of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Flannel", "VpcCniShared"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(pod_network_modes).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `pod_network_modes` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(pod_network_modes) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._pod_network_modes = pod_network_modes

    @property
    def versions_compatibilities_kubernetes_versions(self):
        """Gets the versions_compatibilities_kubernetes_versions of this FilterForListSupportedAddonsInput.  # noqa: E501


        :return: The versions_compatibilities_kubernetes_versions of this FilterForListSupportedAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._versions_compatibilities_kubernetes_versions

    @versions_compatibilities_kubernetes_versions.setter
    def versions_compatibilities_kubernetes_versions(self, versions_compatibilities_kubernetes_versions):
        """Sets the versions_compatibilities_kubernetes_versions of this FilterForListSupportedAddonsInput.


        :param versions_compatibilities_kubernetes_versions: The versions_compatibilities_kubernetes_versions of this FilterForListSupportedAddonsInput.  # noqa: E501
        :type: list[str]
        """

        self._versions_compatibilities_kubernetes_versions = versions_compatibilities_kubernetes_versions

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListSupportedAddonsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListSupportedAddonsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListSupportedAddonsInput):
            return True

        return self.to_dict() != other.to_dict()
