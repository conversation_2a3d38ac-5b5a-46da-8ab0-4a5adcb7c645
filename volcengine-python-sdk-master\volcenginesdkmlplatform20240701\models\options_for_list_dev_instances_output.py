# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OptionsForListDevInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'internal_sync': 'int',
        'meta_cache_capacity': 'int',
        'meta_cache_expiry_minutes': 'int',
        'meta_cache_expiry_msec': 'int'
    }

    attribute_map = {
        'internal_sync': 'InternalSync',
        'meta_cache_capacity': 'MetaCacheCapacity',
        'meta_cache_expiry_minutes': 'MetaCacheExpiryMinutes',
        'meta_cache_expiry_msec': 'MetaCacheExpiryMsec'
    }

    def __init__(self, internal_sync=None, meta_cache_capacity=None, meta_cache_expiry_minutes=None, meta_cache_expiry_msec=None, _configuration=None):  # noqa: E501
        """OptionsForListDevInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._internal_sync = None
        self._meta_cache_capacity = None
        self._meta_cache_expiry_minutes = None
        self._meta_cache_expiry_msec = None
        self.discriminator = None

        if internal_sync is not None:
            self.internal_sync = internal_sync
        if meta_cache_capacity is not None:
            self.meta_cache_capacity = meta_cache_capacity
        if meta_cache_expiry_minutes is not None:
            self.meta_cache_expiry_minutes = meta_cache_expiry_minutes
        if meta_cache_expiry_msec is not None:
            self.meta_cache_expiry_msec = meta_cache_expiry_msec

    @property
    def internal_sync(self):
        """Gets the internal_sync of this OptionsForListDevInstancesOutput.  # noqa: E501


        :return: The internal_sync of this OptionsForListDevInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._internal_sync

    @internal_sync.setter
    def internal_sync(self, internal_sync):
        """Sets the internal_sync of this OptionsForListDevInstancesOutput.


        :param internal_sync: The internal_sync of this OptionsForListDevInstancesOutput.  # noqa: E501
        :type: int
        """

        self._internal_sync = internal_sync

    @property
    def meta_cache_capacity(self):
        """Gets the meta_cache_capacity of this OptionsForListDevInstancesOutput.  # noqa: E501


        :return: The meta_cache_capacity of this OptionsForListDevInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._meta_cache_capacity

    @meta_cache_capacity.setter
    def meta_cache_capacity(self, meta_cache_capacity):
        """Sets the meta_cache_capacity of this OptionsForListDevInstancesOutput.


        :param meta_cache_capacity: The meta_cache_capacity of this OptionsForListDevInstancesOutput.  # noqa: E501
        :type: int
        """

        self._meta_cache_capacity = meta_cache_capacity

    @property
    def meta_cache_expiry_minutes(self):
        """Gets the meta_cache_expiry_minutes of this OptionsForListDevInstancesOutput.  # noqa: E501


        :return: The meta_cache_expiry_minutes of this OptionsForListDevInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._meta_cache_expiry_minutes

    @meta_cache_expiry_minutes.setter
    def meta_cache_expiry_minutes(self, meta_cache_expiry_minutes):
        """Sets the meta_cache_expiry_minutes of this OptionsForListDevInstancesOutput.


        :param meta_cache_expiry_minutes: The meta_cache_expiry_minutes of this OptionsForListDevInstancesOutput.  # noqa: E501
        :type: int
        """

        self._meta_cache_expiry_minutes = meta_cache_expiry_minutes

    @property
    def meta_cache_expiry_msec(self):
        """Gets the meta_cache_expiry_msec of this OptionsForListDevInstancesOutput.  # noqa: E501


        :return: The meta_cache_expiry_msec of this OptionsForListDevInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._meta_cache_expiry_msec

    @meta_cache_expiry_msec.setter
    def meta_cache_expiry_msec(self, meta_cache_expiry_msec):
        """Sets the meta_cache_expiry_msec of this OptionsForListDevInstancesOutput.


        :param meta_cache_expiry_msec: The meta_cache_expiry_msec of this OptionsForListDevInstancesOutput.  # noqa: E501
        :type: int
        """

        self._meta_cache_expiry_msec = meta_cache_expiry_msec

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OptionsForListDevInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OptionsForListDevInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OptionsForListDevInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
