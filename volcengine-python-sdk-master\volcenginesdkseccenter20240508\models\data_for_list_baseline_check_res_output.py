# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListBaselineCheckResOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'checklist_id': 'int',
        'checklist_name': 'str',
        'checklist_status': 'str',
        'detect_status': 'str',
        'failed_detail': 'str',
        'level': 'str',
        'whitelist_detail': 'str',
        'whitelist_status': 'bool'
    }

    attribute_map = {
        'checklist_id': 'ChecklistID',
        'checklist_name': 'ChecklistName',
        'checklist_status': 'ChecklistStatus',
        'detect_status': 'DetectStatus',
        'failed_detail': 'FailedDetail',
        'level': 'Level',
        'whitelist_detail': 'WhitelistDetail',
        'whitelist_status': 'WhitelistStatus'
    }

    def __init__(self, checklist_id=None, checklist_name=None, checklist_status=None, detect_status=None, failed_detail=None, level=None, whitelist_detail=None, whitelist_status=None, _configuration=None):  # noqa: E501
        """DataForListBaselineCheckResOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._checklist_id = None
        self._checklist_name = None
        self._checklist_status = None
        self._detect_status = None
        self._failed_detail = None
        self._level = None
        self._whitelist_detail = None
        self._whitelist_status = None
        self.discriminator = None

        if checklist_id is not None:
            self.checklist_id = checklist_id
        if checklist_name is not None:
            self.checklist_name = checklist_name
        if checklist_status is not None:
            self.checklist_status = checklist_status
        if detect_status is not None:
            self.detect_status = detect_status
        if failed_detail is not None:
            self.failed_detail = failed_detail
        if level is not None:
            self.level = level
        if whitelist_detail is not None:
            self.whitelist_detail = whitelist_detail
        if whitelist_status is not None:
            self.whitelist_status = whitelist_status

    @property
    def checklist_id(self):
        """Gets the checklist_id of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The checklist_id of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: int
        """
        return self._checklist_id

    @checklist_id.setter
    def checklist_id(self, checklist_id):
        """Sets the checklist_id of this DataForListBaselineCheckResOutput.


        :param checklist_id: The checklist_id of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: int
        """

        self._checklist_id = checklist_id

    @property
    def checklist_name(self):
        """Gets the checklist_name of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The checklist_name of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._checklist_name

    @checklist_name.setter
    def checklist_name(self, checklist_name):
        """Sets the checklist_name of this DataForListBaselineCheckResOutput.


        :param checklist_name: The checklist_name of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._checklist_name = checklist_name

    @property
    def checklist_status(self):
        """Gets the checklist_status of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The checklist_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._checklist_status

    @checklist_status.setter
    def checklist_status(self, checklist_status):
        """Sets the checklist_status of this DataForListBaselineCheckResOutput.


        :param checklist_status: The checklist_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._checklist_status = checklist_status

    @property
    def detect_status(self):
        """Gets the detect_status of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The detect_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_status

    @detect_status.setter
    def detect_status(self, detect_status):
        """Sets the detect_status of this DataForListBaselineCheckResOutput.


        :param detect_status: The detect_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._detect_status = detect_status

    @property
    def failed_detail(self):
        """Gets the failed_detail of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The failed_detail of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._failed_detail

    @failed_detail.setter
    def failed_detail(self, failed_detail):
        """Sets the failed_detail of this DataForListBaselineCheckResOutput.


        :param failed_detail: The failed_detail of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._failed_detail = failed_detail

    @property
    def level(self):
        """Gets the level of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The level of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this DataForListBaselineCheckResOutput.


        :param level: The level of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def whitelist_detail(self):
        """Gets the whitelist_detail of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The whitelist_detail of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: str
        """
        return self._whitelist_detail

    @whitelist_detail.setter
    def whitelist_detail(self, whitelist_detail):
        """Sets the whitelist_detail of this DataForListBaselineCheckResOutput.


        :param whitelist_detail: The whitelist_detail of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: str
        """

        self._whitelist_detail = whitelist_detail

    @property
    def whitelist_status(self):
        """Gets the whitelist_status of this DataForListBaselineCheckResOutput.  # noqa: E501


        :return: The whitelist_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :rtype: bool
        """
        return self._whitelist_status

    @whitelist_status.setter
    def whitelist_status(self, whitelist_status):
        """Sets the whitelist_status of this DataForListBaselineCheckResOutput.


        :param whitelist_status: The whitelist_status of this DataForListBaselineCheckResOutput.  # noqa: E501
        :type: bool
        """

        self._whitelist_status = whitelist_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListBaselineCheckResOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListBaselineCheckResOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListBaselineCheckResOutput):
            return True

        return self.to_dict() != other.to_dict()
