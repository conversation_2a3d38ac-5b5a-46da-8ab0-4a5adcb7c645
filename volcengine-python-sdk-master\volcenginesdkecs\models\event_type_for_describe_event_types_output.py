# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EventTypeForDescribeEventTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'kind': 'str',
        'response_required': 'bool',
        'status': 'list[str]',
        'title': 'str',
        'type': 'str'
    }

    attribute_map = {
        'kind': 'Kind',
        'response_required': 'ResponseRequired',
        'status': 'Status',
        'title': 'Title',
        'type': 'Type'
    }

    def __init__(self, kind=None, response_required=None, status=None, title=None, type=None, _configuration=None):  # noqa: E501
        """EventTypeForDescribeEventTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._kind = None
        self._response_required = None
        self._status = None
        self._title = None
        self._type = None
        self.discriminator = None

        if kind is not None:
            self.kind = kind
        if response_required is not None:
            self.response_required = response_required
        if status is not None:
            self.status = status
        if title is not None:
            self.title = title
        if type is not None:
            self.type = type

    @property
    def kind(self):
        """Gets the kind of this EventTypeForDescribeEventTypesOutput.  # noqa: E501


        :return: The kind of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this EventTypeForDescribeEventTypesOutput.


        :param kind: The kind of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def response_required(self):
        """Gets the response_required of this EventTypeForDescribeEventTypesOutput.  # noqa: E501


        :return: The response_required of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._response_required

    @response_required.setter
    def response_required(self, response_required):
        """Sets the response_required of this EventTypeForDescribeEventTypesOutput.


        :param response_required: The response_required of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :type: bool
        """

        self._response_required = response_required

    @property
    def status(self):
        """Gets the status of this EventTypeForDescribeEventTypesOutput.  # noqa: E501


        :return: The status of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this EventTypeForDescribeEventTypesOutput.


        :param status: The status of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def title(self):
        """Gets the title of this EventTypeForDescribeEventTypesOutput.  # noqa: E501


        :return: The title of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this EventTypeForDescribeEventTypesOutput.


        :param title: The title of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def type(self):
        """Gets the type of this EventTypeForDescribeEventTypesOutput.  # noqa: E501


        :return: The type of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this EventTypeForDescribeEventTypesOutput.


        :param type: The type of this EventTypeForDescribeEventTypesOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EventTypeForDescribeEventTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EventTypeForDescribeEventTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EventTypeForDescribeEventTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
