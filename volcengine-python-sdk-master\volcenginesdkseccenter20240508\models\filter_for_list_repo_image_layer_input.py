# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRepoImageLayerInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pkg_name': 'str',
        'vuln_id': 'str'
    }

    attribute_map = {
        'pkg_name': 'PkgName',
        'vuln_id': 'VulnID'
    }

    def __init__(self, pkg_name=None, vuln_id=None, _configuration=None):  # noqa: E501
        """FilterForListRepoImageLayerInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pkg_name = None
        self._vuln_id = None
        self.discriminator = None

        if pkg_name is not None:
            self.pkg_name = pkg_name
        if vuln_id is not None:
            self.vuln_id = vuln_id

    @property
    def pkg_name(self):
        """Gets the pkg_name of this FilterForListRepoImageLayerInput.  # noqa: E501


        :return: The pkg_name of this FilterForListRepoImageLayerInput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, pkg_name):
        """Sets the pkg_name of this FilterForListRepoImageLayerInput.


        :param pkg_name: The pkg_name of this FilterForListRepoImageLayerInput.  # noqa: E501
        :type: str
        """

        self._pkg_name = pkg_name

    @property
    def vuln_id(self):
        """Gets the vuln_id of this FilterForListRepoImageLayerInput.  # noqa: E501


        :return: The vuln_id of this FilterForListRepoImageLayerInput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_id

    @vuln_id.setter
    def vuln_id(self, vuln_id):
        """Sets the vuln_id of this FilterForListRepoImageLayerInput.


        :param vuln_id: The vuln_id of this FilterForListRepoImageLayerInput.  # noqa: E501
        :type: str
        """

        self._vuln_id = vuln_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRepoImageLayerInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRepoImageLayerInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRepoImageLayerInput):
            return True

        return self.to_dict() != other.to_dict()
