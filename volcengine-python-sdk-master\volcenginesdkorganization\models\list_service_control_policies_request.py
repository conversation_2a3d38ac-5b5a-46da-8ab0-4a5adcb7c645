# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListServiceControlPoliciesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'policy_type': 'str',
        'query': 'str',
        'sort_by': 'str',
        'sort_order': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'policy_type': 'PolicyType',
        'query': 'Query',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder'
    }

    def __init__(self, page_number=None, page_size=None, policy_type=None, query=None, sort_by=None, sort_order=None, _configuration=None):  # noqa: E501
        """ListServiceControlPoliciesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._policy_type = None
        self._query = None
        self._sort_by = None
        self._sort_order = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if policy_type is not None:
            self.policy_type = policy_type
        if query is not None:
            self.query = query
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order

    @property
    def page_number(self):
        """Gets the page_number of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The page_number of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListServiceControlPoliciesRequest.


        :param page_number: The page_number of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The page_size of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListServiceControlPoliciesRequest.


        :param page_size: The page_size of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def policy_type(self):
        """Gets the policy_type of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The policy_type of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_type

    @policy_type.setter
    def policy_type(self, policy_type):
        """Sets the policy_type of this ListServiceControlPoliciesRequest.


        :param policy_type: The policy_type of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._policy_type = policy_type

    @property
    def query(self):
        """Gets the query of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The query of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this ListServiceControlPoliciesRequest.


        :param query: The query of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._query = query

    @property
    def sort_by(self):
        """Gets the sort_by of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The sort_by of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListServiceControlPoliciesRequest.


        :param sort_by: The sort_by of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListServiceControlPoliciesRequest.  # noqa: E501


        :return: The sort_order of this ListServiceControlPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListServiceControlPoliciesRequest.


        :param sort_order: The sort_order of this ListServiceControlPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListServiceControlPoliciesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListServiceControlPoliciesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListServiceControlPoliciesRequest):
            return True

        return self.to_dict() != other.to_dict()
