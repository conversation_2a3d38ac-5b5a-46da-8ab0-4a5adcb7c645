# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNodeSpecInOneStepRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'node_specs_assigns': 'list[NodeSpecsAssignForModifyNodeSpecInOneStepInput]'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'node_specs_assigns': 'NodeSpecsAssigns'
    }

    def __init__(self, instance_id=None, node_specs_assigns=None, _configuration=None):  # noqa: E501
        """ModifyNodeSpecInOneStepRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._node_specs_assigns = None
        self.discriminator = None

        self.instance_id = instance_id
        if node_specs_assigns is not None:
            self.node_specs_assigns = node_specs_assigns

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyNodeSpecInOneStepRequest.  # noqa: E501


        :return: The instance_id of this ModifyNodeSpecInOneStepRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyNodeSpecInOneStepRequest.


        :param instance_id: The instance_id of this ModifyNodeSpecInOneStepRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def node_specs_assigns(self):
        """Gets the node_specs_assigns of this ModifyNodeSpecInOneStepRequest.  # noqa: E501


        :return: The node_specs_assigns of this ModifyNodeSpecInOneStepRequest.  # noqa: E501
        :rtype: list[NodeSpecsAssignForModifyNodeSpecInOneStepInput]
        """
        return self._node_specs_assigns

    @node_specs_assigns.setter
    def node_specs_assigns(self, node_specs_assigns):
        """Sets the node_specs_assigns of this ModifyNodeSpecInOneStepRequest.


        :param node_specs_assigns: The node_specs_assigns of this ModifyNodeSpecInOneStepRequest.  # noqa: E501
        :type: list[NodeSpecsAssignForModifyNodeSpecInOneStepInput]
        """

        self._node_specs_assigns = node_specs_assigns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNodeSpecInOneStepRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNodeSpecInOneStepRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNodeSpecInOneStepRequest):
            return True

        return self.to_dict() != other.to_dict()
