# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuestionForListQuestionnaireAnswerDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_require': 'int',
        'options': 'list[OptionForListQuestionnaireAnswerDataAPIV2Output]',
        'question': 'str',
        'question_id': 'int',
        'question_sub_tag': 'str',
        'question_tag': 'str'
    }

    attribute_map = {
        'is_require': 'IsRequire',
        'options': 'Options',
        'question': 'Question',
        'question_id': 'QuestionId',
        'question_sub_tag': 'QuestionSubTag',
        'question_tag': 'QuestionTag'
    }

    def __init__(self, is_require=None, options=None, question=None, question_id=None, question_sub_tag=None, question_tag=None, _configuration=None):  # noqa: E501
        """QuestionForListQuestionnaireAnswerDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_require = None
        self._options = None
        self._question = None
        self._question_id = None
        self._question_sub_tag = None
        self._question_tag = None
        self.discriminator = None

        if is_require is not None:
            self.is_require = is_require
        if options is not None:
            self.options = options
        if question is not None:
            self.question = question
        if question_id is not None:
            self.question_id = question_id
        if question_sub_tag is not None:
            self.question_sub_tag = question_sub_tag
        if question_tag is not None:
            self.question_tag = question_tag

    @property
    def is_require(self):
        """Gets the is_require of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The is_require of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._is_require

    @is_require.setter
    def is_require(self, is_require):
        """Sets the is_require of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param is_require: The is_require of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._is_require = is_require

    @property
    def options(self):
        """Gets the options of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The options of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: list[OptionForListQuestionnaireAnswerDataAPIV2Output]
        """
        return self._options

    @options.setter
    def options(self, options):
        """Sets the options of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param options: The options of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: list[OptionForListQuestionnaireAnswerDataAPIV2Output]
        """

        self._options = options

    @property
    def question(self):
        """Gets the question of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._question

    @question.setter
    def question(self, question):
        """Sets the question of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param question: The question of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._question = question

    @property
    def question_id(self):
        """Gets the question_id of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question_id of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._question_id

    @question_id.setter
    def question_id(self, question_id):
        """Sets the question_id of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param question_id: The question_id of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._question_id = question_id

    @property
    def question_sub_tag(self):
        """Gets the question_sub_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question_sub_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._question_sub_tag

    @question_sub_tag.setter
    def question_sub_tag(self, question_sub_tag):
        """Sets the question_sub_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param question_sub_tag: The question_sub_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._question_sub_tag = question_sub_tag

    @property
    def question_tag(self):
        """Gets the question_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._question_tag

    @question_tag.setter
    def question_tag(self, question_tag):
        """Sets the question_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.


        :param question_tag: The question_tag of this QuestionForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._question_tag = question_tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuestionForListQuestionnaireAnswerDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuestionForListQuestionnaireAnswerDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuestionForListQuestionnaireAnswerDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
