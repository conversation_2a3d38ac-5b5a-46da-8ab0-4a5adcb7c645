# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeInvocationResultsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'command_id': 'str',
        'instance_id': 'str',
        'invocation_id': 'str',
        'invocation_result_status': 'str',
        'page_number': 'int',
        'page_size': 'int'
    }

    attribute_map = {
        'command_id': 'CommandId',
        'instance_id': 'InstanceId',
        'invocation_id': 'InvocationId',
        'invocation_result_status': 'InvocationResultStatus',
        'page_number': 'PageNumber',
        'page_size': 'PageSize'
    }

    def __init__(self, command_id=None, instance_id=None, invocation_id=None, invocation_result_status=None, page_number=None, page_size=None, _configuration=None):  # noqa: E501
        """DescribeInvocationResultsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._command_id = None
        self._instance_id = None
        self._invocation_id = None
        self._invocation_result_status = None
        self._page_number = None
        self._page_size = None
        self.discriminator = None

        if command_id is not None:
            self.command_id = command_id
        if instance_id is not None:
            self.instance_id = instance_id
        self.invocation_id = invocation_id
        if invocation_result_status is not None:
            self.invocation_result_status = invocation_result_status
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size

    @property
    def command_id(self):
        """Gets the command_id of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The command_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: str
        """
        return self._command_id

    @command_id.setter
    def command_id(self, command_id):
        """Sets the command_id of this DescribeInvocationResultsRequest.


        :param command_id: The command_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: str
        """

        self._command_id = command_id

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The instance_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeInvocationResultsRequest.


        :param instance_id: The instance_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def invocation_id(self):
        """Gets the invocation_id of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The invocation_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: str
        """
        return self._invocation_id

    @invocation_id.setter
    def invocation_id(self, invocation_id):
        """Sets the invocation_id of this DescribeInvocationResultsRequest.


        :param invocation_id: The invocation_id of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and invocation_id is None:
            raise ValueError("Invalid value for `invocation_id`, must not be `None`")  # noqa: E501

        self._invocation_id = invocation_id

    @property
    def invocation_result_status(self):
        """Gets the invocation_result_status of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The invocation_result_status of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: str
        """
        return self._invocation_result_status

    @invocation_result_status.setter
    def invocation_result_status(self, invocation_result_status):
        """Sets the invocation_result_status of this DescribeInvocationResultsRequest.


        :param invocation_result_status: The invocation_result_status of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: str
        """

        self._invocation_result_status = invocation_result_status

    @property
    def page_number(self):
        """Gets the page_number of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The page_number of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeInvocationResultsRequest.


        :param page_number: The page_number of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeInvocationResultsRequest.  # noqa: E501


        :return: The page_size of this DescribeInvocationResultsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeInvocationResultsRequest.


        :param page_size: The page_size of this DescribeInvocationResultsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeInvocationResultsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeInvocationResultsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeInvocationResultsRequest):
            return True

        return self.to_dict() != other.to_dict()
