# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReplaceCertificateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_center_certificate_id': 'str',
        'certificate_id': 'str',
        'certificate_name': 'str',
        'certificate_source': 'str',
        'description': 'str',
        'old_certificate_id': 'str',
        'private_key': 'str',
        'project_name': 'str',
        'public_key': 'str',
        'tags': 'list[TagForReplaceCertificateInput]',
        'update_mode': 'str'
    }

    attribute_map = {
        'cert_center_certificate_id': 'CertCenterCertificateId',
        'certificate_id': 'CertificateId',
        'certificate_name': 'CertificateName',
        'certificate_source': 'CertificateSource',
        'description': 'Description',
        'old_certificate_id': 'OldCertificateId',
        'private_key': 'PrivateKey',
        'project_name': 'ProjectName',
        'public_key': 'PublicKey',
        'tags': 'Tags',
        'update_mode': 'UpdateMode'
    }

    def __init__(self, cert_center_certificate_id=None, certificate_id=None, certificate_name=None, certificate_source=None, description=None, old_certificate_id=None, private_key=None, project_name=None, public_key=None, tags=None, update_mode=None, _configuration=None):  # noqa: E501
        """ReplaceCertificateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_center_certificate_id = None
        self._certificate_id = None
        self._certificate_name = None
        self._certificate_source = None
        self._description = None
        self._old_certificate_id = None
        self._private_key = None
        self._project_name = None
        self._public_key = None
        self._tags = None
        self._update_mode = None
        self.discriminator = None

        if cert_center_certificate_id is not None:
            self.cert_center_certificate_id = cert_center_certificate_id
        if certificate_id is not None:
            self.certificate_id = certificate_id
        if certificate_name is not None:
            self.certificate_name = certificate_name
        if certificate_source is not None:
            self.certificate_source = certificate_source
        if description is not None:
            self.description = description
        self.old_certificate_id = old_certificate_id
        if private_key is not None:
            self.private_key = private_key
        if project_name is not None:
            self.project_name = project_name
        if public_key is not None:
            self.public_key = public_key
        if tags is not None:
            self.tags = tags
        self.update_mode = update_mode

    @property
    def cert_center_certificate_id(self):
        """Gets the cert_center_certificate_id of this ReplaceCertificateRequest.  # noqa: E501


        :return: The cert_center_certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_center_certificate_id

    @cert_center_certificate_id.setter
    def cert_center_certificate_id(self, cert_center_certificate_id):
        """Sets the cert_center_certificate_id of this ReplaceCertificateRequest.


        :param cert_center_certificate_id: The cert_center_certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._cert_center_certificate_id = cert_center_certificate_id

    @property
    def certificate_id(self):
        """Gets the certificate_id of this ReplaceCertificateRequest.  # noqa: E501


        :return: The certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this ReplaceCertificateRequest.


        :param certificate_id: The certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def certificate_name(self):
        """Gets the certificate_name of this ReplaceCertificateRequest.  # noqa: E501


        :return: The certificate_name of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_name

    @certificate_name.setter
    def certificate_name(self, certificate_name):
        """Sets the certificate_name of this ReplaceCertificateRequest.


        :param certificate_name: The certificate_name of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                certificate_name is not None and len(certificate_name) > 128):
            raise ValueError("Invalid value for `certificate_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                certificate_name is not None and len(certificate_name) < 1):
            raise ValueError("Invalid value for `certificate_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._certificate_name = certificate_name

    @property
    def certificate_source(self):
        """Gets the certificate_source of this ReplaceCertificateRequest.  # noqa: E501


        :return: The certificate_source of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_source

    @certificate_source.setter
    def certificate_source(self, certificate_source):
        """Sets the certificate_source of this ReplaceCertificateRequest.


        :param certificate_source: The certificate_source of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._certificate_source = certificate_source

    @property
    def description(self):
        """Gets the description of this ReplaceCertificateRequest.  # noqa: E501


        :return: The description of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ReplaceCertificateRequest.


        :param description: The description of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def old_certificate_id(self):
        """Gets the old_certificate_id of this ReplaceCertificateRequest.  # noqa: E501


        :return: The old_certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._old_certificate_id

    @old_certificate_id.setter
    def old_certificate_id(self, old_certificate_id):
        """Sets the old_certificate_id of this ReplaceCertificateRequest.


        :param old_certificate_id: The old_certificate_id of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and old_certificate_id is None:
            raise ValueError("Invalid value for `old_certificate_id`, must not be `None`")  # noqa: E501

        self._old_certificate_id = old_certificate_id

    @property
    def private_key(self):
        """Gets the private_key of this ReplaceCertificateRequest.  # noqa: E501


        :return: The private_key of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this ReplaceCertificateRequest.


        :param private_key: The private_key of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    @property
    def project_name(self):
        """Gets the project_name of this ReplaceCertificateRequest.  # noqa: E501


        :return: The project_name of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ReplaceCertificateRequest.


        :param project_name: The project_name of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def public_key(self):
        """Gets the public_key of this ReplaceCertificateRequest.  # noqa: E501


        :return: The public_key of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._public_key

    @public_key.setter
    def public_key(self, public_key):
        """Sets the public_key of this ReplaceCertificateRequest.


        :param public_key: The public_key of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """

        self._public_key = public_key

    @property
    def tags(self):
        """Gets the tags of this ReplaceCertificateRequest.  # noqa: E501


        :return: The tags of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: list[TagForReplaceCertificateInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ReplaceCertificateRequest.


        :param tags: The tags of this ReplaceCertificateRequest.  # noqa: E501
        :type: list[TagForReplaceCertificateInput]
        """

        self._tags = tags

    @property
    def update_mode(self):
        """Gets the update_mode of this ReplaceCertificateRequest.  # noqa: E501


        :return: The update_mode of this ReplaceCertificateRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_mode

    @update_mode.setter
    def update_mode(self, update_mode):
        """Sets the update_mode of this ReplaceCertificateRequest.


        :param update_mode: The update_mode of this ReplaceCertificateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and update_mode is None:
            raise ValueError("Invalid value for `update_mode`, must not be `None`")  # noqa: E501

        self._update_mode = update_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReplaceCertificateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReplaceCertificateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReplaceCertificateRequest):
            return True

        return self.to_dict() != other.to_dict()
