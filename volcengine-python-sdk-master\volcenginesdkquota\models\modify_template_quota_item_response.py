# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTemplateQuotaItemResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'dimensions': 'list[DimensionForModifyTemplateQuotaItemOutput]',
        'provider_code': 'str',
        'quota_code': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'dimensions': 'Dimensions',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode'
    }

    def __init__(self, account_id=None, dimensions=None, provider_code=None, quota_code=None, _configuration=None):  # noqa: E501
        """ModifyTemplateQuotaItemResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._dimensions = None
        self._provider_code = None
        self._quota_code = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if dimensions is not None:
            self.dimensions = dimensions
        if provider_code is not None:
            self.provider_code = provider_code
        if quota_code is not None:
            self.quota_code = quota_code

    @property
    def account_id(self):
        """Gets the account_id of this ModifyTemplateQuotaItemResponse.  # noqa: E501


        :return: The account_id of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ModifyTemplateQuotaItemResponse.


        :param account_id: The account_id of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def dimensions(self):
        """Gets the dimensions of this ModifyTemplateQuotaItemResponse.  # noqa: E501


        :return: The dimensions of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :rtype: list[DimensionForModifyTemplateQuotaItemOutput]
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this ModifyTemplateQuotaItemResponse.


        :param dimensions: The dimensions of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :type: list[DimensionForModifyTemplateQuotaItemOutput]
        """

        self._dimensions = dimensions

    @property
    def provider_code(self):
        """Gets the provider_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501


        :return: The provider_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this ModifyTemplateQuotaItemResponse.


        :param provider_code: The provider_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501


        :return: The quota_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this ModifyTemplateQuotaItemResponse.


        :param quota_code: The quota_code of this ModifyTemplateQuotaItemResponse.  # noqa: E501
        :type: str
        """

        self._quota_code = quota_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTemplateQuotaItemResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTemplateQuotaItemResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTemplateQuotaItemResponse):
            return True

        return self.to_dict() != other.to_dict()
