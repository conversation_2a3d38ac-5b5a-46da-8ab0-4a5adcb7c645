# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PresetDatasetForCreateModelCustomizationJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dataset_version_id': 'str',
        'inject_multiplier': 'float',
        'inject_sample_count': 'int'
    }

    attribute_map = {
        'dataset_version_id': 'DatasetVersionId',
        'inject_multiplier': 'InjectMultiplier',
        'inject_sample_count': 'InjectSampleCount'
    }

    def __init__(self, dataset_version_id=None, inject_multiplier=None, inject_sample_count=None, _configuration=None):  # noqa: E501
        """PresetDatasetForCreateModelCustomizationJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dataset_version_id = None
        self._inject_multiplier = None
        self._inject_sample_count = None
        self.discriminator = None

        if dataset_version_id is not None:
            self.dataset_version_id = dataset_version_id
        if inject_multiplier is not None:
            self.inject_multiplier = inject_multiplier
        if inject_sample_count is not None:
            self.inject_sample_count = inject_sample_count

    @property
    def dataset_version_id(self):
        """Gets the dataset_version_id of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501


        :return: The dataset_version_id of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._dataset_version_id

    @dataset_version_id.setter
    def dataset_version_id(self, dataset_version_id):
        """Sets the dataset_version_id of this PresetDatasetForCreateModelCustomizationJobInput.


        :param dataset_version_id: The dataset_version_id of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :type: str
        """

        self._dataset_version_id = dataset_version_id

    @property
    def inject_multiplier(self):
        """Gets the inject_multiplier of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501


        :return: The inject_multiplier of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :rtype: float
        """
        return self._inject_multiplier

    @inject_multiplier.setter
    def inject_multiplier(self, inject_multiplier):
        """Sets the inject_multiplier of this PresetDatasetForCreateModelCustomizationJobInput.


        :param inject_multiplier: The inject_multiplier of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :type: float
        """

        self._inject_multiplier = inject_multiplier

    @property
    def inject_sample_count(self):
        """Gets the inject_sample_count of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501


        :return: The inject_sample_count of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :rtype: int
        """
        return self._inject_sample_count

    @inject_sample_count.setter
    def inject_sample_count(self, inject_sample_count):
        """Sets the inject_sample_count of this PresetDatasetForCreateModelCustomizationJobInput.


        :param inject_sample_count: The inject_sample_count of this PresetDatasetForCreateModelCustomizationJobInput.  # noqa: E501
        :type: int
        """

        self._inject_sample_count = inject_sample_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PresetDatasetForCreateModelCustomizationJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PresetDatasetForCreateModelCustomizationJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PresetDatasetForCreateModelCustomizationJobInput):
            return True

        return self.to_dict() != other.to_dict()
