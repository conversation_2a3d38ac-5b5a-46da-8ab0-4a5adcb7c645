# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDataProjectWithBindWidthAndFlowRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data': 'str',
        'end_time': 'str',
        'space_id': 'str',
        'start_time': 'str',
        'stream_name': 'str'
    }

    attribute_map = {
        'data': 'Data',
        'end_time': 'EndTime',
        'space_id': 'SpaceID',
        'start_time': 'StartTime',
        'stream_name': 'StreamName'
    }

    def __init__(self, data=None, end_time=None, space_id=None, start_time=None, stream_name=None, _configuration=None):  # noqa: E501
        """GetDataProjectWithBindWidthAndFlowRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data = None
        self._end_time = None
        self._space_id = None
        self._start_time = None
        self._stream_name = None
        self.discriminator = None

        self.data = data
        self.end_time = end_time
        self.space_id = space_id
        self.start_time = start_time
        self.stream_name = stream_name

    @property
    def data(self):
        """Gets the data of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501


        :return: The data of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this GetDataProjectWithBindWidthAndFlowRequest.


        :param data: The data of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and data is None:
            raise ValueError("Invalid value for `data`, must not be `None`")  # noqa: E501

        self._data = data

    @property
    def end_time(self):
        """Gets the end_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501


        :return: The end_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetDataProjectWithBindWidthAndFlowRequest.


        :param end_time: The end_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def space_id(self):
        """Gets the space_id of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501


        :return: The space_id of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this GetDataProjectWithBindWidthAndFlowRequest.


        :param space_id: The space_id of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_id is None:
            raise ValueError("Invalid value for `space_id`, must not be `None`")  # noqa: E501

        self._space_id = space_id

    @property
    def start_time(self):
        """Gets the start_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501


        :return: The start_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GetDataProjectWithBindWidthAndFlowRequest.


        :param start_time: The start_time of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def stream_name(self):
        """Gets the stream_name of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501


        :return: The stream_name of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_name

    @stream_name.setter
    def stream_name(self, stream_name):
        """Sets the stream_name of this GetDataProjectWithBindWidthAndFlowRequest.


        :param stream_name: The stream_name of this GetDataProjectWithBindWidthAndFlowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_name is None:
            raise ValueError("Invalid value for `stream_name`, must not be `None`")  # noqa: E501

        self._stream_name = stream_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDataProjectWithBindWidthAndFlowRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDataProjectWithBindWidthAndFlowRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDataProjectWithBindWidthAndFlowRequest):
            return True

        return self.to_dict() != other.to_dict()
