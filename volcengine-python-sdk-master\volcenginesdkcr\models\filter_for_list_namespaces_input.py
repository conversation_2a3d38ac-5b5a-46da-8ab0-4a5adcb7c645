# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListNamespacesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'in_project': 'bool',
        'names': 'list[str]',
        'projects': 'list[str]'
    }

    attribute_map = {
        'in_project': 'InProject',
        'names': 'Names',
        'projects': 'Projects'
    }

    def __init__(self, in_project=None, names=None, projects=None, _configuration=None):  # noqa: E501
        """FilterForListNamespacesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._in_project = None
        self._names = None
        self._projects = None
        self.discriminator = None

        if in_project is not None:
            self.in_project = in_project
        if names is not None:
            self.names = names
        if projects is not None:
            self.projects = projects

    @property
    def in_project(self):
        """Gets the in_project of this FilterForListNamespacesInput.  # noqa: E501


        :return: The in_project of this FilterForListNamespacesInput.  # noqa: E501
        :rtype: bool
        """
        return self._in_project

    @in_project.setter
    def in_project(self, in_project):
        """Sets the in_project of this FilterForListNamespacesInput.


        :param in_project: The in_project of this FilterForListNamespacesInput.  # noqa: E501
        :type: bool
        """

        self._in_project = in_project

    @property
    def names(self):
        """Gets the names of this FilterForListNamespacesInput.  # noqa: E501


        :return: The names of this FilterForListNamespacesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._names

    @names.setter
    def names(self, names):
        """Sets the names of this FilterForListNamespacesInput.


        :param names: The names of this FilterForListNamespacesInput.  # noqa: E501
        :type: list[str]
        """

        self._names = names

    @property
    def projects(self):
        """Gets the projects of this FilterForListNamespacesInput.  # noqa: E501


        :return: The projects of this FilterForListNamespacesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._projects

    @projects.setter
    def projects(self, projects):
        """Sets the projects of this FilterForListNamespacesInput.


        :param projects: The projects of this FilterForListNamespacesInput.  # noqa: E501
        :type: list[str]
        """

        self._projects = projects

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListNamespacesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListNamespacesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListNamespacesInput):
            return True

        return self.to_dict() != other.to_dict()
