# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'diagnose_config': 'list[DiagnoseConfigForUpdateJobInput]',
        'dry_run': 'bool',
        'id': 'str',
        'name': 'str',
        'resource_config': 'ResourceConfigForUpdateJobInput',
        'retry_config': 'RetryConfigForUpdateJobInput'
    }

    attribute_map = {
        'description': 'Description',
        'diagnose_config': 'DiagnoseConfig',
        'dry_run': 'DryRun',
        'id': 'Id',
        'name': 'Name',
        'resource_config': 'ResourceConfig',
        'retry_config': 'RetryConfig'
    }

    def __init__(self, description=None, diagnose_config=None, dry_run=None, id=None, name=None, resource_config=None, retry_config=None, _configuration=None):  # noqa: E501
        """UpdateJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._diagnose_config = None
        self._dry_run = None
        self._id = None
        self._name = None
        self._resource_config = None
        self._retry_config = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if diagnose_config is not None:
            self.diagnose_config = diagnose_config
        if dry_run is not None:
            self.dry_run = dry_run
        self.id = id
        if name is not None:
            self.name = name
        if resource_config is not None:
            self.resource_config = resource_config
        if retry_config is not None:
            self.retry_config = retry_config

    @property
    def description(self):
        """Gets the description of this UpdateJobRequest.  # noqa: E501


        :return: The description of this UpdateJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateJobRequest.


        :param description: The description of this UpdateJobRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def diagnose_config(self):
        """Gets the diagnose_config of this UpdateJobRequest.  # noqa: E501


        :return: The diagnose_config of this UpdateJobRequest.  # noqa: E501
        :rtype: list[DiagnoseConfigForUpdateJobInput]
        """
        return self._diagnose_config

    @diagnose_config.setter
    def diagnose_config(self, diagnose_config):
        """Sets the diagnose_config of this UpdateJobRequest.


        :param diagnose_config: The diagnose_config of this UpdateJobRequest.  # noqa: E501
        :type: list[DiagnoseConfigForUpdateJobInput]
        """

        self._diagnose_config = diagnose_config

    @property
    def dry_run(self):
        """Gets the dry_run of this UpdateJobRequest.  # noqa: E501


        :return: The dry_run of this UpdateJobRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this UpdateJobRequest.


        :param dry_run: The dry_run of this UpdateJobRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def id(self):
        """Gets the id of this UpdateJobRequest.  # noqa: E501


        :return: The id of this UpdateJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateJobRequest.


        :param id: The id of this UpdateJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateJobRequest.  # noqa: E501


        :return: The name of this UpdateJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateJobRequest.


        :param name: The name of this UpdateJobRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def resource_config(self):
        """Gets the resource_config of this UpdateJobRequest.  # noqa: E501


        :return: The resource_config of this UpdateJobRequest.  # noqa: E501
        :rtype: ResourceConfigForUpdateJobInput
        """
        return self._resource_config

    @resource_config.setter
    def resource_config(self, resource_config):
        """Sets the resource_config of this UpdateJobRequest.


        :param resource_config: The resource_config of this UpdateJobRequest.  # noqa: E501
        :type: ResourceConfigForUpdateJobInput
        """

        self._resource_config = resource_config

    @property
    def retry_config(self):
        """Gets the retry_config of this UpdateJobRequest.  # noqa: E501


        :return: The retry_config of this UpdateJobRequest.  # noqa: E501
        :rtype: RetryConfigForUpdateJobInput
        """
        return self._retry_config

    @retry_config.setter
    def retry_config(self, retry_config):
        """Sets the retry_config of this UpdateJobRequest.


        :param retry_config: The retry_config of this UpdateJobRequest.  # noqa: E501
        :type: RetryConfigForUpdateJobInput
        """

        self._retry_config = retry_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateJobRequest):
            return True

        return self.to_dict() != other.to_dict()
