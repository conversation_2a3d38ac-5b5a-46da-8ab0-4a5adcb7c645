# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBListenerAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'certificate_id': 'str',
        'connection_timeout': 'int',
        'create_time': 'str',
        'description': 'str',
        'enabled': 'bool',
        'end_port': 'int',
        'listener_id': 'str',
        'listener_name': 'str',
        'load_balancer_id': 'str',
        'port': 'int',
        'protocol': 'str',
        'request_id': 'str',
        'security_policy_id': 'str',
        'security_policy_type': 'str',
        'server_group_id': 'str',
        'start_port': 'int',
        'status': 'str',
        'tags': 'list[TagForDescribeNLBListenerAttributesOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'certificate_id': 'CertificateId',
        'connection_timeout': 'ConnectionTimeout',
        'create_time': 'CreateTime',
        'description': 'Description',
        'enabled': 'Enabled',
        'end_port': 'EndPort',
        'listener_id': 'ListenerId',
        'listener_name': 'ListenerName',
        'load_balancer_id': 'LoadBalancerId',
        'port': 'Port',
        'protocol': 'Protocol',
        'request_id': 'RequestId',
        'security_policy_id': 'SecurityPolicyId',
        'security_policy_type': 'SecurityPolicyType',
        'server_group_id': 'ServerGroupId',
        'start_port': 'StartPort',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, certificate_id=None, connection_timeout=None, create_time=None, description=None, enabled=None, end_port=None, listener_id=None, listener_name=None, load_balancer_id=None, port=None, protocol=None, request_id=None, security_policy_id=None, security_policy_type=None, server_group_id=None, start_port=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeNLBListenerAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._certificate_id = None
        self._connection_timeout = None
        self._create_time = None
        self._description = None
        self._enabled = None
        self._end_port = None
        self._listener_id = None
        self._listener_name = None
        self._load_balancer_id = None
        self._port = None
        self._protocol = None
        self._request_id = None
        self._security_policy_id = None
        self._security_policy_type = None
        self._server_group_id = None
        self._start_port = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if certificate_id is not None:
            self.certificate_id = certificate_id
        if connection_timeout is not None:
            self.connection_timeout = connection_timeout
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if enabled is not None:
            self.enabled = enabled
        if end_port is not None:
            self.end_port = end_port
        if listener_id is not None:
            self.listener_id = listener_id
        if listener_name is not None:
            self.listener_name = listener_name
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if port is not None:
            self.port = port
        if protocol is not None:
            self.protocol = protocol
        if request_id is not None:
            self.request_id = request_id
        if security_policy_id is not None:
            self.security_policy_id = security_policy_id
        if security_policy_type is not None:
            self.security_policy_type = security_policy_type
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if start_port is not None:
            self.start_port = start_port
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeNLBListenerAttributesResponse.


        :param account_id: The account_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def certificate_id(self):
        """Gets the certificate_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The certificate_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this DescribeNLBListenerAttributesResponse.


        :param certificate_id: The certificate_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def connection_timeout(self):
        """Gets the connection_timeout of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The connection_timeout of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._connection_timeout

    @connection_timeout.setter
    def connection_timeout(self, connection_timeout):
        """Sets the connection_timeout of this DescribeNLBListenerAttributesResponse.


        :param connection_timeout: The connection_timeout of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._connection_timeout = connection_timeout

    @property
    def create_time(self):
        """Gets the create_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeNLBListenerAttributesResponse.


        :param create_time: The create_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The description of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeNLBListenerAttributesResponse.


        :param description: The description of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enabled(self):
        """Gets the enabled of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The enabled of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this DescribeNLBListenerAttributesResponse.


        :param enabled: The enabled of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def end_port(self):
        """Gets the end_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The end_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._end_port

    @end_port.setter
    def end_port(self, end_port):
        """Sets the end_port of this DescribeNLBListenerAttributesResponse.


        :param end_port: The end_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._end_port = end_port

    @property
    def listener_id(self):
        """Gets the listener_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The listener_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this DescribeNLBListenerAttributesResponse.


        :param listener_id: The listener_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def listener_name(self):
        """Gets the listener_name of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The listener_name of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._listener_name

    @listener_name.setter
    def listener_name(self, listener_name):
        """Sets the listener_name of this DescribeNLBListenerAttributesResponse.


        :param listener_name: The listener_name of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._listener_name = listener_name

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The load_balancer_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this DescribeNLBListenerAttributesResponse.


        :param load_balancer_id: The load_balancer_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def port(self):
        """Gets the port of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this DescribeNLBListenerAttributesResponse.


        :param port: The port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def protocol(self):
        """Gets the protocol of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The protocol of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DescribeNLBListenerAttributesResponse.


        :param protocol: The protocol of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def request_id(self):
        """Gets the request_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeNLBListenerAttributesResponse.


        :param request_id: The request_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def security_policy_id(self):
        """Gets the security_policy_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The security_policy_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_id

    @security_policy_id.setter
    def security_policy_id(self, security_policy_id):
        """Sets the security_policy_id of this DescribeNLBListenerAttributesResponse.


        :param security_policy_id: The security_policy_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._security_policy_id = security_policy_id

    @property
    def security_policy_type(self):
        """Gets the security_policy_type of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The security_policy_type of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_type

    @security_policy_type.setter
    def security_policy_type(self, security_policy_type):
        """Sets the security_policy_type of this DescribeNLBListenerAttributesResponse.


        :param security_policy_type: The security_policy_type of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._security_policy_type = security_policy_type

    @property
    def server_group_id(self):
        """Gets the server_group_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The server_group_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this DescribeNLBListenerAttributesResponse.


        :param server_group_id: The server_group_id of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def start_port(self):
        """Gets the start_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The start_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._start_port

    @start_port.setter
    def start_port(self, start_port):
        """Sets the start_port of this DescribeNLBListenerAttributesResponse.


        :param start_port: The start_port of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._start_port = start_port

    @property
    def status(self):
        """Gets the status of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The status of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNLBListenerAttributesResponse.


        :param status: The status of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeNLBListenerAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeNLBListenerAttributesResponse.


        :param tags: The tags of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeNLBListenerAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeNLBListenerAttributesResponse.


        :param update_time: The update_time of this DescribeNLBListenerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBListenerAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBListenerAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBListenerAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
