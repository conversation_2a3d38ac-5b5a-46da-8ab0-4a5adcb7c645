# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateLoopVideoAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'line_id': 'int',
        'loop_number': 'int',
        'loop_video': 'list[LoopVideoForUpdateLoopVideoAPIInput]'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'line_id': 'LineId',
        'loop_number': 'LoopNumber',
        'loop_video': 'LoopVideo'
    }

    def __init__(self, activity_id=None, line_id=None, loop_number=None, loop_video=None, _configuration=None):  # noqa: E501
        """UpdateLoopVideoAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._line_id = None
        self._loop_number = None
        self._loop_video = None
        self.discriminator = None

        self.activity_id = activity_id
        self.line_id = line_id
        self.loop_number = loop_number
        if loop_video is not None:
            self.loop_video = loop_video

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateLoopVideoAPIRequest.  # noqa: E501


        :return: The activity_id of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateLoopVideoAPIRequest.


        :param activity_id: The activity_id of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def line_id(self):
        """Gets the line_id of this UpdateLoopVideoAPIRequest.  # noqa: E501


        :return: The line_id of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._line_id

    @line_id.setter
    def line_id(self, line_id):
        """Sets the line_id of this UpdateLoopVideoAPIRequest.


        :param line_id: The line_id of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and line_id is None:
            raise ValueError("Invalid value for `line_id`, must not be `None`")  # noqa: E501

        self._line_id = line_id

    @property
    def loop_number(self):
        """Gets the loop_number of this UpdateLoopVideoAPIRequest.  # noqa: E501


        :return: The loop_number of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._loop_number

    @loop_number.setter
    def loop_number(self, loop_number):
        """Sets the loop_number of this UpdateLoopVideoAPIRequest.


        :param loop_number: The loop_number of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and loop_number is None:
            raise ValueError("Invalid value for `loop_number`, must not be `None`")  # noqa: E501

        self._loop_number = loop_number

    @property
    def loop_video(self):
        """Gets the loop_video of this UpdateLoopVideoAPIRequest.  # noqa: E501


        :return: The loop_video of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :rtype: list[LoopVideoForUpdateLoopVideoAPIInput]
        """
        return self._loop_video

    @loop_video.setter
    def loop_video(self, loop_video):
        """Sets the loop_video of this UpdateLoopVideoAPIRequest.


        :param loop_video: The loop_video of this UpdateLoopVideoAPIRequest.  # noqa: E501
        :type: list[LoopVideoForUpdateLoopVideoAPIInput]
        """

        self._loop_video = loop_video

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateLoopVideoAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateLoopVideoAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateLoopVideoAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
