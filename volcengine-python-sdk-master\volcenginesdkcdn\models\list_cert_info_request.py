# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCertInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_id': 'str',
        'cert_type': 'str',
        'configured_domain': 'str',
        'encry_type': 'str',
        'fuzzy_match': 'bool',
        'name': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'sort_rule': 'SortRuleForListCertInfoInput',
        'source': 'str',
        'status': 'str'
    }

    attribute_map = {
        'cert_id': 'CertId',
        'cert_type': 'CertType',
        'configured_domain': 'ConfiguredDomain',
        'encry_type': 'EncryType',
        'fuzzy_match': 'FuzzyMatch',
        'name': 'Name',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'sort_rule': 'SortRule',
        'source': 'Source',
        'status': 'Status'
    }

    def __init__(self, cert_id=None, cert_type=None, configured_domain=None, encry_type=None, fuzzy_match=None, name=None, page_num=None, page_size=None, sort_rule=None, source=None, status=None, _configuration=None):  # noqa: E501
        """ListCertInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_id = None
        self._cert_type = None
        self._configured_domain = None
        self._encry_type = None
        self._fuzzy_match = None
        self._name = None
        self._page_num = None
        self._page_size = None
        self._sort_rule = None
        self._source = None
        self._status = None
        self.discriminator = None

        if cert_id is not None:
            self.cert_id = cert_id
        if cert_type is not None:
            self.cert_type = cert_type
        if configured_domain is not None:
            self.configured_domain = configured_domain
        if encry_type is not None:
            self.encry_type = encry_type
        if fuzzy_match is not None:
            self.fuzzy_match = fuzzy_match
        if name is not None:
            self.name = name
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if sort_rule is not None:
            self.sort_rule = sort_rule
        self.source = source
        if status is not None:
            self.status = status

    @property
    def cert_id(self):
        """Gets the cert_id of this ListCertInfoRequest.  # noqa: E501


        :return: The cert_id of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_id

    @cert_id.setter
    def cert_id(self, cert_id):
        """Sets the cert_id of this ListCertInfoRequest.


        :param cert_id: The cert_id of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._cert_id = cert_id

    @property
    def cert_type(self):
        """Gets the cert_type of this ListCertInfoRequest.  # noqa: E501


        :return: The cert_type of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_type

    @cert_type.setter
    def cert_type(self, cert_type):
        """Sets the cert_type of this ListCertInfoRequest.


        :param cert_type: The cert_type of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._cert_type = cert_type

    @property
    def configured_domain(self):
        """Gets the configured_domain of this ListCertInfoRequest.  # noqa: E501


        :return: The configured_domain of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._configured_domain

    @configured_domain.setter
    def configured_domain(self, configured_domain):
        """Sets the configured_domain of this ListCertInfoRequest.


        :param configured_domain: The configured_domain of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._configured_domain = configured_domain

    @property
    def encry_type(self):
        """Gets the encry_type of this ListCertInfoRequest.  # noqa: E501


        :return: The encry_type of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._encry_type

    @encry_type.setter
    def encry_type(self, encry_type):
        """Sets the encry_type of this ListCertInfoRequest.


        :param encry_type: The encry_type of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._encry_type = encry_type

    @property
    def fuzzy_match(self):
        """Gets the fuzzy_match of this ListCertInfoRequest.  # noqa: E501


        :return: The fuzzy_match of this ListCertInfoRequest.  # noqa: E501
        :rtype: bool
        """
        return self._fuzzy_match

    @fuzzy_match.setter
    def fuzzy_match(self, fuzzy_match):
        """Sets the fuzzy_match of this ListCertInfoRequest.


        :param fuzzy_match: The fuzzy_match of this ListCertInfoRequest.  # noqa: E501
        :type: bool
        """

        self._fuzzy_match = fuzzy_match

    @property
    def name(self):
        """Gets the name of this ListCertInfoRequest.  # noqa: E501


        :return: The name of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListCertInfoRequest.


        :param name: The name of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_num(self):
        """Gets the page_num of this ListCertInfoRequest.  # noqa: E501


        :return: The page_num of this ListCertInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListCertInfoRequest.


        :param page_num: The page_num of this ListCertInfoRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListCertInfoRequest.  # noqa: E501


        :return: The page_size of this ListCertInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCertInfoRequest.


        :param page_size: The page_size of this ListCertInfoRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def sort_rule(self):
        """Gets the sort_rule of this ListCertInfoRequest.  # noqa: E501


        :return: The sort_rule of this ListCertInfoRequest.  # noqa: E501
        :rtype: SortRuleForListCertInfoInput
        """
        return self._sort_rule

    @sort_rule.setter
    def sort_rule(self, sort_rule):
        """Sets the sort_rule of this ListCertInfoRequest.


        :param sort_rule: The sort_rule of this ListCertInfoRequest.  # noqa: E501
        :type: SortRuleForListCertInfoInput
        """

        self._sort_rule = sort_rule

    @property
    def source(self):
        """Gets the source of this ListCertInfoRequest.  # noqa: E501


        :return: The source of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ListCertInfoRequest.


        :param source: The source of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source is None:
            raise ValueError("Invalid value for `source`, must not be `None`")  # noqa: E501

        self._source = source

    @property
    def status(self):
        """Gets the status of this ListCertInfoRequest.  # noqa: E501


        :return: The status of this ListCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListCertInfoRequest.


        :param status: The status of this ListCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCertInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCertInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCertInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
