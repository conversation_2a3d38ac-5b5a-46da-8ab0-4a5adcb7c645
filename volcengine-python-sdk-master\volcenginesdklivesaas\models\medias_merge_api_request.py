# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MediasMergeAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'force_replay_status': 'bool',
        'media_name': 'str',
        'online_status': 'int',
        'vids': 'list[str]'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'force_replay_status': 'ForceReplayStatus',
        'media_name': 'MediaName',
        'online_status': 'OnlineStatus',
        'vids': 'Vids'
    }

    def __init__(self, activity_id=None, force_replay_status=None, media_name=None, online_status=None, vids=None, _configuration=None):  # noqa: E501
        """MediasMergeAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._force_replay_status = None
        self._media_name = None
        self._online_status = None
        self._vids = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if force_replay_status is not None:
            self.force_replay_status = force_replay_status
        if media_name is not None:
            self.media_name = media_name
        if online_status is not None:
            self.online_status = online_status
        if vids is not None:
            self.vids = vids

    @property
    def activity_id(self):
        """Gets the activity_id of this MediasMergeAPIRequest.  # noqa: E501


        :return: The activity_id of this MediasMergeAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this MediasMergeAPIRequest.


        :param activity_id: The activity_id of this MediasMergeAPIRequest.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def force_replay_status(self):
        """Gets the force_replay_status of this MediasMergeAPIRequest.  # noqa: E501


        :return: The force_replay_status of this MediasMergeAPIRequest.  # noqa: E501
        :rtype: bool
        """
        return self._force_replay_status

    @force_replay_status.setter
    def force_replay_status(self, force_replay_status):
        """Sets the force_replay_status of this MediasMergeAPIRequest.


        :param force_replay_status: The force_replay_status of this MediasMergeAPIRequest.  # noqa: E501
        :type: bool
        """

        self._force_replay_status = force_replay_status

    @property
    def media_name(self):
        """Gets the media_name of this MediasMergeAPIRequest.  # noqa: E501


        :return: The media_name of this MediasMergeAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._media_name

    @media_name.setter
    def media_name(self, media_name):
        """Sets the media_name of this MediasMergeAPIRequest.


        :param media_name: The media_name of this MediasMergeAPIRequest.  # noqa: E501
        :type: str
        """

        self._media_name = media_name

    @property
    def online_status(self):
        """Gets the online_status of this MediasMergeAPIRequest.  # noqa: E501


        :return: The online_status of this MediasMergeAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._online_status

    @online_status.setter
    def online_status(self, online_status):
        """Sets the online_status of this MediasMergeAPIRequest.


        :param online_status: The online_status of this MediasMergeAPIRequest.  # noqa: E501
        :type: int
        """

        self._online_status = online_status

    @property
    def vids(self):
        """Gets the vids of this MediasMergeAPIRequest.  # noqa: E501


        :return: The vids of this MediasMergeAPIRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vids

    @vids.setter
    def vids(self, vids):
        """Sets the vids of this MediasMergeAPIRequest.


        :param vids: The vids of this MediasMergeAPIRequest.  # noqa: E501
        :type: list[str]
        """

        self._vids = vids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MediasMergeAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MediasMergeAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MediasMergeAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
