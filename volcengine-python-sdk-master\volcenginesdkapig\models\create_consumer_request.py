# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateConsumerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comments': 'str',
        'gateway_id': 'str',
        'name': 'str'
    }

    attribute_map = {
        'comments': 'Comments',
        'gateway_id': 'GatewayId',
        'name': 'Name'
    }

    def __init__(self, comments=None, gateway_id=None, name=None, _configuration=None):  # noqa: E501
        """CreateConsumerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comments = None
        self._gateway_id = None
        self._name = None
        self.discriminator = None

        if comments is not None:
            self.comments = comments
        self.gateway_id = gateway_id
        self.name = name

    @property
    def comments(self):
        """Gets the comments of this CreateConsumerRequest.  # noqa: E501


        :return: The comments of this CreateConsumerRequest.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this CreateConsumerRequest.


        :param comments: The comments of this CreateConsumerRequest.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def gateway_id(self):
        """Gets the gateway_id of this CreateConsumerRequest.  # noqa: E501


        :return: The gateway_id of this CreateConsumerRequest.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this CreateConsumerRequest.


        :param gateway_id: The gateway_id of this CreateConsumerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and gateway_id is None:
            raise ValueError("Invalid value for `gateway_id`, must not be `None`")  # noqa: E501

        self._gateway_id = gateway_id

    @property
    def name(self):
        """Gets the name of this CreateConsumerRequest.  # noqa: E501


        :return: The name of this CreateConsumerRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateConsumerRequest.


        :param name: The name of this CreateConsumerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateConsumerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateConsumerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateConsumerRequest):
            return True

        return self.to_dict() != other.to_dict()
