# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCustomizedCfgAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'customized_cfg_content': 'str',
        'customized_cfg_id': 'str',
        'customized_cfg_name': 'str',
        'description': 'str',
        'listeners': 'list[ListenerForDescribeCustomizedCfgAttributesOutput]',
        'project_name': 'str',
        'request_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeCustomizedCfgAttributesOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'customized_cfg_content': 'CustomizedCfgContent',
        'customized_cfg_id': 'CustomizedCfgId',
        'customized_cfg_name': 'CustomizedCfgName',
        'description': 'Description',
        'listeners': 'Listeners',
        'project_name': 'ProjectName',
        'request_id': 'RequestId',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, customized_cfg_content=None, customized_cfg_id=None, customized_cfg_name=None, description=None, listeners=None, project_name=None, request_id=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeCustomizedCfgAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._customized_cfg_content = None
        self._customized_cfg_id = None
        self._customized_cfg_name = None
        self._description = None
        self._listeners = None
        self._project_name = None
        self._request_id = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if customized_cfg_content is not None:
            self.customized_cfg_content = customized_cfg_content
        if customized_cfg_id is not None:
            self.customized_cfg_id = customized_cfg_id
        if customized_cfg_name is not None:
            self.customized_cfg_name = customized_cfg_name
        if description is not None:
            self.description = description
        if listeners is not None:
            self.listeners = listeners
        if project_name is not None:
            self.project_name = project_name
        if request_id is not None:
            self.request_id = request_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeCustomizedCfgAttributesResponse.


        :param create_time: The create_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def customized_cfg_content(self):
        """Gets the customized_cfg_content of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The customized_cfg_content of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._customized_cfg_content

    @customized_cfg_content.setter
    def customized_cfg_content(self, customized_cfg_content):
        """Sets the customized_cfg_content of this DescribeCustomizedCfgAttributesResponse.


        :param customized_cfg_content: The customized_cfg_content of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._customized_cfg_content = customized_cfg_content

    @property
    def customized_cfg_id(self):
        """Gets the customized_cfg_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The customized_cfg_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._customized_cfg_id

    @customized_cfg_id.setter
    def customized_cfg_id(self, customized_cfg_id):
        """Sets the customized_cfg_id of this DescribeCustomizedCfgAttributesResponse.


        :param customized_cfg_id: The customized_cfg_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._customized_cfg_id = customized_cfg_id

    @property
    def customized_cfg_name(self):
        """Gets the customized_cfg_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The customized_cfg_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._customized_cfg_name

    @customized_cfg_name.setter
    def customized_cfg_name(self, customized_cfg_name):
        """Sets the customized_cfg_name of this DescribeCustomizedCfgAttributesResponse.


        :param customized_cfg_name: The customized_cfg_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._customized_cfg_name = customized_cfg_name

    @property
    def description(self):
        """Gets the description of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The description of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeCustomizedCfgAttributesResponse.


        :param description: The description of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def listeners(self):
        """Gets the listeners of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The listeners of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: list[ListenerForDescribeCustomizedCfgAttributesOutput]
        """
        return self._listeners

    @listeners.setter
    def listeners(self, listeners):
        """Sets the listeners of this DescribeCustomizedCfgAttributesResponse.


        :param listeners: The listeners of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: list[ListenerForDescribeCustomizedCfgAttributesOutput]
        """

        self._listeners = listeners

    @property
    def project_name(self):
        """Gets the project_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeCustomizedCfgAttributesResponse.


        :param project_name: The project_name of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def request_id(self):
        """Gets the request_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeCustomizedCfgAttributesResponse.


        :param request_id: The request_id of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def status(self):
        """Gets the status of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The status of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeCustomizedCfgAttributesResponse.


        :param status: The status of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeCustomizedCfgAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeCustomizedCfgAttributesResponse.


        :param tags: The tags of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeCustomizedCfgAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeCustomizedCfgAttributesResponse.


        :param update_time: The update_time of this DescribeCustomizedCfgAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCustomizedCfgAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCustomizedCfgAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCustomizedCfgAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
