# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProductConfigForUpdateActivityProductInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_redirect_image': 'str',
        'display_enable': 'int',
        'enable_floating': 'bool',
        'enable_mini_app': 'bool',
        'enable_ua': 'bool',
        'id': 'int',
        'menu_name': 'str',
        'mini_app_id': 'str',
        'product': 'ConvertProductForUpdateActivityProductInput',
        'products_position': 'int',
        'ua_info': 'ConvertUAInfoForUpdateActivityProductInput'
    }

    attribute_map = {
        'default_redirect_image': 'DefaultRedirectImage',
        'display_enable': 'DisplayEnable',
        'enable_floating': 'EnableFloating',
        'enable_mini_app': 'EnableMiniApp',
        'enable_ua': 'EnableUA',
        'id': 'Id',
        'menu_name': 'MenuName',
        'mini_app_id': 'MiniAppId',
        'product': 'Product',
        'products_position': 'ProductsPosition',
        'ua_info': 'UAInfo'
    }

    def __init__(self, default_redirect_image=None, display_enable=None, enable_floating=None, enable_mini_app=None, enable_ua=None, id=None, menu_name=None, mini_app_id=None, product=None, products_position=None, ua_info=None, _configuration=None):  # noqa: E501
        """ProductConfigForUpdateActivityProductInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_redirect_image = None
        self._display_enable = None
        self._enable_floating = None
        self._enable_mini_app = None
        self._enable_ua = None
        self._id = None
        self._menu_name = None
        self._mini_app_id = None
        self._product = None
        self._products_position = None
        self._ua_info = None
        self.discriminator = None

        if default_redirect_image is not None:
            self.default_redirect_image = default_redirect_image
        if display_enable is not None:
            self.display_enable = display_enable
        if enable_floating is not None:
            self.enable_floating = enable_floating
        if enable_mini_app is not None:
            self.enable_mini_app = enable_mini_app
        if enable_ua is not None:
            self.enable_ua = enable_ua
        if id is not None:
            self.id = id
        if menu_name is not None:
            self.menu_name = menu_name
        if mini_app_id is not None:
            self.mini_app_id = mini_app_id
        if product is not None:
            self.product = product
        if products_position is not None:
            self.products_position = products_position
        if ua_info is not None:
            self.ua_info = ua_info

    @property
    def default_redirect_image(self):
        """Gets the default_redirect_image of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The default_redirect_image of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: str
        """
        return self._default_redirect_image

    @default_redirect_image.setter
    def default_redirect_image(self, default_redirect_image):
        """Sets the default_redirect_image of this ProductConfigForUpdateActivityProductInput.


        :param default_redirect_image: The default_redirect_image of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: str
        """

        self._default_redirect_image = default_redirect_image

    @property
    def display_enable(self):
        """Gets the display_enable of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The display_enable of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: int
        """
        return self._display_enable

    @display_enable.setter
    def display_enable(self, display_enable):
        """Sets the display_enable of this ProductConfigForUpdateActivityProductInput.


        :param display_enable: The display_enable of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: int
        """

        self._display_enable = display_enable

    @property
    def enable_floating(self):
        """Gets the enable_floating of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The enable_floating of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_floating

    @enable_floating.setter
    def enable_floating(self, enable_floating):
        """Sets the enable_floating of this ProductConfigForUpdateActivityProductInput.


        :param enable_floating: The enable_floating of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: bool
        """

        self._enable_floating = enable_floating

    @property
    def enable_mini_app(self):
        """Gets the enable_mini_app of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The enable_mini_app of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_mini_app

    @enable_mini_app.setter
    def enable_mini_app(self, enable_mini_app):
        """Sets the enable_mini_app of this ProductConfigForUpdateActivityProductInput.


        :param enable_mini_app: The enable_mini_app of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: bool
        """

        self._enable_mini_app = enable_mini_app

    @property
    def enable_ua(self):
        """Gets the enable_ua of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The enable_ua of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_ua

    @enable_ua.setter
    def enable_ua(self, enable_ua):
        """Sets the enable_ua of this ProductConfigForUpdateActivityProductInput.


        :param enable_ua: The enable_ua of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: bool
        """

        self._enable_ua = enable_ua

    @property
    def id(self):
        """Gets the id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ProductConfigForUpdateActivityProductInput.


        :param id: The id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def menu_name(self):
        """Gets the menu_name of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The menu_name of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: str
        """
        return self._menu_name

    @menu_name.setter
    def menu_name(self, menu_name):
        """Sets the menu_name of this ProductConfigForUpdateActivityProductInput.


        :param menu_name: The menu_name of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: str
        """

        self._menu_name = menu_name

    @property
    def mini_app_id(self):
        """Gets the mini_app_id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The mini_app_id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_id

    @mini_app_id.setter
    def mini_app_id(self, mini_app_id):
        """Sets the mini_app_id of this ProductConfigForUpdateActivityProductInput.


        :param mini_app_id: The mini_app_id of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: str
        """

        self._mini_app_id = mini_app_id

    @property
    def product(self):
        """Gets the product of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The product of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: ConvertProductForUpdateActivityProductInput
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ProductConfigForUpdateActivityProductInput.


        :param product: The product of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: ConvertProductForUpdateActivityProductInput
        """

        self._product = product

    @property
    def products_position(self):
        """Gets the products_position of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The products_position of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: int
        """
        return self._products_position

    @products_position.setter
    def products_position(self, products_position):
        """Sets the products_position of this ProductConfigForUpdateActivityProductInput.


        :param products_position: The products_position of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: int
        """

        self._products_position = products_position

    @property
    def ua_info(self):
        """Gets the ua_info of this ProductConfigForUpdateActivityProductInput.  # noqa: E501


        :return: The ua_info of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :rtype: ConvertUAInfoForUpdateActivityProductInput
        """
        return self._ua_info

    @ua_info.setter
    def ua_info(self, ua_info):
        """Sets the ua_info of this ProductConfigForUpdateActivityProductInput.


        :param ua_info: The ua_info of this ProductConfigForUpdateActivityProductInput.  # noqa: E501
        :type: ConvertUAInfoForUpdateActivityProductInput
        """

        self._ua_info = ua_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProductConfigForUpdateActivityProductInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProductConfigForUpdateActivityProductInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProductConfigForUpdateActivityProductInput):
            return True

        return self.to_dict() != other.to_dict()
