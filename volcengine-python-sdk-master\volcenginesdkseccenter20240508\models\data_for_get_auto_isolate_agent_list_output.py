# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetAutoIsolateAgentListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'ecs_instance': 'EcsInstanceForGetAutoIsolateAgentListOutput',
        'extranet_ipv4': 'list[str]',
        'extranet_ipv6': 'list[str]',
        'hostname': 'str',
        'important_protect': 'bool',
        'intranet_ipv4': 'list[str]',
        'intranet_ipv6': 'list[str]',
        'tags': 'list[str]',
        'update_time': 'int'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'ecs_instance': 'EcsInstance',
        'extranet_ipv4': 'ExtranetIPv4',
        'extranet_ipv6': 'ExtranetIPv6',
        'hostname': 'Hostname',
        'important_protect': 'ImportantProtect',
        'intranet_ipv4': 'IntranetIPv4',
        'intranet_ipv6': 'IntranetIPv6',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, agent_id=None, ecs_instance=None, extranet_ipv4=None, extranet_ipv6=None, hostname=None, important_protect=None, intranet_ipv4=None, intranet_ipv6=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DataForGetAutoIsolateAgentListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._ecs_instance = None
        self._extranet_ipv4 = None
        self._extranet_ipv6 = None
        self._hostname = None
        self._important_protect = None
        self._intranet_ipv4 = None
        self._intranet_ipv6 = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if ecs_instance is not None:
            self.ecs_instance = ecs_instance
        if extranet_ipv4 is not None:
            self.extranet_ipv4 = extranet_ipv4
        if extranet_ipv6 is not None:
            self.extranet_ipv6 = extranet_ipv6
        if hostname is not None:
            self.hostname = hostname
        if important_protect is not None:
            self.important_protect = important_protect
        if intranet_ipv4 is not None:
            self.intranet_ipv4 = intranet_ipv4
        if intranet_ipv6 is not None:
            self.intranet_ipv6 = intranet_ipv6
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The agent_id of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetAutoIsolateAgentListOutput.


        :param agent_id: The agent_id of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def ecs_instance(self):
        """Gets the ecs_instance of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The ecs_instance of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: EcsInstanceForGetAutoIsolateAgentListOutput
        """
        return self._ecs_instance

    @ecs_instance.setter
    def ecs_instance(self, ecs_instance):
        """Sets the ecs_instance of this DataForGetAutoIsolateAgentListOutput.


        :param ecs_instance: The ecs_instance of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: EcsInstanceForGetAutoIsolateAgentListOutput
        """

        self._ecs_instance = ecs_instance

    @property
    def extranet_ipv4(self):
        """Gets the extranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The extranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._extranet_ipv4

    @extranet_ipv4.setter
    def extranet_ipv4(self, extranet_ipv4):
        """Sets the extranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.


        :param extranet_ipv4: The extranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._extranet_ipv4 = extranet_ipv4

    @property
    def extranet_ipv6(self):
        """Gets the extranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The extranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._extranet_ipv6

    @extranet_ipv6.setter
    def extranet_ipv6(self, extranet_ipv6):
        """Sets the extranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.


        :param extranet_ipv6: The extranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._extranet_ipv6 = extranet_ipv6

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The hostname of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetAutoIsolateAgentListOutput.


        :param hostname: The hostname of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def important_protect(self):
        """Gets the important_protect of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The important_protect of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: bool
        """
        return self._important_protect

    @important_protect.setter
    def important_protect(self, important_protect):
        """Sets the important_protect of this DataForGetAutoIsolateAgentListOutput.


        :param important_protect: The important_protect of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: bool
        """

        self._important_protect = important_protect

    @property
    def intranet_ipv4(self):
        """Gets the intranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The intranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._intranet_ipv4

    @intranet_ipv4.setter
    def intranet_ipv4(self, intranet_ipv4):
        """Sets the intranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.


        :param intranet_ipv4: The intranet_ipv4 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._intranet_ipv4 = intranet_ipv4

    @property
    def intranet_ipv6(self):
        """Gets the intranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The intranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._intranet_ipv6

    @intranet_ipv6.setter
    def intranet_ipv6(self, intranet_ipv6):
        """Sets the intranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.


        :param intranet_ipv6: The intranet_ipv6 of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._intranet_ipv6 = intranet_ipv6

    @property
    def tags(self):
        """Gets the tags of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The tags of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DataForGetAutoIsolateAgentListOutput.


        :param tags: The tags of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The update_time of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetAutoIsolateAgentListOutput.


        :param update_time: The update_time of this DataForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetAutoIsolateAgentListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetAutoIsolateAgentListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetAutoIsolateAgentListOutput):
            return True

        return self.to_dict() != other.to_dict()
