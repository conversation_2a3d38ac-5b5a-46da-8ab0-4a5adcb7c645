# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExtendInfoForGetRaspAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'listen_ports': 'list[ListenPortForGetRaspAlarmSummaryInfoOutput]',
        'vul_info': 'list[VulInfoForGetRaspAlarmSummaryInfoOutput]'
    }

    attribute_map = {
        'listen_ports': 'ListenPorts',
        'vul_info': 'VulInfo'
    }

    def __init__(self, listen_ports=None, vul_info=None, _configuration=None):  # noqa: E501
        """ExtendInfoForGetRaspAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._listen_ports = None
        self._vul_info = None
        self.discriminator = None

        if listen_ports is not None:
            self.listen_ports = listen_ports
        if vul_info is not None:
            self.vul_info = vul_info

    @property
    def listen_ports(self):
        """Gets the listen_ports of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The listen_ports of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[ListenPortForGetRaspAlarmSummaryInfoOutput]
        """
        return self._listen_ports

    @listen_ports.setter
    def listen_ports(self, listen_ports):
        """Sets the listen_ports of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.


        :param listen_ports: The listen_ports of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[ListenPortForGetRaspAlarmSummaryInfoOutput]
        """

        self._listen_ports = listen_ports

    @property
    def vul_info(self):
        """Gets the vul_info of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The vul_info of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[VulInfoForGetRaspAlarmSummaryInfoOutput]
        """
        return self._vul_info

    @vul_info.setter
    def vul_info(self, vul_info):
        """Sets the vul_info of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.


        :param vul_info: The vul_info of this ExtendInfoForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[VulInfoForGetRaspAlarmSummaryInfoOutput]
        """

        self._vul_info = vul_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExtendInfoForGetRaspAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExtendInfoForGetRaspAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExtendInfoForGetRaspAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
