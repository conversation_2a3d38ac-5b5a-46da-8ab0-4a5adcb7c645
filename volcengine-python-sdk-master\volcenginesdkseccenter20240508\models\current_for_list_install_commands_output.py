# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CurrentForListInstallCommandsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_protect': 'bool',
        'cloud_provider': 'str',
        'command': 'str',
        'command_id': 'str',
        'expire_time': 'int',
        'group_id': 'str',
        'group_name': 'str',
        'host_engine': 'int',
        'installation_method': 'str',
        'os': 'str',
        'proxy_name': 'str'
    }

    attribute_map = {
        'auto_protect': 'AutoProtect',
        'cloud_provider': 'CloudProvider',
        'command': 'Command',
        'command_id': 'CommandId',
        'expire_time': 'ExpireTime',
        'group_id': 'GroupId',
        'group_name': 'GroupName',
        'host_engine': 'HostEngine',
        'installation_method': 'InstallationMethod',
        'os': 'OS',
        'proxy_name': 'ProxyName'
    }

    def __init__(self, auto_protect=None, cloud_provider=None, command=None, command_id=None, expire_time=None, group_id=None, group_name=None, host_engine=None, installation_method=None, os=None, proxy_name=None, _configuration=None):  # noqa: E501
        """CurrentForListInstallCommandsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_protect = None
        self._cloud_provider = None
        self._command = None
        self._command_id = None
        self._expire_time = None
        self._group_id = None
        self._group_name = None
        self._host_engine = None
        self._installation_method = None
        self._os = None
        self._proxy_name = None
        self.discriminator = None

        if auto_protect is not None:
            self.auto_protect = auto_protect
        if cloud_provider is not None:
            self.cloud_provider = cloud_provider
        if command is not None:
            self.command = command
        if command_id is not None:
            self.command_id = command_id
        if expire_time is not None:
            self.expire_time = expire_time
        if group_id is not None:
            self.group_id = group_id
        if group_name is not None:
            self.group_name = group_name
        if host_engine is not None:
            self.host_engine = host_engine
        if installation_method is not None:
            self.installation_method = installation_method
        if os is not None:
            self.os = os
        if proxy_name is not None:
            self.proxy_name = proxy_name

    @property
    def auto_protect(self):
        """Gets the auto_protect of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The auto_protect of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_protect

    @auto_protect.setter
    def auto_protect(self, auto_protect):
        """Sets the auto_protect of this CurrentForListInstallCommandsOutput.


        :param auto_protect: The auto_protect of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: bool
        """

        self._auto_protect = auto_protect

    @property
    def cloud_provider(self):
        """Gets the cloud_provider of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The cloud_provider of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_provider

    @cloud_provider.setter
    def cloud_provider(self, cloud_provider):
        """Sets the cloud_provider of this CurrentForListInstallCommandsOutput.


        :param cloud_provider: The cloud_provider of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._cloud_provider = cloud_provider

    @property
    def command(self):
        """Gets the command of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The command of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._command

    @command.setter
    def command(self, command):
        """Sets the command of this CurrentForListInstallCommandsOutput.


        :param command: The command of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._command = command

    @property
    def command_id(self):
        """Gets the command_id of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The command_id of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._command_id

    @command_id.setter
    def command_id(self, command_id):
        """Sets the command_id of this CurrentForListInstallCommandsOutput.


        :param command_id: The command_id of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._command_id = command_id

    @property
    def expire_time(self):
        """Gets the expire_time of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The expire_time of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this CurrentForListInstallCommandsOutput.


        :param expire_time: The expire_time of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def group_id(self):
        """Gets the group_id of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The group_id of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this CurrentForListInstallCommandsOutput.


        :param group_id: The group_id of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def group_name(self):
        """Gets the group_name of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The group_name of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this CurrentForListInstallCommandsOutput.


        :param group_name: The group_name of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def host_engine(self):
        """Gets the host_engine of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The host_engine of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_engine

    @host_engine.setter
    def host_engine(self, host_engine):
        """Sets the host_engine of this CurrentForListInstallCommandsOutput.


        :param host_engine: The host_engine of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: int
        """

        self._host_engine = host_engine

    @property
    def installation_method(self):
        """Gets the installation_method of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The installation_method of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._installation_method

    @installation_method.setter
    def installation_method(self, installation_method):
        """Sets the installation_method of this CurrentForListInstallCommandsOutput.


        :param installation_method: The installation_method of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._installation_method = installation_method

    @property
    def os(self):
        """Gets the os of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The os of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this CurrentForListInstallCommandsOutput.


        :param os: The os of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def proxy_name(self):
        """Gets the proxy_name of this CurrentForListInstallCommandsOutput.  # noqa: E501


        :return: The proxy_name of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :rtype: str
        """
        return self._proxy_name

    @proxy_name.setter
    def proxy_name(self, proxy_name):
        """Sets the proxy_name of this CurrentForListInstallCommandsOutput.


        :param proxy_name: The proxy_name of this CurrentForListInstallCommandsOutput.  # noqa: E501
        :type: str
        """

        self._proxy_name = proxy_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CurrentForListInstallCommandsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CurrentForListInstallCommandsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CurrentForListInstallCommandsOutput):
            return True

        return self.to_dict() != other.to_dict()
