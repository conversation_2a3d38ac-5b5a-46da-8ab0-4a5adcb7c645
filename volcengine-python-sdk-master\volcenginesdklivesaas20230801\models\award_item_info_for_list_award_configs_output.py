# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AwardItemInfoForListAwardConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'award_item_amounts': 'int',
        'award_item_count': 'int',
        'award_item_icon': 'str',
        'award_item_id': 'int',
        'award_item_name': 'str',
        'award_item_num': 'str',
        'award_item_type': 'int',
        'award_lottery_ticket_addr': 'str',
        'winner_count': 'int',
        'winner_info_type': 'str'
    }

    attribute_map = {
        'award_item_amounts': 'AwardItemAmounts',
        'award_item_count': 'AwardItemCount',
        'award_item_icon': 'AwardItemIcon',
        'award_item_id': 'AwardItemId',
        'award_item_name': 'AwardItemName',
        'award_item_num': 'AwardItemNum',
        'award_item_type': 'AwardItemType',
        'award_lottery_ticket_addr': 'AwardLotteryTicketAddr',
        'winner_count': 'WinnerCount',
        'winner_info_type': 'WinnerInfoType'
    }

    def __init__(self, award_item_amounts=None, award_item_count=None, award_item_icon=None, award_item_id=None, award_item_name=None, award_item_num=None, award_item_type=None, award_lottery_ticket_addr=None, winner_count=None, winner_info_type=None, _configuration=None):  # noqa: E501
        """AwardItemInfoForListAwardConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._award_item_amounts = None
        self._award_item_count = None
        self._award_item_icon = None
        self._award_item_id = None
        self._award_item_name = None
        self._award_item_num = None
        self._award_item_type = None
        self._award_lottery_ticket_addr = None
        self._winner_count = None
        self._winner_info_type = None
        self.discriminator = None

        if award_item_amounts is not None:
            self.award_item_amounts = award_item_amounts
        if award_item_count is not None:
            self.award_item_count = award_item_count
        if award_item_icon is not None:
            self.award_item_icon = award_item_icon
        if award_item_id is not None:
            self.award_item_id = award_item_id
        if award_item_name is not None:
            self.award_item_name = award_item_name
        if award_item_num is not None:
            self.award_item_num = award_item_num
        if award_item_type is not None:
            self.award_item_type = award_item_type
        if award_lottery_ticket_addr is not None:
            self.award_lottery_ticket_addr = award_lottery_ticket_addr
        if winner_count is not None:
            self.winner_count = winner_count
        if winner_info_type is not None:
            self.winner_info_type = winner_info_type

    @property
    def award_item_amounts(self):
        """Gets the award_item_amounts of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_amounts of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_amounts

    @award_item_amounts.setter
    def award_item_amounts(self, award_item_amounts):
        """Sets the award_item_amounts of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_amounts: The award_item_amounts of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_item_amounts = award_item_amounts

    @property
    def award_item_count(self):
        """Gets the award_item_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_count

    @award_item_count.setter
    def award_item_count(self, award_item_count):
        """Sets the award_item_count of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_count: The award_item_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_item_count = award_item_count

    @property
    def award_item_icon(self):
        """Gets the award_item_icon of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_icon of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_icon

    @award_item_icon.setter
    def award_item_icon(self, award_item_icon):
        """Sets the award_item_icon of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_icon: The award_item_icon of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._award_item_icon = award_item_icon

    @property
    def award_item_id(self):
        """Gets the award_item_id of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_id of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_id

    @award_item_id.setter
    def award_item_id(self, award_item_id):
        """Sets the award_item_id of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_id: The award_item_id of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_item_id = award_item_id

    @property
    def award_item_name(self):
        """Gets the award_item_name of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_name of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_name

    @award_item_name.setter
    def award_item_name(self, award_item_name):
        """Sets the award_item_name of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_name: The award_item_name of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._award_item_name = award_item_name

    @property
    def award_item_num(self):
        """Gets the award_item_num of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_num of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_num

    @award_item_num.setter
    def award_item_num(self, award_item_num):
        """Sets the award_item_num of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_num: The award_item_num of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._award_item_num = award_item_num

    @property
    def award_item_type(self):
        """Gets the award_item_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_type

    @award_item_type.setter
    def award_item_type(self, award_item_type):
        """Sets the award_item_type of this AwardItemInfoForListAwardConfigsOutput.


        :param award_item_type: The award_item_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_item_type = award_item_type

    @property
    def award_lottery_ticket_addr(self):
        """Gets the award_lottery_ticket_addr of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The award_lottery_ticket_addr of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_lottery_ticket_addr

    @award_lottery_ticket_addr.setter
    def award_lottery_ticket_addr(self, award_lottery_ticket_addr):
        """Sets the award_lottery_ticket_addr of this AwardItemInfoForListAwardConfigsOutput.


        :param award_lottery_ticket_addr: The award_lottery_ticket_addr of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._award_lottery_ticket_addr = award_lottery_ticket_addr

    @property
    def winner_count(self):
        """Gets the winner_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The winner_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._winner_count

    @winner_count.setter
    def winner_count(self, winner_count):
        """Sets the winner_count of this AwardItemInfoForListAwardConfigsOutput.


        :param winner_count: The winner_count of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._winner_count = winner_count

    @property
    def winner_info_type(self):
        """Gets the winner_info_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501


        :return: The winner_info_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._winner_info_type

    @winner_info_type.setter
    def winner_info_type(self, winner_info_type):
        """Sets the winner_info_type of this AwardItemInfoForListAwardConfigsOutput.


        :param winner_info_type: The winner_info_type of this AwardItemInfoForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._winner_info_type = winner_info_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AwardItemInfoForListAwardConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AwardItemInfoForListAwardConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AwardItemInfoForListAwardConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
