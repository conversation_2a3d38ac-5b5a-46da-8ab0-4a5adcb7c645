# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InjectConfigListForListRaspConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'match_content': 'list[str]',
        'match_key': 'str',
        'match_type': 'int'
    }

    attribute_map = {
        'match_content': 'MatchContent',
        'match_key': 'MatchKey',
        'match_type': 'MatchType'
    }

    def __init__(self, match_content=None, match_key=None, match_type=None, _configuration=None):  # noqa: E501
        """InjectConfigListForListRaspConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._match_content = None
        self._match_key = None
        self._match_type = None
        self.discriminator = None

        if match_content is not None:
            self.match_content = match_content
        if match_key is not None:
            self.match_key = match_key
        if match_type is not None:
            self.match_type = match_type

    @property
    def match_content(self):
        """Gets the match_content of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501


        :return: The match_content of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._match_content

    @match_content.setter
    def match_content(self, match_content):
        """Sets the match_content of this InjectConfigListForListRaspConfigsOutput.


        :param match_content: The match_content of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :type: list[str]
        """

        self._match_content = match_content

    @property
    def match_key(self):
        """Gets the match_key of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501


        :return: The match_key of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._match_key

    @match_key.setter
    def match_key(self, match_key):
        """Sets the match_key of this InjectConfigListForListRaspConfigsOutput.


        :param match_key: The match_key of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :type: str
        """

        self._match_key = match_key

    @property
    def match_type(self):
        """Gets the match_type of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501


        :return: The match_type of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._match_type

    @match_type.setter
    def match_type(self, match_type):
        """Sets the match_type of this InjectConfigListForListRaspConfigsOutput.


        :param match_type: The match_type of this InjectConfigListForListRaspConfigsOutput.  # noqa: E501
        :type: int
        """

        self._match_type = match_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InjectConfigListForListRaspConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InjectConfigListForListRaspConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InjectConfigListForListRaspConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
