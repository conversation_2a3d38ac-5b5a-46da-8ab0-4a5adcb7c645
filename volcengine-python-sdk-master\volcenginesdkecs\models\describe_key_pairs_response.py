# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeKeyPairsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'key_pairs': 'list[KeyPairForDescribeKeyPairsOutput]',
        'next_token': 'str'
    }

    attribute_map = {
        'key_pairs': 'KeyPairs',
        'next_token': 'NextToken'
    }

    def __init__(self, key_pairs=None, next_token=None, _configuration=None):  # noqa: E501
        """DescribeKeyPairsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._key_pairs = None
        self._next_token = None
        self.discriminator = None

        if key_pairs is not None:
            self.key_pairs = key_pairs
        if next_token is not None:
            self.next_token = next_token

    @property
    def key_pairs(self):
        """Gets the key_pairs of this DescribeKeyPairsResponse.  # noqa: E501


        :return: The key_pairs of this DescribeKeyPairsResponse.  # noqa: E501
        :rtype: list[KeyPairForDescribeKeyPairsOutput]
        """
        return self._key_pairs

    @key_pairs.setter
    def key_pairs(self, key_pairs):
        """Sets the key_pairs of this DescribeKeyPairsResponse.


        :param key_pairs: The key_pairs of this DescribeKeyPairsResponse.  # noqa: E501
        :type: list[KeyPairForDescribeKeyPairsOutput]
        """

        self._key_pairs = key_pairs

    @property
    def next_token(self):
        """Gets the next_token of this DescribeKeyPairsResponse.  # noqa: E501


        :return: The next_token of this DescribeKeyPairsResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeKeyPairsResponse.


        :param next_token: The next_token of this DescribeKeyPairsResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeKeyPairsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeKeyPairsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeKeyPairsResponse):
            return True

        return self.to_dict() != other.to_dict()
