# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListPublicImageTagsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'name': 'str',
        'size_bytes': 'int',
        'update_time': 'str',
        'url': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'name': 'Name',
        'size_bytes': 'SizeBytes',
        'update_time': 'UpdateTime',
        'url': 'Url'
    }

    def __init__(self, create_time=None, name=None, size_bytes=None, update_time=None, url=None, _configuration=None):  # noqa: E501
        """ItemForListPublicImageTagsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._name = None
        self._size_bytes = None
        self._update_time = None
        self._url = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if name is not None:
            self.name = name
        if size_bytes is not None:
            self.size_bytes = size_bytes
        if update_time is not None:
            self.update_time = update_time
        if url is not None:
            self.url = url

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListPublicImageTagsOutput.  # noqa: E501


        :return: The create_time of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListPublicImageTagsOutput.


        :param create_time: The create_time of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def name(self):
        """Gets the name of this ItemForListPublicImageTagsOutput.  # noqa: E501


        :return: The name of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListPublicImageTagsOutput.


        :param name: The name of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def size_bytes(self):
        """Gets the size_bytes of this ItemForListPublicImageTagsOutput.  # noqa: E501


        :return: The size_bytes of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :rtype: int
        """
        return self._size_bytes

    @size_bytes.setter
    def size_bytes(self, size_bytes):
        """Sets the size_bytes of this ItemForListPublicImageTagsOutput.


        :param size_bytes: The size_bytes of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :type: int
        """

        self._size_bytes = size_bytes

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListPublicImageTagsOutput.  # noqa: E501


        :return: The update_time of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListPublicImageTagsOutput.


        :param update_time: The update_time of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def url(self):
        """Gets the url of this ItemForListPublicImageTagsOutput.  # noqa: E501


        :return: The url of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ItemForListPublicImageTagsOutput.


        :param url: The url of this ItemForListPublicImageTagsOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListPublicImageTagsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListPublicImageTagsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListPublicImageTagsOutput):
            return True

        return self.to_dict() != other.to_dict()
