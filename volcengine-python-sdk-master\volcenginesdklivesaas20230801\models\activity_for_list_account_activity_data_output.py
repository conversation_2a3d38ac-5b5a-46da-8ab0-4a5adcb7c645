# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActivityForListAccountActivityDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'activity_name': 'str',
        'app_template_live_count': 'int',
        'app_template_live_duration': 'int',
        'comment_count': 'int',
        'live_count': 'int',
        'live_duration': 'int',
        'live_promotion_live_count': 'int',
        'live_promotion_live_duration': 'int',
        'live_promotion_platform_count': 'int',
        'live_time': 'int',
        'pcu': 'int',
        'pv': 'int',
        'uv': 'int',
        'watch_duration_per_people': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'activity_name': 'ActivityName',
        'app_template_live_count': 'AppTemplateLiveCount',
        'app_template_live_duration': 'AppTemplateLiveDuration',
        'comment_count': 'CommentCount',
        'live_count': 'LiveCount',
        'live_duration': 'LiveDuration',
        'live_promotion_live_count': 'LivePromotionLiveCount',
        'live_promotion_live_duration': 'LivePromotionLiveDuration',
        'live_promotion_platform_count': 'LivePromotionPlatformCount',
        'live_time': 'LiveTime',
        'pcu': 'PCU',
        'pv': 'PV',
        'uv': 'UV',
        'watch_duration_per_people': 'WatchDurationPerPeople'
    }

    def __init__(self, activity_id=None, activity_name=None, app_template_live_count=None, app_template_live_duration=None, comment_count=None, live_count=None, live_duration=None, live_promotion_live_count=None, live_promotion_live_duration=None, live_promotion_platform_count=None, live_time=None, pcu=None, pv=None, uv=None, watch_duration_per_people=None, _configuration=None):  # noqa: E501
        """ActivityForListAccountActivityDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._activity_name = None
        self._app_template_live_count = None
        self._app_template_live_duration = None
        self._comment_count = None
        self._live_count = None
        self._live_duration = None
        self._live_promotion_live_count = None
        self._live_promotion_live_duration = None
        self._live_promotion_platform_count = None
        self._live_time = None
        self._pcu = None
        self._pv = None
        self._uv = None
        self._watch_duration_per_people = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if activity_name is not None:
            self.activity_name = activity_name
        if app_template_live_count is not None:
            self.app_template_live_count = app_template_live_count
        if app_template_live_duration is not None:
            self.app_template_live_duration = app_template_live_duration
        if comment_count is not None:
            self.comment_count = comment_count
        if live_count is not None:
            self.live_count = live_count
        if live_duration is not None:
            self.live_duration = live_duration
        if live_promotion_live_count is not None:
            self.live_promotion_live_count = live_promotion_live_count
        if live_promotion_live_duration is not None:
            self.live_promotion_live_duration = live_promotion_live_duration
        if live_promotion_platform_count is not None:
            self.live_promotion_platform_count = live_promotion_platform_count
        if live_time is not None:
            self.live_time = live_time
        if pcu is not None:
            self.pcu = pcu
        if pv is not None:
            self.pv = pv
        if uv is not None:
            self.uv = uv
        if watch_duration_per_people is not None:
            self.watch_duration_per_people = watch_duration_per_people

    @property
    def activity_id(self):
        """Gets the activity_id of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The activity_id of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ActivityForListAccountActivityDataOutput.


        :param activity_id: The activity_id of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def activity_name(self):
        """Gets the activity_name of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The activity_name of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._activity_name

    @activity_name.setter
    def activity_name(self, activity_name):
        """Sets the activity_name of this ActivityForListAccountActivityDataOutput.


        :param activity_name: The activity_name of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: str
        """

        self._activity_name = activity_name

    @property
    def app_template_live_count(self):
        """Gets the app_template_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The app_template_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._app_template_live_count

    @app_template_live_count.setter
    def app_template_live_count(self, app_template_live_count):
        """Sets the app_template_live_count of this ActivityForListAccountActivityDataOutput.


        :param app_template_live_count: The app_template_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._app_template_live_count = app_template_live_count

    @property
    def app_template_live_duration(self):
        """Gets the app_template_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The app_template_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._app_template_live_duration

    @app_template_live_duration.setter
    def app_template_live_duration(self, app_template_live_duration):
        """Sets the app_template_live_duration of this ActivityForListAccountActivityDataOutput.


        :param app_template_live_duration: The app_template_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._app_template_live_duration = app_template_live_duration

    @property
    def comment_count(self):
        """Gets the comment_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The comment_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._comment_count

    @comment_count.setter
    def comment_count(self, comment_count):
        """Sets the comment_count of this ActivityForListAccountActivityDataOutput.


        :param comment_count: The comment_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._comment_count = comment_count

    @property
    def live_count(self):
        """Gets the live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_count

    @live_count.setter
    def live_count(self, live_count):
        """Sets the live_count of this ActivityForListAccountActivityDataOutput.


        :param live_count: The live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_count = live_count

    @property
    def live_duration(self):
        """Gets the live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_duration

    @live_duration.setter
    def live_duration(self, live_duration):
        """Sets the live_duration of this ActivityForListAccountActivityDataOutput.


        :param live_duration: The live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_duration = live_duration

    @property
    def live_promotion_live_count(self):
        """Gets the live_promotion_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_promotion_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_live_count

    @live_promotion_live_count.setter
    def live_promotion_live_count(self, live_promotion_live_count):
        """Sets the live_promotion_live_count of this ActivityForListAccountActivityDataOutput.


        :param live_promotion_live_count: The live_promotion_live_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_promotion_live_count = live_promotion_live_count

    @property
    def live_promotion_live_duration(self):
        """Gets the live_promotion_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_promotion_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_live_duration

    @live_promotion_live_duration.setter
    def live_promotion_live_duration(self, live_promotion_live_duration):
        """Sets the live_promotion_live_duration of this ActivityForListAccountActivityDataOutput.


        :param live_promotion_live_duration: The live_promotion_live_duration of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_promotion_live_duration = live_promotion_live_duration

    @property
    def live_promotion_platform_count(self):
        """Gets the live_promotion_platform_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_promotion_platform_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_platform_count

    @live_promotion_platform_count.setter
    def live_promotion_platform_count(self, live_promotion_platform_count):
        """Sets the live_promotion_platform_count of this ActivityForListAccountActivityDataOutput.


        :param live_promotion_platform_count: The live_promotion_platform_count of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_promotion_platform_count = live_promotion_platform_count

    @property
    def live_time(self):
        """Gets the live_time of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The live_time of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this ActivityForListAccountActivityDataOutput.


        :param live_time: The live_time of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def pcu(self):
        """Gets the pcu of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The pcu of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._pcu

    @pcu.setter
    def pcu(self, pcu):
        """Sets the pcu of this ActivityForListAccountActivityDataOutput.


        :param pcu: The pcu of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._pcu = pcu

    @property
    def pv(self):
        """Gets the pv of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The pv of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._pv

    @pv.setter
    def pv(self, pv):
        """Sets the pv of this ActivityForListAccountActivityDataOutput.


        :param pv: The pv of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._pv = pv

    @property
    def uv(self):
        """Gets the uv of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The uv of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._uv

    @uv.setter
    def uv(self, uv):
        """Sets the uv of this ActivityForListAccountActivityDataOutput.


        :param uv: The uv of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._uv = uv

    @property
    def watch_duration_per_people(self):
        """Gets the watch_duration_per_people of this ActivityForListAccountActivityDataOutput.  # noqa: E501


        :return: The watch_duration_per_people of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_duration_per_people

    @watch_duration_per_people.setter
    def watch_duration_per_people(self, watch_duration_per_people):
        """Sets the watch_duration_per_people of this ActivityForListAccountActivityDataOutput.


        :param watch_duration_per_people: The watch_duration_per_people of this ActivityForListAccountActivityDataOutput.  # noqa: E501
        :type: int
        """

        self._watch_duration_per_people = watch_duration_per_people

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActivityForListAccountActivityDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActivityForListAccountActivityDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActivityForListAccountActivityDataOutput):
            return True

        return self.to_dict() != other.to_dict()
