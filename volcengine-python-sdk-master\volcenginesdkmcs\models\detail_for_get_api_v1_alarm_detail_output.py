# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DetailForGetApiV1AlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_info_list': 'list[AdditionalInfoListForGetApiV1AlarmDetailOutput]',
        'affected_asset': 'AffectedAssetForGetApiV1AlarmDetailOutput',
        'alarm_alert_type': 'str',
        'alarm_created_time_milli': 'int',
        'alarm_desc': 'str',
        'alarm_id': 'str',
        'alarm_raw_data': 'str',
        'alarm_risk_level': 'str',
        'alarm_risk_status': 'str',
        'alarm_source_product': 'AlarmSourceProductForGetApiV1AlarmDetailOutput',
        'alarm_title': 'str',
        'alarm_updated_time_milli': 'int',
        'alarm_vendor_alert_meta': 'AlarmVendorAlertMetaForGetApiV1AlarmDetailOutput',
        'cloud_account': 'CloudAccountForGetApiV1AlarmDetailOutput',
        'llm_analysis_result': 'str',
        'variation_info': 'VariationInfoForGetApiV1AlarmDetailOutput'
    }

    attribute_map = {
        'additional_info_list': 'additional_info_list',
        'affected_asset': 'affected_asset',
        'alarm_alert_type': 'alarm_alert_type',
        'alarm_created_time_milli': 'alarm_created_time_milli',
        'alarm_desc': 'alarm_desc',
        'alarm_id': 'alarm_id',
        'alarm_raw_data': 'alarm_raw_data',
        'alarm_risk_level': 'alarm_risk_level',
        'alarm_risk_status': 'alarm_risk_status',
        'alarm_source_product': 'alarm_source_product',
        'alarm_title': 'alarm_title',
        'alarm_updated_time_milli': 'alarm_updated_time_milli',
        'alarm_vendor_alert_meta': 'alarm_vendor_alert_meta',
        'cloud_account': 'cloud_account',
        'llm_analysis_result': 'llm_analysis_result',
        'variation_info': 'variation_info'
    }

    def __init__(self, additional_info_list=None, affected_asset=None, alarm_alert_type=None, alarm_created_time_milli=None, alarm_desc=None, alarm_id=None, alarm_raw_data=None, alarm_risk_level=None, alarm_risk_status=None, alarm_source_product=None, alarm_title=None, alarm_updated_time_milli=None, alarm_vendor_alert_meta=None, cloud_account=None, llm_analysis_result=None, variation_info=None, _configuration=None):  # noqa: E501
        """DetailForGetApiV1AlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_info_list = None
        self._affected_asset = None
        self._alarm_alert_type = None
        self._alarm_created_time_milli = None
        self._alarm_desc = None
        self._alarm_id = None
        self._alarm_raw_data = None
        self._alarm_risk_level = None
        self._alarm_risk_status = None
        self._alarm_source_product = None
        self._alarm_title = None
        self._alarm_updated_time_milli = None
        self._alarm_vendor_alert_meta = None
        self._cloud_account = None
        self._llm_analysis_result = None
        self._variation_info = None
        self.discriminator = None

        if additional_info_list is not None:
            self.additional_info_list = additional_info_list
        if affected_asset is not None:
            self.affected_asset = affected_asset
        if alarm_alert_type is not None:
            self.alarm_alert_type = alarm_alert_type
        if alarm_created_time_milli is not None:
            self.alarm_created_time_milli = alarm_created_time_milli
        if alarm_desc is not None:
            self.alarm_desc = alarm_desc
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alarm_raw_data is not None:
            self.alarm_raw_data = alarm_raw_data
        if alarm_risk_level is not None:
            self.alarm_risk_level = alarm_risk_level
        if alarm_risk_status is not None:
            self.alarm_risk_status = alarm_risk_status
        if alarm_source_product is not None:
            self.alarm_source_product = alarm_source_product
        if alarm_title is not None:
            self.alarm_title = alarm_title
        if alarm_updated_time_milli is not None:
            self.alarm_updated_time_milli = alarm_updated_time_milli
        if alarm_vendor_alert_meta is not None:
            self.alarm_vendor_alert_meta = alarm_vendor_alert_meta
        if cloud_account is not None:
            self.cloud_account = cloud_account
        if llm_analysis_result is not None:
            self.llm_analysis_result = llm_analysis_result
        if variation_info is not None:
            self.variation_info = variation_info

    @property
    def additional_info_list(self):
        """Gets the additional_info_list of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The additional_info_list of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: list[AdditionalInfoListForGetApiV1AlarmDetailOutput]
        """
        return self._additional_info_list

    @additional_info_list.setter
    def additional_info_list(self, additional_info_list):
        """Sets the additional_info_list of this DetailForGetApiV1AlarmDetailOutput.


        :param additional_info_list: The additional_info_list of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: list[AdditionalInfoListForGetApiV1AlarmDetailOutput]
        """

        self._additional_info_list = additional_info_list

    @property
    def affected_asset(self):
        """Gets the affected_asset of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The affected_asset of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: AffectedAssetForGetApiV1AlarmDetailOutput
        """
        return self._affected_asset

    @affected_asset.setter
    def affected_asset(self, affected_asset):
        """Sets the affected_asset of this DetailForGetApiV1AlarmDetailOutput.


        :param affected_asset: The affected_asset of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: AffectedAssetForGetApiV1AlarmDetailOutput
        """

        self._affected_asset = affected_asset

    @property
    def alarm_alert_type(self):
        """Gets the alarm_alert_type of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_alert_type of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_alert_type

    @alarm_alert_type.setter
    def alarm_alert_type(self, alarm_alert_type):
        """Sets the alarm_alert_type of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_alert_type: The alarm_alert_type of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_alert_type = alarm_alert_type

    @property
    def alarm_created_time_milli(self):
        """Gets the alarm_created_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_created_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_created_time_milli

    @alarm_created_time_milli.setter
    def alarm_created_time_milli(self, alarm_created_time_milli):
        """Sets the alarm_created_time_milli of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_created_time_milli: The alarm_created_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: int
        """

        self._alarm_created_time_milli = alarm_created_time_milli

    @property
    def alarm_desc(self):
        """Gets the alarm_desc of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_desc of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_desc

    @alarm_desc.setter
    def alarm_desc(self, alarm_desc):
        """Sets the alarm_desc of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_desc: The alarm_desc of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_desc = alarm_desc

    @property
    def alarm_id(self):
        """Gets the alarm_id of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_id of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_id: The alarm_id of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alarm_raw_data(self):
        """Gets the alarm_raw_data of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_raw_data of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_raw_data

    @alarm_raw_data.setter
    def alarm_raw_data(self, alarm_raw_data):
        """Sets the alarm_raw_data of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_raw_data: The alarm_raw_data of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_raw_data = alarm_raw_data

    @property
    def alarm_risk_level(self):
        """Gets the alarm_risk_level of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_risk_level of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_risk_level

    @alarm_risk_level.setter
    def alarm_risk_level(self, alarm_risk_level):
        """Sets the alarm_risk_level of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_risk_level: The alarm_risk_level of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_risk_level = alarm_risk_level

    @property
    def alarm_risk_status(self):
        """Gets the alarm_risk_status of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_risk_status of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_risk_status

    @alarm_risk_status.setter
    def alarm_risk_status(self, alarm_risk_status):
        """Sets the alarm_risk_status of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_risk_status: The alarm_risk_status of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_risk_status = alarm_risk_status

    @property
    def alarm_source_product(self):
        """Gets the alarm_source_product of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_source_product of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: AlarmSourceProductForGetApiV1AlarmDetailOutput
        """
        return self._alarm_source_product

    @alarm_source_product.setter
    def alarm_source_product(self, alarm_source_product):
        """Sets the alarm_source_product of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_source_product: The alarm_source_product of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: AlarmSourceProductForGetApiV1AlarmDetailOutput
        """

        self._alarm_source_product = alarm_source_product

    @property
    def alarm_title(self):
        """Gets the alarm_title of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_title of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_title

    @alarm_title.setter
    def alarm_title(self, alarm_title):
        """Sets the alarm_title of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_title: The alarm_title of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_title = alarm_title

    @property
    def alarm_updated_time_milli(self):
        """Gets the alarm_updated_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_updated_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_updated_time_milli

    @alarm_updated_time_milli.setter
    def alarm_updated_time_milli(self, alarm_updated_time_milli):
        """Sets the alarm_updated_time_milli of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_updated_time_milli: The alarm_updated_time_milli of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: int
        """

        self._alarm_updated_time_milli = alarm_updated_time_milli

    @property
    def alarm_vendor_alert_meta(self):
        """Gets the alarm_vendor_alert_meta of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_vendor_alert_meta of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: AlarmVendorAlertMetaForGetApiV1AlarmDetailOutput
        """
        return self._alarm_vendor_alert_meta

    @alarm_vendor_alert_meta.setter
    def alarm_vendor_alert_meta(self, alarm_vendor_alert_meta):
        """Sets the alarm_vendor_alert_meta of this DetailForGetApiV1AlarmDetailOutput.


        :param alarm_vendor_alert_meta: The alarm_vendor_alert_meta of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: AlarmVendorAlertMetaForGetApiV1AlarmDetailOutput
        """

        self._alarm_vendor_alert_meta = alarm_vendor_alert_meta

    @property
    def cloud_account(self):
        """Gets the cloud_account of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The cloud_account of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: CloudAccountForGetApiV1AlarmDetailOutput
        """
        return self._cloud_account

    @cloud_account.setter
    def cloud_account(self, cloud_account):
        """Sets the cloud_account of this DetailForGetApiV1AlarmDetailOutput.


        :param cloud_account: The cloud_account of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: CloudAccountForGetApiV1AlarmDetailOutput
        """

        self._cloud_account = cloud_account

    @property
    def llm_analysis_result(self):
        """Gets the llm_analysis_result of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The llm_analysis_result of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._llm_analysis_result

    @llm_analysis_result.setter
    def llm_analysis_result(self, llm_analysis_result):
        """Sets the llm_analysis_result of this DetailForGetApiV1AlarmDetailOutput.


        :param llm_analysis_result: The llm_analysis_result of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._llm_analysis_result = llm_analysis_result

    @property
    def variation_info(self):
        """Gets the variation_info of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The variation_info of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: VariationInfoForGetApiV1AlarmDetailOutput
        """
        return self._variation_info

    @variation_info.setter
    def variation_info(self, variation_info):
        """Sets the variation_info of this DetailForGetApiV1AlarmDetailOutput.


        :param variation_info: The variation_info of this DetailForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: VariationInfoForGetApiV1AlarmDetailOutput
        """

        self._variation_info = variation_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DetailForGetApiV1AlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DetailForGetApiV1AlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DetailForGetApiV1AlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
