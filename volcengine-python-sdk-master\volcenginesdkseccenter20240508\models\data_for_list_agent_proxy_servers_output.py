# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAgentProxyServersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_number': 'int',
        'hostname': 'str',
        'private_ip': 'list[str]',
        'public_ip': 'list[str]',
        'server_name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'client_number': 'ClientNumber',
        'hostname': 'Hostname',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'server_name': 'ServerName',
        'status': 'Status'
    }

    def __init__(self, client_number=None, hostname=None, private_ip=None, public_ip=None, server_name=None, status=None, _configuration=None):  # noqa: E501
        """DataForListAgentProxyServersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_number = None
        self._hostname = None
        self._private_ip = None
        self._public_ip = None
        self._server_name = None
        self._status = None
        self.discriminator = None

        if client_number is not None:
            self.client_number = client_number
        if hostname is not None:
            self.hostname = hostname
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if server_name is not None:
            self.server_name = server_name
        if status is not None:
            self.status = status

    @property
    def client_number(self):
        """Gets the client_number of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The client_number of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: int
        """
        return self._client_number

    @client_number.setter
    def client_number(self, client_number):
        """Sets the client_number of this DataForListAgentProxyServersOutput.


        :param client_number: The client_number of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: int
        """

        self._client_number = client_number

    @property
    def hostname(self):
        """Gets the hostname of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The hostname of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForListAgentProxyServersOutput.


        :param hostname: The hostname of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The private_ip of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForListAgentProxyServersOutput.


        :param private_ip: The private_ip of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: list[str]
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The public_ip of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForListAgentProxyServersOutput.


        :param public_ip: The public_ip of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: list[str]
        """

        self._public_ip = public_ip

    @property
    def server_name(self):
        """Gets the server_name of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The server_name of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_name

    @server_name.setter
    def server_name(self, server_name):
        """Sets the server_name of this DataForListAgentProxyServersOutput.


        :param server_name: The server_name of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: str
        """

        self._server_name = server_name

    @property
    def status(self):
        """Gets the status of this DataForListAgentProxyServersOutput.  # noqa: E501


        :return: The status of this DataForListAgentProxyServersOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListAgentProxyServersOutput.


        :param status: The status of this DataForListAgentProxyServersOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAgentProxyServersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAgentProxyServersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAgentProxyServersOutput):
            return True

        return self.to_dict() != other.to_dict()
