# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PartitionsInfoForDescribeTopicPartitionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_offset': 'int',
        'insync_replicas': 'list[int]',
        'leader': 'int',
        'message_count': 'int',
        'partition_id': 'int',
        'replicas': 'list[int]',
        'start_offset': 'int',
        'under_insync_replicas': 'list[int]'
    }

    attribute_map = {
        'end_offset': 'EndOffset',
        'insync_replicas': 'InsyncReplicas',
        'leader': 'Leader',
        'message_count': 'MessageCount',
        'partition_id': 'PartitionId',
        'replicas': 'Replicas',
        'start_offset': 'StartOffset',
        'under_insync_replicas': 'UnderInsyncReplicas'
    }

    def __init__(self, end_offset=None, insync_replicas=None, leader=None, message_count=None, partition_id=None, replicas=None, start_offset=None, under_insync_replicas=None, _configuration=None):  # noqa: E501
        """PartitionsInfoForDescribeTopicPartitionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_offset = None
        self._insync_replicas = None
        self._leader = None
        self._message_count = None
        self._partition_id = None
        self._replicas = None
        self._start_offset = None
        self._under_insync_replicas = None
        self.discriminator = None

        if end_offset is not None:
            self.end_offset = end_offset
        if insync_replicas is not None:
            self.insync_replicas = insync_replicas
        if leader is not None:
            self.leader = leader
        if message_count is not None:
            self.message_count = message_count
        if partition_id is not None:
            self.partition_id = partition_id
        if replicas is not None:
            self.replicas = replicas
        if start_offset is not None:
            self.start_offset = start_offset
        if under_insync_replicas is not None:
            self.under_insync_replicas = under_insync_replicas

    @property
    def end_offset(self):
        """Gets the end_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The end_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_offset

    @end_offset.setter
    def end_offset(self, end_offset):
        """Sets the end_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param end_offset: The end_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._end_offset = end_offset

    @property
    def insync_replicas(self):
        """Gets the insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._insync_replicas

    @insync_replicas.setter
    def insync_replicas(self, insync_replicas):
        """Sets the insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param insync_replicas: The insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: list[int]
        """

        self._insync_replicas = insync_replicas

    @property
    def leader(self):
        """Gets the leader of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The leader of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._leader

    @leader.setter
    def leader(self, leader):
        """Sets the leader of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param leader: The leader of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._leader = leader

    @property
    def message_count(self):
        """Gets the message_count of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The message_count of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_count

    @message_count.setter
    def message_count(self, message_count):
        """Sets the message_count of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param message_count: The message_count of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._message_count = message_count

    @property
    def partition_id(self):
        """Gets the partition_id of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The partition_id of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._partition_id

    @partition_id.setter
    def partition_id(self, partition_id):
        """Sets the partition_id of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param partition_id: The partition_id of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._partition_id = partition_id

    @property
    def replicas(self):
        """Gets the replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param replicas: The replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: list[int]
        """

        self._replicas = replicas

    @property
    def start_offset(self):
        """Gets the start_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The start_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_offset

    @start_offset.setter
    def start_offset(self, start_offset):
        """Sets the start_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param start_offset: The start_offset of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._start_offset = start_offset

    @property
    def under_insync_replicas(self):
        """Gets the under_insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501


        :return: The under_insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._under_insync_replicas

    @under_insync_replicas.setter
    def under_insync_replicas(self, under_insync_replicas):
        """Sets the under_insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.


        :param under_insync_replicas: The under_insync_replicas of this PartitionsInfoForDescribeTopicPartitionsOutput.  # noqa: E501
        :type: list[int]
        """

        self._under_insync_replicas = under_insync_replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PartitionsInfoForDescribeTopicPartitionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PartitionsInfoForDescribeTopicPartitionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PartitionsInfoForDescribeTopicPartitionsOutput):
            return True

        return self.to_dict() != other.to_dict()
