# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allowed': 'bool',
        'check_item': 'str',
        'reason': 'str'
    }

    attribute_map = {
        'allowed': 'Allowed',
        'check_item': 'CheckItem',
        'reason': 'Reason'
    }

    def __init__(self, allowed=None, check_item=None, reason=None, _configuration=None):  # noqa: E501
        """CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allowed = None
        self._check_item = None
        self._reason = None
        self.discriminator = None

        if allowed is not None:
            self.allowed = allowed
        if check_item is not None:
            self.check_item = check_item
        if reason is not None:
            self.reason = reason

    @property
    def allowed(self):
        """Gets the allowed of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501


        :return: The allowed of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._allowed

    @allowed.setter
    def allowed(self, allowed):
        """Sets the allowed of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.


        :param allowed: The allowed of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :type: bool
        """

        self._allowed = allowed

    @property
    def check_item(self):
        """Gets the check_item of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501


        :return: The check_item of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_item

    @check_item.setter
    def check_item(self, check_item):
        """Sets the check_item of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.


        :param check_item: The check_item of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :type: str
        """

        self._check_item = check_item

    @property
    def reason(self):
        """Gets the reason of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501


        :return: The reason of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.


        :param reason: The reason of this CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
