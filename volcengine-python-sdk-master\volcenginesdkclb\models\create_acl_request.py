# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAclRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'acl_name': 'str',
        'description': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateAclInput]'
    }

    attribute_map = {
        'acl_name': 'AclName',
        'description': 'Description',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, acl_name=None, description=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """CreateAclRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._acl_name = None
        self._description = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        if acl_name is not None:
            self.acl_name = acl_name
        if description is not None:
            self.description = description
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def acl_name(self):
        """Gets the acl_name of this CreateAclRequest.  # noqa: E501


        :return: The acl_name of this CreateAclRequest.  # noqa: E501
        :rtype: str
        """
        return self._acl_name

    @acl_name.setter
    def acl_name(self, acl_name):
        """Sets the acl_name of this CreateAclRequest.


        :param acl_name: The acl_name of this CreateAclRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                acl_name is not None and len(acl_name) > 255):
            raise ValueError("Invalid value for `acl_name`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                acl_name is not None and len(acl_name) < 2):
            raise ValueError("Invalid value for `acl_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._acl_name = acl_name

    @property
    def description(self):
        """Gets the description of this CreateAclRequest.  # noqa: E501


        :return: The description of this CreateAclRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateAclRequest.


        :param description: The description of this CreateAclRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def project_name(self):
        """Gets the project_name of this CreateAclRequest.  # noqa: E501


        :return: The project_name of this CreateAclRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateAclRequest.


        :param project_name: The project_name of this CreateAclRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateAclRequest.  # noqa: E501


        :return: The tags of this CreateAclRequest.  # noqa: E501
        :rtype: list[TagForCreateAclInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateAclRequest.


        :param tags: The tags of this CreateAclRequest.  # noqa: E501
        :type: list[TagForCreateAclInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAclRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAclRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAclRequest):
            return True

        return self.to_dict() != other.to_dict()
