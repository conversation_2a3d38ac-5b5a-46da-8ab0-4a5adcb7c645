# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVirtualInterfaceBFDRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'detect_multiplier': 'int',
        'receive_interval': 'int',
        'transmit_interval': 'int',
        'vif_instance_id': 'str'
    }

    attribute_map = {
        'detect_multiplier': 'DetectMultiplier',
        'receive_interval': 'ReceiveInterval',
        'transmit_interval': 'TransmitInterval',
        'vif_instance_id': 'VIFInstanceId'
    }

    def __init__(self, detect_multiplier=None, receive_interval=None, transmit_interval=None, vif_instance_id=None, _configuration=None):  # noqa: E501
        """CreateVirtualInterfaceBFDRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._detect_multiplier = None
        self._receive_interval = None
        self._transmit_interval = None
        self._vif_instance_id = None
        self.discriminator = None

        self.detect_multiplier = detect_multiplier
        self.receive_interval = receive_interval
        self.transmit_interval = transmit_interval
        self.vif_instance_id = vif_instance_id

    @property
    def detect_multiplier(self):
        """Gets the detect_multiplier of this CreateVirtualInterfaceBFDRequest.  # noqa: E501


        :return: The detect_multiplier of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :rtype: int
        """
        return self._detect_multiplier

    @detect_multiplier.setter
    def detect_multiplier(self, detect_multiplier):
        """Sets the detect_multiplier of this CreateVirtualInterfaceBFDRequest.


        :param detect_multiplier: The detect_multiplier of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and detect_multiplier is None:
            raise ValueError("Invalid value for `detect_multiplier`, must not be `None`")  # noqa: E501

        self._detect_multiplier = detect_multiplier

    @property
    def receive_interval(self):
        """Gets the receive_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501


        :return: The receive_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :rtype: int
        """
        return self._receive_interval

    @receive_interval.setter
    def receive_interval(self, receive_interval):
        """Sets the receive_interval of this CreateVirtualInterfaceBFDRequest.


        :param receive_interval: The receive_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and receive_interval is None:
            raise ValueError("Invalid value for `receive_interval`, must not be `None`")  # noqa: E501

        self._receive_interval = receive_interval

    @property
    def transmit_interval(self):
        """Gets the transmit_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501


        :return: The transmit_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :rtype: int
        """
        return self._transmit_interval

    @transmit_interval.setter
    def transmit_interval(self, transmit_interval):
        """Sets the transmit_interval of this CreateVirtualInterfaceBFDRequest.


        :param transmit_interval: The transmit_interval of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and transmit_interval is None:
            raise ValueError("Invalid value for `transmit_interval`, must not be `None`")  # noqa: E501

        self._transmit_interval = transmit_interval

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this CreateVirtualInterfaceBFDRequest.  # noqa: E501


        :return: The vif_instance_id of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this CreateVirtualInterfaceBFDRequest.


        :param vif_instance_id: The vif_instance_id of this CreateVirtualInterfaceBFDRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vif_instance_id is None:
            raise ValueError("Invalid value for `vif_instance_id`, must not be `None`")  # noqa: E501

        self._vif_instance_id = vif_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVirtualInterfaceBFDRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVirtualInterfaceBFDRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVirtualInterfaceBFDRequest):
            return True

        return self.to_dict() != other.to_dict()
