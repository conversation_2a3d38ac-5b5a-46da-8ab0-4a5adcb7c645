# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourceShareAssociationsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'associations': 'list[AssociationForListResourceShareAssociationsOutput]',
        'next_token': 'str',
        'total_page': 'int'
    }

    attribute_map = {
        'associations': 'Associations',
        'next_token': 'NextToken',
        'total_page': 'TotalPage'
    }

    def __init__(self, associations=None, next_token=None, total_page=None, _configuration=None):  # noqa: E501
        """ListResourceShareAssociationsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._associations = None
        self._next_token = None
        self._total_page = None
        self.discriminator = None

        if associations is not None:
            self.associations = associations
        if next_token is not None:
            self.next_token = next_token
        if total_page is not None:
            self.total_page = total_page

    @property
    def associations(self):
        """Gets the associations of this ListResourceShareAssociationsResponse.  # noqa: E501


        :return: The associations of this ListResourceShareAssociationsResponse.  # noqa: E501
        :rtype: list[AssociationForListResourceShareAssociationsOutput]
        """
        return self._associations

    @associations.setter
    def associations(self, associations):
        """Sets the associations of this ListResourceShareAssociationsResponse.


        :param associations: The associations of this ListResourceShareAssociationsResponse.  # noqa: E501
        :type: list[AssociationForListResourceShareAssociationsOutput]
        """

        self._associations = associations

    @property
    def next_token(self):
        """Gets the next_token of this ListResourceShareAssociationsResponse.  # noqa: E501


        :return: The next_token of this ListResourceShareAssociationsResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListResourceShareAssociationsResponse.


        :param next_token: The next_token of this ListResourceShareAssociationsResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def total_page(self):
        """Gets the total_page of this ListResourceShareAssociationsResponse.  # noqa: E501


        :return: The total_page of this ListResourceShareAssociationsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_page

    @total_page.setter
    def total_page(self, total_page):
        """Sets the total_page of this ListResourceShareAssociationsResponse.


        :param total_page: The total_page of this ListResourceShareAssociationsResponse.  # noqa: E501
        :type: int
        """

        self._total_page = total_page

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourceShareAssociationsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourceShareAssociationsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourceShareAssociationsResponse):
            return True

        return self.to_dict() != other.to_dict()
