# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetBaselineDetectProgressDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dev_detail': 'DevDetailForGetBaselineDetectProgressDetailOutput',
        'host_detail': 'HostDetailForGetBaselineDetectProgressDetailOutput',
        'host_num': 'HostNumForGetBaselineDetectProgressDetailOutput',
        'progress_detail': 'ProgressDetailForGetBaselineDetectProgressDetailOutput',
        'risk_num': 'RiskNumForGetBaselineDetectProgressDetailOutput',
        'status': 'str'
    }

    attribute_map = {
        'dev_detail': 'DevDetail',
        'host_detail': 'HostDetail',
        'host_num': 'HostNum',
        'progress_detail': 'ProgressDetail',
        'risk_num': 'RiskNum',
        'status': 'Status'
    }

    def __init__(self, dev_detail=None, host_detail=None, host_num=None, progress_detail=None, risk_num=None, status=None, _configuration=None):  # noqa: E501
        """GetBaselineDetectProgressDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dev_detail = None
        self._host_detail = None
        self._host_num = None
        self._progress_detail = None
        self._risk_num = None
        self._status = None
        self.discriminator = None

        if dev_detail is not None:
            self.dev_detail = dev_detail
        if host_detail is not None:
            self.host_detail = host_detail
        if host_num is not None:
            self.host_num = host_num
        if progress_detail is not None:
            self.progress_detail = progress_detail
        if risk_num is not None:
            self.risk_num = risk_num
        if status is not None:
            self.status = status

    @property
    def dev_detail(self):
        """Gets the dev_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The dev_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: DevDetailForGetBaselineDetectProgressDetailOutput
        """
        return self._dev_detail

    @dev_detail.setter
    def dev_detail(self, dev_detail):
        """Sets the dev_detail of this GetBaselineDetectProgressDetailResponse.


        :param dev_detail: The dev_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: DevDetailForGetBaselineDetectProgressDetailOutput
        """

        self._dev_detail = dev_detail

    @property
    def host_detail(self):
        """Gets the host_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The host_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: HostDetailForGetBaselineDetectProgressDetailOutput
        """
        return self._host_detail

    @host_detail.setter
    def host_detail(self, host_detail):
        """Sets the host_detail of this GetBaselineDetectProgressDetailResponse.


        :param host_detail: The host_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: HostDetailForGetBaselineDetectProgressDetailOutput
        """

        self._host_detail = host_detail

    @property
    def host_num(self):
        """Gets the host_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The host_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: HostNumForGetBaselineDetectProgressDetailOutput
        """
        return self._host_num

    @host_num.setter
    def host_num(self, host_num):
        """Sets the host_num of this GetBaselineDetectProgressDetailResponse.


        :param host_num: The host_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: HostNumForGetBaselineDetectProgressDetailOutput
        """

        self._host_num = host_num

    @property
    def progress_detail(self):
        """Gets the progress_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The progress_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: ProgressDetailForGetBaselineDetectProgressDetailOutput
        """
        return self._progress_detail

    @progress_detail.setter
    def progress_detail(self, progress_detail):
        """Sets the progress_detail of this GetBaselineDetectProgressDetailResponse.


        :param progress_detail: The progress_detail of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: ProgressDetailForGetBaselineDetectProgressDetailOutput
        """

        self._progress_detail = progress_detail

    @property
    def risk_num(self):
        """Gets the risk_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The risk_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: RiskNumForGetBaselineDetectProgressDetailOutput
        """
        return self._risk_num

    @risk_num.setter
    def risk_num(self, risk_num):
        """Sets the risk_num of this GetBaselineDetectProgressDetailResponse.


        :param risk_num: The risk_num of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: RiskNumForGetBaselineDetectProgressDetailOutput
        """

        self._risk_num = risk_num

    @property
    def status(self):
        """Gets the status of this GetBaselineDetectProgressDetailResponse.  # noqa: E501


        :return: The status of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetBaselineDetectProgressDetailResponse.


        :param status: The status of this GetBaselineDetectProgressDetailResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetBaselineDetectProgressDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetBaselineDetectProgressDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetBaselineDetectProgressDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
