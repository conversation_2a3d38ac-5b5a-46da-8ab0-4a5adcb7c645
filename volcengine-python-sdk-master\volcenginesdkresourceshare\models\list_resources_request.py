# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourcesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'principal': 'str',
        'resource_share_trns': 'str',
        'resource_trns': 'str',
        'resource_type': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'principal': 'Principal',
        'resource_share_trns': 'ResourceShareTrns',
        'resource_trns': 'ResourceTrns',
        'resource_type': 'ResourceType'
    }

    def __init__(self, max_results=None, next_token=None, principal=None, resource_share_trns=None, resource_trns=None, resource_type=None, _configuration=None):  # noqa: E501
        """ListResourcesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._principal = None
        self._resource_share_trns = None
        self._resource_trns = None
        self._resource_type = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if principal is not None:
            self.principal = principal
        if resource_share_trns is not None:
            self.resource_share_trns = resource_share_trns
        if resource_trns is not None:
            self.resource_trns = resource_trns
        if resource_type is not None:
            self.resource_type = resource_type

    @property
    def max_results(self):
        """Gets the max_results of this ListResourcesRequest.  # noqa: E501


        :return: The max_results of this ListResourcesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListResourcesRequest.


        :param max_results: The max_results of this ListResourcesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListResourcesRequest.  # noqa: E501


        :return: The next_token of this ListResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListResourcesRequest.


        :param next_token: The next_token of this ListResourcesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def principal(self):
        """Gets the principal of this ListResourcesRequest.  # noqa: E501


        :return: The principal of this ListResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._principal

    @principal.setter
    def principal(self, principal):
        """Sets the principal of this ListResourcesRequest.


        :param principal: The principal of this ListResourcesRequest.  # noqa: E501
        :type: str
        """

        self._principal = principal

    @property
    def resource_share_trns(self):
        """Gets the resource_share_trns of this ListResourcesRequest.  # noqa: E501


        :return: The resource_share_trns of this ListResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trns

    @resource_share_trns.setter
    def resource_share_trns(self, resource_share_trns):
        """Sets the resource_share_trns of this ListResourcesRequest.


        :param resource_share_trns: The resource_share_trns of this ListResourcesRequest.  # noqa: E501
        :type: str
        """

        self._resource_share_trns = resource_share_trns

    @property
    def resource_trns(self):
        """Gets the resource_trns of this ListResourcesRequest.  # noqa: E501


        :return: The resource_trns of this ListResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_trns

    @resource_trns.setter
    def resource_trns(self, resource_trns):
        """Sets the resource_trns of this ListResourcesRequest.


        :param resource_trns: The resource_trns of this ListResourcesRequest.  # noqa: E501
        :type: str
        """

        self._resource_trns = resource_trns

    @property
    def resource_type(self):
        """Gets the resource_type of this ListResourcesRequest.  # noqa: E501


        :return: The resource_type of this ListResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListResourcesRequest.


        :param resource_type: The resource_type of this ListResourcesRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourcesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourcesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourcesRequest):
            return True

        return self.to_dict() != other.to_dict()
