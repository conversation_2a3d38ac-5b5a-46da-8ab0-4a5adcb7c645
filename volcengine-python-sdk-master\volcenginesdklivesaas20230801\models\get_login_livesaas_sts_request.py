# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetLoginLivesaasStsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'duration_seconds': 'int',
        'follower_user_name': 'str'
    }

    attribute_map = {
        'duration_seconds': 'DurationSeconds',
        'follower_user_name': 'FollowerUserName'
    }

    def __init__(self, duration_seconds=None, follower_user_name=None, _configuration=None):  # noqa: E501
        """GetLoginLivesaasStsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._duration_seconds = None
        self._follower_user_name = None
        self.discriminator = None

        if duration_seconds is not None:
            self.duration_seconds = duration_seconds
        if follower_user_name is not None:
            self.follower_user_name = follower_user_name

    @property
    def duration_seconds(self):
        """Gets the duration_seconds of this GetLoginLivesaasStsRequest.  # noqa: E501


        :return: The duration_seconds of this GetLoginLivesaasStsRequest.  # noqa: E501
        :rtype: int
        """
        return self._duration_seconds

    @duration_seconds.setter
    def duration_seconds(self, duration_seconds):
        """Sets the duration_seconds of this GetLoginLivesaasStsRequest.


        :param duration_seconds: The duration_seconds of this GetLoginLivesaasStsRequest.  # noqa: E501
        :type: int
        """

        self._duration_seconds = duration_seconds

    @property
    def follower_user_name(self):
        """Gets the follower_user_name of this GetLoginLivesaasStsRequest.  # noqa: E501


        :return: The follower_user_name of this GetLoginLivesaasStsRequest.  # noqa: E501
        :rtype: str
        """
        return self._follower_user_name

    @follower_user_name.setter
    def follower_user_name(self, follower_user_name):
        """Sets the follower_user_name of this GetLoginLivesaasStsRequest.


        :param follower_user_name: The follower_user_name of this GetLoginLivesaasStsRequest.  # noqa: E501
        :type: str
        """

        self._follower_user_name = follower_user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetLoginLivesaasStsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetLoginLivesaasStsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetLoginLivesaasStsRequest):
            return True

        return self.to_dict() != other.to_dict()
