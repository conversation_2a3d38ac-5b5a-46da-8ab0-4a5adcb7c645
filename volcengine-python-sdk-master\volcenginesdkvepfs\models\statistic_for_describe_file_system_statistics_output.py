# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatisticForDescribeFileSystemStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'capacity_info': 'CapacityInfoForDescribeFileSystemStatisticsOutput',
        'file_system_type': 'str',
        'region_id': 'str',
        'store_type': 'str',
        'store_type_cn': 'str',
        'store_type_en': 'str',
        'total_count': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'capacity_info': 'CapacityInfo',
        'file_system_type': 'FileSystemType',
        'region_id': 'RegionId',
        'store_type': 'StoreType',
        'store_type_cn': 'StoreTypeCN',
        'store_type_en': 'StoreTypeEN',
        'total_count': 'TotalCount'
    }

    def __init__(self, account_id=None, capacity_info=None, file_system_type=None, region_id=None, store_type=None, store_type_cn=None, store_type_en=None, total_count=None, _configuration=None):  # noqa: E501
        """StatisticForDescribeFileSystemStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._capacity_info = None
        self._file_system_type = None
        self._region_id = None
        self._store_type = None
        self._store_type_cn = None
        self._store_type_en = None
        self._total_count = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if capacity_info is not None:
            self.capacity_info = capacity_info
        if file_system_type is not None:
            self.file_system_type = file_system_type
        if region_id is not None:
            self.region_id = region_id
        if store_type is not None:
            self.store_type = store_type
        if store_type_cn is not None:
            self.store_type_cn = store_type_cn
        if store_type_en is not None:
            self.store_type_en = store_type_en
        if total_count is not None:
            self.total_count = total_count

    @property
    def account_id(self):
        """Gets the account_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The account_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this StatisticForDescribeFileSystemStatisticsOutput.


        :param account_id: The account_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def capacity_info(self):
        """Gets the capacity_info of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The capacity_info of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: CapacityInfoForDescribeFileSystemStatisticsOutput
        """
        return self._capacity_info

    @capacity_info.setter
    def capacity_info(self, capacity_info):
        """Sets the capacity_info of this StatisticForDescribeFileSystemStatisticsOutput.


        :param capacity_info: The capacity_info of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: CapacityInfoForDescribeFileSystemStatisticsOutput
        """

        self._capacity_info = capacity_info

    @property
    def file_system_type(self):
        """Gets the file_system_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The file_system_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_type

    @file_system_type.setter
    def file_system_type(self, file_system_type):
        """Sets the file_system_type of this StatisticForDescribeFileSystemStatisticsOutput.


        :param file_system_type: The file_system_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._file_system_type = file_system_type

    @property
    def region_id(self):
        """Gets the region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this StatisticForDescribeFileSystemStatisticsOutput.


        :param region_id: The region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def store_type(self):
        """Gets the store_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The store_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type

    @store_type.setter
    def store_type(self, store_type):
        """Sets the store_type of this StatisticForDescribeFileSystemStatisticsOutput.


        :param store_type: The store_type of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._store_type = store_type

    @property
    def store_type_cn(self):
        """Gets the store_type_cn of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The store_type_cn of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type_cn

    @store_type_cn.setter
    def store_type_cn(self, store_type_cn):
        """Sets the store_type_cn of this StatisticForDescribeFileSystemStatisticsOutput.


        :param store_type_cn: The store_type_cn of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._store_type_cn = store_type_cn

    @property
    def store_type_en(self):
        """Gets the store_type_en of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The store_type_en of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._store_type_en

    @store_type_en.setter
    def store_type_en(self, store_type_en):
        """Sets the store_type_en of this StatisticForDescribeFileSystemStatisticsOutput.


        :param store_type_en: The store_type_en of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._store_type_en = store_type_en

    @property
    def total_count(self):
        """Gets the total_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The total_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this StatisticForDescribeFileSystemStatisticsOutput.


        :param total_count: The total_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatisticForDescribeFileSystemStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatisticForDescribeFileSystemStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatisticForDescribeFileSystemStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
