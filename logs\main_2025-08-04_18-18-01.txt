usage: main.py [-h] [--name NAME] [--rename] [--list]

AI图像识别验证工具

options:
  -h, --help   show this help message and exit
  --name NAME  指定要使用的batch_configs配置文件名（如：1.json 或 2025-08-01_example.json）
  --rename     将batch_configs目录中的所有JSON文件重命名为数字格式（1.json, 2.json, ...）
  --list       列出batch_configs目录中所有可用的配置文件

使用示例:
  python main.py                           # 交互式模式
  python main.py --name 1.json            # 指定配置文件运行
  python main.py --name 2025-08-01_example.json  # 指定配置文件运行
  python main.py --rename                  # 重命名配置文件为数字格式
  python main.py --list                    # 列出所有可用的配置文件
        

日志已保存到: logs\main_2025-08-04_18-18-01.txt
