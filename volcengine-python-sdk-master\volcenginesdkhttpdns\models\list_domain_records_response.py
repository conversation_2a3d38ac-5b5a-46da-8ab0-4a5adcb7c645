# coding: utf-8

"""
    httpdns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDomainRecordsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aaaa_list': 'list[AAAAListForListDomainRecordsOutput]',
        'a_list': 'list[AListForListDomainRecordsOutput]',
        'cname_list': 'list[CnameListForListDomainRecordsOutput]',
        'create_time': 'str',
        'domain_id': 'int',
        'domain_name': 'str'
    }

    attribute_map = {
        'aaaa_list': 'AAAAList',
        'a_list': 'AList',
        'cname_list': 'CnameList',
        'create_time': 'CreateTime',
        'domain_id': 'DomainId',
        'domain_name': 'DomainName'
    }

    def __init__(self, aaaa_list=None, a_list=None, cname_list=None, create_time=None, domain_id=None, domain_name=None, _configuration=None):  # noqa: E501
        """ListDomainRecordsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aaaa_list = None
        self._a_list = None
        self._cname_list = None
        self._create_time = None
        self._domain_id = None
        self._domain_name = None
        self.discriminator = None

        if aaaa_list is not None:
            self.aaaa_list = aaaa_list
        if a_list is not None:
            self.a_list = a_list
        if cname_list is not None:
            self.cname_list = cname_list
        if create_time is not None:
            self.create_time = create_time
        if domain_id is not None:
            self.domain_id = domain_id
        if domain_name is not None:
            self.domain_name = domain_name

    @property
    def aaaa_list(self):
        """Gets the aaaa_list of this ListDomainRecordsResponse.  # noqa: E501


        :return: The aaaa_list of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: list[AAAAListForListDomainRecordsOutput]
        """
        return self._aaaa_list

    @aaaa_list.setter
    def aaaa_list(self, aaaa_list):
        """Sets the aaaa_list of this ListDomainRecordsResponse.


        :param aaaa_list: The aaaa_list of this ListDomainRecordsResponse.  # noqa: E501
        :type: list[AAAAListForListDomainRecordsOutput]
        """

        self._aaaa_list = aaaa_list

    @property
    def a_list(self):
        """Gets the a_list of this ListDomainRecordsResponse.  # noqa: E501


        :return: The a_list of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: list[AListForListDomainRecordsOutput]
        """
        return self._a_list

    @a_list.setter
    def a_list(self, a_list):
        """Sets the a_list of this ListDomainRecordsResponse.


        :param a_list: The a_list of this ListDomainRecordsResponse.  # noqa: E501
        :type: list[AListForListDomainRecordsOutput]
        """

        self._a_list = a_list

    @property
    def cname_list(self):
        """Gets the cname_list of this ListDomainRecordsResponse.  # noqa: E501


        :return: The cname_list of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: list[CnameListForListDomainRecordsOutput]
        """
        return self._cname_list

    @cname_list.setter
    def cname_list(self, cname_list):
        """Sets the cname_list of this ListDomainRecordsResponse.


        :param cname_list: The cname_list of this ListDomainRecordsResponse.  # noqa: E501
        :type: list[CnameListForListDomainRecordsOutput]
        """

        self._cname_list = cname_list

    @property
    def create_time(self):
        """Gets the create_time of this ListDomainRecordsResponse.  # noqa: E501


        :return: The create_time of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ListDomainRecordsResponse.


        :param create_time: The create_time of this ListDomainRecordsResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def domain_id(self):
        """Gets the domain_id of this ListDomainRecordsResponse.  # noqa: E501


        :return: The domain_id of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: int
        """
        return self._domain_id

    @domain_id.setter
    def domain_id(self, domain_id):
        """Sets the domain_id of this ListDomainRecordsResponse.


        :param domain_id: The domain_id of this ListDomainRecordsResponse.  # noqa: E501
        :type: int
        """

        self._domain_id = domain_id

    @property
    def domain_name(self):
        """Gets the domain_name of this ListDomainRecordsResponse.  # noqa: E501


        :return: The domain_name of this ListDomainRecordsResponse.  # noqa: E501
        :rtype: str
        """
        return self._domain_name

    @domain_name.setter
    def domain_name(self, domain_name):
        """Sets the domain_name of this ListDomainRecordsResponse.


        :param domain_name: The domain_name of this ListDomainRecordsResponse.  # noqa: E501
        :type: str
        """

        self._domain_name = domain_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDomainRecordsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDomainRecordsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDomainRecordsResponse):
            return True

        return self.to_dict() != other.to_dict()
