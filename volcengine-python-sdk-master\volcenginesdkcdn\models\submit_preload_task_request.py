# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubmitPreloadTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'concurrent_limit': 'int',
        'layer': 'str',
        'request_header_instances': 'list[RequestHeaderInstanceForSubmitPreloadTaskInput]',
        'urls': 'str'
    }

    attribute_map = {
        'area': 'Area',
        'concurrent_limit': 'ConcurrentLimit',
        'layer': 'Layer',
        'request_header_instances': 'RequestHeaderInstances',
        'urls': 'Urls'
    }

    def __init__(self, area=None, concurrent_limit=None, layer=None, request_header_instances=None, urls=None, _configuration=None):  # noqa: E501
        """SubmitPreloadTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._concurrent_limit = None
        self._layer = None
        self._request_header_instances = None
        self._urls = None
        self.discriminator = None

        if area is not None:
            self.area = area
        if concurrent_limit is not None:
            self.concurrent_limit = concurrent_limit
        if layer is not None:
            self.layer = layer
        if request_header_instances is not None:
            self.request_header_instances = request_header_instances
        self.urls = urls

    @property
    def area(self):
        """Gets the area of this SubmitPreloadTaskRequest.  # noqa: E501


        :return: The area of this SubmitPreloadTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this SubmitPreloadTaskRequest.


        :param area: The area of this SubmitPreloadTaskRequest.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def concurrent_limit(self):
        """Gets the concurrent_limit of this SubmitPreloadTaskRequest.  # noqa: E501


        :return: The concurrent_limit of this SubmitPreloadTaskRequest.  # noqa: E501
        :rtype: int
        """
        return self._concurrent_limit

    @concurrent_limit.setter
    def concurrent_limit(self, concurrent_limit):
        """Sets the concurrent_limit of this SubmitPreloadTaskRequest.


        :param concurrent_limit: The concurrent_limit of this SubmitPreloadTaskRequest.  # noqa: E501
        :type: int
        """

        self._concurrent_limit = concurrent_limit

    @property
    def layer(self):
        """Gets the layer of this SubmitPreloadTaskRequest.  # noqa: E501


        :return: The layer of this SubmitPreloadTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._layer

    @layer.setter
    def layer(self, layer):
        """Sets the layer of this SubmitPreloadTaskRequest.


        :param layer: The layer of this SubmitPreloadTaskRequest.  # noqa: E501
        :type: str
        """

        self._layer = layer

    @property
    def request_header_instances(self):
        """Gets the request_header_instances of this SubmitPreloadTaskRequest.  # noqa: E501


        :return: The request_header_instances of this SubmitPreloadTaskRequest.  # noqa: E501
        :rtype: list[RequestHeaderInstanceForSubmitPreloadTaskInput]
        """
        return self._request_header_instances

    @request_header_instances.setter
    def request_header_instances(self, request_header_instances):
        """Sets the request_header_instances of this SubmitPreloadTaskRequest.


        :param request_header_instances: The request_header_instances of this SubmitPreloadTaskRequest.  # noqa: E501
        :type: list[RequestHeaderInstanceForSubmitPreloadTaskInput]
        """

        self._request_header_instances = request_header_instances

    @property
    def urls(self):
        """Gets the urls of this SubmitPreloadTaskRequest.  # noqa: E501


        :return: The urls of this SubmitPreloadTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._urls

    @urls.setter
    def urls(self, urls):
        """Sets the urls of this SubmitPreloadTaskRequest.


        :param urls: The urls of this SubmitPreloadTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and urls is None:
            raise ValueError("Invalid value for `urls`, must not be `None`")  # noqa: E501

        self._urls = urls

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubmitPreloadTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubmitPreloadTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubmitPreloadTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
