# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRecordAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ttls': 'list[int]',
        'types': 'list[str]',
        'weight_limit': 'int'
    }

    attribute_map = {
        'ttls': 'TTLs',
        'types': 'Types',
        'weight_limit': 'WeightLimit'
    }

    def __init__(self, ttls=None, types=None, weight_limit=None, _configuration=None):  # noqa: E501
        """ListRecordAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ttls = None
        self._types = None
        self._weight_limit = None
        self.discriminator = None

        if ttls is not None:
            self.ttls = ttls
        if types is not None:
            self.types = types
        if weight_limit is not None:
            self.weight_limit = weight_limit

    @property
    def ttls(self):
        """Gets the ttls of this ListRecordAttributesResponse.  # noqa: E501


        :return: The ttls of this ListRecordAttributesResponse.  # noqa: E501
        :rtype: list[int]
        """
        return self._ttls

    @ttls.setter
    def ttls(self, ttls):
        """Sets the ttls of this ListRecordAttributesResponse.


        :param ttls: The ttls of this ListRecordAttributesResponse.  # noqa: E501
        :type: list[int]
        """

        self._ttls = ttls

    @property
    def types(self):
        """Gets the types of this ListRecordAttributesResponse.  # noqa: E501


        :return: The types of this ListRecordAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this ListRecordAttributesResponse.


        :param types: The types of this ListRecordAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._types = types

    @property
    def weight_limit(self):
        """Gets the weight_limit of this ListRecordAttributesResponse.  # noqa: E501


        :return: The weight_limit of this ListRecordAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._weight_limit

    @weight_limit.setter
    def weight_limit(self, weight_limit):
        """Sets the weight_limit of this ListRecordAttributesResponse.


        :param weight_limit: The weight_limit of this ListRecordAttributesResponse.  # noqa: E501
        :type: int
        """

        self._weight_limit = weight_limit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRecordAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRecordAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRecordAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
