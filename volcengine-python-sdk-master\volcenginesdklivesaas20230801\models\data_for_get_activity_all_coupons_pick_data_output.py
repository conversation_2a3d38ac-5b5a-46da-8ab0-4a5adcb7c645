# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetActivityAllCouponsPickDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'coupon_id': 'int',
        'external_user_id': 'str',
        'id': 'int',
        'name': 'str',
        'pickup_time': 'int',
        'send_time': 'int',
        'third_party_id': 'str',
        'user_id': 'int',
        'user_nick_name': 'str'
    }

    attribute_map = {
        'coupon_id': 'CouponId',
        'external_user_id': 'ExternalUserId',
        'id': 'Id',
        'name': 'Name',
        'pickup_time': 'PickupTime',
        'send_time': 'SendTime',
        'third_party_id': 'ThirdPartyId',
        'user_id': 'UserId',
        'user_nick_name': 'UserNickName'
    }

    def __init__(self, coupon_id=None, external_user_id=None, id=None, name=None, pickup_time=None, send_time=None, third_party_id=None, user_id=None, user_nick_name=None, _configuration=None):  # noqa: E501
        """DataForGetActivityAllCouponsPickDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._coupon_id = None
        self._external_user_id = None
        self._id = None
        self._name = None
        self._pickup_time = None
        self._send_time = None
        self._third_party_id = None
        self._user_id = None
        self._user_nick_name = None
        self.discriminator = None

        if coupon_id is not None:
            self.coupon_id = coupon_id
        if external_user_id is not None:
            self.external_user_id = external_user_id
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if pickup_time is not None:
            self.pickup_time = pickup_time
        if send_time is not None:
            self.send_time = send_time
        if third_party_id is not None:
            self.third_party_id = third_party_id
        if user_id is not None:
            self.user_id = user_id
        if user_nick_name is not None:
            self.user_nick_name = user_nick_name

    @property
    def coupon_id(self):
        """Gets the coupon_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The coupon_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._coupon_id

    @coupon_id.setter
    def coupon_id(self, coupon_id):
        """Sets the coupon_id of this DataForGetActivityAllCouponsPickDataOutput.


        :param coupon_id: The coupon_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: int
        """

        self._coupon_id = coupon_id

    @property
    def external_user_id(self):
        """Gets the external_user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The external_user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this DataForGetActivityAllCouponsPickDataOutput.


        :param external_user_id: The external_user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def id(self):
        """Gets the id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetActivityAllCouponsPickDataOutput.


        :param id: The id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForGetActivityAllCouponsPickDataOutput.


        :param name: The name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def pickup_time(self):
        """Gets the pickup_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The pickup_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._pickup_time

    @pickup_time.setter
    def pickup_time(self, pickup_time):
        """Sets the pickup_time of this DataForGetActivityAllCouponsPickDataOutput.


        :param pickup_time: The pickup_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: int
        """

        self._pickup_time = pickup_time

    @property
    def send_time(self):
        """Gets the send_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The send_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this DataForGetActivityAllCouponsPickDataOutput.


        :param send_time: The send_time of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def third_party_id(self):
        """Gets the third_party_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The third_party_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._third_party_id

    @third_party_id.setter
    def third_party_id(self, third_party_id):
        """Sets the third_party_id of this DataForGetActivityAllCouponsPickDataOutput.


        :param third_party_id: The third_party_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: str
        """

        self._third_party_id = third_party_id

    @property
    def user_id(self):
        """Gets the user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this DataForGetActivityAllCouponsPickDataOutput.


        :param user_id: The user_id of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_nick_name(self):
        """Gets the user_nick_name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501


        :return: The user_nick_name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_nick_name

    @user_nick_name.setter
    def user_nick_name(self, user_nick_name):
        """Sets the user_nick_name of this DataForGetActivityAllCouponsPickDataOutput.


        :param user_nick_name: The user_nick_name of this DataForGetActivityAllCouponsPickDataOutput.  # noqa: E501
        :type: str
        """

        self._user_nick_name = user_nick_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetActivityAllCouponsPickDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetActivityAllCouponsPickDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetActivityAllCouponsPickDataOutput):
            return True

        return self.to_dict() != other.to_dict()
