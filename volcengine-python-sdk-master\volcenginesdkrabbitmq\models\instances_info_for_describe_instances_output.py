# coding: utf-8

"""
    rabbitmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstancesInfoForDescribeInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'apply_private_dnsto_public': 'bool',
        'arch_type': 'str',
        'charge_detail': 'ChargeDetailForDescribeInstancesOutput',
        'compute_spec': 'str',
        'create_time': 'str',
        'eip_id': 'str',
        'init_user_name': 'str',
        'instance_description': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'is_encrypted': 'bool',
        'project_name': 'str',
        'region_description': 'str',
        'region_id': 'str',
        'storage_space': 'int',
        'subnet_id': 'str',
        'tags': 'list[TagForDescribeInstancesOutput]',
        'used_storage_space': 'int',
        'version': 'str',
        'vpc_id': 'str',
        'zone_description': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'apply_private_dnsto_public': 'ApplyPrivateDNSToPublic',
        'arch_type': 'ArchType',
        'charge_detail': 'ChargeDetail',
        'compute_spec': 'ComputeSpec',
        'create_time': 'CreateTime',
        'eip_id': 'EipId',
        'init_user_name': 'InitUserName',
        'instance_description': 'InstanceDescription',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'is_encrypted': 'IsEncrypted',
        'project_name': 'ProjectName',
        'region_description': 'RegionDescription',
        'region_id': 'RegionId',
        'storage_space': 'StorageSpace',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'used_storage_space': 'UsedStorageSpace',
        'version': 'Version',
        'vpc_id': 'VpcId',
        'zone_description': 'ZoneDescription',
        'zone_id': 'ZoneId'
    }

    def __init__(self, account_id=None, apply_private_dnsto_public=None, arch_type=None, charge_detail=None, compute_spec=None, create_time=None, eip_id=None, init_user_name=None, instance_description=None, instance_id=None, instance_name=None, instance_status=None, is_encrypted=None, project_name=None, region_description=None, region_id=None, storage_space=None, subnet_id=None, tags=None, used_storage_space=None, version=None, vpc_id=None, zone_description=None, zone_id=None, _configuration=None):  # noqa: E501
        """InstancesInfoForDescribeInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._apply_private_dnsto_public = None
        self._arch_type = None
        self._charge_detail = None
        self._compute_spec = None
        self._create_time = None
        self._eip_id = None
        self._init_user_name = None
        self._instance_description = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._is_encrypted = None
        self._project_name = None
        self._region_description = None
        self._region_id = None
        self._storage_space = None
        self._subnet_id = None
        self._tags = None
        self._used_storage_space = None
        self._version = None
        self._vpc_id = None
        self._zone_description = None
        self._zone_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if apply_private_dnsto_public is not None:
            self.apply_private_dnsto_public = apply_private_dnsto_public
        if arch_type is not None:
            self.arch_type = arch_type
        if charge_detail is not None:
            self.charge_detail = charge_detail
        if compute_spec is not None:
            self.compute_spec = compute_spec
        if create_time is not None:
            self.create_time = create_time
        if eip_id is not None:
            self.eip_id = eip_id
        if init_user_name is not None:
            self.init_user_name = init_user_name
        if instance_description is not None:
            self.instance_description = instance_description
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if is_encrypted is not None:
            self.is_encrypted = is_encrypted
        if project_name is not None:
            self.project_name = project_name
        if region_description is not None:
            self.region_description = region_description
        if region_id is not None:
            self.region_id = region_id
        if storage_space is not None:
            self.storage_space = storage_space
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if used_storage_space is not None:
            self.used_storage_space = used_storage_space
        if version is not None:
            self.version = version
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_description is not None:
            self.zone_description = zone_description
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def account_id(self):
        """Gets the account_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The account_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this InstancesInfoForDescribeInstancesOutput.


        :param account_id: The account_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def apply_private_dnsto_public(self):
        """Gets the apply_private_dnsto_public of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The apply_private_dnsto_public of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._apply_private_dnsto_public

    @apply_private_dnsto_public.setter
    def apply_private_dnsto_public(self, apply_private_dnsto_public):
        """Sets the apply_private_dnsto_public of this InstancesInfoForDescribeInstancesOutput.


        :param apply_private_dnsto_public: The apply_private_dnsto_public of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._apply_private_dnsto_public = apply_private_dnsto_public

    @property
    def arch_type(self):
        """Gets the arch_type of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The arch_type of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._arch_type

    @arch_type.setter
    def arch_type(self, arch_type):
        """Sets the arch_type of this InstancesInfoForDescribeInstancesOutput.


        :param arch_type: The arch_type of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._arch_type = arch_type

    @property
    def charge_detail(self):
        """Gets the charge_detail of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The charge_detail of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: ChargeDetailForDescribeInstancesOutput
        """
        return self._charge_detail

    @charge_detail.setter
    def charge_detail(self, charge_detail):
        """Sets the charge_detail of this InstancesInfoForDescribeInstancesOutput.


        :param charge_detail: The charge_detail of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: ChargeDetailForDescribeInstancesOutput
        """

        self._charge_detail = charge_detail

    @property
    def compute_spec(self):
        """Gets the compute_spec of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The compute_spec of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._compute_spec

    @compute_spec.setter
    def compute_spec(self, compute_spec):
        """Sets the compute_spec of this InstancesInfoForDescribeInstancesOutput.


        :param compute_spec: The compute_spec of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._compute_spec = compute_spec

    @property
    def create_time(self):
        """Gets the create_time of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The create_time of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this InstancesInfoForDescribeInstancesOutput.


        :param create_time: The create_time of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def eip_id(self):
        """Gets the eip_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The eip_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_id

    @eip_id.setter
    def eip_id(self, eip_id):
        """Sets the eip_id of this InstancesInfoForDescribeInstancesOutput.


        :param eip_id: The eip_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._eip_id = eip_id

    @property
    def init_user_name(self):
        """Gets the init_user_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The init_user_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._init_user_name

    @init_user_name.setter
    def init_user_name(self, init_user_name):
        """Sets the init_user_name of this InstancesInfoForDescribeInstancesOutput.


        :param init_user_name: The init_user_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._init_user_name = init_user_name

    @property
    def instance_description(self):
        """Gets the instance_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_description

    @instance_description.setter
    def instance_description(self, instance_description):
        """Sets the instance_description of this InstancesInfoForDescribeInstancesOutput.


        :param instance_description: The instance_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_description = instance_description

    @property
    def instance_id(self):
        """Gets the instance_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstancesInfoForDescribeInstancesOutput.


        :param instance_id: The instance_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstancesInfoForDescribeInstancesOutput.


        :param instance_name: The instance_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_status of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this InstancesInfoForDescribeInstancesOutput.


        :param instance_status: The instance_status of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def is_encrypted(self):
        """Gets the is_encrypted of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The is_encrypted of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_encrypted

    @is_encrypted.setter
    def is_encrypted(self, is_encrypted):
        """Sets the is_encrypted of this InstancesInfoForDescribeInstancesOutput.


        :param is_encrypted: The is_encrypted of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._is_encrypted = is_encrypted

    @property
    def project_name(self):
        """Gets the project_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The project_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstancesInfoForDescribeInstancesOutput.


        :param project_name: The project_name of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_description(self):
        """Gets the region_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The region_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_description

    @region_description.setter
    def region_description(self, region_description):
        """Sets the region_description of this InstancesInfoForDescribeInstancesOutput.


        :param region_description: The region_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._region_description = region_description

    @property
    def region_id(self):
        """Gets the region_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The region_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this InstancesInfoForDescribeInstancesOutput.


        :param region_id: The region_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def storage_space(self):
        """Gets the storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this InstancesInfoForDescribeInstancesOutput.


        :param storage_space: The storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def subnet_id(self):
        """Gets the subnet_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The subnet_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this InstancesInfoForDescribeInstancesOutput.


        :param subnet_id: The subnet_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The tags of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[TagForDescribeInstancesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this InstancesInfoForDescribeInstancesOutput.


        :param tags: The tags of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: list[TagForDescribeInstancesOutput]
        """

        self._tags = tags

    @property
    def used_storage_space(self):
        """Gets the used_storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The used_storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._used_storage_space

    @used_storage_space.setter
    def used_storage_space(self, used_storage_space):
        """Sets the used_storage_space of this InstancesInfoForDescribeInstancesOutput.


        :param used_storage_space: The used_storage_space of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._used_storage_space = used_storage_space

    @property
    def version(self):
        """Gets the version of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The version of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this InstancesInfoForDescribeInstancesOutput.


        :param version: The version of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    @property
    def vpc_id(self):
        """Gets the vpc_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The vpc_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this InstancesInfoForDescribeInstancesOutput.


        :param vpc_id: The vpc_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_description(self):
        """Gets the zone_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The zone_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_description

    @zone_description.setter
    def zone_description(self, zone_description):
        """Sets the zone_description of this InstancesInfoForDescribeInstancesOutput.


        :param zone_description: The zone_description of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._zone_description = zone_description

    @property
    def zone_id(self):
        """Gets the zone_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501


        :return: The zone_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this InstancesInfoForDescribeInstancesOutput.


        :param zone_id: The zone_id of this InstancesInfoForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstancesInfoForDescribeInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstancesInfoForDescribeInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstancesInfoForDescribeInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
