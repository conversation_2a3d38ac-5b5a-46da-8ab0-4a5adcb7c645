# coding: utf-8

# flake8: noqa
"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkecs.models.account_for_describe_image_share_permission_output import AccountForDescribeImageSharePermissionOutput
from volcenginesdkecs.models.allocate_dedicated_hosts_request import AllocateDedicatedHostsRequest
from volcenginesdkecs.models.allocate_dedicated_hosts_response import AllocateDedicatedHostsResponse
from volcenginesdkecs.models.associate_instances_iam_role_request import AssociateInstancesIamRoleRequest
from volcenginesdkecs.models.associate_instances_iam_role_response import AssociateInstancesIamRoleResponse
from volcenginesdkecs.models.attach_key_pair_request import AttachKeyPairRequest
from volcenginesdkecs.models.attach_key_pair_response import AttachKeyPairResponse
from volcenginesdkecs.models.available_instance_type_for_describe_dedicated_host_clusters_output import AvailableInstanceTypeForDescribeDedicatedHostClustersOutput
from volcenginesdkecs.models.available_resource_for_describe_available_resource_output import AvailableResourceForDescribeAvailableResourceOutput
from volcenginesdkecs.models.available_spot_resource_for_describe_spot_advice_output import AvailableSpotResourceForDescribeSpotAdviceOutput
from volcenginesdkecs.models.available_zone_for_describe_available_resource_output import AvailableZoneForDescribeAvailableResourceOutput
from volcenginesdkecs.models.capacity_for_describe_dedicated_hosts_output import CapacityForDescribeDedicatedHostsOutput
from volcenginesdkecs.models.capacity_for_describe_deployment_sets_output import CapacityForDescribeDeploymentSetsOutput
from volcenginesdkecs.models.command_for_describe_commands_output import CommandForDescribeCommandsOutput
from volcenginesdkecs.models.configuration_for_modify_reserved_instances_input import ConfigurationForModifyReservedInstancesInput
from volcenginesdkecs.models.copy_image_request import CopyImageRequest
from volcenginesdkecs.models.copy_image_response import CopyImageResponse
from volcenginesdkecs.models.cpu_options_for_describe_instances_output import CpuOptionsForDescribeInstancesOutput
from volcenginesdkecs.models.create_command_request import CreateCommandRequest
from volcenginesdkecs.models.create_command_response import CreateCommandResponse
from volcenginesdkecs.models.create_dedicated_host_cluster_request import CreateDedicatedHostClusterRequest
from volcenginesdkecs.models.create_dedicated_host_cluster_response import CreateDedicatedHostClusterResponse
from volcenginesdkecs.models.create_deployment_set_request import CreateDeploymentSetRequest
from volcenginesdkecs.models.create_deployment_set_response import CreateDeploymentSetResponse
from volcenginesdkecs.models.create_hpc_cluster_request import CreateHpcClusterRequest
from volcenginesdkecs.models.create_hpc_cluster_response import CreateHpcClusterResponse
from volcenginesdkecs.models.create_image_request import CreateImageRequest
from volcenginesdkecs.models.create_image_response import CreateImageResponse
from volcenginesdkecs.models.create_key_pair_request import CreateKeyPairRequest
from volcenginesdkecs.models.create_key_pair_response import CreateKeyPairResponse
from volcenginesdkecs.models.create_scheduled_instances_request import CreateScheduledInstancesRequest
from volcenginesdkecs.models.create_scheduled_instances_response import CreateScheduledInstancesResponse
from volcenginesdkecs.models.create_subscription_request import CreateSubscriptionRequest
from volcenginesdkecs.models.create_subscription_response import CreateSubscriptionResponse
from volcenginesdkecs.models.create_tags_request import CreateTagsRequest
from volcenginesdkecs.models.create_tags_response import CreateTagsResponse
from volcenginesdkecs.models.dedicated_host_cluster_capacity_for_describe_dedicated_host_clusters_output import DedicatedHostClusterCapacityForDescribeDedicatedHostClustersOutput
from volcenginesdkecs.models.dedicated_host_cluster_for_describe_dedicated_host_clusters_output import DedicatedHostClusterForDescribeDedicatedHostClustersOutput
from volcenginesdkecs.models.dedicated_host_for_describe_dedicated_hosts_output import DedicatedHostForDescribeDedicatedHostsOutput
from volcenginesdkecs.models.dedicated_host_type_for_describe_dedicated_host_types_output import DedicatedHostTypeForDescribeDedicatedHostTypesOutput
from volcenginesdkecs.models.delete_command_request import DeleteCommandRequest
from volcenginesdkecs.models.delete_command_response import DeleteCommandResponse
from volcenginesdkecs.models.delete_dedicated_host_cluster_request import DeleteDedicatedHostClusterRequest
from volcenginesdkecs.models.delete_dedicated_host_cluster_response import DeleteDedicatedHostClusterResponse
from volcenginesdkecs.models.delete_deployment_set_request import DeleteDeploymentSetRequest
from volcenginesdkecs.models.delete_deployment_set_response import DeleteDeploymentSetResponse
from volcenginesdkecs.models.delete_hpc_cluster_request import DeleteHpcClusterRequest
from volcenginesdkecs.models.delete_hpc_cluster_response import DeleteHpcClusterResponse
from volcenginesdkecs.models.delete_images_request import DeleteImagesRequest
from volcenginesdkecs.models.delete_images_response import DeleteImagesResponse
from volcenginesdkecs.models.delete_instance_request import DeleteInstanceRequest
from volcenginesdkecs.models.delete_instance_response import DeleteInstanceResponse
from volcenginesdkecs.models.delete_instances_request import DeleteInstancesRequest
from volcenginesdkecs.models.delete_instances_response import DeleteInstancesResponse
from volcenginesdkecs.models.delete_invocation_request import DeleteInvocationRequest
from volcenginesdkecs.models.delete_invocation_response import DeleteInvocationResponse
from volcenginesdkecs.models.delete_key_pairs_request import DeleteKeyPairsRequest
from volcenginesdkecs.models.delete_key_pairs_response import DeleteKeyPairsResponse
from volcenginesdkecs.models.delete_scheduled_instance_request import DeleteScheduledInstanceRequest
from volcenginesdkecs.models.delete_scheduled_instance_response import DeleteScheduledInstanceResponse
from volcenginesdkecs.models.delete_tags_request import DeleteTagsRequest
from volcenginesdkecs.models.delete_tags_response import DeleteTagsResponse
from volcenginesdkecs.models.deployment_set_for_describe_deployment_sets_output import DeploymentSetForDescribeDeploymentSetsOutput
from volcenginesdkecs.models.describe_available_resource_request import DescribeAvailableResourceRequest
from volcenginesdkecs.models.describe_available_resource_response import DescribeAvailableResourceResponse
from volcenginesdkecs.models.describe_cloud_assistant_status_request import DescribeCloudAssistantStatusRequest
from volcenginesdkecs.models.describe_cloud_assistant_status_response import DescribeCloudAssistantStatusResponse
from volcenginesdkecs.models.describe_commands_request import DescribeCommandsRequest
from volcenginesdkecs.models.describe_commands_response import DescribeCommandsResponse
from volcenginesdkecs.models.describe_dedicated_host_clusters_request import DescribeDedicatedHostClustersRequest
from volcenginesdkecs.models.describe_dedicated_host_clusters_response import DescribeDedicatedHostClustersResponse
from volcenginesdkecs.models.describe_dedicated_host_types_request import DescribeDedicatedHostTypesRequest
from volcenginesdkecs.models.describe_dedicated_host_types_response import DescribeDedicatedHostTypesResponse
from volcenginesdkecs.models.describe_dedicated_hosts_request import DescribeDedicatedHostsRequest
from volcenginesdkecs.models.describe_dedicated_hosts_response import DescribeDedicatedHostsResponse
from volcenginesdkecs.models.describe_deployment_set_supported_instance_type_family_request import DescribeDeploymentSetSupportedInstanceTypeFamilyRequest
from volcenginesdkecs.models.describe_deployment_set_supported_instance_type_family_response import DescribeDeploymentSetSupportedInstanceTypeFamilyResponse
from volcenginesdkecs.models.describe_deployment_sets_request import DescribeDeploymentSetsRequest
from volcenginesdkecs.models.describe_deployment_sets_response import DescribeDeploymentSetsResponse
from volcenginesdkecs.models.describe_event_types_request import DescribeEventTypesRequest
from volcenginesdkecs.models.describe_event_types_response import DescribeEventTypesResponse
from volcenginesdkecs.models.describe_hpc_clusters_request import DescribeHpcClustersRequest
from volcenginesdkecs.models.describe_hpc_clusters_response import DescribeHpcClustersResponse
from volcenginesdkecs.models.describe_image_share_permission_request import DescribeImageSharePermissionRequest
from volcenginesdkecs.models.describe_image_share_permission_response import DescribeImageSharePermissionResponse
from volcenginesdkecs.models.describe_images_request import DescribeImagesRequest
from volcenginesdkecs.models.describe_images_response import DescribeImagesResponse
from volcenginesdkecs.models.describe_instance_ecs_terminal_url_request import DescribeInstanceECSTerminalUrlRequest
from volcenginesdkecs.models.describe_instance_ecs_terminal_url_response import DescribeInstanceECSTerminalUrlResponse
from volcenginesdkecs.models.describe_instance_type_families_request import DescribeInstanceTypeFamiliesRequest
from volcenginesdkecs.models.describe_instance_type_families_response import DescribeInstanceTypeFamiliesResponse
from volcenginesdkecs.models.describe_instance_types_request import DescribeInstanceTypesRequest
from volcenginesdkecs.models.describe_instance_types_response import DescribeInstanceTypesResponse
from volcenginesdkecs.models.describe_instance_vnc_url_request import DescribeInstanceVncUrlRequest
from volcenginesdkecs.models.describe_instance_vnc_url_response import DescribeInstanceVncUrlResponse
from volcenginesdkecs.models.describe_instances_iam_roles_request import DescribeInstancesIamRolesRequest
from volcenginesdkecs.models.describe_instances_iam_roles_response import DescribeInstancesIamRolesResponse
from volcenginesdkecs.models.describe_instances_request import DescribeInstancesRequest
from volcenginesdkecs.models.describe_instances_response import DescribeInstancesResponse
from volcenginesdkecs.models.describe_invocation_instances_request import DescribeInvocationInstancesRequest
from volcenginesdkecs.models.describe_invocation_instances_response import DescribeInvocationInstancesResponse
from volcenginesdkecs.models.describe_invocation_results_request import DescribeInvocationResultsRequest
from volcenginesdkecs.models.describe_invocation_results_response import DescribeInvocationResultsResponse
from volcenginesdkecs.models.describe_invocations_request import DescribeInvocationsRequest
from volcenginesdkecs.models.describe_invocations_response import DescribeInvocationsResponse
from volcenginesdkecs.models.describe_key_pairs_request import DescribeKeyPairsRequest
from volcenginesdkecs.models.describe_key_pairs_response import DescribeKeyPairsResponse
from volcenginesdkecs.models.describe_regions_request import DescribeRegionsRequest
from volcenginesdkecs.models.describe_regions_response import DescribeRegionsResponse
from volcenginesdkecs.models.describe_reserved_instances_request import DescribeReservedInstancesRequest
from volcenginesdkecs.models.describe_reserved_instances_response import DescribeReservedInstancesResponse
from volcenginesdkecs.models.describe_scheduled_instance_stock_request import DescribeScheduledInstanceStockRequest
from volcenginesdkecs.models.describe_scheduled_instance_stock_response import DescribeScheduledInstanceStockResponse
from volcenginesdkecs.models.describe_scheduled_instances_request import DescribeScheduledInstancesRequest
from volcenginesdkecs.models.describe_scheduled_instances_response import DescribeScheduledInstancesResponse
from volcenginesdkecs.models.describe_spot_advice_request import DescribeSpotAdviceRequest
from volcenginesdkecs.models.describe_spot_advice_response import DescribeSpotAdviceResponse
from volcenginesdkecs.models.describe_spot_price_history_request import DescribeSpotPriceHistoryRequest
from volcenginesdkecs.models.describe_spot_price_history_response import DescribeSpotPriceHistoryResponse
from volcenginesdkecs.models.describe_subscriptions_request import DescribeSubscriptionsRequest
from volcenginesdkecs.models.describe_subscriptions_response import DescribeSubscriptionsResponse
from volcenginesdkecs.models.describe_system_events_request import DescribeSystemEventsRequest
from volcenginesdkecs.models.describe_system_events_response import DescribeSystemEventsResponse
from volcenginesdkecs.models.describe_tags_request import DescribeTagsRequest
from volcenginesdkecs.models.describe_tags_response import DescribeTagsResponse
from volcenginesdkecs.models.describe_tasks_request import DescribeTasksRequest
from volcenginesdkecs.models.describe_tasks_response import DescribeTasksResponse
from volcenginesdkecs.models.describe_user_data_request import DescribeUserDataRequest
from volcenginesdkecs.models.describe_user_data_response import DescribeUserDataResponse
from volcenginesdkecs.models.describe_zones_request import DescribeZonesRequest
from volcenginesdkecs.models.describe_zones_response import DescribeZonesResponse
from volcenginesdkecs.models.detach_key_pair_request import DetachKeyPairRequest
from volcenginesdkecs.models.detach_key_pair_response import DetachKeyPairResponse
from volcenginesdkecs.models.detect_image_request import DetectImageRequest
from volcenginesdkecs.models.detect_image_response import DetectImageResponse
from volcenginesdkecs.models.detection_results_for_describe_images_output import DetectionResultsForDescribeImagesOutput
from volcenginesdkecs.models.disassociate_instances_iam_role_request import DisassociateInstancesIamRoleRequest
from volcenginesdkecs.models.disassociate_instances_iam_role_response import DisassociateInstancesIamRoleResponse
from volcenginesdkecs.models.eip_address_for_create_scheduled_instances_input import EipAddressForCreateScheduledInstancesInput
from volcenginesdkecs.models.eip_address_for_describe_instances_output import EipAddressForDescribeInstancesOutput
from volcenginesdkecs.models.eip_address_for_describe_scheduled_instances_output import EipAddressForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.eip_address_for_run_instances_input import EipAddressForRunInstancesInput
from volcenginesdkecs.models.error_for_associate_instances_iam_role_output import ErrorForAssociateInstancesIamRoleOutput
from volcenginesdkecs.models.error_for_attach_key_pair_output import ErrorForAttachKeyPairOutput
from volcenginesdkecs.models.error_for_create_tags_output import ErrorForCreateTagsOutput
from volcenginesdkecs.models.error_for_delete_images_output import ErrorForDeleteImagesOutput
from volcenginesdkecs.models.error_for_delete_instances_output import ErrorForDeleteInstancesOutput
from volcenginesdkecs.models.error_for_delete_key_pairs_output import ErrorForDeleteKeyPairsOutput
from volcenginesdkecs.models.error_for_delete_tags_output import ErrorForDeleteTagsOutput
from volcenginesdkecs.models.error_for_detach_key_pair_output import ErrorForDetachKeyPairOutput
from volcenginesdkecs.models.error_for_disassociate_instances_iam_role_output import ErrorForDisassociateInstancesIamRoleOutput
from volcenginesdkecs.models.error_for_reboot_instances_output import ErrorForRebootInstancesOutput
from volcenginesdkecs.models.error_for_start_instances_output import ErrorForStartInstancesOutput
from volcenginesdkecs.models.error_for_stop_instances_output import ErrorForStopInstancesOutput
from volcenginesdkecs.models.error_for_update_system_events_output import ErrorForUpdateSystemEventsOutput
from volcenginesdkecs.models.event_type_for_describe_event_types_output import EventTypeForDescribeEventTypesOutput
from volcenginesdkecs.models.export_image_request import ExportImageRequest
from volcenginesdkecs.models.export_image_response import ExportImageResponse
from volcenginesdkecs.models.failed_instance_for_install_cloud_assistant_output import FailedInstanceForInstallCloudAssistantOutput
from volcenginesdkecs.models.failed_instance_for_uninstall_cloud_assistants_output import FailedInstanceForUninstallCloudAssistantsOutput
from volcenginesdkecs.models.failed_instance_for_upgrade_cloud_assistants_output import FailedInstanceForUpgradeCloudAssistantsOutput
from volcenginesdkecs.models.get_console_output_request import GetConsoleOutputRequest
from volcenginesdkecs.models.get_console_output_response import GetConsoleOutputResponse
from volcenginesdkecs.models.get_console_screenshot_request import GetConsoleScreenshotRequest
from volcenginesdkecs.models.get_console_screenshot_response import GetConsoleScreenshotResponse
from volcenginesdkecs.models.get_scheduled_instance_latest_release_at_request import GetScheduledInstanceLatestReleaseAtRequest
from volcenginesdkecs.models.get_scheduled_instance_latest_release_at_response import GetScheduledInstanceLatestReleaseAtResponse
from volcenginesdkecs.models.gpu_device_for_describe_instance_types_output import GpuDeviceForDescribeInstanceTypesOutput
from volcenginesdkecs.models.gpu_for_describe_instance_types_output import GpuForDescribeInstanceTypesOutput
from volcenginesdkecs.models.gpu_for_describe_spot_advice_input import GpuForDescribeSpotAdviceInput
from volcenginesdkecs.models.hpc_cluster_for_describe_hpc_clusters_output import HpcClusterForDescribeHpcClustersOutput
from volcenginesdkecs.models.image_for_describe_images_output import ImageForDescribeImagesOutput
from volcenginesdkecs.models.import_data_volume_for_import_image_input import ImportDataVolumeForImportImageInput
from volcenginesdkecs.models.import_image_request import ImportImageRequest
from volcenginesdkecs.models.import_image_response import ImportImageResponse
from volcenginesdkecs.models.import_key_pair_request import ImportKeyPairRequest
from volcenginesdkecs.models.import_key_pair_response import ImportKeyPairResponse
from volcenginesdkecs.models.install_cloud_assistant_request import InstallCloudAssistantRequest
from volcenginesdkecs.models.install_cloud_assistant_response import InstallCloudAssistantResponse
from volcenginesdkecs.models.instance_config_for_describe_scheduled_instances_output import InstanceConfigForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.instance_for_describe_cloud_assistant_status_output import InstanceForDescribeCloudAssistantStatusOutput
from volcenginesdkecs.models.instance_for_describe_dedicated_hosts_output import InstanceForDescribeDedicatedHostsOutput
from volcenginesdkecs.models.instance_for_describe_instances_output import InstanceForDescribeInstancesOutput
from volcenginesdkecs.models.instance_type_family_for_describe_instance_type_families_output import InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput
from volcenginesdkecs.models.instance_type_for_describe_instance_types_output import InstanceTypeForDescribeInstanceTypesOutput
from volcenginesdkecs.models.instances_iam_role_for_describe_instances_iam_roles_output import InstancesIamRoleForDescribeInstancesIamRolesOutput
from volcenginesdkecs.models.invocation_for_describe_invocations_output import InvocationForDescribeInvocationsOutput
from volcenginesdkecs.models.invocation_instance_for_describe_invocation_instances_output import InvocationInstanceForDescribeInvocationInstancesOutput
from volcenginesdkecs.models.invocation_result_for_describe_invocation_results_output import InvocationResultForDescribeInvocationResultsOutput
from volcenginesdkecs.models.invoke_command_request import InvokeCommandRequest
from volcenginesdkecs.models.invoke_command_response import InvokeCommandResponse
from volcenginesdkecs.models.item_for_describe_images_output import ItemForDescribeImagesOutput
from volcenginesdkecs.models.key_pair_for_describe_key_pairs_output import KeyPairForDescribeKeyPairsOutput
from volcenginesdkecs.models.list_tags_for_resources_request import ListTagsForResourcesRequest
from volcenginesdkecs.models.list_tags_for_resources_response import ListTagsForResourcesResponse
from volcenginesdkecs.models.local_volume_capacity_for_describe_dedicated_host_clusters_output import LocalVolumeCapacityForDescribeDedicatedHostClustersOutput
from volcenginesdkecs.models.local_volume_capacity_for_describe_dedicated_hosts_output import LocalVolumeCapacityForDescribeDedicatedHostsOutput
from volcenginesdkecs.models.local_volume_for_describe_dedicated_host_types_output import LocalVolumeForDescribeDedicatedHostTypesOutput
from volcenginesdkecs.models.local_volume_for_describe_instance_types_output import LocalVolumeForDescribeInstanceTypesOutput
from volcenginesdkecs.models.local_volume_for_describe_instances_output import LocalVolumeForDescribeInstancesOutput
from volcenginesdkecs.models.memory_for_describe_instance_types_output import MemoryForDescribeInstanceTypesOutput
from volcenginesdkecs.models.modify_command_request import ModifyCommandRequest
from volcenginesdkecs.models.modify_command_response import ModifyCommandResponse
from volcenginesdkecs.models.modify_dedicated_host_attribute_request import ModifyDedicatedHostAttributeRequest
from volcenginesdkecs.models.modify_dedicated_host_attribute_response import ModifyDedicatedHostAttributeResponse
from volcenginesdkecs.models.modify_dedicated_host_cluster_attribute_request import ModifyDedicatedHostClusterAttributeRequest
from volcenginesdkecs.models.modify_dedicated_host_cluster_attribute_response import ModifyDedicatedHostClusterAttributeResponse
from volcenginesdkecs.models.modify_deployment_set_attribute_request import ModifyDeploymentSetAttributeRequest
from volcenginesdkecs.models.modify_deployment_set_attribute_response import ModifyDeploymentSetAttributeResponse
from volcenginesdkecs.models.modify_image_attribute_request import ModifyImageAttributeRequest
from volcenginesdkecs.models.modify_image_attribute_response import ModifyImageAttributeResponse
from volcenginesdkecs.models.modify_image_share_permission_request import ModifyImageSharePermissionRequest
from volcenginesdkecs.models.modify_image_share_permission_response import ModifyImageSharePermissionResponse
from volcenginesdkecs.models.modify_instance_attribute_request import ModifyInstanceAttributeRequest
from volcenginesdkecs.models.modify_instance_attribute_response import ModifyInstanceAttributeResponse
from volcenginesdkecs.models.modify_instance_charge_type_request import ModifyInstanceChargeTypeRequest
from volcenginesdkecs.models.modify_instance_charge_type_response import ModifyInstanceChargeTypeResponse
from volcenginesdkecs.models.modify_instance_deployment_request import ModifyInstanceDeploymentRequest
from volcenginesdkecs.models.modify_instance_deployment_response import ModifyInstanceDeploymentResponse
from volcenginesdkecs.models.modify_instance_placement_request import ModifyInstancePlacementRequest
from volcenginesdkecs.models.modify_instance_placement_response import ModifyInstancePlacementResponse
from volcenginesdkecs.models.modify_instance_spec_request import ModifyInstanceSpecRequest
from volcenginesdkecs.models.modify_instance_spec_response import ModifyInstanceSpecResponse
from volcenginesdkecs.models.modify_instance_vpc_attribute_request import ModifyInstanceVpcAttributeRequest
from volcenginesdkecs.models.modify_instance_vpc_attribute_response import ModifyInstanceVpcAttributeResponse
from volcenginesdkecs.models.modify_key_pair_attribute_request import ModifyKeyPairAttributeRequest
from volcenginesdkecs.models.modify_key_pair_attribute_response import ModifyKeyPairAttributeResponse
from volcenginesdkecs.models.modify_reserved_instances_request import ModifyReservedInstancesRequest
from volcenginesdkecs.models.modify_reserved_instances_response import ModifyReservedInstancesResponse
from volcenginesdkecs.models.modify_subscription_event_types_request import ModifySubscriptionEventTypesRequest
from volcenginesdkecs.models.modify_subscription_event_types_response import ModifySubscriptionEventTypesResponse
from volcenginesdkecs.models.network_for_describe_instance_types_output import NetworkForDescribeInstanceTypesOutput
from volcenginesdkecs.models.network_interface_for_create_scheduled_instances_input import NetworkInterfaceForCreateScheduledInstancesInput
from volcenginesdkecs.models.network_interface_for_describe_instances_output import NetworkInterfaceForDescribeInstancesOutput
from volcenginesdkecs.models.network_interface_for_run_instances_input import NetworkInterfaceForRunInstancesInput
from volcenginesdkecs.models.network_interfaces_re_for_describe_scheduled_instances_output import NetworkInterfacesReForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.operation_detail_for_associate_instances_iam_role_output import OperationDetailForAssociateInstancesIamRoleOutput
from volcenginesdkecs.models.operation_detail_for_attach_key_pair_output import OperationDetailForAttachKeyPairOutput
from volcenginesdkecs.models.operation_detail_for_create_tags_output import OperationDetailForCreateTagsOutput
from volcenginesdkecs.models.operation_detail_for_delete_images_output import OperationDetailForDeleteImagesOutput
from volcenginesdkecs.models.operation_detail_for_delete_instances_output import OperationDetailForDeleteInstancesOutput
from volcenginesdkecs.models.operation_detail_for_delete_key_pairs_output import OperationDetailForDeleteKeyPairsOutput
from volcenginesdkecs.models.operation_detail_for_delete_tags_output import OperationDetailForDeleteTagsOutput
from volcenginesdkecs.models.operation_detail_for_detach_key_pair_output import OperationDetailForDetachKeyPairOutput
from volcenginesdkecs.models.operation_detail_for_disassociate_instances_iam_role_output import OperationDetailForDisassociateInstancesIamRoleOutput
from volcenginesdkecs.models.operation_detail_for_reboot_instances_output import OperationDetailForRebootInstancesOutput
from volcenginesdkecs.models.operation_detail_for_start_instances_output import OperationDetailForStartInstancesOutput
from volcenginesdkecs.models.operation_detail_for_stop_instances_output import OperationDetailForStopInstancesOutput
from volcenginesdkecs.models.operation_detail_for_update_system_events_output import OperationDetailForUpdateSystemEventsOutput
from volcenginesdkecs.models.parameter_definition_for_create_command_input import ParameterDefinitionForCreateCommandInput
from volcenginesdkecs.models.parameter_definition_for_describe_commands_output import ParameterDefinitionForDescribeCommandsOutput
from volcenginesdkecs.models.parameter_definition_for_describe_invocations_output import ParameterDefinitionForDescribeInvocationsOutput
from volcenginesdkecs.models.parameter_definition_for_modify_command_input import ParameterDefinitionForModifyCommandInput
from volcenginesdkecs.models.parameter_definition_for_run_command_input import ParameterDefinitionForRunCommandInput
from volcenginesdkecs.models.placement_for_describe_instances_output import PlacementForDescribeInstancesOutput
from volcenginesdkecs.models.placement_for_run_instances_input import PlacementForRunInstancesInput
from volcenginesdkecs.models.processor_for_describe_instance_types_output import ProcessorForDescribeInstanceTypesOutput
from volcenginesdkecs.models.purchase_reserved_instances_request import PurchaseReservedInstancesRequest
from volcenginesdkecs.models.purchase_reserved_instances_response import PurchaseReservedInstancesResponse
from volcenginesdkecs.models.rdma_for_describe_instance_types_output import RdmaForDescribeInstanceTypesOutput
from volcenginesdkecs.models.reboot_instance_request import RebootInstanceRequest
from volcenginesdkecs.models.reboot_instance_response import RebootInstanceResponse
from volcenginesdkecs.models.reboot_instances_request import RebootInstancesRequest
from volcenginesdkecs.models.reboot_instances_response import RebootInstancesResponse
from volcenginesdkecs.models.redeploy_dedicated_host_request import RedeployDedicatedHostRequest
from volcenginesdkecs.models.redeploy_dedicated_host_response import RedeployDedicatedHostResponse
from volcenginesdkecs.models.region_for_describe_regions_output import RegionForDescribeRegionsOutput
from volcenginesdkecs.models.renew_dedicated_host_request import RenewDedicatedHostRequest
from volcenginesdkecs.models.renew_dedicated_host_response import RenewDedicatedHostResponse
from volcenginesdkecs.models.renew_instance_request import RenewInstanceRequest
from volcenginesdkecs.models.renew_instance_response import RenewInstanceResponse
from volcenginesdkecs.models.repair_image_request import RepairImageRequest
from volcenginesdkecs.models.repair_image_response import RepairImageResponse
from volcenginesdkecs.models.replace_system_volume_request import ReplaceSystemVolumeRequest
from volcenginesdkecs.models.replace_system_volume_response import ReplaceSystemVolumeResponse
from volcenginesdkecs.models.reserved_instance_for_describe_reserved_instances_output import ReservedInstanceForDescribeReservedInstancesOutput
from volcenginesdkecs.models.run_command_request import RunCommandRequest
from volcenginesdkecs.models.run_command_response import RunCommandResponse
from volcenginesdkecs.models.run_instances_request import RunInstancesRequest
from volcenginesdkecs.models.run_instances_response import RunInstancesResponse
from volcenginesdkecs.models.scheduled_instance_info_for_describe_scheduled_instances_output import ScheduledInstanceInfoForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.scheduled_instance_stock_for_describe_scheduled_instance_stock_output import ScheduledInstanceStockForDescribeScheduledInstanceStockOutput
from volcenginesdkecs.models.snapshot_for_describe_images_output import SnapshotForDescribeImagesOutput
from volcenginesdkecs.models.spot_price_for_describe_spot_price_history_output import SpotPriceForDescribeSpotPriceHistoryOutput
from volcenginesdkecs.models.start_instance_request import StartInstanceRequest
from volcenginesdkecs.models.start_instance_response import StartInstanceResponse
from volcenginesdkecs.models.start_instances_request import StartInstancesRequest
from volcenginesdkecs.models.start_instances_response import StartInstancesResponse
from volcenginesdkecs.models.stop_instance_request import StopInstanceRequest
from volcenginesdkecs.models.stop_instance_response import StopInstanceResponse
from volcenginesdkecs.models.stop_instances_request import StopInstancesRequest
from volcenginesdkecs.models.stop_instances_response import StopInstancesResponse
from volcenginesdkecs.models.stop_invocation_request import StopInvocationRequest
from volcenginesdkecs.models.stop_invocation_response import StopInvocationResponse
from volcenginesdkecs.models.subscription_for_describe_subscriptions_output import SubscriptionForDescribeSubscriptionsOutput
from volcenginesdkecs.models.supported_resource_for_describe_available_resource_output import SupportedResourceForDescribeAvailableResourceOutput
from volcenginesdkecs.models.system_event_for_describe_system_events_output import SystemEventForDescribeSystemEventsOutput
from volcenginesdkecs.models.tag_filter_for_describe_commands_input import TagFilterForDescribeCommandsInput
from volcenginesdkecs.models.tag_filter_for_describe_images_input import TagFilterForDescribeImagesInput
from volcenginesdkecs.models.tag_filter_for_describe_instances_input import TagFilterForDescribeInstancesInput
from volcenginesdkecs.models.tag_filter_for_describe_invocations_input import TagFilterForDescribeInvocationsInput
from volcenginesdkecs.models.tag_filter_for_describe_key_pairs_input import TagFilterForDescribeKeyPairsInput
from volcenginesdkecs.models.tag_filter_for_describe_reserved_instances_input import TagFilterForDescribeReservedInstancesInput
from volcenginesdkecs.models.tag_filter_for_describe_scheduled_instances_input import TagFilterForDescribeScheduledInstancesInput
from volcenginesdkecs.models.tag_filter_for_describe_tags_input import TagFilterForDescribeTagsInput
from volcenginesdkecs.models.tag_filter_for_list_tags_for_resources_input import TagFilterForListTagsForResourcesInput
from volcenginesdkecs.models.tag_for_create_command_input import TagForCreateCommandInput
from volcenginesdkecs.models.tag_for_create_image_input import TagForCreateImageInput
from volcenginesdkecs.models.tag_for_create_key_pair_input import TagForCreateKeyPairInput
from volcenginesdkecs.models.tag_for_create_scheduled_instances_input import TagForCreateScheduledInstancesInput
from volcenginesdkecs.models.tag_for_create_tags_input import TagForCreateTagsInput
from volcenginesdkecs.models.tag_for_describe_commands_output import TagForDescribeCommandsOutput
from volcenginesdkecs.models.tag_for_describe_images_output import TagForDescribeImagesOutput
from volcenginesdkecs.models.tag_for_describe_instances_output import TagForDescribeInstancesOutput
from volcenginesdkecs.models.tag_for_describe_invocations_output import TagForDescribeInvocationsOutput
from volcenginesdkecs.models.tag_for_describe_key_pairs_output import TagForDescribeKeyPairsOutput
from volcenginesdkecs.models.tag_for_describe_reserved_instances_output import TagForDescribeReservedInstancesOutput
from volcenginesdkecs.models.tag_for_describe_scheduled_instances_output import TagForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.tag_for_import_image_input import TagForImportImageInput
from volcenginesdkecs.models.tag_for_import_key_pair_input import TagForImportKeyPairInput
from volcenginesdkecs.models.tag_for_invoke_command_input import TagForInvokeCommandInput
from volcenginesdkecs.models.tag_for_modify_reserved_instances_input import TagForModifyReservedInstancesInput
from volcenginesdkecs.models.tag_for_purchase_reserved_instances_input import TagForPurchaseReservedInstancesInput
from volcenginesdkecs.models.tag_for_run_command_input import TagForRunCommandInput
from volcenginesdkecs.models.tag_for_run_instances_input import TagForRunInstancesInput
from volcenginesdkecs.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkecs.models.tag_resource_for_describe_tags_output import TagResourceForDescribeTagsOutput
from volcenginesdkecs.models.tag_resource_for_list_tags_for_resources_output import TagResourceForListTagsForResourcesOutput
from volcenginesdkecs.models.tag_resources_request import TagResourcesRequest
from volcenginesdkecs.models.tag_resources_response import TagResourcesResponse
from volcenginesdkecs.models.task_for_describe_tasks_output import TaskForDescribeTasksOutput
from volcenginesdkecs.models.uninstall_cloud_assistants_request import UninstallCloudAssistantsRequest
from volcenginesdkecs.models.uninstall_cloud_assistants_response import UninstallCloudAssistantsResponse
from volcenginesdkecs.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkecs.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkecs.models.update_system_events_request import UpdateSystemEventsRequest
from volcenginesdkecs.models.update_system_events_response import UpdateSystemEventsResponse
from volcenginesdkecs.models.upgrade_cloud_assistants_request import UpgradeCloudAssistantsRequest
from volcenginesdkecs.models.upgrade_cloud_assistants_response import UpgradeCloudAssistantsResponse
from volcenginesdkecs.models.volume_for_create_scheduled_instances_input import VolumeForCreateScheduledInstancesInput
from volcenginesdkecs.models.volume_for_describe_instance_types_output import VolumeForDescribeInstanceTypesOutput
from volcenginesdkecs.models.volume_for_describe_instances_output import VolumeForDescribeInstancesOutput
from volcenginesdkecs.models.volume_for_describe_scheduled_instances_output import VolumeForDescribeScheduledInstancesOutput
from volcenginesdkecs.models.volume_for_run_instances_input import VolumeForRunInstancesInput
from volcenginesdkecs.models.zone_for_describe_zones_output import ZoneForDescribeZonesOutput
