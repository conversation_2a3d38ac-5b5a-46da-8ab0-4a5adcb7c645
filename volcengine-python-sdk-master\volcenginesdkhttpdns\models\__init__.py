# coding: utf-8

# flake8: noqa
"""
    httpdns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkhttpdns.models.aaaa_list_for_list_domain_records_output import AAAAListForListDomainRecordsOutput
from volcenginesdkhttpdns.models.a_list_for_list_domain_records_output import AListForListDomainRecordsOutput
from volcenginesdkhttpdns.models.add_domain_record_request import AddDomainRecordRequest
from volcenginesdkhttpdns.models.add_domain_record_response import AddDomainRecordResponse
from volcenginesdkhttpdns.models.add_domain_request import AddDomainRequest
from volcenginesdkhttpdns.models.add_domain_response import AddDomainResponse
from volcenginesdkhttpdns.models.cname_list_for_list_domain_records_output import CnameListForListDomainRecordsOutput
from volcenginesdkhttpdns.models.delete_domain_record_request import DeleteDomainRecordRequest
from volcenginesdkhttpdns.models.delete_domain_record_response import DeleteDomainRecordResponse
from volcenginesdkhttpdns.models.delete_domain_request import DeleteDomainRequest
from volcenginesdkhttpdns.models.delete_domain_response import DeleteDomainResponse
from volcenginesdkhttpdns.models.domain_list_for_list_domain_overview_output import DomainListForListDomainOverviewOutput
from volcenginesdkhttpdns.models.line_for_list_lines_output import LineForListLinesOutput
from volcenginesdkhttpdns.models.list_domain_overview_request import ListDomainOverviewRequest
from volcenginesdkhttpdns.models.list_domain_overview_response import ListDomainOverviewResponse
from volcenginesdkhttpdns.models.list_domain_records_request import ListDomainRecordsRequest
from volcenginesdkhttpdns.models.list_domain_records_response import ListDomainRecordsResponse
from volcenginesdkhttpdns.models.list_lines_request import ListLinesRequest
from volcenginesdkhttpdns.models.list_lines_response import ListLinesResponse
from volcenginesdkhttpdns.models.record_for_list_domain_records_output import RecordForListDomainRecordsOutput
from volcenginesdkhttpdns.models.update_domain_record_request import UpdateDomainRecordRequest
from volcenginesdkhttpdns.models.update_domain_record_response import UpdateDomainRecordResponse
from volcenginesdkhttpdns.models.weight_for_add_domain_record_input import WeightForAddDomainRecordInput
from volcenginesdkhttpdns.models.weight_for_add_domain_record_output import WeightForAddDomainRecordOutput
from volcenginesdkhttpdns.models.weight_for_delete_domain_record_output import WeightForDeleteDomainRecordOutput
from volcenginesdkhttpdns.models.weight_for_list_domain_records_output import WeightForListDomainRecordsOutput
from volcenginesdkhttpdns.models.weight_for_update_domain_record_input import WeightForUpdateDomainRecordInput
from volcenginesdkhttpdns.models.weight_for_update_domain_record_output import WeightForUpdateDomainRecordOutput
