# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDXPTrafficStatisticResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        '_in': 'list[InForGetDXPTrafficStatisticOutput]',
        'out': 'list[OutForGetDXPTrafficStatisticOutput]'
    }

    attribute_map = {
        '_in': 'In',
        'out': 'Out'
    }

    def __init__(self, _in=None, out=None, _configuration=None):  # noqa: E501
        """GetDXPTrafficStatisticResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self.__in = None
        self._out = None
        self.discriminator = None

        if _in is not None:
            self._in = _in
        if out is not None:
            self.out = out

    @property
    def _in(self):
        """Gets the _in of this GetDXPTrafficStatisticResponse.  # noqa: E501


        :return: The _in of this GetDXPTrafficStatisticResponse.  # noqa: E501
        :rtype: list[InForGetDXPTrafficStatisticOutput]
        """
        return self.__in

    @_in.setter
    def _in(self, _in):
        """Sets the _in of this GetDXPTrafficStatisticResponse.


        :param _in: The _in of this GetDXPTrafficStatisticResponse.  # noqa: E501
        :type: list[InForGetDXPTrafficStatisticOutput]
        """

        self.__in = _in

    @property
    def out(self):
        """Gets the out of this GetDXPTrafficStatisticResponse.  # noqa: E501


        :return: The out of this GetDXPTrafficStatisticResponse.  # noqa: E501
        :rtype: list[OutForGetDXPTrafficStatisticOutput]
        """
        return self._out

    @out.setter
    def out(self, out):
        """Sets the out of this GetDXPTrafficStatisticResponse.


        :param out: The out of this GetDXPTrafficStatisticResponse.  # noqa: E501
        :type: list[OutForGetDXPTrafficStatisticOutput]
        """

        self._out = out

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDXPTrafficStatisticResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDXPTrafficStatisticResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDXPTrafficStatisticResponse):
            return True

        return self.to_dict() != other.to_dict()
