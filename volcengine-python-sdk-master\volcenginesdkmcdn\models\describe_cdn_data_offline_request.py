# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnDataOfflineRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cdn_types': 'list[str]',
        'cloud_account_ids': 'list[str]',
        'domain_ids': 'list[str]',
        'domains': 'list[str]',
        'end_time': 'int',
        'group_by': 'str',
        'interval': 'str',
        'metric': 'str',
        'start_time': 'int',
        'sub_products': 'list[str]',
        'vendors': 'list[str]'
    }

    attribute_map = {
        'cdn_types': 'CdnTypes',
        'cloud_account_ids': 'CloudAccountIds',
        'domain_ids': 'DomainIds',
        'domains': 'Domains',
        'end_time': 'EndTime',
        'group_by': 'GroupBy',
        'interval': 'Interval',
        'metric': 'Metric',
        'start_time': 'StartTime',
        'sub_products': 'SubProducts',
        'vendors': 'Vendors'
    }

    def __init__(self, cdn_types=None, cloud_account_ids=None, domain_ids=None, domains=None, end_time=None, group_by=None, interval=None, metric=None, start_time=None, sub_products=None, vendors=None, _configuration=None):  # noqa: E501
        """DescribeCdnDataOfflineRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cdn_types = None
        self._cloud_account_ids = None
        self._domain_ids = None
        self._domains = None
        self._end_time = None
        self._group_by = None
        self._interval = None
        self._metric = None
        self._start_time = None
        self._sub_products = None
        self._vendors = None
        self.discriminator = None

        if cdn_types is not None:
            self.cdn_types = cdn_types
        if cloud_account_ids is not None:
            self.cloud_account_ids = cloud_account_ids
        if domain_ids is not None:
            self.domain_ids = domain_ids
        if domains is not None:
            self.domains = domains
        self.end_time = end_time
        if group_by is not None:
            self.group_by = group_by
        self.interval = interval
        self.metric = metric
        self.start_time = start_time
        if sub_products is not None:
            self.sub_products = sub_products
        if vendors is not None:
            self.vendors = vendors

    @property
    def cdn_types(self):
        """Gets the cdn_types of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The cdn_types of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cdn_types

    @cdn_types.setter
    def cdn_types(self, cdn_types):
        """Sets the cdn_types of this DescribeCdnDataOfflineRequest.


        :param cdn_types: The cdn_types of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._cdn_types = cdn_types

    @property
    def cloud_account_ids(self):
        """Gets the cloud_account_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The cloud_account_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_account_ids

    @cloud_account_ids.setter
    def cloud_account_ids(self, cloud_account_ids):
        """Sets the cloud_account_ids of this DescribeCdnDataOfflineRequest.


        :param cloud_account_ids: The cloud_account_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_account_ids = cloud_account_ids

    @property
    def domain_ids(self):
        """Gets the domain_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The domain_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._domain_ids

    @domain_ids.setter
    def domain_ids(self, domain_ids):
        """Sets the domain_ids of this DescribeCdnDataOfflineRequest.


        :param domain_ids: The domain_ids of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._domain_ids = domain_ids

    @property
    def domains(self):
        """Gets the domains of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The domains of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._domains

    @domains.setter
    def domains(self, domains):
        """Sets the domains of this DescribeCdnDataOfflineRequest.


        :param domains: The domains of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._domains = domains

    @property
    def end_time(self):
        """Gets the end_time of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The end_time of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeCdnDataOfflineRequest.


        :param end_time: The end_time of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def group_by(self):
        """Gets the group_by of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The group_by of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: str
        """
        return self._group_by

    @group_by.setter
    def group_by(self, group_by):
        """Sets the group_by of this DescribeCdnDataOfflineRequest.


        :param group_by: The group_by of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: str
        """

        self._group_by = group_by

    @property
    def interval(self):
        """Gets the interval of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The interval of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: str
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this DescribeCdnDataOfflineRequest.


        :param interval: The interval of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and interval is None:
            raise ValueError("Invalid value for `interval`, must not be `None`")  # noqa: E501

        self._interval = interval

    @property
    def metric(self):
        """Gets the metric of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The metric of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric

    @metric.setter
    def metric(self, metric):
        """Sets the metric of this DescribeCdnDataOfflineRequest.


        :param metric: The metric of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric is None:
            raise ValueError("Invalid value for `metric`, must not be `None`")  # noqa: E501

        self._metric = metric

    @property
    def start_time(self):
        """Gets the start_time of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The start_time of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeCdnDataOfflineRequest.


        :param start_time: The start_time of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def sub_products(self):
        """Gets the sub_products of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The sub_products of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._sub_products

    @sub_products.setter
    def sub_products(self, sub_products):
        """Sets the sub_products of this DescribeCdnDataOfflineRequest.


        :param sub_products: The sub_products of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._sub_products = sub_products

    @property
    def vendors(self):
        """Gets the vendors of this DescribeCdnDataOfflineRequest.  # noqa: E501


        :return: The vendors of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vendors

    @vendors.setter
    def vendors(self, vendors):
        """Sets the vendors of this DescribeCdnDataOfflineRequest.


        :param vendors: The vendors of this DescribeCdnDataOfflineRequest.  # noqa: E501
        :type: list[str]
        """

        self._vendors = vendors

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnDataOfflineRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnDataOfflineRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnDataOfflineRequest):
            return True

        return self.to_dict() != other.to_dict()
