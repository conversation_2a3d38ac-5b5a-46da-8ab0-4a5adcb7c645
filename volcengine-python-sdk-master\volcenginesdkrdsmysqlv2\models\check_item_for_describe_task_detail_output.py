# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckItemForDescribeTaskDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'check_detail': 'CheckDetailForDescribeTaskDetailOutput',
        'description': 'str',
        'item_name': 'str',
        'risk_level': 'str'
    }

    attribute_map = {
        'check_detail': 'CheckDetail',
        'description': 'Description',
        'item_name': 'ItemName',
        'risk_level': 'RiskLevel'
    }

    def __init__(self, check_detail=None, description=None, item_name=None, risk_level=None, _configuration=None):  # noqa: E501
        """CheckItemForDescribeTaskDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._check_detail = None
        self._description = None
        self._item_name = None
        self._risk_level = None
        self.discriminator = None

        if check_detail is not None:
            self.check_detail = check_detail
        if description is not None:
            self.description = description
        if item_name is not None:
            self.item_name = item_name
        if risk_level is not None:
            self.risk_level = risk_level

    @property
    def check_detail(self):
        """Gets the check_detail of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501


        :return: The check_detail of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: CheckDetailForDescribeTaskDetailOutput
        """
        return self._check_detail

    @check_detail.setter
    def check_detail(self, check_detail):
        """Sets the check_detail of this CheckItemForDescribeTaskDetailOutput.


        :param check_detail: The check_detail of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :type: CheckDetailForDescribeTaskDetailOutput
        """

        self._check_detail = check_detail

    @property
    def description(self):
        """Gets the description of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501


        :return: The description of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CheckItemForDescribeTaskDetailOutput.


        :param description: The description of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def item_name(self):
        """Gets the item_name of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501


        :return: The item_name of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._item_name

    @item_name.setter
    def item_name(self, item_name):
        """Sets the item_name of this CheckItemForDescribeTaskDetailOutput.


        :param item_name: The item_name of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :type: str
        """

        self._item_name = item_name

    @property
    def risk_level(self):
        """Gets the risk_level of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501


        :return: The risk_level of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_level

    @risk_level.setter
    def risk_level(self, risk_level):
        """Sets the risk_level of this CheckItemForDescribeTaskDetailOutput.


        :param risk_level: The risk_level of this CheckItemForDescribeTaskDetailOutput.  # noqa: E501
        :type: str
        """

        self._risk_level = risk_level

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckItemForDescribeTaskDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckItemForDescribeTaskDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckItemForDescribeTaskDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
