# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CapacityForDescribeDeploymentSetsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_count': 'int',
        'used_count': 'int',
        'zone_id': 'str'
    }

    attribute_map = {
        'available_count': 'AvailableCount',
        'used_count': 'UsedCount',
        'zone_id': 'ZoneId'
    }

    def __init__(self, available_count=None, used_count=None, zone_id=None, _configuration=None):  # noqa: E501
        """CapacityForDescribeDeploymentSetsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_count = None
        self._used_count = None
        self._zone_id = None
        self.discriminator = None

        if available_count is not None:
            self.available_count = available_count
        if used_count is not None:
            self.used_count = used_count
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def available_count(self):
        """Gets the available_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501


        :return: The available_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._available_count

    @available_count.setter
    def available_count(self, available_count):
        """Sets the available_count of this CapacityForDescribeDeploymentSetsOutput.


        :param available_count: The available_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :type: int
        """

        self._available_count = available_count

    @property
    def used_count(self):
        """Gets the used_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501


        :return: The used_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._used_count

    @used_count.setter
    def used_count(self, used_count):
        """Sets the used_count of this CapacityForDescribeDeploymentSetsOutput.


        :param used_count: The used_count of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :type: int
        """

        self._used_count = used_count

    @property
    def zone_id(self):
        """Gets the zone_id of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501


        :return: The zone_id of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this CapacityForDescribeDeploymentSetsOutput.


        :param zone_id: The zone_id of this CapacityForDescribeDeploymentSetsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CapacityForDescribeDeploymentSetsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CapacityForDescribeDeploymentSetsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CapacityForDescribeDeploymentSetsOutput):
            return True

        return self.to_dict() != other.to_dict()
