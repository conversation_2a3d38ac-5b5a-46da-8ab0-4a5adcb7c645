# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'status_overview': 'StatusOverviewForPostApiV1AlarmDescribeOverviewOutput',
        'unhandled': 'UnhandledForPostApiV1AlarmDescribeOverviewOutput'
    }

    attribute_map = {
        'status_overview': 'status_overview',
        'unhandled': 'unhandled'
    }

    def __init__(self, status_overview=None, unhandled=None, _configuration=None):  # noqa: E501
        """AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._status_overview = None
        self._unhandled = None
        self.discriminator = None

        if status_overview is not None:
            self.status_overview = status_overview
        if unhandled is not None:
            self.unhandled = unhandled

    @property
    def status_overview(self):
        """Gets the status_overview of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501


        :return: The status_overview of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501
        :rtype: StatusOverviewForPostApiV1AlarmDescribeOverviewOutput
        """
        return self._status_overview

    @status_overview.setter
    def status_overview(self, status_overview):
        """Sets the status_overview of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.


        :param status_overview: The status_overview of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501
        :type: StatusOverviewForPostApiV1AlarmDescribeOverviewOutput
        """

        self._status_overview = status_overview

    @property
    def unhandled(self):
        """Gets the unhandled of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501


        :return: The unhandled of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501
        :rtype: UnhandledForPostApiV1AlarmDescribeOverviewOutput
        """
        return self._unhandled

    @unhandled.setter
    def unhandled(self, unhandled):
        """Sets the unhandled of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.


        :param unhandled: The unhandled of this AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput.  # noqa: E501
        :type: UnhandledForPostApiV1AlarmDescribeOverviewOutput
        """

        self._unhandled = unhandled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmStatOverviewForPostApiV1AlarmDescribeOverviewOutput):
            return True

        return self.to_dict() != other.to_dict()
