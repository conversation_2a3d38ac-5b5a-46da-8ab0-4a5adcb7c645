# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdatePluginBindingRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'enable': 'bool',
        'id': 'str',
        'plugin_config': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'enable': 'Enable',
        'id': 'Id',
        'plugin_config': 'PluginConfig'
    }

    def __init__(self, description=None, enable=None, id=None, plugin_config=None, _configuration=None):  # noqa: E501
        """UpdatePluginBindingRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._enable = None
        self._id = None
        self._plugin_config = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.enable = enable
        self.id = id
        self.plugin_config = plugin_config

    @property
    def description(self):
        """Gets the description of this UpdatePluginBindingRequest.  # noqa: E501


        :return: The description of this UpdatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdatePluginBindingRequest.


        :param description: The description of this UpdatePluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable(self):
        """Gets the enable of this UpdatePluginBindingRequest.  # noqa: E501


        :return: The enable of this UpdatePluginBindingRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this UpdatePluginBindingRequest.


        :param enable: The enable of this UpdatePluginBindingRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable is None:
            raise ValueError("Invalid value for `enable`, must not be `None`")  # noqa: E501

        self._enable = enable

    @property
    def id(self):
        """Gets the id of this UpdatePluginBindingRequest.  # noqa: E501


        :return: The id of this UpdatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdatePluginBindingRequest.


        :param id: The id of this UpdatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def plugin_config(self):
        """Gets the plugin_config of this UpdatePluginBindingRequest.  # noqa: E501


        :return: The plugin_config of this UpdatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._plugin_config

    @plugin_config.setter
    def plugin_config(self, plugin_config):
        """Sets the plugin_config of this UpdatePluginBindingRequest.


        :param plugin_config: The plugin_config of this UpdatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plugin_config is None:
            raise ValueError("Invalid value for `plugin_config`, must not be `None`")  # noqa: E501

        self._plugin_config = plugin_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdatePluginBindingRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdatePluginBindingRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdatePluginBindingRequest):
            return True

        return self.to_dict() != other.to_dict()
