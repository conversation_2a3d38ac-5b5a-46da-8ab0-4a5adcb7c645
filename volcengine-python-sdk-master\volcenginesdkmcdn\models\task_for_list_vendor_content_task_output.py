# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskForListVendorContentTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'int',
        'description': 'str',
        'status': 'str',
        'url': 'str',
        'vendor_task_id': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'description': 'Description',
        'status': 'Status',
        'url': 'Url',
        'vendor_task_id': 'VendorTaskId'
    }

    def __init__(self, created_at=None, description=None, status=None, url=None, vendor_task_id=None, _configuration=None):  # noqa: E501
        """TaskForListVendorContentTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._description = None
        self._status = None
        self._url = None
        self._vendor_task_id = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if status is not None:
            self.status = status
        if url is not None:
            self.url = url
        if vendor_task_id is not None:
            self.vendor_task_id = vendor_task_id

    @property
    def created_at(self):
        """Gets the created_at of this TaskForListVendorContentTaskOutput.  # noqa: E501


        :return: The created_at of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this TaskForListVendorContentTaskOutput.


        :param created_at: The created_at of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this TaskForListVendorContentTaskOutput.  # noqa: E501


        :return: The description of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TaskForListVendorContentTaskOutput.


        :param description: The description of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def status(self):
        """Gets the status of this TaskForListVendorContentTaskOutput.  # noqa: E501


        :return: The status of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TaskForListVendorContentTaskOutput.


        :param status: The status of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def url(self):
        """Gets the url of this TaskForListVendorContentTaskOutput.  # noqa: E501


        :return: The url of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this TaskForListVendorContentTaskOutput.


        :param url: The url of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    @property
    def vendor_task_id(self):
        """Gets the vendor_task_id of this TaskForListVendorContentTaskOutput.  # noqa: E501


        :return: The vendor_task_id of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._vendor_task_id

    @vendor_task_id.setter
    def vendor_task_id(self, vendor_task_id):
        """Sets the vendor_task_id of this TaskForListVendorContentTaskOutput.


        :param vendor_task_id: The vendor_task_id of this TaskForListVendorContentTaskOutput.  # noqa: E501
        :type: str
        """

        self._vendor_task_id = vendor_task_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskForListVendorContentTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskForListVendorContentTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskForListVendorContentTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
