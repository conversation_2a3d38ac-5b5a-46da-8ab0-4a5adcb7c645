# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetStreamRecordResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'int',
        'id': 'str',
        'msg': 'str',
        'res': 'ResForGetStreamRecordOutput',
        'status': 'str',
        'stopped_at': 'int',
        'updated_at': 'int'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'id': 'ID',
        'msg': 'Msg',
        'res': 'Res',
        'status': 'Status',
        'stopped_at': 'StoppedAt',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, created_at=None, id=None, msg=None, res=None, status=None, stopped_at=None, updated_at=None, _configuration=None):  # noqa: E501
        """GetStreamRecordResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._id = None
        self._msg = None
        self._res = None
        self._status = None
        self._stopped_at = None
        self._updated_at = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if id is not None:
            self.id = id
        if msg is not None:
            self.msg = msg
        if res is not None:
            self.res = res
        if status is not None:
            self.status = status
        if stopped_at is not None:
            self.stopped_at = stopped_at
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def created_at(self):
        """Gets the created_at of this GetStreamRecordResponse.  # noqa: E501


        :return: The created_at of this GetStreamRecordResponse.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this GetStreamRecordResponse.


        :param created_at: The created_at of this GetStreamRecordResponse.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def id(self):
        """Gets the id of this GetStreamRecordResponse.  # noqa: E501


        :return: The id of this GetStreamRecordResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetStreamRecordResponse.


        :param id: The id of this GetStreamRecordResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def msg(self):
        """Gets the msg of this GetStreamRecordResponse.  # noqa: E501


        :return: The msg of this GetStreamRecordResponse.  # noqa: E501
        :rtype: str
        """
        return self._msg

    @msg.setter
    def msg(self, msg):
        """Sets the msg of this GetStreamRecordResponse.


        :param msg: The msg of this GetStreamRecordResponse.  # noqa: E501
        :type: str
        """

        self._msg = msg

    @property
    def res(self):
        """Gets the res of this GetStreamRecordResponse.  # noqa: E501


        :return: The res of this GetStreamRecordResponse.  # noqa: E501
        :rtype: ResForGetStreamRecordOutput
        """
        return self._res

    @res.setter
    def res(self, res):
        """Sets the res of this GetStreamRecordResponse.


        :param res: The res of this GetStreamRecordResponse.  # noqa: E501
        :type: ResForGetStreamRecordOutput
        """

        self._res = res

    @property
    def status(self):
        """Gets the status of this GetStreamRecordResponse.  # noqa: E501


        :return: The status of this GetStreamRecordResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetStreamRecordResponse.


        :param status: The status of this GetStreamRecordResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stopped_at(self):
        """Gets the stopped_at of this GetStreamRecordResponse.  # noqa: E501


        :return: The stopped_at of this GetStreamRecordResponse.  # noqa: E501
        :rtype: int
        """
        return self._stopped_at

    @stopped_at.setter
    def stopped_at(self, stopped_at):
        """Sets the stopped_at of this GetStreamRecordResponse.


        :param stopped_at: The stopped_at of this GetStreamRecordResponse.  # noqa: E501
        :type: int
        """

        self._stopped_at = stopped_at

    @property
    def updated_at(self):
        """Gets the updated_at of this GetStreamRecordResponse.  # noqa: E501


        :return: The updated_at of this GetStreamRecordResponse.  # noqa: E501
        :rtype: int
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this GetStreamRecordResponse.


        :param updated_at: The updated_at of this GetStreamRecordResponse.  # noqa: E501
        :type: int
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetStreamRecordResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetStreamRecordResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetStreamRecordResponse):
            return True

        return self.to_dict() != other.to_dict()
