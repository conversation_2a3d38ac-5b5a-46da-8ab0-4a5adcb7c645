# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssociateResourceShareRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'principals': 'str',
        'resource_share_trn': 'str',
        'resource_trns': 'str'
    }

    attribute_map = {
        'principals': 'Principals',
        'resource_share_trn': 'ResourceShareTrn',
        'resource_trns': 'ResourceTrns'
    }

    def __init__(self, principals=None, resource_share_trn=None, resource_trns=None, _configuration=None):  # noqa: E501
        """AssociateResourceShareRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._principals = None
        self._resource_share_trn = None
        self._resource_trns = None
        self.discriminator = None

        if principals is not None:
            self.principals = principals
        self.resource_share_trn = resource_share_trn
        if resource_trns is not None:
            self.resource_trns = resource_trns

    @property
    def principals(self):
        """Gets the principals of this AssociateResourceShareRequest.  # noqa: E501


        :return: The principals of this AssociateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._principals

    @principals.setter
    def principals(self, principals):
        """Sets the principals of this AssociateResourceShareRequest.


        :param principals: The principals of this AssociateResourceShareRequest.  # noqa: E501
        :type: str
        """

        self._principals = principals

    @property
    def resource_share_trn(self):
        """Gets the resource_share_trn of this AssociateResourceShareRequest.  # noqa: E501


        :return: The resource_share_trn of this AssociateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trn

    @resource_share_trn.setter
    def resource_share_trn(self, resource_share_trn):
        """Sets the resource_share_trn of this AssociateResourceShareRequest.


        :param resource_share_trn: The resource_share_trn of this AssociateResourceShareRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_share_trn is None:
            raise ValueError("Invalid value for `resource_share_trn`, must not be `None`")  # noqa: E501

        self._resource_share_trn = resource_share_trn

    @property
    def resource_trns(self):
        """Gets the resource_trns of this AssociateResourceShareRequest.  # noqa: E501


        :return: The resource_trns of this AssociateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_trns

    @resource_trns.setter
    def resource_trns(self, resource_trns):
        """Sets the resource_trns of this AssociateResourceShareRequest.


        :param resource_trns: The resource_trns of this AssociateResourceShareRequest.  # noqa: E501
        :type: str
        """

        self._resource_trns = resource_trns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssociateResourceShareRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssociateResourceShareRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssociateResourceShareRequest):
            return True

        return self.to_dict() != other.to_dict()
