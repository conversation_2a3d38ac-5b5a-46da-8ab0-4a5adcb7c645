# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListTagsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'items': 'list[ItemForListTagsOutput]',
        'namespace': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'registry': 'str',
        'repository': 'str',
        'total_count': 'int'
    }

    attribute_map = {
        'items': 'Items',
        'namespace': 'Namespace',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'registry': 'Registry',
        'repository': 'Repository',
        'total_count': 'TotalCount'
    }

    def __init__(self, items=None, namespace=None, page_number=None, page_size=None, registry=None, repository=None, total_count=None, _configuration=None):  # noqa: E501
        """ListTagsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._items = None
        self._namespace = None
        self._page_number = None
        self._page_size = None
        self._registry = None
        self._repository = None
        self._total_count = None
        self.discriminator = None

        if items is not None:
            self.items = items
        if namespace is not None:
            self.namespace = namespace
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if registry is not None:
            self.registry = registry
        if repository is not None:
            self.repository = repository
        if total_count is not None:
            self.total_count = total_count

    @property
    def items(self):
        """Gets the items of this ListTagsResponse.  # noqa: E501


        :return: The items of this ListTagsResponse.  # noqa: E501
        :rtype: list[ItemForListTagsOutput]
        """
        return self._items

    @items.setter
    def items(self, items):
        """Sets the items of this ListTagsResponse.


        :param items: The items of this ListTagsResponse.  # noqa: E501
        :type: list[ItemForListTagsOutput]
        """

        self._items = items

    @property
    def namespace(self):
        """Gets the namespace of this ListTagsResponse.  # noqa: E501


        :return: The namespace of this ListTagsResponse.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this ListTagsResponse.


        :param namespace: The namespace of this ListTagsResponse.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def page_number(self):
        """Gets the page_number of this ListTagsResponse.  # noqa: E501


        :return: The page_number of this ListTagsResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListTagsResponse.


        :param page_number: The page_number of this ListTagsResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListTagsResponse.  # noqa: E501


        :return: The page_size of this ListTagsResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListTagsResponse.


        :param page_size: The page_size of this ListTagsResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def registry(self):
        """Gets the registry of this ListTagsResponse.  # noqa: E501


        :return: The registry of this ListTagsResponse.  # noqa: E501
        :rtype: str
        """
        return self._registry

    @registry.setter
    def registry(self, registry):
        """Sets the registry of this ListTagsResponse.


        :param registry: The registry of this ListTagsResponse.  # noqa: E501
        :type: str
        """

        self._registry = registry

    @property
    def repository(self):
        """Gets the repository of this ListTagsResponse.  # noqa: E501


        :return: The repository of this ListTagsResponse.  # noqa: E501
        :rtype: str
        """
        return self._repository

    @repository.setter
    def repository(self, repository):
        """Sets the repository of this ListTagsResponse.


        :param repository: The repository of this ListTagsResponse.  # noqa: E501
        :type: str
        """

        self._repository = repository

    @property
    def total_count(self):
        """Gets the total_count of this ListTagsResponse.  # noqa: E501


        :return: The total_count of this ListTagsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ListTagsResponse.


        :param total_count: The total_count of this ListTagsResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListTagsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListTagsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListTagsResponse):
            return True

        return self.to_dict() != other.to_dict()
