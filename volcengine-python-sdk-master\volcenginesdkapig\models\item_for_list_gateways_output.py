# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListGatewaysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backend_spec': 'BackendSpecForListGatewaysOutput',
        'comments': 'str',
        'create_time': 'str',
        'custom_log': 'CustomLogForListGatewaysOutput',
        'events': 'list[EventForListGatewaysOutput]',
        'id': 'str',
        'log_spec': 'LogSpecForListGatewaysOutput',
        'message': 'str',
        'monitor_spec': 'MonitorSpecForListGatewaysOutput',
        'name': 'str',
        'network_spec': 'NetworkSpecForListGatewaysOutput',
        'project_name': 'str',
        'region': 'str',
        'resource_spec': 'ResourceSpecForListGatewaysOutput',
        'status': 'str',
        'tags': 'list[TagForListGatewaysOutput]',
        'type': 'str',
        'version': 'str'
    }

    attribute_map = {
        'backend_spec': 'BackendSpec',
        'comments': 'Comments',
        'create_time': 'CreateTime',
        'custom_log': 'CustomLog',
        'events': 'Events',
        'id': 'Id',
        'log_spec': 'LogSpec',
        'message': 'Message',
        'monitor_spec': 'MonitorSpec',
        'name': 'Name',
        'network_spec': 'NetworkSpec',
        'project_name': 'ProjectName',
        'region': 'Region',
        'resource_spec': 'ResourceSpec',
        'status': 'Status',
        'tags': 'Tags',
        'type': 'Type',
        'version': 'Version'
    }

    def __init__(self, backend_spec=None, comments=None, create_time=None, custom_log=None, events=None, id=None, log_spec=None, message=None, monitor_spec=None, name=None, network_spec=None, project_name=None, region=None, resource_spec=None, status=None, tags=None, type=None, version=None, _configuration=None):  # noqa: E501
        """ItemForListGatewaysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backend_spec = None
        self._comments = None
        self._create_time = None
        self._custom_log = None
        self._events = None
        self._id = None
        self._log_spec = None
        self._message = None
        self._monitor_spec = None
        self._name = None
        self._network_spec = None
        self._project_name = None
        self._region = None
        self._resource_spec = None
        self._status = None
        self._tags = None
        self._type = None
        self._version = None
        self.discriminator = None

        if backend_spec is not None:
            self.backend_spec = backend_spec
        if comments is not None:
            self.comments = comments
        if create_time is not None:
            self.create_time = create_time
        if custom_log is not None:
            self.custom_log = custom_log
        if events is not None:
            self.events = events
        if id is not None:
            self.id = id
        if log_spec is not None:
            self.log_spec = log_spec
        if message is not None:
            self.message = message
        if monitor_spec is not None:
            self.monitor_spec = monitor_spec
        if name is not None:
            self.name = name
        if network_spec is not None:
            self.network_spec = network_spec
        if project_name is not None:
            self.project_name = project_name
        if region is not None:
            self.region = region
        if resource_spec is not None:
            self.resource_spec = resource_spec
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type
        if version is not None:
            self.version = version

    @property
    def backend_spec(self):
        """Gets the backend_spec of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The backend_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: BackendSpecForListGatewaysOutput
        """
        return self._backend_spec

    @backend_spec.setter
    def backend_spec(self, backend_spec):
        """Sets the backend_spec of this ItemForListGatewaysOutput.


        :param backend_spec: The backend_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :type: BackendSpecForListGatewaysOutput
        """

        self._backend_spec = backend_spec

    @property
    def comments(self):
        """Gets the comments of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The comments of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this ItemForListGatewaysOutput.


        :param comments: The comments of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The create_time of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListGatewaysOutput.


        :param create_time: The create_time of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def custom_log(self):
        """Gets the custom_log of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The custom_log of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: CustomLogForListGatewaysOutput
        """
        return self._custom_log

    @custom_log.setter
    def custom_log(self, custom_log):
        """Sets the custom_log of this ItemForListGatewaysOutput.


        :param custom_log: The custom_log of this ItemForListGatewaysOutput.  # noqa: E501
        :type: CustomLogForListGatewaysOutput
        """

        self._custom_log = custom_log

    @property
    def events(self):
        """Gets the events of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The events of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: list[EventForListGatewaysOutput]
        """
        return self._events

    @events.setter
    def events(self, events):
        """Sets the events of this ItemForListGatewaysOutput.


        :param events: The events of this ItemForListGatewaysOutput.  # noqa: E501
        :type: list[EventForListGatewaysOutput]
        """

        self._events = events

    @property
    def id(self):
        """Gets the id of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The id of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListGatewaysOutput.


        :param id: The id of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def log_spec(self):
        """Gets the log_spec of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The log_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: LogSpecForListGatewaysOutput
        """
        return self._log_spec

    @log_spec.setter
    def log_spec(self, log_spec):
        """Sets the log_spec of this ItemForListGatewaysOutput.


        :param log_spec: The log_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :type: LogSpecForListGatewaysOutput
        """

        self._log_spec = log_spec

    @property
    def message(self):
        """Gets the message of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The message of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListGatewaysOutput.


        :param message: The message of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def monitor_spec(self):
        """Gets the monitor_spec of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The monitor_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: MonitorSpecForListGatewaysOutput
        """
        return self._monitor_spec

    @monitor_spec.setter
    def monitor_spec(self, monitor_spec):
        """Sets the monitor_spec of this ItemForListGatewaysOutput.


        :param monitor_spec: The monitor_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :type: MonitorSpecForListGatewaysOutput
        """

        self._monitor_spec = monitor_spec

    @property
    def name(self):
        """Gets the name of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The name of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListGatewaysOutput.


        :param name: The name of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def network_spec(self):
        """Gets the network_spec of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The network_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: NetworkSpecForListGatewaysOutput
        """
        return self._network_spec

    @network_spec.setter
    def network_spec(self, network_spec):
        """Sets the network_spec of this ItemForListGatewaysOutput.


        :param network_spec: The network_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :type: NetworkSpecForListGatewaysOutput
        """

        self._network_spec = network_spec

    @property
    def project_name(self):
        """Gets the project_name of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The project_name of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ItemForListGatewaysOutput.


        :param project_name: The project_name of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region(self):
        """Gets the region of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The region of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ItemForListGatewaysOutput.


        :param region: The region of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def resource_spec(self):
        """Gets the resource_spec of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The resource_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: ResourceSpecForListGatewaysOutput
        """
        return self._resource_spec

    @resource_spec.setter
    def resource_spec(self, resource_spec):
        """Sets the resource_spec of this ItemForListGatewaysOutput.


        :param resource_spec: The resource_spec of this ItemForListGatewaysOutput.  # noqa: E501
        :type: ResourceSpecForListGatewaysOutput
        """

        self._resource_spec = resource_spec

    @property
    def status(self):
        """Gets the status of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The status of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListGatewaysOutput.


        :param status: The status of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The tags of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: list[TagForListGatewaysOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListGatewaysOutput.


        :param tags: The tags of this ItemForListGatewaysOutput.  # noqa: E501
        :type: list[TagForListGatewaysOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The type of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListGatewaysOutput.


        :param type: The type of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def version(self):
        """Gets the version of this ItemForListGatewaysOutput.  # noqa: E501


        :return: The version of this ItemForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this ItemForListGatewaysOutput.


        :param version: The version of this ItemForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListGatewaysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListGatewaysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListGatewaysOutput):
            return True

        return self.to_dict() != other.to_dict()
