# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDeviceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_notification': 'AlertNotificationForCreateDeviceInput',
        'auto_pull_after_register': 'bool',
        'auto_pull_after_regiter': 'bool',
        'coordinates': 'CoordinatesForCreateDeviceInput',
        'description': 'str',
        'device_nsid': 'str',
        'device_name': 'str',
        'location': 'str',
        'password': 'str',
        'rtp_transport_tcp': 'bool',
        'space_id': 'str',
        'type': 'str',
        'use_sub_stream': 'bool',
        'user_name': 'str',
        'username': 'str'
    }

    attribute_map = {
        'alert_notification': 'AlertNotification',
        'auto_pull_after_register': 'AutoPullAfterRegister',
        'auto_pull_after_regiter': 'AutoPullAfterRegiter',
        'coordinates': 'Coordinates',
        'description': 'Description',
        'device_nsid': 'DeviceNSID',
        'device_name': 'DeviceName',
        'location': 'Location',
        'password': 'Password',
        'rtp_transport_tcp': 'RtpTransportTcp',
        'space_id': 'SpaceID',
        'type': 'Type',
        'use_sub_stream': 'UseSubStream',
        'user_name': 'UserName',
        'username': 'Username'
    }

    def __init__(self, alert_notification=None, auto_pull_after_register=None, auto_pull_after_regiter=None, coordinates=None, description=None, device_nsid=None, device_name=None, location=None, password=None, rtp_transport_tcp=None, space_id=None, type=None, use_sub_stream=None, user_name=None, username=None, _configuration=None):  # noqa: E501
        """CreateDeviceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_notification = None
        self._auto_pull_after_register = None
        self._auto_pull_after_regiter = None
        self._coordinates = None
        self._description = None
        self._device_nsid = None
        self._device_name = None
        self._location = None
        self._password = None
        self._rtp_transport_tcp = None
        self._space_id = None
        self._type = None
        self._use_sub_stream = None
        self._user_name = None
        self._username = None
        self.discriminator = None

        if alert_notification is not None:
            self.alert_notification = alert_notification
        if auto_pull_after_register is not None:
            self.auto_pull_after_register = auto_pull_after_register
        if auto_pull_after_regiter is not None:
            self.auto_pull_after_regiter = auto_pull_after_regiter
        if coordinates is not None:
            self.coordinates = coordinates
        if description is not None:
            self.description = description
        if device_nsid is not None:
            self.device_nsid = device_nsid
        self.device_name = device_name
        if location is not None:
            self.location = location
        if password is not None:
            self.password = password
        if rtp_transport_tcp is not None:
            self.rtp_transport_tcp = rtp_transport_tcp
        self.space_id = space_id
        self.type = type
        if use_sub_stream is not None:
            self.use_sub_stream = use_sub_stream
        self.user_name = user_name
        if username is not None:
            self.username = username

    @property
    def alert_notification(self):
        """Gets the alert_notification of this CreateDeviceRequest.  # noqa: E501


        :return: The alert_notification of this CreateDeviceRequest.  # noqa: E501
        :rtype: AlertNotificationForCreateDeviceInput
        """
        return self._alert_notification

    @alert_notification.setter
    def alert_notification(self, alert_notification):
        """Sets the alert_notification of this CreateDeviceRequest.


        :param alert_notification: The alert_notification of this CreateDeviceRequest.  # noqa: E501
        :type: AlertNotificationForCreateDeviceInput
        """

        self._alert_notification = alert_notification

    @property
    def auto_pull_after_register(self):
        """Gets the auto_pull_after_register of this CreateDeviceRequest.  # noqa: E501


        :return: The auto_pull_after_register of this CreateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pull_after_register

    @auto_pull_after_register.setter
    def auto_pull_after_register(self, auto_pull_after_register):
        """Sets the auto_pull_after_register of this CreateDeviceRequest.


        :param auto_pull_after_register: The auto_pull_after_register of this CreateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._auto_pull_after_register = auto_pull_after_register

    @property
    def auto_pull_after_regiter(self):
        """Gets the auto_pull_after_regiter of this CreateDeviceRequest.  # noqa: E501


        :return: The auto_pull_after_regiter of this CreateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pull_after_regiter

    @auto_pull_after_regiter.setter
    def auto_pull_after_regiter(self, auto_pull_after_regiter):
        """Sets the auto_pull_after_regiter of this CreateDeviceRequest.


        :param auto_pull_after_regiter: The auto_pull_after_regiter of this CreateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._auto_pull_after_regiter = auto_pull_after_regiter

    @property
    def coordinates(self):
        """Gets the coordinates of this CreateDeviceRequest.  # noqa: E501


        :return: The coordinates of this CreateDeviceRequest.  # noqa: E501
        :rtype: CoordinatesForCreateDeviceInput
        """
        return self._coordinates

    @coordinates.setter
    def coordinates(self, coordinates):
        """Sets the coordinates of this CreateDeviceRequest.


        :param coordinates: The coordinates of this CreateDeviceRequest.  # noqa: E501
        :type: CoordinatesForCreateDeviceInput
        """

        self._coordinates = coordinates

    @property
    def description(self):
        """Gets the description of this CreateDeviceRequest.  # noqa: E501


        :return: The description of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDeviceRequest.


        :param description: The description of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def device_nsid(self):
        """Gets the device_nsid of this CreateDeviceRequest.  # noqa: E501


        :return: The device_nsid of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this CreateDeviceRequest.


        :param device_nsid: The device_nsid of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def device_name(self):
        """Gets the device_name of this CreateDeviceRequest.  # noqa: E501


        :return: The device_name of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this CreateDeviceRequest.


        :param device_name: The device_name of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and device_name is None:
            raise ValueError("Invalid value for `device_name`, must not be `None`")  # noqa: E501

        self._device_name = device_name

    @property
    def location(self):
        """Gets the location of this CreateDeviceRequest.  # noqa: E501


        :return: The location of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this CreateDeviceRequest.


        :param location: The location of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._location = location

    @property
    def password(self):
        """Gets the password of this CreateDeviceRequest.  # noqa: E501


        :return: The password of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this CreateDeviceRequest.


        :param password: The password of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def rtp_transport_tcp(self):
        """Gets the rtp_transport_tcp of this CreateDeviceRequest.  # noqa: E501


        :return: The rtp_transport_tcp of this CreateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._rtp_transport_tcp

    @rtp_transport_tcp.setter
    def rtp_transport_tcp(self, rtp_transport_tcp):
        """Sets the rtp_transport_tcp of this CreateDeviceRequest.


        :param rtp_transport_tcp: The rtp_transport_tcp of this CreateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._rtp_transport_tcp = rtp_transport_tcp

    @property
    def space_id(self):
        """Gets the space_id of this CreateDeviceRequest.  # noqa: E501


        :return: The space_id of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this CreateDeviceRequest.


        :param space_id: The space_id of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_id is None:
            raise ValueError("Invalid value for `space_id`, must not be `None`")  # noqa: E501

        self._space_id = space_id

    @property
    def type(self):
        """Gets the type of this CreateDeviceRequest.  # noqa: E501


        :return: The type of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateDeviceRequest.


        :param type: The type of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def use_sub_stream(self):
        """Gets the use_sub_stream of this CreateDeviceRequest.  # noqa: E501


        :return: The use_sub_stream of this CreateDeviceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._use_sub_stream

    @use_sub_stream.setter
    def use_sub_stream(self, use_sub_stream):
        """Sets the use_sub_stream of this CreateDeviceRequest.


        :param use_sub_stream: The use_sub_stream of this CreateDeviceRequest.  # noqa: E501
        :type: bool
        """

        self._use_sub_stream = use_sub_stream

    @property
    def user_name(self):
        """Gets the user_name of this CreateDeviceRequest.  # noqa: E501


        :return: The user_name of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this CreateDeviceRequest.


        :param user_name: The user_name of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user_name is None:
            raise ValueError("Invalid value for `user_name`, must not be `None`")  # noqa: E501

        self._user_name = user_name

    @property
    def username(self):
        """Gets the username of this CreateDeviceRequest.  # noqa: E501


        :return: The username of this CreateDeviceRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this CreateDeviceRequest.


        :param username: The username of this CreateDeviceRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDeviceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDeviceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDeviceRequest):
            return True

        return self.to_dict() != other.to_dict()
