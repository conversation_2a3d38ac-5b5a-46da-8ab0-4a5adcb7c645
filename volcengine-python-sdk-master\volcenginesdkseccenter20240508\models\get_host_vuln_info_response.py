# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetHostVulnInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affect': 'list[AffectForGetHostVulnInfoOutput]',
        'eip_address': 'str',
        'fix_command_list': 'list[str]',
        'hostname': 'str',
        'platform': 'str',
        'primary_ip_address': 'str',
        'suggest': 'str'
    }

    attribute_map = {
        'affect': 'Affect',
        'eip_address': 'EipAddress',
        'fix_command_list': 'FixCommandList',
        'hostname': 'Hostname',
        'platform': 'Platform',
        'primary_ip_address': 'PrimaryIpAddress',
        'suggest': 'Suggest'
    }

    def __init__(self, affect=None, eip_address=None, fix_command_list=None, hostname=None, platform=None, primary_ip_address=None, suggest=None, _configuration=None):  # noqa: E501
        """GetHostVulnInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affect = None
        self._eip_address = None
        self._fix_command_list = None
        self._hostname = None
        self._platform = None
        self._primary_ip_address = None
        self._suggest = None
        self.discriminator = None

        if affect is not None:
            self.affect = affect
        if eip_address is not None:
            self.eip_address = eip_address
        if fix_command_list is not None:
            self.fix_command_list = fix_command_list
        if hostname is not None:
            self.hostname = hostname
        if platform is not None:
            self.platform = platform
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if suggest is not None:
            self.suggest = suggest

    @property
    def affect(self):
        """Gets the affect of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The affect of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: list[AffectForGetHostVulnInfoOutput]
        """
        return self._affect

    @affect.setter
    def affect(self, affect):
        """Sets the affect of this GetHostVulnInfoResponse.


        :param affect: The affect of this GetHostVulnInfoResponse.  # noqa: E501
        :type: list[AffectForGetHostVulnInfoOutput]
        """

        self._affect = affect

    @property
    def eip_address(self):
        """Gets the eip_address of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The eip_address of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this GetHostVulnInfoResponse.


        :param eip_address: The eip_address of this GetHostVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def fix_command_list(self):
        """Gets the fix_command_list of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The fix_command_list of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._fix_command_list

    @fix_command_list.setter
    def fix_command_list(self, fix_command_list):
        """Sets the fix_command_list of this GetHostVulnInfoResponse.


        :param fix_command_list: The fix_command_list of this GetHostVulnInfoResponse.  # noqa: E501
        :type: list[str]
        """

        self._fix_command_list = fix_command_list

    @property
    def hostname(self):
        """Gets the hostname of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The hostname of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this GetHostVulnInfoResponse.


        :param hostname: The hostname of this GetHostVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def platform(self):
        """Gets the platform of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The platform of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this GetHostVulnInfoResponse.


        :param platform: The platform of this GetHostVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The primary_ip_address of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this GetHostVulnInfoResponse.


        :param primary_ip_address: The primary_ip_address of this GetHostVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def suggest(self):
        """Gets the suggest of this GetHostVulnInfoResponse.  # noqa: E501


        :return: The suggest of this GetHostVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._suggest

    @suggest.setter
    def suggest(self, suggest):
        """Sets the suggest of this GetHostVulnInfoResponse.


        :param suggest: The suggest of this GetHostVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._suggest = suggest

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetHostVulnInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetHostVulnInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetHostVulnInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
