# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CalculateRepoImageScanQuotaRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_ids': 'list[str]',
        'asset_type': 'str'
    }

    attribute_map = {
        'asset_ids': 'AssetIDs',
        'asset_type': 'AssetType'
    }

    def __init__(self, asset_ids=None, asset_type=None, _configuration=None):  # noqa: E501
        """CalculateRepoImageScanQuotaRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_ids = None
        self._asset_type = None
        self.discriminator = None

        if asset_ids is not None:
            self.asset_ids = asset_ids
        self.asset_type = asset_type

    @property
    def asset_ids(self):
        """Gets the asset_ids of this CalculateRepoImageScanQuotaRequest.  # noqa: E501


        :return: The asset_ids of this CalculateRepoImageScanQuotaRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_ids

    @asset_ids.setter
    def asset_ids(self, asset_ids):
        """Sets the asset_ids of this CalculateRepoImageScanQuotaRequest.


        :param asset_ids: The asset_ids of this CalculateRepoImageScanQuotaRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_ids = asset_ids

    @property
    def asset_type(self):
        """Gets the asset_type of this CalculateRepoImageScanQuotaRequest.  # noqa: E501


        :return: The asset_type of this CalculateRepoImageScanQuotaRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this CalculateRepoImageScanQuotaRequest.


        :param asset_type: The asset_type of this CalculateRepoImageScanQuotaRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and asset_type is None:
            raise ValueError("Invalid value for `asset_type`, must not be `None`")  # noqa: E501

        self._asset_type = asset_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CalculateRepoImageScanQuotaRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CalculateRepoImageScanQuotaRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CalculateRepoImageScanQuotaRequest):
            return True

        return self.to_dict() != other.to_dict()
