# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplicationLayoutForCreateNodeGroupInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'layout_component_names': 'list[str]'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'layout_component_names': 'LayoutComponentNames'
    }

    def __init__(self, application_name=None, layout_component_names=None, _configuration=None):  # noqa: E501
        """ApplicationLayoutForCreateNodeGroupInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._layout_component_names = None
        self.discriminator = None

        if application_name is not None:
            self.application_name = application_name
        if layout_component_names is not None:
            self.layout_component_names = layout_component_names

    @property
    def application_name(self):
        """Gets the application_name of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501


        :return: The application_name of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ApplicationLayoutForCreateNodeGroupInput.


        :param application_name: The application_name of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def layout_component_names(self):
        """Gets the layout_component_names of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501


        :return: The layout_component_names of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._layout_component_names

    @layout_component_names.setter
    def layout_component_names(self, layout_component_names):
        """Sets the layout_component_names of this ApplicationLayoutForCreateNodeGroupInput.


        :param layout_component_names: The layout_component_names of this ApplicationLayoutForCreateNodeGroupInput.  # noqa: E501
        :type: list[str]
        """

        self._layout_component_names = layout_component_names

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplicationLayoutForCreateNodeGroupInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplicationLayoutForCreateNodeGroupInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplicationLayoutForCreateNodeGroupInput):
            return True

        return self.to_dict() != other.to_dict()
