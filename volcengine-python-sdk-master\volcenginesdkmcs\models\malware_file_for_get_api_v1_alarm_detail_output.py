# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MalwareFileForGetApiV1AlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_md5': 'str',
        'file_path': 'str'
    }

    attribute_map = {
        'file_md5': 'file_md5',
        'file_path': 'file_path'
    }

    def __init__(self, file_md5=None, file_path=None, _configuration=None):  # noqa: E501
        """MalwareFileForGetApiV1AlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_md5 = None
        self._file_path = None
        self.discriminator = None

        if file_md5 is not None:
            self.file_md5 = file_md5
        if file_path is not None:
            self.file_path = file_path

    @property
    def file_md5(self):
        """Gets the file_md5 of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The file_md5 of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_md5

    @file_md5.setter
    def file_md5(self, file_md5):
        """Sets the file_md5 of this MalwareFileForGetApiV1AlarmDetailOutput.


        :param file_md5: The file_md5 of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._file_md5 = file_md5

    @property
    def file_path(self):
        """Gets the file_path of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The file_path of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this MalwareFileForGetApiV1AlarmDetailOutput.


        :param file_path: The file_path of this MalwareFileForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MalwareFileForGetApiV1AlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MalwareFileForGetApiV1AlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MalwareFileForGetApiV1AlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
