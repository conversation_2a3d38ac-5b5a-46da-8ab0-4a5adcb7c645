# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateListenerInOneStepRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'backup_endpoint_groups': 'list[BackupEndpointGroupForCreateListenerInOneStepInput]',
        'disable_isolate_tcp_null_conn': 'bool',
        'disable_pre_connect': 'bool',
        'enable_affinity': 'bool',
        'endpoint_groups': 'list[EndpointGroupForCreateListenerInOneStepInput]',
        'fixed_source_return': 'FixedSourceReturnForCreateListenerInOneStepInput',
        'ip_access': 'IPAccessForCreateListenerInOneStepInput',
        'name': 'str',
        'port_ranges': 'list[PortRangeForCreateListenerInOneStepInput]',
        'protocol': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'backup_endpoint_groups': 'BackupEndpointGroups',
        'disable_isolate_tcp_null_conn': 'DisableIsolateTCPNullConn',
        'disable_pre_connect': 'DisablePreConnect',
        'enable_affinity': 'EnableAffinity',
        'endpoint_groups': 'EndpointGroups',
        'fixed_source_return': 'FixedSourceReturn',
        'ip_access': 'IPAccess',
        'name': 'Name',
        'port_ranges': 'PortRanges',
        'protocol': 'Protocol'
    }

    def __init__(self, accelerator_id=None, backup_endpoint_groups=None, disable_isolate_tcp_null_conn=None, disable_pre_connect=None, enable_affinity=None, endpoint_groups=None, fixed_source_return=None, ip_access=None, name=None, port_ranges=None, protocol=None, _configuration=None):  # noqa: E501
        """CreateListenerInOneStepRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._backup_endpoint_groups = None
        self._disable_isolate_tcp_null_conn = None
        self._disable_pre_connect = None
        self._enable_affinity = None
        self._endpoint_groups = None
        self._fixed_source_return = None
        self._ip_access = None
        self._name = None
        self._port_ranges = None
        self._protocol = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        if backup_endpoint_groups is not None:
            self.backup_endpoint_groups = backup_endpoint_groups
        if disable_isolate_tcp_null_conn is not None:
            self.disable_isolate_tcp_null_conn = disable_isolate_tcp_null_conn
        if disable_pre_connect is not None:
            self.disable_pre_connect = disable_pre_connect
        self.enable_affinity = enable_affinity
        if endpoint_groups is not None:
            self.endpoint_groups = endpoint_groups
        if fixed_source_return is not None:
            self.fixed_source_return = fixed_source_return
        if ip_access is not None:
            self.ip_access = ip_access
        if name is not None:
            self.name = name
        if port_ranges is not None:
            self.port_ranges = port_ranges
        self.protocol = protocol

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The accelerator_id of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this CreateListenerInOneStepRequest.


        :param accelerator_id: The accelerator_id of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def backup_endpoint_groups(self):
        """Gets the backup_endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The backup_endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: list[BackupEndpointGroupForCreateListenerInOneStepInput]
        """
        return self._backup_endpoint_groups

    @backup_endpoint_groups.setter
    def backup_endpoint_groups(self, backup_endpoint_groups):
        """Sets the backup_endpoint_groups of this CreateListenerInOneStepRequest.


        :param backup_endpoint_groups: The backup_endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: list[BackupEndpointGroupForCreateListenerInOneStepInput]
        """

        self._backup_endpoint_groups = backup_endpoint_groups

    @property
    def disable_isolate_tcp_null_conn(self):
        """Gets the disable_isolate_tcp_null_conn of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The disable_isolate_tcp_null_conn of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: bool
        """
        return self._disable_isolate_tcp_null_conn

    @disable_isolate_tcp_null_conn.setter
    def disable_isolate_tcp_null_conn(self, disable_isolate_tcp_null_conn):
        """Sets the disable_isolate_tcp_null_conn of this CreateListenerInOneStepRequest.


        :param disable_isolate_tcp_null_conn: The disable_isolate_tcp_null_conn of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: bool
        """

        self._disable_isolate_tcp_null_conn = disable_isolate_tcp_null_conn

    @property
    def disable_pre_connect(self):
        """Gets the disable_pre_connect of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The disable_pre_connect of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: bool
        """
        return self._disable_pre_connect

    @disable_pre_connect.setter
    def disable_pre_connect(self, disable_pre_connect):
        """Sets the disable_pre_connect of this CreateListenerInOneStepRequest.


        :param disable_pre_connect: The disable_pre_connect of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: bool
        """

        self._disable_pre_connect = disable_pre_connect

    @property
    def enable_affinity(self):
        """Gets the enable_affinity of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The enable_affinity of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_affinity

    @enable_affinity.setter
    def enable_affinity(self, enable_affinity):
        """Sets the enable_affinity of this CreateListenerInOneStepRequest.


        :param enable_affinity: The enable_affinity of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable_affinity is None:
            raise ValueError("Invalid value for `enable_affinity`, must not be `None`")  # noqa: E501

        self._enable_affinity = enable_affinity

    @property
    def endpoint_groups(self):
        """Gets the endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: list[EndpointGroupForCreateListenerInOneStepInput]
        """
        return self._endpoint_groups

    @endpoint_groups.setter
    def endpoint_groups(self, endpoint_groups):
        """Sets the endpoint_groups of this CreateListenerInOneStepRequest.


        :param endpoint_groups: The endpoint_groups of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: list[EndpointGroupForCreateListenerInOneStepInput]
        """

        self._endpoint_groups = endpoint_groups

    @property
    def fixed_source_return(self):
        """Gets the fixed_source_return of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The fixed_source_return of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: FixedSourceReturnForCreateListenerInOneStepInput
        """
        return self._fixed_source_return

    @fixed_source_return.setter
    def fixed_source_return(self, fixed_source_return):
        """Sets the fixed_source_return of this CreateListenerInOneStepRequest.


        :param fixed_source_return: The fixed_source_return of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: FixedSourceReturnForCreateListenerInOneStepInput
        """

        self._fixed_source_return = fixed_source_return

    @property
    def ip_access(self):
        """Gets the ip_access of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The ip_access of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: IPAccessForCreateListenerInOneStepInput
        """
        return self._ip_access

    @ip_access.setter
    def ip_access(self, ip_access):
        """Sets the ip_access of this CreateListenerInOneStepRequest.


        :param ip_access: The ip_access of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: IPAccessForCreateListenerInOneStepInput
        """

        self._ip_access = ip_access

    @property
    def name(self):
        """Gets the name of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The name of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateListenerInOneStepRequest.


        :param name: The name of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def port_ranges(self):
        """Gets the port_ranges of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The port_ranges of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: list[PortRangeForCreateListenerInOneStepInput]
        """
        return self._port_ranges

    @port_ranges.setter
    def port_ranges(self, port_ranges):
        """Sets the port_ranges of this CreateListenerInOneStepRequest.


        :param port_ranges: The port_ranges of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: list[PortRangeForCreateListenerInOneStepInput]
        """

        self._port_ranges = port_ranges

    @property
    def protocol(self):
        """Gets the protocol of this CreateListenerInOneStepRequest.  # noqa: E501


        :return: The protocol of this CreateListenerInOneStepRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateListenerInOneStepRequest.


        :param protocol: The protocol of this CreateListenerInOneStepRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protocol is None:
            raise ValueError("Invalid value for `protocol`, must not be `None`")  # noqa: E501

        self._protocol = protocol

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateListenerInOneStepRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateListenerInOneStepRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateListenerInOneStepRequest):
            return True

        return self.to_dict() != other.to_dict()
