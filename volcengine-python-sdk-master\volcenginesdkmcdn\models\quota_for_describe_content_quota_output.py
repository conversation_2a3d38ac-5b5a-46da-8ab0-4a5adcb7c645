# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotaForDescribeContentQuotaOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'cloud_account_id': 'str',
        'cloud_account_name': 'str',
        'preload_quota': 'int',
        'preload_remain': 'int',
        'product_type': 'str',
        'refresh_dir_quota': 'int',
        'refresh_dir_remain': 'int',
        'refresh_url_quota': 'int',
        'refresh_url_remain': 'int',
        'sub_product': 'str',
        'vendor': 'str'
    }

    attribute_map = {
        'area': 'Area',
        'cloud_account_id': 'CloudAccountId',
        'cloud_account_name': 'CloudAccountName',
        'preload_quota': 'PreloadQuota',
        'preload_remain': 'PreloadRemain',
        'product_type': 'ProductType',
        'refresh_dir_quota': 'RefreshDirQuota',
        'refresh_dir_remain': 'RefreshDirRemain',
        'refresh_url_quota': 'RefreshUrlQuota',
        'refresh_url_remain': 'RefreshUrlRemain',
        'sub_product': 'SubProduct',
        'vendor': 'Vendor'
    }

    def __init__(self, area=None, cloud_account_id=None, cloud_account_name=None, preload_quota=None, preload_remain=None, product_type=None, refresh_dir_quota=None, refresh_dir_remain=None, refresh_url_quota=None, refresh_url_remain=None, sub_product=None, vendor=None, _configuration=None):  # noqa: E501
        """QuotaForDescribeContentQuotaOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._cloud_account_id = None
        self._cloud_account_name = None
        self._preload_quota = None
        self._preload_remain = None
        self._product_type = None
        self._refresh_dir_quota = None
        self._refresh_dir_remain = None
        self._refresh_url_quota = None
        self._refresh_url_remain = None
        self._sub_product = None
        self._vendor = None
        self.discriminator = None

        if area is not None:
            self.area = area
        if cloud_account_id is not None:
            self.cloud_account_id = cloud_account_id
        if cloud_account_name is not None:
            self.cloud_account_name = cloud_account_name
        if preload_quota is not None:
            self.preload_quota = preload_quota
        if preload_remain is not None:
            self.preload_remain = preload_remain
        if product_type is not None:
            self.product_type = product_type
        if refresh_dir_quota is not None:
            self.refresh_dir_quota = refresh_dir_quota
        if refresh_dir_remain is not None:
            self.refresh_dir_remain = refresh_dir_remain
        if refresh_url_quota is not None:
            self.refresh_url_quota = refresh_url_quota
        if refresh_url_remain is not None:
            self.refresh_url_remain = refresh_url_remain
        if sub_product is not None:
            self.sub_product = sub_product
        if vendor is not None:
            self.vendor = vendor

    @property
    def area(self):
        """Gets the area of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The area of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this QuotaForDescribeContentQuotaOutput.


        :param area: The area of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def cloud_account_id(self):
        """Gets the cloud_account_id of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The cloud_account_id of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_id

    @cloud_account_id.setter
    def cloud_account_id(self, cloud_account_id):
        """Sets the cloud_account_id of this QuotaForDescribeContentQuotaOutput.


        :param cloud_account_id: The cloud_account_id of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._cloud_account_id = cloud_account_id

    @property
    def cloud_account_name(self):
        """Gets the cloud_account_name of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The cloud_account_name of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_name

    @cloud_account_name.setter
    def cloud_account_name(self, cloud_account_name):
        """Sets the cloud_account_name of this QuotaForDescribeContentQuotaOutput.


        :param cloud_account_name: The cloud_account_name of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._cloud_account_name = cloud_account_name

    @property
    def preload_quota(self):
        """Gets the preload_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The preload_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._preload_quota

    @preload_quota.setter
    def preload_quota(self, preload_quota):
        """Sets the preload_quota of this QuotaForDescribeContentQuotaOutput.


        :param preload_quota: The preload_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._preload_quota = preload_quota

    @property
    def preload_remain(self):
        """Gets the preload_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The preload_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._preload_remain

    @preload_remain.setter
    def preload_remain(self, preload_remain):
        """Sets the preload_remain of this QuotaForDescribeContentQuotaOutput.


        :param preload_remain: The preload_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._preload_remain = preload_remain

    @property
    def product_type(self):
        """Gets the product_type of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The product_type of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_type

    @product_type.setter
    def product_type(self, product_type):
        """Sets the product_type of this QuotaForDescribeContentQuotaOutput.


        :param product_type: The product_type of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._product_type = product_type

    @property
    def refresh_dir_quota(self):
        """Gets the refresh_dir_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The refresh_dir_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._refresh_dir_quota

    @refresh_dir_quota.setter
    def refresh_dir_quota(self, refresh_dir_quota):
        """Sets the refresh_dir_quota of this QuotaForDescribeContentQuotaOutput.


        :param refresh_dir_quota: The refresh_dir_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._refresh_dir_quota = refresh_dir_quota

    @property
    def refresh_dir_remain(self):
        """Gets the refresh_dir_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The refresh_dir_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._refresh_dir_remain

    @refresh_dir_remain.setter
    def refresh_dir_remain(self, refresh_dir_remain):
        """Sets the refresh_dir_remain of this QuotaForDescribeContentQuotaOutput.


        :param refresh_dir_remain: The refresh_dir_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._refresh_dir_remain = refresh_dir_remain

    @property
    def refresh_url_quota(self):
        """Gets the refresh_url_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The refresh_url_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._refresh_url_quota

    @refresh_url_quota.setter
    def refresh_url_quota(self, refresh_url_quota):
        """Sets the refresh_url_quota of this QuotaForDescribeContentQuotaOutput.


        :param refresh_url_quota: The refresh_url_quota of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._refresh_url_quota = refresh_url_quota

    @property
    def refresh_url_remain(self):
        """Gets the refresh_url_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The refresh_url_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._refresh_url_remain

    @refresh_url_remain.setter
    def refresh_url_remain(self, refresh_url_remain):
        """Sets the refresh_url_remain of this QuotaForDescribeContentQuotaOutput.


        :param refresh_url_remain: The refresh_url_remain of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: int
        """

        self._refresh_url_remain = refresh_url_remain

    @property
    def sub_product(self):
        """Gets the sub_product of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The sub_product of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_product

    @sub_product.setter
    def sub_product(self, sub_product):
        """Sets the sub_product of this QuotaForDescribeContentQuotaOutput.


        :param sub_product: The sub_product of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._sub_product = sub_product

    @property
    def vendor(self):
        """Gets the vendor of this QuotaForDescribeContentQuotaOutput.  # noqa: E501


        :return: The vendor of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this QuotaForDescribeContentQuotaOutput.


        :param vendor: The vendor of this QuotaForDescribeContentQuotaOutput.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotaForDescribeContentQuotaOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotaForDescribeContentQuotaOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotaForDescribeContentQuotaOutput):
            return True

        return self.to_dict() != other.to_dict()
