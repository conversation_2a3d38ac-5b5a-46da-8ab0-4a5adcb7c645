# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListInvitationsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'order_by': 'int',
        'order_str': 'str',
        'org_unit_id': 'str',
        'search': 'str'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'order_by': 'OrderBy',
        'order_str': 'OrderStr',
        'org_unit_id': 'OrgUnitId',
        'search': 'Search'
    }

    def __init__(self, limit=None, offset=None, order_by=None, order_str=None, org_unit_id=None, search=None, _configuration=None):  # noqa: E501
        """ListInvitationsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._order_by = None
        self._order_str = None
        self._org_unit_id = None
        self._search = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if order_by is not None:
            self.order_by = order_by
        if order_str is not None:
            self.order_str = order_str
        if org_unit_id is not None:
            self.org_unit_id = org_unit_id
        if search is not None:
            self.search = search

    @property
    def limit(self):
        """Gets the limit of this ListInvitationsRequest.  # noqa: E501


        :return: The limit of this ListInvitationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListInvitationsRequest.


        :param limit: The limit of this ListInvitationsRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListInvitationsRequest.  # noqa: E501


        :return: The offset of this ListInvitationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListInvitationsRequest.


        :param offset: The offset of this ListInvitationsRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def order_by(self):
        """Gets the order_by of this ListInvitationsRequest.  # noqa: E501


        :return: The order_by of this ListInvitationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this ListInvitationsRequest.


        :param order_by: The order_by of this ListInvitationsRequest.  # noqa: E501
        :type: int
        """

        self._order_by = order_by

    @property
    def order_str(self):
        """Gets the order_str of this ListInvitationsRequest.  # noqa: E501


        :return: The order_str of this ListInvitationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_str

    @order_str.setter
    def order_str(self, order_str):
        """Sets the order_str of this ListInvitationsRequest.


        :param order_str: The order_str of this ListInvitationsRequest.  # noqa: E501
        :type: str
        """

        self._order_str = order_str

    @property
    def org_unit_id(self):
        """Gets the org_unit_id of this ListInvitationsRequest.  # noqa: E501


        :return: The org_unit_id of this ListInvitationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._org_unit_id

    @org_unit_id.setter
    def org_unit_id(self, org_unit_id):
        """Sets the org_unit_id of this ListInvitationsRequest.


        :param org_unit_id: The org_unit_id of this ListInvitationsRequest.  # noqa: E501
        :type: str
        """

        self._org_unit_id = org_unit_id

    @property
    def search(self):
        """Gets the search of this ListInvitationsRequest.  # noqa: E501


        :return: The search of this ListInvitationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search

    @search.setter
    def search(self, search):
        """Sets the search of this ListInvitationsRequest.


        :param search: The search of this ListInvitationsRequest.  # noqa: E501
        :type: str
        """

        self._search = search

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListInvitationsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListInvitationsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListInvitationsRequest):
            return True

        return self.to_dict() != other.to_dict()
