# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HandleAlarmByAgentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_id': 'str',
        'alarm_type': 'str',
        'files': 'list[FileForHandleAlarmByAgentInput]',
        'processes': 'list[ProcessForHandleAlarmByAgentInput]'
    }

    attribute_map = {
        'alarm_id': 'AlarmID',
        'alarm_type': 'AlarmType',
        'files': 'Files',
        'processes': 'Processes'
    }

    def __init__(self, alarm_id=None, alarm_type=None, files=None, processes=None, _configuration=None):  # noqa: E501
        """HandleAlarmByAgentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_id = None
        self._alarm_type = None
        self._files = None
        self._processes = None
        self.discriminator = None

        if alarm_id is not None:
            self.alarm_id = alarm_id
        self.alarm_type = alarm_type
        if files is not None:
            self.files = files
        if processes is not None:
            self.processes = processes

    @property
    def alarm_id(self):
        """Gets the alarm_id of this HandleAlarmByAgentRequest.  # noqa: E501


        :return: The alarm_id of this HandleAlarmByAgentRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this HandleAlarmByAgentRequest.


        :param alarm_id: The alarm_id of this HandleAlarmByAgentRequest.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alarm_type(self):
        """Gets the alarm_type of this HandleAlarmByAgentRequest.  # noqa: E501


        :return: The alarm_type of this HandleAlarmByAgentRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this HandleAlarmByAgentRequest.


        :param alarm_type: The alarm_type of this HandleAlarmByAgentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and alarm_type is None:
            raise ValueError("Invalid value for `alarm_type`, must not be `None`")  # noqa: E501
        allowed_values = ["hids", "virus"]  # noqa: E501
        if (self._configuration.client_side_validation and
                alarm_type not in allowed_values):
            raise ValueError(
                "Invalid value for `alarm_type` ({0}), must be one of {1}"  # noqa: E501
                .format(alarm_type, allowed_values)
            )

        self._alarm_type = alarm_type

    @property
    def files(self):
        """Gets the files of this HandleAlarmByAgentRequest.  # noqa: E501


        :return: The files of this HandleAlarmByAgentRequest.  # noqa: E501
        :rtype: list[FileForHandleAlarmByAgentInput]
        """
        return self._files

    @files.setter
    def files(self, files):
        """Sets the files of this HandleAlarmByAgentRequest.


        :param files: The files of this HandleAlarmByAgentRequest.  # noqa: E501
        :type: list[FileForHandleAlarmByAgentInput]
        """

        self._files = files

    @property
    def processes(self):
        """Gets the processes of this HandleAlarmByAgentRequest.  # noqa: E501


        :return: The processes of this HandleAlarmByAgentRequest.  # noqa: E501
        :rtype: list[ProcessForHandleAlarmByAgentInput]
        """
        return self._processes

    @processes.setter
    def processes(self, processes):
        """Sets the processes of this HandleAlarmByAgentRequest.


        :param processes: The processes of this HandleAlarmByAgentRequest.  # noqa: E501
        :type: list[ProcessForHandleAlarmByAgentInput]
        """

        self._processes = processes

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HandleAlarmByAgentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HandleAlarmByAgentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HandleAlarmByAgentRequest):
            return True

        return self.to_dict() != other.to_dict()
