# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAlarmTraceRawDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_id': 'list[str]',
        'data_type': 'list[str]',
        'origin_data_id': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'rasp_data_type': 'list[str]',
        'sort_by': 'str',
        'sort_order': 'str',
        'trace_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'data_id': 'DataID',
        'data_type': 'DataType',
        'origin_data_id': 'OriginDataID',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'rasp_data_type': 'RaspDataType',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'trace_id': 'TraceID',
        'type': 'Type'
    }

    def __init__(self, data_id=None, data_type=None, origin_data_id=None, page_number=None, page_size=None, rasp_data_type=None, sort_by=None, sort_order=None, trace_id=None, type=None, _configuration=None):  # noqa: E501
        """GetAlarmTraceRawDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_id = None
        self._data_type = None
        self._origin_data_id = None
        self._page_number = None
        self._page_size = None
        self._rasp_data_type = None
        self._sort_by = None
        self._sort_order = None
        self._trace_id = None
        self._type = None
        self.discriminator = None

        if data_id is not None:
            self.data_id = data_id
        if data_type is not None:
            self.data_type = data_type
        if origin_data_id is not None:
            self.origin_data_id = origin_data_id
        self.page_number = page_number
        self.page_size = page_size
        if rasp_data_type is not None:
            self.rasp_data_type = rasp_data_type
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if trace_id is not None:
            self.trace_id = trace_id
        if type is not None:
            self.type = type

    @property
    def data_id(self):
        """Gets the data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_id

    @data_id.setter
    def data_id(self, data_id):
        """Sets the data_id of this GetAlarmTraceRawDataRequest.


        :param data_id: The data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._data_id = data_id

    @property
    def data_type(self):
        """Gets the data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this GetAlarmTraceRawDataRequest.


        :param data_type: The data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._data_type = data_type

    @property
    def origin_data_id(self):
        """Gets the origin_data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The origin_data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._origin_data_id

    @origin_data_id.setter
    def origin_data_id(self, origin_data_id):
        """Sets the origin_data_id of this GetAlarmTraceRawDataRequest.


        :param origin_data_id: The origin_data_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._origin_data_id = origin_data_id

    @property
    def page_number(self):
        """Gets the page_number of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The page_number of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetAlarmTraceRawDataRequest.


        :param page_number: The page_number of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The page_size of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetAlarmTraceRawDataRequest.


        :param page_size: The page_size of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def rasp_data_type(self):
        """Gets the rasp_data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The rasp_data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._rasp_data_type

    @rasp_data_type.setter
    def rasp_data_type(self, rasp_data_type):
        """Sets the rasp_data_type of this GetAlarmTraceRawDataRequest.


        :param rasp_data_type: The rasp_data_type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._rasp_data_type = rasp_data_type

    @property
    def sort_by(self):
        """Gets the sort_by of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The sort_by of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this GetAlarmTraceRawDataRequest.


        :param sort_by: The sort_by of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The sort_order of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this GetAlarmTraceRawDataRequest.


        :param sort_order: The sort_order of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def trace_id(self):
        """Gets the trace_id of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The trace_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this GetAlarmTraceRawDataRequest.


        :param trace_id: The trace_id of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    @property
    def type(self):
        """Gets the type of this GetAlarmTraceRawDataRequest.  # noqa: E501


        :return: The type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this GetAlarmTraceRawDataRequest.


        :param type: The type of this GetAlarmTraceRawDataRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAlarmTraceRawDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAlarmTraceRawDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAlarmTraceRawDataRequest):
            return True

        return self.to_dict() != other.to_dict()
