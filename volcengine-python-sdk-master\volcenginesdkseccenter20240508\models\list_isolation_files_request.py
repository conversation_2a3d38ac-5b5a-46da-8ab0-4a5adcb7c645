# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListIsolationFilesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'auto_isolate': 'bool',
        'cloud_providers': 'list[str]',
        'file_path': 'str',
        'filebox_id': 'str',
        'hostname': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[int]',
        'time_end': 'int',
        'time_start': 'int',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'auto_isolate': 'AutoIsolate',
        'cloud_providers': 'CloudProviders',
        'file_path': 'FilePath',
        'filebox_id': 'FileboxID',
        'hostname': 'Hostname',
        'ip': 'IP',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'time_end': 'TimeEnd',
        'time_start': 'TimeStart',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, auto_isolate=None, cloud_providers=None, file_path=None, filebox_id=None, hostname=None, ip=None, leaf_group_ids=None, page_number=None, page_size=None, sort_by=None, sort_order=None, status=None, time_end=None, time_start=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListIsolationFilesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._auto_isolate = None
        self._cloud_providers = None
        self._file_path = None
        self._filebox_id = None
        self._hostname = None
        self._ip = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._time_end = None
        self._time_start = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if auto_isolate is not None:
            self.auto_isolate = auto_isolate
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if file_path is not None:
            self.file_path = file_path
        if filebox_id is not None:
            self.filebox_id = filebox_id
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if time_end is not None:
            self.time_end = time_end
        if time_start is not None:
            self.time_start = time_start
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ListIsolationFilesRequest.  # noqa: E501


        :return: The agent_id of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListIsolationFilesRequest.


        :param agent_id: The agent_id of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def auto_isolate(self):
        """Gets the auto_isolate of this ListIsolationFilesRequest.  # noqa: E501


        :return: The auto_isolate of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_isolate

    @auto_isolate.setter
    def auto_isolate(self, auto_isolate):
        """Sets the auto_isolate of this ListIsolationFilesRequest.


        :param auto_isolate: The auto_isolate of this ListIsolationFilesRequest.  # noqa: E501
        :type: bool
        """

        self._auto_isolate = auto_isolate

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListIsolationFilesRequest.  # noqa: E501


        :return: The cloud_providers of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListIsolationFilesRequest.


        :param cloud_providers: The cloud_providers of this ListIsolationFilesRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def file_path(self):
        """Gets the file_path of this ListIsolationFilesRequest.  # noqa: E501


        :return: The file_path of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this ListIsolationFilesRequest.


        :param file_path: The file_path of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def filebox_id(self):
        """Gets the filebox_id of this ListIsolationFilesRequest.  # noqa: E501


        :return: The filebox_id of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._filebox_id

    @filebox_id.setter
    def filebox_id(self, filebox_id):
        """Sets the filebox_id of this ListIsolationFilesRequest.


        :param filebox_id: The filebox_id of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._filebox_id = filebox_id

    @property
    def hostname(self):
        """Gets the hostname of this ListIsolationFilesRequest.  # noqa: E501


        :return: The hostname of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ListIsolationFilesRequest.


        :param hostname: The hostname of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ListIsolationFilesRequest.  # noqa: E501


        :return: The ip of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListIsolationFilesRequest.


        :param ip: The ip of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListIsolationFilesRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListIsolationFilesRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListIsolationFilesRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this ListIsolationFilesRequest.  # noqa: E501


        :return: The page_number of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListIsolationFilesRequest.


        :param page_number: The page_number of this ListIsolationFilesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListIsolationFilesRequest.  # noqa: E501


        :return: The page_size of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListIsolationFilesRequest.


        :param page_size: The page_size of this ListIsolationFilesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListIsolationFilesRequest.  # noqa: E501


        :return: The sort_by of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListIsolationFilesRequest.


        :param sort_by: The sort_by of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListIsolationFilesRequest.  # noqa: E501


        :return: The sort_order of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListIsolationFilesRequest.


        :param sort_order: The sort_order of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListIsolationFilesRequest.  # noqa: E501


        :return: The status of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListIsolationFilesRequest.


        :param status: The status of this ListIsolationFilesRequest.  # noqa: E501
        :type: list[int]
        """

        self._status = status

    @property
    def time_end(self):
        """Gets the time_end of this ListIsolationFilesRequest.  # noqa: E501


        :return: The time_end of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: int
        """
        return self._time_end

    @time_end.setter
    def time_end(self, time_end):
        """Sets the time_end of this ListIsolationFilesRequest.


        :param time_end: The time_end of this ListIsolationFilesRequest.  # noqa: E501
        :type: int
        """

        self._time_end = time_end

    @property
    def time_start(self):
        """Gets the time_start of this ListIsolationFilesRequest.  # noqa: E501


        :return: The time_start of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: int
        """
        return self._time_start

    @time_start.setter
    def time_start(self, time_start):
        """Sets the time_start of this ListIsolationFilesRequest.


        :param time_start: The time_start of this ListIsolationFilesRequest.  # noqa: E501
        :type: int
        """

        self._time_start = time_start

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListIsolationFilesRequest.  # noqa: E501


        :return: The top_group_id of this ListIsolationFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListIsolationFilesRequest.


        :param top_group_id: The top_group_id of this ListIsolationFilesRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListIsolationFilesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListIsolationFilesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListIsolationFilesRequest):
            return True

        return self.to_dict() != other.to_dict()
