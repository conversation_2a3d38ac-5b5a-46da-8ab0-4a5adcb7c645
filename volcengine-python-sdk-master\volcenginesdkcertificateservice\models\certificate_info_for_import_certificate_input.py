# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateInfoForImportCertificateInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_chain': 'str',
        'private_key': 'str'
    }

    attribute_map = {
        'certificate_chain': 'CertificateChain',
        'private_key': 'PrivateKey'
    }

    def __init__(self, certificate_chain=None, private_key=None, _configuration=None):  # noqa: E501
        """CertificateInfoForImportCertificateInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_chain = None
        self._private_key = None
        self.discriminator = None

        if certificate_chain is not None:
            self.certificate_chain = certificate_chain
        if private_key is not None:
            self.private_key = private_key

    @property
    def certificate_chain(self):
        """Gets the certificate_chain of this CertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The certificate_chain of this CertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_chain

    @certificate_chain.setter
    def certificate_chain(self, certificate_chain):
        """Sets the certificate_chain of this CertificateInfoForImportCertificateInput.


        :param certificate_chain: The certificate_chain of this CertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._certificate_chain = certificate_chain

    @property
    def private_key(self):
        """Gets the private_key of this CertificateInfoForImportCertificateInput.  # noqa: E501


        :return: The private_key of this CertificateInfoForImportCertificateInput.  # noqa: E501
        :rtype: str
        """
        return self._private_key

    @private_key.setter
    def private_key(self, private_key):
        """Sets the private_key of this CertificateInfoForImportCertificateInput.


        :param private_key: The private_key of this CertificateInfoForImportCertificateInput.  # noqa: E501
        :type: str
        """

        self._private_key = private_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateInfoForImportCertificateInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateInfoForImportCertificateInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateInfoForImportCertificateInput):
            return True

        return self.to_dict() != other.to_dict()
