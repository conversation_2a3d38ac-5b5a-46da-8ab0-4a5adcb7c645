# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeKeyScanJobsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'job_list': 'list[JobListForDescribeKeyScanJobsOutput]',
        'total_jobs_number': 'int'
    }

    attribute_map = {
        'job_list': 'JobList',
        'total_jobs_number': 'TotalJobsNumber'
    }

    def __init__(self, job_list=None, total_jobs_number=None, _configuration=None):  # noqa: E501
        """DescribeKeyScanJobsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._job_list = None
        self._total_jobs_number = None
        self.discriminator = None

        if job_list is not None:
            self.job_list = job_list
        if total_jobs_number is not None:
            self.total_jobs_number = total_jobs_number

    @property
    def job_list(self):
        """Gets the job_list of this DescribeKeyScanJobsResponse.  # noqa: E501


        :return: The job_list of this DescribeKeyScanJobsResponse.  # noqa: E501
        :rtype: list[JobListForDescribeKeyScanJobsOutput]
        """
        return self._job_list

    @job_list.setter
    def job_list(self, job_list):
        """Sets the job_list of this DescribeKeyScanJobsResponse.


        :param job_list: The job_list of this DescribeKeyScanJobsResponse.  # noqa: E501
        :type: list[JobListForDescribeKeyScanJobsOutput]
        """

        self._job_list = job_list

    @property
    def total_jobs_number(self):
        """Gets the total_jobs_number of this DescribeKeyScanJobsResponse.  # noqa: E501


        :return: The total_jobs_number of this DescribeKeyScanJobsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_jobs_number

    @total_jobs_number.setter
    def total_jobs_number(self, total_jobs_number):
        """Sets the total_jobs_number of this DescribeKeyScanJobsResponse.


        :param total_jobs_number: The total_jobs_number of this DescribeKeyScanJobsResponse.  # noqa: E501
        :type: int
        """

        self._total_jobs_number = total_jobs_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeKeyScanJobsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeKeyScanJobsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeKeyScanJobsResponse):
            return True

        return self.to_dict() != other.to_dict()
