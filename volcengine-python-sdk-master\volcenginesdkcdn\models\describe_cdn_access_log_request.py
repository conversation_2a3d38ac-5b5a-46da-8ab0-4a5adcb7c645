# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnAccessLogRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'end_time': 'int',
        'page_num': 'int',
        'page_size': 'int',
        'service_region': 'str',
        'start_time': 'int'
    }

    attribute_map = {
        'domain': 'Domain',
        'end_time': 'EndTime',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'service_region': 'ServiceRegion',
        'start_time': 'StartTime'
    }

    def __init__(self, domain=None, end_time=None, page_num=None, page_size=None, service_region=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeCdnAccessLogRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._end_time = None
        self._page_num = None
        self._page_size = None
        self._service_region = None
        self._start_time = None
        self.discriminator = None

        self.domain = domain
        self.end_time = end_time
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if service_region is not None:
            self.service_region = service_region
        self.start_time = start_time

    @property
    def domain(self):
        """Gets the domain of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The domain of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeCdnAccessLogRequest.


        :param domain: The domain of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def end_time(self):
        """Gets the end_time of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The end_time of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeCdnAccessLogRequest.


        :param end_time: The end_time of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def page_num(self):
        """Gets the page_num of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The page_num of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this DescribeCdnAccessLogRequest.


        :param page_num: The page_num of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The page_size of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeCdnAccessLogRequest.


        :param page_size: The page_size of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def service_region(self):
        """Gets the service_region of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The service_region of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_region

    @service_region.setter
    def service_region(self, service_region):
        """Sets the service_region of this DescribeCdnAccessLogRequest.


        :param service_region: The service_region of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: str
        """

        self._service_region = service_region

    @property
    def start_time(self):
        """Gets the start_time of this DescribeCdnAccessLogRequest.  # noqa: E501


        :return: The start_time of this DescribeCdnAccessLogRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeCdnAccessLogRequest.


        :param start_time: The start_time of this DescribeCdnAccessLogRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnAccessLogRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnAccessLogRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnAccessLogRequest):
            return True

        return self.to_dict() != other.to_dict()
