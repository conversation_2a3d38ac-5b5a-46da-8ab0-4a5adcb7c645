# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EventForListGatewaysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'str',
        'create_time': 'str',
        'data': 'DataForListGatewaysOutput',
        'description': 'str'
    }

    attribute_map = {
        'code': 'Code',
        'create_time': 'CreateTime',
        'data': 'Data',
        'description': 'Description'
    }

    def __init__(self, code=None, create_time=None, data=None, description=None, _configuration=None):  # noqa: E501
        """EventForListGatewaysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code = None
        self._create_time = None
        self._data = None
        self._description = None
        self.discriminator = None

        if code is not None:
            self.code = code
        if create_time is not None:
            self.create_time = create_time
        if data is not None:
            self.data = data
        if description is not None:
            self.description = description

    @property
    def code(self):
        """Gets the code of this EventForListGatewaysOutput.  # noqa: E501


        :return: The code of this EventForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this EventForListGatewaysOutput.


        :param code: The code of this EventForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._code = code

    @property
    def create_time(self):
        """Gets the create_time of this EventForListGatewaysOutput.  # noqa: E501


        :return: The create_time of this EventForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this EventForListGatewaysOutput.


        :param create_time: The create_time of this EventForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def data(self):
        """Gets the data of this EventForListGatewaysOutput.  # noqa: E501


        :return: The data of this EventForListGatewaysOutput.  # noqa: E501
        :rtype: DataForListGatewaysOutput
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this EventForListGatewaysOutput.


        :param data: The data of this EventForListGatewaysOutput.  # noqa: E501
        :type: DataForListGatewaysOutput
        """

        self._data = data

    @property
    def description(self):
        """Gets the description of this EventForListGatewaysOutput.  # noqa: E501


        :return: The description of this EventForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this EventForListGatewaysOutput.


        :param description: The description of this EventForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EventForListGatewaysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EventForListGatewaysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EventForListGatewaysOutput):
            return True

        return self.to_dict() != other.to_dict()
