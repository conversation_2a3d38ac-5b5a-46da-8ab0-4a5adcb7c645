# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RateLimitForGetEndpointOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'rpm': 'int',
        'tpm': 'int'
    }

    attribute_map = {
        'rpm': 'Rpm',
        'tpm': 'Tpm'
    }

    def __init__(self, rpm=None, tpm=None, _configuration=None):  # noqa: E501
        """RateLimitForGetEndpointOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._rpm = None
        self._tpm = None
        self.discriminator = None

        if rpm is not None:
            self.rpm = rpm
        if tpm is not None:
            self.tpm = tpm

    @property
    def rpm(self):
        """Gets the rpm of this RateLimitForGetEndpointOutput.  # noqa: E501


        :return: The rpm of this RateLimitForGetEndpointOutput.  # noqa: E501
        :rtype: int
        """
        return self._rpm

    @rpm.setter
    def rpm(self, rpm):
        """Sets the rpm of this RateLimitForGetEndpointOutput.


        :param rpm: The rpm of this RateLimitForGetEndpointOutput.  # noqa: E501
        :type: int
        """

        self._rpm = rpm

    @property
    def tpm(self):
        """Gets the tpm of this RateLimitForGetEndpointOutput.  # noqa: E501


        :return: The tpm of this RateLimitForGetEndpointOutput.  # noqa: E501
        :rtype: int
        """
        return self._tpm

    @tpm.setter
    def tpm(self, tpm):
        """Sets the tpm of this RateLimitForGetEndpointOutput.


        :param tpm: The tpm of this RateLimitForGetEndpointOutput.  # noqa: E501
        :type: int
        """

        self._tpm = tpm

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RateLimitForGetEndpointOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RateLimitForGetEndpointOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RateLimitForGetEndpointOutput):
            return True

        return self.to_dict() != other.to_dict()
