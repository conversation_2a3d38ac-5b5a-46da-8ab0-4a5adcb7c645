# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccelerateIPForListBasicAccelerateIPsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerate_ip_address': 'str',
        'accelerate_ipid': 'str',
        'accelerator_id': 'str',
        'binding_mode': 'str',
        'edge_node_alias': 'str',
        'edge_node_name': 'str',
        'endpoints': 'list[EndpointForListBasicAccelerateIPsOutput]',
        'ip_set_id': 'str',
        'isp': 'str',
        'state': 'str'
    }

    attribute_map = {
        'accelerate_ip_address': 'AccelerateIPAddress',
        'accelerate_ipid': 'AccelerateIPId',
        'accelerator_id': 'AcceleratorId',
        'binding_mode': 'BindingMode',
        'edge_node_alias': 'EdgeNodeAlias',
        'edge_node_name': 'EdgeNodeName',
        'endpoints': 'Endpoints',
        'ip_set_id': 'IPSetId',
        'isp': 'ISP',
        'state': 'State'
    }

    def __init__(self, accelerate_ip_address=None, accelerate_ipid=None, accelerator_id=None, binding_mode=None, edge_node_alias=None, edge_node_name=None, endpoints=None, ip_set_id=None, isp=None, state=None, _configuration=None):  # noqa: E501
        """AccelerateIPForListBasicAccelerateIPsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerate_ip_address = None
        self._accelerate_ipid = None
        self._accelerator_id = None
        self._binding_mode = None
        self._edge_node_alias = None
        self._edge_node_name = None
        self._endpoints = None
        self._ip_set_id = None
        self._isp = None
        self._state = None
        self.discriminator = None

        if accelerate_ip_address is not None:
            self.accelerate_ip_address = accelerate_ip_address
        if accelerate_ipid is not None:
            self.accelerate_ipid = accelerate_ipid
        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if binding_mode is not None:
            self.binding_mode = binding_mode
        if edge_node_alias is not None:
            self.edge_node_alias = edge_node_alias
        if edge_node_name is not None:
            self.edge_node_name = edge_node_name
        if endpoints is not None:
            self.endpoints = endpoints
        if ip_set_id is not None:
            self.ip_set_id = ip_set_id
        if isp is not None:
            self.isp = isp
        if state is not None:
            self.state = state

    @property
    def accelerate_ip_address(self):
        """Gets the accelerate_ip_address of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The accelerate_ip_address of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_ip_address

    @accelerate_ip_address.setter
    def accelerate_ip_address(self, accelerate_ip_address):
        """Sets the accelerate_ip_address of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param accelerate_ip_address: The accelerate_ip_address of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._accelerate_ip_address = accelerate_ip_address

    @property
    def accelerate_ipid(self):
        """Gets the accelerate_ipid of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The accelerate_ipid of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_ipid

    @accelerate_ipid.setter
    def accelerate_ipid(self, accelerate_ipid):
        """Sets the accelerate_ipid of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param accelerate_ipid: The accelerate_ipid of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._accelerate_ipid = accelerate_ipid

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The accelerator_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param accelerator_id: The accelerator_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def binding_mode(self):
        """Gets the binding_mode of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The binding_mode of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._binding_mode

    @binding_mode.setter
    def binding_mode(self, binding_mode):
        """Sets the binding_mode of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param binding_mode: The binding_mode of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._binding_mode = binding_mode

    @property
    def edge_node_alias(self):
        """Gets the edge_node_alias of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The edge_node_alias of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._edge_node_alias

    @edge_node_alias.setter
    def edge_node_alias(self, edge_node_alias):
        """Sets the edge_node_alias of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param edge_node_alias: The edge_node_alias of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._edge_node_alias = edge_node_alias

    @property
    def edge_node_name(self):
        """Gets the edge_node_name of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The edge_node_name of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._edge_node_name

    @edge_node_name.setter
    def edge_node_name(self, edge_node_name):
        """Sets the edge_node_name of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param edge_node_name: The edge_node_name of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._edge_node_name = edge_node_name

    @property
    def endpoints(self):
        """Gets the endpoints of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The endpoints of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: list[EndpointForListBasicAccelerateIPsOutput]
        """
        return self._endpoints

    @endpoints.setter
    def endpoints(self, endpoints):
        """Sets the endpoints of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param endpoints: The endpoints of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: list[EndpointForListBasicAccelerateIPsOutput]
        """

        self._endpoints = endpoints

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The ip_set_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param ip_set_id: The ip_set_id of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._ip_set_id = ip_set_id

    @property
    def isp(self):
        """Gets the isp of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The isp of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param isp: The isp of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def state(self):
        """Gets the state of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501


        :return: The state of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this AccelerateIPForListBasicAccelerateIPsOutput.


        :param state: The state of this AccelerateIPForListBasicAccelerateIPsOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccelerateIPForListBasicAccelerateIPsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccelerateIPForListBasicAccelerateIPsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccelerateIPForListBasicAccelerateIPsOutput):
            return True

        return self.to_dict() != other.to_dict()
