# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RoleForListRolesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'cluster_id': 'int',
        'cluster_name': 'str',
        'created_at': 'int',
        'created_by': 'str',
        'description': 'str',
        'grant_users': 'list[str]',
        'id': 'int',
        'is_default': 'bool',
        'name': 'str',
        'updated_at': 'int',
        'updated_by': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'created_at': 'CreatedAt',
        'created_by': 'CreatedBy',
        'description': 'Description',
        'grant_users': 'GrantUsers',
        'id': 'ID',
        'is_default': 'IsDefault',
        'name': 'Name',
        'updated_at': 'UpdatedAt',
        'updated_by': 'UpdatedBy'
    }

    def __init__(self, account_id=None, cluster_id=None, cluster_name=None, created_at=None, created_by=None, description=None, grant_users=None, id=None, is_default=None, name=None, updated_at=None, updated_by=None, _configuration=None):  # noqa: E501
        """RoleForListRolesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._cluster_id = None
        self._cluster_name = None
        self._created_at = None
        self._created_by = None
        self._description = None
        self._grant_users = None
        self._id = None
        self._is_default = None
        self._name = None
        self._updated_at = None
        self._updated_by = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if created_at is not None:
            self.created_at = created_at
        if created_by is not None:
            self.created_by = created_by
        if description is not None:
            self.description = description
        if grant_users is not None:
            self.grant_users = grant_users
        if id is not None:
            self.id = id
        if is_default is not None:
            self.is_default = is_default
        if name is not None:
            self.name = name
        if updated_at is not None:
            self.updated_at = updated_at
        if updated_by is not None:
            self.updated_by = updated_by

    @property
    def account_id(self):
        """Gets the account_id of this RoleForListRolesOutput.  # noqa: E501


        :return: The account_id of this RoleForListRolesOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this RoleForListRolesOutput.


        :param account_id: The account_id of this RoleForListRolesOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def cluster_id(self):
        """Gets the cluster_id of this RoleForListRolesOutput.  # noqa: E501


        :return: The cluster_id of this RoleForListRolesOutput.  # noqa: E501
        :rtype: int
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this RoleForListRolesOutput.


        :param cluster_id: The cluster_id of this RoleForListRolesOutput.  # noqa: E501
        :type: int
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this RoleForListRolesOutput.  # noqa: E501


        :return: The cluster_name of this RoleForListRolesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this RoleForListRolesOutput.


        :param cluster_name: The cluster_name of this RoleForListRolesOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def created_at(self):
        """Gets the created_at of this RoleForListRolesOutput.  # noqa: E501


        :return: The created_at of this RoleForListRolesOutput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this RoleForListRolesOutput.


        :param created_at: The created_at of this RoleForListRolesOutput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def created_by(self):
        """Gets the created_by of this RoleForListRolesOutput.  # noqa: E501


        :return: The created_by of this RoleForListRolesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this RoleForListRolesOutput.


        :param created_by: The created_by of this RoleForListRolesOutput.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def description(self):
        """Gets the description of this RoleForListRolesOutput.  # noqa: E501


        :return: The description of this RoleForListRolesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this RoleForListRolesOutput.


        :param description: The description of this RoleForListRolesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def grant_users(self):
        """Gets the grant_users of this RoleForListRolesOutput.  # noqa: E501


        :return: The grant_users of this RoleForListRolesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._grant_users

    @grant_users.setter
    def grant_users(self, grant_users):
        """Sets the grant_users of this RoleForListRolesOutput.


        :param grant_users: The grant_users of this RoleForListRolesOutput.  # noqa: E501
        :type: list[str]
        """

        self._grant_users = grant_users

    @property
    def id(self):
        """Gets the id of this RoleForListRolesOutput.  # noqa: E501


        :return: The id of this RoleForListRolesOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this RoleForListRolesOutput.


        :param id: The id of this RoleForListRolesOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_default(self):
        """Gets the is_default of this RoleForListRolesOutput.  # noqa: E501


        :return: The is_default of this RoleForListRolesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_default

    @is_default.setter
    def is_default(self, is_default):
        """Sets the is_default of this RoleForListRolesOutput.


        :param is_default: The is_default of this RoleForListRolesOutput.  # noqa: E501
        :type: bool
        """

        self._is_default = is_default

    @property
    def name(self):
        """Gets the name of this RoleForListRolesOutput.  # noqa: E501


        :return: The name of this RoleForListRolesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RoleForListRolesOutput.


        :param name: The name of this RoleForListRolesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def updated_at(self):
        """Gets the updated_at of this RoleForListRolesOutput.  # noqa: E501


        :return: The updated_at of this RoleForListRolesOutput.  # noqa: E501
        :rtype: int
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this RoleForListRolesOutput.


        :param updated_at: The updated_at of this RoleForListRolesOutput.  # noqa: E501
        :type: int
        """

        self._updated_at = updated_at

    @property
    def updated_by(self):
        """Gets the updated_by of this RoleForListRolesOutput.  # noqa: E501


        :return: The updated_by of this RoleForListRolesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_by

    @updated_by.setter
    def updated_by(self, updated_by):
        """Sets the updated_by of this RoleForListRolesOutput.


        :param updated_by: The updated_by of this RoleForListRolesOutput.  # noqa: E501
        :type: str
        """

        self._updated_by = updated_by

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RoleForListRolesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RoleForListRolesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RoleForListRolesOutput):
            return True

        return self.to_dict() != other.to_dict()
