# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRepoImageLayerOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_by': 'str',
        'created_time': 'str',
        'id': 'str',
        'layer_id': 'str',
        'senfile_cnt': 'int',
        'senfile_distr': 'SenfileDistrForListRepoImageLayerOutput',
        'sort': 'int',
        'virus_cnt': 'int',
        'vuln_cnt': 'int',
        'vuln_distr': 'VulnDistrForListRepoImageLayerOutput'
    }

    attribute_map = {
        'created_by': 'CreatedBy',
        'created_time': 'CreatedTime',
        'id': 'ID',
        'layer_id': 'LayerID',
        'senfile_cnt': 'SenfileCnt',
        'senfile_distr': 'SenfileDistr',
        'sort': 'Sort',
        'virus_cnt': 'VirusCnt',
        'vuln_cnt': 'VulnCnt',
        'vuln_distr': 'VulnDistr'
    }

    def __init__(self, created_by=None, created_time=None, id=None, layer_id=None, senfile_cnt=None, senfile_distr=None, sort=None, virus_cnt=None, vuln_cnt=None, vuln_distr=None, _configuration=None):  # noqa: E501
        """DataForListRepoImageLayerOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_by = None
        self._created_time = None
        self._id = None
        self._layer_id = None
        self._senfile_cnt = None
        self._senfile_distr = None
        self._sort = None
        self._virus_cnt = None
        self._vuln_cnt = None
        self._vuln_distr = None
        self.discriminator = None

        if created_by is not None:
            self.created_by = created_by
        if created_time is not None:
            self.created_time = created_time
        if id is not None:
            self.id = id
        if layer_id is not None:
            self.layer_id = layer_id
        if senfile_cnt is not None:
            self.senfile_cnt = senfile_cnt
        if senfile_distr is not None:
            self.senfile_distr = senfile_distr
        if sort is not None:
            self.sort = sort
        if virus_cnt is not None:
            self.virus_cnt = virus_cnt
        if vuln_cnt is not None:
            self.vuln_cnt = vuln_cnt
        if vuln_distr is not None:
            self.vuln_distr = vuln_distr

    @property
    def created_by(self):
        """Gets the created_by of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The created_by of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this DataForListRepoImageLayerOutput.


        :param created_by: The created_by of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def created_time(self):
        """Gets the created_time of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The created_time of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this DataForListRepoImageLayerOutput.


        :param created_time: The created_time of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def id(self):
        """Gets the id of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The id of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRepoImageLayerOutput.


        :param id: The id of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def layer_id(self):
        """Gets the layer_id of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The layer_id of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: str
        """
        return self._layer_id

    @layer_id.setter
    def layer_id(self, layer_id):
        """Sets the layer_id of this DataForListRepoImageLayerOutput.


        :param layer_id: The layer_id of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: str
        """

        self._layer_id = layer_id

    @property
    def senfile_cnt(self):
        """Gets the senfile_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The senfile_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: int
        """
        return self._senfile_cnt

    @senfile_cnt.setter
    def senfile_cnt(self, senfile_cnt):
        """Sets the senfile_cnt of this DataForListRepoImageLayerOutput.


        :param senfile_cnt: The senfile_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: int
        """

        self._senfile_cnt = senfile_cnt

    @property
    def senfile_distr(self):
        """Gets the senfile_distr of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The senfile_distr of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: SenfileDistrForListRepoImageLayerOutput
        """
        return self._senfile_distr

    @senfile_distr.setter
    def senfile_distr(self, senfile_distr):
        """Sets the senfile_distr of this DataForListRepoImageLayerOutput.


        :param senfile_distr: The senfile_distr of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: SenfileDistrForListRepoImageLayerOutput
        """

        self._senfile_distr = senfile_distr

    @property
    def sort(self):
        """Gets the sort of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The sort of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: int
        """
        return self._sort

    @sort.setter
    def sort(self, sort):
        """Sets the sort of this DataForListRepoImageLayerOutput.


        :param sort: The sort of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: int
        """

        self._sort = sort

    @property
    def virus_cnt(self):
        """Gets the virus_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The virus_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: int
        """
        return self._virus_cnt

    @virus_cnt.setter
    def virus_cnt(self, virus_cnt):
        """Sets the virus_cnt of this DataForListRepoImageLayerOutput.


        :param virus_cnt: The virus_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: int
        """

        self._virus_cnt = virus_cnt

    @property
    def vuln_cnt(self):
        """Gets the vuln_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The vuln_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: int
        """
        return self._vuln_cnt

    @vuln_cnt.setter
    def vuln_cnt(self, vuln_cnt):
        """Sets the vuln_cnt of this DataForListRepoImageLayerOutput.


        :param vuln_cnt: The vuln_cnt of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: int
        """

        self._vuln_cnt = vuln_cnt

    @property
    def vuln_distr(self):
        """Gets the vuln_distr of this DataForListRepoImageLayerOutput.  # noqa: E501


        :return: The vuln_distr of this DataForListRepoImageLayerOutput.  # noqa: E501
        :rtype: VulnDistrForListRepoImageLayerOutput
        """
        return self._vuln_distr

    @vuln_distr.setter
    def vuln_distr(self, vuln_distr):
        """Sets the vuln_distr of this DataForListRepoImageLayerOutput.


        :param vuln_distr: The vuln_distr of this DataForListRepoImageLayerOutput.  # noqa: E501
        :type: VulnDistrForListRepoImageLayerOutput
        """

        self._vuln_distr = vuln_distr

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRepoImageLayerOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRepoImageLayerOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRepoImageLayerOutput):
            return True

        return self.to_dict() != other.to_dict()
