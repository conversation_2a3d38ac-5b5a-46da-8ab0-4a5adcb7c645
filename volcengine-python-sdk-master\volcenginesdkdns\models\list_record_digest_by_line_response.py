# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRecordDigestByLineResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'line': 'str',
        'record_digests': 'list[RecordDigestForListRecordDigestByLineOutput]'
    }

    attribute_map = {
        'line': 'Line',
        'record_digests': 'RecordDigests'
    }

    def __init__(self, line=None, record_digests=None, _configuration=None):  # noqa: E501
        """ListRecordDigestByLineResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._line = None
        self._record_digests = None
        self.discriminator = None

        if line is not None:
            self.line = line
        if record_digests is not None:
            self.record_digests = record_digests

    @property
    def line(self):
        """Gets the line of this ListRecordDigestByLineResponse.  # noqa: E501


        :return: The line of this ListRecordDigestByLineResponse.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this ListRecordDigestByLineResponse.


        :param line: The line of this ListRecordDigestByLineResponse.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def record_digests(self):
        """Gets the record_digests of this ListRecordDigestByLineResponse.  # noqa: E501


        :return: The record_digests of this ListRecordDigestByLineResponse.  # noqa: E501
        :rtype: list[RecordDigestForListRecordDigestByLineOutput]
        """
        return self._record_digests

    @record_digests.setter
    def record_digests(self, record_digests):
        """Sets the record_digests of this ListRecordDigestByLineResponse.


        :param record_digests: The record_digests of this ListRecordDigestByLineResponse.  # noqa: E501
        :type: list[RecordDigestForListRecordDigestByLineOutput]
        """

        self._record_digests = record_digests

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRecordDigestByLineResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRecordDigestByLineResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRecordDigestByLineResponse):
            return True

        return self.to_dict() != other.to_dict()
