# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForDownloadVulnDatasInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_tags': 'list[str]',
        'asset_name': 'str',
        'cloud_providers': 'list[str]',
        'host_name': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'status': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_tags': 'AgentTags',
        'asset_name': 'AssetName',
        'cloud_providers': 'CloudProviders',
        'host_name': 'HostName',
        'ip': 'IP',
        'leaf_group_ids': 'LeafGroupIDs',
        'status': 'Status',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_tags=None, asset_name=None, cloud_providers=None, host_name=None, ip=None, leaf_group_ids=None, status=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ConditionsForDownloadVulnDatasInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_tags = None
        self._asset_name = None
        self._cloud_providers = None
        self._host_name = None
        self._ip = None
        self._leaf_group_ids = None
        self._status = None
        self._top_group_id = None
        self.discriminator = None

        if agent_tags is not None:
            self.agent_tags = agent_tags
        if asset_name is not None:
            self.asset_name = asset_name
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if host_name is not None:
            self.host_name = host_name
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if status is not None:
            self.status = status
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The agent_tags of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this ConditionsForDownloadVulnDatasInput.


        :param agent_tags: The agent_tags of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def asset_name(self):
        """Gets the asset_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The asset_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this ConditionsForDownloadVulnDatasInput.


        :param asset_name: The asset_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The cloud_providers of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ConditionsForDownloadVulnDatasInput.


        :param cloud_providers: The cloud_providers of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def host_name(self):
        """Gets the host_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The host_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this ConditionsForDownloadVulnDatasInput.


        :param host_name: The host_name of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def ip(self):
        """Gets the ip of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The ip of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ConditionsForDownloadVulnDatasInput.


        :param ip: The ip of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The leaf_group_ids of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ConditionsForDownloadVulnDatasInput.


        :param leaf_group_ids: The leaf_group_ids of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def status(self):
        """Gets the status of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The status of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ConditionsForDownloadVulnDatasInput.


        :param status: The status of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ConditionsForDownloadVulnDatasInput.  # noqa: E501


        :return: The top_group_id of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ConditionsForDownloadVulnDatasInput.


        :param top_group_id: The top_group_id of this ConditionsForDownloadVulnDatasInput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForDownloadVulnDatasInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForDownloadVulnDatasInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForDownloadVulnDatasInput):
            return True

        return self.to_dict() != other.to_dict()
