# coding: utf-8

"""
    rds_mysql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConnectionInfoForDescribeDBInstanceConnectionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_read_only': 'str',
        'enable_read_write_splitting': 'str',
        'internal_domain': 'str',
        'internal_port': 'str',
        'public_domain': 'str',
        'public_port': 'str'
    }

    attribute_map = {
        'enable_read_only': 'EnableReadOnly',
        'enable_read_write_splitting': 'EnableReadWriteSplitting',
        'internal_domain': 'InternalDomain',
        'internal_port': 'InternalPort',
        'public_domain': 'PublicDomain',
        'public_port': 'PublicPort'
    }

    def __init__(self, enable_read_only=None, enable_read_write_splitting=None, internal_domain=None, internal_port=None, public_domain=None, public_port=None, _configuration=None):  # noqa: E501
        """ConnectionInfoForDescribeDBInstanceConnectionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_read_only = None
        self._enable_read_write_splitting = None
        self._internal_domain = None
        self._internal_port = None
        self._public_domain = None
        self._public_port = None
        self.discriminator = None

        if enable_read_only is not None:
            self.enable_read_only = enable_read_only
        if enable_read_write_splitting is not None:
            self.enable_read_write_splitting = enable_read_write_splitting
        if internal_domain is not None:
            self.internal_domain = internal_domain
        if internal_port is not None:
            self.internal_port = internal_port
        if public_domain is not None:
            self.public_domain = public_domain
        if public_port is not None:
            self.public_port = public_port

    @property
    def enable_read_only(self):
        """Gets the enable_read_only of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The enable_read_only of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_read_only

    @enable_read_only.setter
    def enable_read_only(self, enable_read_only):
        """Sets the enable_read_only of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param enable_read_only: The enable_read_only of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Disable", "Enable"]  # noqa: E501
        if (self._configuration.client_side_validation and
                enable_read_only not in allowed_values):
            raise ValueError(
                "Invalid value for `enable_read_only` ({0}), must be one of {1}"  # noqa: E501
                .format(enable_read_only, allowed_values)
            )

        self._enable_read_only = enable_read_only

    @property
    def enable_read_write_splitting(self):
        """Gets the enable_read_write_splitting of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The enable_read_write_splitting of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_read_write_splitting

    @enable_read_write_splitting.setter
    def enable_read_write_splitting(self, enable_read_write_splitting):
        """Sets the enable_read_write_splitting of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param enable_read_write_splitting: The enable_read_write_splitting of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Disable", "Enable"]  # noqa: E501
        if (self._configuration.client_side_validation and
                enable_read_write_splitting not in allowed_values):
            raise ValueError(
                "Invalid value for `enable_read_write_splitting` ({0}), must be one of {1}"  # noqa: E501
                .format(enable_read_write_splitting, allowed_values)
            )

        self._enable_read_write_splitting = enable_read_write_splitting

    @property
    def internal_domain(self):
        """Gets the internal_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The internal_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._internal_domain

    @internal_domain.setter
    def internal_domain(self, internal_domain):
        """Sets the internal_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param internal_domain: The internal_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """

        self._internal_domain = internal_domain

    @property
    def internal_port(self):
        """Gets the internal_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The internal_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._internal_port

    @internal_port.setter
    def internal_port(self, internal_port):
        """Sets the internal_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param internal_port: The internal_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """

        self._internal_port = internal_port

    @property
    def public_domain(self):
        """Gets the public_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The public_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_domain

    @public_domain.setter
    def public_domain(self, public_domain):
        """Sets the public_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param public_domain: The public_domain of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """

        self._public_domain = public_domain

    @property
    def public_port(self):
        """Gets the public_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501


        :return: The public_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_port

    @public_port.setter
    def public_port(self, public_port):
        """Sets the public_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.


        :param public_port: The public_port of this ConnectionInfoForDescribeDBInstanceConnectionOutput.  # noqa: E501
        :type: str
        """

        self._public_port = public_port

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConnectionInfoForDescribeDBInstanceConnectionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConnectionInfoForDescribeDBInstanceConnectionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConnectionInfoForDescribeDBInstanceConnectionOutput):
            return True

        return self.to_dict() != other.to_dict()
