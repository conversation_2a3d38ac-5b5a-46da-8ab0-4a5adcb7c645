# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetDevFingerprintPortOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'cmdline': 'str',
        'comm': 'str',
        'id': 'str',
        'pid': 'str',
        'private_ip': 'str',
        'protocol': 'str',
        'public_ip': 'str',
        'sip': 'str',
        'sport': 'str',
        'start_time': 'int',
        'status': 'str',
        'uid': 'str',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'id': 'ID',
        'pid': 'Pid',
        'private_ip': 'PrivateIP',
        'protocol': 'Protocol',
        'public_ip': 'PublicIP',
        'sip': 'Sip',
        'sport': 'Sport',
        'start_time': 'StartTime',
        'status': 'Status',
        'uid': 'Uid',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, account_id=None, asset_id=None, asset_name=None, cmdline=None, comm=None, id=None, pid=None, private_ip=None, protocol=None, public_ip=None, sip=None, sport=None, start_time=None, status=None, uid=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetDevFingerprintPortOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asset_id = None
        self._asset_name = None
        self._cmdline = None
        self._comm = None
        self._id = None
        self._pid = None
        self._private_ip = None
        self._protocol = None
        self._public_ip = None
        self._sip = None
        self._sport = None
        self._start_time = None
        self._status = None
        self._uid = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if id is not None:
            self.id = id
        if pid is not None:
            self.pid = pid
        if private_ip is not None:
            self.private_ip = private_ip
        if protocol is not None:
            self.protocol = protocol
        if public_ip is not None:
            self.public_ip = public_ip
        if sip is not None:
            self.sip = sip
        if sport is not None:
            self.sport = sport
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if uid is not None:
            self.uid = uid
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def account_id(self):
        """Gets the account_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The account_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForGetDevFingerprintPortOutput.


        :param account_id: The account_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The asset_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForGetDevFingerprintPortOutput.


        :param asset_id: The asset_id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The asset_name of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this DataForGetDevFingerprintPortOutput.


        :param asset_name: The asset_name of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def cmdline(self):
        """Gets the cmdline of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The cmdline of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this DataForGetDevFingerprintPortOutput.


        :param cmdline: The cmdline of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The comm of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this DataForGetDevFingerprintPortOutput.


        :param comm: The comm of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def id(self):
        """Gets the id of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetDevFingerprintPortOutput.


        :param id: The id of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def pid(self):
        """Gets the pid of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The pid of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForGetDevFingerprintPortOutput.


        :param pid: The pid of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The private_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForGetDevFingerprintPortOutput.


        :param private_ip: The private_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def protocol(self):
        """Gets the protocol of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The protocol of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DataForGetDevFingerprintPortOutput.


        :param protocol: The protocol of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The public_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForGetDevFingerprintPortOutput.


        :param public_ip: The public_ip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def sip(self):
        """Gets the sip of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The sip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip

    @sip.setter
    def sip(self, sip):
        """Sets the sip of this DataForGetDevFingerprintPortOutput.


        :param sip: The sip of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._sip = sip

    @property
    def sport(self):
        """Gets the sport of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The sport of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._sport

    @sport.setter
    def sport(self, sport):
        """Sets the sport of this DataForGetDevFingerprintPortOutput.


        :param sport: The sport of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._sport = sport

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The start_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetDevFingerprintPortOutput.


        :param start_time: The start_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The status of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForGetDevFingerprintPortOutput.


        :param status: The status of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def uid(self):
        """Gets the uid of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The uid of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this DataForGetDevFingerprintPortOutput.


        :param uid: The uid of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The update_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetDevFingerprintPortOutput.


        :param update_time: The update_time of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetDevFingerprintPortOutput.  # noqa: E501


        :return: The username of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetDevFingerprintPortOutput.


        :param username: The username of this DataForGetDevFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetDevFingerprintPortOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetDevFingerprintPortOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetDevFingerprintPortOutput):
            return True

        return self.to_dict() != other.to_dict()
