# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_end_time': 'str',
        'create_start_time': 'str',
        'db_engine': 'str',
        'db_engine_version': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeDBInstancesInput]',
        'update_end_time': 'str',
        'update_start_time': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'create_end_time': 'CreateEndTime',
        'create_start_time': 'CreateStartTime',
        'db_engine': 'DBEngine',
        'db_engine_version': 'DBEngineVersion',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'update_end_time': 'UpdateEndTime',
        'update_start_time': 'UpdateStartTime',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, create_end_time=None, create_start_time=None, db_engine=None, db_engine_version=None, instance_id=None, instance_name=None, instance_status=None, instance_type=None, page_number=None, page_size=None, project_name=None, tag_filters=None, update_end_time=None, update_start_time=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeDBInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_end_time = None
        self._create_start_time = None
        self._db_engine = None
        self._db_engine_version = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_type = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self._update_end_time = None
        self._update_start_time = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if create_end_time is not None:
            self.create_end_time = create_end_time
        if create_start_time is not None:
            self.create_start_time = create_start_time
        if db_engine is not None:
            self.db_engine = db_engine
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if update_end_time is not None:
            self.update_end_time = update_end_time
        if update_start_time is not None:
            self.update_start_time = update_start_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def create_end_time(self):
        """Gets the create_end_time of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The create_end_time of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_end_time

    @create_end_time.setter
    def create_end_time(self, create_end_time):
        """Sets the create_end_time of this DescribeDBInstancesRequest.


        :param create_end_time: The create_end_time of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_end_time = create_end_time

    @property
    def create_start_time(self):
        """Gets the create_start_time of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The create_start_time of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_start_time

    @create_start_time.setter
    def create_start_time(self, create_start_time):
        """Sets the create_start_time of this DescribeDBInstancesRequest.


        :param create_start_time: The create_start_time of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_start_time = create_start_time

    @property
    def db_engine(self):
        """Gets the db_engine of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The db_engine of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_engine

    @db_engine.setter
    def db_engine(self, db_engine):
        """Sets the db_engine of this DescribeDBInstancesRequest.


        :param db_engine: The db_engine of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MongoDB"]  # noqa: E501
        if (self._configuration.client_side_validation and
                db_engine not in allowed_values):
            raise ValueError(
                "Invalid value for `db_engine` ({0}), must be one of {1}"  # noqa: E501
                .format(db_engine, allowed_values)
            )

        self._db_engine = db_engine

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this DescribeDBInstancesRequest.


        :param db_engine_version: The db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MongoDB_4_0", "MongoDB_4_2", "MongoDB_4_4", "MongoDB_5_0", "MongoDB_6_0"]  # noqa: E501
        if (self._configuration.client_side_validation and
                db_engine_version not in allowed_values):
            raise ValueError(
                "Invalid value for `db_engine_version` ({0}), must be one of {1}"  # noqa: E501
                .format(db_engine_version, allowed_values)
            )

        self._db_engine_version = db_engine_version

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_id of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBInstancesRequest.


        :param instance_id: The instance_id of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_name of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DescribeDBInstancesRequest.


        :param instance_name: The instance_name of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_status of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this DescribeDBInstancesRequest.


        :param instance_status: The instance_status of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Creating", "Running", "Deleting", "Destroying", "Restarting", "Rebuilding", "Updating", "Migrating", "Restoring", "Importing", "Error", "Scaling", "Upgrading", "Deleted", "Recycled", "Closed", "CreateFailed", "NetCreating", "NetReleasing", "WaitingPaid", "Closing", "Released", "Destroyed", "Reclaiming", "Resuming", "AllowListMaintaining", "TaskFailed_Available", "Unavailable", "NetworkMaintaining", "TDEMaintaining", "SSLUpdating", "SwitchMastering", "Temporary"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_status not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_status` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_status, allowed_values)
            )

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_type of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this DescribeDBInstancesRequest.


        :param instance_type: The instance_type of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["ReplicaSet", "ShardedCluster"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The page_number of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDBInstancesRequest.


        :param page_number: The page_number of this DescribeDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The page_size of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDBInstancesRequest.


        :param page_size: The page_size of this DescribeDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The project_name of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeDBInstancesRequest.


        :param project_name: The project_name of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The tag_filters of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeDBInstancesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeDBInstancesRequest.


        :param tag_filters: The tag_filters of this DescribeDBInstancesRequest.  # noqa: E501
        :type: list[TagFilterForDescribeDBInstancesInput]
        """

        self._tag_filters = tag_filters

    @property
    def update_end_time(self):
        """Gets the update_end_time of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The update_end_time of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_end_time

    @update_end_time.setter
    def update_end_time(self, update_end_time):
        """Sets the update_end_time of this DescribeDBInstancesRequest.


        :param update_end_time: The update_end_time of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._update_end_time = update_end_time

    @property
    def update_start_time(self):
        """Gets the update_start_time of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The update_start_time of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_start_time

    @update_start_time.setter
    def update_start_time(self, update_start_time):
        """Sets the update_start_time of this DescribeDBInstancesRequest.


        :param update_start_time: The update_start_time of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._update_start_time = update_start_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The vpc_id of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeDBInstancesRequest.


        :param vpc_id: The vpc_id of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The zone_id of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeDBInstancesRequest.


        :param zone_id: The zone_id of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
