# coding: utf-8

# flake8: noqa
"""
    quota

    No description provided (generated by <PERSON>wagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkquota.models.alarm_record_list_for_list_alarm_history_output import AlarmRecordListForListAlarmHistoryOutput
from volcenginesdkquota.models.application_for_create_quota_application_output import ApplicationForCreateQuotaApplicationOutput
from volcenginesdkquota.models.application_for_get_quota_application_output import ApplicationForGetQuotaApplicationOutput
from volcenginesdkquota.models.application_for_list_quota_applications_output import ApplicationForListQuotaApplicationsOutput
from volcenginesdkquota.models.create_alarm_rule_request import CreateAlarmRuleRequest
from volcenginesdkquota.models.create_alarm_rule_response import CreateAlarmRuleResponse
from volcenginesdkquota.models.create_quota_application_request import CreateQuotaApplicationRequest
from volcenginesdkquota.models.create_quota_application_response import CreateQuotaApplicationResponse
from volcenginesdkquota.models.create_template_quota_item_request import CreateTemplateQuotaItemRequest
from volcenginesdkquota.models.create_template_quota_item_response import CreateTemplateQuotaItemResponse
from volcenginesdkquota.models.delete_alarm_rules_request import DeleteAlarmRulesRequest
from volcenginesdkquota.models.delete_alarm_rules_response import DeleteAlarmRulesResponse
from volcenginesdkquota.models.delete_template_quota_item_request import DeleteTemplateQuotaItemRequest
from volcenginesdkquota.models.delete_template_quota_item_response import DeleteTemplateQuotaItemResponse
from volcenginesdkquota.models.dimension_for_create_alarm_rule_input import DimensionForCreateAlarmRuleInput
from volcenginesdkquota.models.dimension_for_create_quota_application_input import DimensionForCreateQuotaApplicationInput
from volcenginesdkquota.models.dimension_for_create_quota_application_output import DimensionForCreateQuotaApplicationOutput
from volcenginesdkquota.models.dimension_for_create_template_quota_item_input import DimensionForCreateTemplateQuotaItemInput
from volcenginesdkquota.models.dimension_for_create_template_quota_item_output import DimensionForCreateTemplateQuotaItemOutput
from volcenginesdkquota.models.dimension_for_delete_template_quota_item_input import DimensionForDeleteTemplateQuotaItemInput
from volcenginesdkquota.models.dimension_for_delete_template_quota_item_output import DimensionForDeleteTemplateQuotaItemOutput
from volcenginesdkquota.models.dimension_for_get_product_quota_input import DimensionForGetProductQuotaInput
from volcenginesdkquota.models.dimension_for_get_product_quota_output import DimensionForGetProductQuotaOutput
from volcenginesdkquota.models.dimension_for_get_quota_application_output import DimensionForGetQuotaApplicationOutput
from volcenginesdkquota.models.dimension_for_list_product_quota_dimensions_output import DimensionForListProductQuotaDimensionsOutput
from volcenginesdkquota.models.dimension_for_list_product_quotas_input import DimensionForListProductQuotasInput
from volcenginesdkquota.models.dimension_for_list_product_quotas_output import DimensionForListProductQuotasOutput
from volcenginesdkquota.models.dimension_for_list_quota_alarm_rules_input import DimensionForListQuotaAlarmRulesInput
from volcenginesdkquota.models.dimension_for_list_quota_application_templates_input import DimensionForListQuotaApplicationTemplatesInput
from volcenginesdkquota.models.dimension_for_list_quota_applications_input import DimensionForListQuotaApplicationsInput
from volcenginesdkquota.models.dimension_for_list_quota_applications_output import DimensionForListQuotaApplicationsOutput
from volcenginesdkquota.models.dimension_for_modify_template_quota_item_input import DimensionForModifyTemplateQuotaItemInput
from volcenginesdkquota.models.dimension_for_modify_template_quota_item_output import DimensionForModifyTemplateQuotaItemOutput
from volcenginesdkquota.models.dimension_value_for_list_product_quota_dimensions_output import DimensionValueForListProductQuotaDimensionsOutput
from volcenginesdkquota.models.dimensions_with_cn_for_list_quota_application_templates_output import DimensionsWithCnForListQuotaApplicationTemplatesOutput
from volcenginesdkquota.models.get_alarm_rule_request import GetAlarmRuleRequest
from volcenginesdkquota.models.get_alarm_rule_response import GetAlarmRuleResponse
from volcenginesdkquota.models.get_product_quota_request import GetProductQuotaRequest
from volcenginesdkquota.models.get_product_quota_response import GetProductQuotaResponse
from volcenginesdkquota.models.get_quota_application_request import GetQuotaApplicationRequest
from volcenginesdkquota.models.get_quota_application_response import GetQuotaApplicationResponse
from volcenginesdkquota.models.get_quota_template_service_status_request import GetQuotaTemplateServiceStatusRequest
from volcenginesdkquota.models.get_quota_template_service_status_response import GetQuotaTemplateServiceStatusResponse
from volcenginesdkquota.models.list_alarm_history_request import ListAlarmHistoryRequest
from volcenginesdkquota.models.list_alarm_history_response import ListAlarmHistoryResponse
from volcenginesdkquota.models.list_product_quota_dimensions_request import ListProductQuotaDimensionsRequest
from volcenginesdkquota.models.list_product_quota_dimensions_response import ListProductQuotaDimensionsResponse
from volcenginesdkquota.models.list_product_quotas_request import ListProductQuotasRequest
from volcenginesdkquota.models.list_product_quotas_response import ListProductQuotasResponse
from volcenginesdkquota.models.list_products_request import ListProductsRequest
from volcenginesdkquota.models.list_products_response import ListProductsResponse
from volcenginesdkquota.models.list_quota_alarm_rules_request import ListQuotaAlarmRulesRequest
from volcenginesdkquota.models.list_quota_alarm_rules_response import ListQuotaAlarmRulesResponse
from volcenginesdkquota.models.list_quota_application_templates_request import ListQuotaApplicationTemplatesRequest
from volcenginesdkquota.models.list_quota_application_templates_response import ListQuotaApplicationTemplatesResponse
from volcenginesdkquota.models.list_quota_applications_request import ListQuotaApplicationsRequest
from volcenginesdkquota.models.list_quota_applications_response import ListQuotaApplicationsResponse
from volcenginesdkquota.models.modify_quota_template_service_status_request import ModifyQuotaTemplateServiceStatusRequest
from volcenginesdkquota.models.modify_quota_template_service_status_response import ModifyQuotaTemplateServiceStatusResponse
from volcenginesdkquota.models.modify_template_quota_item_request import ModifyTemplateQuotaItemRequest
from volcenginesdkquota.models.modify_template_quota_item_response import ModifyTemplateQuotaItemResponse
from volcenginesdkquota.models.product_info_for_list_products_output import ProductInfoForListProductsOutput
from volcenginesdkquota.models.quota_alarm_rule_for_get_alarm_rule_output import QuotaAlarmRuleForGetAlarmRuleOutput
from volcenginesdkquota.models.quota_alarm_rule_list_for_list_quota_alarm_rules_output import QuotaAlarmRuleListForListQuotaAlarmRulesOutput
from volcenginesdkquota.models.quota_for_list_product_quotas_output import QuotaForListProductQuotasOutput
from volcenginesdkquota.models.quota_template_list_for_list_quota_application_templates_output import QuotaTemplateListForListQuotaApplicationTemplatesOutput
from volcenginesdkquota.models.quotas_for_get_product_quota_output import QuotasForGetProductQuotaOutput
from volcenginesdkquota.models.total_usage_for_get_product_quota_output import TotalUsageForGetProductQuotaOutput
from volcenginesdkquota.models.total_usage_for_list_product_quotas_output import TotalUsageForListProductQuotasOutput
from volcenginesdkquota.models.update_alarm_rule_request import UpdateAlarmRuleRequest
from volcenginesdkquota.models.update_alarm_rule_response import UpdateAlarmRuleResponse
