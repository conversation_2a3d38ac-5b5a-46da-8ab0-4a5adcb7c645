# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExtendedKeyUsagesForCreateLeafInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_auth': 'bool',
        'code_signing': 'bool',
        'email_protection': 'bool',
        'ocsp_signing': 'bool',
        'server_auth': 'bool',
        'time_stamping': 'bool'
    }

    attribute_map = {
        'client_auth': 'client_auth',
        'code_signing': 'code_signing',
        'email_protection': 'email_protection',
        'ocsp_signing': 'ocsp_signing',
        'server_auth': 'server_auth',
        'time_stamping': 'time_stamping'
    }

    def __init__(self, client_auth=None, code_signing=None, email_protection=None, ocsp_signing=None, server_auth=None, time_stamping=None, _configuration=None):  # noqa: E501
        """ExtendedKeyUsagesForCreateLeafInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_auth = None
        self._code_signing = None
        self._email_protection = None
        self._ocsp_signing = None
        self._server_auth = None
        self._time_stamping = None
        self.discriminator = None

        if client_auth is not None:
            self.client_auth = client_auth
        if code_signing is not None:
            self.code_signing = code_signing
        if email_protection is not None:
            self.email_protection = email_protection
        if ocsp_signing is not None:
            self.ocsp_signing = ocsp_signing
        if server_auth is not None:
            self.server_auth = server_auth
        if time_stamping is not None:
            self.time_stamping = time_stamping

    @property
    def client_auth(self):
        """Gets the client_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The client_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._client_auth

    @client_auth.setter
    def client_auth(self, client_auth):
        """Sets the client_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param client_auth: The client_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._client_auth = client_auth

    @property
    def code_signing(self):
        """Gets the code_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The code_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._code_signing

    @code_signing.setter
    def code_signing(self, code_signing):
        """Sets the code_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param code_signing: The code_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._code_signing = code_signing

    @property
    def email_protection(self):
        """Gets the email_protection of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The email_protection of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._email_protection

    @email_protection.setter
    def email_protection(self, email_protection):
        """Sets the email_protection of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param email_protection: The email_protection of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._email_protection = email_protection

    @property
    def ocsp_signing(self):
        """Gets the ocsp_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The ocsp_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._ocsp_signing

    @ocsp_signing.setter
    def ocsp_signing(self, ocsp_signing):
        """Sets the ocsp_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param ocsp_signing: The ocsp_signing of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._ocsp_signing = ocsp_signing

    @property
    def server_auth(self):
        """Gets the server_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The server_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._server_auth

    @server_auth.setter
    def server_auth(self, server_auth):
        """Sets the server_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param server_auth: The server_auth of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._server_auth = server_auth

    @property
    def time_stamping(self):
        """Gets the time_stamping of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The time_stamping of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._time_stamping

    @time_stamping.setter
    def time_stamping(self, time_stamping):
        """Sets the time_stamping of this ExtendedKeyUsagesForCreateLeafInstanceInput.


        :param time_stamping: The time_stamping of this ExtendedKeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._time_stamping = time_stamping

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExtendedKeyUsagesForCreateLeafInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExtendedKeyUsagesForCreateLeafInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExtendedKeyUsagesForCreateLeafInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
