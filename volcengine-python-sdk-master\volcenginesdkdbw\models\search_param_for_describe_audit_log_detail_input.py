# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SearchParamForDescribeAuditLogDetailInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affect_row_down': 'int',
        'affect_row_up': 'int',
        'dbs': 'list[str]',
        'during_down': 'int',
        'during_up': 'int',
        'log_levels': 'list[str]',
        'log_type': 'list[str]',
        'node_id': 'str',
        'source_ips': 'list[str]',
        'sql_fingerprint': 'str',
        'sql_methods': 'list[str]',
        'tables': 'list[str]',
        'users': 'list[str]'
    }

    attribute_map = {
        'affect_row_down': 'AffectRowDown',
        'affect_row_up': 'AffectRowUp',
        'dbs': 'DBs',
        'during_down': 'DuringDown',
        'during_up': 'DuringUp',
        'log_levels': 'LogLevels',
        'log_type': 'LogType',
        'node_id': 'NodeId',
        'source_ips': 'SourceIPs',
        'sql_fingerprint': 'SqlFingerprint',
        'sql_methods': 'SqlMethods',
        'tables': 'Tables',
        'users': 'Users'
    }

    def __init__(self, affect_row_down=None, affect_row_up=None, dbs=None, during_down=None, during_up=None, log_levels=None, log_type=None, node_id=None, source_ips=None, sql_fingerprint=None, sql_methods=None, tables=None, users=None, _configuration=None):  # noqa: E501
        """SearchParamForDescribeAuditLogDetailInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affect_row_down = None
        self._affect_row_up = None
        self._dbs = None
        self._during_down = None
        self._during_up = None
        self._log_levels = None
        self._log_type = None
        self._node_id = None
        self._source_ips = None
        self._sql_fingerprint = None
        self._sql_methods = None
        self._tables = None
        self._users = None
        self.discriminator = None

        if affect_row_down is not None:
            self.affect_row_down = affect_row_down
        if affect_row_up is not None:
            self.affect_row_up = affect_row_up
        if dbs is not None:
            self.dbs = dbs
        if during_down is not None:
            self.during_down = during_down
        if during_up is not None:
            self.during_up = during_up
        if log_levels is not None:
            self.log_levels = log_levels
        if log_type is not None:
            self.log_type = log_type
        if node_id is not None:
            self.node_id = node_id
        if source_ips is not None:
            self.source_ips = source_ips
        if sql_fingerprint is not None:
            self.sql_fingerprint = sql_fingerprint
        if sql_methods is not None:
            self.sql_methods = sql_methods
        if tables is not None:
            self.tables = tables
        if users is not None:
            self.users = users

    @property
    def affect_row_down(self):
        """Gets the affect_row_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The affect_row_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: int
        """
        return self._affect_row_down

    @affect_row_down.setter
    def affect_row_down(self, affect_row_down):
        """Sets the affect_row_down of this SearchParamForDescribeAuditLogDetailInput.


        :param affect_row_down: The affect_row_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: int
        """

        self._affect_row_down = affect_row_down

    @property
    def affect_row_up(self):
        """Gets the affect_row_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The affect_row_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: int
        """
        return self._affect_row_up

    @affect_row_up.setter
    def affect_row_up(self, affect_row_up):
        """Sets the affect_row_up of this SearchParamForDescribeAuditLogDetailInput.


        :param affect_row_up: The affect_row_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: int
        """

        self._affect_row_up = affect_row_up

    @property
    def dbs(self):
        """Gets the dbs of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The dbs of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._dbs

    @dbs.setter
    def dbs(self, dbs):
        """Sets the dbs of this SearchParamForDescribeAuditLogDetailInput.


        :param dbs: The dbs of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """

        self._dbs = dbs

    @property
    def during_down(self):
        """Gets the during_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The during_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: int
        """
        return self._during_down

    @during_down.setter
    def during_down(self, during_down):
        """Sets the during_down of this SearchParamForDescribeAuditLogDetailInput.


        :param during_down: The during_down of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: int
        """

        self._during_down = during_down

    @property
    def during_up(self):
        """Gets the during_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The during_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: int
        """
        return self._during_up

    @during_up.setter
    def during_up(self, during_up):
        """Sets the during_up of this SearchParamForDescribeAuditLogDetailInput.


        :param during_up: The during_up of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: int
        """

        self._during_up = during_up

    @property
    def log_levels(self):
        """Gets the log_levels of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The log_levels of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._log_levels

    @log_levels.setter
    def log_levels(self, log_levels):
        """Sets the log_levels of this SearchParamForDescribeAuditLogDetailInput.


        :param log_levels: The log_levels of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Normal", "Incident", "Warning"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(log_levels).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `log_levels` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(log_levels) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._log_levels = log_levels

    @property
    def log_type(self):
        """Gets the log_type of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The log_type of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._log_type

    @log_type.setter
    def log_type(self, log_type):
        """Sets the log_type of this SearchParamForDescribeAuditLogDetailInput.


        :param log_type: The log_type of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """

        self._log_type = log_type

    @property
    def node_id(self):
        """Gets the node_id of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The node_id of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this SearchParamForDescribeAuditLogDetailInput.


        :param node_id: The node_id of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def source_ips(self):
        """Gets the source_ips of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The source_ips of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_ips

    @source_ips.setter
    def source_ips(self, source_ips):
        """Sets the source_ips of this SearchParamForDescribeAuditLogDetailInput.


        :param source_ips: The source_ips of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """

        self._source_ips = source_ips

    @property
    def sql_fingerprint(self):
        """Gets the sql_fingerprint of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The sql_fingerprint of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: str
        """
        return self._sql_fingerprint

    @sql_fingerprint.setter
    def sql_fingerprint(self, sql_fingerprint):
        """Sets the sql_fingerprint of this SearchParamForDescribeAuditLogDetailInput.


        :param sql_fingerprint: The sql_fingerprint of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: str
        """

        self._sql_fingerprint = sql_fingerprint

    @property
    def sql_methods(self):
        """Gets the sql_methods of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The sql_methods of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._sql_methods

    @sql_methods.setter
    def sql_methods(self, sql_methods):
        """Sets the sql_methods of this SearchParamForDescribeAuditLogDetailInput.


        :param sql_methods: The sql_methods of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["SELECT", "INSERT", "UPDATE", "DELETE", "ALTER", "CREATE", "DROP", "RENAME", "TRUNCATE", "LOGIN", "LOGOUT"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(sql_methods).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `sql_methods` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(sql_methods) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._sql_methods = sql_methods

    @property
    def tables(self):
        """Gets the tables of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The tables of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tables

    @tables.setter
    def tables(self, tables):
        """Sets the tables of this SearchParamForDescribeAuditLogDetailInput.


        :param tables: The tables of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """

        self._tables = tables

    @property
    def users(self):
        """Gets the users of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501


        :return: The users of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._users

    @users.setter
    def users(self, users):
        """Sets the users of this SearchParamForDescribeAuditLogDetailInput.


        :param users: The users of this SearchParamForDescribeAuditLogDetailInput.  # noqa: E501
        :type: list[str]
        """

        self._users = users

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SearchParamForDescribeAuditLogDetailInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SearchParamForDescribeAuditLogDetailInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SearchParamForDescribeAuditLogDetailInput):
            return True

        return self.to_dict() != other.to_dict()
