# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RiskNumForListBaselinesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'high_num': 'int',
        'low_num': 'int',
        'medium_num': 'int'
    }

    attribute_map = {
        'high_num': 'HighNum',
        'low_num': 'LowNum',
        'medium_num': 'MediumNum'
    }

    def __init__(self, high_num=None, low_num=None, medium_num=None, _configuration=None):  # noqa: E501
        """RiskNumForListBaselinesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._high_num = None
        self._low_num = None
        self._medium_num = None
        self.discriminator = None

        if high_num is not None:
            self.high_num = high_num
        if low_num is not None:
            self.low_num = low_num
        if medium_num is not None:
            self.medium_num = medium_num

    @property
    def high_num(self):
        """Gets the high_num of this RiskNumForListBaselinesOutput.  # noqa: E501


        :return: The high_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._high_num

    @high_num.setter
    def high_num(self, high_num):
        """Sets the high_num of this RiskNumForListBaselinesOutput.


        :param high_num: The high_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._high_num = high_num

    @property
    def low_num(self):
        """Gets the low_num of this RiskNumForListBaselinesOutput.  # noqa: E501


        :return: The low_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._low_num

    @low_num.setter
    def low_num(self, low_num):
        """Sets the low_num of this RiskNumForListBaselinesOutput.


        :param low_num: The low_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._low_num = low_num

    @property
    def medium_num(self):
        """Gets the medium_num of this RiskNumForListBaselinesOutput.  # noqa: E501


        :return: The medium_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._medium_num

    @medium_num.setter
    def medium_num(self, medium_num):
        """Sets the medium_num of this RiskNumForListBaselinesOutput.


        :param medium_num: The medium_num of this RiskNumForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._medium_num = medium_num

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RiskNumForListBaselinesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RiskNumForListBaselinesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RiskNumForListBaselinesOutput):
            return True

        return self.to_dict() != other.to_dict()
