# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusForUpdateServiceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message': 'str',
        'secondary_state': 'str',
        'state': 'str'
    }

    attribute_map = {
        'message': 'Message',
        'secondary_state': 'SecondaryState',
        'state': 'State'
    }

    def __init__(self, message=None, secondary_state=None, state=None, _configuration=None):  # noqa: E501
        """StatusForUpdateServiceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message = None
        self._secondary_state = None
        self._state = None
        self.discriminator = None

        if message is not None:
            self.message = message
        if secondary_state is not None:
            self.secondary_state = secondary_state
        if state is not None:
            self.state = state

    @property
    def message(self):
        """Gets the message of this StatusForUpdateServiceOutput.  # noqa: E501


        :return: The message of this StatusForUpdateServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this StatusForUpdateServiceOutput.


        :param message: The message of this StatusForUpdateServiceOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def secondary_state(self):
        """Gets the secondary_state of this StatusForUpdateServiceOutput.  # noqa: E501


        :return: The secondary_state of this StatusForUpdateServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._secondary_state

    @secondary_state.setter
    def secondary_state(self, secondary_state):
        """Sets the secondary_state of this StatusForUpdateServiceOutput.


        :param secondary_state: The secondary_state of this StatusForUpdateServiceOutput.  # noqa: E501
        :type: str
        """

        self._secondary_state = secondary_state

    @property
    def state(self):
        """Gets the state of this StatusForUpdateServiceOutput.  # noqa: E501


        :return: The state of this StatusForUpdateServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this StatusForUpdateServiceOutput.


        :param state: The state of this StatusForUpdateServiceOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusForUpdateServiceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusForUpdateServiceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusForUpdateServiceOutput):
            return True

        return self.to_dict() != other.to_dict()
