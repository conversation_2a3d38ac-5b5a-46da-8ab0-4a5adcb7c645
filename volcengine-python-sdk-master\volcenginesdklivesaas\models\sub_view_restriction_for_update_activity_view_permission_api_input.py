# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubViewRestrictionForUpdateActivityViewPermissionAPIInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_viewing_restriction': 'CustomViewingRestrictionForUpdateActivityViewPermissionAPIInput',
        'viewing_account_type': 'int',
        'viewing_restriction_button_title': 'str'
    }

    attribute_map = {
        'custom_viewing_restriction': 'CustomViewingRestriction',
        'viewing_account_type': 'ViewingAccountType',
        'viewing_restriction_button_title': 'ViewingRestrictionButtonTitle'
    }

    def __init__(self, custom_viewing_restriction=None, viewing_account_type=None, viewing_restriction_button_title=None, _configuration=None):  # noqa: E501
        """SubViewRestrictionForUpdateActivityViewPermissionAPIInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_viewing_restriction = None
        self._viewing_account_type = None
        self._viewing_restriction_button_title = None
        self.discriminator = None

        if custom_viewing_restriction is not None:
            self.custom_viewing_restriction = custom_viewing_restriction
        if viewing_account_type is not None:
            self.viewing_account_type = viewing_account_type
        if viewing_restriction_button_title is not None:
            self.viewing_restriction_button_title = viewing_restriction_button_title

    @property
    def custom_viewing_restriction(self):
        """Gets the custom_viewing_restriction of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501


        :return: The custom_viewing_restriction of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :rtype: CustomViewingRestrictionForUpdateActivityViewPermissionAPIInput
        """
        return self._custom_viewing_restriction

    @custom_viewing_restriction.setter
    def custom_viewing_restriction(self, custom_viewing_restriction):
        """Sets the custom_viewing_restriction of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.


        :param custom_viewing_restriction: The custom_viewing_restriction of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :type: CustomViewingRestrictionForUpdateActivityViewPermissionAPIInput
        """

        self._custom_viewing_restriction = custom_viewing_restriction

    @property
    def viewing_account_type(self):
        """Gets the viewing_account_type of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501


        :return: The viewing_account_type of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :rtype: int
        """
        return self._viewing_account_type

    @viewing_account_type.setter
    def viewing_account_type(self, viewing_account_type):
        """Sets the viewing_account_type of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.


        :param viewing_account_type: The viewing_account_type of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :type: int
        """

        self._viewing_account_type = viewing_account_type

    @property
    def viewing_restriction_button_title(self):
        """Gets the viewing_restriction_button_title of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501


        :return: The viewing_restriction_button_title of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :rtype: str
        """
        return self._viewing_restriction_button_title

    @viewing_restriction_button_title.setter
    def viewing_restriction_button_title(self, viewing_restriction_button_title):
        """Sets the viewing_restriction_button_title of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.


        :param viewing_restriction_button_title: The viewing_restriction_button_title of this SubViewRestrictionForUpdateActivityViewPermissionAPIInput.  # noqa: E501
        :type: str
        """

        self._viewing_restriction_button_title = viewing_restriction_button_title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubViewRestrictionForUpdateActivityViewPermissionAPIInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubViewRestrictionForUpdateActivityViewPermissionAPIInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubViewRestrictionForUpdateActivityViewPermissionAPIInput):
            return True

        return self.to_dict() != other.to_dict()
