# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CustomLogForListGatewaysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_variables': 'list[CustomVariableForListGatewaysOutput]',
        'request_headers': 'list[RequestHeaderForListGatewaysOutput]',
        'response_headers': 'list[ResponseHeaderForListGatewaysOutput]'
    }

    attribute_map = {
        'custom_variables': 'CustomVariables',
        'request_headers': 'RequestHeaders',
        'response_headers': 'ResponseHeaders'
    }

    def __init__(self, custom_variables=None, request_headers=None, response_headers=None, _configuration=None):  # noqa: E501
        """CustomLogForListGatewaysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_variables = None
        self._request_headers = None
        self._response_headers = None
        self.discriminator = None

        if custom_variables is not None:
            self.custom_variables = custom_variables
        if request_headers is not None:
            self.request_headers = request_headers
        if response_headers is not None:
            self.response_headers = response_headers

    @property
    def custom_variables(self):
        """Gets the custom_variables of this CustomLogForListGatewaysOutput.  # noqa: E501


        :return: The custom_variables of this CustomLogForListGatewaysOutput.  # noqa: E501
        :rtype: list[CustomVariableForListGatewaysOutput]
        """
        return self._custom_variables

    @custom_variables.setter
    def custom_variables(self, custom_variables):
        """Sets the custom_variables of this CustomLogForListGatewaysOutput.


        :param custom_variables: The custom_variables of this CustomLogForListGatewaysOutput.  # noqa: E501
        :type: list[CustomVariableForListGatewaysOutput]
        """

        self._custom_variables = custom_variables

    @property
    def request_headers(self):
        """Gets the request_headers of this CustomLogForListGatewaysOutput.  # noqa: E501


        :return: The request_headers of this CustomLogForListGatewaysOutput.  # noqa: E501
        :rtype: list[RequestHeaderForListGatewaysOutput]
        """
        return self._request_headers

    @request_headers.setter
    def request_headers(self, request_headers):
        """Sets the request_headers of this CustomLogForListGatewaysOutput.


        :param request_headers: The request_headers of this CustomLogForListGatewaysOutput.  # noqa: E501
        :type: list[RequestHeaderForListGatewaysOutput]
        """

        self._request_headers = request_headers

    @property
    def response_headers(self):
        """Gets the response_headers of this CustomLogForListGatewaysOutput.  # noqa: E501


        :return: The response_headers of this CustomLogForListGatewaysOutput.  # noqa: E501
        :rtype: list[ResponseHeaderForListGatewaysOutput]
        """
        return self._response_headers

    @response_headers.setter
    def response_headers(self, response_headers):
        """Sets the response_headers of this CustomLogForListGatewaysOutput.


        :param response_headers: The response_headers of this CustomLogForListGatewaysOutput.  # noqa: E501
        :type: list[ResponseHeaderForListGatewaysOutput]
        """

        self._response_headers = response_headers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomLogForListGatewaysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomLogForListGatewaysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CustomLogForListGatewaysOutput):
            return True

        return self.to_dict() != other.to_dict()
