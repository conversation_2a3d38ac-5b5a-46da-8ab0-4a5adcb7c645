# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyMigrationJobAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'migration_job_description': 'str',
        'migration_job_id': 'str',
        'migration_job_name': 'str',
        'network_transition_mode': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'migration_job_description': 'MigrationJobDescription',
        'migration_job_id': 'MigrationJobId',
        'migration_job_name': 'MigrationJobName',
        'network_transition_mode': 'NetworkTransitionMode'
    }

    def __init__(self, bandwidth=None, migration_job_description=None, migration_job_id=None, migration_job_name=None, network_transition_mode=None, _configuration=None):  # noqa: E501
        """ModifyMigrationJobAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._migration_job_description = None
        self._migration_job_id = None
        self._migration_job_name = None
        self._network_transition_mode = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if migration_job_description is not None:
            self.migration_job_description = migration_job_description
        self.migration_job_id = migration_job_id
        if migration_job_name is not None:
            self.migration_job_name = migration_job_name
        if network_transition_mode is not None:
            self.network_transition_mode = network_transition_mode

    @property
    def bandwidth(self):
        """Gets the bandwidth of this ModifyMigrationJobAttributeRequest.  # noqa: E501


        :return: The bandwidth of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this ModifyMigrationJobAttributeRequest.


        :param bandwidth: The bandwidth of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def migration_job_description(self):
        """Gets the migration_job_description of this ModifyMigrationJobAttributeRequest.  # noqa: E501


        :return: The migration_job_description of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_description

    @migration_job_description.setter
    def migration_job_description(self, migration_job_description):
        """Sets the migration_job_description of this ModifyMigrationJobAttributeRequest.


        :param migration_job_description: The migration_job_description of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_description = migration_job_description

    @property
    def migration_job_id(self):
        """Gets the migration_job_id of this ModifyMigrationJobAttributeRequest.  # noqa: E501


        :return: The migration_job_id of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_id

    @migration_job_id.setter
    def migration_job_id(self, migration_job_id):
        """Sets the migration_job_id of this ModifyMigrationJobAttributeRequest.


        :param migration_job_id: The migration_job_id of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and migration_job_id is None:
            raise ValueError("Invalid value for `migration_job_id`, must not be `None`")  # noqa: E501

        self._migration_job_id = migration_job_id

    @property
    def migration_job_name(self):
        """Gets the migration_job_name of this ModifyMigrationJobAttributeRequest.  # noqa: E501


        :return: The migration_job_name of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_name

    @migration_job_name.setter
    def migration_job_name(self, migration_job_name):
        """Sets the migration_job_name of this ModifyMigrationJobAttributeRequest.


        :param migration_job_name: The migration_job_name of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_name = migration_job_name

    @property
    def network_transition_mode(self):
        """Gets the network_transition_mode of this ModifyMigrationJobAttributeRequest.  # noqa: E501


        :return: The network_transition_mode of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_transition_mode

    @network_transition_mode.setter
    def network_transition_mode(self, network_transition_mode):
        """Sets the network_transition_mode of this ModifyMigrationJobAttributeRequest.


        :param network_transition_mode: The network_transition_mode of this ModifyMigrationJobAttributeRequest.  # noqa: E501
        :type: str
        """

        self._network_transition_mode = network_transition_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyMigrationJobAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyMigrationJobAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyMigrationJobAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
