# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UaAccessRuleForBatchUpdateCdnConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_empty': 'bool',
        'ignore_case': 'bool',
        'rule_type': 'str',
        'switch': 'bool',
        'user_agent': 'list[str]'
    }

    attribute_map = {
        'allow_empty': 'AllowEmpty',
        'ignore_case': 'IgnoreCase',
        'rule_type': 'RuleType',
        'switch': 'Switch',
        'user_agent': 'UserAgent'
    }

    def __init__(self, allow_empty=None, ignore_case=None, rule_type=None, switch=None, user_agent=None, _configuration=None):  # noqa: E501
        """UaAccessRuleForBatchUpdateCdnConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_empty = None
        self._ignore_case = None
        self._rule_type = None
        self._switch = None
        self._user_agent = None
        self.discriminator = None

        if allow_empty is not None:
            self.allow_empty = allow_empty
        if ignore_case is not None:
            self.ignore_case = ignore_case
        if rule_type is not None:
            self.rule_type = rule_type
        if switch is not None:
            self.switch = switch
        if user_agent is not None:
            self.user_agent = user_agent

    @property
    def allow_empty(self):
        """Gets the allow_empty of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The allow_empty of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._allow_empty

    @allow_empty.setter
    def allow_empty(self, allow_empty):
        """Sets the allow_empty of this UaAccessRuleForBatchUpdateCdnConfigInput.


        :param allow_empty: The allow_empty of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: bool
        """

        self._allow_empty = allow_empty

    @property
    def ignore_case(self):
        """Gets the ignore_case of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The ignore_case of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._ignore_case

    @ignore_case.setter
    def ignore_case(self, ignore_case):
        """Sets the ignore_case of this UaAccessRuleForBatchUpdateCdnConfigInput.


        :param ignore_case: The ignore_case of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: bool
        """

        self._ignore_case = ignore_case

    @property
    def rule_type(self):
        """Gets the rule_type of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The rule_type of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._rule_type

    @rule_type.setter
    def rule_type(self, rule_type):
        """Sets the rule_type of this UaAccessRuleForBatchUpdateCdnConfigInput.


        :param rule_type: The rule_type of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: str
        """

        self._rule_type = rule_type

    @property
    def switch(self):
        """Gets the switch of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The switch of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._switch

    @switch.setter
    def switch(self, switch):
        """Sets the switch of this UaAccessRuleForBatchUpdateCdnConfigInput.


        :param switch: The switch of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: bool
        """

        self._switch = switch

    @property
    def user_agent(self):
        """Gets the user_agent of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501


        :return: The user_agent of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this UaAccessRuleForBatchUpdateCdnConfigInput.


        :param user_agent: The user_agent of this UaAccessRuleForBatchUpdateCdnConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._user_agent = user_agent

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UaAccessRuleForBatchUpdateCdnConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UaAccessRuleForBatchUpdateCdnConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UaAccessRuleForBatchUpdateCdnConfigInput):
            return True

        return self.to_dict() != other.to_dict()
