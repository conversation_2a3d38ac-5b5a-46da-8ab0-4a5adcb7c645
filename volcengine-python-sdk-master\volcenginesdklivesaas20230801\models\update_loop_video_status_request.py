# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateLoopVideoStatusRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'is_start_all_line': 'bool',
        'is_start_loop_video': 'int',
        'line_id': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'is_start_all_line': 'IsStartAllLine',
        'is_start_loop_video': 'IsStartLoopVideo',
        'line_id': 'LineId'
    }

    def __init__(self, activity_id=None, is_start_all_line=None, is_start_loop_video=None, line_id=None, _configuration=None):  # noqa: E501
        """UpdateLoopVideoStatusRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._is_start_all_line = None
        self._is_start_loop_video = None
        self._line_id = None
        self.discriminator = None

        self.activity_id = activity_id
        if is_start_all_line is not None:
            self.is_start_all_line = is_start_all_line
        self.is_start_loop_video = is_start_loop_video
        if line_id is not None:
            self.line_id = line_id

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateLoopVideoStatusRequest.  # noqa: E501


        :return: The activity_id of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateLoopVideoStatusRequest.


        :param activity_id: The activity_id of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def is_start_all_line(self):
        """Gets the is_start_all_line of this UpdateLoopVideoStatusRequest.  # noqa: E501


        :return: The is_start_all_line of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_start_all_line

    @is_start_all_line.setter
    def is_start_all_line(self, is_start_all_line):
        """Sets the is_start_all_line of this UpdateLoopVideoStatusRequest.


        :param is_start_all_line: The is_start_all_line of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :type: bool
        """

        self._is_start_all_line = is_start_all_line

    @property
    def is_start_loop_video(self):
        """Gets the is_start_loop_video of this UpdateLoopVideoStatusRequest.  # noqa: E501


        :return: The is_start_loop_video of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_start_loop_video

    @is_start_loop_video.setter
    def is_start_loop_video(self, is_start_loop_video):
        """Sets the is_start_loop_video of this UpdateLoopVideoStatusRequest.


        :param is_start_loop_video: The is_start_loop_video of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and is_start_loop_video is None:
            raise ValueError("Invalid value for `is_start_loop_video`, must not be `None`")  # noqa: E501

        self._is_start_loop_video = is_start_loop_video

    @property
    def line_id(self):
        """Gets the line_id of this UpdateLoopVideoStatusRequest.  # noqa: E501


        :return: The line_id of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :rtype: int
        """
        return self._line_id

    @line_id.setter
    def line_id(self, line_id):
        """Sets the line_id of this UpdateLoopVideoStatusRequest.


        :param line_id: The line_id of this UpdateLoopVideoStatusRequest.  # noqa: E501
        :type: int
        """

        self._line_id = line_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateLoopVideoStatusRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateLoopVideoStatusRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateLoopVideoStatusRequest):
            return True

        return self.to_dict() != other.to_dict()
