# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopDataDetailForDescribeEdgeStatusCodeRankingOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'item_key': 'str',
        'status2xx': 'float',
        'status2xx_ratio': 'float',
        'status3xx': 'float',
        'status3xx_ratio': 'float',
        'status4xx': 'float',
        'status4xx_ratio': 'float',
        'status5xx': 'float',
        'status5xx_ratio': 'float'
    }

    attribute_map = {
        'item_key': 'ItemKey',
        'status2xx': 'Status2xx',
        'status2xx_ratio': 'Status2xxRatio',
        'status3xx': 'Status3xx',
        'status3xx_ratio': 'Status3xxRatio',
        'status4xx': 'Status4xx',
        'status4xx_ratio': 'Status4xxRatio',
        'status5xx': 'Status5xx',
        'status5xx_ratio': 'Status5xxRatio'
    }

    def __init__(self, item_key=None, status2xx=None, status2xx_ratio=None, status3xx=None, status3xx_ratio=None, status4xx=None, status4xx_ratio=None, status5xx=None, status5xx_ratio=None, _configuration=None):  # noqa: E501
        """TopDataDetailForDescribeEdgeStatusCodeRankingOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._item_key = None
        self._status2xx = None
        self._status2xx_ratio = None
        self._status3xx = None
        self._status3xx_ratio = None
        self._status4xx = None
        self._status4xx_ratio = None
        self._status5xx = None
        self._status5xx_ratio = None
        self.discriminator = None

        if item_key is not None:
            self.item_key = item_key
        if status2xx is not None:
            self.status2xx = status2xx
        if status2xx_ratio is not None:
            self.status2xx_ratio = status2xx_ratio
        if status3xx is not None:
            self.status3xx = status3xx
        if status3xx_ratio is not None:
            self.status3xx_ratio = status3xx_ratio
        if status4xx is not None:
            self.status4xx = status4xx
        if status4xx_ratio is not None:
            self.status4xx_ratio = status4xx_ratio
        if status5xx is not None:
            self.status5xx = status5xx
        if status5xx_ratio is not None:
            self.status5xx_ratio = status5xx_ratio

    @property
    def item_key(self):
        """Gets the item_key of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The item_key of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: str
        """
        return self._item_key

    @item_key.setter
    def item_key(self, item_key):
        """Sets the item_key of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param item_key: The item_key of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: str
        """

        self._item_key = item_key

    @property
    def status2xx(self):
        """Gets the status2xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status2xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status2xx

    @status2xx.setter
    def status2xx(self, status2xx):
        """Sets the status2xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status2xx: The status2xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status2xx = status2xx

    @property
    def status2xx_ratio(self):
        """Gets the status2xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status2xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status2xx_ratio

    @status2xx_ratio.setter
    def status2xx_ratio(self, status2xx_ratio):
        """Sets the status2xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status2xx_ratio: The status2xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status2xx_ratio = status2xx_ratio

    @property
    def status3xx(self):
        """Gets the status3xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status3xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status3xx

    @status3xx.setter
    def status3xx(self, status3xx):
        """Sets the status3xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status3xx: The status3xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status3xx = status3xx

    @property
    def status3xx_ratio(self):
        """Gets the status3xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status3xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status3xx_ratio

    @status3xx_ratio.setter
    def status3xx_ratio(self, status3xx_ratio):
        """Sets the status3xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status3xx_ratio: The status3xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status3xx_ratio = status3xx_ratio

    @property
    def status4xx(self):
        """Gets the status4xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status4xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status4xx

    @status4xx.setter
    def status4xx(self, status4xx):
        """Sets the status4xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status4xx: The status4xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status4xx = status4xx

    @property
    def status4xx_ratio(self):
        """Gets the status4xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status4xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status4xx_ratio

    @status4xx_ratio.setter
    def status4xx_ratio(self, status4xx_ratio):
        """Sets the status4xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status4xx_ratio: The status4xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status4xx_ratio = status4xx_ratio

    @property
    def status5xx(self):
        """Gets the status5xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status5xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status5xx

    @status5xx.setter
    def status5xx(self, status5xx):
        """Sets the status5xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status5xx: The status5xx of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status5xx = status5xx

    @property
    def status5xx_ratio(self):
        """Gets the status5xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501


        :return: The status5xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :rtype: float
        """
        return self._status5xx_ratio

    @status5xx_ratio.setter
    def status5xx_ratio(self, status5xx_ratio):
        """Sets the status5xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.


        :param status5xx_ratio: The status5xx_ratio of this TopDataDetailForDescribeEdgeStatusCodeRankingOutput.  # noqa: E501
        :type: float
        """

        self._status5xx_ratio = status5xx_ratio

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopDataDetailForDescribeEdgeStatusCodeRankingOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopDataDetailForDescribeEdgeStatusCodeRankingOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopDataDetailForDescribeEdgeStatusCodeRankingOutput):
            return True

        return self.to_dict() != other.to_dict()
