# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MCStrategyRiskListForGetOverviewCardOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_cnt': 'int',
        'resource_type': 'str',
        'risk_id': 'str',
        'risk_level': 'str',
        'risk_name': 'str',
        'risk_occur_time_milli': 'int'
    }

    attribute_map = {
        'resource_cnt': 'ResourceCnt',
        'resource_type': 'ResourceType',
        'risk_id': 'RiskID',
        'risk_level': 'RiskLevel',
        'risk_name': 'RiskName',
        'risk_occur_time_milli': 'RiskOccurTimeMilli'
    }

    def __init__(self, resource_cnt=None, resource_type=None, risk_id=None, risk_level=None, risk_name=None, risk_occur_time_milli=None, _configuration=None):  # noqa: E501
        """MCStrategyRiskListForGetOverviewCardOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_cnt = None
        self._resource_type = None
        self._risk_id = None
        self._risk_level = None
        self._risk_name = None
        self._risk_occur_time_milli = None
        self.discriminator = None

        if resource_cnt is not None:
            self.resource_cnt = resource_cnt
        if resource_type is not None:
            self.resource_type = resource_type
        if risk_id is not None:
            self.risk_id = risk_id
        if risk_level is not None:
            self.risk_level = risk_level
        if risk_name is not None:
            self.risk_name = risk_name
        if risk_occur_time_milli is not None:
            self.risk_occur_time_milli = risk_occur_time_milli

    @property
    def resource_cnt(self):
        """Gets the resource_cnt of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The resource_cnt of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: int
        """
        return self._resource_cnt

    @resource_cnt.setter
    def resource_cnt(self, resource_cnt):
        """Sets the resource_cnt of this MCStrategyRiskListForGetOverviewCardOutput.


        :param resource_cnt: The resource_cnt of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: int
        """

        self._resource_cnt = resource_cnt

    @property
    def resource_type(self):
        """Gets the resource_type of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The resource_type of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this MCStrategyRiskListForGetOverviewCardOutput.


        :param resource_type: The resource_type of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def risk_id(self):
        """Gets the risk_id of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The risk_id of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_id

    @risk_id.setter
    def risk_id(self, risk_id):
        """Sets the risk_id of this MCStrategyRiskListForGetOverviewCardOutput.


        :param risk_id: The risk_id of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: str
        """

        self._risk_id = risk_id

    @property
    def risk_level(self):
        """Gets the risk_level of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The risk_level of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_level

    @risk_level.setter
    def risk_level(self, risk_level):
        """Sets the risk_level of this MCStrategyRiskListForGetOverviewCardOutput.


        :param risk_level: The risk_level of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: str
        """

        self._risk_level = risk_level

    @property
    def risk_name(self):
        """Gets the risk_name of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The risk_name of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_name

    @risk_name.setter
    def risk_name(self, risk_name):
        """Sets the risk_name of this MCStrategyRiskListForGetOverviewCardOutput.


        :param risk_name: The risk_name of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: str
        """

        self._risk_name = risk_name

    @property
    def risk_occur_time_milli(self):
        """Gets the risk_occur_time_milli of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501


        :return: The risk_occur_time_milli of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :rtype: int
        """
        return self._risk_occur_time_milli

    @risk_occur_time_milli.setter
    def risk_occur_time_milli(self, risk_occur_time_milli):
        """Sets the risk_occur_time_milli of this MCStrategyRiskListForGetOverviewCardOutput.


        :param risk_occur_time_milli: The risk_occur_time_milli of this MCStrategyRiskListForGetOverviewCardOutput.  # noqa: E501
        :type: int
        """

        self._risk_occur_time_milli = risk_occur_time_milli

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MCStrategyRiskListForGetOverviewCardOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MCStrategyRiskListForGetOverviewCardOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MCStrategyRiskListForGetOverviewCardOutput):
            return True

        return self.to_dict() != other.to_dict()
