# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TagForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pod_num': 'int',
        'product_id': 'str',
        'tag_desc': 'str',
        'tag_id': 'str',
        'tag_name': 'str'
    }

    attribute_map = {
        'pod_num': 'PodNum',
        'product_id': 'ProductId',
        'tag_desc': 'TagDesc',
        'tag_id': 'TagId',
        'tag_name': 'TagName'
    }

    def __init__(self, pod_num=None, product_id=None, tag_desc=None, tag_id=None, tag_name=None, _configuration=None):  # noqa: E501
        """TagForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pod_num = None
        self._product_id = None
        self._tag_desc = None
        self._tag_id = None
        self._tag_name = None
        self.discriminator = None

        if pod_num is not None:
            self.pod_num = pod_num
        if product_id is not None:
            self.product_id = product_id
        if tag_desc is not None:
            self.tag_desc = tag_desc
        if tag_id is not None:
            self.tag_id = tag_id
        if tag_name is not None:
            self.tag_name = tag_name

    @property
    def pod_num(self):
        """Gets the pod_num of this TagForListPodOutput.  # noqa: E501


        :return: The pod_num of this TagForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._pod_num

    @pod_num.setter
    def pod_num(self, pod_num):
        """Sets the pod_num of this TagForListPodOutput.


        :param pod_num: The pod_num of this TagForListPodOutput.  # noqa: E501
        :type: int
        """

        self._pod_num = pod_num

    @property
    def product_id(self):
        """Gets the product_id of this TagForListPodOutput.  # noqa: E501


        :return: The product_id of this TagForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this TagForListPodOutput.


        :param product_id: The product_id of this TagForListPodOutput.  # noqa: E501
        :type: str
        """

        self._product_id = product_id

    @property
    def tag_desc(self):
        """Gets the tag_desc of this TagForListPodOutput.  # noqa: E501


        :return: The tag_desc of this TagForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag_desc

    @tag_desc.setter
    def tag_desc(self, tag_desc):
        """Sets the tag_desc of this TagForListPodOutput.


        :param tag_desc: The tag_desc of this TagForListPodOutput.  # noqa: E501
        :type: str
        """

        self._tag_desc = tag_desc

    @property
    def tag_id(self):
        """Gets the tag_id of this TagForListPodOutput.  # noqa: E501


        :return: The tag_id of this TagForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag_id

    @tag_id.setter
    def tag_id(self, tag_id):
        """Sets the tag_id of this TagForListPodOutput.


        :param tag_id: The tag_id of this TagForListPodOutput.  # noqa: E501
        :type: str
        """

        self._tag_id = tag_id

    @property
    def tag_name(self):
        """Gets the tag_name of this TagForListPodOutput.  # noqa: E501


        :return: The tag_name of this TagForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag_name

    @tag_name.setter
    def tag_name(self, tag_name):
        """Sets the tag_name of this TagForListPodOutput.


        :param tag_name: The tag_name of this TagForListPodOutput.  # noqa: E501
        :type: str
        """

        self._tag_name = tag_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TagForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TagForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TagForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
