# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRepoImageVulnOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fixed': 'bool',
        'fixed_ver': 'str',
        'id': 'str',
        'pkg_name': 'str',
        'pkg_path': 'str',
        'pkg_type': 'str',
        'pkg_ver': 'str',
        'severity': 'str',
        'title': 'str',
        'vuln_id': 'str',
        'vuln_tags': 'list[str]'
    }

    attribute_map = {
        'fixed': 'Fixed',
        'fixed_ver': 'FixedVer',
        'id': 'ID',
        'pkg_name': 'PkgName',
        'pkg_path': 'PkgPath',
        'pkg_type': 'PkgType',
        'pkg_ver': 'PkgVer',
        'severity': 'Severity',
        'title': 'Title',
        'vuln_id': 'VulnID',
        'vuln_tags': 'VulnTags'
    }

    def __init__(self, fixed=None, fixed_ver=None, id=None, pkg_name=None, pkg_path=None, pkg_type=None, pkg_ver=None, severity=None, title=None, vuln_id=None, vuln_tags=None, _configuration=None):  # noqa: E501
        """DataForListRepoImageVulnOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fixed = None
        self._fixed_ver = None
        self._id = None
        self._pkg_name = None
        self._pkg_path = None
        self._pkg_type = None
        self._pkg_ver = None
        self._severity = None
        self._title = None
        self._vuln_id = None
        self._vuln_tags = None
        self.discriminator = None

        if fixed is not None:
            self.fixed = fixed
        if fixed_ver is not None:
            self.fixed_ver = fixed_ver
        if id is not None:
            self.id = id
        if pkg_name is not None:
            self.pkg_name = pkg_name
        if pkg_path is not None:
            self.pkg_path = pkg_path
        if pkg_type is not None:
            self.pkg_type = pkg_type
        if pkg_ver is not None:
            self.pkg_ver = pkg_ver
        if severity is not None:
            self.severity = severity
        if title is not None:
            self.title = title
        if vuln_id is not None:
            self.vuln_id = vuln_id
        if vuln_tags is not None:
            self.vuln_tags = vuln_tags

    @property
    def fixed(self):
        """Gets the fixed of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The fixed of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: bool
        """
        return self._fixed

    @fixed.setter
    def fixed(self, fixed):
        """Sets the fixed of this DataForListRepoImageVulnOutput.


        :param fixed: The fixed of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: bool
        """

        self._fixed = fixed

    @property
    def fixed_ver(self):
        """Gets the fixed_ver of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The fixed_ver of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._fixed_ver

    @fixed_ver.setter
    def fixed_ver(self, fixed_ver):
        """Sets the fixed_ver of this DataForListRepoImageVulnOutput.


        :param fixed_ver: The fixed_ver of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._fixed_ver = fixed_ver

    @property
    def id(self):
        """Gets the id of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The id of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRepoImageVulnOutput.


        :param id: The id of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def pkg_name(self):
        """Gets the pkg_name of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The pkg_name of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, pkg_name):
        """Sets the pkg_name of this DataForListRepoImageVulnOutput.


        :param pkg_name: The pkg_name of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._pkg_name = pkg_name

    @property
    def pkg_path(self):
        """Gets the pkg_path of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The pkg_path of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_path

    @pkg_path.setter
    def pkg_path(self, pkg_path):
        """Sets the pkg_path of this DataForListRepoImageVulnOutput.


        :param pkg_path: The pkg_path of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._pkg_path = pkg_path

    @property
    def pkg_type(self):
        """Gets the pkg_type of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The pkg_type of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_type

    @pkg_type.setter
    def pkg_type(self, pkg_type):
        """Sets the pkg_type of this DataForListRepoImageVulnOutput.


        :param pkg_type: The pkg_type of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._pkg_type = pkg_type

    @property
    def pkg_ver(self):
        """Gets the pkg_ver of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The pkg_ver of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_ver

    @pkg_ver.setter
    def pkg_ver(self, pkg_ver):
        """Sets the pkg_ver of this DataForListRepoImageVulnOutput.


        :param pkg_ver: The pkg_ver of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._pkg_ver = pkg_ver

    @property
    def severity(self):
        """Gets the severity of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The severity of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._severity

    @severity.setter
    def severity(self, severity):
        """Sets the severity of this DataForListRepoImageVulnOutput.


        :param severity: The severity of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._severity = severity

    @property
    def title(self):
        """Gets the title of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The title of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this DataForListRepoImageVulnOutput.


        :param title: The title of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def vuln_id(self):
        """Gets the vuln_id of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The vuln_id of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_id

    @vuln_id.setter
    def vuln_id(self, vuln_id):
        """Sets the vuln_id of this DataForListRepoImageVulnOutput.


        :param vuln_id: The vuln_id of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: str
        """

        self._vuln_id = vuln_id

    @property
    def vuln_tags(self):
        """Gets the vuln_tags of this DataForListRepoImageVulnOutput.  # noqa: E501


        :return: The vuln_tags of this DataForListRepoImageVulnOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_tags

    @vuln_tags.setter
    def vuln_tags(self, vuln_tags):
        """Sets the vuln_tags of this DataForListRepoImageVulnOutput.


        :param vuln_tags: The vuln_tags of this DataForListRepoImageVulnOutput.  # noqa: E501
        :type: list[str]
        """

        self._vuln_tags = vuln_tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRepoImageVulnOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRepoImageVulnOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRepoImageVulnOutput):
            return True

        return self.to_dict() != other.to_dict()
