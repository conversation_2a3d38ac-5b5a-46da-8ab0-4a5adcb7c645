# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MpsForDownloadLocalMediaInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'm3_u8_option': 'M3U8OptionForDownloadLocalMediaInput',
        'subtitle': 'SubtitleForDownloadLocalMediaInput'
    }

    attribute_map = {
        'm3_u8_option': 'M3U8Option',
        'subtitle': 'Subtitle'
    }

    def __init__(self, m3_u8_option=None, subtitle=None, _configuration=None):  # noqa: E501
        """MpsForDownloadLocalMediaInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._m3_u8_option = None
        self._subtitle = None
        self.discriminator = None

        if m3_u8_option is not None:
            self.m3_u8_option = m3_u8_option
        if subtitle is not None:
            self.subtitle = subtitle

    @property
    def m3_u8_option(self):
        """Gets the m3_u8_option of this MpsForDownloadLocalMediaInput.  # noqa: E501


        :return: The m3_u8_option of this MpsForDownloadLocalMediaInput.  # noqa: E501
        :rtype: M3U8OptionForDownloadLocalMediaInput
        """
        return self._m3_u8_option

    @m3_u8_option.setter
    def m3_u8_option(self, m3_u8_option):
        """Sets the m3_u8_option of this MpsForDownloadLocalMediaInput.


        :param m3_u8_option: The m3_u8_option of this MpsForDownloadLocalMediaInput.  # noqa: E501
        :type: M3U8OptionForDownloadLocalMediaInput
        """

        self._m3_u8_option = m3_u8_option

    @property
    def subtitle(self):
        """Gets the subtitle of this MpsForDownloadLocalMediaInput.  # noqa: E501


        :return: The subtitle of this MpsForDownloadLocalMediaInput.  # noqa: E501
        :rtype: SubtitleForDownloadLocalMediaInput
        """
        return self._subtitle

    @subtitle.setter
    def subtitle(self, subtitle):
        """Sets the subtitle of this MpsForDownloadLocalMediaInput.


        :param subtitle: The subtitle of this MpsForDownloadLocalMediaInput.  # noqa: E501
        :type: SubtitleForDownloadLocalMediaInput
        """

        self._subtitle = subtitle

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MpsForDownloadLocalMediaInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MpsForDownloadLocalMediaInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MpsForDownloadLocalMediaInput):
            return True

        return self.to_dict() != other.to_dict()
