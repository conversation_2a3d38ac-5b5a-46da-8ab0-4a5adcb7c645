# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRulesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'kind': 'str',
        'name': 'str',
        'rule_file_names': 'list[str]',
        'rule_group_names': 'list[str]',
        'status': 'list[str]',
        'workspace_id': 'str'
    }

    attribute_map = {
        'kind': 'Kind',
        'name': 'Name',
        'rule_file_names': 'RuleFileNames',
        'rule_group_names': 'RuleGroupNames',
        'status': 'Status',
        'workspace_id': 'WorkspaceId'
    }

    def __init__(self, kind=None, name=None, rule_file_names=None, rule_group_names=None, status=None, workspace_id=None, _configuration=None):  # noqa: E501
        """FilterForListRulesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._kind = None
        self._name = None
        self._rule_file_names = None
        self._rule_group_names = None
        self._status = None
        self._workspace_id = None
        self.discriminator = None

        if kind is not None:
            self.kind = kind
        if name is not None:
            self.name = name
        if rule_file_names is not None:
            self.rule_file_names = rule_file_names
        if rule_group_names is not None:
            self.rule_group_names = rule_group_names
        if status is not None:
            self.status = status
        if workspace_id is not None:
            self.workspace_id = workspace_id

    @property
    def kind(self):
        """Gets the kind of this FilterForListRulesInput.  # noqa: E501


        :return: The kind of this FilterForListRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this FilterForListRulesInput.


        :param kind: The kind of this FilterForListRulesInput.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def name(self):
        """Gets the name of this FilterForListRulesInput.  # noqa: E501


        :return: The name of this FilterForListRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListRulesInput.


        :param name: The name of this FilterForListRulesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def rule_file_names(self):
        """Gets the rule_file_names of this FilterForListRulesInput.  # noqa: E501


        :return: The rule_file_names of this FilterForListRulesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._rule_file_names

    @rule_file_names.setter
    def rule_file_names(self, rule_file_names):
        """Sets the rule_file_names of this FilterForListRulesInput.


        :param rule_file_names: The rule_file_names of this FilterForListRulesInput.  # noqa: E501
        :type: list[str]
        """

        self._rule_file_names = rule_file_names

    @property
    def rule_group_names(self):
        """Gets the rule_group_names of this FilterForListRulesInput.  # noqa: E501


        :return: The rule_group_names of this FilterForListRulesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._rule_group_names

    @rule_group_names.setter
    def rule_group_names(self, rule_group_names):
        """Sets the rule_group_names of this FilterForListRulesInput.


        :param rule_group_names: The rule_group_names of this FilterForListRulesInput.  # noqa: E501
        :type: list[str]
        """

        self._rule_group_names = rule_group_names

    @property
    def status(self):
        """Gets the status of this FilterForListRulesInput.  # noqa: E501


        :return: The status of this FilterForListRulesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListRulesInput.


        :param status: The status of this FilterForListRulesInput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def workspace_id(self):
        """Gets the workspace_id of this FilterForListRulesInput.  # noqa: E501


        :return: The workspace_id of this FilterForListRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this FilterForListRulesInput.


        :param workspace_id: The workspace_id of this FilterForListRulesInput.  # noqa: E501
        :type: str
        """

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRulesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRulesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRulesInput):
            return True

        return self.to_dict() != other.to_dict()
