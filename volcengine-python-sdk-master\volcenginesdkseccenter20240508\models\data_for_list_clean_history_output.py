# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListCleanHistoryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'clean_time': 'int',
        'cloud_provider': 'str',
        'group_name': 'str',
        'host_id': 'str',
        'host_name': 'str',
        'offline_time': 'int',
        'platform': 'str',
        'private_ip': 'str',
        'public_ip': 'str',
        'region': 'str',
        'tags': 'list[str]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'clean_time': 'CleanTime',
        'cloud_provider': 'CloudProvider',
        'group_name': 'GroupName',
        'host_id': 'HostId',
        'host_name': 'HostName',
        'offline_time': 'OfflineTime',
        'platform': 'Platform',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'region': 'Region',
        'tags': 'Tags',
        'vpc_id': 'VpcId'
    }

    def __init__(self, clean_time=None, cloud_provider=None, group_name=None, host_id=None, host_name=None, offline_time=None, platform=None, private_ip=None, public_ip=None, region=None, tags=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DataForListCleanHistoryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._clean_time = None
        self._cloud_provider = None
        self._group_name = None
        self._host_id = None
        self._host_name = None
        self._offline_time = None
        self._platform = None
        self._private_ip = None
        self._public_ip = None
        self._region = None
        self._tags = None
        self._vpc_id = None
        self.discriminator = None

        if clean_time is not None:
            self.clean_time = clean_time
        if cloud_provider is not None:
            self.cloud_provider = cloud_provider
        if group_name is not None:
            self.group_name = group_name
        if host_id is not None:
            self.host_id = host_id
        if host_name is not None:
            self.host_name = host_name
        if offline_time is not None:
            self.offline_time = offline_time
        if platform is not None:
            self.platform = platform
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if region is not None:
            self.region = region
        if tags is not None:
            self.tags = tags
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def clean_time(self):
        """Gets the clean_time of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The clean_time of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: int
        """
        return self._clean_time

    @clean_time.setter
    def clean_time(self, clean_time):
        """Sets the clean_time of this DataForListCleanHistoryOutput.


        :param clean_time: The clean_time of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: int
        """

        self._clean_time = clean_time

    @property
    def cloud_provider(self):
        """Gets the cloud_provider of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The cloud_provider of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_provider

    @cloud_provider.setter
    def cloud_provider(self, cloud_provider):
        """Sets the cloud_provider of this DataForListCleanHistoryOutput.


        :param cloud_provider: The cloud_provider of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._cloud_provider = cloud_provider

    @property
    def group_name(self):
        """Gets the group_name of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The group_name of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this DataForListCleanHistoryOutput.


        :param group_name: The group_name of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def host_id(self):
        """Gets the host_id of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The host_id of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_id

    @host_id.setter
    def host_id(self, host_id):
        """Sets the host_id of this DataForListCleanHistoryOutput.


        :param host_id: The host_id of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._host_id = host_id

    @property
    def host_name(self):
        """Gets the host_name of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The host_name of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this DataForListCleanHistoryOutput.


        :param host_name: The host_name of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def offline_time(self):
        """Gets the offline_time of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The offline_time of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: int
        """
        return self._offline_time

    @offline_time.setter
    def offline_time(self, offline_time):
        """Sets the offline_time of this DataForListCleanHistoryOutput.


        :param offline_time: The offline_time of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: int
        """

        self._offline_time = offline_time

    @property
    def platform(self):
        """Gets the platform of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The platform of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DataForListCleanHistoryOutput.


        :param platform: The platform of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The private_ip of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForListCleanHistoryOutput.


        :param private_ip: The private_ip of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The public_ip of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForListCleanHistoryOutput.


        :param public_ip: The public_ip of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def region(self):
        """Gets the region of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The region of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListCleanHistoryOutput.


        :param region: The region of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def tags(self):
        """Gets the tags of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The tags of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DataForListCleanHistoryOutput.


        :param tags: The tags of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DataForListCleanHistoryOutput.  # noqa: E501


        :return: The vpc_id of this DataForListCleanHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DataForListCleanHistoryOutput.


        :param vpc_id: The vpc_id of this DataForListCleanHistoryOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListCleanHistoryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListCleanHistoryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListCleanHistoryOutput):
            return True

        return self.to_dict() != other.to_dict()
