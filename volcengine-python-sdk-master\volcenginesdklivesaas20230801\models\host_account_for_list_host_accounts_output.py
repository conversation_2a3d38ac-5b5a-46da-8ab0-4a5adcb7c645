# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HostAccountForListHostAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'avatar_url': 'str',
        'create_time': 'int',
        'home_url': 'str',
        'id': 'int',
        'is_follow_enable': 'int',
        'name': 'str'
    }

    attribute_map = {
        'avatar_url': 'AvatarUrl',
        'create_time': 'CreateTime',
        'home_url': 'HomeUrl',
        'id': 'Id',
        'is_follow_enable': 'IsFollowEnable',
        'name': 'Name'
    }

    def __init__(self, avatar_url=None, create_time=None, home_url=None, id=None, is_follow_enable=None, name=None, _configuration=None):  # noqa: E501
        """HostAccountForListHostAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._avatar_url = None
        self._create_time = None
        self._home_url = None
        self._id = None
        self._is_follow_enable = None
        self._name = None
        self.discriminator = None

        if avatar_url is not None:
            self.avatar_url = avatar_url
        if create_time is not None:
            self.create_time = create_time
        if home_url is not None:
            self.home_url = home_url
        if id is not None:
            self.id = id
        if is_follow_enable is not None:
            self.is_follow_enable = is_follow_enable
        if name is not None:
            self.name = name

    @property
    def avatar_url(self):
        """Gets the avatar_url of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The avatar_url of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._avatar_url

    @avatar_url.setter
    def avatar_url(self, avatar_url):
        """Sets the avatar_url of this HostAccountForListHostAccountsOutput.


        :param avatar_url: The avatar_url of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: str
        """

        self._avatar_url = avatar_url

    @property
    def create_time(self):
        """Gets the create_time of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The create_time of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this HostAccountForListHostAccountsOutput.


        :param create_time: The create_time of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def home_url(self):
        """Gets the home_url of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The home_url of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._home_url

    @home_url.setter
    def home_url(self, home_url):
        """Sets the home_url of this HostAccountForListHostAccountsOutput.


        :param home_url: The home_url of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: str
        """

        self._home_url = home_url

    @property
    def id(self):
        """Gets the id of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The id of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this HostAccountForListHostAccountsOutput.


        :param id: The id of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_follow_enable(self):
        """Gets the is_follow_enable of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The is_follow_enable of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_follow_enable

    @is_follow_enable.setter
    def is_follow_enable(self, is_follow_enable):
        """Sets the is_follow_enable of this HostAccountForListHostAccountsOutput.


        :param is_follow_enable: The is_follow_enable of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: int
        """

        self._is_follow_enable = is_follow_enable

    @property
    def name(self):
        """Gets the name of this HostAccountForListHostAccountsOutput.  # noqa: E501


        :return: The name of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this HostAccountForListHostAccountsOutput.


        :param name: The name of this HostAccountForListHostAccountsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HostAccountForListHostAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HostAccountForListHostAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HostAccountForListHostAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
