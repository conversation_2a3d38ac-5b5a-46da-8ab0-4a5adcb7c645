# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreToExistedInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_id': 'str',
        'databases': 'list[DatabaseForRestoreToExistedInstanceInput]',
        'restore_time': 'str',
        'source_db_instance_id': 'str',
        'target_db_instance_id': 'str'
    }

    attribute_map = {
        'backup_id': 'BackupId',
        'databases': 'Databases',
        'restore_time': 'RestoreTime',
        'source_db_instance_id': 'SourceDBInstanceId',
        'target_db_instance_id': 'TargetDBInstanceId'
    }

    def __init__(self, backup_id=None, databases=None, restore_time=None, source_db_instance_id=None, target_db_instance_id=None, _configuration=None):  # noqa: E501
        """RestoreToExistedInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_id = None
        self._databases = None
        self._restore_time = None
        self._source_db_instance_id = None
        self._target_db_instance_id = None
        self.discriminator = None

        if backup_id is not None:
            self.backup_id = backup_id
        if databases is not None:
            self.databases = databases
        if restore_time is not None:
            self.restore_time = restore_time
        self.source_db_instance_id = source_db_instance_id
        self.target_db_instance_id = target_db_instance_id

    @property
    def backup_id(self):
        """Gets the backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this RestoreToExistedInstanceRequest.


        :param backup_id: The backup_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def databases(self):
        """Gets the databases of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The databases of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: list[DatabaseForRestoreToExistedInstanceInput]
        """
        return self._databases

    @databases.setter
    def databases(self, databases):
        """Sets the databases of this RestoreToExistedInstanceRequest.


        :param databases: The databases of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: list[DatabaseForRestoreToExistedInstanceInput]
        """

        self._databases = databases

    @property
    def restore_time(self):
        """Gets the restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_time

    @restore_time.setter
    def restore_time(self, restore_time):
        """Sets the restore_time of this RestoreToExistedInstanceRequest.


        :param restore_time: The restore_time of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """

        self._restore_time = restore_time

    @property
    def source_db_instance_id(self):
        """Gets the source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_db_instance_id

    @source_db_instance_id.setter
    def source_db_instance_id(self, source_db_instance_id):
        """Sets the source_db_instance_id of this RestoreToExistedInstanceRequest.


        :param source_db_instance_id: The source_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_db_instance_id is None:
            raise ValueError("Invalid value for `source_db_instance_id`, must not be `None`")  # noqa: E501

        self._source_db_instance_id = source_db_instance_id

    @property
    def target_db_instance_id(self):
        """Gets the target_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501


        :return: The target_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._target_db_instance_id

    @target_db_instance_id.setter
    def target_db_instance_id(self, target_db_instance_id):
        """Sets the target_db_instance_id of this RestoreToExistedInstanceRequest.


        :param target_db_instance_id: The target_db_instance_id of this RestoreToExistedInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and target_db_instance_id is None:
            raise ValueError("Invalid value for `target_db_instance_id`, must not be `None`")  # noqa: E501

        self._target_db_instance_id = target_db_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreToExistedInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreToExistedInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreToExistedInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
