# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartupProbeForUpdateDeploymentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enabled': 'bool',
        '_exec': 'ExecForUpdateDeploymentInput',
        'failure_threshold': 'int',
        'http_get': 'HTTPGetForUpdateDeploymentInput',
        'initial_delay_seconds': 'int',
        'period_seconds': 'int',
        'success_threshold': 'int',
        'tcp_socket': 'TCPSocketForUpdateDeploymentInput',
        'timeout_seconds': 'int'
    }

    attribute_map = {
        'enabled': 'Enabled',
        '_exec': 'Exec',
        'failure_threshold': 'FailureThreshold',
        'http_get': 'HTTPGet',
        'initial_delay_seconds': 'InitialDelaySeconds',
        'period_seconds': 'PeriodSeconds',
        'success_threshold': 'SuccessThreshold',
        'tcp_socket': 'TCPSocket',
        'timeout_seconds': 'TimeoutSeconds'
    }

    def __init__(self, enabled=None, _exec=None, failure_threshold=None, http_get=None, initial_delay_seconds=None, period_seconds=None, success_threshold=None, tcp_socket=None, timeout_seconds=None, _configuration=None):  # noqa: E501
        """StartupProbeForUpdateDeploymentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enabled = None
        self.__exec = None
        self._failure_threshold = None
        self._http_get = None
        self._initial_delay_seconds = None
        self._period_seconds = None
        self._success_threshold = None
        self._tcp_socket = None
        self._timeout_seconds = None
        self.discriminator = None

        if enabled is not None:
            self.enabled = enabled
        if _exec is not None:
            self._exec = _exec
        if failure_threshold is not None:
            self.failure_threshold = failure_threshold
        if http_get is not None:
            self.http_get = http_get
        if initial_delay_seconds is not None:
            self.initial_delay_seconds = initial_delay_seconds
        if period_seconds is not None:
            self.period_seconds = period_seconds
        if success_threshold is not None:
            self.success_threshold = success_threshold
        if tcp_socket is not None:
            self.tcp_socket = tcp_socket
        if timeout_seconds is not None:
            self.timeout_seconds = timeout_seconds

    @property
    def enabled(self):
        """Gets the enabled of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The enabled of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this StartupProbeForUpdateDeploymentInput.


        :param enabled: The enabled of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def _exec(self):
        """Gets the _exec of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The _exec of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: ExecForUpdateDeploymentInput
        """
        return self.__exec

    @_exec.setter
    def _exec(self, _exec):
        """Sets the _exec of this StartupProbeForUpdateDeploymentInput.


        :param _exec: The _exec of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: ExecForUpdateDeploymentInput
        """

        self.__exec = _exec

    @property
    def failure_threshold(self):
        """Gets the failure_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The failure_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._failure_threshold

    @failure_threshold.setter
    def failure_threshold(self, failure_threshold):
        """Sets the failure_threshold of this StartupProbeForUpdateDeploymentInput.


        :param failure_threshold: The failure_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._failure_threshold = failure_threshold

    @property
    def http_get(self):
        """Gets the http_get of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The http_get of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: HTTPGetForUpdateDeploymentInput
        """
        return self._http_get

    @http_get.setter
    def http_get(self, http_get):
        """Sets the http_get of this StartupProbeForUpdateDeploymentInput.


        :param http_get: The http_get of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: HTTPGetForUpdateDeploymentInput
        """

        self._http_get = http_get

    @property
    def initial_delay_seconds(self):
        """Gets the initial_delay_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The initial_delay_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._initial_delay_seconds

    @initial_delay_seconds.setter
    def initial_delay_seconds(self, initial_delay_seconds):
        """Sets the initial_delay_seconds of this StartupProbeForUpdateDeploymentInput.


        :param initial_delay_seconds: The initial_delay_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._initial_delay_seconds = initial_delay_seconds

    @property
    def period_seconds(self):
        """Gets the period_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The period_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._period_seconds

    @period_seconds.setter
    def period_seconds(self, period_seconds):
        """Sets the period_seconds of this StartupProbeForUpdateDeploymentInput.


        :param period_seconds: The period_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._period_seconds = period_seconds

    @property
    def success_threshold(self):
        """Gets the success_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The success_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._success_threshold

    @success_threshold.setter
    def success_threshold(self, success_threshold):
        """Sets the success_threshold of this StartupProbeForUpdateDeploymentInput.


        :param success_threshold: The success_threshold of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._success_threshold = success_threshold

    @property
    def tcp_socket(self):
        """Gets the tcp_socket of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The tcp_socket of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: TCPSocketForUpdateDeploymentInput
        """
        return self._tcp_socket

    @tcp_socket.setter
    def tcp_socket(self, tcp_socket):
        """Sets the tcp_socket of this StartupProbeForUpdateDeploymentInput.


        :param tcp_socket: The tcp_socket of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: TCPSocketForUpdateDeploymentInput
        """

        self._tcp_socket = tcp_socket

    @property
    def timeout_seconds(self):
        """Gets the timeout_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501


        :return: The timeout_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._timeout_seconds

    @timeout_seconds.setter
    def timeout_seconds(self, timeout_seconds):
        """Sets the timeout_seconds of this StartupProbeForUpdateDeploymentInput.


        :param timeout_seconds: The timeout_seconds of this StartupProbeForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._timeout_seconds = timeout_seconds

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartupProbeForUpdateDeploymentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartupProbeForUpdateDeploymentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartupProbeForUpdateDeploymentInput):
            return True

        return self.to_dict() != other.to_dict()
