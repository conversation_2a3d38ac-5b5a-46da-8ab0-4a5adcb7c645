# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackupPodRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pod_id_list': 'list[str]',
        'product_id': 'str'
    }

    attribute_map = {
        'pod_id_list': 'PodIdList',
        'product_id': 'ProductId'
    }

    def __init__(self, pod_id_list=None, product_id=None, _configuration=None):  # noqa: E501
        """BackupPodRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pod_id_list = None
        self._product_id = None
        self.discriminator = None

        if pod_id_list is not None:
            self.pod_id_list = pod_id_list
        self.product_id = product_id

    @property
    def pod_id_list(self):
        """Gets the pod_id_list of this BackupPodRequest.  # noqa: E501


        :return: The pod_id_list of this BackupPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._pod_id_list

    @pod_id_list.setter
    def pod_id_list(self, pod_id_list):
        """Sets the pod_id_list of this BackupPodRequest.


        :param pod_id_list: The pod_id_list of this BackupPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._pod_id_list = pod_id_list

    @property
    def product_id(self):
        """Gets the product_id of this BackupPodRequest.  # noqa: E501


        :return: The product_id of this BackupPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this BackupPodRequest.


        :param product_id: The product_id of this BackupPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product_id is None:
            raise ValueError("Invalid value for `product_id`, must not be `None`")  # noqa: E501

        self._product_id = product_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackupPodRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupPodRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupPodRequest):
            return True

        return self.to_dict() != other.to_dict()
