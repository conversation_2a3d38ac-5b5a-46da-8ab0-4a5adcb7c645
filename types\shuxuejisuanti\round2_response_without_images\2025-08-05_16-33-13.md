## 准确率：49.28%  （(276 - 140) / 276）

## 错题
- 第 1 组响应: 第1组
- 第 3 组响应: 第3组
- 第 6 组响应: 第6组
- 第 12 组响应: 第12组
- 第 13 组响应: 第13组
- 第 14 组响应: 第14组
- 第 17 组响应: 第17组
- 第 18 组响应: 第18组
- 第 19 组响应: 第19组
- 第 20 组响应: 第20组
- 第 23 组响应: 第23组
- 第 28 组响应: 第28组
- 第 31 组响应: 第31组
- 第 32 组响应: 第32组
- 第 34 组响应: 第34组
- 第 35 组响应: 第35组
- 第 36 组响应: 第36组
- 第 37 组响应: 第37组
- 第 40 组响应: 第40组
- 第 41 组响应: 第41组
- 第 42 组响应: 第42组
- 第 45 组响应: 第45组
- 第 51 组响应: 第51组
- 第 52 组响应: 第52组
- 第 53 组响应: 第53组
- 第 57 组响应: 第57组
- 第 59 组响应: 第59组
- 第 60 组响应: 第60组
- 第 63 组响应: 第63组
- 第 64 组响应: 第64组
- 第 65 组响应: 第65组
- 第 69 组响应: 第69组
- 第 70 组响应: 第70组
- 第 71 组响应: 第71组
- 第 72 组响应: 第72组
- 第 75 组响应: 第75组
- 第 76 组响应: 第76组
- 第 81 组响应: 第81组
- 第 82 组响应: 第82组
- 第 83 组响应: 第83组
- 第 86 组响应: 第86组
- 第 89 组响应: 第89组
- 第 93 组响应: 第93组
- 第 94 组响应: 第94组
- 第 96 组响应: 第96组
- 第 98 组响应: 第98组
- 第 100 组响应: 第100组
- 第 102 组响应: 第102组
- 第 103 组响应: 第103组
- 第 105 组响应: 第105组
- 第 106 组响应: 第106组
- 第 107 组响应: 第107组
- 第 108 组响应: 第108组
- 第 109 组响应: 第109组
- 第 112 组响应: 第112组
- 第 113 组响应: 第113组
- 第 114 组响应: 第114组
- 第 115 组响应: 第115组
- 第 121 组响应: 第121组
- 第 123 组响应: 第123组
- 第 124 组响应: 第124组
- 第 125 组响应: 第125组
- 第 127 组响应: 第127组
- 第 129 组响应: 第129组
- 第 130 组响应: 第130组
- 第 131 组响应: 第131组
- 第 132 组响应: 第132组
- 第 133 组响应: 第133组
- 第 135 组响应: 第135组
- 第 140 组响应: 第140组
- 第 141 组响应: 第141组
- 第 142 组响应: 第142组
- 第 143 组响应: 第143组
- 第 146 组响应: 第146组
- 第 147 组响应: 第147组
- 第 149 组响应: 第149组
- 第 150 组响应: 第150组
- 第 152 组响应: 第152组
- 第 153 组响应: 第153组
- 第 154 组响应: 第154组
- 第 156 组响应: 第156组
- 第 158 组响应: 第158组
- 第 159 组响应: 第159组
- 第 161 组响应: 第161组
- 第 162 组响应: 第162组
- 第 163 组响应: 第163组
- 第 164 组响应: 第164组
- 第 166 组响应: 第166组
- 第 169 组响应: 第169组
- 第 172 组响应: 第172组
- 第 175 组响应: 第175组
- 第 176 组响应: 第176组
- 第 179 组响应: 第179组
- 第 181 组响应: 第181组
- 第 182 组响应: 第182组
- 第 183 组响应: 第183组
- 第 184 组响应: 第184组
- 第 186 组响应: 第186组
- 第 187 组响应: 第187组
- 第 190 组响应: 第190组
- 第 192 组响应: 第192组
- 第 193 组响应: 第193组
- 第 196 组响应: 第196组
- 第 197 组响应: 第197组
- 第 198 组响应: 第198组
- 第 199 组响应: 第199组
- 第 201 组响应: 第201组
- 第 204 组响应: 第204组
- 第 207 组响应: 第207组
- 第 208 组响应: 第208组
- 第 212 组响应: 第212组
- 第 213 组响应: 第213组
- 第 216 组响应: 第216组
- 第 218 组响应: 第218组
- 第 220 组响应: 第220组
- 第 221 组响应: 第221组
- 第 223 组响应: 第223组
- 第 224 组响应: 第224组
- 第 225 组响应: 第225组
- 第 227 组响应: 第227组
- 第 237 组响应: 第237组
- 第 238 组响应: 第238组
- 第 241 组响应: 第241组
- 第 243 组响应: 第243组
- 第 246 组响应: 第246组
- 第 249 组响应: 第249组
- 第 250 组响应: 第250组
- 第 251 组响应: 第251组
- 第 252 组响应: 第252组
- 第 254 组响应: 第254组
- 第 257 组响应: 第257组
- 第 258 组响应: 第258组
- 第 260 组响应: 第260组
- 第 264 组响应: 第264组
- 第 266 组响应: 第266组
- 第 267 组响应: 第267组
- 第 268 组响应: 第268组
- 第 273 组响应: 第273组
- 第 274 组响应: 第274组
- 第 276 组响应: 第276组

# 运行时间: 2025-08-05_16-33-13

**批改方式：** JSON比对

**比对说明：** 直接比对学生答案和正确答案的JSON字符串


==================================================
处理第 1 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "1.1", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 2 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "29.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 3 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 4 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 5 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 6 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "NAN", "题目 3": "NAN", "题目 4": "NAN", "题目 5": "NAN", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":false,"题目4":false,"题目5":false,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 7 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "7/12", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 8 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 9 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 10 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 11 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3", "题目 2": "NAN", "题目 3": "7", "题目 4": "13.5"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 12 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "3/5", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 13 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "7/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 14 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 15 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.625", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 16 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 17 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 18 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 19 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.5", "题目 2": "1", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 20 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 21 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 22 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 23 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 24 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "0.1", "题目 4": "7/18", "题目 5": "3/4", "题目 6": "13/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 25 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 26 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 27 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 3/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 28 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3\\frac{5}{7}", "题目 2": "1\\frac{1}{3}", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 29 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "11.2"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 30 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 31 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0010秒

==================================================
处理第 32 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 33 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 34 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.75", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 35 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2/3", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 36 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 37 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "5/8", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 38 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 39 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 40 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 41 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "5/8", "题目 2": "11/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 42 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 43 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 44 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 45 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 46 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 47 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 48 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 49 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 50 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 51 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 52 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 53 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 54 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 55 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 56 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "26.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 57 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 58 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 59 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "2/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 60 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.125", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 61 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 62 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 63 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "40.92", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 64 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 65 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 66 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 67 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 68 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 69 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "2/3", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":false,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 70 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "1/2", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 71 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 72 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 73 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 74 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 75 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 76 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 77 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 78 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 79 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 80 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 81 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "7/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 82 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.00", "题目 2": "40.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 83 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 84 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 85 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "40.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 86 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10 或 0.1", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 87 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 88 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 89 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 90 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "7"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 91 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 92 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 93 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 94 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 95 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 96 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 97 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 98 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "0", "题目 3": "0.1", "题目 4": "7/18", "题目 5": "11/12", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false,"题目5":false,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 99 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 100 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 101 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 102 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 103 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 104 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 105 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 106 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 107 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 108 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 109 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 110 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 111 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "36", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":false,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 112 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 113 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 114 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 115 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "26/7", "题目 2": "1/12", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 116 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 117 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1/8", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 118 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 119 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 120 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 121 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 122 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "5/24", "题目 2": "5/6", "题目 3": "17/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 123 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 124 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.25", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 125 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "9/12", "题目 6": "13/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":false,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 126 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 127 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.75", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 128 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 129 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3/2", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 130 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 131 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 132 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "21/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 133 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 - 2/7", "题目 2": "1", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 134 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 135 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 136 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 137 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 138 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 139 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 140 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 141 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 142 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3", "题目 2": "1", "题目 3": "7", "题目 4": "11.125"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 143 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "11/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 144 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 145 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 146 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 147 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1/2", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 148 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 149 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "19/9", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 150 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 151 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "17/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 152 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 153 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.75", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 154 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.75", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 155 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 156 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.5", "题目 2": "1.5", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 157 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.5", "题目 2": "1.33333333333", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 158 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 159 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 160 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3\\frac{5}{7}", "题目 2": "1\\frac{1}{3}", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 161 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 162 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "35/8", "题目 2": "19/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 163 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "5/6", "题目 3": "1/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 164 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 165 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 166 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 167 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 168 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.7142857142857144", "题目 2": "0.3333333333333333", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 169 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 170 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 171 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2.25", "题目 2": "5/12", "题目 3": "7", "题目 4": "11.25"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 172 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 173 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 174 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 175 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "5/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 176 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 177 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "2/3", "题目 3": "7/10"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 178 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 179 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.25 - 2/7 + 3/4", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 180 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 181 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 182 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 183 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 184 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 185 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "NAN", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 186 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "9/8", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 187 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "36", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":false,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 188 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 189 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 190 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "5/4", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 191 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 192 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 193 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 194 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 195 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 196 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "35/7", "题目 2": "4/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 197 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 198 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "0", "题目 3": "0.1", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 199 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0010秒

==================================================
处理第 200 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 201 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 202 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "11/18", "题目 5": "3/4", "题目 6": "13/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 203 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 204 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 205 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 206 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 207 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "9/8"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 208 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.75", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 209 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 210 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 211 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 212 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19.00", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 213 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 - 2/7", "题目 2": "1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 214 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1/3", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 215 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 216 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/8", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 217 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "2/9", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 218 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "-5/24", "题目 2": "3/2", "题目 3": "7/95"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 219 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 220 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "0.1", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 221 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.5", "题目 2": "1.25", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 222 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 223 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 224 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.5714285714285716", "题目 2": "1.3333333333333333", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 225 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 226 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 227 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "NAN", "题目 5": "3/4", "题目 6": "13/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 228 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 229 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "40.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 230 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 231 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.0", "题目 2": "0.7777777777777777", "题目 3": "7.0", "题目 4": "12.0"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":false,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 232 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 233 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 234 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 235 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "35/7", "题目 2": "4/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 236 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 237 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 2/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 238 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/4", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 239 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 240 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 241 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "13/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 242 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 243 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3.7142857142857144", "题目 2": "1.3333333333333333", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 244 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 245 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 246 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 247 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 248 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 249 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2/3", "题目 2": "7/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 250 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 - 2/7", "题目 2": "1", "题目 3": "7", "题目 4": "12"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 251 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 252 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 26/28", "题目 2": "1 5/9", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 253 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 254 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "1.375", "题目 2": "2.2", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 255 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 256 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 257 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "25.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false}
```
### 响应时间：0.0000秒

==================================================
处理第 258 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 259 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 260 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "4/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "11/8"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 261 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "2/3", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 262 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "1 1/3", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 263 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 264 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "35/7", "题目 2": "1", "题目 3": "7", "题目 4": "13"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true,"题目4":true}
```
### 响应时间：0.0000秒

==================================================
处理第 265 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 266 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.10", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":false,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 267 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "1/2", "题目 5": "3/4", "题目 6": "23/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 268 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "1/3", "题目 3": "1/10", "题目 4": "13/18", "题目 5": "3/4", "题目 6": "NAN"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":false,"题目4":true,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 269 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 270 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "13/24", "题目 2": "5/6", "题目 3": "1/2"}
```

### 正确答案：
```json
{"题目1": "13/24", "题目2": "5/6", "题目3": "1/2"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 271 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3 5/7", "题目 2": "4/3", "题目 3": "7", "题目 4": "13.75"}
```

### 正确答案：
```json
{"题目1": "3 5/7", "题目2": "7/12", "题目3": "7", "题目4": "13"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true,"题目4":false}
```
### 响应时间：0.0000秒

==================================================
处理第 272 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "21.1", "题目 2": "41.52", "题目 3": "27.44"}
```

### 正确答案：
```json
{"题目1": "21.1", "题目2": "41.52", "题目3": "27.44"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 273 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "2", "题目 2": "-1/3", "题目 3": "1/10", "题目 4": "7/18", "题目 5": "3/4", "题目 6": "3/10"}
```

### 正确答案：
```json
{"题目1": "2", "题目2": "1/3", "题目3": "0.1", "题目4": "13/18", "题目5": "3/4", "题目6": "21/20"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":false,"题目4":false,"题目5":true,"题目6":false}
```
### 响应时间：0.0000秒

==================================================
处理第 274 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "3/4", "题目 2": "21/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":false,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
处理第 275 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "19", "题目 2": "30.1", "题目 3": "2700", "题目 4": "2346", "题目 5": "24", "题目 6": "8"}
```

### 正确答案：
```json
{"题目1": "19", "题目2": "30.1", "题目3": "2700", "题目4": "2346", "题目5": "24", "题目6": "8"}
```

### 比对结果：
```json
{"题目1":true,"题目2":true,"题目3":true,"题目4":true,"题目5":true,"题目6":true}
```
### 响应时间：0.0000秒

==================================================
处理第 276 组JSON响应

==================================================
### 学生答案：
```json
{"题目 1": "7/4", "题目 2": "11/5", "题目 3": "4"}
```

### 正确答案：
```json
{"题目1": "7/4", "题目2": "2.2", "题目3": "4"}
```

### 比对结果：
```json
{"题目1":true,"题目2":false,"题目3":true}
```
### 响应时间：0.0000秒

==================================================
所有JSON响应处理完成！
==================================================
