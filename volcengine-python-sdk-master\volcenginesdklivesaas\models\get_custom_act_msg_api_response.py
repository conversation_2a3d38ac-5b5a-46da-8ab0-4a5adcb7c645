# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetCustomActMsgAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'act_basic_msg': 'ActBasicMsgForGetCustomActMsgAPIOutput',
        'act_msg': 'ActMsgForGetCustomActMsgAPIOutput',
        'act_stream_msg': 'ActStreamMsgForGetCustomActMsgAPIOutput',
        'real_time_online_number': 'RealTimeOnlineNumberForGetCustomActMsgAPIOutput'
    }

    attribute_map = {
        'act_basic_msg': 'ActBasicMsg',
        'act_msg': 'ActMsg',
        'act_stream_msg': 'ActStreamMsg',
        'real_time_online_number': 'RealTimeOnlineNumber'
    }

    def __init__(self, act_basic_msg=None, act_msg=None, act_stream_msg=None, real_time_online_number=None, _configuration=None):  # noqa: E501
        """GetCustomActMsgAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._act_basic_msg = None
        self._act_msg = None
        self._act_stream_msg = None
        self._real_time_online_number = None
        self.discriminator = None

        if act_basic_msg is not None:
            self.act_basic_msg = act_basic_msg
        if act_msg is not None:
            self.act_msg = act_msg
        if act_stream_msg is not None:
            self.act_stream_msg = act_stream_msg
        if real_time_online_number is not None:
            self.real_time_online_number = real_time_online_number

    @property
    def act_basic_msg(self):
        """Gets the act_basic_msg of this GetCustomActMsgAPIResponse.  # noqa: E501


        :return: The act_basic_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :rtype: ActBasicMsgForGetCustomActMsgAPIOutput
        """
        return self._act_basic_msg

    @act_basic_msg.setter
    def act_basic_msg(self, act_basic_msg):
        """Sets the act_basic_msg of this GetCustomActMsgAPIResponse.


        :param act_basic_msg: The act_basic_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :type: ActBasicMsgForGetCustomActMsgAPIOutput
        """

        self._act_basic_msg = act_basic_msg

    @property
    def act_msg(self):
        """Gets the act_msg of this GetCustomActMsgAPIResponse.  # noqa: E501


        :return: The act_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :rtype: ActMsgForGetCustomActMsgAPIOutput
        """
        return self._act_msg

    @act_msg.setter
    def act_msg(self, act_msg):
        """Sets the act_msg of this GetCustomActMsgAPIResponse.


        :param act_msg: The act_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :type: ActMsgForGetCustomActMsgAPIOutput
        """

        self._act_msg = act_msg

    @property
    def act_stream_msg(self):
        """Gets the act_stream_msg of this GetCustomActMsgAPIResponse.  # noqa: E501


        :return: The act_stream_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :rtype: ActStreamMsgForGetCustomActMsgAPIOutput
        """
        return self._act_stream_msg

    @act_stream_msg.setter
    def act_stream_msg(self, act_stream_msg):
        """Sets the act_stream_msg of this GetCustomActMsgAPIResponse.


        :param act_stream_msg: The act_stream_msg of this GetCustomActMsgAPIResponse.  # noqa: E501
        :type: ActStreamMsgForGetCustomActMsgAPIOutput
        """

        self._act_stream_msg = act_stream_msg

    @property
    def real_time_online_number(self):
        """Gets the real_time_online_number of this GetCustomActMsgAPIResponse.  # noqa: E501


        :return: The real_time_online_number of this GetCustomActMsgAPIResponse.  # noqa: E501
        :rtype: RealTimeOnlineNumberForGetCustomActMsgAPIOutput
        """
        return self._real_time_online_number

    @real_time_online_number.setter
    def real_time_online_number(self, real_time_online_number):
        """Sets the real_time_online_number of this GetCustomActMsgAPIResponse.


        :param real_time_online_number: The real_time_online_number of this GetCustomActMsgAPIResponse.  # noqa: E501
        :type: RealTimeOnlineNumberForGetCustomActMsgAPIOutput
        """

        self._real_time_online_number = real_time_online_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetCustomActMsgAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetCustomActMsgAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetCustomActMsgAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
