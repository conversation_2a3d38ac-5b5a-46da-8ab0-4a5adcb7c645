## 准确率：12.66%  （(229 - 200) / 229）

## 运行时间: 2025-08-04_09-38-08

**使用模型ID：** doubao-1-5-thinking-vision-pro-250428

**使用图片文件夹：** /images

## 错题

- 第 1 张图片: 002cf098ac2f48c8875dcec1d9b0b1fe.jpg
- 第 2 张图片: 01abb21695654166bd23562c64971dfa.jpg
- 第 3 张图片: 02eb651bfa8e472893842f7a72efba6c.jpg
- 第 4 张图片: 0358ede635cb4cacaf3bcb9712628c68.jpg
- 第 5 张图片: 0414005c92e344be9b79c597c2c953c8.jpg
- 第 6 张图片: 05f34e0dad8c42e694106ca0f86552a5.jpg
- 第 9 张图片: 0b3b587533c942eba0eb0239a635bcd8.jpg
- 第 10 张图片: 0bd05ea3d8f74b5aa04b0960b9f823a5.jpg
- 第 11 张图片: 0c5983ba8dba4ee6aa5a6ac15f5e7436.jpg
- 第 13 张图片: 132326c9797d4c079e6e7576101fb4bb.jpg
- 第 14 张图片: 132420b71c63484dbf6705a828acb44f.jpg
- 第 16 张图片: 14624ee4cf434391a4a7a70044ad214e.jpg
- 第 17 张图片: 160b87e5696142c29b493980624996b6.jpg
- 第 20 张图片: 1a8a67881ee5404fbf12dfa9b4e15ed4.jpg
- 第 21 张图片: 1afa3ad85978477382389db83690aef8.jpg
- 第 22 张图片: 1b2c7904ba97445c833c88c906029ccb.jpg
- 第 23 张图片: 1caaebe2535743d1a431bf15556cdfde.jpg
- 第 24 张图片: 1dad85695bab479dabb3d164cafccb13.jpg
- 第 25 张图片: 1e1e400bda684899ae3337f18e6b8806.jpg
- 第 26 张图片: 1e3b90fac2cc40d4a52dd8468c1ae2c3.jpg
- 第 27 张图片: 1fe220f57c2a4bf2b9f4430b1b027789.jpg
- 第 28 张图片: 23736eaba2f243db80089b7f9575dd59.jpg
- 第 29 张图片: 239c263743844a618c45023540384b73.jpg
- 第 30 张图片: 2406743e2ff24bb3aeec05f7fd268aa7.jpg
- 第 31 张图片: 247f93dfa95642efbf101d820572695e.jpg
- 第 33 张图片: 254bff4730ee4df2a2d5b133442ab38e.jpg
- 第 34 张图片: 2634dac5d8244d708f86f36c2f988818.jpg
- 第 35 张图片: 2670ccd23f8d4877a755fefa6a455717.jpg
- 第 36 张图片: 26a4472722264fa4a4f99534d1c40907.jpg
- 第 38 张图片: 285b4d27195a4a4692edf7eaebb9e8f1.jpg
- 第 39 张图片: 28a87a20d0534d07ad8a1d2807e48ce8.jpg
- 第 40 张图片: 299eab1787da461bbf2deea729143fa4.jpg
- 第 42 张图片: 2de2ab43eb5947f3aaa7613d544f44a3.jpg
- 第 43 张图片: 2e167e8ee617458b9ccc51f61b61aa48.jpg
- 第 44 张图片: 31850f81d8fc41d185c54c5265fecf4d.jpg
- 第 45 张图片: 3281490eb16249d4a8ab122b56db6a9b.jpg
- 第 47 张图片: 346195a382c54b3190741bd250be4c98.jpg
- 第 48 张图片: 3481b56a54ae41cdae8bc3e54a684fcf.jpg
- 第 49 张图片: 39310882aac1471ca6eec4faa478fa2b.jpg
- 第 50 张图片: 3a3afa5843064e7888486bc2da5dec2d.jpg
- 第 51 张图片: 3a72492c11264540a6142130be44b2bd.jpg
- 第 52 张图片: 3b6465c2001c4d23bb1393ea8c704808.jpg
- 第 53 张图片: 3ba0eee977864c749f344f3b8900b7ab.jpg
- 第 54 张图片: 3ccdf22a1d2c484caf92b1aacf72ea42.jpg
- 第 55 张图片: 3d23427d224049c39e129da7efb0569b.jpg
- 第 56 张图片: 3d7097974d09432c863e818ad517dd3f.jpg
- 第 57 张图片: 3d8ea17ca84b47ed9cafdeb9009c170b.jpg
- 第 58 张图片: 3ea542982bd740379bee231af76c5d8f.jpg
- 第 59 张图片: 3ee2bfa7b0d242a4b38be910eb9a0214.jpg
- 第 60 张图片: 3fe77929dc414696acdd2c6886d86307.jpg
- 第 61 张图片: 4026d37912cc439590e6272a58694fa5.jpg
- 第 62 张图片: 4092ec061d7c466d91f37d60db18a406.jpg
- 第 63 张图片: 40d879bb15f249ef891f21094e794fb4.jpg
- 第 64 张图片: 416663c4ddc647bf82d9dfb28fe0aecb.jpg
- 第 65 张图片: 41f0fbd5556741cca8681203a6a926b2.jpg
- 第 66 张图片: 4373fe789c8d4004a2ca8291a0f000db.jpg
- 第 67 张图片: 439978a3614440b7bca80251abe66b11.jpg
- 第 68 张图片: 45673343bc9344aab324abb8ce55618a.jpg
- 第 69 张图片: 45f5e681aa1845fa91258ea4588ac53e.jpg
- 第 70 张图片: 484c5c503cad4cdebad63e5d83f97b9b.jpg
- 第 71 张图片: 49133da5a3c6429da370bab9b3200def.jpg
- 第 72 张图片: 496287d8d652439095d6cfede7cd18f0.jpg
- 第 73 张图片: 4b97f26a892447f8a1636741a2d7f03e.jpg
- 第 74 张图片: 4beaafa50c96457e9bd2ea4ba90c603f.jpg
- 第 75 张图片: 4cfa54af5343444c8e0b2fa7500245d0.jpg
- 第 76 张图片: 4eb2c49ab6f540148151f24e393ff259.jpg
- 第 77 张图片: 51a986fa95444374be9044a791879888.jpg
- 第 78 张图片: 52a43d09d4a04eebbb7046942d64c9ff.jpg
- 第 79 张图片: 52f89af7389c4430b0f1c10c5a8157d5.jpg
- 第 80 张图片: 5452323718b044529795a787b22ff0c7.jpg
- 第 81 张图片: 547d045c346e4a68b41f8879d890de34.jpg
- 第 82 张图片: 5495fb89ad4e46d899123bd018ea1376.jpg
- 第 83 张图片: 56483f122afc499f9643a491de68c0f9.jpg
- 第 84 张图片: 5658f36958fb44dea156e24aed3d2db0.jpg
- 第 85 张图片: 56ed3df457df47f9af1880575c44e833.jpg
- 第 86 张图片: 5735df3d746d43d48621bd1b6351deb7.jpg
- 第 87 张图片: 58542f777aae483887b2dd3bd8362c93.jpg
- 第 88 张图片: 5adb8a8722954947a74817df12108979.jpg
- 第 89 张图片: 5f86fb24d4b9464ea73184a5170be042.jpg
- 第 90 张图片: 5ff5e091cbf54140aea224473a1a31f5.jpg
- 第 92 张图片: 61d59f2e4e2b4b9c91b586932d131c9f.jpg
- 第 93 张图片: 64226ec2847d4c778e031aa073437551.jpg
- 第 94 张图片: 64752517ebeb4769ab04104531860dd2.jpg
- 第 95 张图片: 65d463e0296b4c2ca35b5d372d44bd24.jpg
- 第 96 张图片: 662bc7a17e404975bd5d96c05c890eb0.jpg
- 第 98 张图片: 67288f00dcbf4e60942cfa379ff1b157.jpg
- 第 99 张图片: 67c5de5bbb1247c5b31e6d03d650a080.jpg
- 第 100 张图片: 6833b656bdda450aa6f9d097821817e6.jpg
- 第 102 张图片: 6a7ad51d653b4842b15cabf5632fb774.jpg
- 第 103 张图片: 6b0f0a0d54a24309ab893fc599ee6a47.jpg
- 第 104 张图片: 6bb21452643c4827a64e9c04fd8b664f.jpg
- 第 106 张图片: 6be99771273945cab5c8e9d32c39f0b7.jpg
- 第 108 张图片: 6d6ba9e2a9b74747af3e7b6f7f7f9970.jpg
- 第 109 张图片: 6f1b4f33b4b7471388676b80d6cda6f1.jpg
- 第 110 张图片: 6f96fc80514b46b98639f5a8516f283a.jpg
- 第 111 张图片: 72004225db8d4752b868f4b14ce9e88c.jpg
- 第 112 张图片: 738264fc88bd46d88fb0a881b8b8f973.jpg
- 第 113 张图片: 739e160d74b241f3b7054d6b1f3cb2da.jpg
- 第 114 张图片: 78d5e9be13fc409cb0c12291b6d8853c.jpg
- 第 115 张图片: 7a7f2d9d7d344c98835fe07c26cdf52e.jpg
- 第 116 张图片: 7c881281406e4ef29bf1fed7a205c22f.jpg
- 第 117 张图片: 7d659dd63b504610bee9e8b79a9909d8.jpg
- 第 118 张图片: 7d967b5c397e421eaa4b6869189d1d5f.jpg
- 第 119 张图片: 7d9899c9849f43018825b622edd400f5.jpg
- 第 120 张图片: 80745af9cf464743a20ac7b6946f3bab.jpg
- 第 121 张图片: 80f0bd56c2524d1c9fd3d03598455558.jpg
- 第 122 张图片: 81eeb525838a4ffbb6655b4919d79687.jpg
- 第 123 张图片: 827282a1a5254d3fb55f9cb2215a97c8.jpg
- 第 124 张图片: 8301ed4366a846e08bb1d0d758243442.jpg
- 第 126 张图片: 839b5108ab334e41bdcf17a7b3fe0a4b.jpg
- 第 127 张图片: 83ef05b0ea6e45008724e3968936c503.jpg
- 第 128 张图片: 86b7b5f658de4510a147537f896ebf3d.jpg
- 第 129 张图片: 881a62bc79c84021bdbd1513bace6146.jpg
- 第 131 张图片: 886e4e257fff4443a780d6354dc4d0cc.jpg
- 第 132 张图片: 89f828a1d19848708ea1c060a0e4be97.jpg
- 第 133 张图片: 8a4a39d628bb4db88ddfc047d35138f0.jpg
- 第 134 张图片: 8ce2ea3e8de14ebba552d68accd51287.jpg
- 第 136 张图片: 901fbb4455b24dd694c3b8a2916b9f8f.jpg
- 第 137 张图片: 90554ea04e294196a1d6b8d24180db1a.jpg
- 第 138 张图片: 91b8e26a02724754af13b059e1ccf070.jpg
- 第 139 张图片: 92090a0db2a5481886bd9940e6408a28.jpg
- 第 140 张图片: 92bffaa857034bdba5db29f76e4c81b2.jpg
- 第 141 张图片: 93639c182f9345a79c51e2b68065bfb5.jpg
- 第 142 张图片: 95bcb1f8685e4bc28ee7f2209e59f57a.jpg
- 第 143 张图片: 992d73dde3784db2948c5a905fb202ea.jpg
- 第 144 张图片: 99a2930d933041318d28d7b70bb95aa0.jpg
- 第 145 张图片: 9a44fd045a9c47ec964c829cbef5cf7f.jpg
- 第 146 张图片: 9ce5a1a1e8ec481fb9f720586efb860d.jpg
- 第 147 张图片: 9d2922b1438b41c4a9134183879ad249.jpg
- 第 148 张图片: 9d5e5248735f42e4b6c8771a2ceb3276.jpg
- 第 149 张图片: 9ebc665685a44402bc84ddfe490a1900.jpg
- 第 152 张图片: a549046cc7424690bff2ef0657dc0a7b.jpg
- 第 153 张图片: a70c3b6f770c44eabf38c0b4493e8bef.jpg
- 第 154 张图片: a7d472508fcf468eaac815601637a7bd.jpg
- 第 155 张图片: a7f6e5bccd4c4686bd405507533f2d74.jpg
- 第 156 张图片: a8526c8ad1c64a8d8bf18058ed7776d7.jpg
- 第 157 张图片: a8853294185e4f62b97d74d78c90158e.jpg
- 第 158 张图片: a960816d13b3430f924a0b4217b51556.jpg
- 第 159 张图片: acab53833dfc4c1385090d6f57939cf2.jpg
- 第 160 张图片: acb478940bcb42ee9b4e1609a826e6fe.jpg
- 第 163 张图片: added6712b1b4d45b7321a896c1901e5.jpg
- 第 164 张图片: ae1fbb2b88e34ddb93336176df3bfa7a.jpg
- 第 165 张图片: aeadef9cb5d04acdb8a30bd9af2e50b6.jpg
- 第 166 张图片: b1bd9b53174f4b8595dbd2c53881bc5d.jpg
- 第 167 张图片: b34416f8071d4712bec11bf13d72798c.jpg
- 第 168 张图片: b48693ac4697476092be149e65e54351.jpg
- 第 169 张图片: b5cec55d6c414e9e90fcf0cf3494ac9b.jpg
- 第 170 张图片: b898ccfbddf142f9a33e6c1c2e65e8a3.jpg
- 第 171 张图片: bb54699bc2804def931e88d28b26e236.jpg
- 第 172 张图片: bc1af58474dd4492b0e2182504e08378.jpg
- 第 173 张图片: bc6d3931d9c140c6a9685a87f2912a92.jpg
- 第 174 张图片: bd29129e9ed24b718a73407d5d33d813.jpg
- 第 175 张图片: bdc307020fb6426c8f8e7aa36f5f8d17.jpg
- 第 177 张图片: bdee1c81501b4855a7cf54b18c4a157f.jpg
- 第 178 张图片: bf6228847a9e42d5a26a7db6cbd745fc.jpg
- 第 179 张图片: c0bf9ad762e448a081c5e10d8fb78290.jpg
- 第 180 张图片: c1e4967445dd4649a350c4e9919ce913.jpg
- 第 181 张图片: c1f7b4897b9c4a1eacecc20e54032372.jpg
- 第 183 张图片: c2e077c3560d4a3099fc486c6af79663.jpg
- 第 184 张图片: c5acae343fad4b52b231b76c4994e9b2.jpg
- 第 185 张图片: c7e0b75961984615ac351dfd8887a766.jpg
- 第 186 张图片: c8e8b1e468594fa6abfd18751b825b80.jpg
- 第 187 张图片: cce1dcb9a7ed4915a15ac5d35ba47f16.jpg
- 第 189 张图片: cd8a73eae00844ab97e5f21dcd5729b5.jpg
- 第 190 张图片: ce30aab0847e4bac89ae4139d6333bf9.jpg
- 第 191 张图片: cf31ec5c77144e39bbf805acf84e4cdb.jpg
- 第 192 张图片: d29bf96b9d2b4c3c999490a2da97156f.jpg
- 第 194 张图片: d3427ef8c04446b88087377f2f2b2669.jpg
- 第 195 张图片: d50f3727fa084b5fbc6d12fa9c5506b4.jpg
- 第 197 张图片: d61367bef3f24652b3f4187f09221667.jpg
- 第 198 张图片: d7d2abdb4e2c402896dc473b0ae57542.jpg
- 第 199 张图片: db40142f5a6444ed98f0eeb4132b83cc.jpg
- 第 200 张图片: dd6ae6a2feb344a290c91f910bc8efde.jpg
- 第 201 张图片: dd87b5fe25fc4765b395a462b184417e.jpg
- 第 202 张图片: df31058ab4764077a7a83b5d5f26a67f.jpg
- 第 203 张图片: dfede1d7c33a4267bd232867f861d086.jpg
- 第 204 张图片: e081f4660f0943fda23873c1124036b7.jpg
- 第 205 张图片: e1b9cd14579b40a98a8322dff5ffaefd.jpg
- 第 206 张图片: e1d2f251e3f147b7add52171ba5a531a.jpg
- 第 207 张图片: e6224f1beaa742d7a70e6c263903c193.jpg
- 第 208 张图片: e6800a5888f249bfbec93c34b2073b66.jpg
- 第 209 张图片: e73e1d5a2ffb4a008d914a407239edf1.jpg
- 第 210 张图片: e7bd1ffa92a34854a6b97ce3b872e584.jpg
- 第 211 张图片: e83813d2697d4f2092ab06440afe1ba3.jpg
- 第 212 张图片: eb182bff84d8443ca7671606e08c4091.jpg
- 第 214 张图片: ef6ab6a5102e4406a63a92feaa0d8e04.jpg
- 第 215 张图片: f02603e2f8374bfebfda13bb7906f163.jpg
- 第 216 张图片: f0447c9f4a5745339874a1784976024b.jpg
- 第 217 张图片: f0a112878a344f4faf7ca425223c12b9.jpg
- 第 218 张图片: f140473c3cfc40ee96d85897ebfaba23.jpg
- 第 219 张图片: f51b118087d74a7aa53198f1a3c42451.jpg
- 第 220 张图片: f5e41e510df14bc19b977d18c10870b7.jpg
- 第 221 张图片: f5f853f270bc4c5f86683c4abc11c63c.jpg
- 第 222 张图片: f694b6fec9064436bcda75ac283ce26c.jpg
- 第 223 张图片: f918c14ee2f34b9c94f75ba31649123e.jpg
- 第 224 张图片: f9738dcb43414323843ece49d76c05dc.jpg
- 第 225 张图片: fb4c238236bd49f78f794318358f007c.jpg
- 第 226 张图片: fb554953a12c4644a291bfc0523a4aff.jpg
- 第 228 张图片: ff9ea26555d0430b8bd91aed314649aa.jpg
- 第 229 张图片: ffeb8bb186e544b7b8d28968de788b41.jpg

==================================================
处理第 1 张图片: 002cf098ac2f48c8875dcec1d9b0b1fe.jpg

==================================================
![002cf098ac2f48c8875dcec1d9b0b1fe.jpg](../images/002cf098ac2f48c8875dcec1d9b0b1fe.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略275550个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.48秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 2 张图片: 01abb21695654166bd23562c64971dfa.jpg

==================================================
![01abb21695654166bd23562c64971dfa.jpg](../images/01abb21695654166bd23562c64971dfa.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略258406个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.14秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 3 张图片: 02eb651bfa8e472893842f7a72efba6c.jpg

==================================================
![02eb651bfa8e472893842f7a72efba6c.jpg](../images/02eb651bfa8e472893842f7a72efba6c.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略67290个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.67秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 4 张图片: 0358ede635cb4cacaf3bcb9712628c68.jpg

==================================================
![0358ede635cb4cacaf3bcb9712628c68.jpg](../images/0358ede635cb4cacaf3bcb9712628c68.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略253038个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.35秒
### token用量
- total_tokens: 2051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 5 张图片: 0414005c92e344be9b79c597c2c953c8.jpg

==================================================
![0414005c92e344be9b79c597c2c953c8.jpg](../images/0414005c92e344be9b79c597c2c953c8.jpg)

### response_template答案：
```json
{"题目 1":"21","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略243022个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.30秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 6 张图片: 05f34e0dad8c42e694106ca0f86552a5.jpg

==================================================
![05f34e0dad8c42e694106ca0f86552a5.jpg](../images/05f34e0dad8c42e694106ca0f86552a5.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目3":"20.19","题目4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略190626个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.82秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 9 张图片: 0b3b587533c942eba0eb0239a635bcd8.jpg

==================================================
![0b3b587533c942eba0eb0239a635bcd8.jpg](../images/0b3b587533c942eba0eb0239a635bcd8.jpg)

### response_template答案：
```json
{"题目 1": "sweep", "题目 2": "floor", "题目 3": "thosn", "题目 4": "lont","题目5"："on"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略160762个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.49秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 10 张图片: 0bd05ea3d8f74b5aa04b0960b9f823a5.jpg

==================================================
![0bd05ea3d8f74b5aa04b0960b9f823a5.jpg](../images/0bd05ea3d8f74b5aa04b0960b9f823a5.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"☆","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略164118个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.32秒
### token用量
- total_tokens: 1370
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 11 张图片: 0c5983ba8dba4ee6aa5a6ac15f5e7436.jpg

==================================================
![0c5983ba8dba4ee6aa5a6ac15f5e7436.jpg](../images/0c5983ba8dba4ee6aa5a6ac15f5e7436.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略69522个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.77秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 13 张图片: 132326c9797d4c079e6e7576101fb4bb.jpg

==================================================
![132326c9797d4c079e6e7576101fb4bb.jpg](../images/132326c9797d4c079e6e7576101fb4bb.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略68570个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.55秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 14 张图片: 132420b71c63484dbf6705a828acb44f.jpg

==================================================
![132420b71c63484dbf6705a828acb44f.jpg](../images/132420b71c63484dbf6705a828acb44f.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want balloons!","题目 3":"Let's some draw nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273638个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.47秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 16 张图片: 14624ee4cf434391a4a7a70044ad214e.jpg

==================================================
![14624ee4cf434391a4a7a70044ad214e.jpg](../images/14624ee4cf434391a4a7a70044ad214e.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略69010个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.18秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 17 张图片: 160b87e5696142c29b493980624996b6.jpg

==================================================
![160b87e5696142c29b493980624996b6.jpg](../images/160b87e5696142c29b493980624996b6.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略195282个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.08秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 20 张图片: 1a8a67881ee5404fbf12dfa9b4e15ed4.jpg

==================================================
![1a8a67881ee5404fbf12dfa9b4e15ed4.jpg](../images/1a8a67881ee5404fbf12dfa9b4e15ed4.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略60362个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.65秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 21 张图片: 1afa3ad85978477382389db83690aef8.jpg

==================================================
![1afa3ad85978477382389db83690aef8.jpg](../images/1afa3ad85978477382389db83690aef8.jpg)

### response_template答案：
```json
{"题目 1":"2.7","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略187026个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.17秒
### token用量
- total_tokens: 1361
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 22 张图片: 1b2c7904ba97445c833c88c906029ccb.jpg

==================================================
![1b2c7904ba97445c833c88c906029ccb.jpg](../images/1b2c7904ba97445c833c88c906029ccb.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略189914个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.74秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 23 张图片: 1caaebe2535743d1a431bf15556cdfde.jpg

==================================================
![1caaebe2535743d1a431bf15556cdfde.jpg](../images/1caaebe2535743d1a431bf15556cdfde.jpg)

### response_template答案：
```json
{"题目 1":"8.56","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略256658个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.40秒
### token用量
- total_tokens: 2051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 24 张图片: 1dad85695bab479dabb3d164cafccb13.jpg

==================================================
![1dad85695bab479dabb3d164cafccb13.jpg](../images/1dad85695bab479dabb3d164cafccb13.jpg)

### response_template答案：
```json
{"题目 1":"2.7","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191838个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.00秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 25 张图片: 1e1e400bda684899ae3337f18e6b8806.jpg

==================================================
![1e1e400bda684899ae3337f18e6b8806.jpg](../images/1e1e400bda684899ae3337f18e6b8806.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.75秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 26 张图片: 1e3b90fac2cc40d4a52dd8468c1ae2c3.jpg

==================================================
![1e3b90fac2cc40d4a52dd8468c1ae2c3.jpg](../images/1e3b90fac2cc40d4a52dd8468c1ae2c3.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"90020","题目 4":"10020"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191946个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.51秒
### token用量
- total_tokens: 1361
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 27 张图片: 1fe220f57c2a4bf2b9f4430b1b027789.jpg

==================================================
![1fe220f57c2a4bf2b9f4430b1b027789.jpg](../images/1fe220f57c2a4bf2b9f4430b1b027789.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"7","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略253702个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.19秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 28 张图片: 23736eaba2f243db80089b7f9575dd59.jpg

==================================================
![23736eaba2f243db80089b7f9575dd59.jpg](../images/23736eaba2f243db80089b7f9575dd59.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略83610个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.64秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 29 张图片: 239c263743844a618c45023540384b73.jpg

==================================================
![239c263743844a618c45023540384b73.jpg](../images/239c263743844a618c45023540384b73.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略193730个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.75秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 30 张图片: 2406743e2ff24bb3aeec05f7fd268aa7.jpg

==================================================
![2406743e2ff24bb3aeec05f7fd268aa7.jpg](../images/2406743e2ff24bb3aeec05f7fd268aa7.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34.04","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.72秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 31 张图片: 247f93dfa95642efbf101d820572695e.jpg

==================================================
![247f93dfa95642efbf101d820572695e.jpg](../images/247f93dfa95642efbf101d820572695e.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63590个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.21秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 33 张图片: 254bff4730ee4df2a2d5b133442ab38e.jpg

==================================================
![254bff4730ee4df2a2d5b133442ab38e.jpg](../images/254bff4730ee4df2a2d5b133442ab38e.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略285266个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.06秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 34 张图片: 2634dac5d8244d708f86f36c2f988818.jpg

==================================================
![2634dac5d8244d708f86f36c2f988818.jpg](../images/2634dac5d8244d708f86f36c2f988818.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略157174个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.74秒
### token用量
- total_tokens: 1370
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 35 张图片: 2670ccd23f8d4877a755fefa6a455717.jpg

==================================================
![2670ccd23f8d4877a755fefa6a455717.jpg](../images/2670ccd23f8d4877a755fefa6a455717.jpg)

### response_template答案：
```json
{"题目 1":"<","题目 2":"=","题目 3":">","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62474个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.08秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 36 张图片: 26a4472722264fa4a4f99534d1c40907.jpg

==================================================
![26a4472722264fa4a4f99534d1c40907.jpg](../images/26a4472722264fa4a4f99534d1c40907.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略271478个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.58秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 38 张图片: 285b4d27195a4a4692edf7eaebb9e8f1.jpg

==================================================
![285b4d27195a4a4692edf7eaebb9e8f1.jpg](../images/285b4d27195a4a4692edf7eaebb9e8f1.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略281350个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.47秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 39 张图片: 28a87a20d0534d07ad8a1d2807e48ce8.jpg

==================================================
![28a87a20d0534d07ad8a1d2807e48ce8.jpg](../images/28a87a20d0534d07ad8a1d2807e48ce8.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略177650个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.77秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 40 张图片: 299eab1787da461bbf2deea729143fa4.jpg

==================================================
![299eab1787da461bbf2deea729143fa4.jpg](../images/299eab1787da461bbf2deea729143fa4.jpg)

### response_template答案：
```json
{"题目 1":"8.9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略251042个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.17秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 42 张图片: 2de2ab43eb5947f3aaa7613d544f44a3.jpg

==================================================
![2de2ab43eb5947f3aaa7613d544f44a3.jpg](../images/2de2ab43eb5947f3aaa7613d544f44a3.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略163518个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.60秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 43 张图片: 2e167e8ee617458b9ccc51f61b61aa48.jpg

==================================================
![2e167e8ee617458b9ccc51f61b61aa48.jpg](../images/2e167e8ee617458b9ccc51f61b61aa48.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw pictures some nice."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273510个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.86秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 44 张图片: 31850f81d8fc41d185c54c5265fecf4d.jpg

==================================================
![31850f81d8fc41d185c54c5265fecf4d.jpg](../images/31850f81d8fc41d185c54c5265fecf4d.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"3.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略174750个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：6.06秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 45 张图片: 3281490eb16249d4a8ab122b56db6a9b.jpg

==================================================
![3281490eb16249d4a8ab122b56db6a9b.jpg](../images/3281490eb16249d4a8ab122b56db6a9b.jpg)

### response_template答案：
```json
{"题目 1": "sweep", "题目 2": "floor", "题目 3": "under", "题目 4": "grapes","题目5"："fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略161074个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.21秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 47 张图片: 346195a382c54b3190741bd250be4c98.jpg

==================================================
![346195a382c54b3190741bd250be4c98.jpg](../images/346195a382c54b3190741bd250be4c98.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略224758个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.92秒
### token用量
- total_tokens: 2051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 48 张图片: 3481b56a54ae41cdae8bc3e54a684fcf.jpg

==================================================
![3481b56a54ae41cdae8bc3e54a684fcf.jpg](../images/3481b56a54ae41cdae8bc3e54a684fcf.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略269214个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.65秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 49 张图片: 39310882aac1471ca6eec4faa478fa2b.jpg

==================================================
![39310882aac1471ca6eec4faa478fa2b.jpg](../images/39310882aac1471ca6eec4faa478fa2b.jpg)

### response_template答案：
```json
{"题目 1":"9.01","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略276458个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.57秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 50 张图片: 3a3afa5843064e7888486bc2da5dec2d.jpg

==================================================
![3a3afa5843064e7888486bc2da5dec2d.jpg](../images/3a3afa5843064e7888486bc2da5dec2d.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76050个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.01秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 51 张图片: 3a72492c11264540a6142130be44b2bd.jpg

==================================================
![3a72492c11264540a6142130be44b2bd.jpg](../images/3a72492c11264540a6142130be44b2bd.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"=","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略66442个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.14秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 52 张图片: 3b6465c2001c4d23bb1393ea8c704808.jpg

==================================================
![3b6465c2001c4d23bb1393ea8c704808.jpg](../images/3b6465c2001c4d23bb1393ea8c704808.jpg)

### response_template答案：
```json
{"题目 1": "sweep", "题目 2": "floor", "题目 3": "under", "题目 4": "oranges","题目5"："fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略171942个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.06秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 53 张图片: 3ba0eee977864c749f344f3b8900b7ab.jpg

==================================================
![3ba0eee977864c749f344f3b8900b7ab.jpg](../images/3ba0eee977864c749f344f3b8900b7ab.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":"="}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略65382个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.07秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 54 张图片: 3ccdf22a1d2c484caf92b1aacf72ea42.jpg

==================================================
![3ccdf22a1d2c484caf92b1aacf72ea42.jpg](../images/3ccdf22a1d2c484caf92b1aacf72ea42.jpg)

### response_template答案：
```json
{"题目 1": "sweep", "题目 2": "floor", "题目 3": "under", "题目 4": "oranges","题目5"："fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略157178个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.76秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 55 张图片: 3d23427d224049c39e129da7efb0569b.jpg

==================================================
![3d23427d224049c39e129da7efb0569b.jpg](../images/3d23427d224049c39e129da7efb0569b.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I Want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略279702个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.15秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 56 张图片: 3d7097974d09432c863e818ad517dd3f.jpg

==================================================
![3d7097974d09432c863e818ad517dd3f.jpg](../images/3d7097974d09432c863e818ad517dd3f.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略194258个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.40秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 57 张图片: 3d8ea17ca84b47ed9cafdeb9009c170b.jpg

==================================================
![3d8ea17ca84b47ed9cafdeb9009c170b.jpg](../images/3d8ea17ca84b47ed9cafdeb9009c170b.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34.04","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略200566个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.00秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 58 张图片: 3ea542982bd740379bee231af76c5d8f.jpg

==================================================
![3ea542982bd740379bee231af76c5d8f.jpg](../images/3ea542982bd740379bee231af76c5d8f.jpg)

### response_template答案：
```json
{"题目 1":"加法","题目 2":"5.30","题目 3":"☆","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略173122个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.00秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 59 张图片: 3ee2bfa7b0d242a4b38be910eb9a0214.jpg

==================================================
![3ee2bfa7b0d242a4b38be910eb9a0214.jpg](../images/3ee2bfa7b0d242a4b38be910eb9a0214.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273738个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.98秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 60 张图片: 3fe77929dc414696acdd2c6886d86307.jpg

==================================================
![3fe77929dc414696acdd2c6886d86307.jpg](../images/3fe77929dc414696acdd2c6886d86307.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63378个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.03秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 61 张图片: 4026d37912cc439590e6272a58694fa5.jpg

==================================================
![4026d37912cc439590e6272a58694fa5.jpg](../images/4026d37912cc439590e6272a58694fa5.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略75422个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：2.71秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 62 张图片: 4092ec061d7c466d91f37d60db18a406.jpg

==================================================
![4092ec061d7c466d91f37d60db18a406.jpg](../images/4092ec061d7c466d91f37d60db18a406.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.4","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略167954个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.08秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 63 张图片: 40d879bb15f249ef891f21094e794fb4.jpg

==================================================
![40d879bb15f249ef891f21094e794fb4.jpg](../images/40d879bb15f249ef891f21094e794fb4.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略175230个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.97秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 64 张图片: 416663c4ddc647bf82d9dfb28fe0aecb.jpg

==================================================
![416663c4ddc647bf82d9dfb28fe0aecb.jpg](../images/416663c4ddc647bf82d9dfb28fe0aecb.jpg)

### response_template答案：
```json
{"题目 1":"four","题目 2":"NAN","题目 3":"sheep","题目 4":"NAN","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略157098个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.98秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 65 张图片: 41f0fbd5556741cca8681203a6a926b2.jpg

==================================================
![41f0fbd5556741cca8681203a6a926b2.jpg](../images/41f0fbd5556741cca8681203a6a926b2.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略179126个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：6.19秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 66 张图片: 4373fe789c8d4004a2ca8291a0f000db.jpg

==================================================
![4373fe789c8d4004a2ca8291a0f000db.jpg](../images/4373fe789c8d4004a2ca8291a0f000db.jpg)

### response_template答案：
```json
{"题目 1":"2.7400","题目 2":"34","题目 3":"10.19","题目 4":"10.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略189694个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.99秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 67 张图片: 439978a3614440b7bca80251abe66b11.jpg

==================================================
![439978a3614440b7bca80251abe66b11.jpg](../images/439978a3614440b7bca80251abe66b11.jpg)

### response_template答案：
```json
{"题目 1":"9.1","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略257646个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.21秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 68 张图片: 45673343bc9344aab324abb8ce55618a.jpg

==================================================
![45673343bc9344aab324abb8ce55618a.jpg](../images/45673343bc9344aab324abb8ce55618a.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略175966个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.64秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 69 张图片: 45f5e681aa1845fa91258ea4588ac53e.jpg

==================================================
![45f5e681aa1845fa91258ea4588ac53e.jpg](../images/45f5e681aa1845fa91258ea4588ac53e.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略68994个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.45秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 70 张图片: 484c5c503cad4cdebad63e5d83f97b9b.jpg

==================================================
![484c5c503cad4cdebad63e5d83f97b9b.jpg](../images/484c5c503cad4cdebad63e5d83f97b9b.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略74262个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.48秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 71 张图片: 49133da5a3c6429da370bab9b3200def.jpg

==================================================
![49133da5a3c6429da370bab9b3200def.jpg](../images/49133da5a3c6429da370bab9b3200def.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.7"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略192366个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.51秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 72 张图片: 496287d8d652439095d6cfede7cd18f0.jpg

==================================================
![496287d8d652439095d6cfede7cd18f0.jpg](../images/496287d8d652439095d6cfede7cd18f0.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.4","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170450个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.39秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 73 张图片: 4b97f26a892447f8a1636741a2d7f03e.jpg

==================================================
![4b97f26a892447f8a1636741a2d7f03e.jpg](../images/4b97f26a892447f8a1636741a2d7f03e.jpg)

### response_template答案：
```json
{"题目 1":"8.9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273682个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.32秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 74 张图片: 4beaafa50c96457e9bd2ea4ba90c603f.jpg

==================================================
![4beaafa50c96457e9bd2ea4ba90c603f.jpg](../images/4beaafa50c96457e9bd2ea4ba90c603f.jpg)

### response_template答案：
```json
{"题目 1":"<","题目 2":"=","题目 3":">","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63674个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.94秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 75 张图片: 4cfa54af5343444c8e0b2fa7500245d0.jpg

==================================================
![4cfa54af5343444c8e0b2fa7500245d0.jpg](../images/4cfa54af5343444c8e0b2fa7500245d0.jpg)

### response_template答案：
```json
{"题目 1":"27","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略226318个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.00秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 76 张图片: 4eb2c49ab6f540148151f24e393ff259.jpg

==================================================
![4eb2c49ab6f540148151f24e393ff259.jpg](../images/4eb2c49ab6f540148151f24e393ff259.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略198618个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.09秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 77 张图片: 51a986fa95444374be9044a791879888.jpg

==================================================
![51a986fa95444374be9044a791879888.jpg](../images/51a986fa95444374be9044a791879888.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"apples","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略160406个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.26秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 78 张图片: 52a43d09d4a04eebbb7046942d64c9ff.jpg

==================================================
![52a43d09d4a04eebbb7046942d64c9ff.jpg](../images/52a43d09d4a04eebbb7046942d64c9ff.jpg)

### response_template答案：
```json
{"题目 1":"45.1","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略259826个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.71秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 79 张图片: 52f89af7389c4430b0f1c10c5a8157d5.jpg

==================================================
![52f89af7389c4430b0f1c10c5a8157d5.jpg](../images/52f89af7389c4430b0f1c10c5a8157d5.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN","题目 4":"NAN","题目 5":"NAN"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略148358个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.15秒
### token用量
- total_tokens: 2231
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 80 张图片: 5452323718b044529795a787b22ff0c7.jpg

==================================================
![5452323718b044529795a787b22ff0c7.jpg](../images/5452323718b044529795a787b22ff0c7.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63610个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.40秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 81 张图片: 547d045c346e4a68b41f8879d890de34.jpg

==================================================
![547d045c346e4a68b41f8879d890de34.jpg](../images/547d045c346e4a68b41f8879d890de34.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略280742个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.90秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 82 张图片: 5495fb89ad4e46d899123bd018ea1376.jpg

==================================================
![5495fb89ad4e46d899123bd018ea1376.jpg](../images/5495fb89ad4e46d899123bd018ea1376.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"oranges","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略161802个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.71秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 83 张图片: 56483f122afc499f9643a491de68c0f9.jpg

==================================================
![56483f122afc499f9643a491de68c0f9.jpg](../images/56483f122afc499f9643a491de68c0f9.jpg)

### response_template答案：
```json
{"题目 1":"27","题目 2":"34","题目 3":"9101","题目 4":"9"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略188450个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.22秒
### token用量
- total_tokens: 1363
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 84 张图片: 5658f36958fb44dea156e24aed3d2db0.jpg

==================================================
![5658f36958fb44dea156e24aed3d2db0.jpg](../images/5658f36958fb44dea156e24aed3d2db0.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"☆","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略164566个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.22秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 85 张图片: 56ed3df457df47f9af1880575c44e833.jpg

==================================================
![56ed3df457df47f9af1880575c44e833.jpg](../images/56ed3df457df47f9af1880575c44e833.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略65290个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.38秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 86 张图片: 5735df3d746d43d48621bd1b6351deb7.jpg

==================================================
![5735df3d746d43d48621bd1b6351deb7.jpg](../images/5735df3d746d43d48621bd1b6351deb7.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略190530个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.63秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 87 张图片: 58542f777aae483887b2dd3bd8362c93.jpg

==================================================
![58542f777aae483887b2dd3bd8362c93.jpg](../images/58542f777aae483887b2dd3bd8362c93.jpg)

### response_template答案：
```json
{"题目 1":"45.10","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略247774个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.69秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 88 张图片: 5adb8a8722954947a74817df12108979.jpg

==================================================
![5adb8a8722954947a74817df12108979.jpg](../images/5adb8a8722954947a74817df12108979.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略180554个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.55秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 89 张图片: 5f86fb24d4b9464ea73184a5170be042.jpg

==================================================
![5f86fb24d4b9464ea73184a5170be042.jpg](../images/5f86fb24d4b9464ea73184a5170be042.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want balloons colourful!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略283274个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.47秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 90 张图片: 5ff5e091cbf54140aea224473a1a31f5.jpg

==================================================
![5ff5e091cbf54140aea224473a1a31f5.jpg](../images/5ff5e091cbf54140aea224473a1a31f5.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略263226个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.66秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 92 张图片: 61d59f2e4e2b4b9c91b586932d131c9f.jpg

==================================================
![61d59f2e4e2b4b9c91b586932d131c9f.jpg](../images/61d59f2e4e2b4b9c91b586932d131c9f.jpg)

### response_template答案：
```json
{"题目 1":"<","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62530个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.26秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 93 张图片: 64226ec2847d4c778e031aa073437551.jpg

==================================================
![64226ec2847d4c778e031aa073437551.jpg](../images/64226ec2847d4c778e031aa073437551.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略64478个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.19秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 94 张图片: 64752517ebeb4769ab04104531860dd2.jpg

==================================================
![64752517ebeb4769ab04104531860dd2.jpg](../images/64752517ebeb4769ab04104531860dd2.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":"="}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略64610个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.31秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 95 张图片: 65d463e0296b4c2ca35b5d372d44bd24.jpg

==================================================
![65d463e0296b4c2ca35b5d372d44bd24.jpg](../images/65d463e0296b4c2ca35b5d372d44bd24.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some pictures nice."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略302002个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.68秒
### token用量
- total_tokens: 2876
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 96 张图片: 662bc7a17e404975bd5d96c05c890eb0.jpg

==================================================
![662bc7a17e404975bd5d96c05c890eb0.jpg](../images/662bc7a17e404975bd5d96c05c890eb0.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略171010个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.40秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 98 张图片: 67288f00dcbf4e60942cfa379ff1b157.jpg

==================================================
![67288f00dcbf4e60942cfa379ff1b157.jpg](../images/67288f00dcbf4e60942cfa379ff1b157.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34.04","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略190786个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.51秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 99 张图片: 67c5de5bbb1247c5b31e6d03d650a080.jpg

==================================================
![67c5de5bbb1247c5b31e6d03d650a080.jpg](../images/67c5de5bbb1247c5b31e6d03d650a080.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略249334个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.41秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 100 张图片: 6833b656bdda450aa6f9d097821817e6.jpg

==================================================
![6833b656bdda450aa6f9d097821817e6.jpg](../images/6833b656bdda450aa6f9d097821817e6.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170866个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.06秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 102 张图片: 6a7ad51d653b4842b15cabf5632fb774.jpg

==================================================
![6a7ad51d653b4842b15cabf5632fb774.jpg](../images/6a7ad51d653b4842b15cabf5632fb774.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"10.19","题目 4":"11"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略185898个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.89秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 103 张图片: 6b0f0a0d54a24309ab893fc599ee6a47.jpg

==================================================
![6b0f0a0d54a24309ab893fc599ee6a47.jpg](../images/6b0f0a0d54a24309ab893fc599ee6a47.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I wand colourful balloons!","题目 3":"Let's draw some nice plotures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略293814个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.84秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 104 张图片: 6bb21452643c4827a64e9c04fd8b664f.jpg

==================================================
![6bb21452643c4827a64e9c04fd8b664f.jpg](../images/6bb21452643c4827a64e9c04fd8b664f.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Lets some draw nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略301210个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.12秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 106 张图片: 6be99771273945cab5c8e9d32c39f0b7.jpg

==================================================
![6be99771273945cab5c8e9d32c39f0b7.jpg](../images/6be99771273945cab5c8e9d32c39f0b7.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略68366个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.60秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 108 张图片: 6d6ba9e2a9b74747af3e7b6f7f7f9970.jpg

==================================================
![6d6ba9e2a9b74747af3e7b6f7f7f9970.jpg](../images/6d6ba9e2a9b74747af3e7b6f7f7f9970.jpg)

### response_template答案：
```json
{"题目 1":"加法交换","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略164070个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.54秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 109 张图片: 6f1b4f33b4b7471388676b80d6cda6f1.jpg

==================================================
![6f1b4f33b4b7471388676b80d6cda6f1.jpg](../images/6f1b4f33b4b7471388676b80d6cda6f1.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略172150个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.19秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 110 张图片: 6f96fc80514b46b98639f5a8516f283a.jpg

==================================================
![6f96fc80514b46b98639f5a8516f283a.jpg](../images/6f96fc80514b46b98639f5a8516f283a.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略287902个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.95秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 111 张图片: 72004225db8d4752b868f4b14ce9e88c.jpg

==================================================
![72004225db8d4752b868f4b14ce9e88c.jpg](../images/72004225db8d4752b868f4b14ce9e88c.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I whant colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略309598个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.15秒
### token用量
- total_tokens: 2876
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 112 张图片: 738264fc88bd46d88fb0a881b8b8f973.jpg

==================================================
![738264fc88bd46d88fb0a881b8b8f973.jpg](../images/738264fc88bd46d88fb0a881b8b8f973.jpg)

### response_template答案：
```json
{"题目 1":"27","题目 2":"34","题目 3":"9101","题目 4":"9"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略187358个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.76秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 113 张图片: 739e160d74b241f3b7054d6b1f3cb2da.jpg

==================================================
![739e160d74b241f3b7054d6b1f3cb2da.jpg](../images/739e160d74b241f3b7054d6b1f3cb2da.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN","题目 4":"fhis"}
```
### 响应内容：
```json
{"题目1": "NAN", "题目2": "NAN", "题目3": "NAN", "题目4": "错误"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略150170个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.77秒
### token用量
- total_tokens: 2230
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 114 张图片: 78d5e9be13fc409cb0c12291b6d8853c.jpg

==================================================
![78d5e9be13fc409cb0c12291b6d8853c.jpg](../images/78d5e9be13fc409cb0c12291b6d8853c.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略165582个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.31秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 115 张图片: 7a7f2d9d7d344c98835fe07c26cdf52e.jpg

==================================================
![7a7f2d9d7d344c98835fe07c26cdf52e.jpg](../images/7a7f2d9d7d344c98835fe07c26cdf52e.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略176914个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.11秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 116 张图片: 7c881281406e4ef29bf1fed7a205c22f.jpg

==================================================
![7c881281406e4ef29bf1fed7a205c22f.jpg](../images/7c881281406e4ef29bf1fed7a205c22f.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"540","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略167766个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.07秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 117 张图片: 7d659dd63b504610bee9e8b79a9909d8.jpg

==================================================
![7d659dd63b504610bee9e8b79a9909d8.jpg](../images/7d659dd63b504610bee9e8b79a9909d8.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略279322个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.54秒
### token用量
- total_tokens: 2876
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 118 张图片: 7d967b5c397e421eaa4b6869189d1d5f.jpg

==================================================
![7d967b5c397e421eaa4b6869189d1d5f.jpg](../images/7d967b5c397e421eaa4b6869189d1d5f.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略301742个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.52秒
### token用量
- total_tokens: 2876
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 119 张图片: 7d9899c9849f43018825b622edd400f5.jpg

==================================================
![7d9899c9849f43018825b622edd400f5.jpg](../images/7d9899c9849f43018825b622edd400f5.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62778个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.35秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 120 张图片: 80745af9cf464743a20ac7b6946f3bab.jpg

==================================================
![80745af9cf464743a20ac7b6946f3bab.jpg](../images/80745af9cf464743a20ac7b6946f3bab.jpg)

### response_template答案：
```json
{"题目 1":"16","题目 2":"6","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "NAN"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略242198个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.61秒
### token用量
- total_tokens: 2037
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 121 张图片: 80f0bd56c2524d1c9fd3d03598455558.jpg

==================================================
![80f0bd56c2524d1c9fd3d03598455558.jpg](../images/80f0bd56c2524d1c9fd3d03598455558.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62486个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.47秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 122 张图片: 81eeb525838a4ffbb6655b4919d79687.jpg

==================================================
![81eeb525838a4ffbb6655b4919d79687.jpg](../images/81eeb525838a4ffbb6655b4919d79687.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略270646个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.38秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 123 张图片: 827282a1a5254d3fb55f9cb2215a97c8.jpg

==================================================
![827282a1a5254d3fb55f9cb2215a97c8.jpg](../images/827282a1a5254d3fb55f9cb2215a97c8.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略233730个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.47秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 124 张图片: 8301ed4366a846e08bb1d0d758243442.jpg

==================================================
![8301ed4366a846e08bb1d0d758243442.jpg](../images/8301ed4366a846e08bb1d0d758243442.jpg)

### response_template答案：
```json
{"题目 1": "sweep", "题目 2": "floor", "题目 3": "under", "题目 4": "oranges","题目5"："NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略162682个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.24秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 126 张图片: 839b5108ab334e41bdcf17a7b3fe0a4b.jpg

==================================================
![839b5108ab334e41bdcf17a7b3fe0a4b.jpg](../images/839b5108ab334e41bdcf17a7b3fe0a4b.jpg)

### response_template答案：
```json
{"题目 1":"45.1","题目 2":"12","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略274174个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.48秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 127 张图片: 83ef05b0ea6e45008724e3968936c503.jpg

==================================================
![83ef05b0ea6e45008724e3968936c503.jpg](../images/83ef05b0ea6e45008724e3968936c503.jpg)

### response_template答案：
```json
{"题目 1":"12.4","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略258426个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.12秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 128 张图片: 86b7b5f658de4510a147537f896ebf3d.jpg

==================================================
![86b7b5f658de4510a147537f896ebf3d.jpg](../images/86b7b5f658de4510a147537f896ebf3d.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackbeard,","题目 2":"I want colourful balloons!","题目 3":"Let's some draw nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略277658个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.05秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 129 张图片: 881a62bc79c84021bdbd1513bace6146.jpg

==================================================
![881a62bc79c84021bdbd1513bace6146.jpg](../images/881a62bc79c84021bdbd1513bace6146.jpg)

### response_template答案：
```json
{"题目 1":"2.8","题目 2":"34","题目3":" 20.19","题目4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略187894个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.40秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 131 张图片: 886e4e257fff4443a780d6354dc4d0cc.jpg

==================================================
![886e4e257fff4443a780d6354dc4d0cc.jpg](../images/886e4e257fff4443a780d6354dc4d0cc.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"orange","题目5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略166070个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.88秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 132 张图片: 89f828a1d19848708ea1c060a0e4be97.jpg

==================================================
![89f828a1d19848708ea1c060a0e4be97.jpg](../images/89f828a1d19848708ea1c060a0e4be97.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略70862个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.21秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 133 张图片: 8a4a39d628bb4db88ddfc047d35138f0.jpg

==================================================
![8a4a39d628bb4db88ddfc047d35138f0.jpg](../images/8a4a39d628bb4db88ddfc047d35138f0.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略177998个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.26秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 134 张图片: 8ce2ea3e8de14ebba552d68accd51287.jpg

==================================================
![8ce2ea3e8de14ebba552d68accd51287.jpg](../images/8ce2ea3e8de14ebba552d68accd51287.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略61774个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.96秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 136 张图片: 901fbb4455b24dd694c3b8a2916b9f8f.jpg

==================================================
![901fbb4455b24dd694c3b8a2916b9f8f.jpg](../images/901fbb4455b24dd694c3b8a2916b9f8f.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略182594个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.34秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 137 张图片: 90554ea04e294196a1d6b8d24180db1a.jpg

==================================================
![90554ea04e294196a1d6b8d24180db1a.jpg](../images/90554ea04e294196a1d6b8d24180db1a.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略171842个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.16秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 138 张图片: 91b8e26a02724754af13b059e1ccf070.jpg

==================================================
![91b8e26a02724754af13b059e1ccf070.jpg](../images/91b8e26a02724754af13b059e1ccf070.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"orange","题目5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略162210个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.56秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 139 张图片: 92090a0db2a5481886bd9940e6408a28.jpg

==================================================
![92090a0db2a5481886bd9940e6408a28.jpg](../images/92090a0db2a5481886bd9940e6408a28.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"NAN","题目 4":"apple","题目 5":"sweet"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略159422个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.41秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 140 张图片: 92bffaa857034bdba5db29f76e4c81b2.jpg

==================================================
![92bffaa857034bdba5db29f76e4c81b2.jpg](../images/92bffaa857034bdba5db29f76e4c81b2.jpg)

### response_template答案：
```json
{"题目 1":"9.1","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略257046个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.30秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 141 张图片: 93639c182f9345a79c51e2b68065bfb5.jpg

==================================================
![93639c182f9345a79c51e2b68065bfb5.jpg](../images/93639c182f9345a79c51e2b68065bfb5.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"f","题目 3":"NAN","题目 4":"NAN","题目5":"fies"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略148622个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.87秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 142 张图片: 95bcb1f8685e4bc28ee7f2209e59f57a.jpg

==================================================
![95bcb1f8685e4bc28ee7f2209e59f57a.jpg](../images/95bcb1f8685e4bc28ee7f2209e59f57a.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略171902个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.91秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 143 张图片: 992d73dde3784db2948c5a905fb202ea.jpg

==================================================
![992d73dde3784db2948c5a905fb202ea.jpg](../images/992d73dde3784db2948c5a905fb202ea.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170178个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.48秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 144 张图片: 99a2930d933041318d28d7b70bb95aa0.jpg

==================================================
![99a2930d933041318d28d7b70bb95aa0.jpg](../images/99a2930d933041318d28d7b70bb95aa0.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略276670个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.94秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 145 张图片: 9a44fd045a9c47ec964c829cbef5cf7f.jpg

==================================================
![9a44fd045a9c47ec964c829cbef5cf7f.jpg](../images/9a44fd045a9c47ec964c829cbef5cf7f.jpg)

### response_template答案：
```json
{"题目 1":"9.02","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略274378个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.80秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 146 张图片: 9ce5a1a1e8ec481fb9f720586efb860d.jpg

==================================================
![9ce5a1a1e8ec481fb9f720586efb860d.jpg](../images/9ce5a1a1e8ec481fb9f720586efb860d.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard","题目 2":"I want balloons colourfull!","题目 3":"Let's draw some nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略313122个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.48秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 147 张图片: 9d2922b1438b41c4a9134183879ad249.jpg

==================================================
![9d2922b1438b41c4a9134183879ad249.jpg](../images/9d2922b1438b41c4a9134183879ad249.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略69414个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.45秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 148 张图片: 9d5e5248735f42e4b6c8771a2ceb3276.jpg

==================================================
![9d5e5248735f42e4b6c8771a2ceb3276.jpg](../images/9d5e5248735f42e4b6c8771a2ceb3276.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.4","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略172822个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.85秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 149 张图片: 9ebc665685a44402bc84ddfe490a1900.jpg

==================================================
![9ebc665685a44402bc84ddfe490a1900.jpg](../images/9ebc665685a44402bc84ddfe490a1900.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"oranges","题目5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略163218个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.79秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 152 张图片: a549046cc7424690bff2ef0657dc0a7b.jpg

==================================================
![a549046cc7424690bff2ef0657dc0a7b.jpg](../images/a549046cc7424690bff2ef0657dc0a7b.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170186个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.51秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 153 张图片: a70c3b6f770c44eabf38c0b4493e8bef.jpg

==================================================
![a70c3b6f770c44eabf38c0b4493e8bef.jpg](../images/a70c3b6f770c44eabf38c0b4493e8bef.jpg)

### response_template答案：
```json
{"题目 1":"加法交换率","题目 2":"5.4","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略165250个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.28秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 154 张图片: a7d472508fcf468eaac815601637a7bd.jpg

==================================================
![a7d472508fcf468eaac815601637a7bd.jpg](../images/a7d472508fcf468eaac815601637a7bd.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"apples","题目5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略158610个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：6.18秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 155 张图片: a7f6e5bccd4c4686bd405507533f2d74.jpg

==================================================
![a7f6e5bccd4c4686bd405507533f2d74.jpg](../images/a7f6e5bccd4c4686bd405507533f2d74.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.190","题目 4":"20.200"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略188806个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.03秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 156 张图片: a8526c8ad1c64a8d8bf18058ed7776d7.jpg

==================================================
![a8526c8ad1c64a8d8bf18058ed7776d7.jpg](../images/a8526c8ad1c64a8d8bf18058ed7776d7.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略306918个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：6.72秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 157 张图片: a8853294185e4f62b97d74d78c90158e.jpg

==================================================
![a8853294185e4f62b97d74d78c90158e.jpg](../images/a8853294185e4f62b97d74d78c90158e.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"10.19","题目 4":"20.19"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略195154个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.41秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 158 张图片: a960816d13b3430f924a0b4217b51556.jpg

==================================================
![a960816d13b3430f924a0b4217b51556.jpg](../images/a960816d13b3430f924a0b4217b51556.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the black bcurd.","题目 2":"I Want colourful balloo ns!","题目 3":"Let's draw Some nice pictures"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略288482个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.83秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 159 张图片: acab53833dfc4c1385090d6f57939cf2.jpg

==================================================
![acab53833dfc4c1385090d6f57939cf2.jpg](../images/acab53833dfc4c1385090d6f57939cf2.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略195002个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.04秒
### token用量
- total_tokens: 1361
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 160 张图片: acb478940bcb42ee9b4e1609a826e6fe.jpg

==================================================
![acb478940bcb42ee9b4e1609a826e6fe.jpg](../images/acb478940bcb42ee9b4e1609a826e6fe.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略264102个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.38秒
### token用量
- total_tokens: 2051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 163 张图片: added6712b1b4d45b7321a896c1901e5.jpg

==================================================
![added6712b1b4d45b7321a896c1901e5.jpg](../images/added6712b1b4d45b7321a896c1901e5.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略69570个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.51秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 164 张图片: ae1fbb2b88e34ddb93336176df3bfa7a.jpg

==================================================
![ae1fbb2b88e34ddb93336176df3bfa7a.jpg](../images/ae1fbb2b88e34ddb93336176df3bfa7a.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略285218个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.21秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 165 张图片: aeadef9cb5d04acdb8a30bd9af2e50b6.jpg

==================================================
![aeadef9cb5d04acdb8a30bd9af2e50b6.jpg](../images/aeadef9cb5d04acdb8a30bd9af2e50b6.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略280522个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.56秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 166 张图片: b1bd9b53174f4b8595dbd2c53881bc5d.jpg

==================================================
![b1bd9b53174f4b8595dbd2c53881bc5d.jpg](../images/b1bd9b53174f4b8595dbd2c53881bc5d.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略165846个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.04秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 167 张图片: b34416f8071d4712bec11bf13d72798c.jpg

==================================================
![b34416f8071d4712bec11bf13d72798c.jpg](../images/b34416f8071d4712bec11bf13d72798c.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"10.19","题目 4":"10.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191242个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.66秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 168 张图片: b48693ac4697476092be149e65e54351.jpg

==================================================
![b48693ac4697476092be149e65e54351.jpg](../images/b48693ac4697476092be149e65e54351.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"10.19","题目 4":"10.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略188502个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.79秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 169 张图片: b5cec55d6c414e9e90fcf0cf3494ac9b.jpg

==================================================
![b5cec55d6c414e9e90fcf0cf3494ac9b.jpg](../images/b5cec55d6c414e9e90fcf0cf3494ac9b.jpg)

### response_template答案：
```json
{"题目 1":"3","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略208098个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.61秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 170 张图片: b898ccfbddf142f9a33e6c1c2e65e8a3.jpg

==================================================
![b898ccfbddf142f9a33e6c1c2e65e8a3.jpg](../images/b898ccfbddf142f9a33e6c1c2e65e8a3.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"fruit","题目 3":"under","题目 4":"apples","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略156698个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.96秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 171 张图片: bb54699bc2804def931e88d28b26e236.jpg

==================================================
![bb54699bc2804def931e88d28b26e236.jpg](../images/bb54699bc2804def931e88d28b26e236.jpg)

### response_template答案：
```json
{"题目 1":"NAN","题目 2":"NAN","题目 3":"NAN","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略152726个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.39秒
### token用量
- total_tokens: 1370
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 172 张图片: bc1af58474dd4492b0e2182504e08378.jpg

==================================================
![bc1af58474dd4492b0e2182504e08378.jpg](../images/bc1af58474dd4492b0e2182504e08378.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"under","题目5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略166270个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.89秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 173 张图片: bc6d3931d9c140c6a9685a87f2912a92.jpg

==================================================
![bc6d3931d9c140c6a9685a87f2912a92.jpg](../images/bc6d3931d9c140c6a9685a87f2912a92.jpg)

### response_template答案：
```json
{"题目 1":"加法结合律","题目 2":"5.39","题目 3":"4","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略161030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.63秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 174 张图片: bd29129e9ed24b718a73407d5d33d813.jpg

==================================================
![bd29129e9ed24b718a73407d5d33d813.jpg](../images/bd29129e9ed24b718a73407d5d33d813.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略164058个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.75秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 175 张图片: bdc307020fb6426c8f8e7aa36f5f8d17.jpg

==================================================
![bdc307020fb6426c8f8e7aa36f5f8d17.jpg](../images/bdc307020fb6426c8f8e7aa36f5f8d17.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略167994个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.43秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 177 张图片: bdee1c81501b4855a7cf54b18c4a157f.jpg

==================================================
![bdee1c81501b4855a7cf54b18c4a157f.jpg](../images/bdee1c81501b4855a7cf54b18c4a157f.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191258个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.78秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 178 张图片: bf6228847a9e42d5a26a7db6cbd745fc.jpg

==================================================
![bf6228847a9e42d5a26a7db6cbd745fc.jpg](../images/bf6228847a9e42d5a26a7db6cbd745fc.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"orangs","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "错误", "题目4": "错误"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略159942个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.61秒
### token用量
- total_tokens: 2227
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 179 张图片: c0bf9ad762e448a081c5e10d8fb78290.jpg

==================================================
![c0bf9ad762e448a081c5e10d8fb78290.jpg](../images/c0bf9ad762e448a081c5e10d8fb78290.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略257734个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.74秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 180 张图片: c1e4967445dd4649a350c4e9919ce913.jpg

==================================================
![c1e4967445dd4649a350c4e9919ce913.jpg](../images/c1e4967445dd4649a350c4e9919ce913.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want cololrful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略286586个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.15秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 181 张图片: c1f7b4897b9c4a1eacecc20e54032372.jpg

==================================================
![c1f7b4897b9c4a1eacecc20e54032372.jpg](../images/c1f7b4897b9c4a1eacecc20e54032372.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略189106个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.13秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 183 张图片: c2e077c3560d4a3099fc486c6af79663.jpg

==================================================
![c2e077c3560d4a3099fc486c6af79663.jpg](../images/c2e077c3560d4a3099fc486c6af79663.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard,","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略295938个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.81秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 184 张图片: c5acae343fad4b52b231b76c4994e9b2.jpg

==================================================
![c5acae343fad4b52b231b76c4994e9b2.jpg](../images/c5acae343fad4b52b231b76c4994e9b2.jpg)

### response_template答案：
```json
{"题目 1":"9.7","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略282770个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.92秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 185 张图片: c7e0b75961984615ac351dfd8887a766.jpg

==================================================
![c7e0b75961984615ac351dfd8887a766.jpg](../images/c7e0b75961984615ac351dfd8887a766.jpg)

### response_template答案：
```json
{"题目 1":"I clenecanthe blackdoard","题目 2":"balloos I Cotourfl","题目 3":"some draw Let's nice"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "错误"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略293714个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.01秒
### token用量
- total_tokens: 2885
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 186 张图片: c8e8b1e468594fa6abfd18751b825b80.jpg

==================================================
![c8e8b1e468594fa6abfd18751b825b80.jpg](../images/c8e8b1e468594fa6abfd18751b825b80.jpg)

### response_template答案：
```json
{"题目 1":"0.274","题目 2":"34","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略193614个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.56秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 187 张图片: cce1dcb9a7ed4915a15ac5d35ba47f16.jpg

==================================================
![cce1dcb9a7ed4915a15ac5d35ba47f16.jpg](../images/cce1dcb9a7ed4915a15ac5d35ba47f16.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"8"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略182230个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.38秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 189 张图片: cd8a73eae00844ab97e5f21dcd5729b5.jpg

==================================================
![cd8a73eae00844ab97e5f21dcd5729b5.jpg](../images/cd8a73eae00844ab97e5f21dcd5729b5.jpg)

### response_template答案：
```json
{"题目 1":"8.5","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略244634个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.89秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 190 张图片: ce30aab0847e4bac89ae4139d6333bf9.jpg

==================================================
![ce30aab0847e4bac89ae4139d6333bf9.jpg](../images/ce30aab0847e4bac89ae4139d6333bf9.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"10.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略191514个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.92秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 191 张图片: cf31ec5c77144e39bbf805acf84e4cdb.jpg

==================================================
![cf31ec5c77144e39bbf805acf84e4cdb.jpg](../images/cf31ec5c77144e39bbf805acf84e4cdb.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"20.19","题目 4":"20.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略199298个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.47秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 192 张图片: d29bf96b9d2b4c3c999490a2da97156f.jpg

==================================================
![d29bf96b9d2b4c3c999490a2da97156f.jpg](../images/d29bf96b9d2b4c3c999490a2da97156f.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34","题目 3":"10.19","题目 4":"10.20"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略189870个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.12秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 194 张图片: d3427ef8c04446b88087377f2f2b2669.jpg

==================================================
![d3427ef8c04446b88087377f2f2b2669.jpg](../images/d3427ef8c04446b88087377f2f2b2669.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 3":"under","题目 4":"NAN","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略156670个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.91秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 195 张图片: d50f3727fa084b5fbc6d12fa9c5506b4.jpg

==================================================
![d50f3727fa084b5fbc6d12fa9c5506b4.jpg](../images/d50f3727fa084b5fbc6d12fa9c5506b4.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273610个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.01秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 197 张图片: d61367bef3f24652b3f4187f09221667.jpg

==================================================
![d61367bef3f24652b3f4187f09221667.jpg](../images/d61367bef3f24652b3f4187f09221667.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略170430个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.49秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 198 张图片: d7d2abdb4e2c402896dc473b0ae57542.jpg

==================================================
![d7d2abdb4e2c402896dc473b0ae57542.jpg](../images/d7d2abdb4e2c402896dc473b0ae57542.jpg)

### response_template答案：
```json
{"题目 1":"加法结合律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略162602个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.52秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 199 张图片: db40142f5a6444ed98f0eeb4132b83cc.jpg

==================================================
![db40142f5a6444ed98f0eeb4132b83cc.jpg](../images/db40142f5a6444ed98f0eeb4132b83cc.jpg)

### response_template答案：
```json
{"题目 1":"27","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略254030个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.34秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 200 张图片: dd6ae6a2feb344a290c91f910bc8efde.jpg

==================================================
![dd6ae6a2feb344a290c91f910bc8efde.jpg](../images/dd6ae6a2feb344a290c91f910bc8efde.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略76718个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.43秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 201 张图片: dd87b5fe25fc4765b395a462b184417e.jpg

==================================================
![dd87b5fe25fc4765b395a462b184417e.jpg](../images/dd87b5fe25fc4765b395a462b184417e.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.4","题目 3":"NAN","题目 4":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略164010个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.20秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 202 张图片: df31058ab4764077a7a83b5d5f26a67f.jpg

==================================================
![df31058ab4764077a7a83b5d5f26a67f.jpg](../images/df31058ab4764077a7a83b5d5f26a67f.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略66058个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.09秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 203 张图片: dfede1d7c33a4267bd232867f861d086.jpg

==================================================
![dfede1d7c33a4267bd232867f861d086.jpg](../images/dfede1d7c33a4267bd232867f861d086.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want balloons colourful!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略291598个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.78秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 204 张图片: e081f4660f0943fda23873c1124036b7.jpg

==================================================
![e081f4660f0943fda23873c1124036b7.jpg](../images/e081f4660f0943fda23873c1124036b7.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略246674个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.70秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 205 张图片: e1b9cd14579b40a98a8322dff5ffaefd.jpg

==================================================
![e1b9cd14579b40a98a8322dff5ffaefd.jpg](../images/e1b9cd14579b40a98a8322dff5ffaefd.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略66822个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.94秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 206 张图片: e1d2f251e3f147b7add52171ba5a531a.jpg

==================================================
![e1d2f251e3f147b7add52171ba5a531a.jpg](../images/e1d2f251e3f147b7add52171ba5a531a.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63102个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.05秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 207 张图片: e6224f1beaa742d7a70e6c263903c193.jpg

==================================================
![e6224f1beaa742d7a70e6c263903c193.jpg](../images/e6224f1beaa742d7a70e6c263903c193.jpg)

### response_template答案：
```json
{"题目 1":"9.","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略251190个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.84秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 208 张图片: e6800a5888f249bfbec93c34b2073b66.jpg

==================================================
![e6800a5888f249bfbec93c34b2073b66.jpg](../images/e6800a5888f249bfbec93c34b2073b66.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"under","题目 4":"oranges","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略160570个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.88秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 209 张图片: e73e1d5a2ffb4a008d914a407239edf1.jpg

==================================================
![e73e1d5a2ffb4a008d914a407239edf1.jpg](../images/e73e1d5a2ffb4a008d914a407239edf1.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略267606个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.89秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 210 张图片: e7bd1ffa92a34854a6b97ce3b872e584.jpg

==================================================
![e7bd1ffa92a34854a6b97ce3b872e584.jpg](../images/e7bd1ffa92a34854a6b97ce3b872e584.jpg)

### response_template答案：
```json
{"题目 1":"2.8","题目 2":"34","题目 3":"20.29","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略193266个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.41秒
### token用量
- total_tokens: 1373
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 211 张图片: e83813d2697d4f2092ab06440afe1ba3.jpg

==================================================
![e83813d2697d4f2092ab06440afe1ba3.jpg](../images/e83813d2697d4f2092ab06440afe1ba3.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"21","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略296278个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.15秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 212 张图片: eb182bff84d8443ca7671606e08c4091.jpg

==================================================
![eb182bff84d8443ca7671606e08c4091.jpg](../images/eb182bff84d8443ca7671606e08c4091.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略67838个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.69秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 214 张图片: ef6ab6a5102e4406a63a92feaa0d8e04.jpg

==================================================
![ef6ab6a5102e4406a63a92feaa0d8e04.jpg](../images/ef6ab6a5102e4406a63a92feaa0d8e04.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I Want balloons colourful!","题目 3":"Let's draw some nice pictures."}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略280078个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.69秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 215 张图片: f02603e2f8374bfebfda13bb7906f163.jpg

==================================================
![f02603e2f8374bfebfda13bb7906f163.jpg](../images/f02603e2f8374bfebfda13bb7906f163.jpg)

### response_template答案：
```json
{"题目 1":"8.9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略269026个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.24秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 216 张图片: f0447c9f4a5745339874a1784976024b.jpg

==================================================
![f0447c9f4a5745339874a1784976024b.jpg](../images/f0447c9f4a5745339874a1784976024b.jpg)

### response_template答案：
```json
{"题目 1":"sweep","题目 2":"floor","题目 3":"and","题目 4":"orange","题目 5":"fish"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容", "题目4": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略158642个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.64秒
### token用量
- total_tokens: 2247
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 217 张图片: f0a112878a344f4faf7ca425223c12b9.jpg

==================================================
![f0a112878a344f4faf7ca425223c12b9.jpg](../images/f0a112878a344f4faf7ca425223c12b9.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"12","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略280242个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.22秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 218 张图片: f140473c3cfc40ee96d85897ebfaba23.jpg

==================================================
![f140473c3cfc40ee96d85897ebfaba23.jpg](../images/f140473c3cfc40ee96d85897ebfaba23.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":">","题目 3":"<","题目 4":"="}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62842个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.96秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 219 张图片: f51b118087d74a7aa53198f1a3c42451.jpg

==================================================
![f51b118087d74a7aa53198f1a3c42451.jpg](../images/f51b118087d74a7aa53198f1a3c42451.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"12","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略273902个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：5.03秒
### token用量
- total_tokens: 2051
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 220 张图片: f5e41e510df14bc19b977d18c10870b7.jpg

==================================================
![f5e41e510df14bc19b977d18c10870b7.jpg](../images/f5e41e510df14bc19b977d18c10870b7.jpg)

### response_template答案：
```json
{"题目 1":"I can clean the blackboard.","题目 2":"I want colourful balloons!","题目 3":"Let's draw some nice pictures,"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容", "题目2": "未识别到有效答题内容", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略279766个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：4.21秒
### token用量
- total_tokens: 2900
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 221 张图片: f5f853f270bc4c5f86683c4abc11c63c.jpg

==================================================
![f5f853f270bc4c5f86683c4abc11c63c.jpg](../images/f5f853f270bc4c5f86683c4abc11c63c.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略286318个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.72秒
### token用量
- total_tokens: 2027
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 222 张图片: f694b6fec9064436bcda75ac283ce26c.jpg

==================================================
![f694b6fec9064436bcda75ac283ce26c.jpg](../images/f694b6fec9064436bcda75ac283ce26c.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"=","题目 4":"="}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略62394个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.16秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 223 张图片: f918c14ee2f34b9c94f75ba31649123e.jpg

==================================================
![f918c14ee2f34b9c94f75ba31649123e.jpg](../images/f918c14ee2f34b9c94f75ba31649123e.jpg)

### response_template答案：
```json
{"题目 1":"2.74","题目 2":"34.04","题目 3":"20.19","题目 4":"20.2"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略189910个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：3.21秒
### token用量
- total_tokens: 1361
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 224 张图片: f9738dcb43414323843ece49d76c05dc.jpg

==================================================
![f9738dcb43414323843ece49d76c05dc.jpg](../images/f9738dcb43414323843ece49d76c05dc.jpg)

### response_template答案：
```json
{"题目 1":"＜","题目 2":"=","题目 3":"＜","题目 4":"＞"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略64890个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：2.74秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 225 张图片: fb4c238236bd49f78f794318358f007c.jpg

==================================================
![fb4c238236bd49f78f794318358f007c.jpg](../images/fb4c238236bd49f78f794318358f007c.jpg)

### response_template答案：
```json
{"题目 1":"加法交换律","题目 2":"5.40","题目 3":"8","题目 4":"0"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略166134个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：2.41秒
### token用量
- total_tokens: 1346
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 226 张图片: fb554953a12c4644a291bfc0523a4aff.jpg

==================================================
![fb554953a12c4644a291bfc0523a4aff.jpg](../images/fb554953a12c4644a291bfc0523a4aff.jpg)

### response_template答案：
```json
{"题目 1":"9","题目 2":"9","题目 3":"NAN"}
```
### 响应内容：
```json
{"题目1": "错误", "题目2": "错误", "题目3": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略250246个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：2.79秒
### token用量
- total_tokens: 2041
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 228 张图片: ff9ea26555d0430b8bd91aed314649aa.jpg

==================================================
![ff9ea26555d0430b8bd91aed314649aa.jpg](../images/ff9ea26555d0430b8bd91aed314649aa.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":"<"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略63686个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：1.92秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
处理第 229 张图片: ffeb8bb186e544b7b8d28968de788b41.jpg

==================================================
![ffeb8bb186e544b7b8d28968de788b41.jpg](../images/ffeb8bb186e544b7b8d28968de788b41.jpg)

### response_template答案：
```json
{"题目 1":">","题目 2":"=","题目 3":"<","题目 4":">"}
```
### 响应内容：
```json
{"题目1": "未识别到有效答题内容"}
```
### 请求体：
```json
{
  "model": "doubao-1-5-thinking-vision-pro-250428",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "data:image/jpeg;base64,/9j/4AAQSk...[省略70810个字符]",
            "detail": "high"
          }
        }
      ]
    }
  ],
  "max_tokens": 4096,
  "thinking": {
    "type": "disabled"
  }
}
```
### 响应时间：1.80秒
### token用量
- total_tokens: 761
- cached_tokens: 0
- reasoning_tokens: 0

==================================================
所有错题处理完成！
==================================================
