# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LoadBalancerSpecForDescribeLoadBalancerSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'chinese_spec': 'str',
        'english_spec': 'str',
        'max_connections': 'int',
        'new_connections_per_second': 'int',
        'queries_per_second': 'int'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'chinese_spec': 'ChineseSpec',
        'english_spec': 'EnglishSpec',
        'max_connections': 'MaxConnections',
        'new_connections_per_second': 'NewConnectionsPerSecond',
        'queries_per_second': 'QueriesPerSecond'
    }

    def __init__(self, bandwidth=None, chinese_spec=None, english_spec=None, max_connections=None, new_connections_per_second=None, queries_per_second=None, _configuration=None):  # noqa: E501
        """LoadBalancerSpecForDescribeLoadBalancerSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._chinese_spec = None
        self._english_spec = None
        self._max_connections = None
        self._new_connections_per_second = None
        self._queries_per_second = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if chinese_spec is not None:
            self.chinese_spec = chinese_spec
        if english_spec is not None:
            self.english_spec = english_spec
        if max_connections is not None:
            self.max_connections = max_connections
        if new_connections_per_second is not None:
            self.new_connections_per_second = new_connections_per_second
        if queries_per_second is not None:
            self.queries_per_second = queries_per_second

    @property
    def bandwidth(self):
        """Gets the bandwidth of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The bandwidth of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param bandwidth: The bandwidth of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def chinese_spec(self):
        """Gets the chinese_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The chinese_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._chinese_spec

    @chinese_spec.setter
    def chinese_spec(self, chinese_spec):
        """Sets the chinese_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param chinese_spec: The chinese_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: str
        """

        self._chinese_spec = chinese_spec

    @property
    def english_spec(self):
        """Gets the english_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The english_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._english_spec

    @english_spec.setter
    def english_spec(self, english_spec):
        """Sets the english_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param english_spec: The english_spec of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: str
        """

        self._english_spec = english_spec

    @property
    def max_connections(self):
        """Gets the max_connections of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The max_connections of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_connections

    @max_connections.setter
    def max_connections(self, max_connections):
        """Sets the max_connections of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param max_connections: The max_connections of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: int
        """

        self._max_connections = max_connections

    @property
    def new_connections_per_second(self):
        """Gets the new_connections_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The new_connections_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._new_connections_per_second

    @new_connections_per_second.setter
    def new_connections_per_second(self, new_connections_per_second):
        """Sets the new_connections_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param new_connections_per_second: The new_connections_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: int
        """

        self._new_connections_per_second = new_connections_per_second

    @property
    def queries_per_second(self):
        """Gets the queries_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501


        :return: The queries_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._queries_per_second

    @queries_per_second.setter
    def queries_per_second(self, queries_per_second):
        """Sets the queries_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.


        :param queries_per_second: The queries_per_second of this LoadBalancerSpecForDescribeLoadBalancerSpecsOutput.  # noqa: E501
        :type: int
        """

        self._queries_per_second = queries_per_second

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LoadBalancerSpecForDescribeLoadBalancerSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LoadBalancerSpecForDescribeLoadBalancerSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LoadBalancerSpecForDescribeLoadBalancerSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
