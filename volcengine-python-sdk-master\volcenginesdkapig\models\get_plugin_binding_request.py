# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetPluginBindingRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'plugin_name': 'str',
        'scope': 'str',
        'target': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'plugin_name': 'PluginName',
        'scope': 'Scope',
        'target': 'Target'
    }

    def __init__(self, id=None, plugin_name=None, scope=None, target=None, _configuration=None):  # noqa: E501
        """GetPluginBindingRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._plugin_name = None
        self._scope = None
        self._target = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if plugin_name is not None:
            self.plugin_name = plugin_name
        if scope is not None:
            self.scope = scope
        if target is not None:
            self.target = target

    @property
    def id(self):
        """Gets the id of this GetPluginBindingRequest.  # noqa: E501


        :return: The id of this GetPluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetPluginBindingRequest.


        :param id: The id of this GetPluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def plugin_name(self):
        """Gets the plugin_name of this GetPluginBindingRequest.  # noqa: E501


        :return: The plugin_name of this GetPluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._plugin_name

    @plugin_name.setter
    def plugin_name(self, plugin_name):
        """Sets the plugin_name of this GetPluginBindingRequest.


        :param plugin_name: The plugin_name of this GetPluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._plugin_name = plugin_name

    @property
    def scope(self):
        """Gets the scope of this GetPluginBindingRequest.  # noqa: E501


        :return: The scope of this GetPluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._scope

    @scope.setter
    def scope(self, scope):
        """Sets the scope of this GetPluginBindingRequest.


        :param scope: The scope of this GetPluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._scope = scope

    @property
    def target(self):
        """Gets the target of this GetPluginBindingRequest.  # noqa: E501


        :return: The target of this GetPluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this GetPluginBindingRequest.


        :param target: The target of this GetPluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._target = target

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetPluginBindingRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetPluginBindingRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetPluginBindingRequest):
            return True

        return self.to_dict() != other.to_dict()
