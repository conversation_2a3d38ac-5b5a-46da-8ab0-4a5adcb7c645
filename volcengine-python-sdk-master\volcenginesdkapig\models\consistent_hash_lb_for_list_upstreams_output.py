# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsistentHashLBForListUpstreamsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'http_cookie': 'HTTPCookieForListUpstreamsOutput',
        'hash_key': 'str',
        'http_header_name': 'str',
        'http_query_parameter_name': 'str',
        'use_source_ip': 'str'
    }

    attribute_map = {
        'http_cookie': 'HTTPCookie',
        'hash_key': 'HashKey',
        'http_header_name': 'HttpHeaderName',
        'http_query_parameter_name': 'HttpQueryParameterName',
        'use_source_ip': 'UseSourceIp'
    }

    def __init__(self, http_cookie=None, hash_key=None, http_header_name=None, http_query_parameter_name=None, use_source_ip=None, _configuration=None):  # noqa: E501
        """ConsistentHashLBForListUpstreamsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._http_cookie = None
        self._hash_key = None
        self._http_header_name = None
        self._http_query_parameter_name = None
        self._use_source_ip = None
        self.discriminator = None

        if http_cookie is not None:
            self.http_cookie = http_cookie
        if hash_key is not None:
            self.hash_key = hash_key
        if http_header_name is not None:
            self.http_header_name = http_header_name
        if http_query_parameter_name is not None:
            self.http_query_parameter_name = http_query_parameter_name
        if use_source_ip is not None:
            self.use_source_ip = use_source_ip

    @property
    def http_cookie(self):
        """Gets the http_cookie of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501


        :return: The http_cookie of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :rtype: HTTPCookieForListUpstreamsOutput
        """
        return self._http_cookie

    @http_cookie.setter
    def http_cookie(self, http_cookie):
        """Sets the http_cookie of this ConsistentHashLBForListUpstreamsOutput.


        :param http_cookie: The http_cookie of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :type: HTTPCookieForListUpstreamsOutput
        """

        self._http_cookie = http_cookie

    @property
    def hash_key(self):
        """Gets the hash_key of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501


        :return: The hash_key of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._hash_key

    @hash_key.setter
    def hash_key(self, hash_key):
        """Sets the hash_key of this ConsistentHashLBForListUpstreamsOutput.


        :param hash_key: The hash_key of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :type: str
        """

        self._hash_key = hash_key

    @property
    def http_header_name(self):
        """Gets the http_header_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501


        :return: The http_header_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_header_name

    @http_header_name.setter
    def http_header_name(self, http_header_name):
        """Sets the http_header_name of this ConsistentHashLBForListUpstreamsOutput.


        :param http_header_name: The http_header_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :type: str
        """

        self._http_header_name = http_header_name

    @property
    def http_query_parameter_name(self):
        """Gets the http_query_parameter_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501


        :return: The http_query_parameter_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_query_parameter_name

    @http_query_parameter_name.setter
    def http_query_parameter_name(self, http_query_parameter_name):
        """Sets the http_query_parameter_name of this ConsistentHashLBForListUpstreamsOutput.


        :param http_query_parameter_name: The http_query_parameter_name of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :type: str
        """

        self._http_query_parameter_name = http_query_parameter_name

    @property
    def use_source_ip(self):
        """Gets the use_source_ip of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501


        :return: The use_source_ip of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._use_source_ip

    @use_source_ip.setter
    def use_source_ip(self, use_source_ip):
        """Sets the use_source_ip of this ConsistentHashLBForListUpstreamsOutput.


        :param use_source_ip: The use_source_ip of this ConsistentHashLBForListUpstreamsOutput.  # noqa: E501
        :type: str
        """

        self._use_source_ip = use_source_ip

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsistentHashLBForListUpstreamsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsistentHashLBForListUpstreamsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsistentHashLBForListUpstreamsOutput):
            return True

        return self.to_dict() != other.to_dict()
