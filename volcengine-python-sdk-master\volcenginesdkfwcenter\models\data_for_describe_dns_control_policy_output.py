# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeDnsControlPolicyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'description': 'str',
        'destination': 'str',
        'destination_group_list': 'list[str]',
        'destination_type': 'str',
        'domain_list': 'list[str]',
        'hit_cnt': 'int',
        'last_hit_time': 'int',
        'rule_id': 'str',
        'source': 'list[SourceForDescribeDnsControlPolicyOutput]',
        'status': 'bool',
        'use_count': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'description': 'Description',
        'destination': 'Destination',
        'destination_group_list': 'DestinationGroupList',
        'destination_type': 'DestinationType',
        'domain_list': 'DomainList',
        'hit_cnt': 'HitCnt',
        'last_hit_time': 'LastHitTime',
        'rule_id': 'RuleId',
        'source': 'Source',
        'status': 'Status',
        'use_count': 'UseCount'
    }

    def __init__(self, account_id=None, description=None, destination=None, destination_group_list=None, destination_type=None, domain_list=None, hit_cnt=None, last_hit_time=None, rule_id=None, source=None, status=None, use_count=None, _configuration=None):  # noqa: E501
        """DataForDescribeDnsControlPolicyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._description = None
        self._destination = None
        self._destination_group_list = None
        self._destination_type = None
        self._domain_list = None
        self._hit_cnt = None
        self._last_hit_time = None
        self._rule_id = None
        self._source = None
        self._status = None
        self._use_count = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if description is not None:
            self.description = description
        if destination is not None:
            self.destination = destination
        if destination_group_list is not None:
            self.destination_group_list = destination_group_list
        if destination_type is not None:
            self.destination_type = destination_type
        if domain_list is not None:
            self.domain_list = domain_list
        if hit_cnt is not None:
            self.hit_cnt = hit_cnt
        if last_hit_time is not None:
            self.last_hit_time = last_hit_time
        if rule_id is not None:
            self.rule_id = rule_id
        if source is not None:
            self.source = source
        if status is not None:
            self.status = status
        if use_count is not None:
            self.use_count = use_count

    @property
    def account_id(self):
        """Gets the account_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The account_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForDescribeDnsControlPolicyOutput.


        :param account_id: The account_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def description(self):
        """Gets the description of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The description of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DataForDescribeDnsControlPolicyOutput.


        :param description: The description of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination(self):
        """Gets the destination of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The destination of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination

    @destination.setter
    def destination(self, destination):
        """Sets the destination of this DataForDescribeDnsControlPolicyOutput.


        :param destination: The destination of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: str
        """

        self._destination = destination

    @property
    def destination_group_list(self):
        """Gets the destination_group_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The destination_group_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_group_list

    @destination_group_list.setter
    def destination_group_list(self, destination_group_list):
        """Sets the destination_group_list of this DataForDescribeDnsControlPolicyOutput.


        :param destination_group_list: The destination_group_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: list[str]
        """

        self._destination_group_list = destination_group_list

    @property
    def destination_type(self):
        """Gets the destination_type of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The destination_type of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_type

    @destination_type.setter
    def destination_type(self, destination_type):
        """Sets the destination_type of this DataForDescribeDnsControlPolicyOutput.


        :param destination_type: The destination_type of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: str
        """

        self._destination_type = destination_type

    @property
    def domain_list(self):
        """Gets the domain_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The domain_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._domain_list

    @domain_list.setter
    def domain_list(self, domain_list):
        """Sets the domain_list of this DataForDescribeDnsControlPolicyOutput.


        :param domain_list: The domain_list of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: list[str]
        """

        self._domain_list = domain_list

    @property
    def hit_cnt(self):
        """Gets the hit_cnt of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The hit_cnt of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._hit_cnt

    @hit_cnt.setter
    def hit_cnt(self, hit_cnt):
        """Sets the hit_cnt of this DataForDescribeDnsControlPolicyOutput.


        :param hit_cnt: The hit_cnt of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: int
        """

        self._hit_cnt = hit_cnt

    @property
    def last_hit_time(self):
        """Gets the last_hit_time of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The last_hit_time of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_hit_time

    @last_hit_time.setter
    def last_hit_time(self, last_hit_time):
        """Sets the last_hit_time of this DataForDescribeDnsControlPolicyOutput.


        :param last_hit_time: The last_hit_time of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: int
        """

        self._last_hit_time = last_hit_time

    @property
    def rule_id(self):
        """Gets the rule_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The rule_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DataForDescribeDnsControlPolicyOutput.


        :param rule_id: The rule_id of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def source(self):
        """Gets the source of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The source of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: list[SourceForDescribeDnsControlPolicyOutput]
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this DataForDescribeDnsControlPolicyOutput.


        :param source: The source of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: list[SourceForDescribeDnsControlPolicyOutput]
        """

        self._source = source

    @property
    def status(self):
        """Gets the status of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The status of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: bool
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForDescribeDnsControlPolicyOutput.


        :param status: The status of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: bool
        """

        self._status = status

    @property
    def use_count(self):
        """Gets the use_count of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501


        :return: The use_count of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :rtype: int
        """
        return self._use_count

    @use_count.setter
    def use_count(self, use_count):
        """Sets the use_count of this DataForDescribeDnsControlPolicyOutput.


        :param use_count: The use_count of this DataForDescribeDnsControlPolicyOutput.  # noqa: E501
        :type: int
        """

        self._use_count = use_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeDnsControlPolicyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeDnsControlPolicyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeDnsControlPolicyOutput):
            return True

        return self.to_dict() != other.to_dict()
