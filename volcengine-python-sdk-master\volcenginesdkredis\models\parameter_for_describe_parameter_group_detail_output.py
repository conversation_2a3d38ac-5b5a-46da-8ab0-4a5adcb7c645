# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ParameterForDescribeParameterGroupDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'current_value': 'str',
        'description': 'str',
        'need_reboot': 'bool',
        'options': 'list[OptionForDescribeParameterGroupDetailOutput]',
        'param_name': 'str',
        'range': 'str',
        'type': 'str',
        'unit': 'str'
    }

    attribute_map = {
        'current_value': 'CurrentValue',
        'description': 'Description',
        'need_reboot': 'NeedReboot',
        'options': 'Options',
        'param_name': 'ParamName',
        'range': 'Range',
        'type': 'Type',
        'unit': 'Unit'
    }

    def __init__(self, current_value=None, description=None, need_reboot=None, options=None, param_name=None, range=None, type=None, unit=None, _configuration=None):  # noqa: E501
        """ParameterForDescribeParameterGroupDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._current_value = None
        self._description = None
        self._need_reboot = None
        self._options = None
        self._param_name = None
        self._range = None
        self._type = None
        self._unit = None
        self.discriminator = None

        if current_value is not None:
            self.current_value = current_value
        if description is not None:
            self.description = description
        if need_reboot is not None:
            self.need_reboot = need_reboot
        if options is not None:
            self.options = options
        if param_name is not None:
            self.param_name = param_name
        if range is not None:
            self.range = range
        if type is not None:
            self.type = type
        if unit is not None:
            self.unit = unit

    @property
    def current_value(self):
        """Gets the current_value of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The current_value of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._current_value

    @current_value.setter
    def current_value(self, current_value):
        """Sets the current_value of this ParameterForDescribeParameterGroupDetailOutput.


        :param current_value: The current_value of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._current_value = current_value

    @property
    def description(self):
        """Gets the description of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The description of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ParameterForDescribeParameterGroupDetailOutput.


        :param description: The description of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def need_reboot(self):
        """Gets the need_reboot of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The need_reboot of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._need_reboot

    @need_reboot.setter
    def need_reboot(self, need_reboot):
        """Sets the need_reboot of this ParameterForDescribeParameterGroupDetailOutput.


        :param need_reboot: The need_reboot of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: bool
        """

        self._need_reboot = need_reboot

    @property
    def options(self):
        """Gets the options of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The options of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: list[OptionForDescribeParameterGroupDetailOutput]
        """
        return self._options

    @options.setter
    def options(self, options):
        """Sets the options of this ParameterForDescribeParameterGroupDetailOutput.


        :param options: The options of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: list[OptionForDescribeParameterGroupDetailOutput]
        """

        self._options = options

    @property
    def param_name(self):
        """Gets the param_name of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The param_name of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._param_name

    @param_name.setter
    def param_name(self, param_name):
        """Sets the param_name of this ParameterForDescribeParameterGroupDetailOutput.


        :param param_name: The param_name of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._param_name = param_name

    @property
    def range(self):
        """Gets the range of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The range of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._range

    @range.setter
    def range(self, range):
        """Sets the range of this ParameterForDescribeParameterGroupDetailOutput.


        :param range: The range of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._range = range

    @property
    def type(self):
        """Gets the type of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The type of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ParameterForDescribeParameterGroupDetailOutput.


        :param type: The type of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def unit(self):
        """Gets the unit of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501


        :return: The unit of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._unit

    @unit.setter
    def unit(self, unit):
        """Sets the unit of this ParameterForDescribeParameterGroupDetailOutput.


        :param unit: The unit of this ParameterForDescribeParameterGroupDetailOutput.  # noqa: E501
        :type: str
        """

        self._unit = unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ParameterForDescribeParameterGroupDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ParameterForDescribeParameterGroupDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ParameterForDescribeParameterGroupDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
