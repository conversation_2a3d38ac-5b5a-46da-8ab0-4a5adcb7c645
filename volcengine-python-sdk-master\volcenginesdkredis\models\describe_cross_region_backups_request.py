# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCrossRegionBackupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_point_id': 'str',
        'backup_point_name': 'str',
        'backup_strategy_list': 'list[str]',
        'end_time': 'str',
        'instance_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'scope': 'str',
        'start_time': 'str',
        'status': 'str'
    }

    attribute_map = {
        'backup_point_id': 'BackupPointId',
        'backup_point_name': 'BackupPointName',
        'backup_strategy_list': 'BackupStrategyList',
        'end_time': 'EndTime',
        'instance_id': 'InstanceId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'scope': 'Scope',
        'start_time': 'StartTime',
        'status': 'Status'
    }

    def __init__(self, backup_point_id=None, backup_point_name=None, backup_strategy_list=None, end_time=None, instance_id=None, page_number=None, page_size=None, project_name=None, scope=None, start_time=None, status=None, _configuration=None):  # noqa: E501
        """DescribeCrossRegionBackupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_point_id = None
        self._backup_point_name = None
        self._backup_strategy_list = None
        self._end_time = None
        self._instance_id = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._scope = None
        self._start_time = None
        self._status = None
        self.discriminator = None

        if backup_point_id is not None:
            self.backup_point_id = backup_point_id
        if backup_point_name is not None:
            self.backup_point_name = backup_point_name
        if backup_strategy_list is not None:
            self.backup_strategy_list = backup_strategy_list
        if end_time is not None:
            self.end_time = end_time
        if instance_id is not None:
            self.instance_id = instance_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if scope is not None:
            self.scope = scope
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status

    @property
    def backup_point_id(self):
        """Gets the backup_point_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The backup_point_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_id

    @backup_point_id.setter
    def backup_point_id(self, backup_point_id):
        """Sets the backup_point_id of this DescribeCrossRegionBackupsRequest.


        :param backup_point_id: The backup_point_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_id = backup_point_id

    @property
    def backup_point_name(self):
        """Gets the backup_point_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The backup_point_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_name

    @backup_point_name.setter
    def backup_point_name(self, backup_point_name):
        """Sets the backup_point_name of this DescribeCrossRegionBackupsRequest.


        :param backup_point_name: The backup_point_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_name = backup_point_name

    @property
    def backup_strategy_list(self):
        """Gets the backup_strategy_list of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The backup_strategy_list of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._backup_strategy_list

    @backup_strategy_list.setter
    def backup_strategy_list(self, backup_strategy_list):
        """Sets the backup_strategy_list of this DescribeCrossRegionBackupsRequest.


        :param backup_strategy_list: The backup_strategy_list of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["ManualBackup", "AutomatedBackup", "DataFlashBack", "AllStrategy"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(backup_strategy_list).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `backup_strategy_list` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(backup_strategy_list) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._backup_strategy_list = backup_strategy_list

    @property
    def end_time(self):
        """Gets the end_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The end_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeCrossRegionBackupsRequest.


        :param end_time: The end_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The instance_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeCrossRegionBackupsRequest.


        :param instance_id: The instance_id of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The page_number of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeCrossRegionBackupsRequest.


        :param page_number: The page_number of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The page_size of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeCrossRegionBackupsRequest.


        :param page_size: The page_size of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The project_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeCrossRegionBackupsRequest.


        :param project_name: The project_name of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def scope(self):
        """Gets the scope of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The scope of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._scope

    @scope.setter
    def scope(self, scope):
        """Sets the scope of this DescribeCrossRegionBackupsRequest.


        :param scope: The scope of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["OneInstance", "DestroyedInstances", "AccountInstances"]  # noqa: E501
        if (self._configuration.client_side_validation and
                scope not in allowed_values):
            raise ValueError(
                "Invalid value for `scope` ({0}), must be one of {1}"  # noqa: E501
                .format(scope, allowed_values)
            )

        self._scope = scope

    @property
    def start_time(self):
        """Gets the start_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The start_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeCrossRegionBackupsRequest.


        :param start_time: The start_time of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DescribeCrossRegionBackupsRequest.  # noqa: E501


        :return: The status of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeCrossRegionBackupsRequest.


        :param status: The status of this DescribeCrossRegionBackupsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Creating", "Available", "Unavailable", "Deleting"]  # noqa: E501
        if (self._configuration.client_side_validation and
                status not in allowed_values):
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCrossRegionBackupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCrossRegionBackupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCrossRegionBackupsRequest):
            return True

        return self.to_dict() != other.to_dict()
