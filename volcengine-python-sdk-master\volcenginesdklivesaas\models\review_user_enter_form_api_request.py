# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReviewUserEnterFormAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'review_status': 'int',
        'user_form_id_list': 'list[str]'
    }

    attribute_map = {
        'activity_id': 'ActivityID',
        'review_status': 'ReviewStatus',
        'user_form_id_list': 'UserFormIDList'
    }

    def __init__(self, activity_id=None, review_status=None, user_form_id_list=None, _configuration=None):  # noqa: E501
        """ReviewUserEnterFormAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._review_status = None
        self._user_form_id_list = None
        self.discriminator = None

        self.activity_id = activity_id
        self.review_status = review_status
        if user_form_id_list is not None:
            self.user_form_id_list = user_form_id_list

    @property
    def activity_id(self):
        """Gets the activity_id of this ReviewUserEnterFormAPIRequest.  # noqa: E501


        :return: The activity_id of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ReviewUserEnterFormAPIRequest.


        :param activity_id: The activity_id of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def review_status(self):
        """Gets the review_status of this ReviewUserEnterFormAPIRequest.  # noqa: E501


        :return: The review_status of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._review_status

    @review_status.setter
    def review_status(self, review_status):
        """Sets the review_status of this ReviewUserEnterFormAPIRequest.


        :param review_status: The review_status of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and review_status is None:
            raise ValueError("Invalid value for `review_status`, must not be `None`")  # noqa: E501

        self._review_status = review_status

    @property
    def user_form_id_list(self):
        """Gets the user_form_id_list of this ReviewUserEnterFormAPIRequest.  # noqa: E501


        :return: The user_form_id_list of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_form_id_list

    @user_form_id_list.setter
    def user_form_id_list(self, user_form_id_list):
        """Sets the user_form_id_list of this ReviewUserEnterFormAPIRequest.


        :param user_form_id_list: The user_form_id_list of this ReviewUserEnterFormAPIRequest.  # noqa: E501
        :type: list[str]
        """

        self._user_form_id_list = user_form_id_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReviewUserEnterFormAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReviewUserEnterFormAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReviewUserEnterFormAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
