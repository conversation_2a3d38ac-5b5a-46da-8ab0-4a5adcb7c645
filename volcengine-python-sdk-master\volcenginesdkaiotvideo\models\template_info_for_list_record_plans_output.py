# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TemplateInfoForListRecordPlansOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'format': 'str',
        'ttl': 'TTLForListRecordPlansOutput'
    }

    attribute_map = {
        'format': 'Format',
        'ttl': 'TTL'
    }

    def __init__(self, format=None, ttl=None, _configuration=None):  # noqa: E501
        """TemplateInfoForListRecordPlansOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._format = None
        self._ttl = None
        self.discriminator = None

        if format is not None:
            self.format = format
        if ttl is not None:
            self.ttl = ttl

    @property
    def format(self):
        """Gets the format of this TemplateInfoForListRecordPlansOutput.  # noqa: E501


        :return: The format of this TemplateInfoForListRecordPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._format

    @format.setter
    def format(self, format):
        """Sets the format of this TemplateInfoForListRecordPlansOutput.


        :param format: The format of this TemplateInfoForListRecordPlansOutput.  # noqa: E501
        :type: str
        """

        self._format = format

    @property
    def ttl(self):
        """Gets the ttl of this TemplateInfoForListRecordPlansOutput.  # noqa: E501


        :return: The ttl of this TemplateInfoForListRecordPlansOutput.  # noqa: E501
        :rtype: TTLForListRecordPlansOutput
        """
        return self._ttl

    @ttl.setter
    def ttl(self, ttl):
        """Sets the ttl of this TemplateInfoForListRecordPlansOutput.


        :param ttl: The ttl of this TemplateInfoForListRecordPlansOutput.  # noqa: E501
        :type: TTLForListRecordPlansOutput
        """

        self._ttl = ttl

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TemplateInfoForListRecordPlansOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TemplateInfoForListRecordPlansOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TemplateInfoForListRecordPlansOutput):
            return True

        return self.to_dict() != other.to_dict()
