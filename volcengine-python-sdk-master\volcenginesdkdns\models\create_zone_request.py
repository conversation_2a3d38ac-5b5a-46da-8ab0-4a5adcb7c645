# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateZoneRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'project_name': 'str',
        'remark': 'str',
        'tags': 'list[TagForCreateZoneInput]',
        'zone_name': 'str'
    }

    attribute_map = {
        'project_name': 'ProjectName',
        'remark': 'Remark',
        'tags': 'Tags',
        'zone_name': 'ZoneName'
    }

    def __init__(self, project_name=None, remark=None, tags=None, zone_name=None, _configuration=None):  # noqa: E501
        """CreateZoneRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._project_name = None
        self._remark = None
        self._tags = None
        self._zone_name = None
        self.discriminator = None

        if project_name is not None:
            self.project_name = project_name
        if remark is not None:
            self.remark = remark
        if tags is not None:
            self.tags = tags
        self.zone_name = zone_name

    @property
    def project_name(self):
        """Gets the project_name of this CreateZoneRequest.  # noqa: E501


        :return: The project_name of this CreateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateZoneRequest.


        :param project_name: The project_name of this CreateZoneRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def remark(self):
        """Gets the remark of this CreateZoneRequest.  # noqa: E501


        :return: The remark of this CreateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this CreateZoneRequest.


        :param remark: The remark of this CreateZoneRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def tags(self):
        """Gets the tags of this CreateZoneRequest.  # noqa: E501


        :return: The tags of this CreateZoneRequest.  # noqa: E501
        :rtype: list[TagForCreateZoneInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateZoneRequest.


        :param tags: The tags of this CreateZoneRequest.  # noqa: E501
        :type: list[TagForCreateZoneInput]
        """

        self._tags = tags

    @property
    def zone_name(self):
        """Gets the zone_name of this CreateZoneRequest.  # noqa: E501


        :return: The zone_name of this CreateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this CreateZoneRequest.


        :param zone_name: The zone_name of this CreateZoneRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_name is None:
            raise ValueError("Invalid value for `zone_name`, must not be `None`")  # noqa: E501

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateZoneRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateZoneRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateZoneRequest):
            return True

        return self.to_dict() != other.to_dict()
