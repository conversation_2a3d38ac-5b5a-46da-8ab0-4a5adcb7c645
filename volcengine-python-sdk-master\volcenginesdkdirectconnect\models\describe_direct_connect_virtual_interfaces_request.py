# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDirectConnectVirtualInterfacesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direct_connect_connection_id': 'str',
        'direct_connect_gateway_id': 'str',
        'local_ip': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'peer_ip': 'str',
        'route_type': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForDescribeDirectConnectVirtualInterfacesInput]',
        'virtual_interface_ids': 'list[str]',
        'virtual_interface_name': 'str',
        'vlan_id': 'int'
    }

    attribute_map = {
        'direct_connect_connection_id': 'DirectConnectConnectionId',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'local_ip': 'LocalIp',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'peer_ip': 'PeerIp',
        'route_type': 'RouteType',
        'status': 'Status',
        'tag_filters': 'TagFilters',
        'virtual_interface_ids': 'VirtualInterfaceIds',
        'virtual_interface_name': 'VirtualInterfaceName',
        'vlan_id': 'VlanId'
    }

    def __init__(self, direct_connect_connection_id=None, direct_connect_gateway_id=None, local_ip=None, page_number=None, page_size=None, peer_ip=None, route_type=None, status=None, tag_filters=None, virtual_interface_ids=None, virtual_interface_name=None, vlan_id=None, _configuration=None):  # noqa: E501
        """DescribeDirectConnectVirtualInterfacesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direct_connect_connection_id = None
        self._direct_connect_gateway_id = None
        self._local_ip = None
        self._page_number = None
        self._page_size = None
        self._peer_ip = None
        self._route_type = None
        self._status = None
        self._tag_filters = None
        self._virtual_interface_ids = None
        self._virtual_interface_name = None
        self._vlan_id = None
        self.discriminator = None

        if direct_connect_connection_id is not None:
            self.direct_connect_connection_id = direct_connect_connection_id
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if local_ip is not None:
            self.local_ip = local_ip
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if peer_ip is not None:
            self.peer_ip = peer_ip
        if route_type is not None:
            self.route_type = route_type
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if virtual_interface_ids is not None:
            self.virtual_interface_ids = virtual_interface_ids
        if virtual_interface_name is not None:
            self.virtual_interface_name = virtual_interface_name
        if vlan_id is not None:
            self.vlan_id = vlan_id

    @property
    def direct_connect_connection_id(self):
        """Gets the direct_connect_connection_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The direct_connect_connection_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_id

    @direct_connect_connection_id.setter
    def direct_connect_connection_id(self, direct_connect_connection_id):
        """Sets the direct_connect_connection_id of this DescribeDirectConnectVirtualInterfacesRequest.


        :param direct_connect_connection_id: The direct_connect_connection_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_connection_id = direct_connect_connection_id

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The direct_connect_gateway_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this DescribeDirectConnectVirtualInterfacesRequest.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def local_ip(self):
        """Gets the local_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The local_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._local_ip

    @local_ip.setter
    def local_ip(self, local_ip):
        """Sets the local_ip of this DescribeDirectConnectVirtualInterfacesRequest.


        :param local_ip: The local_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._local_ip = local_ip

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The page_number of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDirectConnectVirtualInterfacesRequest.


        :param page_number: The page_number of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The page_size of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDirectConnectVirtualInterfacesRequest.


        :param page_size: The page_size of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def peer_ip(self):
        """Gets the peer_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The peer_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_ip

    @peer_ip.setter
    def peer_ip(self, peer_ip):
        """Sets the peer_ip of this DescribeDirectConnectVirtualInterfacesRequest.


        :param peer_ip: The peer_ip of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._peer_ip = peer_ip

    @property
    def route_type(self):
        """Gets the route_type of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The route_type of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._route_type

    @route_type.setter
    def route_type(self, route_type):
        """Sets the route_type of this DescribeDirectConnectVirtualInterfacesRequest.


        :param route_type: The route_type of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._route_type = route_type

    @property
    def status(self):
        """Gets the status of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The status of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeDirectConnectVirtualInterfacesRequest.


        :param status: The status of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The tag_filters of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeDirectConnectVirtualInterfacesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeDirectConnectVirtualInterfacesRequest.


        :param tag_filters: The tag_filters of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: list[TagFilterForDescribeDirectConnectVirtualInterfacesInput]
        """

        self._tag_filters = tag_filters

    @property
    def virtual_interface_ids(self):
        """Gets the virtual_interface_ids of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The virtual_interface_ids of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._virtual_interface_ids

    @virtual_interface_ids.setter
    def virtual_interface_ids(self, virtual_interface_ids):
        """Sets the virtual_interface_ids of this DescribeDirectConnectVirtualInterfacesRequest.


        :param virtual_interface_ids: The virtual_interface_ids of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: list[str]
        """

        self._virtual_interface_ids = virtual_interface_ids

    @property
    def virtual_interface_name(self):
        """Gets the virtual_interface_name of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The virtual_interface_name of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: str
        """
        return self._virtual_interface_name

    @virtual_interface_name.setter
    def virtual_interface_name(self, virtual_interface_name):
        """Sets the virtual_interface_name of this DescribeDirectConnectVirtualInterfacesRequest.


        :param virtual_interface_name: The virtual_interface_name of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: str
        """

        self._virtual_interface_name = virtual_interface_name

    @property
    def vlan_id(self):
        """Gets the vlan_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501


        :return: The vlan_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :rtype: int
        """
        return self._vlan_id

    @vlan_id.setter
    def vlan_id(self, vlan_id):
        """Sets the vlan_id of this DescribeDirectConnectVirtualInterfacesRequest.


        :param vlan_id: The vlan_id of this DescribeDirectConnectVirtualInterfacesRequest.  # noqa: E501
        :type: int
        """

        self._vlan_id = vlan_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDirectConnectVirtualInterfacesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDirectConnectVirtualInterfacesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDirectConnectVirtualInterfacesRequest):
            return True

        return self.to_dict() != other.to_dict()
