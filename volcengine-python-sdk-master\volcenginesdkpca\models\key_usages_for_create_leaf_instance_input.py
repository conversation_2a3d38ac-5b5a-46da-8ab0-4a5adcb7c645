# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class KeyUsagesForCreateLeafInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'crl_sign': 'bool',
        'data_encipherment': 'bool',
        'decipher_only': 'bool',
        'digital_signature': 'bool',
        'encipher_only': 'bool',
        'key_agreement': 'bool',
        'key_cert_sign': 'bool',
        'key_encipherment': 'bool',
        'non_repudiation': 'bool'
    }

    attribute_map = {
        'crl_sign': 'crl_sign',
        'data_encipherment': 'data_encipherment',
        'decipher_only': 'decipher_only',
        'digital_signature': 'digital_signature',
        'encipher_only': 'encipher_only',
        'key_agreement': 'key_agreement',
        'key_cert_sign': 'key_cert_sign',
        'key_encipherment': 'key_encipherment',
        'non_repudiation': 'non_repudiation'
    }

    def __init__(self, crl_sign=None, data_encipherment=None, decipher_only=None, digital_signature=None, encipher_only=None, key_agreement=None, key_cert_sign=None, key_encipherment=None, non_repudiation=None, _configuration=None):  # noqa: E501
        """KeyUsagesForCreateLeafInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._crl_sign = None
        self._data_encipherment = None
        self._decipher_only = None
        self._digital_signature = None
        self._encipher_only = None
        self._key_agreement = None
        self._key_cert_sign = None
        self._key_encipherment = None
        self._non_repudiation = None
        self.discriminator = None

        if crl_sign is not None:
            self.crl_sign = crl_sign
        if data_encipherment is not None:
            self.data_encipherment = data_encipherment
        if decipher_only is not None:
            self.decipher_only = decipher_only
        if digital_signature is not None:
            self.digital_signature = digital_signature
        if encipher_only is not None:
            self.encipher_only = encipher_only
        if key_agreement is not None:
            self.key_agreement = key_agreement
        if key_cert_sign is not None:
            self.key_cert_sign = key_cert_sign
        if key_encipherment is not None:
            self.key_encipherment = key_encipherment
        if non_repudiation is not None:
            self.non_repudiation = non_repudiation

    @property
    def crl_sign(self):
        """Gets the crl_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The crl_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._crl_sign

    @crl_sign.setter
    def crl_sign(self, crl_sign):
        """Sets the crl_sign of this KeyUsagesForCreateLeafInstanceInput.


        :param crl_sign: The crl_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._crl_sign = crl_sign

    @property
    def data_encipherment(self):
        """Gets the data_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The data_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._data_encipherment

    @data_encipherment.setter
    def data_encipherment(self, data_encipherment):
        """Sets the data_encipherment of this KeyUsagesForCreateLeafInstanceInput.


        :param data_encipherment: The data_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._data_encipherment = data_encipherment

    @property
    def decipher_only(self):
        """Gets the decipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The decipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._decipher_only

    @decipher_only.setter
    def decipher_only(self, decipher_only):
        """Sets the decipher_only of this KeyUsagesForCreateLeafInstanceInput.


        :param decipher_only: The decipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._decipher_only = decipher_only

    @property
    def digital_signature(self):
        """Gets the digital_signature of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The digital_signature of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._digital_signature

    @digital_signature.setter
    def digital_signature(self, digital_signature):
        """Sets the digital_signature of this KeyUsagesForCreateLeafInstanceInput.


        :param digital_signature: The digital_signature of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._digital_signature = digital_signature

    @property
    def encipher_only(self):
        """Gets the encipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The encipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._encipher_only

    @encipher_only.setter
    def encipher_only(self, encipher_only):
        """Sets the encipher_only of this KeyUsagesForCreateLeafInstanceInput.


        :param encipher_only: The encipher_only of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._encipher_only = encipher_only

    @property
    def key_agreement(self):
        """Gets the key_agreement of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The key_agreement of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._key_agreement

    @key_agreement.setter
    def key_agreement(self, key_agreement):
        """Sets the key_agreement of this KeyUsagesForCreateLeafInstanceInput.


        :param key_agreement: The key_agreement of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._key_agreement = key_agreement

    @property
    def key_cert_sign(self):
        """Gets the key_cert_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The key_cert_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._key_cert_sign

    @key_cert_sign.setter
    def key_cert_sign(self, key_cert_sign):
        """Sets the key_cert_sign of this KeyUsagesForCreateLeafInstanceInput.


        :param key_cert_sign: The key_cert_sign of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._key_cert_sign = key_cert_sign

    @property
    def key_encipherment(self):
        """Gets the key_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The key_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._key_encipherment

    @key_encipherment.setter
    def key_encipherment(self, key_encipherment):
        """Sets the key_encipherment of this KeyUsagesForCreateLeafInstanceInput.


        :param key_encipherment: The key_encipherment of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._key_encipherment = key_encipherment

    @property
    def non_repudiation(self):
        """Gets the non_repudiation of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501


        :return: The non_repudiation of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._non_repudiation

    @non_repudiation.setter
    def non_repudiation(self, non_repudiation):
        """Sets the non_repudiation of this KeyUsagesForCreateLeafInstanceInput.


        :param non_repudiation: The non_repudiation of this KeyUsagesForCreateLeafInstanceInput.  # noqa: E501
        :type: bool
        """

        self._non_repudiation = non_repudiation

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(KeyUsagesForCreateLeafInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, KeyUsagesForCreateLeafInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, KeyUsagesForCreateLeafInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
