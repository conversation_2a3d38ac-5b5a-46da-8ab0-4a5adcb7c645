# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteIMConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'im_config_uid': 'str'
    }

    attribute_map = {
        'im_config_uid': 'IMConfigUID'
    }

    def __init__(self, im_config_uid=None, _configuration=None):  # noqa: E501
        """DeleteIMConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._im_config_uid = None
        self.discriminator = None

        self.im_config_uid = im_config_uid

    @property
    def im_config_uid(self):
        """Gets the im_config_uid of this DeleteIMConfigRequest.  # noqa: E501


        :return: The im_config_uid of this DeleteIMConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._im_config_uid

    @im_config_uid.setter
    def im_config_uid(self, im_config_uid):
        """Sets the im_config_uid of this DeleteIMConfigRequest.


        :param im_config_uid: The im_config_uid of this DeleteIMConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and im_config_uid is None:
            raise ValueError("Invalid value for `im_config_uid`, must not be `None`")  # noqa: E501

        self._im_config_uid = im_config_uid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteIMConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteIMConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteIMConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
