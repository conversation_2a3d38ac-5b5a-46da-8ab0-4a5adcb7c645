# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateHostTagRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'before_tag': 'str',
        'conditions': 'ConditionsForCreateHostTagInput',
        'id_list': 'list[str]',
        'tags': 'list[str]'
    }

    attribute_map = {
        'before_tag': 'BeforeTag',
        'conditions': 'Conditions',
        'id_list': 'IdList',
        'tags': 'Tags'
    }

    def __init__(self, before_tag=None, conditions=None, id_list=None, tags=None, _configuration=None):  # noqa: E501
        """CreateHostTagRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._before_tag = None
        self._conditions = None
        self._id_list = None
        self._tags = None
        self.discriminator = None

        if before_tag is not None:
            self.before_tag = before_tag
        if conditions is not None:
            self.conditions = conditions
        if id_list is not None:
            self.id_list = id_list
        if tags is not None:
            self.tags = tags

    @property
    def before_tag(self):
        """Gets the before_tag of this CreateHostTagRequest.  # noqa: E501


        :return: The before_tag of this CreateHostTagRequest.  # noqa: E501
        :rtype: str
        """
        return self._before_tag

    @before_tag.setter
    def before_tag(self, before_tag):
        """Sets the before_tag of this CreateHostTagRequest.


        :param before_tag: The before_tag of this CreateHostTagRequest.  # noqa: E501
        :type: str
        """

        self._before_tag = before_tag

    @property
    def conditions(self):
        """Gets the conditions of this CreateHostTagRequest.  # noqa: E501


        :return: The conditions of this CreateHostTagRequest.  # noqa: E501
        :rtype: ConditionsForCreateHostTagInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this CreateHostTagRequest.


        :param conditions: The conditions of this CreateHostTagRequest.  # noqa: E501
        :type: ConditionsForCreateHostTagInput
        """

        self._conditions = conditions

    @property
    def id_list(self):
        """Gets the id_list of this CreateHostTagRequest.  # noqa: E501


        :return: The id_list of this CreateHostTagRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._id_list

    @id_list.setter
    def id_list(self, id_list):
        """Sets the id_list of this CreateHostTagRequest.


        :param id_list: The id_list of this CreateHostTagRequest.  # noqa: E501
        :type: list[str]
        """

        self._id_list = id_list

    @property
    def tags(self):
        """Gets the tags of this CreateHostTagRequest.  # noqa: E501


        :return: The tags of this CreateHostTagRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateHostTagRequest.


        :param tags: The tags of this CreateHostTagRequest.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateHostTagRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateHostTagRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateHostTagRequest):
            return True

        return self.to_dict() != other.to_dict()
