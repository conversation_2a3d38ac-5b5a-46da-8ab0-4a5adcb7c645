# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBListenerCertificatesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_ids': 'list[str]',
        'certificate_type': 'str',
        'listener_id': 'str',
        'max_results': 'int',
        'next_token': 'str'
    }

    attribute_map = {
        'certificate_ids': 'CertificateIds',
        'certificate_type': 'CertificateType',
        'listener_id': 'ListenerId',
        'max_results': 'MaxResults',
        'next_token': 'NextToken'
    }

    def __init__(self, certificate_ids=None, certificate_type=None, listener_id=None, max_results=None, next_token=None, _configuration=None):  # noqa: E501
        """DescribeNLBListenerCertificatesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_ids = None
        self._certificate_type = None
        self._listener_id = None
        self._max_results = None
        self._next_token = None
        self.discriminator = None

        if certificate_ids is not None:
            self.certificate_ids = certificate_ids
        if certificate_type is not None:
            self.certificate_type = certificate_type
        self.listener_id = listener_id
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token

    @property
    def certificate_ids(self):
        """Gets the certificate_ids of this DescribeNLBListenerCertificatesRequest.  # noqa: E501


        :return: The certificate_ids of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._certificate_ids

    @certificate_ids.setter
    def certificate_ids(self, certificate_ids):
        """Sets the certificate_ids of this DescribeNLBListenerCertificatesRequest.


        :param certificate_ids: The certificate_ids of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :type: list[str]
        """

        self._certificate_ids = certificate_ids

    @property
    def certificate_type(self):
        """Gets the certificate_type of this DescribeNLBListenerCertificatesRequest.  # noqa: E501


        :return: The certificate_type of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_type

    @certificate_type.setter
    def certificate_type(self, certificate_type):
        """Sets the certificate_type of this DescribeNLBListenerCertificatesRequest.


        :param certificate_type: The certificate_type of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :type: str
        """

        self._certificate_type = certificate_type

    @property
    def listener_id(self):
        """Gets the listener_id of this DescribeNLBListenerCertificatesRequest.  # noqa: E501


        :return: The listener_id of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this DescribeNLBListenerCertificatesRequest.


        :param listener_id: The listener_id of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and listener_id is None:
            raise ValueError("Invalid value for `listener_id`, must not be `None`")  # noqa: E501

        self._listener_id = listener_id

    @property
    def max_results(self):
        """Gets the max_results of this DescribeNLBListenerCertificatesRequest.  # noqa: E501


        :return: The max_results of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeNLBListenerCertificatesRequest.


        :param max_results: The max_results of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeNLBListenerCertificatesRequest.  # noqa: E501


        :return: The next_token of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeNLBListenerCertificatesRequest.


        :param next_token: The next_token of this DescribeNLBListenerCertificatesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBListenerCertificatesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBListenerCertificatesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBListenerCertificatesRequest):
            return True

        return self.to_dict() != other.to_dict()
