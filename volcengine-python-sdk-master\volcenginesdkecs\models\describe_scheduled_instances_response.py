# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeScheduledInstancesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'next_token': 'str',
        'scheduled_instance_info': 'list[ScheduledInstanceInfoForDescribeScheduledInstancesOutput]'
    }

    attribute_map = {
        'next_token': 'NextToken',
        'scheduled_instance_info': 'ScheduledInstanceInfo'
    }

    def __init__(self, next_token=None, scheduled_instance_info=None, _configuration=None):  # noqa: E501
        """DescribeScheduledInstancesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._next_token = None
        self._scheduled_instance_info = None
        self.discriminator = None

        if next_token is not None:
            self.next_token = next_token
        if scheduled_instance_info is not None:
            self.scheduled_instance_info = scheduled_instance_info

    @property
    def next_token(self):
        """Gets the next_token of this DescribeScheduledInstancesResponse.  # noqa: E501


        :return: The next_token of this DescribeScheduledInstancesResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeScheduledInstancesResponse.


        :param next_token: The next_token of this DescribeScheduledInstancesResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def scheduled_instance_info(self):
        """Gets the scheduled_instance_info of this DescribeScheduledInstancesResponse.  # noqa: E501


        :return: The scheduled_instance_info of this DescribeScheduledInstancesResponse.  # noqa: E501
        :rtype: list[ScheduledInstanceInfoForDescribeScheduledInstancesOutput]
        """
        return self._scheduled_instance_info

    @scheduled_instance_info.setter
    def scheduled_instance_info(self, scheduled_instance_info):
        """Sets the scheduled_instance_info of this DescribeScheduledInstancesResponse.


        :param scheduled_instance_info: The scheduled_instance_info of this DescribeScheduledInstancesResponse.  # noqa: E501
        :type: list[ScheduledInstanceInfoForDescribeScheduledInstancesOutput]
        """

        self._scheduled_instance_info = scheduled_instance_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeScheduledInstancesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeScheduledInstancesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeScheduledInstancesResponse):
            return True

        return self.to_dict() != other.to_dict()
