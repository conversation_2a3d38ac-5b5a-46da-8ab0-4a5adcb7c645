# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MessageForBatchAddFwdRuleOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'err_msgs': 'list[str]',
        'fwd_port': 'int',
        'is_succeed': 'int',
        'proto': 'str'
    }

    attribute_map = {
        'err_msgs': 'ErrMsgs',
        'fwd_port': 'FwdPort',
        'is_succeed': 'IsSucceed',
        'proto': 'Proto'
    }

    def __init__(self, err_msgs=None, fwd_port=None, is_succeed=None, proto=None, _configuration=None):  # noqa: E501
        """MessageForBatchAddFwdRuleOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._err_msgs = None
        self._fwd_port = None
        self._is_succeed = None
        self._proto = None
        self.discriminator = None

        if err_msgs is not None:
            self.err_msgs = err_msgs
        if fwd_port is not None:
            self.fwd_port = fwd_port
        if is_succeed is not None:
            self.is_succeed = is_succeed
        if proto is not None:
            self.proto = proto

    @property
    def err_msgs(self):
        """Gets the err_msgs of this MessageForBatchAddFwdRuleOutput.  # noqa: E501


        :return: The err_msgs of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._err_msgs

    @err_msgs.setter
    def err_msgs(self, err_msgs):
        """Sets the err_msgs of this MessageForBatchAddFwdRuleOutput.


        :param err_msgs: The err_msgs of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :type: list[str]
        """

        self._err_msgs = err_msgs

    @property
    def fwd_port(self):
        """Gets the fwd_port of this MessageForBatchAddFwdRuleOutput.  # noqa: E501


        :return: The fwd_port of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :rtype: int
        """
        return self._fwd_port

    @fwd_port.setter
    def fwd_port(self, fwd_port):
        """Sets the fwd_port of this MessageForBatchAddFwdRuleOutput.


        :param fwd_port: The fwd_port of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :type: int
        """

        self._fwd_port = fwd_port

    @property
    def is_succeed(self):
        """Gets the is_succeed of this MessageForBatchAddFwdRuleOutput.  # noqa: E501


        :return: The is_succeed of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_succeed

    @is_succeed.setter
    def is_succeed(self, is_succeed):
        """Sets the is_succeed of this MessageForBatchAddFwdRuleOutput.


        :param is_succeed: The is_succeed of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :type: int
        """

        self._is_succeed = is_succeed

    @property
    def proto(self):
        """Gets the proto of this MessageForBatchAddFwdRuleOutput.  # noqa: E501


        :return: The proto of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :rtype: str
        """
        return self._proto

    @proto.setter
    def proto(self, proto):
        """Sets the proto of this MessageForBatchAddFwdRuleOutput.


        :param proto: The proto of this MessageForBatchAddFwdRuleOutput.  # noqa: E501
        :type: str
        """

        self._proto = proto

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MessageForBatchAddFwdRuleOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MessageForBatchAddFwdRuleOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MessageForBatchAddFwdRuleOutput):
            return True

        return self.to_dict() != other.to_dict()
