# coding: utf-8

"""
    resourcecenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceForSearchResourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'create_time': 'str',
        'private_ip_address': 'list[str]',
        'project_name': 'str',
        'public_ip_address': 'list[str]',
        'region': 'str',
        'resource_id': 'str',
        'resource_name': 'str',
        'resource_type': 'str',
        'service': 'str',
        'tags': 'list[TagForSearchResourcesOutput]'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'create_time': 'CreateTime',
        'private_ip_address': 'PrivateIpAddress',
        'project_name': 'ProjectName',
        'public_ip_address': 'PublicIpAddress',
        'region': 'Region',
        'resource_id': 'ResourceID',
        'resource_name': 'ResourceName',
        'resource_type': 'ResourceType',
        'service': 'Service',
        'tags': 'Tags'
    }

    def __init__(self, account_id=None, create_time=None, private_ip_address=None, project_name=None, public_ip_address=None, region=None, resource_id=None, resource_name=None, resource_type=None, service=None, tags=None, _configuration=None):  # noqa: E501
        """ResourceForSearchResourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._create_time = None
        self._private_ip_address = None
        self._project_name = None
        self._public_ip_address = None
        self._region = None
        self._resource_id = None
        self._resource_name = None
        self._resource_type = None
        self._service = None
        self._tags = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if create_time is not None:
            self.create_time = create_time
        if private_ip_address is not None:
            self.private_ip_address = private_ip_address
        if project_name is not None:
            self.project_name = project_name
        if public_ip_address is not None:
            self.public_ip_address = public_ip_address
        if region is not None:
            self.region = region
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_name is not None:
            self.resource_name = resource_name
        if resource_type is not None:
            self.resource_type = resource_type
        if service is not None:
            self.service = service
        if tags is not None:
            self.tags = tags

    @property
    def account_id(self):
        """Gets the account_id of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The account_id of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ResourceForSearchResourcesOutput.


        :param account_id: The account_id of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def create_time(self):
        """Gets the create_time of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The create_time of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ResourceForSearchResourcesOutput.


        :param create_time: The create_time of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def private_ip_address(self):
        """Gets the private_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The private_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ip_address

    @private_ip_address.setter
    def private_ip_address(self, private_ip_address):
        """Sets the private_ip_address of this ResourceForSearchResourcesOutput.


        :param private_ip_address: The private_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._private_ip_address = private_ip_address

    @property
    def project_name(self):
        """Gets the project_name of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The project_name of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ResourceForSearchResourcesOutput.


        :param project_name: The project_name of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def public_ip_address(self):
        """Gets the public_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The public_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._public_ip_address

    @public_ip_address.setter
    def public_ip_address(self, public_ip_address):
        """Sets the public_ip_address of this ResourceForSearchResourcesOutput.


        :param public_ip_address: The public_ip_address of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._public_ip_address = public_ip_address

    @property
    def region(self):
        """Gets the region of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The region of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ResourceForSearchResourcesOutput.


        :param region: The region of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def resource_id(self):
        """Gets the resource_id of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The resource_id of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this ResourceForSearchResourcesOutput.


        :param resource_id: The resource_id of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_name(self):
        """Gets the resource_name of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The resource_name of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_name

    @resource_name.setter
    def resource_name(self, resource_name):
        """Sets the resource_name of this ResourceForSearchResourcesOutput.


        :param resource_name: The resource_name of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_name = resource_name

    @property
    def resource_type(self):
        """Gets the resource_type of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The resource_type of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ResourceForSearchResourcesOutput.


        :param resource_type: The resource_type of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service(self):
        """Gets the service of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The service of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._service

    @service.setter
    def service(self, service):
        """Sets the service of this ResourceForSearchResourcesOutput.


        :param service: The service of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: str
        """

        self._service = service

    @property
    def tags(self):
        """Gets the tags of this ResourceForSearchResourcesOutput.  # noqa: E501


        :return: The tags of this ResourceForSearchResourcesOutput.  # noqa: E501
        :rtype: list[TagForSearchResourcesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ResourceForSearchResourcesOutput.


        :param tags: The tags of this ResourceForSearchResourcesOutput.  # noqa: E501
        :type: list[TagForSearchResourcesOutput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceForSearchResourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceForSearchResourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceForSearchResourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
