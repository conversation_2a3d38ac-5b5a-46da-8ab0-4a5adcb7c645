# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBaselineCheckResRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'baseline_id': 'int',
        'checklist_name': 'str',
        'cloud_providers': 'list[str]',
        'if_white': 'bool',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'result': 'list[str]',
        'sort_by': 'str',
        'sort_order': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'baseline_id': 'BaselineID',
        'checklist_name': 'ChecklistName',
        'cloud_providers': 'CloudProviders',
        'if_white': 'IfWhite',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'result': 'Result',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, baseline_id=None, checklist_name=None, cloud_providers=None, if_white=None, leaf_group_ids=None, page_number=None, page_size=None, result=None, sort_by=None, sort_order=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListBaselineCheckResRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._baseline_id = None
        self._checklist_name = None
        self._cloud_providers = None
        self._if_white = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._result = None
        self._sort_by = None
        self._sort_order = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        if baseline_id is not None:
            self.baseline_id = baseline_id
        if checklist_name is not None:
            self.checklist_name = checklist_name
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if if_white is not None:
            self.if_white = if_white
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if result is not None:
            self.result = result
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The agent_id of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListBaselineCheckResRequest.


        :param agent_id: The agent_id of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The asset_id of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListBaselineCheckResRequest.


        :param asset_id: The asset_id of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The asset_type of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this ListBaselineCheckResRequest.


        :param asset_type: The asset_type of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def baseline_id(self):
        """Gets the baseline_id of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The baseline_id of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this ListBaselineCheckResRequest.


        :param baseline_id: The baseline_id of this ListBaselineCheckResRequest.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def checklist_name(self):
        """Gets the checklist_name of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The checklist_name of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._checklist_name

    @checklist_name.setter
    def checklist_name(self, checklist_name):
        """Sets the checklist_name of this ListBaselineCheckResRequest.


        :param checklist_name: The checklist_name of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._checklist_name = checklist_name

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The cloud_providers of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListBaselineCheckResRequest.


        :param cloud_providers: The cloud_providers of this ListBaselineCheckResRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def if_white(self):
        """Gets the if_white of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The if_white of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_white

    @if_white.setter
    def if_white(self, if_white):
        """Sets the if_white of this ListBaselineCheckResRequest.


        :param if_white: The if_white of this ListBaselineCheckResRequest.  # noqa: E501
        :type: bool
        """

        self._if_white = if_white

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListBaselineCheckResRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListBaselineCheckResRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The page_number of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListBaselineCheckResRequest.


        :param page_number: The page_number of this ListBaselineCheckResRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The page_size of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListBaselineCheckResRequest.


        :param page_size: The page_size of this ListBaselineCheckResRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def result(self):
        """Gets the result of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The result of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this ListBaselineCheckResRequest.


        :param result: The result of this ListBaselineCheckResRequest.  # noqa: E501
        :type: list[str]
        """

        self._result = result

    @property
    def sort_by(self):
        """Gets the sort_by of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The sort_by of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListBaselineCheckResRequest.


        :param sort_by: The sort_by of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The sort_order of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListBaselineCheckResRequest.


        :param sort_order: The sort_order of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListBaselineCheckResRequest.  # noqa: E501


        :return: The top_group_id of this ListBaselineCheckResRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListBaselineCheckResRequest.


        :param top_group_id: The top_group_id of this ListBaselineCheckResRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBaselineCheckResRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBaselineCheckResRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBaselineCheckResRequest):
            return True

        return self.to_dict() != other.to_dict()
