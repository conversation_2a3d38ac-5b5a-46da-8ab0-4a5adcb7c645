# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'basic_data': 'BasicDataForListAccountUserDataOutput',
        'interact_data': 'InteractDataForListAccountUserDataOutput',
        'pay_data': 'PayDataForListAccountUserDataOutput',
        'user_enter_form': 'UserEnterFormForListAccountUserDataOutput',
        'watch_data': 'WatchDataForListAccountUserDataOutput'
    }

    attribute_map = {
        'basic_data': 'BasicData',
        'interact_data': 'InteractData',
        'pay_data': 'PayData',
        'user_enter_form': 'UserEnterForm',
        'watch_data': 'WatchData'
    }

    def __init__(self, basic_data=None, interact_data=None, pay_data=None, user_enter_form=None, watch_data=None, _configuration=None):  # noqa: E501
        """UserForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._basic_data = None
        self._interact_data = None
        self._pay_data = None
        self._user_enter_form = None
        self._watch_data = None
        self.discriminator = None

        if basic_data is not None:
            self.basic_data = basic_data
        if interact_data is not None:
            self.interact_data = interact_data
        if pay_data is not None:
            self.pay_data = pay_data
        if user_enter_form is not None:
            self.user_enter_form = user_enter_form
        if watch_data is not None:
            self.watch_data = watch_data

    @property
    def basic_data(self):
        """Gets the basic_data of this UserForListAccountUserDataOutput.  # noqa: E501


        :return: The basic_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :rtype: BasicDataForListAccountUserDataOutput
        """
        return self._basic_data

    @basic_data.setter
    def basic_data(self, basic_data):
        """Sets the basic_data of this UserForListAccountUserDataOutput.


        :param basic_data: The basic_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :type: BasicDataForListAccountUserDataOutput
        """

        self._basic_data = basic_data

    @property
    def interact_data(self):
        """Gets the interact_data of this UserForListAccountUserDataOutput.  # noqa: E501


        :return: The interact_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :rtype: InteractDataForListAccountUserDataOutput
        """
        return self._interact_data

    @interact_data.setter
    def interact_data(self, interact_data):
        """Sets the interact_data of this UserForListAccountUserDataOutput.


        :param interact_data: The interact_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :type: InteractDataForListAccountUserDataOutput
        """

        self._interact_data = interact_data

    @property
    def pay_data(self):
        """Gets the pay_data of this UserForListAccountUserDataOutput.  # noqa: E501


        :return: The pay_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :rtype: PayDataForListAccountUserDataOutput
        """
        return self._pay_data

    @pay_data.setter
    def pay_data(self, pay_data):
        """Sets the pay_data of this UserForListAccountUserDataOutput.


        :param pay_data: The pay_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :type: PayDataForListAccountUserDataOutput
        """

        self._pay_data = pay_data

    @property
    def user_enter_form(self):
        """Gets the user_enter_form of this UserForListAccountUserDataOutput.  # noqa: E501


        :return: The user_enter_form of this UserForListAccountUserDataOutput.  # noqa: E501
        :rtype: UserEnterFormForListAccountUserDataOutput
        """
        return self._user_enter_form

    @user_enter_form.setter
    def user_enter_form(self, user_enter_form):
        """Sets the user_enter_form of this UserForListAccountUserDataOutput.


        :param user_enter_form: The user_enter_form of this UserForListAccountUserDataOutput.  # noqa: E501
        :type: UserEnterFormForListAccountUserDataOutput
        """

        self._user_enter_form = user_enter_form

    @property
    def watch_data(self):
        """Gets the watch_data of this UserForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :rtype: WatchDataForListAccountUserDataOutput
        """
        return self._watch_data

    @watch_data.setter
    def watch_data(self, watch_data):
        """Sets the watch_data of this UserForListAccountUserDataOutput.


        :param watch_data: The watch_data of this UserForListAccountUserDataOutput.  # noqa: E501
        :type: WatchDataForListAccountUserDataOutput
        """

        self._watch_data = watch_data

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
