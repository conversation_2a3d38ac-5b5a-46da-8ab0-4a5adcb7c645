# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListHostAccountAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'host_account_id': 'int',
        'host_account_name': 'str',
        'page_item_count': 'int',
        'page_no': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'host_account_id': 'HostAccountId',
        'host_account_name': 'HostAccountName',
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo'
    }

    def __init__(self, activity_id=None, host_account_id=None, host_account_name=None, page_item_count=None, page_no=None, _configuration=None):  # noqa: E501
        """ListHostAccountAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._host_account_id = None
        self._host_account_name = None
        self._page_item_count = None
        self._page_no = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if host_account_id is not None:
            self.host_account_id = host_account_id
        if host_account_name is not None:
            self.host_account_name = host_account_name
        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no

    @property
    def activity_id(self):
        """Gets the activity_id of this ListHostAccountAPIRequest.  # noqa: E501


        :return: The activity_id of this ListHostAccountAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListHostAccountAPIRequest.


        :param activity_id: The activity_id of this ListHostAccountAPIRequest.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def host_account_id(self):
        """Gets the host_account_id of this ListHostAccountAPIRequest.  # noqa: E501


        :return: The host_account_id of this ListHostAccountAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._host_account_id

    @host_account_id.setter
    def host_account_id(self, host_account_id):
        """Sets the host_account_id of this ListHostAccountAPIRequest.


        :param host_account_id: The host_account_id of this ListHostAccountAPIRequest.  # noqa: E501
        :type: int
        """

        self._host_account_id = host_account_id

    @property
    def host_account_name(self):
        """Gets the host_account_name of this ListHostAccountAPIRequest.  # noqa: E501


        :return: The host_account_name of this ListHostAccountAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._host_account_name

    @host_account_name.setter
    def host_account_name(self, host_account_name):
        """Sets the host_account_name of this ListHostAccountAPIRequest.


        :param host_account_name: The host_account_name of this ListHostAccountAPIRequest.  # noqa: E501
        :type: str
        """

        self._host_account_name = host_account_name

    @property
    def page_item_count(self):
        """Gets the page_item_count of this ListHostAccountAPIRequest.  # noqa: E501


        :return: The page_item_count of this ListHostAccountAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this ListHostAccountAPIRequest.


        :param page_item_count: The page_item_count of this ListHostAccountAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this ListHostAccountAPIRequest.  # noqa: E501


        :return: The page_no of this ListHostAccountAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this ListHostAccountAPIRequest.


        :param page_no: The page_no of this ListHostAccountAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListHostAccountAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListHostAccountAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListHostAccountAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
