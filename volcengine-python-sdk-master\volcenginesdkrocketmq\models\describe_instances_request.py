# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'spec': 'str',
        'tag_filters': 'list[TagFilterForDescribeInstancesInput]',
        'version': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'spec': 'Spec',
        'tag_filters': 'TagFilters',
        'version': 'Version',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, charge_type=None, instance_id=None, instance_name=None, instance_status=None, page_number=None, page_size=None, project_name=None, sort_by=None, sort_order=None, spec=None, tag_filters=None, version=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._sort_by = None
        self._sort_order = None
        self._spec = None
        self._tag_filters = None
        self._version = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        self.page_number = page_number
        self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if spec is not None:
            self.spec = spec
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if version is not None:
            self.version = version
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def charge_type(self):
        """Gets the charge_type of this DescribeInstancesRequest.  # noqa: E501


        :return: The charge_type of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this DescribeInstancesRequest.


        :param charge_type: The charge_type of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeInstancesRequest.  # noqa: E501


        :return: The instance_id of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeInstancesRequest.


        :param instance_id: The instance_id of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DescribeInstancesRequest.  # noqa: E501


        :return: The instance_name of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DescribeInstancesRequest.


        :param instance_name: The instance_name of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this DescribeInstancesRequest.  # noqa: E501


        :return: The instance_status of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this DescribeInstancesRequest.


        :param instance_status: The instance_status of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def page_number(self):
        """Gets the page_number of this DescribeInstancesRequest.  # noqa: E501


        :return: The page_number of this DescribeInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeInstancesRequest.


        :param page_number: The page_number of this DescribeInstancesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeInstancesRequest.  # noqa: E501


        :return: The page_size of this DescribeInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeInstancesRequest.


        :param page_size: The page_size of this DescribeInstancesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeInstancesRequest.  # noqa: E501


        :return: The project_name of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeInstancesRequest.


        :param project_name: The project_name of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def sort_by(self):
        """Gets the sort_by of this DescribeInstancesRequest.  # noqa: E501


        :return: The sort_by of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this DescribeInstancesRequest.


        :param sort_by: The sort_by of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this DescribeInstancesRequest.  # noqa: E501


        :return: The sort_order of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this DescribeInstancesRequest.


        :param sort_order: The sort_order of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def spec(self):
        """Gets the spec of this DescribeInstancesRequest.  # noqa: E501


        :return: The spec of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._spec

    @spec.setter
    def spec(self, spec):
        """Sets the spec of this DescribeInstancesRequest.


        :param spec: The spec of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._spec = spec

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeInstancesRequest.  # noqa: E501


        :return: The tag_filters of this DescribeInstancesRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeInstancesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeInstancesRequest.


        :param tag_filters: The tag_filters of this DescribeInstancesRequest.  # noqa: E501
        :type: list[TagFilterForDescribeInstancesInput]
        """

        self._tag_filters = tag_filters

    @property
    def version(self):
        """Gets the version of this DescribeInstancesRequest.  # noqa: E501


        :return: The version of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this DescribeInstancesRequest.


        :param version: The version of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._version = version

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeInstancesRequest.  # noqa: E501


        :return: The vpc_id of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeInstancesRequest.


        :param vpc_id: The vpc_id of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeInstancesRequest.  # noqa: E501


        :return: The zone_id of this DescribeInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeInstancesRequest.


        :param zone_id: The zone_id of this DescribeInstancesRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
