# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version

    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""

import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'allocations': 'list[AllocationForDescribeTransitRouterBandwidthPackagesOutput]',
        'bandwidth': 'int',
        'billing_type': 'int',
        'business_status': 'str',
        'creation_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'expired_time': 'str',
        'local_geographic_region_set_id': 'str',
        'peer_geographic_region_set_id': 'str',
        'project_name': 'str',
        'remaining_bandwidth': 'int',
        'status': 'str',
        'tags': 'list[TagForDescribeTransitRouterBandwidthPackagesOutput]',
        'transit_router_bandwidth_package_id': 'str',
        'transit_router_bandwidth_package_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'allocations': 'Allocations',
        'bandwidth': 'Bandwidth',
        'billing_type': 'BillingType',
        'business_status': 'BusinessStatus',
        'creation_time': 'CreationTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'expired_time': 'ExpiredTime',
        'local_geographic_region_set_id': 'LocalGeographicRegionSetId',
        'peer_geographic_region_set_id': 'PeerGeographicRegionSetId',
        'project_name': 'ProjectName',
        'remaining_bandwidth': 'RemainingBandwidth',
        'status': 'Status',
        'tags': 'Tags',
        'transit_router_bandwidth_package_id': 'TransitRouterBandwidthPackageId',
        'transit_router_bandwidth_package_name': 'TransitRouterBandwidthPackageName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, allocations=None, bandwidth=None, billing_type=None, business_status=None,
                 creation_time=None, deleted_time=None, description=None, expired_time=None,
                 local_geographic_region_set_id=None, peer_geographic_region_set_id=None, project_name=None,
                 remaining_bandwidth=None, status=None, tags=None, transit_router_bandwidth_package_id=None,
                 transit_router_bandwidth_package_name=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._allocations = None
        self._bandwidth = None
        self._billing_type = None
        self._business_status = None
        self._creation_time = None
        self._deleted_time = None
        self._description = None
        self._expired_time = None
        self._local_geographic_region_set_id = None
        self._peer_geographic_region_set_id = None
        self._project_name = None
        self._remaining_bandwidth = None
        self._status = None
        self._tags = None
        self._transit_router_bandwidth_package_id = None
        self._transit_router_bandwidth_package_name = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if allocations is not None:
            self.allocations = allocations
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if billing_type is not None:
            self.billing_type = billing_type
        if business_status is not None:
            self.business_status = business_status
        if creation_time is not None:
            self.creation_time = creation_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if expired_time is not None:
            self.expired_time = expired_time
        if local_geographic_region_set_id is not None:
            self.local_geographic_region_set_id = local_geographic_region_set_id
        if peer_geographic_region_set_id is not None:
            self.peer_geographic_region_set_id = peer_geographic_region_set_id
        if project_name is not None:
            self.project_name = project_name
        if remaining_bandwidth is not None:
            self.remaining_bandwidth = remaining_bandwidth
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if transit_router_bandwidth_package_id is not None:
            self.transit_router_bandwidth_package_id = transit_router_bandwidth_package_id
        if transit_router_bandwidth_package_name is not None:
            self.transit_router_bandwidth_package_name = transit_router_bandwidth_package_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The account_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param account_id: The account_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def allocations(self):
        """Gets the allocations of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The allocations of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: list[AllocationForDescribeTransitRouterBandwidthPackagesOutput]
        """
        return self._allocations

    @allocations.setter
    def allocations(self, allocations):
        """Sets the allocations of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param allocations: The allocations of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: list[AllocationForDescribeTransitRouterBandwidthPackagesOutput]
        """

        self._allocations = allocations

    @property
    def bandwidth(self):
        """Gets the bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param bandwidth: The bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def billing_type(self):
        """Gets the billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param billing_type: The billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def business_status(self):
        """Gets the business_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The business_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param business_status: The business_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param creation_time: The creation_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The deleted_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param deleted_time: The deleted_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The description of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param description: The description of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expired_time(self):
        """Gets the expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param expired_time: The expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def local_geographic_region_set_id(self):
        """Gets the local_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The local_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._local_geographic_region_set_id

    @local_geographic_region_set_id.setter
    def local_geographic_region_set_id(self, local_geographic_region_set_id):
        """Sets the local_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param local_geographic_region_set_id: The local_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._local_geographic_region_set_id = local_geographic_region_set_id

    @property
    def peer_geographic_region_set_id(self):
        """Gets the peer_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The peer_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._peer_geographic_region_set_id

    @peer_geographic_region_set_id.setter
    def peer_geographic_region_set_id(self, peer_geographic_region_set_id):
        """Sets the peer_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param peer_geographic_region_set_id: The peer_geographic_region_set_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._peer_geographic_region_set_id = peer_geographic_region_set_id

    @property
    def project_name(self):
        """Gets the project_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The project_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param project_name: The project_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def remaining_bandwidth(self):
        """Gets the remaining_bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The remaining_bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._remaining_bandwidth

    @remaining_bandwidth.setter
    def remaining_bandwidth(self, remaining_bandwidth):
        """Sets the remaining_bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param remaining_bandwidth: The remaining_bandwidth of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: int
        """

        self._remaining_bandwidth = remaining_bandwidth

    @property
    def status(self):
        """Gets the status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param status: The status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The tags of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: list[TagForDescribeTransitRouterBandwidthPackagesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param tags: The tags of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: list[TagForDescribeTransitRouterBandwidthPackagesOutput]
        """

        self._tags = tags

    @property
    def transit_router_bandwidth_package_id(self):
        """Gets the transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_bandwidth_package_id

    @transit_router_bandwidth_package_id.setter
    def transit_router_bandwidth_package_id(self, transit_router_bandwidth_package_id):
        """Sets the transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param transit_router_bandwidth_package_id: The transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    @property
    def transit_router_bandwidth_package_name(self):
        """Gets the transit_router_bandwidth_package_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The transit_router_bandwidth_package_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_bandwidth_package_name

    @transit_router_bandwidth_package_name.setter
    def transit_router_bandwidth_package_name(self, transit_router_bandwidth_package_name):
        """Sets the transit_router_bandwidth_package_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param transit_router_bandwidth_package_name: The transit_router_bandwidth_package_name of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_bandwidth_package_name = transit_router_bandwidth_package_name

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.


        :param update_time: The update_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'direction': 'str',
        'status': 'str',
        'transit_router_id': 'str',
        'transit_router_route_policy_table_id': 'str',
        'transit_router_route_policy_table_name': 'str',
        'transit_router_route_table_ids': 'list[str]',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'direction': 'Direction',
        'status': 'Status',
        'transit_router_id': 'TransitRouterId',
        'transit_router_route_policy_table_id': 'TransitRouterRoutePolicyTableId',
        'transit_router_route_policy_table_name': 'TransitRouterRoutePolicyTableName',
        'transit_router_route_table_ids': 'TransitRouterRouteTableIds',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, direction=None, status=None, transit_router_id=None,
                 transit_router_route_policy_table_id=None, transit_router_route_policy_table_name=None,
                 transit_router_route_table_ids=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._direction = None
        self._status = None
        self._transit_router_id = None
        self._transit_router_route_policy_table_id = None
        self._transit_router_route_policy_table_name = None
        self._transit_router_route_table_ids = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if direction is not None:
            self.direction = direction
        if status is not None:
            self.status = status
        if transit_router_id is not None:
            self.transit_router_id = transit_router_id
        if transit_router_route_policy_table_id is not None:
            self.transit_router_route_policy_table_id = transit_router_route_policy_table_id
        if transit_router_route_policy_table_name is not None:
            self.transit_router_route_policy_table_name = transit_router_route_policy_table_name
        if transit_router_route_table_ids is not None:
            self.transit_router_route_table_ids = transit_router_route_table_ids
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param creation_time: The creation_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The description of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param description: The description of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direction(self):
        """Gets the direction of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The direction of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param direction: The direction of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._direction = direction

    @property
    def status(self):
        """Gets the status of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The status of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param status: The status of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The transit_router_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param transit_router_id: The transit_router_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_id = transit_router_id

    @property
    def transit_router_route_policy_table_id(self):
        """Gets the transit_router_route_policy_table_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The transit_router_route_policy_table_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_table_id

    @transit_router_route_policy_table_id.setter
    def transit_router_route_policy_table_id(self, transit_router_route_policy_table_id):
        """Sets the transit_router_route_policy_table_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param transit_router_route_policy_table_id: The transit_router_route_policy_table_id of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_policy_table_id = transit_router_route_policy_table_id

    @property
    def transit_router_route_policy_table_name(self):
        """Gets the transit_router_route_policy_table_name of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The transit_router_route_policy_table_name of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_table_name

    @transit_router_route_policy_table_name.setter
    def transit_router_route_policy_table_name(self, transit_router_route_policy_table_name):
        """Sets the transit_router_route_policy_table_name of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param transit_router_route_policy_table_name: The transit_router_route_policy_table_name of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_policy_table_name = transit_router_route_policy_table_name

    @property
    def transit_router_route_table_ids(self):
        """Gets the transit_router_route_table_ids of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The transit_router_route_table_ids of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._transit_router_route_table_ids

    @transit_router_route_table_ids.setter
    def transit_router_route_table_ids(self, transit_router_route_table_ids):
        """Sets the transit_router_route_table_ids of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param transit_router_route_table_ids: The transit_router_route_table_ids of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: list[str]
        """

        self._transit_router_route_table_ids = transit_router_route_table_ids

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.


        :param update_time: The update_time of this TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterRoutePolicyTableForDescribeTransitRouterRoutePolicyTablesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_result': 'str',
        'apply_as_path_values': 'list[int]',
        'as_path_operate_mode': 'str',
        'creation_time': 'str',
        'description': 'str',
        'destination_resource_ids': 'list[str]',
        'destination_resource_types': 'list[str]',
        'ip_prefixes': 'list[str]',
        'priority': 'int',
        'source_resource_ids': 'list[str]',
        'source_resource_types': 'list[str]',
        'status': 'str',
        'transit_router_route_policy_entry_id': 'str',
        'transit_router_route_policy_table_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'action_result': 'ActionResult',
        'apply_as_path_values': 'ApplyAsPathValues',
        'as_path_operate_mode': 'AsPathOperateMode',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'destination_resource_ids': 'DestinationResourceIds',
        'destination_resource_types': 'DestinationResourceTypes',
        'ip_prefixes': 'IpPrefixes',
        'priority': 'Priority',
        'source_resource_ids': 'SourceResourceIds',
        'source_resource_types': 'SourceResourceTypes',
        'status': 'Status',
        'transit_router_route_policy_entry_id': 'TransitRouterRoutePolicyEntryId',
        'transit_router_route_policy_table_id': 'TransitRouterRoutePolicyTableId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, action_result=None, apply_as_path_values=None, as_path_operate_mode=None, creation_time=None,
                 description=None, destination_resource_ids=None, destination_resource_types=None, ip_prefixes=None,
                 priority=None, source_resource_ids=None, source_resource_types=None, status=None,
                 transit_router_route_policy_entry_id=None, transit_router_route_policy_table_id=None, update_time=None,
                 _configuration=None):  # noqa: E501
        """TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_result = None
        self._apply_as_path_values = None
        self._as_path_operate_mode = None
        self._creation_time = None
        self._description = None
        self._destination_resource_ids = None
        self._destination_resource_types = None
        self._ip_prefixes = None
        self._priority = None
        self._source_resource_ids = None
        self._source_resource_types = None
        self._status = None
        self._transit_router_route_policy_entry_id = None
        self._transit_router_route_policy_table_id = None
        self._update_time = None
        self.discriminator = None

        if action_result is not None:
            self.action_result = action_result
        if apply_as_path_values is not None:
            self.apply_as_path_values = apply_as_path_values
        if as_path_operate_mode is not None:
            self.as_path_operate_mode = as_path_operate_mode
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if destination_resource_ids is not None:
            self.destination_resource_ids = destination_resource_ids
        if destination_resource_types is not None:
            self.destination_resource_types = destination_resource_types
        if ip_prefixes is not None:
            self.ip_prefixes = ip_prefixes
        if priority is not None:
            self.priority = priority
        if source_resource_ids is not None:
            self.source_resource_ids = source_resource_ids
        if source_resource_types is not None:
            self.source_resource_types = source_resource_types
        if status is not None:
            self.status = status
        if transit_router_route_policy_entry_id is not None:
            self.transit_router_route_policy_entry_id = transit_router_route_policy_entry_id
        if transit_router_route_policy_table_id is not None:
            self.transit_router_route_policy_table_id = transit_router_route_policy_table_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def action_result(self):
        """Gets the action_result of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The action_result of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._action_result

    @action_result.setter
    def action_result(self, action_result):
        """Sets the action_result of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param action_result: The action_result of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._action_result = action_result

    @property
    def apply_as_path_values(self):
        """Gets the apply_as_path_values of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The apply_as_path_values of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._apply_as_path_values

    @apply_as_path_values.setter
    def apply_as_path_values(self, apply_as_path_values):
        """Sets the apply_as_path_values of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param apply_as_path_values: The apply_as_path_values of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[int]
        """

        self._apply_as_path_values = apply_as_path_values

    @property
    def as_path_operate_mode(self):
        """Gets the as_path_operate_mode of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The as_path_operate_mode of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._as_path_operate_mode

    @as_path_operate_mode.setter
    def as_path_operate_mode(self, as_path_operate_mode):
        """Sets the as_path_operate_mode of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param as_path_operate_mode: The as_path_operate_mode of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._as_path_operate_mode = as_path_operate_mode

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param creation_time: The creation_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The description of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param description: The description of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_resource_ids(self):
        """Gets the destination_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The destination_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_resource_ids

    @destination_resource_ids.setter
    def destination_resource_ids(self, destination_resource_ids):
        """Sets the destination_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param destination_resource_ids: The destination_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._destination_resource_ids = destination_resource_ids

    @property
    def destination_resource_types(self):
        """Gets the destination_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The destination_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_resource_types

    @destination_resource_types.setter
    def destination_resource_types(self, destination_resource_types):
        """Sets the destination_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param destination_resource_types: The destination_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._destination_resource_types = destination_resource_types

    @property
    def ip_prefixes(self):
        """Gets the ip_prefixes of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The ip_prefixes of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_prefixes

    @ip_prefixes.setter
    def ip_prefixes(self, ip_prefixes):
        """Sets the ip_prefixes of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param ip_prefixes: The ip_prefixes of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._ip_prefixes = ip_prefixes

    @property
    def priority(self):
        """Gets the priority of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The priority of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param priority: The priority of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def source_resource_ids(self):
        """Gets the source_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The source_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_resource_ids

    @source_resource_ids.setter
    def source_resource_ids(self, source_resource_ids):
        """Sets the source_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param source_resource_ids: The source_resource_ids of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._source_resource_ids = source_resource_ids

    @property
    def source_resource_types(self):
        """Gets the source_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The source_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_resource_types

    @source_resource_types.setter
    def source_resource_types(self, source_resource_types):
        """Sets the source_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param source_resource_types: The source_resource_types of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._source_resource_types = source_resource_types

    @property
    def status(self):
        """Gets the status of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The status of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param status: The status of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_route_policy_entry_id(self):
        """Gets the transit_router_route_policy_entry_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The transit_router_route_policy_entry_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_entry_id

    @transit_router_route_policy_entry_id.setter
    def transit_router_route_policy_entry_id(self, transit_router_route_policy_entry_id):
        """Sets the transit_router_route_policy_entry_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param transit_router_route_policy_entry_id: The transit_router_route_policy_entry_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_policy_entry_id = transit_router_route_policy_entry_id

    @property
    def transit_router_route_policy_table_id(self):
        """Gets the transit_router_route_policy_table_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The transit_router_route_policy_table_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_table_id

    @transit_router_route_policy_table_id.setter
    def transit_router_route_policy_table_id(self, transit_router_route_policy_table_id):
        """Sets the transit_router_route_policy_table_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param transit_router_route_policy_table_id: The transit_router_route_policy_table_id of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_policy_table_id = transit_router_route_policy_table_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.


        :param update_time: The update_time of this TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterRoutePolicyEntryForDescribeTransitRouterRoutePolicyEntriesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'status': 'str',
        'transit_router_forward_policy_table_id': 'str',
        'transit_router_forward_policy_table_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'status': 'Status',
        'transit_router_forward_policy_table_id': 'TransitRouterForwardPolicyTableId',
        'transit_router_forward_policy_table_name': 'TransitRouterForwardPolicyTableName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, status=None, transit_router_forward_policy_table_id=None,
                 transit_router_forward_policy_table_name=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._status = None
        self._transit_router_forward_policy_table_id = None
        self._transit_router_forward_policy_table_name = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if status is not None:
            self.status = status
        if transit_router_forward_policy_table_id is not None:
            self.transit_router_forward_policy_table_id = transit_router_forward_policy_table_id
        if transit_router_forward_policy_table_name is not None:
            self.transit_router_forward_policy_table_name = transit_router_forward_policy_table_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param creation_time: The creation_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The description of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param description: The description of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def status(self):
        """Gets the status of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The status of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param status: The status of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_forward_policy_table_id(self):
        """Gets the transit_router_forward_policy_table_id of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The transit_router_forward_policy_table_id of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_table_id

    @transit_router_forward_policy_table_id.setter
    def transit_router_forward_policy_table_id(self, transit_router_forward_policy_table_id):
        """Sets the transit_router_forward_policy_table_id of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param transit_router_forward_policy_table_id: The transit_router_forward_policy_table_id of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_forward_policy_table_id = transit_router_forward_policy_table_id

    @property
    def transit_router_forward_policy_table_name(self):
        """Gets the transit_router_forward_policy_table_name of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The transit_router_forward_policy_table_name of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_table_name

    @transit_router_forward_policy_table_name.setter
    def transit_router_forward_policy_table_name(self, transit_router_forward_policy_table_name):
        """Sets the transit_router_forward_policy_table_name of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param transit_router_forward_policy_table_name: The transit_router_forward_policy_table_name of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_forward_policy_table_name = transit_router_forward_policy_table_name

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.


        :param update_time: The update_time of this TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterForwardPolicyTableForDescribeTransitRouterForwardPolicyTablesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'priority': 'int',
        'source_cidr_block': 'str',
        'status': 'str',
        'transit_router_forward_policy_entry_id': 'str',
        'transit_router_forward_policy_table_id': 'str',
        'transit_router_route_table_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'priority': 'Priority',
        'source_cidr_block': 'SourceCidrBlock',
        'status': 'Status',
        'transit_router_forward_policy_entry_id': 'TransitRouterForwardPolicyEntryId',
        'transit_router_forward_policy_table_id': 'TransitRouterForwardPolicyTableId',
        'transit_router_route_table_id': 'TransitRouterRouteTableId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, priority=None, source_cidr_block=None, status=None,
                 transit_router_forward_policy_entry_id=None, transit_router_forward_policy_table_id=None,
                 transit_router_route_table_id=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._priority = None
        self._source_cidr_block = None
        self._status = None
        self._transit_router_forward_policy_entry_id = None
        self._transit_router_forward_policy_table_id = None
        self._transit_router_route_table_id = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if priority is not None:
            self.priority = priority
        if source_cidr_block is not None:
            self.source_cidr_block = source_cidr_block
        if status is not None:
            self.status = status
        if transit_router_forward_policy_entry_id is not None:
            self.transit_router_forward_policy_entry_id = transit_router_forward_policy_entry_id
        if transit_router_forward_policy_table_id is not None:
            self.transit_router_forward_policy_table_id = transit_router_forward_policy_table_id
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param creation_time: The creation_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The description of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param description: The description of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def priority(self):
        """Gets the priority of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The priority of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param priority: The priority of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def source_cidr_block(self):
        """Gets the source_cidr_block of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The source_cidr_block of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_cidr_block

    @source_cidr_block.setter
    def source_cidr_block(self, source_cidr_block):
        """Sets the source_cidr_block of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param source_cidr_block: The source_cidr_block of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._source_cidr_block = source_cidr_block

    @property
    def status(self):
        """Gets the status of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The status of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param status: The status of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_forward_policy_entry_id(self):
        """Gets the transit_router_forward_policy_entry_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The transit_router_forward_policy_entry_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_entry_id

    @transit_router_forward_policy_entry_id.setter
    def transit_router_forward_policy_entry_id(self, transit_router_forward_policy_entry_id):
        """Sets the transit_router_forward_policy_entry_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param transit_router_forward_policy_entry_id: The transit_router_forward_policy_entry_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_forward_policy_entry_id = transit_router_forward_policy_entry_id

    @property
    def transit_router_forward_policy_table_id(self):
        """Gets the transit_router_forward_policy_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The transit_router_forward_policy_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_table_id

    @transit_router_forward_policy_table_id.setter
    def transit_router_forward_policy_table_id(self, transit_router_forward_policy_table_id):
        """Sets the transit_router_forward_policy_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param transit_router_forward_policy_table_id: The transit_router_forward_policy_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_forward_policy_table_id = transit_router_forward_policy_table_id

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The transit_router_route_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param transit_router_route_table_id: The transit_router_route_table_id of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.


        :param update_time: The update_time of this TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterForwardPolicyEntryForDescribeTransitRouterForwardPolicyEntriesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_status': 'int',
        'billing_type': 'int',
        'expired_time': 'str',
        'reclaim_time': 'str',
        'remain_renew_times': 'int',
        'renew_type': 'str',
        'transit_router_bandwidth_package_id': 'str'
    }

    attribute_map = {
        'billing_status': 'BillingStatus',
        'billing_type': 'BillingType',
        'expired_time': 'ExpiredTime',
        'reclaim_time': 'ReclaimTime',
        'remain_renew_times': 'RemainRenewTimes',
        'renew_type': 'RenewType',
        'transit_router_bandwidth_package_id': 'TransitRouterBandwidthPackageId'
    }

    def __init__(self, billing_status=None, billing_type=None, expired_time=None, reclaim_time=None,
                 remain_renew_times=None, renew_type=None, transit_router_bandwidth_package_id=None,
                 _configuration=None):  # noqa: E501
        """TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_status = None
        self._billing_type = None
        self._expired_time = None
        self._reclaim_time = None
        self._remain_renew_times = None
        self._renew_type = None
        self._transit_router_bandwidth_package_id = None
        self.discriminator = None

        if billing_status is not None:
            self.billing_status = billing_status
        if billing_type is not None:
            self.billing_type = billing_type
        if expired_time is not None:
            self.expired_time = expired_time
        if reclaim_time is not None:
            self.reclaim_time = reclaim_time
        if remain_renew_times is not None:
            self.remain_renew_times = remain_renew_times
        if renew_type is not None:
            self.renew_type = renew_type
        if transit_router_bandwidth_package_id is not None:
            self.transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    @property
    def billing_status(self):
        """Gets the billing_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The billing_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_status

    @billing_status.setter
    def billing_status(self, billing_status):
        """Sets the billing_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param billing_status: The billing_status of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: int
        """

        self._billing_status = billing_status

    @property
    def billing_type(self):
        """Gets the billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param billing_type: The billing_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def expired_time(self):
        """Gets the expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param expired_time: The expired_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def reclaim_time(self):
        """Gets the reclaim_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The reclaim_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._reclaim_time

    @reclaim_time.setter
    def reclaim_time(self, reclaim_time):
        """Sets the reclaim_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param reclaim_time: The reclaim_time of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: str
        """

        self._reclaim_time = reclaim_time

    @property
    def remain_renew_times(self):
        """Gets the remain_renew_times of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The remain_renew_times of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: int
        """
        return self._remain_renew_times

    @remain_renew_times.setter
    def remain_renew_times(self, remain_renew_times):
        """Sets the remain_renew_times of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param remain_renew_times: The remain_renew_times of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: int
        """

        self._remain_renew_times = remain_renew_times

    @property
    def renew_type(self):
        """Gets the renew_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The renew_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param renew_type: The renew_type of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: str
        """

        self._renew_type = renew_type

    @property
    def transit_router_bandwidth_package_id(self):
        """Gets the transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501


        :return: The transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_bandwidth_package_id

    @transit_router_bandwidth_package_id.setter
    def transit_router_bandwidth_package_id(self, transit_router_bandwidth_package_id):
        """Sets the transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.


        :param transit_router_bandwidth_package_id: The transit_router_bandwidth_package_id of this TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_bandwidth_package_id = transit_router_bandwidth_package_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterBandwidthPackageForDescribeTransitRouterBandwidthPackagesBillingOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'auto_publish_route_enabled': 'bool',
        'creation_time': 'str',
        'description': 'str',
        'direct_connect_gateway_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput]',
        'transit_router_attachment_id': 'str',
        'transit_router_attachment_name': 'str',
        'transit_router_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'auto_publish_route_enabled': 'AutoPublishRouteEnabled',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'status': 'Status',
        'tags': 'Tags',
        'transit_router_attachment_id': 'TransitRouterAttachmentId',
        'transit_router_attachment_name': 'TransitRouterAttachmentName',
        'transit_router_id': 'TransitRouterId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, auto_publish_route_enabled=None, creation_time=None, description=None,
                 direct_connect_gateway_id=None, status=None, tags=None, transit_router_attachment_id=None,
                 transit_router_attachment_name=None, transit_router_id=None, update_time=None,
                 _configuration=None):  # noqa: E501
        """TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._auto_publish_route_enabled = None
        self._creation_time = None
        self._description = None
        self._direct_connect_gateway_id = None
        self._status = None
        self._tags = None
        self._transit_router_attachment_id = None
        self._transit_router_attachment_name = None
        self._transit_router_id = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if auto_publish_route_enabled is not None:
            self.auto_publish_route_enabled = auto_publish_route_enabled
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if transit_router_attachment_id is not None:
            self.transit_router_attachment_id = transit_router_attachment_id
        if transit_router_attachment_name is not None:
            self.transit_router_attachment_name = transit_router_attachment_name
        if transit_router_id is not None:
            self.transit_router_id = transit_router_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The account_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param account_id: The account_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def auto_publish_route_enabled(self):
        """Gets the auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_publish_route_enabled

    @auto_publish_route_enabled.setter
    def auto_publish_route_enabled(self, auto_publish_route_enabled):
        """Sets the auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param auto_publish_route_enabled: The auto_publish_route_enabled of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: bool
        """

        self._auto_publish_route_enabled = auto_publish_route_enabled

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param creation_time: The creation_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The description of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param description: The description of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The direct_connect_gateway_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def status(self):
        """Gets the status of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The status of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param status: The status of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The tags of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: list[TagForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param tags: The tags of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: list[TagForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput]
        """

        self._tags = tags

    @property
    def transit_router_attachment_id(self):
        """Gets the transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_id

    @transit_router_attachment_id.setter
    def transit_router_attachment_id(self, transit_router_attachment_id):
        """Sets the transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param transit_router_attachment_id: The transit_router_attachment_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_id = transit_router_attachment_id

    @property
    def transit_router_attachment_name(self):
        """Gets the transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_name

    @transit_router_attachment_name.setter
    def transit_router_attachment_name(self, transit_router_attachment_name):
        """Sets the transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param transit_router_attachment_name: The transit_router_attachment_name of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_name = transit_router_attachment_name

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param transit_router_id: The transit_router_id of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_id = transit_router_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501


        :return: The update_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.


        :param update_time: The update_time of this TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterAttachmentForDescribeTransitRouterDirectConnectGatewayAttachmentsOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'status': 'str',
        'transit_router_attachment_id': 'str',
        'transit_router_route_table_id': 'str'
    }

    attribute_map = {
        'status': 'Status',
        'transit_router_attachment_id': 'TransitRouterAttachmentId',
        'transit_router_route_table_id': 'TransitRouterRouteTableId'
    }

    def __init__(self, status=None, transit_router_attachment_id=None, transit_router_route_table_id=None,
                 _configuration=None):  # noqa: E501
        """TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._status = None
        self._transit_router_attachment_id = None
        self._transit_router_route_table_id = None
        self.discriminator = None

        if status is not None:
            self.status = status
        if transit_router_attachment_id is not None:
            self.transit_router_attachment_id = transit_router_attachment_id
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id

    @property
    def status(self):
        """Gets the status of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501


        :return: The status of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.


        :param status: The status of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_attachment_id(self):
        """Gets the transit_router_attachment_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501


        :return: The transit_router_attachment_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_id

    @transit_router_attachment_id.setter
    def transit_router_attachment_id(self, transit_router_attachment_id):
        """Sets the transit_router_attachment_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.


        :param transit_router_attachment_id: The transit_router_attachment_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_id = transit_router_attachment_id

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501


        :return: The transit_router_route_table_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.


        :param transit_router_route_table_id: The transit_router_route_table_id of this TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other,
                          TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other,
                          TransitRouterRouteTableAssociationForDescribeTransitRouterRouteTableAssociationsOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'status': 'str',
        'transit_router_attachment_id': 'str',
        'transit_router_route_table_id': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'status': 'Status',
        'transit_router_attachment_id': 'TransitRouterAttachmentId',
        'transit_router_route_table_id': 'TransitRouterRouteTableId'
    }

    def __init__(self, creation_time=None, status=None, transit_router_attachment_id=None,
                 transit_router_route_table_id=None, _configuration=None):  # noqa: E501
        """TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._status = None
        self._transit_router_attachment_id = None
        self._transit_router_route_table_id = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if status is not None:
            self.status = status
        if transit_router_attachment_id is not None:
            self.transit_router_attachment_id = transit_router_attachment_id
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.


        :param creation_time: The creation_time of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def status(self):
        """Gets the status of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501


        :return: The status of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.


        :param status: The status of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_attachment_id(self):
        """Gets the transit_router_attachment_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501


        :return: The transit_router_attachment_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_attachment_id

    @transit_router_attachment_id.setter
    def transit_router_attachment_id(self, transit_router_attachment_id):
        """Sets the transit_router_attachment_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.


        :param transit_router_attachment_id: The transit_router_attachment_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_attachment_id = transit_router_attachment_id

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501


        :return: The transit_router_route_table_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.


        :param transit_router_route_table_id: The transit_router_route_table_id of this TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other,
                          TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other,
                          TransitRouterRouteTablePropagationForDescribeTransitRouterRouteTablePropagationsOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_percent': 'int',
        'creation_time': 'str',
        'description': 'str',
        'dscps': 'list[int]',
        'is_default': 'bool',
        'status': 'str',
        'transit_router_traffic_qos_queue_entry_id': 'str',
        'transit_router_traffic_qos_queue_entry_name': 'str',
        'transit_router_traffic_qos_queue_policy_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'bandwidth_percent': 'BandwidthPercent',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'dscps': 'Dscps',
        'is_default': 'IsDefault',
        'status': 'Status',
        'transit_router_traffic_qos_queue_entry_id': 'TransitRouterTrafficQosQueueEntryId',
        'transit_router_traffic_qos_queue_entry_name': 'TransitRouterTrafficQosQueueEntryName',
        'transit_router_traffic_qos_queue_policy_id': 'TransitRouterTrafficQosQueuePolicyId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, bandwidth_percent=None, creation_time=None, description=None, dscps=None, is_default=None,
                 status=None, transit_router_traffic_qos_queue_entry_id=None,
                 transit_router_traffic_qos_queue_entry_name=None, transit_router_traffic_qos_queue_policy_id=None,
                 update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_percent = None
        self._creation_time = None
        self._description = None
        self._dscps = None
        self._is_default = None
        self._status = None
        self._transit_router_traffic_qos_queue_entry_id = None
        self._transit_router_traffic_qos_queue_entry_name = None
        self._transit_router_traffic_qos_queue_policy_id = None
        self._update_time = None
        self.discriminator = None

        if bandwidth_percent is not None:
            self.bandwidth_percent = bandwidth_percent
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if dscps is not None:
            self.dscps = dscps
        if is_default is not None:
            self.is_default = is_default
        if status is not None:
            self.status = status
        if transit_router_traffic_qos_queue_entry_id is not None:
            self.transit_router_traffic_qos_queue_entry_id = transit_router_traffic_qos_queue_entry_id
        if transit_router_traffic_qos_queue_entry_name is not None:
            self.transit_router_traffic_qos_queue_entry_name = transit_router_traffic_qos_queue_entry_name
        if transit_router_traffic_qos_queue_policy_id is not None:
            self.transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def bandwidth_percent(self):
        """Gets the bandwidth_percent of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The bandwidth_percent of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_percent

    @bandwidth_percent.setter
    def bandwidth_percent(self, bandwidth_percent):
        """Sets the bandwidth_percent of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param bandwidth_percent: The bandwidth_percent of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth_percent = bandwidth_percent

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param creation_time: The creation_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The description of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param description: The description of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dscps(self):
        """Gets the dscps of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The dscps of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._dscps

    @dscps.setter
    def dscps(self, dscps):
        """Sets the dscps of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param dscps: The dscps of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: list[int]
        """

        self._dscps = dscps

    @property
    def is_default(self):
        """Gets the is_default of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The is_default of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_default

    @is_default.setter
    def is_default(self, is_default):
        """Sets the is_default of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param is_default: The is_default of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: bool
        """

        self._is_default = is_default

    @property
    def status(self):
        """Gets the status of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The status of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param status: The status of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_traffic_qos_queue_entry_id(self):
        """Gets the transit_router_traffic_qos_queue_entry_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_entry_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_entry_id

    @transit_router_traffic_qos_queue_entry_id.setter
    def transit_router_traffic_qos_queue_entry_id(self, transit_router_traffic_qos_queue_entry_id):
        """Sets the transit_router_traffic_qos_queue_entry_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param transit_router_traffic_qos_queue_entry_id: The transit_router_traffic_qos_queue_entry_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_entry_id = transit_router_traffic_qos_queue_entry_id

    @property
    def transit_router_traffic_qos_queue_entry_name(self):
        """Gets the transit_router_traffic_qos_queue_entry_name of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_entry_name of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_entry_name

    @transit_router_traffic_qos_queue_entry_name.setter
    def transit_router_traffic_qos_queue_entry_name(self, transit_router_traffic_qos_queue_entry_name):
        """Sets the transit_router_traffic_qos_queue_entry_name of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param transit_router_traffic_qos_queue_entry_name: The transit_router_traffic_qos_queue_entry_name of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_entry_name = transit_router_traffic_qos_queue_entry_name

    @property
    def transit_router_traffic_qos_queue_policy_id(self):
        """Gets the transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_policy_id

    @transit_router_traffic_qos_queue_policy_id.setter
    def transit_router_traffic_qos_queue_policy_id(self, transit_router_traffic_qos_queue_policy_id):
        """Sets the transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param transit_router_traffic_qos_queue_policy_id: The transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.


        :param update_time: The update_time of this TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterTrafficQosQueueEntryForDescribeTransitRouterTrafficQosQueueEntriesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'status': 'str',
        'transit_router_traffic_qos_queue_policy_id': 'str',
        'transit_router_traffic_qos_queue_policy_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'status': 'Status',
        'transit_router_traffic_qos_queue_policy_id': 'TransitRouterTrafficQosQueuePolicyId',
        'transit_router_traffic_qos_queue_policy_name': 'TransitRouterTrafficQosQueuePolicyName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, status=None,
                 transit_router_traffic_qos_queue_policy_id=None, transit_router_traffic_qos_queue_policy_name=None,
                 update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._status = None
        self._transit_router_traffic_qos_queue_policy_id = None
        self._transit_router_traffic_qos_queue_policy_name = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if status is not None:
            self.status = status
        if transit_router_traffic_qos_queue_policy_id is not None:
            self.transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id
        if transit_router_traffic_qos_queue_policy_name is not None:
            self.transit_router_traffic_qos_queue_policy_name = transit_router_traffic_qos_queue_policy_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param creation_time: The creation_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The description of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param description: The description of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def status(self):
        """Gets the status of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The status of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param status: The status of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_traffic_qos_queue_policy_id(self):
        """Gets the transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_policy_id

    @transit_router_traffic_qos_queue_policy_id.setter
    def transit_router_traffic_qos_queue_policy_id(self, transit_router_traffic_qos_queue_policy_id):
        """Sets the transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param transit_router_traffic_qos_queue_policy_id: The transit_router_traffic_qos_queue_policy_id of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_policy_id = transit_router_traffic_qos_queue_policy_id

    @property
    def transit_router_traffic_qos_queue_policy_name(self):
        """Gets the transit_router_traffic_qos_queue_policy_name of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_queue_policy_name of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_queue_policy_name

    @transit_router_traffic_qos_queue_policy_name.setter
    def transit_router_traffic_qos_queue_policy_name(self, transit_router_traffic_qos_queue_policy_name):
        """Sets the transit_router_traffic_qos_queue_policy_name of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param transit_router_traffic_qos_queue_policy_name: The transit_router_traffic_qos_queue_policy_name of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_queue_policy_name = transit_router_traffic_qos_queue_policy_name

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.


        :param update_time: The update_time of this TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosQueuePolicyForDescribeTransitRouterTrafficQosQueuePoliciesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'destination_cidr_block': 'str',
        'destination_port_end': 'int',
        'destination_port_start': 'int',
        'match_dscp': 'int',
        'priority': 'int',
        'protocol': 'str',
        'remarking_dscp': 'int',
        'source_cidr_block': 'str',
        'source_port_end': 'int',
        'source_port_start': 'int',
        'status': 'str',
        'transit_router_traffic_qos_marking_entry_id': 'str',
        'transit_router_traffic_qos_marking_entry_name': 'str',
        'transit_router_traffic_qos_marking_policy_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'destination_cidr_block': 'DestinationCidrBlock',
        'destination_port_end': 'DestinationPortEnd',
        'destination_port_start': 'DestinationPortStart',
        'match_dscp': 'MatchDscp',
        'priority': 'Priority',
        'protocol': 'Protocol',
        'remarking_dscp': 'RemarkingDscp',
        'source_cidr_block': 'SourceCidrBlock',
        'source_port_end': 'SourcePortEnd',
        'source_port_start': 'SourcePortStart',
        'status': 'Status',
        'transit_router_traffic_qos_marking_entry_id': 'TransitRouterTrafficQosMarkingEntryId',
        'transit_router_traffic_qos_marking_entry_name': 'TransitRouterTrafficQosMarkingEntryName',
        'transit_router_traffic_qos_marking_policy_id': 'TransitRouterTrafficQosMarkingPolicyId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, destination_cidr_block=None, destination_port_end=None,
                 destination_port_start=None, match_dscp=None, priority=None, protocol=None, remarking_dscp=None,
                 source_cidr_block=None, source_port_end=None, source_port_start=None, status=None,
                 transit_router_traffic_qos_marking_entry_id=None, transit_router_traffic_qos_marking_entry_name=None,
                 transit_router_traffic_qos_marking_policy_id=None, update_time=None,
                 _configuration=None):  # noqa: E501
        """TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._destination_cidr_block = None
        self._destination_port_end = None
        self._destination_port_start = None
        self._match_dscp = None
        self._priority = None
        self._protocol = None
        self._remarking_dscp = None
        self._source_cidr_block = None
        self._source_port_end = None
        self._source_port_start = None
        self._status = None
        self._transit_router_traffic_qos_marking_entry_id = None
        self._transit_router_traffic_qos_marking_entry_name = None
        self._transit_router_traffic_qos_marking_policy_id = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if destination_port_end is not None:
            self.destination_port_end = destination_port_end
        if destination_port_start is not None:
            self.destination_port_start = destination_port_start
        if match_dscp is not None:
            self.match_dscp = match_dscp
        if priority is not None:
            self.priority = priority
        if protocol is not None:
            self.protocol = protocol
        if remarking_dscp is not None:
            self.remarking_dscp = remarking_dscp
        if source_cidr_block is not None:
            self.source_cidr_block = source_cidr_block
        if source_port_end is not None:
            self.source_port_end = source_port_end
        if source_port_start is not None:
            self.source_port_start = source_port_start
        if status is not None:
            self.status = status
        if transit_router_traffic_qos_marking_entry_id is not None:
            self.transit_router_traffic_qos_marking_entry_id = transit_router_traffic_qos_marking_entry_id
        if transit_router_traffic_qos_marking_entry_name is not None:
            self.transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name
        if transit_router_traffic_qos_marking_policy_id is not None:
            self.transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param creation_time: The creation_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The description of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param description: The description of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The destination_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param destination_cidr_block: The destination_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def destination_port_end(self):
        """Gets the destination_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The destination_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._destination_port_end

    @destination_port_end.setter
    def destination_port_end(self, destination_port_end):
        """Sets the destination_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param destination_port_end: The destination_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._destination_port_end = destination_port_end

    @property
    def destination_port_start(self):
        """Gets the destination_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The destination_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._destination_port_start

    @destination_port_start.setter
    def destination_port_start(self, destination_port_start):
        """Sets the destination_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param destination_port_start: The destination_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._destination_port_start = destination_port_start

    @property
    def match_dscp(self):
        """Gets the match_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The match_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._match_dscp

    @match_dscp.setter
    def match_dscp(self, match_dscp):
        """Sets the match_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param match_dscp: The match_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._match_dscp = match_dscp

    @property
    def priority(self):
        """Gets the priority of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The priority of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param priority: The priority of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def protocol(self):
        """Gets the protocol of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The protocol of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param protocol: The protocol of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def remarking_dscp(self):
        """Gets the remarking_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The remarking_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._remarking_dscp

    @remarking_dscp.setter
    def remarking_dscp(self, remarking_dscp):
        """Sets the remarking_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param remarking_dscp: The remarking_dscp of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._remarking_dscp = remarking_dscp

    @property
    def source_cidr_block(self):
        """Gets the source_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The source_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_cidr_block

    @source_cidr_block.setter
    def source_cidr_block(self, source_cidr_block):
        """Sets the source_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param source_cidr_block: The source_cidr_block of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._source_cidr_block = source_cidr_block

    @property
    def source_port_end(self):
        """Gets the source_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The source_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._source_port_end

    @source_port_end.setter
    def source_port_end(self, source_port_end):
        """Sets the source_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param source_port_end: The source_port_end of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._source_port_end = source_port_end

    @property
    def source_port_start(self):
        """Gets the source_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The source_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: int
        """
        return self._source_port_start

    @source_port_start.setter
    def source_port_start(self, source_port_start):
        """Sets the source_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param source_port_start: The source_port_start of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: int
        """

        self._source_port_start = source_port_start

    @property
    def status(self):
        """Gets the status of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The status of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param status: The status of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_traffic_qos_marking_entry_id(self):
        """Gets the transit_router_traffic_qos_marking_entry_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_entry_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_entry_id

    @transit_router_traffic_qos_marking_entry_id.setter
    def transit_router_traffic_qos_marking_entry_id(self, transit_router_traffic_qos_marking_entry_id):
        """Sets the transit_router_traffic_qos_marking_entry_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param transit_router_traffic_qos_marking_entry_id: The transit_router_traffic_qos_marking_entry_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_entry_id = transit_router_traffic_qos_marking_entry_id

    @property
    def transit_router_traffic_qos_marking_entry_name(self):
        """Gets the transit_router_traffic_qos_marking_entry_name of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_entry_name of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_entry_name

    @transit_router_traffic_qos_marking_entry_name.setter
    def transit_router_traffic_qos_marking_entry_name(self, transit_router_traffic_qos_marking_entry_name):
        """Sets the transit_router_traffic_qos_marking_entry_name of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param transit_router_traffic_qos_marking_entry_name: The transit_router_traffic_qos_marking_entry_name of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name

    @property
    def transit_router_traffic_qos_marking_policy_id(self):
        """Gets the transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_id

    @transit_router_traffic_qos_marking_policy_id.setter
    def transit_router_traffic_qos_marking_policy_id(self, transit_router_traffic_qos_marking_policy_id):
        """Sets the transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param transit_router_traffic_qos_marking_policy_id: The transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.


        :param update_time: The update_time of this TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosMarkingEntryForDescribeTransitRouterTrafficQosMarkingEntriesOutput):
            return True

        return self.to_dict() != other.to_dict()


class TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'status': 'str',
        'transit_router_traffic_qos_marking_policy_id': 'str',
        'transit_router_traffic_qos_marking_policy_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'status': 'Status',
        'transit_router_traffic_qos_marking_policy_id': 'TransitRouterTrafficQosMarkingPolicyId',
        'transit_router_traffic_qos_marking_policy_name': 'TransitRouterTrafficQosMarkingPolicyName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, status=None,
                 transit_router_traffic_qos_marking_policy_id=None, transit_router_traffic_qos_marking_policy_name=None,
                 update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._status = None
        self._transit_router_traffic_qos_marking_policy_id = None
        self._transit_router_traffic_qos_marking_policy_name = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if status is not None:
            self.status = status
        if transit_router_traffic_qos_marking_policy_id is not None:
            self.transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id
        if transit_router_traffic_qos_marking_policy_name is not None:
            self.transit_router_traffic_qos_marking_policy_name = transit_router_traffic_qos_marking_policy_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param creation_time: The creation_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The description of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param description: The description of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def status(self):
        """Gets the status of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The status of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param status: The status of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_traffic_qos_marking_policy_id(self):
        """Gets the transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_id

    @transit_router_traffic_qos_marking_policy_id.setter
    def transit_router_traffic_qos_marking_policy_id(self, transit_router_traffic_qos_marking_policy_id):
        """Sets the transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param transit_router_traffic_qos_marking_policy_id: The transit_router_traffic_qos_marking_policy_id of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    @property
    def transit_router_traffic_qos_marking_policy_name(self):
        """Gets the transit_router_traffic_qos_marking_policy_name of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_name of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_name

    @transit_router_traffic_qos_marking_policy_name.setter
    def transit_router_traffic_qos_marking_policy_name(self, transit_router_traffic_qos_marking_policy_name):
        """Sets the transit_router_traffic_qos_marking_policy_name of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param transit_router_traffic_qos_marking_policy_name: The transit_router_traffic_qos_marking_policy_name of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_policy_name = transit_router_traffic_qos_marking_policy_name

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.


        :param update_time: The update_time of this TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput,
                      dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other,
                          TransitRouterTrafficQosMarkingPolicyForDescribeTransitRouterTrafficQosMarkingPoliciesOutput):
            return True

        return self.to_dict() != other.to_dict()
