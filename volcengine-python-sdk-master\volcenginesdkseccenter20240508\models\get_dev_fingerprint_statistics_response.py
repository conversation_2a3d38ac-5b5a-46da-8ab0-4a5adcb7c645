# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDevFingerprintStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'port': 'int',
        'process': 'int',
        'software': 'int'
    }

    attribute_map = {
        'port': 'Port',
        'process': 'Process',
        'software': 'Software'
    }

    def __init__(self, port=None, process=None, software=None, _configuration=None):  # noqa: E501
        """GetDevFingerprintStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._port = None
        self._process = None
        self._software = None
        self.discriminator = None

        if port is not None:
            self.port = port
        if process is not None:
            self.process = process
        if software is not None:
            self.software = software

    @property
    def port(self):
        """Gets the port of this GetDevFingerprintStatisticsResponse.  # noqa: E501


        :return: The port of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this GetDevFingerprintStatisticsResponse.


        :param port: The port of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def process(self):
        """Gets the process of this GetDevFingerprintStatisticsResponse.  # noqa: E501


        :return: The process of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._process

    @process.setter
    def process(self, process):
        """Sets the process of this GetDevFingerprintStatisticsResponse.


        :param process: The process of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._process = process

    @property
    def software(self):
        """Gets the software of this GetDevFingerprintStatisticsResponse.  # noqa: E501


        :return: The software of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._software

    @software.setter
    def software(self, software):
        """Sets the software of this GetDevFingerprintStatisticsResponse.


        :param software: The software of this GetDevFingerprintStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._software = software

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDevFingerprintStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDevFingerprintStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDevFingerprintStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
