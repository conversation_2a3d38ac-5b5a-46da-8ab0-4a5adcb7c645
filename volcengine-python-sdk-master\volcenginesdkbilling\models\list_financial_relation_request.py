# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListFinancialRelationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id_search_list': 'list[str]',
        'limit': 'int',
        'offset': 'int',
        'relation': 'list[str]',
        'status': 'list[str]'
    }

    attribute_map = {
        'account_id_search_list': 'AccountIDSearchList',
        'limit': 'Limit',
        'offset': 'Offset',
        'relation': 'Relation',
        'status': 'Status'
    }

    def __init__(self, account_id_search_list=None, limit=None, offset=None, relation=None, status=None, _configuration=None):  # noqa: E501
        """ListFinancialRelationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id_search_list = None
        self._limit = None
        self._offset = None
        self._relation = None
        self._status = None
        self.discriminator = None

        if account_id_search_list is not None:
            self.account_id_search_list = account_id_search_list
        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if relation is not None:
            self.relation = relation
        if status is not None:
            self.status = status

    @property
    def account_id_search_list(self):
        """Gets the account_id_search_list of this ListFinancialRelationRequest.  # noqa: E501


        :return: The account_id_search_list of this ListFinancialRelationRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._account_id_search_list

    @account_id_search_list.setter
    def account_id_search_list(self, account_id_search_list):
        """Sets the account_id_search_list of this ListFinancialRelationRequest.


        :param account_id_search_list: The account_id_search_list of this ListFinancialRelationRequest.  # noqa: E501
        :type: list[str]
        """

        self._account_id_search_list = account_id_search_list

    @property
    def limit(self):
        """Gets the limit of this ListFinancialRelationRequest.  # noqa: E501


        :return: The limit of this ListFinancialRelationRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListFinancialRelationRequest.


        :param limit: The limit of this ListFinancialRelationRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListFinancialRelationRequest.  # noqa: E501


        :return: The offset of this ListFinancialRelationRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListFinancialRelationRequest.


        :param offset: The offset of this ListFinancialRelationRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def relation(self):
        """Gets the relation of this ListFinancialRelationRequest.  # noqa: E501


        :return: The relation of this ListFinancialRelationRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._relation

    @relation.setter
    def relation(self, relation):
        """Sets the relation of this ListFinancialRelationRequest.


        :param relation: The relation of this ListFinancialRelationRequest.  # noqa: E501
        :type: list[str]
        """

        self._relation = relation

    @property
    def status(self):
        """Gets the status of this ListFinancialRelationRequest.  # noqa: E501


        :return: The status of this ListFinancialRelationRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListFinancialRelationRequest.


        :param status: The status of this ListFinancialRelationRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListFinancialRelationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListFinancialRelationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListFinancialRelationRequest):
            return True

        return self.to_dict() != other.to_dict()
