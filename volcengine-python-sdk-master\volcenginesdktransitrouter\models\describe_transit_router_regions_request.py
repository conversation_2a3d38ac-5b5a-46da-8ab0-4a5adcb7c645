# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTransitRouterRegionsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'geographic_region_set_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'region_ids': 'list[str]'
    }

    attribute_map = {
        'geographic_region_set_id': 'GeographicRegionSetId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'region_ids': 'RegionIds'
    }

    def __init__(self, geographic_region_set_id=None, page_number=None, page_size=None, region_ids=None, _configuration=None):  # noqa: E501
        """DescribeTransitRouterRegionsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._geographic_region_set_id = None
        self._page_number = None
        self._page_size = None
        self._region_ids = None
        self.discriminator = None

        if geographic_region_set_id is not None:
            self.geographic_region_set_id = geographic_region_set_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if region_ids is not None:
            self.region_ids = region_ids

    @property
    def geographic_region_set_id(self):
        """Gets the geographic_region_set_id of this DescribeTransitRouterRegionsRequest.  # noqa: E501


        :return: The geographic_region_set_id of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :rtype: str
        """
        return self._geographic_region_set_id

    @geographic_region_set_id.setter
    def geographic_region_set_id(self, geographic_region_set_id):
        """Sets the geographic_region_set_id of this DescribeTransitRouterRegionsRequest.


        :param geographic_region_set_id: The geographic_region_set_id of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :type: str
        """

        self._geographic_region_set_id = geographic_region_set_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTransitRouterRegionsRequest.  # noqa: E501


        :return: The page_number of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTransitRouterRegionsRequest.


        :param page_number: The page_number of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTransitRouterRegionsRequest.  # noqa: E501


        :return: The page_size of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTransitRouterRegionsRequest.


        :param page_size: The page_size of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def region_ids(self):
        """Gets the region_ids of this DescribeTransitRouterRegionsRequest.  # noqa: E501


        :return: The region_ids of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._region_ids

    @region_ids.setter
    def region_ids(self, region_ids):
        """Sets the region_ids of this DescribeTransitRouterRegionsRequest.


        :param region_ids: The region_ids of this DescribeTransitRouterRegionsRequest.  # noqa: E501
        :type: list[str]
        """

        self._region_ids = region_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTransitRouterRegionsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTransitRouterRegionsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTransitRouterRegionsRequest):
            return True

        return self.to_dict() != other.to_dict()
