# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiskInfoForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'disk_index': 'int',
        'disk_name': 'str',
        'disk_size': 'int',
        'file_system_format': 'str',
        'is_system_disk': 'bool',
        'partitions': 'list[PartitionForDescribeMigrationSourcesOutput]'
    }

    attribute_map = {
        'disk_index': 'DiskIndex',
        'disk_name': 'DiskName',
        'disk_size': 'DiskSize',
        'file_system_format': 'FileSystemFormat',
        'is_system_disk': 'IsSystemDisk',
        'partitions': 'Partitions'
    }

    def __init__(self, disk_index=None, disk_name=None, disk_size=None, file_system_format=None, is_system_disk=None, partitions=None, _configuration=None):  # noqa: E501
        """DiskInfoForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._disk_index = None
        self._disk_name = None
        self._disk_size = None
        self._file_system_format = None
        self._is_system_disk = None
        self._partitions = None
        self.discriminator = None

        if disk_index is not None:
            self.disk_index = disk_index
        if disk_name is not None:
            self.disk_name = disk_name
        if disk_size is not None:
            self.disk_size = disk_size
        if file_system_format is not None:
            self.file_system_format = file_system_format
        if is_system_disk is not None:
            self.is_system_disk = is_system_disk
        if partitions is not None:
            self.partitions = partitions

    @property
    def disk_index(self):
        """Gets the disk_index of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The disk_index of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._disk_index

    @disk_index.setter
    def disk_index(self, disk_index):
        """Sets the disk_index of this DiskInfoForDescribeMigrationSourcesOutput.


        :param disk_index: The disk_index of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._disk_index = disk_index

    @property
    def disk_name(self):
        """Gets the disk_name of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The disk_name of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._disk_name

    @disk_name.setter
    def disk_name(self, disk_name):
        """Sets the disk_name of this DiskInfoForDescribeMigrationSourcesOutput.


        :param disk_name: The disk_name of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._disk_name = disk_name

    @property
    def disk_size(self):
        """Gets the disk_size of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The disk_size of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._disk_size

    @disk_size.setter
    def disk_size(self, disk_size):
        """Sets the disk_size of this DiskInfoForDescribeMigrationSourcesOutput.


        :param disk_size: The disk_size of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._disk_size = disk_size

    @property
    def file_system_format(self):
        """Gets the file_system_format of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The file_system_format of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_format

    @file_system_format.setter
    def file_system_format(self, file_system_format):
        """Sets the file_system_format of this DiskInfoForDescribeMigrationSourcesOutput.


        :param file_system_format: The file_system_format of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_format = file_system_format

    @property
    def is_system_disk(self):
        """Gets the is_system_disk of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The is_system_disk of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_system_disk

    @is_system_disk.setter
    def is_system_disk(self, is_system_disk):
        """Sets the is_system_disk of this DiskInfoForDescribeMigrationSourcesOutput.


        :param is_system_disk: The is_system_disk of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: bool
        """

        self._is_system_disk = is_system_disk

    @property
    def partitions(self):
        """Gets the partitions of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The partitions of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[PartitionForDescribeMigrationSourcesOutput]
        """
        return self._partitions

    @partitions.setter
    def partitions(self, partitions):
        """Sets the partitions of this DiskInfoForDescribeMigrationSourcesOutput.


        :param partitions: The partitions of this DiskInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[PartitionForDescribeMigrationSourcesOutput]
        """

        self._partitions = partitions

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiskInfoForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiskInfoForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiskInfoForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
