# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProjectRoleForListProjectIdentitiesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'display_name': 'str',
        'policy': 'list[PolicyForListProjectIdentitiesOutput]',
        'role_name': 'str',
        'update_date': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'display_name': 'DisplayName',
        'policy': 'Policy',
        'role_name': 'RoleName',
        'update_date': 'UpdateDate'
    }

    def __init__(self, description=None, display_name=None, policy=None, role_name=None, update_date=None, _configuration=None):  # noqa: E501
        """ProjectRoleForListProjectIdentitiesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._display_name = None
        self._policy = None
        self._role_name = None
        self._update_date = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if display_name is not None:
            self.display_name = display_name
        if policy is not None:
            self.policy = policy
        if role_name is not None:
            self.role_name = role_name
        if update_date is not None:
            self.update_date = update_date

    @property
    def description(self):
        """Gets the description of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501


        :return: The description of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ProjectRoleForListProjectIdentitiesOutput.


        :param description: The description of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def display_name(self):
        """Gets the display_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501


        :return: The display_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this ProjectRoleForListProjectIdentitiesOutput.


        :param display_name: The display_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def policy(self):
        """Gets the policy of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501


        :return: The policy of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :rtype: list[PolicyForListProjectIdentitiesOutput]
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this ProjectRoleForListProjectIdentitiesOutput.


        :param policy: The policy of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :type: list[PolicyForListProjectIdentitiesOutput]
        """

        self._policy = policy

    @property
    def role_name(self):
        """Gets the role_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501


        :return: The role_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this ProjectRoleForListProjectIdentitiesOutput.


        :param role_name: The role_name of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :type: str
        """

        self._role_name = role_name

    @property
    def update_date(self):
        """Gets the update_date of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501


        :return: The update_date of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this ProjectRoleForListProjectIdentitiesOutput.


        :param update_date: The update_date of this ProjectRoleForListProjectIdentitiesOutput.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProjectRoleForListProjectIdentitiesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProjectRoleForListProjectIdentitiesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProjectRoleForListProjectIdentitiesOutput):
            return True

        return self.to_dict() != other.to_dict()
