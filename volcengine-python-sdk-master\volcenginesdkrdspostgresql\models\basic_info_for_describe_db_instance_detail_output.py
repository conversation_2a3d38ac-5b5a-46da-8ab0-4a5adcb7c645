# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicInfoForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'db_engine_version': 'str',
        'data_sync_mode': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_tag': 'list[InstanceTagForDescribeDBInstanceDetailOutput]',
        'instance_type': 'str',
        'memory': 'int',
        'node_number': 'str',
        'node_spec': 'str',
        'project_name': 'str',
        'region_id': 'str',
        'storage_data_use': 'int',
        'storage_log_use': 'int',
        'storage_space': 'int',
        'storage_temp_use': 'int',
        'storage_type': 'str',
        'storage_use': 'int',
        'storage_wal_use': 'int',
        'subnet_id': 'str',
        'update_time': 'str',
        'vcpu': 'int',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'db_engine_version': 'DBEngineVersion',
        'data_sync_mode': 'DataSyncMode',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_tag': 'InstanceTag',
        'instance_type': 'InstanceType',
        'memory': 'Memory',
        'node_number': 'NodeNumber',
        'node_spec': 'NodeSpec',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'storage_data_use': 'StorageDataUse',
        'storage_log_use': 'StorageLogUse',
        'storage_space': 'StorageSpace',
        'storage_temp_use': 'StorageTempUse',
        'storage_type': 'StorageType',
        'storage_use': 'StorageUse',
        'storage_wal_use': 'StorageWALUse',
        'subnet_id': 'SubnetId',
        'update_time': 'UpdateTime',
        'vcpu': 'VCPU',
        'vpc_id': 'VpcID',
        'zone_id': 'ZoneId'
    }

    def __init__(self, create_time=None, db_engine_version=None, data_sync_mode=None, instance_id=None, instance_name=None, instance_status=None, instance_tag=None, instance_type=None, memory=None, node_number=None, node_spec=None, project_name=None, region_id=None, storage_data_use=None, storage_log_use=None, storage_space=None, storage_temp_use=None, storage_type=None, storage_use=None, storage_wal_use=None, subnet_id=None, update_time=None, vcpu=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """BasicInfoForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._db_engine_version = None
        self._data_sync_mode = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_tag = None
        self._instance_type = None
        self._memory = None
        self._node_number = None
        self._node_spec = None
        self._project_name = None
        self._region_id = None
        self._storage_data_use = None
        self._storage_log_use = None
        self._storage_space = None
        self._storage_temp_use = None
        self._storage_type = None
        self._storage_use = None
        self._storage_wal_use = None
        self._subnet_id = None
        self._update_time = None
        self._vcpu = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if data_sync_mode is not None:
            self.data_sync_mode = data_sync_mode
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_tag is not None:
            self.instance_tag = instance_tag
        if instance_type is not None:
            self.instance_type = instance_type
        if memory is not None:
            self.memory = memory
        if node_number is not None:
            self.node_number = node_number
        if node_spec is not None:
            self.node_spec = node_spec
        if project_name is not None:
            self.project_name = project_name
        if region_id is not None:
            self.region_id = region_id
        if storage_data_use is not None:
            self.storage_data_use = storage_data_use
        if storage_log_use is not None:
            self.storage_log_use = storage_log_use
        if storage_space is not None:
            self.storage_space = storage_space
        if storage_temp_use is not None:
            self.storage_temp_use = storage_temp_use
        if storage_type is not None:
            self.storage_type = storage_type
        if storage_use is not None:
            self.storage_use = storage_use
        if storage_wal_use is not None:
            self.storage_wal_use = storage_wal_use
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if update_time is not None:
            self.update_time = update_time
        if vcpu is not None:
            self.vcpu = vcpu
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def create_time(self):
        """Gets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param db_engine_version: The db_engine_version of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def data_sync_mode(self):
        """Gets the data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_sync_mode

    @data_sync_mode.setter
    def data_sync_mode(self, data_sync_mode):
        """Sets the data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param data_sync_mode: The data_sync_mode of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._data_sync_mode = data_sync_mode

    @property
    def instance_id(self):
        """Gets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_status: The instance_status of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_tag(self):
        """Gets the instance_tag of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_tag of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[InstanceTagForDescribeDBInstanceDetailOutput]
        """
        return self._instance_tag

    @instance_tag.setter
    def instance_tag(self, instance_tag):
        """Sets the instance_tag of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_tag: The instance_tag of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[InstanceTagForDescribeDBInstanceDetailOutput]
        """

        self._instance_tag = instance_tag

    @property
    def instance_type(self):
        """Gets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param instance_type: The instance_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def memory(self):
        """Gets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param memory: The memory of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def node_number(self):
        """Gets the node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_number

    @node_number.setter
    def node_number(self, node_number):
        """Sets the node_number of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param node_number: The node_number of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_number = node_number

    @property
    def node_spec(self):
        """Gets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param node_spec: The node_spec of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_spec = node_spec

    @property
    def project_name(self):
        """Gets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param project_name: The project_name of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param region_id: The region_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def storage_data_use(self):
        """Gets the storage_data_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_data_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_data_use

    @storage_data_use.setter
    def storage_data_use(self, storage_data_use):
        """Sets the storage_data_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_data_use: The storage_data_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_data_use = storage_data_use

    @property
    def storage_log_use(self):
        """Gets the storage_log_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_log_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_log_use

    @storage_log_use.setter
    def storage_log_use(self, storage_log_use):
        """Sets the storage_log_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_log_use: The storage_log_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_log_use = storage_log_use

    @property
    def storage_space(self):
        """Gets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_space: The storage_space of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_temp_use(self):
        """Gets the storage_temp_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_temp_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_temp_use

    @storage_temp_use.setter
    def storage_temp_use(self, storage_temp_use):
        """Sets the storage_temp_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_temp_use: The storage_temp_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_temp_use = storage_temp_use

    @property
    def storage_type(self):
        """Gets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_type: The storage_type of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    @property
    def storage_use(self):
        """Gets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_use

    @storage_use.setter
    def storage_use(self, storage_use):
        """Sets the storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_use: The storage_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_use = storage_use

    @property
    def storage_wal_use(self):
        """Gets the storage_wal_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_wal_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_wal_use

    @storage_wal_use.setter
    def storage_wal_use(self, storage_wal_use):
        """Sets the storage_wal_use of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param storage_wal_use: The storage_wal_use of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._storage_wal_use = storage_wal_use

    @property
    def subnet_id(self):
        """Gets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param subnet_id: The subnet_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def update_time(self):
        """Gets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param update_time: The update_time of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vcpu(self):
        """Gets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vcpu: The vcpu of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def vpc_id(self):
        """Gets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param vpc_id: The vpc_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.


        :param zone_id: The zone_id of this BasicInfoForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicInfoForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicInfoForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
