# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DetailForUninstallAgentClientOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'code': 'int',
        'msg': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'code': 'Code',
        'msg': 'Msg'
    }

    def __init__(self, agent_id=None, code=None, msg=None, _configuration=None):  # noqa: E501
        """DetailForUninstallAgentClientOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._code = None
        self._msg = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if code is not None:
            self.code = code
        if msg is not None:
            self.msg = msg

    @property
    def agent_id(self):
        """Gets the agent_id of this DetailForUninstallAgentClientOutput.  # noqa: E501


        :return: The agent_id of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DetailForUninstallAgentClientOutput.


        :param agent_id: The agent_id of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def code(self):
        """Gets the code of this DetailForUninstallAgentClientOutput.  # noqa: E501


        :return: The code of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :rtype: int
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this DetailForUninstallAgentClientOutput.


        :param code: The code of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :type: int
        """

        self._code = code

    @property
    def msg(self):
        """Gets the msg of this DetailForUninstallAgentClientOutput.  # noqa: E501


        :return: The msg of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :rtype: str
        """
        return self._msg

    @msg.setter
    def msg(self, msg):
        """Sets the msg of this DetailForUninstallAgentClientOutput.


        :param msg: The msg of this DetailForUninstallAgentClientOutput.  # noqa: E501
        :type: str
        """

        self._msg = msg

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DetailForUninstallAgentClientOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DetailForUninstallAgentClientOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DetailForUninstallAgentClientOutput):
            return True

        return self.to_dict() != other.to_dict()
