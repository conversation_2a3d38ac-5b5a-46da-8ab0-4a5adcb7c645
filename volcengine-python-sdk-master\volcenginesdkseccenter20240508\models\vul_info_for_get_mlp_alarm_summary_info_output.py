# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VulInfoForGetMlpAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cve': 'str',
        'pid': 'str',
        'title_cn': 'str',
        'vuln_list': 'list[VulnListForGetMlpAlarmSummaryInfoOutput]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cve': 'Cve',
        'pid': 'Pid',
        'title_cn': 'TitleCn',
        'vuln_list': 'VulnList'
    }

    def __init__(self, agent_id=None, cve=None, pid=None, title_cn=None, vuln_list=None, _configuration=None):  # noqa: E501
        """VulInfoForGetMlpAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cve = None
        self._pid = None
        self._title_cn = None
        self._vuln_list = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cve is not None:
            self.cve = cve
        if pid is not None:
            self.pid = pid
        if title_cn is not None:
            self.title_cn = title_cn
        if vuln_list is not None:
            self.vuln_list = vuln_list

    @property
    def agent_id(self):
        """Gets the agent_id of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The agent_id of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this VulInfoForGetMlpAlarmSummaryInfoOutput.


        :param agent_id: The agent_id of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cve(self):
        """Gets the cve of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cve of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve

    @cve.setter
    def cve(self, cve):
        """Sets the cve of this VulInfoForGetMlpAlarmSummaryInfoOutput.


        :param cve: The cve of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._cve = cve

    @property
    def pid(self):
        """Gets the pid of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this VulInfoForGetMlpAlarmSummaryInfoOutput.


        :param pid: The pid of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def title_cn(self):
        """Gets the title_cn of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The title_cn of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._title_cn

    @title_cn.setter
    def title_cn(self, title_cn):
        """Sets the title_cn of this VulInfoForGetMlpAlarmSummaryInfoOutput.


        :param title_cn: The title_cn of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._title_cn = title_cn

    @property
    def vuln_list(self):
        """Gets the vuln_list of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The vuln_list of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[VulnListForGetMlpAlarmSummaryInfoOutput]
        """
        return self._vuln_list

    @vuln_list.setter
    def vuln_list(self, vuln_list):
        """Sets the vuln_list of this VulInfoForGetMlpAlarmSummaryInfoOutput.


        :param vuln_list: The vuln_list of this VulInfoForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[VulnListForGetMlpAlarmSummaryInfoOutput]
        """

        self._vuln_list = vuln_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VulInfoForGetMlpAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VulInfoForGetMlpAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VulInfoForGetMlpAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
