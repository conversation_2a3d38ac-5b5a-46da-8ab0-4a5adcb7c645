# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubInstanceForDescribeInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sub_instance_id': 'str',
        'sub_instance_status': 'str',
        'sub_instance_type': 'str'
    }

    attribute_map = {
        'sub_instance_id': 'SubInstanceId',
        'sub_instance_status': 'SubInstanceStatus',
        'sub_instance_type': 'SubInstanceType'
    }

    def __init__(self, sub_instance_id=None, sub_instance_status=None, sub_instance_type=None, _configuration=None):  # noqa: E501
        """SubInstanceForDescribeInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sub_instance_id = None
        self._sub_instance_status = None
        self._sub_instance_type = None
        self.discriminator = None

        if sub_instance_id is not None:
            self.sub_instance_id = sub_instance_id
        if sub_instance_status is not None:
            self.sub_instance_status = sub_instance_status
        if sub_instance_type is not None:
            self.sub_instance_type = sub_instance_type

    @property
    def sub_instance_id(self):
        """Gets the sub_instance_id of this SubInstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The sub_instance_id of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_instance_id

    @sub_instance_id.setter
    def sub_instance_id(self, sub_instance_id):
        """Sets the sub_instance_id of this SubInstanceForDescribeInstancesOutput.


        :param sub_instance_id: The sub_instance_id of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._sub_instance_id = sub_instance_id

    @property
    def sub_instance_status(self):
        """Gets the sub_instance_status of this SubInstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The sub_instance_status of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_instance_status

    @sub_instance_status.setter
    def sub_instance_status(self, sub_instance_status):
        """Sets the sub_instance_status of this SubInstanceForDescribeInstancesOutput.


        :param sub_instance_status: The sub_instance_status of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._sub_instance_status = sub_instance_status

    @property
    def sub_instance_type(self):
        """Gets the sub_instance_type of this SubInstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The sub_instance_type of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_instance_type

    @sub_instance_type.setter
    def sub_instance_type(self, sub_instance_type):
        """Sets the sub_instance_type of this SubInstanceForDescribeInstancesOutput.


        :param sub_instance_type: The sub_instance_type of this SubInstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._sub_instance_type = sub_instance_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubInstanceForDescribeInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubInstanceForDescribeInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubInstanceForDescribeInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
