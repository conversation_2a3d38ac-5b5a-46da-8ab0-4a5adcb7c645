# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetFingerprintRefreshStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cooldown_time': 'int',
        'percent': 'int',
        'status': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'cooldown_time': 'CooldownTime',
        'percent': 'Percent',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, cooldown_time=None, percent=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """GetFingerprintRefreshStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cooldown_time = None
        self._percent = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if cooldown_time is not None:
            self.cooldown_time = cooldown_time
        if percent is not None:
            self.percent = percent
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def cooldown_time(self):
        """Gets the cooldown_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501


        :return: The cooldown_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._cooldown_time

    @cooldown_time.setter
    def cooldown_time(self, cooldown_time):
        """Sets the cooldown_time of this GetFingerprintRefreshStatusResponse.


        :param cooldown_time: The cooldown_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :type: int
        """

        self._cooldown_time = cooldown_time

    @property
    def percent(self):
        """Gets the percent of this GetFingerprintRefreshStatusResponse.  # noqa: E501


        :return: The percent of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._percent

    @percent.setter
    def percent(self, percent):
        """Sets the percent of this GetFingerprintRefreshStatusResponse.


        :param percent: The percent of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :type: int
        """

        self._percent = percent

    @property
    def status(self):
        """Gets the status of this GetFingerprintRefreshStatusResponse.  # noqa: E501


        :return: The status of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetFingerprintRefreshStatusResponse.


        :param status: The status of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501


        :return: The update_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetFingerprintRefreshStatusResponse.


        :param update_time: The update_time of this GetFingerprintRefreshStatusResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetFingerprintRefreshStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetFingerprintRefreshStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetFingerprintRefreshStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
