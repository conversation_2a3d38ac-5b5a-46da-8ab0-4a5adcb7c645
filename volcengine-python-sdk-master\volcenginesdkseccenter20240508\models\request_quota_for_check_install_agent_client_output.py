# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RequestQuotaForCheckInstallAgentClientOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'core': 'int',
        'hosts_count': 'int'
    }

    attribute_map = {
        'core': 'Core',
        'hosts_count': 'HostsCount'
    }

    def __init__(self, core=None, hosts_count=None, _configuration=None):  # noqa: E501
        """RequestQuotaForCheckInstallAgentClientOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._core = None
        self._hosts_count = None
        self.discriminator = None

        if core is not None:
            self.core = core
        if hosts_count is not None:
            self.hosts_count = hosts_count

    @property
    def core(self):
        """Gets the core of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501


        :return: The core of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501
        :rtype: int
        """
        return self._core

    @core.setter
    def core(self, core):
        """Sets the core of this RequestQuotaForCheckInstallAgentClientOutput.


        :param core: The core of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501
        :type: int
        """

        self._core = core

    @property
    def hosts_count(self):
        """Gets the hosts_count of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501


        :return: The hosts_count of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501
        :rtype: int
        """
        return self._hosts_count

    @hosts_count.setter
    def hosts_count(self, hosts_count):
        """Sets the hosts_count of this RequestQuotaForCheckInstallAgentClientOutput.


        :param hosts_count: The hosts_count of this RequestQuotaForCheckInstallAgentClientOutput.  # noqa: E501
        :type: int
        """

        self._hosts_count = hosts_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RequestQuotaForCheckInstallAgentClientOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RequestQuotaForCheckInstallAgentClientOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RequestQuotaForCheckInstallAgentClientOutput):
            return True

        return self.to_dict() != other.to_dict()
