# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListSubmissionsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'display_level': 'str',
        'ids': 'list[str]',
        'keyword': 'str',
        'status': 'list[str]',
        'workflow_id': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'display_level': 'DisplayLevel',
        'ids': 'IDs',
        'keyword': 'Keyword',
        'status': 'Status',
        'workflow_id': 'WorkflowID'
    }

    def __init__(self, cluster_id=None, display_level=None, ids=None, keyword=None, status=None, workflow_id=None, _configuration=None):  # noqa: E501
        """FilterForListSubmissionsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._display_level = None
        self._ids = None
        self._keyword = None
        self._status = None
        self._workflow_id = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if display_level is not None:
            self.display_level = display_level
        if ids is not None:
            self.ids = ids
        if keyword is not None:
            self.keyword = keyword
        if status is not None:
            self.status = status
        if workflow_id is not None:
            self.workflow_id = workflow_id

    @property
    def cluster_id(self):
        """Gets the cluster_id of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The cluster_id of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this FilterForListSubmissionsInput.


        :param cluster_id: The cluster_id of this FilterForListSubmissionsInput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def display_level(self):
        """Gets the display_level of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The display_level of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._display_level

    @display_level.setter
    def display_level(self, display_level):
        """Sets the display_level of this FilterForListSubmissionsInput.


        :param display_level: The display_level of this FilterForListSubmissionsInput.  # noqa: E501
        :type: str
        """

        self._display_level = display_level

    @property
    def ids(self):
        """Gets the ids of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The ids of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListSubmissionsInput.


        :param ids: The ids of this FilterForListSubmissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def keyword(self):
        """Gets the keyword of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The keyword of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._keyword

    @keyword.setter
    def keyword(self, keyword):
        """Sets the keyword of this FilterForListSubmissionsInput.


        :param keyword: The keyword of this FilterForListSubmissionsInput.  # noqa: E501
        :type: str
        """

        self._keyword = keyword

    @property
    def status(self):
        """Gets the status of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The status of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListSubmissionsInput.


        :param status: The status of this FilterForListSubmissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def workflow_id(self):
        """Gets the workflow_id of this FilterForListSubmissionsInput.  # noqa: E501


        :return: The workflow_id of this FilterForListSubmissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, workflow_id):
        """Sets the workflow_id of this FilterForListSubmissionsInput.


        :param workflow_id: The workflow_id of this FilterForListSubmissionsInput.  # noqa: E501
        :type: str
        """

        self._workflow_id = workflow_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListSubmissionsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListSubmissionsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListSubmissionsInput):
            return True

        return self.to_dict() != other.to_dict()
