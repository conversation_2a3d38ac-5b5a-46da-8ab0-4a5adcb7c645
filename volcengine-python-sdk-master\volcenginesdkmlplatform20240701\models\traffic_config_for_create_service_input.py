# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TrafficConfigForCreateServiceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enables': 'list[str]',
        'mirror_traffic_policy': 'MirrorTrafficPolicyForCreateServiceInput',
        'mode': 'str',
        'weights': 'list[ConvertWeightForCreateServiceInput]'
    }

    attribute_map = {
        'enables': 'Enables',
        'mirror_traffic_policy': 'MirrorTrafficPolicy',
        'mode': 'Mode',
        'weights': 'Weights'
    }

    def __init__(self, enables=None, mirror_traffic_policy=None, mode=None, weights=None, _configuration=None):  # noqa: E501
        """TrafficConfigForCreateServiceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enables = None
        self._mirror_traffic_policy = None
        self._mode = None
        self._weights = None
        self.discriminator = None

        if enables is not None:
            self.enables = enables
        if mirror_traffic_policy is not None:
            self.mirror_traffic_policy = mirror_traffic_policy
        if mode is not None:
            self.mode = mode
        if weights is not None:
            self.weights = weights

    @property
    def enables(self):
        """Gets the enables of this TrafficConfigForCreateServiceInput.  # noqa: E501


        :return: The enables of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._enables

    @enables.setter
    def enables(self, enables):
        """Sets the enables of this TrafficConfigForCreateServiceInput.


        :param enables: The enables of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :type: list[str]
        """

        self._enables = enables

    @property
    def mirror_traffic_policy(self):
        """Gets the mirror_traffic_policy of this TrafficConfigForCreateServiceInput.  # noqa: E501


        :return: The mirror_traffic_policy of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :rtype: MirrorTrafficPolicyForCreateServiceInput
        """
        return self._mirror_traffic_policy

    @mirror_traffic_policy.setter
    def mirror_traffic_policy(self, mirror_traffic_policy):
        """Sets the mirror_traffic_policy of this TrafficConfigForCreateServiceInput.


        :param mirror_traffic_policy: The mirror_traffic_policy of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :type: MirrorTrafficPolicyForCreateServiceInput
        """

        self._mirror_traffic_policy = mirror_traffic_policy

    @property
    def mode(self):
        """Gets the mode of this TrafficConfigForCreateServiceInput.  # noqa: E501


        :return: The mode of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this TrafficConfigForCreateServiceInput.


        :param mode: The mode of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Default", "Weight"]  # noqa: E501
        if (self._configuration.client_side_validation and
                mode not in allowed_values):
            raise ValueError(
                "Invalid value for `mode` ({0}), must be one of {1}"  # noqa: E501
                .format(mode, allowed_values)
            )

        self._mode = mode

    @property
    def weights(self):
        """Gets the weights of this TrafficConfigForCreateServiceInput.  # noqa: E501


        :return: The weights of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :rtype: list[ConvertWeightForCreateServiceInput]
        """
        return self._weights

    @weights.setter
    def weights(self, weights):
        """Sets the weights of this TrafficConfigForCreateServiceInput.


        :param weights: The weights of this TrafficConfigForCreateServiceInput.  # noqa: E501
        :type: list[ConvertWeightForCreateServiceInput]
        """

        self._weights = weights

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrafficConfigForCreateServiceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrafficConfigForCreateServiceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TrafficConfigForCreateServiceInput):
            return True

        return self.to_dict() != other.to_dict()
