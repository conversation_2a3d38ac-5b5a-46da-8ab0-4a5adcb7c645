# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBaselinesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'baseline_name': 'str',
        'cloud_providers': 'list[str]',
        'cluster_id': 'str',
        'group_id': 'int',
        'last_detected_time_end': 'int',
        'last_detected_time_start': 'int',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'baseline_name': 'BaselineName',
        'cloud_providers': 'CloudProviders',
        'cluster_id': 'ClusterID',
        'group_id': 'GroupID',
        'last_detected_time_end': 'LastDetectedTimeEnd',
        'last_detected_time_start': 'LastDetectedTimeStart',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, baseline_name=None, cloud_providers=None, cluster_id=None, group_id=None, last_detected_time_end=None, last_detected_time_start=None, leaf_group_ids=None, page_number=None, page_size=None, sort_by=None, sort_order=None, status=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListBaselinesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._baseline_name = None
        self._cloud_providers = None
        self._cluster_id = None
        self._group_id = None
        self._last_detected_time_end = None
        self._last_detected_time_start = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        if baseline_name is not None:
            self.baseline_name = baseline_name
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if group_id is not None:
            self.group_id = group_id
        if last_detected_time_end is not None:
            self.last_detected_time_end = last_detected_time_end
        if last_detected_time_start is not None:
            self.last_detected_time_start = last_detected_time_start
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ListBaselinesRequest.  # noqa: E501


        :return: The agent_id of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListBaselinesRequest.


        :param agent_id: The agent_id of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this ListBaselinesRequest.  # noqa: E501


        :return: The asset_id of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListBaselinesRequest.


        :param asset_id: The asset_id of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this ListBaselinesRequest.  # noqa: E501


        :return: The asset_type of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this ListBaselinesRequest.


        :param asset_type: The asset_type of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def baseline_name(self):
        """Gets the baseline_name of this ListBaselinesRequest.  # noqa: E501


        :return: The baseline_name of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._baseline_name

    @baseline_name.setter
    def baseline_name(self, baseline_name):
        """Sets the baseline_name of this ListBaselinesRequest.


        :param baseline_name: The baseline_name of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._baseline_name = baseline_name

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListBaselinesRequest.  # noqa: E501


        :return: The cloud_providers of this ListBaselinesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListBaselinesRequest.


        :param cloud_providers: The cloud_providers of this ListBaselinesRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListBaselinesRequest.  # noqa: E501


        :return: The cluster_id of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListBaselinesRequest.


        :param cluster_id: The cluster_id of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def group_id(self):
        """Gets the group_id of this ListBaselinesRequest.  # noqa: E501


        :return: The group_id of this ListBaselinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this ListBaselinesRequest.


        :param group_id: The group_id of this ListBaselinesRequest.  # noqa: E501
        :type: int
        """

        self._group_id = group_id

    @property
    def last_detected_time_end(self):
        """Gets the last_detected_time_end of this ListBaselinesRequest.  # noqa: E501


        :return: The last_detected_time_end of this ListBaselinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._last_detected_time_end

    @last_detected_time_end.setter
    def last_detected_time_end(self, last_detected_time_end):
        """Sets the last_detected_time_end of this ListBaselinesRequest.


        :param last_detected_time_end: The last_detected_time_end of this ListBaselinesRequest.  # noqa: E501
        :type: int
        """

        self._last_detected_time_end = last_detected_time_end

    @property
    def last_detected_time_start(self):
        """Gets the last_detected_time_start of this ListBaselinesRequest.  # noqa: E501


        :return: The last_detected_time_start of this ListBaselinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._last_detected_time_start

    @last_detected_time_start.setter
    def last_detected_time_start(self, last_detected_time_start):
        """Sets the last_detected_time_start of this ListBaselinesRequest.


        :param last_detected_time_start: The last_detected_time_start of this ListBaselinesRequest.  # noqa: E501
        :type: int
        """

        self._last_detected_time_start = last_detected_time_start

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListBaselinesRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListBaselinesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListBaselinesRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListBaselinesRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this ListBaselinesRequest.  # noqa: E501


        :return: The page_number of this ListBaselinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListBaselinesRequest.


        :param page_number: The page_number of this ListBaselinesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListBaselinesRequest.  # noqa: E501


        :return: The page_size of this ListBaselinesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListBaselinesRequest.


        :param page_size: The page_size of this ListBaselinesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListBaselinesRequest.  # noqa: E501


        :return: The sort_by of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListBaselinesRequest.


        :param sort_by: The sort_by of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListBaselinesRequest.  # noqa: E501


        :return: The sort_order of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListBaselinesRequest.


        :param sort_order: The sort_order of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListBaselinesRequest.  # noqa: E501


        :return: The status of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListBaselinesRequest.


        :param status: The status of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListBaselinesRequest.  # noqa: E501


        :return: The top_group_id of this ListBaselinesRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListBaselinesRequest.


        :param top_group_id: The top_group_id of this ListBaselinesRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBaselinesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBaselinesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBaselinesRequest):
            return True

        return self.to_dict() != other.to_dict()
