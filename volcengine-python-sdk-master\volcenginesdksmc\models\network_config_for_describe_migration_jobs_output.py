# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkConfigForDescribeMigrationJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_limit': 'int',
        'network_transition_mode': 'str',
        'subnet_id': 'str',
        'transfer_ip': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'bandwidth_limit': 'BandwidthLimit',
        'network_transition_mode': 'NetworkTransitionMode',
        'subnet_id': 'SubnetId',
        'transfer_ip': 'TransferIP',
        'vpc_id': 'VpcId'
    }

    def __init__(self, bandwidth_limit=None, network_transition_mode=None, subnet_id=None, transfer_ip=None, vpc_id=None, _configuration=None):  # noqa: E501
        """NetworkConfigForDescribeMigrationJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_limit = None
        self._network_transition_mode = None
        self._subnet_id = None
        self._transfer_ip = None
        self._vpc_id = None
        self.discriminator = None

        if bandwidth_limit is not None:
            self.bandwidth_limit = bandwidth_limit
        if network_transition_mode is not None:
            self.network_transition_mode = network_transition_mode
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if transfer_ip is not None:
            self.transfer_ip = transfer_ip
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def bandwidth_limit(self):
        """Gets the bandwidth_limit of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The bandwidth_limit of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_limit

    @bandwidth_limit.setter
    def bandwidth_limit(self, bandwidth_limit):
        """Sets the bandwidth_limit of this NetworkConfigForDescribeMigrationJobsOutput.


        :param bandwidth_limit: The bandwidth_limit of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth_limit = bandwidth_limit

    @property
    def network_transition_mode(self):
        """Gets the network_transition_mode of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The network_transition_mode of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_transition_mode

    @network_transition_mode.setter
    def network_transition_mode(self, network_transition_mode):
        """Sets the network_transition_mode of this NetworkConfigForDescribeMigrationJobsOutput.


        :param network_transition_mode: The network_transition_mode of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._network_transition_mode = network_transition_mode

    @property
    def subnet_id(self):
        """Gets the subnet_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The subnet_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this NetworkConfigForDescribeMigrationJobsOutput.


        :param subnet_id: The subnet_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def transfer_ip(self):
        """Gets the transfer_ip of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The transfer_ip of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._transfer_ip

    @transfer_ip.setter
    def transfer_ip(self, transfer_ip):
        """Sets the transfer_ip of this NetworkConfigForDescribeMigrationJobsOutput.


        :param transfer_ip: The transfer_ip of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._transfer_ip = transfer_ip

    @property
    def vpc_id(self):
        """Gets the vpc_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The vpc_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this NetworkConfigForDescribeMigrationJobsOutput.


        :param vpc_id: The vpc_id of this NetworkConfigForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkConfigForDescribeMigrationJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkConfigForDescribeMigrationJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkConfigForDescribeMigrationJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
