# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListScanTasksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'list[str]',
        'file_path': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'task_name': 'str',
        'task_status': 'list[str]',
        'task_user': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'file_path': 'FilePath',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'task_name': 'TaskName',
        'task_status': 'TaskStatus',
        'task_user': 'TaskUser',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, action=None, file_path=None, page_number=None, page_size=None, sort_by=None, sort_order=None, task_name=None, task_status=None, task_user=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListScanTasksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._file_path = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._task_name = None
        self._task_status = None
        self._task_user = None
        self._top_group_id = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if file_path is not None:
            self.file_path = file_path
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if task_name is not None:
            self.task_name = task_name
        if task_status is not None:
            self.task_status = task_status
        if task_user is not None:
            self.task_user = task_user
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def action(self):
        """Gets the action of this ListScanTasksRequest.  # noqa: E501


        :return: The action of this ListScanTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this ListScanTasksRequest.


        :param action: The action of this ListScanTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._action = action

    @property
    def file_path(self):
        """Gets the file_path of this ListScanTasksRequest.  # noqa: E501


        :return: The file_path of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this ListScanTasksRequest.


        :param file_path: The file_path of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def page_number(self):
        """Gets the page_number of this ListScanTasksRequest.  # noqa: E501


        :return: The page_number of this ListScanTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListScanTasksRequest.


        :param page_number: The page_number of this ListScanTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListScanTasksRequest.  # noqa: E501


        :return: The page_size of this ListScanTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListScanTasksRequest.


        :param page_size: The page_size of this ListScanTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListScanTasksRequest.  # noqa: E501


        :return: The sort_by of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListScanTasksRequest.


        :param sort_by: The sort_by of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListScanTasksRequest.  # noqa: E501


        :return: The sort_order of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListScanTasksRequest.


        :param sort_order: The sort_order of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def task_name(self):
        """Gets the task_name of this ListScanTasksRequest.  # noqa: E501


        :return: The task_name of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this ListScanTasksRequest.


        :param task_name: The task_name of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    @property
    def task_status(self):
        """Gets the task_status of this ListScanTasksRequest.  # noqa: E501


        :return: The task_status of this ListScanTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this ListScanTasksRequest.


        :param task_status: The task_status of this ListScanTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._task_status = task_status

    @property
    def task_user(self):
        """Gets the task_user of this ListScanTasksRequest.  # noqa: E501


        :return: The task_user of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_user

    @task_user.setter
    def task_user(self, task_user):
        """Sets the task_user of this ListScanTasksRequest.


        :param task_user: The task_user of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._task_user = task_user

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListScanTasksRequest.  # noqa: E501


        :return: The top_group_id of this ListScanTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListScanTasksRequest.


        :param top_group_id: The top_group_id of this ListScanTasksRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListScanTasksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListScanTasksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListScanTasksRequest):
            return True

        return self.to_dict() != other.to_dict()
