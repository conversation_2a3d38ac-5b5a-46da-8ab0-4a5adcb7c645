# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListProjectIdentitiesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'project_roles': 'list[ProjectRoleForListProjectIdentitiesOutput]',
        'project_user_groups': 'list[ProjectUserGroupForListProjectIdentitiesOutput]',
        'project_users': 'list[ProjectUserForListProjectIdentitiesOutput]',
        'total': 'int'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'project_roles': 'ProjectRoles',
        'project_user_groups': 'ProjectUserGroups',
        'project_users': 'ProjectUsers',
        'total': 'Total'
    }

    def __init__(self, limit=None, offset=None, project_roles=None, project_user_groups=None, project_users=None, total=None, _configuration=None):  # noqa: E501
        """ListProjectIdentitiesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._project_roles = None
        self._project_user_groups = None
        self._project_users = None
        self._total = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if project_roles is not None:
            self.project_roles = project_roles
        if project_user_groups is not None:
            self.project_user_groups = project_user_groups
        if project_users is not None:
            self.project_users = project_users
        if total is not None:
            self.total = total

    @property
    def limit(self):
        """Gets the limit of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The limit of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListProjectIdentitiesResponse.


        :param limit: The limit of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The offset of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListProjectIdentitiesResponse.


        :param offset: The offset of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def project_roles(self):
        """Gets the project_roles of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The project_roles of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: list[ProjectRoleForListProjectIdentitiesOutput]
        """
        return self._project_roles

    @project_roles.setter
    def project_roles(self, project_roles):
        """Sets the project_roles of this ListProjectIdentitiesResponse.


        :param project_roles: The project_roles of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: list[ProjectRoleForListProjectIdentitiesOutput]
        """

        self._project_roles = project_roles

    @property
    def project_user_groups(self):
        """Gets the project_user_groups of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The project_user_groups of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: list[ProjectUserGroupForListProjectIdentitiesOutput]
        """
        return self._project_user_groups

    @project_user_groups.setter
    def project_user_groups(self, project_user_groups):
        """Sets the project_user_groups of this ListProjectIdentitiesResponse.


        :param project_user_groups: The project_user_groups of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: list[ProjectUserGroupForListProjectIdentitiesOutput]
        """

        self._project_user_groups = project_user_groups

    @property
    def project_users(self):
        """Gets the project_users of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The project_users of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: list[ProjectUserForListProjectIdentitiesOutput]
        """
        return self._project_users

    @project_users.setter
    def project_users(self, project_users):
        """Sets the project_users of this ListProjectIdentitiesResponse.


        :param project_users: The project_users of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: list[ProjectUserForListProjectIdentitiesOutput]
        """

        self._project_users = project_users

    @property
    def total(self):
        """Gets the total of this ListProjectIdentitiesResponse.  # noqa: E501


        :return: The total of this ListProjectIdentitiesResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListProjectIdentitiesResponse.


        :param total: The total of this ListProjectIdentitiesResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListProjectIdentitiesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListProjectIdentitiesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListProjectIdentitiesResponse):
            return True

        return self.to_dict() != other.to_dict()
