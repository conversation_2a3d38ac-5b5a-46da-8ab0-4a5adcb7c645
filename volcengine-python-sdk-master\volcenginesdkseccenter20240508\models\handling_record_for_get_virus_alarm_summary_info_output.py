# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HandlingRecordForGetVirusAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'action_content': 'ActionContentForGetVirusAlarmSummaryInfoOutput',
        'extra_description': 'str',
        'handle_time': 'int',
        'language': 'str',
        'result': 'ResultForGetVirusAlarmSummaryInfoOutput',
        'soar_name': 'str',
        'user': 'str',
        'white_list_id': 'str',
        'white_list_name': 'str',
        'white_list_status': 'bool'
    }

    attribute_map = {
        'action': 'Action',
        'action_content': 'ActionContent',
        'extra_description': 'ExtraDescription',
        'handle_time': 'HandleTime',
        'language': 'Language',
        'result': 'Result',
        'soar_name': 'SoarName',
        'user': 'User',
        'white_list_id': 'WhiteListID',
        'white_list_name': 'WhiteListName',
        'white_list_status': 'WhiteListStatus'
    }

    def __init__(self, action=None, action_content=None, extra_description=None, handle_time=None, language=None, result=None, soar_name=None, user=None, white_list_id=None, white_list_name=None, white_list_status=None, _configuration=None):  # noqa: E501
        """HandlingRecordForGetVirusAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._action_content = None
        self._extra_description = None
        self._handle_time = None
        self._language = None
        self._result = None
        self._soar_name = None
        self._user = None
        self._white_list_id = None
        self._white_list_name = None
        self._white_list_status = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if action_content is not None:
            self.action_content = action_content
        if extra_description is not None:
            self.extra_description = extra_description
        if handle_time is not None:
            self.handle_time = handle_time
        if language is not None:
            self.language = language
        if result is not None:
            self.result = result
        if soar_name is not None:
            self.soar_name = soar_name
        if user is not None:
            self.user = user
        if white_list_id is not None:
            self.white_list_id = white_list_id
        if white_list_name is not None:
            self.white_list_name = white_list_name
        if white_list_status is not None:
            self.white_list_status = white_list_status

    @property
    def action(self):
        """Gets the action of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The action of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param action: The action of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def action_content(self):
        """Gets the action_content of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The action_content of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: ActionContentForGetVirusAlarmSummaryInfoOutput
        """
        return self._action_content

    @action_content.setter
    def action_content(self, action_content):
        """Sets the action_content of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param action_content: The action_content of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: ActionContentForGetVirusAlarmSummaryInfoOutput
        """

        self._action_content = action_content

    @property
    def extra_description(self):
        """Gets the extra_description of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The extra_description of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra_description

    @extra_description.setter
    def extra_description(self, extra_description):
        """Sets the extra_description of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param extra_description: The extra_description of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._extra_description = extra_description

    @property
    def handle_time(self):
        """Gets the handle_time of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The handle_time of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._handle_time

    @handle_time.setter
    def handle_time(self, handle_time):
        """Sets the handle_time of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param handle_time: The handle_time of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._handle_time = handle_time

    @property
    def language(self):
        """Gets the language of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The language of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param language: The language of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._language = language

    @property
    def result(self):
        """Gets the result of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The result of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: ResultForGetVirusAlarmSummaryInfoOutput
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param result: The result of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: ResultForGetVirusAlarmSummaryInfoOutput
        """

        self._result = result

    @property
    def soar_name(self):
        """Gets the soar_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The soar_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._soar_name

    @soar_name.setter
    def soar_name(self, soar_name):
        """Sets the soar_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param soar_name: The soar_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._soar_name = soar_name

    @property
    def user(self):
        """Gets the user of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param user: The user of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    @property
    def white_list_id(self):
        """Gets the white_list_id of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The white_list_id of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._white_list_id

    @white_list_id.setter
    def white_list_id(self, white_list_id):
        """Sets the white_list_id of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param white_list_id: The white_list_id of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._white_list_id = white_list_id

    @property
    def white_list_name(self):
        """Gets the white_list_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The white_list_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._white_list_name

    @white_list_name.setter
    def white_list_name(self, white_list_name):
        """Sets the white_list_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param white_list_name: The white_list_name of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._white_list_name = white_list_name

    @property
    def white_list_status(self):
        """Gets the white_list_status of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The white_list_status of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._white_list_status

    @white_list_status.setter
    def white_list_status(self, white_list_status):
        """Sets the white_list_status of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.


        :param white_list_status: The white_list_status of this HandlingRecordForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: bool
        """

        self._white_list_status = white_list_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HandlingRecordForGetVirusAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HandlingRecordForGetVirusAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HandlingRecordForGetVirusAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
