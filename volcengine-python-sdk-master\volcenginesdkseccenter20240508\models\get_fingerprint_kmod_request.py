# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetFingerprintKmodRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cloud_providers': 'list[str]',
        'hostname': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'state': 'list[str]',
        'tags': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentId',
        'cloud_providers': 'CloudProviders',
        'hostname': 'Hostname',
        'ip': 'Ip',
        'leaf_group_ids': 'LeafGroupIDs',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'state': 'State',
        'tags': 'Tags',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, cloud_providers=None, hostname=None, ip=None, leaf_group_ids=None, name=None, page_number=None, page_size=None, sort_by=None, sort_order=None, state=None, tags=None, top_group_id=None, _configuration=None):  # noqa: E501
        """GetFingerprintKmodRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cloud_providers = None
        self._hostname = None
        self._ip = None
        self._leaf_group_ids = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._state = None
        self._tags = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if name is not None:
            self.name = name
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if state is not None:
            self.state = state
        if tags is not None:
            self.tags = tags
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The agent_id of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this GetFingerprintKmodRequest.


        :param agent_id: The agent_id of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The cloud_providers of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this GetFingerprintKmodRequest.


        :param cloud_providers: The cloud_providers of this GetFingerprintKmodRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def hostname(self):
        """Gets the hostname of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The hostname of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this GetFingerprintKmodRequest.


        :param hostname: The hostname of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The ip of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this GetFingerprintKmodRequest.


        :param ip: The ip of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The leaf_group_ids of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this GetFingerprintKmodRequest.


        :param leaf_group_ids: The leaf_group_ids of this GetFingerprintKmodRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def name(self):
        """Gets the name of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The name of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetFingerprintKmodRequest.


        :param name: The name of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The page_number of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetFingerprintKmodRequest.


        :param page_number: The page_number of this GetFingerprintKmodRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The page_size of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetFingerprintKmodRequest.


        :param page_size: The page_size of this GetFingerprintKmodRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The sort_by of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this GetFingerprintKmodRequest.


        :param sort_by: The sort_by of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The sort_order of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this GetFingerprintKmodRequest.


        :param sort_order: The sort_order of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def state(self):
        """Gets the state of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The state of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this GetFingerprintKmodRequest.


        :param state: The state of this GetFingerprintKmodRequest.  # noqa: E501
        :type: list[str]
        """

        self._state = state

    @property
    def tags(self):
        """Gets the tags of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The tags of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this GetFingerprintKmodRequest.


        :param tags: The tags of this GetFingerprintKmodRequest.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def top_group_id(self):
        """Gets the top_group_id of this GetFingerprintKmodRequest.  # noqa: E501


        :return: The top_group_id of this GetFingerprintKmodRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this GetFingerprintKmodRequest.


        :param top_group_id: The top_group_id of this GetFingerprintKmodRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetFingerprintKmodRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetFingerprintKmodRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetFingerprintKmodRequest):
            return True

        return self.to_dict() != other.to_dict()
