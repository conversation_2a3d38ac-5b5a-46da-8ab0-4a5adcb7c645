# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertAdAuditForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'int',
        'duration': 'float',
        'label': 'list[int]',
        'reason': 'str',
        'status': 'str'
    }

    attribute_map = {
        'code': 'Code',
        'duration': 'Duration',
        'label': 'Label',
        'reason': 'Reason',
        'status': 'Status'
    }

    def __init__(self, code=None, duration=None, label=None, reason=None, status=None, _configuration=None):  # noqa: E501
        """ConvertAdAuditForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code = None
        self._duration = None
        self._label = None
        self._reason = None
        self._status = None
        self.discriminator = None

        if code is not None:
            self.code = code
        if duration is not None:
            self.duration = duration
        if label is not None:
            self.label = label
        if reason is not None:
            self.reason = reason
        if status is not None:
            self.status = status

    @property
    def code(self):
        """Gets the code of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501


        :return: The code of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this ConvertAdAuditForGetExecutionOutput.


        :param code: The code of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._code = code

    @property
    def duration(self):
        """Gets the duration of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertAdAuditForGetExecutionOutput.


        :param duration: The duration of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def label(self):
        """Gets the label of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501


        :return: The label of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._label

    @label.setter
    def label(self, label):
        """Sets the label of this ConvertAdAuditForGetExecutionOutput.


        :param label: The label of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :type: list[int]
        """

        self._label = label

    @property
    def reason(self):
        """Gets the reason of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501


        :return: The reason of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this ConvertAdAuditForGetExecutionOutput.


        :param reason: The reason of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def status(self):
        """Gets the status of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501


        :return: The status of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ConvertAdAuditForGetExecutionOutput.


        :param status: The status of this ConvertAdAuditForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertAdAuditForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertAdAuditForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertAdAuditForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
