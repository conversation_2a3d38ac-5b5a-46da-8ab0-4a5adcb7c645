# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryDLQMessageByGroupIdResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dlq_message_list': 'list[DLQMessageListForQueryDLQMessageByGroupIdOutput]',
        'has_more_message': 'bool'
    }

    attribute_map = {
        'dlq_message_list': 'DLQMessageList',
        'has_more_message': 'HasMoreMessage'
    }

    def __init__(self, dlq_message_list=None, has_more_message=None, _configuration=None):  # noqa: E501
        """QueryDLQMessageByGroupIdResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dlq_message_list = None
        self._has_more_message = None
        self.discriminator = None

        if dlq_message_list is not None:
            self.dlq_message_list = dlq_message_list
        if has_more_message is not None:
            self.has_more_message = has_more_message

    @property
    def dlq_message_list(self):
        """Gets the dlq_message_list of this QueryDLQMessageByGroupIdResponse.  # noqa: E501


        :return: The dlq_message_list of this QueryDLQMessageByGroupIdResponse.  # noqa: E501
        :rtype: list[DLQMessageListForQueryDLQMessageByGroupIdOutput]
        """
        return self._dlq_message_list

    @dlq_message_list.setter
    def dlq_message_list(self, dlq_message_list):
        """Sets the dlq_message_list of this QueryDLQMessageByGroupIdResponse.


        :param dlq_message_list: The dlq_message_list of this QueryDLQMessageByGroupIdResponse.  # noqa: E501
        :type: list[DLQMessageListForQueryDLQMessageByGroupIdOutput]
        """

        self._dlq_message_list = dlq_message_list

    @property
    def has_more_message(self):
        """Gets the has_more_message of this QueryDLQMessageByGroupIdResponse.  # noqa: E501


        :return: The has_more_message of this QueryDLQMessageByGroupIdResponse.  # noqa: E501
        :rtype: bool
        """
        return self._has_more_message

    @has_more_message.setter
    def has_more_message(self, has_more_message):
        """Sets the has_more_message of this QueryDLQMessageByGroupIdResponse.


        :param has_more_message: The has_more_message of this QueryDLQMessageByGroupIdResponse.  # noqa: E501
        :type: bool
        """

        self._has_more_message = has_more_message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryDLQMessageByGroupIdResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryDLQMessageByGroupIdResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryDLQMessageByGroupIdResponse):
            return True

        return self.to_dict() != other.to_dict()
