# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AffectedResourceForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_created_time_milli': 'int',
        'business_tag_ids': 'list[str]',
        'cloud_account_name': 'str',
        'cloud_account_uid': 'str',
        'cloud_asset_id': 'str',
        'cloud_asset_name': 'str',
        'cloud_asset_product_type': 'str',
        'cloud_asset_type': 'str',
        'cloud_vendor': 'str',
        'record_uuid': 'str',
        'security_situation_tag_ids': 'list[str]'
    }

    attribute_map = {
        'asset_created_time_milli': 'AssetCreatedTimeMilli',
        'business_tag_ids': 'BusinessTagIDs',
        'cloud_account_name': 'CloudAccountName',
        'cloud_account_uid': 'CloudAccountUID',
        'cloud_asset_id': 'CloudAssetID',
        'cloud_asset_name': 'CloudAssetName',
        'cloud_asset_product_type': 'CloudAssetProductType',
        'cloud_asset_type': 'CloudAssetType',
        'cloud_vendor': 'CloudVendor',
        'record_uuid': 'RecordUUID',
        'security_situation_tag_ids': 'SecuritySituationTagIDs'
    }

    def __init__(self, asset_created_time_milli=None, business_tag_ids=None, cloud_account_name=None, cloud_account_uid=None, cloud_asset_id=None, cloud_asset_name=None, cloud_asset_product_type=None, cloud_asset_type=None, cloud_vendor=None, record_uuid=None, security_situation_tag_ids=None, _configuration=None):  # noqa: E501
        """AffectedResourceForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_created_time_milli = None
        self._business_tag_ids = None
        self._cloud_account_name = None
        self._cloud_account_uid = None
        self._cloud_asset_id = None
        self._cloud_asset_name = None
        self._cloud_asset_product_type = None
        self._cloud_asset_type = None
        self._cloud_vendor = None
        self._record_uuid = None
        self._security_situation_tag_ids = None
        self.discriminator = None

        if asset_created_time_milli is not None:
            self.asset_created_time_milli = asset_created_time_milli
        if business_tag_ids is not None:
            self.business_tag_ids = business_tag_ids
        if cloud_account_name is not None:
            self.cloud_account_name = cloud_account_name
        if cloud_account_uid is not None:
            self.cloud_account_uid = cloud_account_uid
        if cloud_asset_id is not None:
            self.cloud_asset_id = cloud_asset_id
        if cloud_asset_name is not None:
            self.cloud_asset_name = cloud_asset_name
        if cloud_asset_product_type is not None:
            self.cloud_asset_product_type = cloud_asset_product_type
        if cloud_asset_type is not None:
            self.cloud_asset_type = cloud_asset_type
        if cloud_vendor is not None:
            self.cloud_vendor = cloud_vendor
        if record_uuid is not None:
            self.record_uuid = record_uuid
        if security_situation_tag_ids is not None:
            self.security_situation_tag_ids = security_situation_tag_ids

    @property
    def asset_created_time_milli(self):
        """Gets the asset_created_time_milli of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The asset_created_time_milli of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: int
        """
        return self._asset_created_time_milli

    @asset_created_time_milli.setter
    def asset_created_time_milli(self, asset_created_time_milli):
        """Sets the asset_created_time_milli of this AffectedResourceForGetRiskOutput.


        :param asset_created_time_milli: The asset_created_time_milli of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: int
        """

        self._asset_created_time_milli = asset_created_time_milli

    @property
    def business_tag_ids(self):
        """Gets the business_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The business_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._business_tag_ids

    @business_tag_ids.setter
    def business_tag_ids(self, business_tag_ids):
        """Sets the business_tag_ids of this AffectedResourceForGetRiskOutput.


        :param business_tag_ids: The business_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: list[str]
        """

        self._business_tag_ids = business_tag_ids

    @property
    def cloud_account_name(self):
        """Gets the cloud_account_name of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_account_name of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_name

    @cloud_account_name.setter
    def cloud_account_name(self, cloud_account_name):
        """Sets the cloud_account_name of this AffectedResourceForGetRiskOutput.


        :param cloud_account_name: The cloud_account_name of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_account_name = cloud_account_name

    @property
    def cloud_account_uid(self):
        """Gets the cloud_account_uid of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_account_uid of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_account_uid

    @cloud_account_uid.setter
    def cloud_account_uid(self, cloud_account_uid):
        """Sets the cloud_account_uid of this AffectedResourceForGetRiskOutput.


        :param cloud_account_uid: The cloud_account_uid of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_account_uid = cloud_account_uid

    @property
    def cloud_asset_id(self):
        """Gets the cloud_asset_id of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_asset_id of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_asset_id

    @cloud_asset_id.setter
    def cloud_asset_id(self, cloud_asset_id):
        """Sets the cloud_asset_id of this AffectedResourceForGetRiskOutput.


        :param cloud_asset_id: The cloud_asset_id of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_asset_id = cloud_asset_id

    @property
    def cloud_asset_name(self):
        """Gets the cloud_asset_name of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_asset_name of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_asset_name

    @cloud_asset_name.setter
    def cloud_asset_name(self, cloud_asset_name):
        """Sets the cloud_asset_name of this AffectedResourceForGetRiskOutput.


        :param cloud_asset_name: The cloud_asset_name of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_asset_name = cloud_asset_name

    @property
    def cloud_asset_product_type(self):
        """Gets the cloud_asset_product_type of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_asset_product_type of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_asset_product_type

    @cloud_asset_product_type.setter
    def cloud_asset_product_type(self, cloud_asset_product_type):
        """Sets the cloud_asset_product_type of this AffectedResourceForGetRiskOutput.


        :param cloud_asset_product_type: The cloud_asset_product_type of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_asset_product_type = cloud_asset_product_type

    @property
    def cloud_asset_type(self):
        """Gets the cloud_asset_type of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_asset_type of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_asset_type

    @cloud_asset_type.setter
    def cloud_asset_type(self, cloud_asset_type):
        """Sets the cloud_asset_type of this AffectedResourceForGetRiskOutput.


        :param cloud_asset_type: The cloud_asset_type of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_asset_type = cloud_asset_type

    @property
    def cloud_vendor(self):
        """Gets the cloud_vendor of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The cloud_vendor of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_vendor

    @cloud_vendor.setter
    def cloud_vendor(self, cloud_vendor):
        """Sets the cloud_vendor of this AffectedResourceForGetRiskOutput.


        :param cloud_vendor: The cloud_vendor of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._cloud_vendor = cloud_vendor

    @property
    def record_uuid(self):
        """Gets the record_uuid of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The record_uuid of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._record_uuid

    @record_uuid.setter
    def record_uuid(self, record_uuid):
        """Sets the record_uuid of this AffectedResourceForGetRiskOutput.


        :param record_uuid: The record_uuid of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._record_uuid = record_uuid

    @property
    def security_situation_tag_ids(self):
        """Gets the security_situation_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501


        :return: The security_situation_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_situation_tag_ids

    @security_situation_tag_ids.setter
    def security_situation_tag_ids(self, security_situation_tag_ids):
        """Sets the security_situation_tag_ids of this AffectedResourceForGetRiskOutput.


        :param security_situation_tag_ids: The security_situation_tag_ids of this AffectedResourceForGetRiskOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_situation_tag_ids = security_situation_tag_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AffectedResourceForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AffectedResourceForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AffectedResourceForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
