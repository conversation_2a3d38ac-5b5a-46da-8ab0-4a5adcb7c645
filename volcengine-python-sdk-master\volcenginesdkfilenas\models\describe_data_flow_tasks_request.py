# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDataFlowTasksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_flow_ids': 'str',
        'data_flow_names': 'str',
        'ids': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'status': 'str'
    }

    attribute_map = {
        'data_flow_ids': 'DataFlowIds',
        'data_flow_names': 'DataFlowNames',
        'ids': 'Ids',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'status': 'Status'
    }

    def __init__(self, data_flow_ids=None, data_flow_names=None, ids=None, page_number=None, page_size=None, status=None, _configuration=None):  # noqa: E501
        """DescribeDataFlowTasksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_flow_ids = None
        self._data_flow_names = None
        self._ids = None
        self._page_number = None
        self._page_size = None
        self._status = None
        self.discriminator = None

        if data_flow_ids is not None:
            self.data_flow_ids = data_flow_ids
        if data_flow_names is not None:
            self.data_flow_names = data_flow_names
        if ids is not None:
            self.ids = ids
        self.page_number = page_number
        self.page_size = page_size
        if status is not None:
            self.status = status

    @property
    def data_flow_ids(self):
        """Gets the data_flow_ids of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The data_flow_ids of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_ids

    @data_flow_ids.setter
    def data_flow_ids(self, data_flow_ids):
        """Sets the data_flow_ids of this DescribeDataFlowTasksRequest.


        :param data_flow_ids: The data_flow_ids of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: str
        """

        self._data_flow_ids = data_flow_ids

    @property
    def data_flow_names(self):
        """Gets the data_flow_names of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The data_flow_names of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_names

    @data_flow_names.setter
    def data_flow_names(self, data_flow_names):
        """Sets the data_flow_names of this DescribeDataFlowTasksRequest.


        :param data_flow_names: The data_flow_names of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: str
        """

        self._data_flow_names = data_flow_names

    @property
    def ids(self):
        """Gets the ids of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The ids of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this DescribeDataFlowTasksRequest.


        :param ids: The ids of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: str
        """

        self._ids = ids

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The page_number of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDataFlowTasksRequest.


        :param page_number: The page_number of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The page_size of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDataFlowTasksRequest.


        :param page_size: The page_size of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def status(self):
        """Gets the status of this DescribeDataFlowTasksRequest.  # noqa: E501


        :return: The status of this DescribeDataFlowTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeDataFlowTasksRequest.


        :param status: The status of this DescribeDataFlowTasksRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDataFlowTasksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDataFlowTasksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDataFlowTasksRequest):
            return True

        return self.to_dict() != other.to_dict()
