# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LoadBalancerSettingsForUpdateUpstreamInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'consistent_hash_lb': 'ConsistentHashLBForUpdateUpstreamInput',
        'lb_policy': 'str',
        'simple_lb': 'str',
        'warmup_duration': 'int'
    }

    attribute_map = {
        'consistent_hash_lb': 'ConsistentHashLB',
        'lb_policy': 'LbPolicy',
        'simple_lb': 'SimpleLB',
        'warmup_duration': 'WarmupDuration'
    }

    def __init__(self, consistent_hash_lb=None, lb_policy=None, simple_lb=None, warmup_duration=None, _configuration=None):  # noqa: E501
        """LoadBalancerSettingsForUpdateUpstreamInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._consistent_hash_lb = None
        self._lb_policy = None
        self._simple_lb = None
        self._warmup_duration = None
        self.discriminator = None

        if consistent_hash_lb is not None:
            self.consistent_hash_lb = consistent_hash_lb
        if lb_policy is not None:
            self.lb_policy = lb_policy
        if simple_lb is not None:
            self.simple_lb = simple_lb
        if warmup_duration is not None:
            self.warmup_duration = warmup_duration

    @property
    def consistent_hash_lb(self):
        """Gets the consistent_hash_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501


        :return: The consistent_hash_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :rtype: ConsistentHashLBForUpdateUpstreamInput
        """
        return self._consistent_hash_lb

    @consistent_hash_lb.setter
    def consistent_hash_lb(self, consistent_hash_lb):
        """Sets the consistent_hash_lb of this LoadBalancerSettingsForUpdateUpstreamInput.


        :param consistent_hash_lb: The consistent_hash_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :type: ConsistentHashLBForUpdateUpstreamInput
        """

        self._consistent_hash_lb = consistent_hash_lb

    @property
    def lb_policy(self):
        """Gets the lb_policy of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501


        :return: The lb_policy of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._lb_policy

    @lb_policy.setter
    def lb_policy(self, lb_policy):
        """Sets the lb_policy of this LoadBalancerSettingsForUpdateUpstreamInput.


        :param lb_policy: The lb_policy of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._lb_policy = lb_policy

    @property
    def simple_lb(self):
        """Gets the simple_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501


        :return: The simple_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._simple_lb

    @simple_lb.setter
    def simple_lb(self, simple_lb):
        """Sets the simple_lb of this LoadBalancerSettingsForUpdateUpstreamInput.


        :param simple_lb: The simple_lb of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._simple_lb = simple_lb

    @property
    def warmup_duration(self):
        """Gets the warmup_duration of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501


        :return: The warmup_duration of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :rtype: int
        """
        return self._warmup_duration

    @warmup_duration.setter
    def warmup_duration(self, warmup_duration):
        """Sets the warmup_duration of this LoadBalancerSettingsForUpdateUpstreamInput.


        :param warmup_duration: The warmup_duration of this LoadBalancerSettingsForUpdateUpstreamInput.  # noqa: E501
        :type: int
        """

        self._warmup_duration = warmup_duration

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LoadBalancerSettingsForUpdateUpstreamInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LoadBalancerSettingsForUpdateUpstreamInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LoadBalancerSettingsForUpdateUpstreamInput):
            return True

        return self.to_dict() != other.to_dict()
