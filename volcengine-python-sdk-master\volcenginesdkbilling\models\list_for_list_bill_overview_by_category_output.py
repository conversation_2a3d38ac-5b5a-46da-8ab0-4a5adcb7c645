# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListBillOverviewByCategoryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bill_category_parent': 'str',
        'bill_period': 'str',
        'business_mode': 'str',
        'country_area': 'str',
        'country_region': 'str',
        'coupon_amount': 'str',
        'credit_carried_amount': 'str',
        'currency': 'str',
        'discount_bill_amount': 'str',
        'original_bill_amount': 'str',
        'owner_customer_name': 'str',
        'owner_id': 'str',
        'owner_user_name': 'str',
        'paid_amount': 'str',
        'payable_amount': 'str',
        'payer_customer_name': 'str',
        'payer_id': 'str',
        'payer_user_name': 'str',
        'posttax_amount': 'str',
        'pretax_amount': 'str',
        'pretax_real_value': 'str',
        'real_value': 'str',
        'seller_customer_name': 'str',
        'seller_id': 'str',
        'seller_user_name': 'str',
        'settle_posttax_amount': 'str',
        'settle_pretax_amount': 'str',
        'settle_pretax_real_value': 'str',
        'settle_real_value': 'str',
        'settle_tax': 'str',
        'settlement_type': 'str',
        'subject_name': 'str',
        'subject_no': 'str',
        'tax': 'str',
        'unpaid_amount': 'str'
    }

    attribute_map = {
        'bill_category_parent': 'BillCategoryParent',
        'bill_period': 'BillPeriod',
        'business_mode': 'BusinessMode',
        'country_area': 'CountryArea',
        'country_region': 'CountryRegion',
        'coupon_amount': 'CouponAmount',
        'credit_carried_amount': 'CreditCarriedAmount',
        'currency': 'Currency',
        'discount_bill_amount': 'DiscountBillAmount',
        'original_bill_amount': 'OriginalBillAmount',
        'owner_customer_name': 'OwnerCustomerName',
        'owner_id': 'OwnerID',
        'owner_user_name': 'OwnerUserName',
        'paid_amount': 'PaidAmount',
        'payable_amount': 'PayableAmount',
        'payer_customer_name': 'PayerCustomerName',
        'payer_id': 'PayerID',
        'payer_user_name': 'PayerUserName',
        'posttax_amount': 'PosttaxAmount',
        'pretax_amount': 'PretaxAmount',
        'pretax_real_value': 'PretaxRealValue',
        'real_value': 'RealValue',
        'seller_customer_name': 'SellerCustomerName',
        'seller_id': 'SellerID',
        'seller_user_name': 'SellerUserName',
        'settle_posttax_amount': 'SettlePosttaxAmount',
        'settle_pretax_amount': 'SettlePretaxAmount',
        'settle_pretax_real_value': 'SettlePretaxRealValue',
        'settle_real_value': 'SettleRealValue',
        'settle_tax': 'SettleTax',
        'settlement_type': 'SettlementType',
        'subject_name': 'SubjectName',
        'subject_no': 'SubjectNo',
        'tax': 'Tax',
        'unpaid_amount': 'UnpaidAmount'
    }

    def __init__(self, bill_category_parent=None, bill_period=None, business_mode=None, country_area=None, country_region=None, coupon_amount=None, credit_carried_amount=None, currency=None, discount_bill_amount=None, original_bill_amount=None, owner_customer_name=None, owner_id=None, owner_user_name=None, paid_amount=None, payable_amount=None, payer_customer_name=None, payer_id=None, payer_user_name=None, posttax_amount=None, pretax_amount=None, pretax_real_value=None, real_value=None, seller_customer_name=None, seller_id=None, seller_user_name=None, settle_posttax_amount=None, settle_pretax_amount=None, settle_pretax_real_value=None, settle_real_value=None, settle_tax=None, settlement_type=None, subject_name=None, subject_no=None, tax=None, unpaid_amount=None, _configuration=None):  # noqa: E501
        """ListForListBillOverviewByCategoryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bill_category_parent = None
        self._bill_period = None
        self._business_mode = None
        self._country_area = None
        self._country_region = None
        self._coupon_amount = None
        self._credit_carried_amount = None
        self._currency = None
        self._discount_bill_amount = None
        self._original_bill_amount = None
        self._owner_customer_name = None
        self._owner_id = None
        self._owner_user_name = None
        self._paid_amount = None
        self._payable_amount = None
        self._payer_customer_name = None
        self._payer_id = None
        self._payer_user_name = None
        self._posttax_amount = None
        self._pretax_amount = None
        self._pretax_real_value = None
        self._real_value = None
        self._seller_customer_name = None
        self._seller_id = None
        self._seller_user_name = None
        self._settle_posttax_amount = None
        self._settle_pretax_amount = None
        self._settle_pretax_real_value = None
        self._settle_real_value = None
        self._settle_tax = None
        self._settlement_type = None
        self._subject_name = None
        self._subject_no = None
        self._tax = None
        self._unpaid_amount = None
        self.discriminator = None

        if bill_category_parent is not None:
            self.bill_category_parent = bill_category_parent
        if bill_period is not None:
            self.bill_period = bill_period
        if business_mode is not None:
            self.business_mode = business_mode
        if country_area is not None:
            self.country_area = country_area
        if country_region is not None:
            self.country_region = country_region
        if coupon_amount is not None:
            self.coupon_amount = coupon_amount
        if credit_carried_amount is not None:
            self.credit_carried_amount = credit_carried_amount
        if currency is not None:
            self.currency = currency
        if discount_bill_amount is not None:
            self.discount_bill_amount = discount_bill_amount
        if original_bill_amount is not None:
            self.original_bill_amount = original_bill_amount
        if owner_customer_name is not None:
            self.owner_customer_name = owner_customer_name
        if owner_id is not None:
            self.owner_id = owner_id
        if owner_user_name is not None:
            self.owner_user_name = owner_user_name
        if paid_amount is not None:
            self.paid_amount = paid_amount
        if payable_amount is not None:
            self.payable_amount = payable_amount
        if payer_customer_name is not None:
            self.payer_customer_name = payer_customer_name
        if payer_id is not None:
            self.payer_id = payer_id
        if payer_user_name is not None:
            self.payer_user_name = payer_user_name
        if posttax_amount is not None:
            self.posttax_amount = posttax_amount
        if pretax_amount is not None:
            self.pretax_amount = pretax_amount
        if pretax_real_value is not None:
            self.pretax_real_value = pretax_real_value
        if real_value is not None:
            self.real_value = real_value
        if seller_customer_name is not None:
            self.seller_customer_name = seller_customer_name
        if seller_id is not None:
            self.seller_id = seller_id
        if seller_user_name is not None:
            self.seller_user_name = seller_user_name
        if settle_posttax_amount is not None:
            self.settle_posttax_amount = settle_posttax_amount
        if settle_pretax_amount is not None:
            self.settle_pretax_amount = settle_pretax_amount
        if settle_pretax_real_value is not None:
            self.settle_pretax_real_value = settle_pretax_real_value
        if settle_real_value is not None:
            self.settle_real_value = settle_real_value
        if settle_tax is not None:
            self.settle_tax = settle_tax
        if settlement_type is not None:
            self.settlement_type = settlement_type
        if subject_name is not None:
            self.subject_name = subject_name
        if subject_no is not None:
            self.subject_no = subject_no
        if tax is not None:
            self.tax = tax
        if unpaid_amount is not None:
            self.unpaid_amount = unpaid_amount

    @property
    def bill_category_parent(self):
        """Gets the bill_category_parent of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The bill_category_parent of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_category_parent

    @bill_category_parent.setter
    def bill_category_parent(self, bill_category_parent):
        """Sets the bill_category_parent of this ListForListBillOverviewByCategoryOutput.


        :param bill_category_parent: The bill_category_parent of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._bill_category_parent = bill_category_parent

    @property
    def bill_period(self):
        """Gets the bill_period of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The bill_period of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._bill_period

    @bill_period.setter
    def bill_period(self, bill_period):
        """Sets the bill_period of this ListForListBillOverviewByCategoryOutput.


        :param bill_period: The bill_period of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._bill_period = bill_period

    @property
    def business_mode(self):
        """Gets the business_mode of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The business_mode of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_mode

    @business_mode.setter
    def business_mode(self, business_mode):
        """Sets the business_mode of this ListForListBillOverviewByCategoryOutput.


        :param business_mode: The business_mode of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._business_mode = business_mode

    @property
    def country_area(self):
        """Gets the country_area of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The country_area of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._country_area

    @country_area.setter
    def country_area(self, country_area):
        """Sets the country_area of this ListForListBillOverviewByCategoryOutput.


        :param country_area: The country_area of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._country_area = country_area

    @property
    def country_region(self):
        """Gets the country_region of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The country_region of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._country_region

    @country_region.setter
    def country_region(self, country_region):
        """Sets the country_region of this ListForListBillOverviewByCategoryOutput.


        :param country_region: The country_region of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._country_region = country_region

    @property
    def coupon_amount(self):
        """Gets the coupon_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The coupon_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._coupon_amount

    @coupon_amount.setter
    def coupon_amount(self, coupon_amount):
        """Sets the coupon_amount of this ListForListBillOverviewByCategoryOutput.


        :param coupon_amount: The coupon_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._coupon_amount = coupon_amount

    @property
    def credit_carried_amount(self):
        """Gets the credit_carried_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The credit_carried_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._credit_carried_amount

    @credit_carried_amount.setter
    def credit_carried_amount(self, credit_carried_amount):
        """Sets the credit_carried_amount of this ListForListBillOverviewByCategoryOutput.


        :param credit_carried_amount: The credit_carried_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._credit_carried_amount = credit_carried_amount

    @property
    def currency(self):
        """Gets the currency of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The currency of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._currency

    @currency.setter
    def currency(self, currency):
        """Sets the currency of this ListForListBillOverviewByCategoryOutput.


        :param currency: The currency of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._currency = currency

    @property
    def discount_bill_amount(self):
        """Gets the discount_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The discount_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._discount_bill_amount

    @discount_bill_amount.setter
    def discount_bill_amount(self, discount_bill_amount):
        """Sets the discount_bill_amount of this ListForListBillOverviewByCategoryOutput.


        :param discount_bill_amount: The discount_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._discount_bill_amount = discount_bill_amount

    @property
    def original_bill_amount(self):
        """Gets the original_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The original_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_bill_amount

    @original_bill_amount.setter
    def original_bill_amount(self, original_bill_amount):
        """Sets the original_bill_amount of this ListForListBillOverviewByCategoryOutput.


        :param original_bill_amount: The original_bill_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._original_bill_amount = original_bill_amount

    @property
    def owner_customer_name(self):
        """Gets the owner_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The owner_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_customer_name

    @owner_customer_name.setter
    def owner_customer_name(self, owner_customer_name):
        """Sets the owner_customer_name of this ListForListBillOverviewByCategoryOutput.


        :param owner_customer_name: The owner_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._owner_customer_name = owner_customer_name

    @property
    def owner_id(self):
        """Gets the owner_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The owner_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_id

    @owner_id.setter
    def owner_id(self, owner_id):
        """Sets the owner_id of this ListForListBillOverviewByCategoryOutput.


        :param owner_id: The owner_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._owner_id = owner_id

    @property
    def owner_user_name(self):
        """Gets the owner_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The owner_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_user_name

    @owner_user_name.setter
    def owner_user_name(self, owner_user_name):
        """Sets the owner_user_name of this ListForListBillOverviewByCategoryOutput.


        :param owner_user_name: The owner_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._owner_user_name = owner_user_name

    @property
    def paid_amount(self):
        """Gets the paid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The paid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_amount

    @paid_amount.setter
    def paid_amount(self, paid_amount):
        """Sets the paid_amount of this ListForListBillOverviewByCategoryOutput.


        :param paid_amount: The paid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._paid_amount = paid_amount

    @property
    def payable_amount(self):
        """Gets the payable_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The payable_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._payable_amount

    @payable_amount.setter
    def payable_amount(self, payable_amount):
        """Sets the payable_amount of this ListForListBillOverviewByCategoryOutput.


        :param payable_amount: The payable_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._payable_amount = payable_amount

    @property
    def payer_customer_name(self):
        """Gets the payer_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The payer_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_customer_name

    @payer_customer_name.setter
    def payer_customer_name(self, payer_customer_name):
        """Sets the payer_customer_name of this ListForListBillOverviewByCategoryOutput.


        :param payer_customer_name: The payer_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._payer_customer_name = payer_customer_name

    @property
    def payer_id(self):
        """Gets the payer_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The payer_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_id

    @payer_id.setter
    def payer_id(self, payer_id):
        """Sets the payer_id of this ListForListBillOverviewByCategoryOutput.


        :param payer_id: The payer_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._payer_id = payer_id

    @property
    def payer_user_name(self):
        """Gets the payer_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The payer_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._payer_user_name

    @payer_user_name.setter
    def payer_user_name(self, payer_user_name):
        """Sets the payer_user_name of this ListForListBillOverviewByCategoryOutput.


        :param payer_user_name: The payer_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._payer_user_name = payer_user_name

    @property
    def posttax_amount(self):
        """Gets the posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._posttax_amount

    @posttax_amount.setter
    def posttax_amount(self, posttax_amount):
        """Sets the posttax_amount of this ListForListBillOverviewByCategoryOutput.


        :param posttax_amount: The posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._posttax_amount = posttax_amount

    @property
    def pretax_amount(self):
        """Gets the pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._pretax_amount

    @pretax_amount.setter
    def pretax_amount(self, pretax_amount):
        """Sets the pretax_amount of this ListForListBillOverviewByCategoryOutput.


        :param pretax_amount: The pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._pretax_amount = pretax_amount

    @property
    def pretax_real_value(self):
        """Gets the pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._pretax_real_value

    @pretax_real_value.setter
    def pretax_real_value(self, pretax_real_value):
        """Sets the pretax_real_value of this ListForListBillOverviewByCategoryOutput.


        :param pretax_real_value: The pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._pretax_real_value = pretax_real_value

    @property
    def real_value(self):
        """Gets the real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._real_value

    @real_value.setter
    def real_value(self, real_value):
        """Sets the real_value of this ListForListBillOverviewByCategoryOutput.


        :param real_value: The real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._real_value = real_value

    @property
    def seller_customer_name(self):
        """Gets the seller_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The seller_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_customer_name

    @seller_customer_name.setter
    def seller_customer_name(self, seller_customer_name):
        """Sets the seller_customer_name of this ListForListBillOverviewByCategoryOutput.


        :param seller_customer_name: The seller_customer_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._seller_customer_name = seller_customer_name

    @property
    def seller_id(self):
        """Gets the seller_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The seller_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_id

    @seller_id.setter
    def seller_id(self, seller_id):
        """Sets the seller_id of this ListForListBillOverviewByCategoryOutput.


        :param seller_id: The seller_id of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._seller_id = seller_id

    @property
    def seller_user_name(self):
        """Gets the seller_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The seller_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._seller_user_name

    @seller_user_name.setter
    def seller_user_name(self, seller_user_name):
        """Sets the seller_user_name of this ListForListBillOverviewByCategoryOutput.


        :param seller_user_name: The seller_user_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._seller_user_name = seller_user_name

    @property
    def settle_posttax_amount(self):
        """Gets the settle_posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settle_posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_posttax_amount

    @settle_posttax_amount.setter
    def settle_posttax_amount(self, settle_posttax_amount):
        """Sets the settle_posttax_amount of this ListForListBillOverviewByCategoryOutput.


        :param settle_posttax_amount: The settle_posttax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settle_posttax_amount = settle_posttax_amount

    @property
    def settle_pretax_amount(self):
        """Gets the settle_pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settle_pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pretax_amount

    @settle_pretax_amount.setter
    def settle_pretax_amount(self, settle_pretax_amount):
        """Sets the settle_pretax_amount of this ListForListBillOverviewByCategoryOutput.


        :param settle_pretax_amount: The settle_pretax_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settle_pretax_amount = settle_pretax_amount

    @property
    def settle_pretax_real_value(self):
        """Gets the settle_pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settle_pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_pretax_real_value

    @settle_pretax_real_value.setter
    def settle_pretax_real_value(self, settle_pretax_real_value):
        """Sets the settle_pretax_real_value of this ListForListBillOverviewByCategoryOutput.


        :param settle_pretax_real_value: The settle_pretax_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settle_pretax_real_value = settle_pretax_real_value

    @property
    def settle_real_value(self):
        """Gets the settle_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settle_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_real_value

    @settle_real_value.setter
    def settle_real_value(self, settle_real_value):
        """Sets the settle_real_value of this ListForListBillOverviewByCategoryOutput.


        :param settle_real_value: The settle_real_value of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settle_real_value = settle_real_value

    @property
    def settle_tax(self):
        """Gets the settle_tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settle_tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settle_tax

    @settle_tax.setter
    def settle_tax(self, settle_tax):
        """Sets the settle_tax of this ListForListBillOverviewByCategoryOutput.


        :param settle_tax: The settle_tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settle_tax = settle_tax

    @property
    def settlement_type(self):
        """Gets the settlement_type of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The settlement_type of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._settlement_type

    @settlement_type.setter
    def settlement_type(self, settlement_type):
        """Sets the settlement_type of this ListForListBillOverviewByCategoryOutput.


        :param settlement_type: The settlement_type of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._settlement_type = settlement_type

    @property
    def subject_name(self):
        """Gets the subject_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The subject_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_name

    @subject_name.setter
    def subject_name(self, subject_name):
        """Sets the subject_name of this ListForListBillOverviewByCategoryOutput.


        :param subject_name: The subject_name of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._subject_name = subject_name

    @property
    def subject_no(self):
        """Gets the subject_no of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The subject_no of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_no

    @subject_no.setter
    def subject_no(self, subject_no):
        """Sets the subject_no of this ListForListBillOverviewByCategoryOutput.


        :param subject_no: The subject_no of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._subject_no = subject_no

    @property
    def tax(self):
        """Gets the tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._tax

    @tax.setter
    def tax(self, tax):
        """Sets the tax of this ListForListBillOverviewByCategoryOutput.


        :param tax: The tax of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._tax = tax

    @property
    def unpaid_amount(self):
        """Gets the unpaid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501


        :return: The unpaid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._unpaid_amount

    @unpaid_amount.setter
    def unpaid_amount(self, unpaid_amount):
        """Sets the unpaid_amount of this ListForListBillOverviewByCategoryOutput.


        :param unpaid_amount: The unpaid_amount of this ListForListBillOverviewByCategoryOutput.  # noqa: E501
        :type: str
        """

        self._unpaid_amount = unpaid_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListBillOverviewByCategoryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListBillOverviewByCategoryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListBillOverviewByCategoryOutput):
            return True

        return self.to_dict() != other.to_dict()
