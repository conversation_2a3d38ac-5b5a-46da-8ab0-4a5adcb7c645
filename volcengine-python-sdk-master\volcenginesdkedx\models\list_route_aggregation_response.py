# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRouteAggregationResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_list': 'list[AggregationListForListRouteAggregationOutput]',
        'page_number': 'int',
        'page_size': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'aggregation_list': 'AggregationList',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'total_count': 'TotalCount'
    }

    def __init__(self, aggregation_list=None, page_number=None, page_size=None, total_count=None, _configuration=None):  # noqa: E501
        """ListRouteAggregationResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_list = None
        self._page_number = None
        self._page_size = None
        self._total_count = None
        self.discriminator = None

        if aggregation_list is not None:
            self.aggregation_list = aggregation_list
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if total_count is not None:
            self.total_count = total_count

    @property
    def aggregation_list(self):
        """Gets the aggregation_list of this ListRouteAggregationResponse.  # noqa: E501


        :return: The aggregation_list of this ListRouteAggregationResponse.  # noqa: E501
        :rtype: list[AggregationListForListRouteAggregationOutput]
        """
        return self._aggregation_list

    @aggregation_list.setter
    def aggregation_list(self, aggregation_list):
        """Sets the aggregation_list of this ListRouteAggregationResponse.


        :param aggregation_list: The aggregation_list of this ListRouteAggregationResponse.  # noqa: E501
        :type: list[AggregationListForListRouteAggregationOutput]
        """

        self._aggregation_list = aggregation_list

    @property
    def page_number(self):
        """Gets the page_number of this ListRouteAggregationResponse.  # noqa: E501


        :return: The page_number of this ListRouteAggregationResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRouteAggregationResponse.


        :param page_number: The page_number of this ListRouteAggregationResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRouteAggregationResponse.  # noqa: E501


        :return: The page_size of this ListRouteAggregationResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRouteAggregationResponse.


        :param page_size: The page_size of this ListRouteAggregationResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def total_count(self):
        """Gets the total_count of this ListRouteAggregationResponse.  # noqa: E501


        :return: The total_count of this ListRouteAggregationResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ListRouteAggregationResponse.


        :param total_count: The total_count of this ListRouteAggregationResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRouteAggregationResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRouteAggregationResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRouteAggregationResponse):
            return True

        return self.to_dict() != other.to_dict()
