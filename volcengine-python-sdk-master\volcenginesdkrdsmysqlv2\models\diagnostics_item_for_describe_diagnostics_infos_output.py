# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiagnosticsItemForDescribeDiagnosticsInfosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnostics_item': 'str',
        'diagnostics_result': 'str',
        'possible_effect': 'str',
        'suggestion': 'str'
    }

    attribute_map = {
        'diagnostics_item': 'DiagnosticsItem',
        'diagnostics_result': 'DiagnosticsResult',
        'possible_effect': 'PossibleEffect',
        'suggestion': 'Suggestion'
    }

    def __init__(self, diagnostics_item=None, diagnostics_result=None, possible_effect=None, suggestion=None, _configuration=None):  # noqa: E501
        """DiagnosticsItemForDescribeDiagnosticsInfosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnostics_item = None
        self._diagnostics_result = None
        self._possible_effect = None
        self._suggestion = None
        self.discriminator = None

        if diagnostics_item is not None:
            self.diagnostics_item = diagnostics_item
        if diagnostics_result is not None:
            self.diagnostics_result = diagnostics_result
        if possible_effect is not None:
            self.possible_effect = possible_effect
        if suggestion is not None:
            self.suggestion = suggestion

    @property
    def diagnostics_item(self):
        """Gets the diagnostics_item of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The diagnostics_item of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._diagnostics_item

    @diagnostics_item.setter
    def diagnostics_item(self, diagnostics_item):
        """Sets the diagnostics_item of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.


        :param diagnostics_item: The diagnostics_item of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._diagnostics_item = diagnostics_item

    @property
    def diagnostics_result(self):
        """Gets the diagnostics_result of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The diagnostics_result of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._diagnostics_result

    @diagnostics_result.setter
    def diagnostics_result(self, diagnostics_result):
        """Sets the diagnostics_result of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.


        :param diagnostics_result: The diagnostics_result of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._diagnostics_result = diagnostics_result

    @property
    def possible_effect(self):
        """Gets the possible_effect of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The possible_effect of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._possible_effect

    @possible_effect.setter
    def possible_effect(self, possible_effect):
        """Sets the possible_effect of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.


        :param possible_effect: The possible_effect of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._possible_effect = possible_effect

    @property
    def suggestion(self):
        """Gets the suggestion of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The suggestion of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._suggestion

    @suggestion.setter
    def suggestion(self, suggestion):
        """Sets the suggestion of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.


        :param suggestion: The suggestion of this DiagnosticsItemForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._suggestion = suggestion

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiagnosticsItemForDescribeDiagnosticsInfosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiagnosticsItemForDescribeDiagnosticsInfosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiagnosticsItemForDescribeDiagnosticsInfosOutput):
            return True

        return self.to_dict() != other.to_dict()
