# coding: utf-8

"""
    dbw

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SearchParamForDescribeSlowLogsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dbs': 'list[str]',
        'max_query_time': 'float',
        'min_query_time': 'float',
        'sql_template': 'str',
        'sql_template_id': 'str',
        'source_ips': 'list[str]',
        'users': 'list[str]'
    }

    attribute_map = {
        'dbs': 'DBs',
        'max_query_time': 'MaxQueryTime',
        'min_query_time': 'MinQueryTime',
        'sql_template': 'SQLTemplate',
        'sql_template_id': 'SQLTemplateID',
        'source_ips': 'SourceIPs',
        'users': 'Users'
    }

    def __init__(self, dbs=None, max_query_time=None, min_query_time=None, sql_template=None, sql_template_id=None, source_ips=None, users=None, _configuration=None):  # noqa: E501
        """SearchParamForDescribeSlowLogsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dbs = None
        self._max_query_time = None
        self._min_query_time = None
        self._sql_template = None
        self._sql_template_id = None
        self._source_ips = None
        self._users = None
        self.discriminator = None

        if dbs is not None:
            self.dbs = dbs
        if max_query_time is not None:
            self.max_query_time = max_query_time
        if min_query_time is not None:
            self.min_query_time = min_query_time
        if sql_template is not None:
            self.sql_template = sql_template
        if sql_template_id is not None:
            self.sql_template_id = sql_template_id
        if source_ips is not None:
            self.source_ips = source_ips
        if users is not None:
            self.users = users

    @property
    def dbs(self):
        """Gets the dbs of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The dbs of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._dbs

    @dbs.setter
    def dbs(self, dbs):
        """Sets the dbs of this SearchParamForDescribeSlowLogsInput.


        :param dbs: The dbs of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: list[str]
        """

        self._dbs = dbs

    @property
    def max_query_time(self):
        """Gets the max_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The max_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: float
        """
        return self._max_query_time

    @max_query_time.setter
    def max_query_time(self, max_query_time):
        """Sets the max_query_time of this SearchParamForDescribeSlowLogsInput.


        :param max_query_time: The max_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: float
        """

        self._max_query_time = max_query_time

    @property
    def min_query_time(self):
        """Gets the min_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The min_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: float
        """
        return self._min_query_time

    @min_query_time.setter
    def min_query_time(self, min_query_time):
        """Sets the min_query_time of this SearchParamForDescribeSlowLogsInput.


        :param min_query_time: The min_query_time of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: float
        """

        self._min_query_time = min_query_time

    @property
    def sql_template(self):
        """Gets the sql_template of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The sql_template of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: str
        """
        return self._sql_template

    @sql_template.setter
    def sql_template(self, sql_template):
        """Sets the sql_template of this SearchParamForDescribeSlowLogsInput.


        :param sql_template: The sql_template of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: str
        """

        self._sql_template = sql_template

    @property
    def sql_template_id(self):
        """Gets the sql_template_id of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The sql_template_id of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: str
        """
        return self._sql_template_id

    @sql_template_id.setter
    def sql_template_id(self, sql_template_id):
        """Sets the sql_template_id of this SearchParamForDescribeSlowLogsInput.


        :param sql_template_id: The sql_template_id of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: str
        """

        self._sql_template_id = sql_template_id

    @property
    def source_ips(self):
        """Gets the source_ips of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The source_ips of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._source_ips

    @source_ips.setter
    def source_ips(self, source_ips):
        """Sets the source_ips of this SearchParamForDescribeSlowLogsInput.


        :param source_ips: The source_ips of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: list[str]
        """

        self._source_ips = source_ips

    @property
    def users(self):
        """Gets the users of this SearchParamForDescribeSlowLogsInput.  # noqa: E501


        :return: The users of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._users

    @users.setter
    def users(self, users):
        """Sets the users of this SearchParamForDescribeSlowLogsInput.


        :param users: The users of this SearchParamForDescribeSlowLogsInput.  # noqa: E501
        :type: list[str]
        """

        self._users = users

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SearchParamForDescribeSlowLogsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SearchParamForDescribeSlowLogsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SearchParamForDescribeSlowLogsInput):
            return True

        return self.to_dict() != other.to_dict()
