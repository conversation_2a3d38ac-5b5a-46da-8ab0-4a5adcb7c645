# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RedirectConfigForModifyRulesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host': 'str',
        'path': 'str',
        'port': 'str',
        'protocol': 'str',
        'status_code': 'str'
    }

    attribute_map = {
        'host': 'Host',
        'path': 'Path',
        'port': 'Port',
        'protocol': 'Protocol',
        'status_code': 'StatusCode'
    }

    def __init__(self, host=None, path=None, port=None, protocol=None, status_code=None, _configuration=None):  # noqa: E501
        """RedirectConfigForModifyRulesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host = None
        self._path = None
        self._port = None
        self._protocol = None
        self._status_code = None
        self.discriminator = None

        if host is not None:
            self.host = host
        if path is not None:
            self.path = path
        if port is not None:
            self.port = port
        if protocol is not None:
            self.protocol = protocol
        if status_code is not None:
            self.status_code = status_code

    @property
    def host(self):
        """Gets the host of this RedirectConfigForModifyRulesInput.  # noqa: E501


        :return: The host of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this RedirectConfigForModifyRulesInput.


        :param host: The host of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def path(self):
        """Gets the path of this RedirectConfigForModifyRulesInput.  # noqa: E501


        :return: The path of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this RedirectConfigForModifyRulesInput.


        :param path: The path of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def port(self):
        """Gets the port of this RedirectConfigForModifyRulesInput.  # noqa: E501


        :return: The port of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this RedirectConfigForModifyRulesInput.


        :param port: The port of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._port = port

    @property
    def protocol(self):
        """Gets the protocol of this RedirectConfigForModifyRulesInput.  # noqa: E501


        :return: The protocol of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this RedirectConfigForModifyRulesInput.


        :param protocol: The protocol of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def status_code(self):
        """Gets the status_code of this RedirectConfigForModifyRulesInput.  # noqa: E501


        :return: The status_code of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._status_code

    @status_code.setter
    def status_code(self, status_code):
        """Sets the status_code of this RedirectConfigForModifyRulesInput.


        :param status_code: The status_code of this RedirectConfigForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._status_code = status_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RedirectConfigForModifyRulesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RedirectConfigForModifyRulesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RedirectConfigForModifyRulesInput):
            return True

        return self.to_dict() != other.to_dict()
