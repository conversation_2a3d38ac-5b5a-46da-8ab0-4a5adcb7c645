# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetFingerprintProcessRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'checksum': 'str',
        'cloud_providers': 'list[str]',
        'cmdline': 'str',
        'comm': 'str',
        'common': 'bool',
        'container': 'bool',
        'container_id': 'str',
        'exe': 'str',
        'hostname': 'str',
        'integrity': 'bool',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'start_time_end': 'int',
        'start_time_start': 'int',
        'tags': 'list[str]',
        'top_group_id': 'str',
        'username': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentId',
        'checksum': 'Checksum',
        'cloud_providers': 'CloudProviders',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'common': 'Common',
        'container': 'Container',
        'container_id': 'ContainerId',
        'exe': 'Exe',
        'hostname': 'Hostname',
        'integrity': 'Integrity',
        'ip': 'Ip',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'start_time_end': 'StartTimeEnd',
        'start_time_start': 'StartTimeStart',
        'tags': 'Tags',
        'top_group_id': 'TopGroupID',
        'username': 'Username'
    }

    def __init__(self, agent_id=None, checksum=None, cloud_providers=None, cmdline=None, comm=None, common=None, container=None, container_id=None, exe=None, hostname=None, integrity=None, ip=None, leaf_group_ids=None, page_number=None, page_size=None, sort_by=None, sort_order=None, start_time_end=None, start_time_start=None, tags=None, top_group_id=None, username=None, _configuration=None):  # noqa: E501
        """GetFingerprintProcessRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._checksum = None
        self._cloud_providers = None
        self._cmdline = None
        self._comm = None
        self._common = None
        self._container = None
        self._container_id = None
        self._exe = None
        self._hostname = None
        self._integrity = None
        self._ip = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._start_time_end = None
        self._start_time_start = None
        self._tags = None
        self._top_group_id = None
        self._username = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if checksum is not None:
            self.checksum = checksum
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if common is not None:
            self.common = common
        if container is not None:
            self.container = container
        if container_id is not None:
            self.container_id = container_id
        if exe is not None:
            self.exe = exe
        if hostname is not None:
            self.hostname = hostname
        if integrity is not None:
            self.integrity = integrity
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if start_time_end is not None:
            self.start_time_end = start_time_end
        if start_time_start is not None:
            self.start_time_start = start_time_start
        if tags is not None:
            self.tags = tags
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if username is not None:
            self.username = username

    @property
    def agent_id(self):
        """Gets the agent_id of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The agent_id of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this GetFingerprintProcessRequest.


        :param agent_id: The agent_id of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def checksum(self):
        """Gets the checksum of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The checksum of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._checksum

    @checksum.setter
    def checksum(self, checksum):
        """Sets the checksum of this GetFingerprintProcessRequest.


        :param checksum: The checksum of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._checksum = checksum

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The cloud_providers of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this GetFingerprintProcessRequest.


        :param cloud_providers: The cloud_providers of this GetFingerprintProcessRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cmdline(self):
        """Gets the cmdline of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The cmdline of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this GetFingerprintProcessRequest.


        :param cmdline: The cmdline of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The comm of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this GetFingerprintProcessRequest.


        :param comm: The comm of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def common(self):
        """Gets the common of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The common of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: bool
        """
        return self._common

    @common.setter
    def common(self, common):
        """Sets the common of this GetFingerprintProcessRequest.


        :param common: The common of this GetFingerprintProcessRequest.  # noqa: E501
        :type: bool
        """

        self._common = common

    @property
    def container(self):
        """Gets the container of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The container of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: bool
        """
        return self._container

    @container.setter
    def container(self, container):
        """Sets the container of this GetFingerprintProcessRequest.


        :param container: The container of this GetFingerprintProcessRequest.  # noqa: E501
        :type: bool
        """

        self._container = container

    @property
    def container_id(self):
        """Gets the container_id of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The container_id of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this GetFingerprintProcessRequest.


        :param container_id: The container_id of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def exe(self):
        """Gets the exe of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The exe of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this GetFingerprintProcessRequest.


        :param exe: The exe of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def hostname(self):
        """Gets the hostname of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The hostname of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this GetFingerprintProcessRequest.


        :param hostname: The hostname of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def integrity(self):
        """Gets the integrity of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The integrity of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: bool
        """
        return self._integrity

    @integrity.setter
    def integrity(self, integrity):
        """Sets the integrity of this GetFingerprintProcessRequest.


        :param integrity: The integrity of this GetFingerprintProcessRequest.  # noqa: E501
        :type: bool
        """

        self._integrity = integrity

    @property
    def ip(self):
        """Gets the ip of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The ip of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this GetFingerprintProcessRequest.


        :param ip: The ip of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The leaf_group_ids of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this GetFingerprintProcessRequest.


        :param leaf_group_ids: The leaf_group_ids of this GetFingerprintProcessRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The page_number of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetFingerprintProcessRequest.


        :param page_number: The page_number of this GetFingerprintProcessRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The page_size of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetFingerprintProcessRequest.


        :param page_size: The page_size of this GetFingerprintProcessRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The sort_by of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this GetFingerprintProcessRequest.


        :param sort_by: The sort_by of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The sort_order of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this GetFingerprintProcessRequest.


        :param sort_order: The sort_order of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def start_time_end(self):
        """Gets the start_time_end of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The start_time_end of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time_end

    @start_time_end.setter
    def start_time_end(self, start_time_end):
        """Sets the start_time_end of this GetFingerprintProcessRequest.


        :param start_time_end: The start_time_end of this GetFingerprintProcessRequest.  # noqa: E501
        :type: int
        """

        self._start_time_end = start_time_end

    @property
    def start_time_start(self):
        """Gets the start_time_start of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The start_time_start of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time_start

    @start_time_start.setter
    def start_time_start(self, start_time_start):
        """Sets the start_time_start of this GetFingerprintProcessRequest.


        :param start_time_start: The start_time_start of this GetFingerprintProcessRequest.  # noqa: E501
        :type: int
        """

        self._start_time_start = start_time_start

    @property
    def tags(self):
        """Gets the tags of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The tags of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this GetFingerprintProcessRequest.


        :param tags: The tags of this GetFingerprintProcessRequest.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def top_group_id(self):
        """Gets the top_group_id of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The top_group_id of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this GetFingerprintProcessRequest.


        :param top_group_id: The top_group_id of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def username(self):
        """Gets the username of this GetFingerprintProcessRequest.  # noqa: E501


        :return: The username of this GetFingerprintProcessRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this GetFingerprintProcessRequest.


        :param username: The username of this GetFingerprintProcessRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetFingerprintProcessRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetFingerprintProcessRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetFingerprintProcessRequest):
            return True

        return self.to_dict() != other.to_dict()
