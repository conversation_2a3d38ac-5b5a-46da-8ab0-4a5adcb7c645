# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StrategyForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affected_resource_type': 'str',
        'force_repair_action_name': 'str',
        'force_repair_action_uid': 'str',
        'repair_advice': 'str',
        'repair_support_automation': 'str',
        'resource_risk_status': 'str',
        'strategy_categories': 'list[str]',
        'strategy_desc': 'str',
        'strategy_id': 'str',
        'strategy_impact_type': 'str',
        'strategy_title': 'str',
        'strategy_type': 'str',
        'support_force_repair': 'bool',
        'task_running': 'bool'
    }

    attribute_map = {
        'affected_resource_type': 'AffectedResourceType',
        'force_repair_action_name': 'ForceRepairActionName',
        'force_repair_action_uid': 'ForceRepairActionUID',
        'repair_advice': 'RepairAdvice',
        'repair_support_automation': 'RepairSupportAutomation',
        'resource_risk_status': 'ResourceRiskStatus',
        'strategy_categories': 'StrategyCategories',
        'strategy_desc': 'StrategyDesc',
        'strategy_id': 'StrategyID',
        'strategy_impact_type': 'StrategyImpactType',
        'strategy_title': 'StrategyTitle',
        'strategy_type': 'StrategyType',
        'support_force_repair': 'SupportForceRepair',
        'task_running': 'TaskRunning'
    }

    def __init__(self, affected_resource_type=None, force_repair_action_name=None, force_repair_action_uid=None, repair_advice=None, repair_support_automation=None, resource_risk_status=None, strategy_categories=None, strategy_desc=None, strategy_id=None, strategy_impact_type=None, strategy_title=None, strategy_type=None, support_force_repair=None, task_running=None, _configuration=None):  # noqa: E501
        """StrategyForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affected_resource_type = None
        self._force_repair_action_name = None
        self._force_repair_action_uid = None
        self._repair_advice = None
        self._repair_support_automation = None
        self._resource_risk_status = None
        self._strategy_categories = None
        self._strategy_desc = None
        self._strategy_id = None
        self._strategy_impact_type = None
        self._strategy_title = None
        self._strategy_type = None
        self._support_force_repair = None
        self._task_running = None
        self.discriminator = None

        if affected_resource_type is not None:
            self.affected_resource_type = affected_resource_type
        if force_repair_action_name is not None:
            self.force_repair_action_name = force_repair_action_name
        if force_repair_action_uid is not None:
            self.force_repair_action_uid = force_repair_action_uid
        if repair_advice is not None:
            self.repair_advice = repair_advice
        if repair_support_automation is not None:
            self.repair_support_automation = repair_support_automation
        if resource_risk_status is not None:
            self.resource_risk_status = resource_risk_status
        if strategy_categories is not None:
            self.strategy_categories = strategy_categories
        if strategy_desc is not None:
            self.strategy_desc = strategy_desc
        if strategy_id is not None:
            self.strategy_id = strategy_id
        if strategy_impact_type is not None:
            self.strategy_impact_type = strategy_impact_type
        if strategy_title is not None:
            self.strategy_title = strategy_title
        if strategy_type is not None:
            self.strategy_type = strategy_type
        if support_force_repair is not None:
            self.support_force_repair = support_force_repair
        if task_running is not None:
            self.task_running = task_running

    @property
    def affected_resource_type(self):
        """Gets the affected_resource_type of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The affected_resource_type of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._affected_resource_type

    @affected_resource_type.setter
    def affected_resource_type(self, affected_resource_type):
        """Sets the affected_resource_type of this StrategyForGetRiskOutput.


        :param affected_resource_type: The affected_resource_type of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._affected_resource_type = affected_resource_type

    @property
    def force_repair_action_name(self):
        """Gets the force_repair_action_name of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The force_repair_action_name of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._force_repair_action_name

    @force_repair_action_name.setter
    def force_repair_action_name(self, force_repair_action_name):
        """Sets the force_repair_action_name of this StrategyForGetRiskOutput.


        :param force_repair_action_name: The force_repair_action_name of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._force_repair_action_name = force_repair_action_name

    @property
    def force_repair_action_uid(self):
        """Gets the force_repair_action_uid of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The force_repair_action_uid of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._force_repair_action_uid

    @force_repair_action_uid.setter
    def force_repair_action_uid(self, force_repair_action_uid):
        """Sets the force_repair_action_uid of this StrategyForGetRiskOutput.


        :param force_repair_action_uid: The force_repair_action_uid of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._force_repair_action_uid = force_repair_action_uid

    @property
    def repair_advice(self):
        """Gets the repair_advice of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The repair_advice of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._repair_advice

    @repair_advice.setter
    def repair_advice(self, repair_advice):
        """Sets the repair_advice of this StrategyForGetRiskOutput.


        :param repair_advice: The repair_advice of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._repair_advice = repair_advice

    @property
    def repair_support_automation(self):
        """Gets the repair_support_automation of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The repair_support_automation of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._repair_support_automation

    @repair_support_automation.setter
    def repair_support_automation(self, repair_support_automation):
        """Sets the repair_support_automation of this StrategyForGetRiskOutput.


        :param repair_support_automation: The repair_support_automation of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._repair_support_automation = repair_support_automation

    @property
    def resource_risk_status(self):
        """Gets the resource_risk_status of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The resource_risk_status of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_risk_status

    @resource_risk_status.setter
    def resource_risk_status(self, resource_risk_status):
        """Sets the resource_risk_status of this StrategyForGetRiskOutput.


        :param resource_risk_status: The resource_risk_status of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._resource_risk_status = resource_risk_status

    @property
    def strategy_categories(self):
        """Gets the strategy_categories of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_categories of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._strategy_categories

    @strategy_categories.setter
    def strategy_categories(self, strategy_categories):
        """Sets the strategy_categories of this StrategyForGetRiskOutput.


        :param strategy_categories: The strategy_categories of this StrategyForGetRiskOutput.  # noqa: E501
        :type: list[str]
        """

        self._strategy_categories = strategy_categories

    @property
    def strategy_desc(self):
        """Gets the strategy_desc of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_desc of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_desc

    @strategy_desc.setter
    def strategy_desc(self, strategy_desc):
        """Sets the strategy_desc of this StrategyForGetRiskOutput.


        :param strategy_desc: The strategy_desc of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._strategy_desc = strategy_desc

    @property
    def strategy_id(self):
        """Gets the strategy_id of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_id of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_id

    @strategy_id.setter
    def strategy_id(self, strategy_id):
        """Sets the strategy_id of this StrategyForGetRiskOutput.


        :param strategy_id: The strategy_id of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._strategy_id = strategy_id

    @property
    def strategy_impact_type(self):
        """Gets the strategy_impact_type of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_impact_type of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_impact_type

    @strategy_impact_type.setter
    def strategy_impact_type(self, strategy_impact_type):
        """Sets the strategy_impact_type of this StrategyForGetRiskOutput.


        :param strategy_impact_type: The strategy_impact_type of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._strategy_impact_type = strategy_impact_type

    @property
    def strategy_title(self):
        """Gets the strategy_title of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_title of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_title

    @strategy_title.setter
    def strategy_title(self, strategy_title):
        """Sets the strategy_title of this StrategyForGetRiskOutput.


        :param strategy_title: The strategy_title of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._strategy_title = strategy_title

    @property
    def strategy_type(self):
        """Gets the strategy_type of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The strategy_type of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_type

    @strategy_type.setter
    def strategy_type(self, strategy_type):
        """Sets the strategy_type of this StrategyForGetRiskOutput.


        :param strategy_type: The strategy_type of this StrategyForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._strategy_type = strategy_type

    @property
    def support_force_repair(self):
        """Gets the support_force_repair of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The support_force_repair of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: bool
        """
        return self._support_force_repair

    @support_force_repair.setter
    def support_force_repair(self, support_force_repair):
        """Sets the support_force_repair of this StrategyForGetRiskOutput.


        :param support_force_repair: The support_force_repair of this StrategyForGetRiskOutput.  # noqa: E501
        :type: bool
        """

        self._support_force_repair = support_force_repair

    @property
    def task_running(self):
        """Gets the task_running of this StrategyForGetRiskOutput.  # noqa: E501


        :return: The task_running of this StrategyForGetRiskOutput.  # noqa: E501
        :rtype: bool
        """
        return self._task_running

    @task_running.setter
    def task_running(self, task_running):
        """Sets the task_running of this StrategyForGetRiskOutput.


        :param task_running: The task_running of this StrategyForGetRiskOutput.  # noqa: E501
        :type: bool
        """

        self._task_running = task_running

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StrategyForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StrategyForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StrategyForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
