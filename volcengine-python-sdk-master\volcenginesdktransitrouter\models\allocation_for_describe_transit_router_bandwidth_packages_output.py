# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AllocationForDescribeTransitRouterBandwidthPackagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocate_time': 'str',
        'bandwidth': 'int',
        'local_region_id': 'str',
        'peer_region_id': 'str',
        'transit_router_peer_attachment_id': 'str'
    }

    attribute_map = {
        'allocate_time': 'AllocateTime',
        'bandwidth': 'Bandwidth',
        'local_region_id': 'LocalRegionId',
        'peer_region_id': 'PeerRegionId',
        'transit_router_peer_attachment_id': 'TransitRouterPeerAttachmentId'
    }

    def __init__(self, allocate_time=None, bandwidth=None, local_region_id=None, peer_region_id=None, transit_router_peer_attachment_id=None, _configuration=None):  # noqa: E501
        """AllocationForDescribeTransitRouterBandwidthPackagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocate_time = None
        self._bandwidth = None
        self._local_region_id = None
        self._peer_region_id = None
        self._transit_router_peer_attachment_id = None
        self.discriminator = None

        if allocate_time is not None:
            self.allocate_time = allocate_time
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if local_region_id is not None:
            self.local_region_id = local_region_id
        if peer_region_id is not None:
            self.peer_region_id = peer_region_id
        if transit_router_peer_attachment_id is not None:
            self.transit_router_peer_attachment_id = transit_router_peer_attachment_id

    @property
    def allocate_time(self):
        """Gets the allocate_time of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The allocate_time of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._allocate_time

    @allocate_time.setter
    def allocate_time(self, allocate_time):
        """Sets the allocate_time of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.


        :param allocate_time: The allocate_time of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._allocate_time = allocate_time

    @property
    def bandwidth(self):
        """Gets the bandwidth of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The bandwidth of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.


        :param bandwidth: The bandwidth of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def local_region_id(self):
        """Gets the local_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The local_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._local_region_id

    @local_region_id.setter
    def local_region_id(self, local_region_id):
        """Sets the local_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.


        :param local_region_id: The local_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._local_region_id = local_region_id

    @property
    def peer_region_id(self):
        """Gets the peer_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The peer_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._peer_region_id

    @peer_region_id.setter
    def peer_region_id(self, peer_region_id):
        """Sets the peer_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.


        :param peer_region_id: The peer_region_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._peer_region_id = peer_region_id

    @property
    def transit_router_peer_attachment_id(self):
        """Gets the transit_router_peer_attachment_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501


        :return: The transit_router_peer_attachment_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_peer_attachment_id

    @transit_router_peer_attachment_id.setter
    def transit_router_peer_attachment_id(self, transit_router_peer_attachment_id):
        """Sets the transit_router_peer_attachment_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.


        :param transit_router_peer_attachment_id: The transit_router_peer_attachment_id of this AllocationForDescribeTransitRouterBandwidthPackagesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_peer_attachment_id = transit_router_peer_attachment_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AllocationForDescribeTransitRouterBandwidthPackagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AllocationForDescribeTransitRouterBandwidthPackagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AllocationForDescribeTransitRouterBandwidthPackagesOutput):
            return True

        return self.to_dict() != other.to_dict()
