# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubscriptionForDescribeSubscriptionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'event_types': 'list[str]',
        'id': 'str',
        'type': 'str',
        'updated_at': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'event_types': 'EventTypes',
        'id': 'Id',
        'type': 'Type',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, created_at=None, event_types=None, id=None, type=None, updated_at=None, _configuration=None):  # noqa: E501
        """SubscriptionForDescribeSubscriptionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._event_types = None
        self._id = None
        self._type = None
        self._updated_at = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if event_types is not None:
            self.event_types = event_types
        if id is not None:
            self.id = id
        if type is not None:
            self.type = type
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def created_at(self):
        """Gets the created_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501


        :return: The created_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this SubscriptionForDescribeSubscriptionsOutput.


        :param created_at: The created_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def event_types(self):
        """Gets the event_types of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501


        :return: The event_types of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_types

    @event_types.setter
    def event_types(self, event_types):
        """Sets the event_types of this SubscriptionForDescribeSubscriptionsOutput.


        :param event_types: The event_types of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :type: list[str]
        """

        self._event_types = event_types

    @property
    def id(self):
        """Gets the id of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501


        :return: The id of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SubscriptionForDescribeSubscriptionsOutput.


        :param id: The id of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def type(self):
        """Gets the type of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501


        :return: The type of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this SubscriptionForDescribeSubscriptionsOutput.


        :param type: The type of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def updated_at(self):
        """Gets the updated_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501


        :return: The updated_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this SubscriptionForDescribeSubscriptionsOutput.


        :param updated_at: The updated_at of this SubscriptionForDescribeSubscriptionsOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubscriptionForDescribeSubscriptionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubscriptionForDescribeSubscriptionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubscriptionForDescribeSubscriptionsOutput):
            return True

        return self.to_dict() != other.to_dict()
