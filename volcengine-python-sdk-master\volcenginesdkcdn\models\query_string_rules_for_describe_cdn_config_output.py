# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryStringRulesForDescribeCdnConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'query_string_components': 'QueryStringComponentsForDescribeCdnConfigOutput',
        'query_string_instances': 'list[QueryStringInstanceForDescribeCdnConfigOutput]'
    }

    attribute_map = {
        'query_string_components': 'QueryStringComponents',
        'query_string_instances': 'QueryStringInstances'
    }

    def __init__(self, query_string_components=None, query_string_instances=None, _configuration=None):  # noqa: E501
        """QueryStringRulesForDescribeCdnConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._query_string_components = None
        self._query_string_instances = None
        self.discriminator = None

        if query_string_components is not None:
            self.query_string_components = query_string_components
        if query_string_instances is not None:
            self.query_string_instances = query_string_instances

    @property
    def query_string_components(self):
        """Gets the query_string_components of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501


        :return: The query_string_components of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: QueryStringComponentsForDescribeCdnConfigOutput
        """
        return self._query_string_components

    @query_string_components.setter
    def query_string_components(self, query_string_components):
        """Sets the query_string_components of this QueryStringRulesForDescribeCdnConfigOutput.


        :param query_string_components: The query_string_components of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501
        :type: QueryStringComponentsForDescribeCdnConfigOutput
        """

        self._query_string_components = query_string_components

    @property
    def query_string_instances(self):
        """Gets the query_string_instances of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501


        :return: The query_string_instances of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501
        :rtype: list[QueryStringInstanceForDescribeCdnConfigOutput]
        """
        return self._query_string_instances

    @query_string_instances.setter
    def query_string_instances(self, query_string_instances):
        """Sets the query_string_instances of this QueryStringRulesForDescribeCdnConfigOutput.


        :param query_string_instances: The query_string_instances of this QueryStringRulesForDescribeCdnConfigOutput.  # noqa: E501
        :type: list[QueryStringInstanceForDescribeCdnConfigOutput]
        """

        self._query_string_instances = query_string_instances

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryStringRulesForDescribeCdnConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryStringRulesForDescribeCdnConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryStringRulesForDescribeCdnConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
