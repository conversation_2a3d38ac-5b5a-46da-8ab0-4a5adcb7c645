# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceBackupPolicyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_backup': 'str',
        'data_backup_retention_day': 'int',
        'data_full_backup_periods': 'list[str]',
        'data_full_backup_time': 'str'
    }

    attribute_map = {
        'auto_backup': 'AutoBackup',
        'data_backup_retention_day': 'DataBackupRetentionDay',
        'data_full_backup_periods': 'DataFullBackupPeriods',
        'data_full_backup_time': 'DataFullBackupTime'
    }

    def __init__(self, auto_backup=None, data_backup_retention_day=None, data_full_backup_periods=None, data_full_backup_time=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceBackupPolicyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_backup = None
        self._data_backup_retention_day = None
        self._data_full_backup_periods = None
        self._data_full_backup_time = None
        self.discriminator = None

        if auto_backup is not None:
            self.auto_backup = auto_backup
        if data_backup_retention_day is not None:
            self.data_backup_retention_day = data_backup_retention_day
        if data_full_backup_periods is not None:
            self.data_full_backup_periods = data_full_backup_periods
        if data_full_backup_time is not None:
            self.data_full_backup_time = data_full_backup_time

    @property
    def auto_backup(self):
        """Gets the auto_backup of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501


        :return: The auto_backup of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :rtype: str
        """
        return self._auto_backup

    @auto_backup.setter
    def auto_backup(self, auto_backup):
        """Sets the auto_backup of this DescribeDBInstanceBackupPolicyResponse.


        :param auto_backup: The auto_backup of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :type: str
        """

        self._auto_backup = auto_backup

    @property
    def data_backup_retention_day(self):
        """Gets the data_backup_retention_day of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501


        :return: The data_backup_retention_day of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :rtype: int
        """
        return self._data_backup_retention_day

    @data_backup_retention_day.setter
    def data_backup_retention_day(self, data_backup_retention_day):
        """Sets the data_backup_retention_day of this DescribeDBInstanceBackupPolicyResponse.


        :param data_backup_retention_day: The data_backup_retention_day of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :type: int
        """

        self._data_backup_retention_day = data_backup_retention_day

    @property
    def data_full_backup_periods(self):
        """Gets the data_full_backup_periods of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501


        :return: The data_full_backup_periods of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_full_backup_periods

    @data_full_backup_periods.setter
    def data_full_backup_periods(self, data_full_backup_periods):
        """Sets the data_full_backup_periods of this DescribeDBInstanceBackupPolicyResponse.


        :param data_full_backup_periods: The data_full_backup_periods of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :type: list[str]
        """

        self._data_full_backup_periods = data_full_backup_periods

    @property
    def data_full_backup_time(self):
        """Gets the data_full_backup_time of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501


        :return: The data_full_backup_time of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_full_backup_time

    @data_full_backup_time.setter
    def data_full_backup_time(self, data_full_backup_time):
        """Sets the data_full_backup_time of this DescribeDBInstanceBackupPolicyResponse.


        :param data_full_backup_time: The data_full_backup_time of this DescribeDBInstanceBackupPolicyResponse.  # noqa: E501
        :type: str
        """

        self._data_full_backup_time = data_full_backup_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceBackupPolicyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceBackupPolicyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceBackupPolicyResponse):
            return True

        return self.to_dict() != other.to_dict()
