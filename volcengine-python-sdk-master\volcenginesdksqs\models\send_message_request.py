# coding: utf-8

"""
    sqs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SendMessageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message_body': 'str',
        'queue_trn': 'str'
    }

    attribute_map = {
        'message_body': 'MessageBody',
        'queue_trn': 'QueueTrn'
    }

    def __init__(self, message_body=None, queue_trn=None, _configuration=None):  # noqa: E501
        """SendMessageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message_body = None
        self._queue_trn = None
        self.discriminator = None

        self.message_body = message_body
        self.queue_trn = queue_trn

    @property
    def message_body(self):
        """Gets the message_body of this SendMessageRequest.  # noqa: E501


        :return: The message_body of this SendMessageRequest.  # noqa: E501
        :rtype: str
        """
        return self._message_body

    @message_body.setter
    def message_body(self, message_body):
        """Sets the message_body of this SendMessageRequest.


        :param message_body: The message_body of this SendMessageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and message_body is None:
            raise ValueError("Invalid value for `message_body`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                message_body is not None and len(message_body) > 262144):
            raise ValueError("Invalid value for `message_body`, length must be less than or equal to `262144`")  # noqa: E501
        if (self._configuration.client_side_validation and
                message_body is not None and len(message_body) < 1):
            raise ValueError("Invalid value for `message_body`, length must be greater than or equal to `1`")  # noqa: E501

        self._message_body = message_body

    @property
    def queue_trn(self):
        """Gets the queue_trn of this SendMessageRequest.  # noqa: E501


        :return: The queue_trn of this SendMessageRequest.  # noqa: E501
        :rtype: str
        """
        return self._queue_trn

    @queue_trn.setter
    def queue_trn(self, queue_trn):
        """Sets the queue_trn of this SendMessageRequest.


        :param queue_trn: The queue_trn of this SendMessageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and queue_trn is None:
            raise ValueError("Invalid value for `queue_trn`, must not be `None`")  # noqa: E501

        self._queue_trn = queue_trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SendMessageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SendMessageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SendMessageRequest):
            return True

        return self.to_dict() != other.to_dict()
