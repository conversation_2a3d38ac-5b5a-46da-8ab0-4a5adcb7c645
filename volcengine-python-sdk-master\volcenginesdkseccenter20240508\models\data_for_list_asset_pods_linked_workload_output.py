# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAssetPodsLinkedWorkloadOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'int',
        'id': 'str',
        'name': 'str',
        'namespace': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'id': 'ID',
        'name': 'Name',
        'namespace': 'Namespace'
    }

    def __init__(self, creation_time=None, id=None, name=None, namespace=None, _configuration=None):  # noqa: E501
        """DataForListAssetPodsLinkedWorkloadOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._id = None
        self._name = None
        self._namespace = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if namespace is not None:
            self.namespace = namespace

    @property
    def creation_time(self):
        """Gets the creation_time of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501


        :return: The creation_time of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DataForListAssetPodsLinkedWorkloadOutput.


        :param creation_time: The creation_time of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :type: int
        """

        self._creation_time = creation_time

    @property
    def id(self):
        """Gets the id of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501


        :return: The id of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListAssetPodsLinkedWorkloadOutput.


        :param id: The id of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501


        :return: The name of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListAssetPodsLinkedWorkloadOutput.


        :param name: The name of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def namespace(self):
        """Gets the namespace of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501


        :return: The namespace of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListAssetPodsLinkedWorkloadOutput.


        :param namespace: The namespace of this DataForListAssetPodsLinkedWorkloadOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAssetPodsLinkedWorkloadOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAssetPodsLinkedWorkloadOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAssetPodsLinkedWorkloadOutput):
            return True

        return self.to_dict() != other.to_dict()
