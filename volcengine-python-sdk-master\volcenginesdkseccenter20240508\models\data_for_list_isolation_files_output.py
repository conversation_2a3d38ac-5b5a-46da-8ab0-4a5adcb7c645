# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListIsolationFilesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'agent_id': 'str',
        'alarm_id': 'str',
        'alarm_type': 'str',
        'auto_isolate': 'bool',
        'error_reason': 'str',
        'file_hash': 'str',
        'file_path': 'str',
        'filebox_id': 'str',
        'group_id': 'str',
        'host': 'HostForListIsolationFilesOutput',
        'insert_time': 'int',
        'is_upload': 'bool',
        'saved_file_name': 'str',
        'status': 'int',
        'task_id': 'str',
        'top_group_id': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'agent_id': 'AgentID',
        'alarm_id': 'AlarmID',
        'alarm_type': 'AlarmType',
        'auto_isolate': 'AutoIsolate',
        'error_reason': 'ErrorReason',
        'file_hash': 'FileHash',
        'file_path': 'FilePath',
        'filebox_id': 'FileboxID',
        'group_id': 'GroupID',
        'host': 'Host',
        'insert_time': 'InsertTime',
        'is_upload': 'IsUpload',
        'saved_file_name': 'SavedFileName',
        'status': 'Status',
        'task_id': 'TaskID',
        'top_group_id': 'TopGroupID',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, agent_id=None, alarm_id=None, alarm_type=None, auto_isolate=None, error_reason=None, file_hash=None, file_path=None, filebox_id=None, group_id=None, host=None, insert_time=None, is_upload=None, saved_file_name=None, status=None, task_id=None, top_group_id=None, update_time=None, _configuration=None):  # noqa: E501
        """DataForListIsolationFilesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._agent_id = None
        self._alarm_id = None
        self._alarm_type = None
        self._auto_isolate = None
        self._error_reason = None
        self._file_hash = None
        self._file_path = None
        self._filebox_id = None
        self._group_id = None
        self._host = None
        self._insert_time = None
        self._is_upload = None
        self._saved_file_name = None
        self._status = None
        self._task_id = None
        self._top_group_id = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if agent_id is not None:
            self.agent_id = agent_id
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alarm_type is not None:
            self.alarm_type = alarm_type
        if auto_isolate is not None:
            self.auto_isolate = auto_isolate
        if error_reason is not None:
            self.error_reason = error_reason
        if file_hash is not None:
            self.file_hash = file_hash
        if file_path is not None:
            self.file_path = file_path
        if filebox_id is not None:
            self.filebox_id = filebox_id
        if group_id is not None:
            self.group_id = group_id
        if host is not None:
            self.host = host
        if insert_time is not None:
            self.insert_time = insert_time
        if is_upload is not None:
            self.is_upload = is_upload
        if saved_file_name is not None:
            self.saved_file_name = saved_file_name
        if status is not None:
            self.status = status
        if task_id is not None:
            self.task_id = task_id
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The account_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListIsolationFilesOutput.


        :param account_id: The account_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The agent_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListIsolationFilesOutput.


        :param agent_id: The agent_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def alarm_id(self):
        """Gets the alarm_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The alarm_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this DataForListIsolationFilesOutput.


        :param alarm_id: The alarm_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alarm_type(self):
        """Gets the alarm_type of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The alarm_type of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this DataForListIsolationFilesOutput.


        :param alarm_type: The alarm_type of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def auto_isolate(self):
        """Gets the auto_isolate of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The auto_isolate of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_isolate

    @auto_isolate.setter
    def auto_isolate(self, auto_isolate):
        """Sets the auto_isolate of this DataForListIsolationFilesOutput.


        :param auto_isolate: The auto_isolate of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: bool
        """

        self._auto_isolate = auto_isolate

    @property
    def error_reason(self):
        """Gets the error_reason of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The error_reason of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_reason

    @error_reason.setter
    def error_reason(self, error_reason):
        """Sets the error_reason of this DataForListIsolationFilesOutput.


        :param error_reason: The error_reason of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._error_reason = error_reason

    @property
    def file_hash(self):
        """Gets the file_hash of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The file_hash of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_hash

    @file_hash.setter
    def file_hash(self, file_hash):
        """Sets the file_hash of this DataForListIsolationFilesOutput.


        :param file_hash: The file_hash of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._file_hash = file_hash

    @property
    def file_path(self):
        """Gets the file_path of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The file_path of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this DataForListIsolationFilesOutput.


        :param file_path: The file_path of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def filebox_id(self):
        """Gets the filebox_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The filebox_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._filebox_id

    @filebox_id.setter
    def filebox_id(self, filebox_id):
        """Sets the filebox_id of this DataForListIsolationFilesOutput.


        :param filebox_id: The filebox_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._filebox_id = filebox_id

    @property
    def group_id(self):
        """Gets the group_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The group_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this DataForListIsolationFilesOutput.


        :param group_id: The group_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def host(self):
        """Gets the host of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The host of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: HostForListIsolationFilesOutput
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this DataForListIsolationFilesOutput.


        :param host: The host of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: HostForListIsolationFilesOutput
        """

        self._host = host

    @property
    def insert_time(self):
        """Gets the insert_time of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The insert_time of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: int
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this DataForListIsolationFilesOutput.


        :param insert_time: The insert_time of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: int
        """

        self._insert_time = insert_time

    @property
    def is_upload(self):
        """Gets the is_upload of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The is_upload of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_upload

    @is_upload.setter
    def is_upload(self, is_upload):
        """Sets the is_upload of this DataForListIsolationFilesOutput.


        :param is_upload: The is_upload of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: bool
        """

        self._is_upload = is_upload

    @property
    def saved_file_name(self):
        """Gets the saved_file_name of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The saved_file_name of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._saved_file_name

    @saved_file_name.setter
    def saved_file_name(self, saved_file_name):
        """Sets the saved_file_name of this DataForListIsolationFilesOutput.


        :param saved_file_name: The saved_file_name of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._saved_file_name = saved_file_name

    @property
    def status(self):
        """Gets the status of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The status of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListIsolationFilesOutput.


        :param status: The status of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def task_id(self):
        """Gets the task_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The task_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DataForListIsolationFilesOutput.


        :param task_id: The task_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def top_group_id(self):
        """Gets the top_group_id of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The top_group_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this DataForListIsolationFilesOutput.


        :param top_group_id: The top_group_id of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def update_time(self):
        """Gets the update_time of this DataForListIsolationFilesOutput.  # noqa: E501


        :return: The update_time of this DataForListIsolationFilesOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListIsolationFilesOutput.


        :param update_time: The update_time of this DataForListIsolationFilesOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListIsolationFilesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListIsolationFilesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListIsolationFilesOutput):
            return True

        return self.to_dict() != other.to_dict()
