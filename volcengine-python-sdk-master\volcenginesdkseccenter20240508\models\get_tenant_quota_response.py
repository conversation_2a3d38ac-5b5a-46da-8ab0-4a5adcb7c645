# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTenantQuotaResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'basic_quota': 'BasicQuotaForGetTenantQuotaOutput',
        'expire_time': 'int',
        'id': 'str',
        'insert_time': 'int',
        'labels': 'list[str]',
        'update_time': 'int',
        'value_added': 'ValueAddedForGetTenantQuotaOutput'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'basic_quota': 'BasicQuota',
        'expire_time': 'ExpireTime',
        'id': 'ID',
        'insert_time': 'InsertTime',
        'labels': 'Labels',
        'update_time': 'UpdateTime',
        'value_added': 'ValueAdded'
    }

    def __init__(self, account_id=None, basic_quota=None, expire_time=None, id=None, insert_time=None, labels=None, update_time=None, value_added=None, _configuration=None):  # noqa: E501
        """GetTenantQuotaResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._basic_quota = None
        self._expire_time = None
        self._id = None
        self._insert_time = None
        self._labels = None
        self._update_time = None
        self._value_added = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if basic_quota is not None:
            self.basic_quota = basic_quota
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if insert_time is not None:
            self.insert_time = insert_time
        if labels is not None:
            self.labels = labels
        if update_time is not None:
            self.update_time = update_time
        if value_added is not None:
            self.value_added = value_added

    @property
    def account_id(self):
        """Gets the account_id of this GetTenantQuotaResponse.  # noqa: E501


        :return: The account_id of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this GetTenantQuotaResponse.


        :param account_id: The account_id of this GetTenantQuotaResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def basic_quota(self):
        """Gets the basic_quota of this GetTenantQuotaResponse.  # noqa: E501


        :return: The basic_quota of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: BasicQuotaForGetTenantQuotaOutput
        """
        return self._basic_quota

    @basic_quota.setter
    def basic_quota(self, basic_quota):
        """Sets the basic_quota of this GetTenantQuotaResponse.


        :param basic_quota: The basic_quota of this GetTenantQuotaResponse.  # noqa: E501
        :type: BasicQuotaForGetTenantQuotaOutput
        """

        self._basic_quota = basic_quota

    @property
    def expire_time(self):
        """Gets the expire_time of this GetTenantQuotaResponse.  # noqa: E501


        :return: The expire_time of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this GetTenantQuotaResponse.


        :param expire_time: The expire_time of this GetTenantQuotaResponse.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this GetTenantQuotaResponse.  # noqa: E501


        :return: The id of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetTenantQuotaResponse.


        :param id: The id of this GetTenantQuotaResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def insert_time(self):
        """Gets the insert_time of this GetTenantQuotaResponse.  # noqa: E501


        :return: The insert_time of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this GetTenantQuotaResponse.


        :param insert_time: The insert_time of this GetTenantQuotaResponse.  # noqa: E501
        :type: int
        """

        self._insert_time = insert_time

    @property
    def labels(self):
        """Gets the labels of this GetTenantQuotaResponse.  # noqa: E501


        :return: The labels of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this GetTenantQuotaResponse.


        :param labels: The labels of this GetTenantQuotaResponse.  # noqa: E501
        :type: list[str]
        """

        self._labels = labels

    @property
    def update_time(self):
        """Gets the update_time of this GetTenantQuotaResponse.  # noqa: E501


        :return: The update_time of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetTenantQuotaResponse.


        :param update_time: The update_time of this GetTenantQuotaResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def value_added(self):
        """Gets the value_added of this GetTenantQuotaResponse.  # noqa: E501


        :return: The value_added of this GetTenantQuotaResponse.  # noqa: E501
        :rtype: ValueAddedForGetTenantQuotaOutput
        """
        return self._value_added

    @value_added.setter
    def value_added(self, value_added):
        """Sets the value_added of this GetTenantQuotaResponse.


        :param value_added: The value_added of this GetTenantQuotaResponse.  # noqa: E501
        :type: ValueAddedForGetTenantQuotaOutput
        """

        self._value_added = value_added

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTenantQuotaResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTenantQuotaResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTenantQuotaResponse):
            return True

        return self.to_dict() != other.to_dict()
