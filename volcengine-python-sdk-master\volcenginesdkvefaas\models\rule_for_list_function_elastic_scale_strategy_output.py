# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RuleForListFunctionElasticScaleStrategyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregate_seconds': 'int',
        'aggregate_type': 'str',
        'metric_type': 'str',
        'scale_down_threshold': 'int',
        'scale_up_threshold': 'int',
        'target': 'int'
    }

    attribute_map = {
        'aggregate_seconds': 'AggregateSeconds',
        'aggregate_type': 'AggregateType',
        'metric_type': 'MetricType',
        'scale_down_threshold': 'ScaleDownThreshold',
        'scale_up_threshold': 'ScaleUpThreshold',
        'target': 'Target'
    }

    def __init__(self, aggregate_seconds=None, aggregate_type=None, metric_type=None, scale_down_threshold=None, scale_up_threshold=None, target=None, _configuration=None):  # noqa: E501
        """RuleForListFunctionElasticScaleStrategyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregate_seconds = None
        self._aggregate_type = None
        self._metric_type = None
        self._scale_down_threshold = None
        self._scale_up_threshold = None
        self._target = None
        self.discriminator = None

        if aggregate_seconds is not None:
            self.aggregate_seconds = aggregate_seconds
        if aggregate_type is not None:
            self.aggregate_type = aggregate_type
        if metric_type is not None:
            self.metric_type = metric_type
        if scale_down_threshold is not None:
            self.scale_down_threshold = scale_down_threshold
        if scale_up_threshold is not None:
            self.scale_up_threshold = scale_up_threshold
        if target is not None:
            self.target = target

    @property
    def aggregate_seconds(self):
        """Gets the aggregate_seconds of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The aggregate_seconds of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: int
        """
        return self._aggregate_seconds

    @aggregate_seconds.setter
    def aggregate_seconds(self, aggregate_seconds):
        """Sets the aggregate_seconds of this RuleForListFunctionElasticScaleStrategyOutput.


        :param aggregate_seconds: The aggregate_seconds of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: int
        """

        self._aggregate_seconds = aggregate_seconds

    @property
    def aggregate_type(self):
        """Gets the aggregate_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The aggregate_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: str
        """
        return self._aggregate_type

    @aggregate_type.setter
    def aggregate_type(self, aggregate_type):
        """Sets the aggregate_type of this RuleForListFunctionElasticScaleStrategyOutput.


        :param aggregate_type: The aggregate_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: str
        """

        self._aggregate_type = aggregate_type

    @property
    def metric_type(self):
        """Gets the metric_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The metric_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: str
        """
        return self._metric_type

    @metric_type.setter
    def metric_type(self, metric_type):
        """Sets the metric_type of this RuleForListFunctionElasticScaleStrategyOutput.


        :param metric_type: The metric_type of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: str
        """

        self._metric_type = metric_type

    @property
    def scale_down_threshold(self):
        """Gets the scale_down_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The scale_down_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: int
        """
        return self._scale_down_threshold

    @scale_down_threshold.setter
    def scale_down_threshold(self, scale_down_threshold):
        """Sets the scale_down_threshold of this RuleForListFunctionElasticScaleStrategyOutput.


        :param scale_down_threshold: The scale_down_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: int
        """

        self._scale_down_threshold = scale_down_threshold

    @property
    def scale_up_threshold(self):
        """Gets the scale_up_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The scale_up_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: int
        """
        return self._scale_up_threshold

    @scale_up_threshold.setter
    def scale_up_threshold(self, scale_up_threshold):
        """Sets the scale_up_threshold of this RuleForListFunctionElasticScaleStrategyOutput.


        :param scale_up_threshold: The scale_up_threshold of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: int
        """

        self._scale_up_threshold = scale_up_threshold

    @property
    def target(self):
        """Gets the target of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501


        :return: The target of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :rtype: int
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this RuleForListFunctionElasticScaleStrategyOutput.


        :param target: The target of this RuleForListFunctionElasticScaleStrategyOutput.  # noqa: E501
        :type: int
        """

        self._target = target

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RuleForListFunctionElasticScaleStrategyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RuleForListFunctionElasticScaleStrategyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RuleForListFunctionElasticScaleStrategyOutput):
            return True

        return self.to_dict() != other.to_dict()
