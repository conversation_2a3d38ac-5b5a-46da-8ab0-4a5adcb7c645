# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateCdnConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area_access_rule': 'AreaAccessRuleForUpdateCdnConfigInput',
        'bandwidth_limit': 'BandwidthLimitForUpdateCdnConfigInput',
        'browser_cache': 'list[BrowserCacheForUpdateCdnConfigInput]',
        'cache': 'list[CacheForUpdateCdnConfigInput]',
        'cache_host': 'CacheHostForUpdateCdnConfigInput',
        'cache_key': 'list[CacheKeyForUpdateCdnConfigInput]',
        'compression': 'CompressionForUpdateCdnConfigInput',
        'conditional_origin': 'ConditionalOriginForUpdateCdnConfigInput',
        'custom_error_page': 'CustomErrorPageForUpdateCdnConfigInput',
        'customize_access_rule': 'CustomizeAccessRuleForUpdateCdnConfigInput',
        'domain': 'str',
        'download_speed_limit': 'DownloadSpeedLimitForUpdateCdnConfigInput',
        'follow_redirect': 'bool',
        'https': 'HTTPSForUpdateCdnConfigInput',
        'http_forced_redirect': 'HttpForcedRedirectForUpdateCdnConfigInput',
        'ipv6': 'IPv6ForUpdateCdnConfigInput',
        'ip_access_rule': 'IpAccessRuleForUpdateCdnConfigInput',
        'ip_freq_limit': 'IpFreqLimitForUpdateCdnConfigInput',
        'method_denied_rule': 'MethodDeniedRuleForUpdateCdnConfigInput',
        'multi_range': 'MultiRangeForUpdateCdnConfigInput',
        'negative_cache': 'list[NegativeCacheForUpdateCdnConfigInput]',
        'offline_cache': 'OfflineCacheForUpdateCdnConfigInput',
        'origin': 'list[OriginForUpdateCdnConfigInput]',
        'origin_access_rule': 'OriginAccessRuleForUpdateCdnConfigInput',
        'origin_arg': 'list[OriginArgForUpdateCdnConfigInput]',
        'origin_cert_check': 'OriginCertCheckForUpdateCdnConfigInput',
        'origin_host': 'str',
        'origin_ipv6': 'str',
        'origin_protocol': 'str',
        'origin_range': 'bool',
        'origin_retry': 'OriginRetryForUpdateCdnConfigInput',
        'origin_rewrite': 'OriginRewriteForUpdateCdnConfigInput',
        'origin_sni': 'OriginSniForUpdateCdnConfigInput',
        'page_optimization': 'PageOptimizationForUpdateCdnConfigInput',
        'quic': 'QuicForUpdateCdnConfigInput',
        'redirection_rewrite': 'RedirectionRewriteForUpdateCdnConfigInput',
        'referer_access_rule': 'RefererAccessRuleForUpdateCdnConfigInput',
        'remote_auth': 'RemoteAuthForUpdateCdnConfigInput',
        'request_block_rule': 'RequestBlockRuleForUpdateCdnConfigInput',
        'request_header': 'list[RequestHeaderForUpdateCdnConfigInput]',
        'response_header': 'list[ResponseHeaderForUpdateCdnConfigInput]',
        'rewrite_hls': 'RewriteHLSForUpdateCdnConfigInput',
        'service_region': 'str',
        'signed_url_auth': 'SignedUrlAuthForUpdateCdnConfigInput',
        'timeout': 'TimeoutForUpdateCdnConfigInput',
        'ua_access_rule': 'UaAccessRuleForUpdateCdnConfigInput',
        'url_normalize': 'UrlNormalizeForUpdateCdnConfigInput',
        'video_drag': 'VideoDragForUpdateCdnConfigInput'
    }

    attribute_map = {
        'area_access_rule': 'AreaAccessRule',
        'bandwidth_limit': 'BandwidthLimit',
        'browser_cache': 'BrowserCache',
        'cache': 'Cache',
        'cache_host': 'CacheHost',
        'cache_key': 'CacheKey',
        'compression': 'Compression',
        'conditional_origin': 'ConditionalOrigin',
        'custom_error_page': 'CustomErrorPage',
        'customize_access_rule': 'CustomizeAccessRule',
        'domain': 'Domain',
        'download_speed_limit': 'DownloadSpeedLimit',
        'follow_redirect': 'FollowRedirect',
        'https': 'HTTPS',
        'http_forced_redirect': 'HttpForcedRedirect',
        'ipv6': 'IPv6',
        'ip_access_rule': 'IpAccessRule',
        'ip_freq_limit': 'IpFreqLimit',
        'method_denied_rule': 'MethodDeniedRule',
        'multi_range': 'MultiRange',
        'negative_cache': 'NegativeCache',
        'offline_cache': 'OfflineCache',
        'origin': 'Origin',
        'origin_access_rule': 'OriginAccessRule',
        'origin_arg': 'OriginArg',
        'origin_cert_check': 'OriginCertCheck',
        'origin_host': 'OriginHost',
        'origin_ipv6': 'OriginIPv6',
        'origin_protocol': 'OriginProtocol',
        'origin_range': 'OriginRange',
        'origin_retry': 'OriginRetry',
        'origin_rewrite': 'OriginRewrite',
        'origin_sni': 'OriginSni',
        'page_optimization': 'PageOptimization',
        'quic': 'Quic',
        'redirection_rewrite': 'RedirectionRewrite',
        'referer_access_rule': 'RefererAccessRule',
        'remote_auth': 'RemoteAuth',
        'request_block_rule': 'RequestBlockRule',
        'request_header': 'RequestHeader',
        'response_header': 'ResponseHeader',
        'rewrite_hls': 'RewriteHLS',
        'service_region': 'ServiceRegion',
        'signed_url_auth': 'SignedUrlAuth',
        'timeout': 'Timeout',
        'ua_access_rule': 'UaAccessRule',
        'url_normalize': 'UrlNormalize',
        'video_drag': 'VideoDrag'
    }

    def __init__(self, area_access_rule=None, bandwidth_limit=None, browser_cache=None, cache=None, cache_host=None, cache_key=None, compression=None, conditional_origin=None, custom_error_page=None, customize_access_rule=None, domain=None, download_speed_limit=None, follow_redirect=None, https=None, http_forced_redirect=None, ipv6=None, ip_access_rule=None, ip_freq_limit=None, method_denied_rule=None, multi_range=None, negative_cache=None, offline_cache=None, origin=None, origin_access_rule=None, origin_arg=None, origin_cert_check=None, origin_host=None, origin_ipv6=None, origin_protocol=None, origin_range=None, origin_retry=None, origin_rewrite=None, origin_sni=None, page_optimization=None, quic=None, redirection_rewrite=None, referer_access_rule=None, remote_auth=None, request_block_rule=None, request_header=None, response_header=None, rewrite_hls=None, service_region=None, signed_url_auth=None, timeout=None, ua_access_rule=None, url_normalize=None, video_drag=None, _configuration=None):  # noqa: E501
        """UpdateCdnConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area_access_rule = None
        self._bandwidth_limit = None
        self._browser_cache = None
        self._cache = None
        self._cache_host = None
        self._cache_key = None
        self._compression = None
        self._conditional_origin = None
        self._custom_error_page = None
        self._customize_access_rule = None
        self._domain = None
        self._download_speed_limit = None
        self._follow_redirect = None
        self._https = None
        self._http_forced_redirect = None
        self._ipv6 = None
        self._ip_access_rule = None
        self._ip_freq_limit = None
        self._method_denied_rule = None
        self._multi_range = None
        self._negative_cache = None
        self._offline_cache = None
        self._origin = None
        self._origin_access_rule = None
        self._origin_arg = None
        self._origin_cert_check = None
        self._origin_host = None
        self._origin_ipv6 = None
        self._origin_protocol = None
        self._origin_range = None
        self._origin_retry = None
        self._origin_rewrite = None
        self._origin_sni = None
        self._page_optimization = None
        self._quic = None
        self._redirection_rewrite = None
        self._referer_access_rule = None
        self._remote_auth = None
        self._request_block_rule = None
        self._request_header = None
        self._response_header = None
        self._rewrite_hls = None
        self._service_region = None
        self._signed_url_auth = None
        self._timeout = None
        self._ua_access_rule = None
        self._url_normalize = None
        self._video_drag = None
        self.discriminator = None

        if area_access_rule is not None:
            self.area_access_rule = area_access_rule
        if bandwidth_limit is not None:
            self.bandwidth_limit = bandwidth_limit
        if browser_cache is not None:
            self.browser_cache = browser_cache
        if cache is not None:
            self.cache = cache
        if cache_host is not None:
            self.cache_host = cache_host
        if cache_key is not None:
            self.cache_key = cache_key
        if compression is not None:
            self.compression = compression
        if conditional_origin is not None:
            self.conditional_origin = conditional_origin
        if custom_error_page is not None:
            self.custom_error_page = custom_error_page
        if customize_access_rule is not None:
            self.customize_access_rule = customize_access_rule
        self.domain = domain
        if download_speed_limit is not None:
            self.download_speed_limit = download_speed_limit
        if follow_redirect is not None:
            self.follow_redirect = follow_redirect
        if https is not None:
            self.https = https
        if http_forced_redirect is not None:
            self.http_forced_redirect = http_forced_redirect
        if ipv6 is not None:
            self.ipv6 = ipv6
        if ip_access_rule is not None:
            self.ip_access_rule = ip_access_rule
        if ip_freq_limit is not None:
            self.ip_freq_limit = ip_freq_limit
        if method_denied_rule is not None:
            self.method_denied_rule = method_denied_rule
        if multi_range is not None:
            self.multi_range = multi_range
        if negative_cache is not None:
            self.negative_cache = negative_cache
        if offline_cache is not None:
            self.offline_cache = offline_cache
        if origin is not None:
            self.origin = origin
        if origin_access_rule is not None:
            self.origin_access_rule = origin_access_rule
        if origin_arg is not None:
            self.origin_arg = origin_arg
        if origin_cert_check is not None:
            self.origin_cert_check = origin_cert_check
        if origin_host is not None:
            self.origin_host = origin_host
        if origin_ipv6 is not None:
            self.origin_ipv6 = origin_ipv6
        if origin_protocol is not None:
            self.origin_protocol = origin_protocol
        if origin_range is not None:
            self.origin_range = origin_range
        if origin_retry is not None:
            self.origin_retry = origin_retry
        if origin_rewrite is not None:
            self.origin_rewrite = origin_rewrite
        if origin_sni is not None:
            self.origin_sni = origin_sni
        if page_optimization is not None:
            self.page_optimization = page_optimization
        if quic is not None:
            self.quic = quic
        if redirection_rewrite is not None:
            self.redirection_rewrite = redirection_rewrite
        if referer_access_rule is not None:
            self.referer_access_rule = referer_access_rule
        if remote_auth is not None:
            self.remote_auth = remote_auth
        if request_block_rule is not None:
            self.request_block_rule = request_block_rule
        if request_header is not None:
            self.request_header = request_header
        if response_header is not None:
            self.response_header = response_header
        if rewrite_hls is not None:
            self.rewrite_hls = rewrite_hls
        if service_region is not None:
            self.service_region = service_region
        if signed_url_auth is not None:
            self.signed_url_auth = signed_url_auth
        if timeout is not None:
            self.timeout = timeout
        if ua_access_rule is not None:
            self.ua_access_rule = ua_access_rule
        if url_normalize is not None:
            self.url_normalize = url_normalize
        if video_drag is not None:
            self.video_drag = video_drag

    @property
    def area_access_rule(self):
        """Gets the area_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The area_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: AreaAccessRuleForUpdateCdnConfigInput
        """
        return self._area_access_rule

    @area_access_rule.setter
    def area_access_rule(self, area_access_rule):
        """Sets the area_access_rule of this UpdateCdnConfigRequest.


        :param area_access_rule: The area_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: AreaAccessRuleForUpdateCdnConfigInput
        """

        self._area_access_rule = area_access_rule

    @property
    def bandwidth_limit(self):
        """Gets the bandwidth_limit of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The bandwidth_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: BandwidthLimitForUpdateCdnConfigInput
        """
        return self._bandwidth_limit

    @bandwidth_limit.setter
    def bandwidth_limit(self, bandwidth_limit):
        """Sets the bandwidth_limit of this UpdateCdnConfigRequest.


        :param bandwidth_limit: The bandwidth_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :type: BandwidthLimitForUpdateCdnConfigInput
        """

        self._bandwidth_limit = bandwidth_limit

    @property
    def browser_cache(self):
        """Gets the browser_cache of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The browser_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[BrowserCacheForUpdateCdnConfigInput]
        """
        return self._browser_cache

    @browser_cache.setter
    def browser_cache(self, browser_cache):
        """Sets the browser_cache of this UpdateCdnConfigRequest.


        :param browser_cache: The browser_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[BrowserCacheForUpdateCdnConfigInput]
        """

        self._browser_cache = browser_cache

    @property
    def cache(self):
        """Gets the cache of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The cache of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[CacheForUpdateCdnConfigInput]
        """
        return self._cache

    @cache.setter
    def cache(self, cache):
        """Sets the cache of this UpdateCdnConfigRequest.


        :param cache: The cache of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[CacheForUpdateCdnConfigInput]
        """

        self._cache = cache

    @property
    def cache_host(self):
        """Gets the cache_host of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The cache_host of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: CacheHostForUpdateCdnConfigInput
        """
        return self._cache_host

    @cache_host.setter
    def cache_host(self, cache_host):
        """Sets the cache_host of this UpdateCdnConfigRequest.


        :param cache_host: The cache_host of this UpdateCdnConfigRequest.  # noqa: E501
        :type: CacheHostForUpdateCdnConfigInput
        """

        self._cache_host = cache_host

    @property
    def cache_key(self):
        """Gets the cache_key of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The cache_key of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[CacheKeyForUpdateCdnConfigInput]
        """
        return self._cache_key

    @cache_key.setter
    def cache_key(self, cache_key):
        """Sets the cache_key of this UpdateCdnConfigRequest.


        :param cache_key: The cache_key of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[CacheKeyForUpdateCdnConfigInput]
        """

        self._cache_key = cache_key

    @property
    def compression(self):
        """Gets the compression of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The compression of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: CompressionForUpdateCdnConfigInput
        """
        return self._compression

    @compression.setter
    def compression(self, compression):
        """Sets the compression of this UpdateCdnConfigRequest.


        :param compression: The compression of this UpdateCdnConfigRequest.  # noqa: E501
        :type: CompressionForUpdateCdnConfigInput
        """

        self._compression = compression

    @property
    def conditional_origin(self):
        """Gets the conditional_origin of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The conditional_origin of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: ConditionalOriginForUpdateCdnConfigInput
        """
        return self._conditional_origin

    @conditional_origin.setter
    def conditional_origin(self, conditional_origin):
        """Sets the conditional_origin of this UpdateCdnConfigRequest.


        :param conditional_origin: The conditional_origin of this UpdateCdnConfigRequest.  # noqa: E501
        :type: ConditionalOriginForUpdateCdnConfigInput
        """

        self._conditional_origin = conditional_origin

    @property
    def custom_error_page(self):
        """Gets the custom_error_page of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The custom_error_page of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: CustomErrorPageForUpdateCdnConfigInput
        """
        return self._custom_error_page

    @custom_error_page.setter
    def custom_error_page(self, custom_error_page):
        """Sets the custom_error_page of this UpdateCdnConfigRequest.


        :param custom_error_page: The custom_error_page of this UpdateCdnConfigRequest.  # noqa: E501
        :type: CustomErrorPageForUpdateCdnConfigInput
        """

        self._custom_error_page = custom_error_page

    @property
    def customize_access_rule(self):
        """Gets the customize_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The customize_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: CustomizeAccessRuleForUpdateCdnConfigInput
        """
        return self._customize_access_rule

    @customize_access_rule.setter
    def customize_access_rule(self, customize_access_rule):
        """Sets the customize_access_rule of this UpdateCdnConfigRequest.


        :param customize_access_rule: The customize_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: CustomizeAccessRuleForUpdateCdnConfigInput
        """

        self._customize_access_rule = customize_access_rule

    @property
    def domain(self):
        """Gets the domain of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The domain of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this UpdateCdnConfigRequest.


        :param domain: The domain of this UpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def download_speed_limit(self):
        """Gets the download_speed_limit of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The download_speed_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: DownloadSpeedLimitForUpdateCdnConfigInput
        """
        return self._download_speed_limit

    @download_speed_limit.setter
    def download_speed_limit(self, download_speed_limit):
        """Sets the download_speed_limit of this UpdateCdnConfigRequest.


        :param download_speed_limit: The download_speed_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :type: DownloadSpeedLimitForUpdateCdnConfigInput
        """

        self._download_speed_limit = download_speed_limit

    @property
    def follow_redirect(self):
        """Gets the follow_redirect of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The follow_redirect of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._follow_redirect

    @follow_redirect.setter
    def follow_redirect(self, follow_redirect):
        """Sets the follow_redirect of this UpdateCdnConfigRequest.


        :param follow_redirect: The follow_redirect of this UpdateCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._follow_redirect = follow_redirect

    @property
    def https(self):
        """Gets the https of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The https of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: HTTPSForUpdateCdnConfigInput
        """
        return self._https

    @https.setter
    def https(self, https):
        """Sets the https of this UpdateCdnConfigRequest.


        :param https: The https of this UpdateCdnConfigRequest.  # noqa: E501
        :type: HTTPSForUpdateCdnConfigInput
        """

        self._https = https

    @property
    def http_forced_redirect(self):
        """Gets the http_forced_redirect of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The http_forced_redirect of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: HttpForcedRedirectForUpdateCdnConfigInput
        """
        return self._http_forced_redirect

    @http_forced_redirect.setter
    def http_forced_redirect(self, http_forced_redirect):
        """Sets the http_forced_redirect of this UpdateCdnConfigRequest.


        :param http_forced_redirect: The http_forced_redirect of this UpdateCdnConfigRequest.  # noqa: E501
        :type: HttpForcedRedirectForUpdateCdnConfigInput
        """

        self._http_forced_redirect = http_forced_redirect

    @property
    def ipv6(self):
        """Gets the ipv6 of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The ipv6 of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: IPv6ForUpdateCdnConfigInput
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this UpdateCdnConfigRequest.


        :param ipv6: The ipv6 of this UpdateCdnConfigRequest.  # noqa: E501
        :type: IPv6ForUpdateCdnConfigInput
        """

        self._ipv6 = ipv6

    @property
    def ip_access_rule(self):
        """Gets the ip_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The ip_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: IpAccessRuleForUpdateCdnConfigInput
        """
        return self._ip_access_rule

    @ip_access_rule.setter
    def ip_access_rule(self, ip_access_rule):
        """Sets the ip_access_rule of this UpdateCdnConfigRequest.


        :param ip_access_rule: The ip_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: IpAccessRuleForUpdateCdnConfigInput
        """

        self._ip_access_rule = ip_access_rule

    @property
    def ip_freq_limit(self):
        """Gets the ip_freq_limit of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The ip_freq_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: IpFreqLimitForUpdateCdnConfigInput
        """
        return self._ip_freq_limit

    @ip_freq_limit.setter
    def ip_freq_limit(self, ip_freq_limit):
        """Sets the ip_freq_limit of this UpdateCdnConfigRequest.


        :param ip_freq_limit: The ip_freq_limit of this UpdateCdnConfigRequest.  # noqa: E501
        :type: IpFreqLimitForUpdateCdnConfigInput
        """

        self._ip_freq_limit = ip_freq_limit

    @property
    def method_denied_rule(self):
        """Gets the method_denied_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The method_denied_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: MethodDeniedRuleForUpdateCdnConfigInput
        """
        return self._method_denied_rule

    @method_denied_rule.setter
    def method_denied_rule(self, method_denied_rule):
        """Sets the method_denied_rule of this UpdateCdnConfigRequest.


        :param method_denied_rule: The method_denied_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: MethodDeniedRuleForUpdateCdnConfigInput
        """

        self._method_denied_rule = method_denied_rule

    @property
    def multi_range(self):
        """Gets the multi_range of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The multi_range of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: MultiRangeForUpdateCdnConfigInput
        """
        return self._multi_range

    @multi_range.setter
    def multi_range(self, multi_range):
        """Sets the multi_range of this UpdateCdnConfigRequest.


        :param multi_range: The multi_range of this UpdateCdnConfigRequest.  # noqa: E501
        :type: MultiRangeForUpdateCdnConfigInput
        """

        self._multi_range = multi_range

    @property
    def negative_cache(self):
        """Gets the negative_cache of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The negative_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[NegativeCacheForUpdateCdnConfigInput]
        """
        return self._negative_cache

    @negative_cache.setter
    def negative_cache(self, negative_cache):
        """Sets the negative_cache of this UpdateCdnConfigRequest.


        :param negative_cache: The negative_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[NegativeCacheForUpdateCdnConfigInput]
        """

        self._negative_cache = negative_cache

    @property
    def offline_cache(self):
        """Gets the offline_cache of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The offline_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OfflineCacheForUpdateCdnConfigInput
        """
        return self._offline_cache

    @offline_cache.setter
    def offline_cache(self, offline_cache):
        """Sets the offline_cache of this UpdateCdnConfigRequest.


        :param offline_cache: The offline_cache of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OfflineCacheForUpdateCdnConfigInput
        """

        self._offline_cache = offline_cache

    @property
    def origin(self):
        """Gets the origin of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[OriginForUpdateCdnConfigInput]
        """
        return self._origin

    @origin.setter
    def origin(self, origin):
        """Sets the origin of this UpdateCdnConfigRequest.


        :param origin: The origin of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[OriginForUpdateCdnConfigInput]
        """

        self._origin = origin

    @property
    def origin_access_rule(self):
        """Gets the origin_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginAccessRuleForUpdateCdnConfigInput
        """
        return self._origin_access_rule

    @origin_access_rule.setter
    def origin_access_rule(self, origin_access_rule):
        """Sets the origin_access_rule of this UpdateCdnConfigRequest.


        :param origin_access_rule: The origin_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OriginAccessRuleForUpdateCdnConfigInput
        """

        self._origin_access_rule = origin_access_rule

    @property
    def origin_arg(self):
        """Gets the origin_arg of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_arg of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[OriginArgForUpdateCdnConfigInput]
        """
        return self._origin_arg

    @origin_arg.setter
    def origin_arg(self, origin_arg):
        """Sets the origin_arg of this UpdateCdnConfigRequest.


        :param origin_arg: The origin_arg of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[OriginArgForUpdateCdnConfigInput]
        """

        self._origin_arg = origin_arg

    @property
    def origin_cert_check(self):
        """Gets the origin_cert_check of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_cert_check of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginCertCheckForUpdateCdnConfigInput
        """
        return self._origin_cert_check

    @origin_cert_check.setter
    def origin_cert_check(self, origin_cert_check):
        """Sets the origin_cert_check of this UpdateCdnConfigRequest.


        :param origin_cert_check: The origin_cert_check of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OriginCertCheckForUpdateCdnConfigInput
        """

        self._origin_cert_check = origin_cert_check

    @property
    def origin_host(self):
        """Gets the origin_host of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_host of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_host

    @origin_host.setter
    def origin_host(self, origin_host):
        """Sets the origin_host of this UpdateCdnConfigRequest.


        :param origin_host: The origin_host of this UpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_host = origin_host

    @property
    def origin_ipv6(self):
        """Gets the origin_ipv6 of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_ipv6 of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_ipv6

    @origin_ipv6.setter
    def origin_ipv6(self, origin_ipv6):
        """Sets the origin_ipv6 of this UpdateCdnConfigRequest.


        :param origin_ipv6: The origin_ipv6 of this UpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_ipv6 = origin_ipv6

    @property
    def origin_protocol(self):
        """Gets the origin_protocol of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_protocol of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_protocol

    @origin_protocol.setter
    def origin_protocol(self, origin_protocol):
        """Sets the origin_protocol of this UpdateCdnConfigRequest.


        :param origin_protocol: The origin_protocol of this UpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_protocol = origin_protocol

    @property
    def origin_range(self):
        """Gets the origin_range of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_range of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._origin_range

    @origin_range.setter
    def origin_range(self, origin_range):
        """Sets the origin_range of this UpdateCdnConfigRequest.


        :param origin_range: The origin_range of this UpdateCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._origin_range = origin_range

    @property
    def origin_retry(self):
        """Gets the origin_retry of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_retry of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginRetryForUpdateCdnConfigInput
        """
        return self._origin_retry

    @origin_retry.setter
    def origin_retry(self, origin_retry):
        """Sets the origin_retry of this UpdateCdnConfigRequest.


        :param origin_retry: The origin_retry of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OriginRetryForUpdateCdnConfigInput
        """

        self._origin_retry = origin_retry

    @property
    def origin_rewrite(self):
        """Gets the origin_rewrite of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_rewrite of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginRewriteForUpdateCdnConfigInput
        """
        return self._origin_rewrite

    @origin_rewrite.setter
    def origin_rewrite(self, origin_rewrite):
        """Sets the origin_rewrite of this UpdateCdnConfigRequest.


        :param origin_rewrite: The origin_rewrite of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OriginRewriteForUpdateCdnConfigInput
        """

        self._origin_rewrite = origin_rewrite

    @property
    def origin_sni(self):
        """Gets the origin_sni of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_sni of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginSniForUpdateCdnConfigInput
        """
        return self._origin_sni

    @origin_sni.setter
    def origin_sni(self, origin_sni):
        """Sets the origin_sni of this UpdateCdnConfigRequest.


        :param origin_sni: The origin_sni of this UpdateCdnConfigRequest.  # noqa: E501
        :type: OriginSniForUpdateCdnConfigInput
        """

        self._origin_sni = origin_sni

    @property
    def page_optimization(self):
        """Gets the page_optimization of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The page_optimization of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: PageOptimizationForUpdateCdnConfigInput
        """
        return self._page_optimization

    @page_optimization.setter
    def page_optimization(self, page_optimization):
        """Sets the page_optimization of this UpdateCdnConfigRequest.


        :param page_optimization: The page_optimization of this UpdateCdnConfigRequest.  # noqa: E501
        :type: PageOptimizationForUpdateCdnConfigInput
        """

        self._page_optimization = page_optimization

    @property
    def quic(self):
        """Gets the quic of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The quic of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: QuicForUpdateCdnConfigInput
        """
        return self._quic

    @quic.setter
    def quic(self, quic):
        """Sets the quic of this UpdateCdnConfigRequest.


        :param quic: The quic of this UpdateCdnConfigRequest.  # noqa: E501
        :type: QuicForUpdateCdnConfigInput
        """

        self._quic = quic

    @property
    def redirection_rewrite(self):
        """Gets the redirection_rewrite of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The redirection_rewrite of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: RedirectionRewriteForUpdateCdnConfigInput
        """
        return self._redirection_rewrite

    @redirection_rewrite.setter
    def redirection_rewrite(self, redirection_rewrite):
        """Sets the redirection_rewrite of this UpdateCdnConfigRequest.


        :param redirection_rewrite: The redirection_rewrite of this UpdateCdnConfigRequest.  # noqa: E501
        :type: RedirectionRewriteForUpdateCdnConfigInput
        """

        self._redirection_rewrite = redirection_rewrite

    @property
    def referer_access_rule(self):
        """Gets the referer_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The referer_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: RefererAccessRuleForUpdateCdnConfigInput
        """
        return self._referer_access_rule

    @referer_access_rule.setter
    def referer_access_rule(self, referer_access_rule):
        """Sets the referer_access_rule of this UpdateCdnConfigRequest.


        :param referer_access_rule: The referer_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: RefererAccessRuleForUpdateCdnConfigInput
        """

        self._referer_access_rule = referer_access_rule

    @property
    def remote_auth(self):
        """Gets the remote_auth of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The remote_auth of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: RemoteAuthForUpdateCdnConfigInput
        """
        return self._remote_auth

    @remote_auth.setter
    def remote_auth(self, remote_auth):
        """Sets the remote_auth of this UpdateCdnConfigRequest.


        :param remote_auth: The remote_auth of this UpdateCdnConfigRequest.  # noqa: E501
        :type: RemoteAuthForUpdateCdnConfigInput
        """

        self._remote_auth = remote_auth

    @property
    def request_block_rule(self):
        """Gets the request_block_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The request_block_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: RequestBlockRuleForUpdateCdnConfigInput
        """
        return self._request_block_rule

    @request_block_rule.setter
    def request_block_rule(self, request_block_rule):
        """Sets the request_block_rule of this UpdateCdnConfigRequest.


        :param request_block_rule: The request_block_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: RequestBlockRuleForUpdateCdnConfigInput
        """

        self._request_block_rule = request_block_rule

    @property
    def request_header(self):
        """Gets the request_header of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The request_header of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[RequestHeaderForUpdateCdnConfigInput]
        """
        return self._request_header

    @request_header.setter
    def request_header(self, request_header):
        """Sets the request_header of this UpdateCdnConfigRequest.


        :param request_header: The request_header of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[RequestHeaderForUpdateCdnConfigInput]
        """

        self._request_header = request_header

    @property
    def response_header(self):
        """Gets the response_header of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The response_header of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[ResponseHeaderForUpdateCdnConfigInput]
        """
        return self._response_header

    @response_header.setter
    def response_header(self, response_header):
        """Sets the response_header of this UpdateCdnConfigRequest.


        :param response_header: The response_header of this UpdateCdnConfigRequest.  # noqa: E501
        :type: list[ResponseHeaderForUpdateCdnConfigInput]
        """

        self._response_header = response_header

    @property
    def rewrite_hls(self):
        """Gets the rewrite_hls of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The rewrite_hls of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: RewriteHLSForUpdateCdnConfigInput
        """
        return self._rewrite_hls

    @rewrite_hls.setter
    def rewrite_hls(self, rewrite_hls):
        """Sets the rewrite_hls of this UpdateCdnConfigRequest.


        :param rewrite_hls: The rewrite_hls of this UpdateCdnConfigRequest.  # noqa: E501
        :type: RewriteHLSForUpdateCdnConfigInput
        """

        self._rewrite_hls = rewrite_hls

    @property
    def service_region(self):
        """Gets the service_region of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The service_region of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_region

    @service_region.setter
    def service_region(self, service_region):
        """Sets the service_region of this UpdateCdnConfigRequest.


        :param service_region: The service_region of this UpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._service_region = service_region

    @property
    def signed_url_auth(self):
        """Gets the signed_url_auth of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The signed_url_auth of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: SignedUrlAuthForUpdateCdnConfigInput
        """
        return self._signed_url_auth

    @signed_url_auth.setter
    def signed_url_auth(self, signed_url_auth):
        """Sets the signed_url_auth of this UpdateCdnConfigRequest.


        :param signed_url_auth: The signed_url_auth of this UpdateCdnConfigRequest.  # noqa: E501
        :type: SignedUrlAuthForUpdateCdnConfigInput
        """

        self._signed_url_auth = signed_url_auth

    @property
    def timeout(self):
        """Gets the timeout of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The timeout of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: TimeoutForUpdateCdnConfigInput
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this UpdateCdnConfigRequest.


        :param timeout: The timeout of this UpdateCdnConfigRequest.  # noqa: E501
        :type: TimeoutForUpdateCdnConfigInput
        """

        self._timeout = timeout

    @property
    def ua_access_rule(self):
        """Gets the ua_access_rule of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The ua_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: UaAccessRuleForUpdateCdnConfigInput
        """
        return self._ua_access_rule

    @ua_access_rule.setter
    def ua_access_rule(self, ua_access_rule):
        """Sets the ua_access_rule of this UpdateCdnConfigRequest.


        :param ua_access_rule: The ua_access_rule of this UpdateCdnConfigRequest.  # noqa: E501
        :type: UaAccessRuleForUpdateCdnConfigInput
        """

        self._ua_access_rule = ua_access_rule

    @property
    def url_normalize(self):
        """Gets the url_normalize of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The url_normalize of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: UrlNormalizeForUpdateCdnConfigInput
        """
        return self._url_normalize

    @url_normalize.setter
    def url_normalize(self, url_normalize):
        """Sets the url_normalize of this UpdateCdnConfigRequest.


        :param url_normalize: The url_normalize of this UpdateCdnConfigRequest.  # noqa: E501
        :type: UrlNormalizeForUpdateCdnConfigInput
        """

        self._url_normalize = url_normalize

    @property
    def video_drag(self):
        """Gets the video_drag of this UpdateCdnConfigRequest.  # noqa: E501


        :return: The video_drag of this UpdateCdnConfigRequest.  # noqa: E501
        :rtype: VideoDragForUpdateCdnConfigInput
        """
        return self._video_drag

    @video_drag.setter
    def video_drag(self, video_drag):
        """Sets the video_drag of this UpdateCdnConfigRequest.


        :param video_drag: The video_drag of this UpdateCdnConfigRequest.  # noqa: E501
        :type: VideoDragForUpdateCdnConfigInput
        """

        self._video_drag = video_drag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateCdnConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateCdnConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateCdnConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
