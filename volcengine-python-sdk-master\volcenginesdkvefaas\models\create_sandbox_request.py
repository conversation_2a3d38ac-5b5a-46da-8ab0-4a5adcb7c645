# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateSandboxRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'envs': 'list[EnvForCreateSandboxInput]',
        'function_id': 'str',
        'instance_tos_mount_config': 'InstanceTosMountConfigForCreateSandboxInput',
        'metadata': 'MetadataForCreateSandboxInput',
        'timeout': 'int'
    }

    attribute_map = {
        'envs': 'Envs',
        'function_id': 'FunctionId',
        'instance_tos_mount_config': 'InstanceTosMountConfig',
        'metadata': 'Metadata',
        'timeout': 'Timeout'
    }

    def __init__(self, envs=None, function_id=None, instance_tos_mount_config=None, metadata=None, timeout=None, _configuration=None):  # noqa: E501
        """CreateSandboxRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._envs = None
        self._function_id = None
        self._instance_tos_mount_config = None
        self._metadata = None
        self._timeout = None
        self.discriminator = None

        if envs is not None:
            self.envs = envs
        self.function_id = function_id
        if instance_tos_mount_config is not None:
            self.instance_tos_mount_config = instance_tos_mount_config
        if metadata is not None:
            self.metadata = metadata
        if timeout is not None:
            self.timeout = timeout

    @property
    def envs(self):
        """Gets the envs of this CreateSandboxRequest.  # noqa: E501


        :return: The envs of this CreateSandboxRequest.  # noqa: E501
        :rtype: list[EnvForCreateSandboxInput]
        """
        return self._envs

    @envs.setter
    def envs(self, envs):
        """Sets the envs of this CreateSandboxRequest.


        :param envs: The envs of this CreateSandboxRequest.  # noqa: E501
        :type: list[EnvForCreateSandboxInput]
        """

        self._envs = envs

    @property
    def function_id(self):
        """Gets the function_id of this CreateSandboxRequest.  # noqa: E501


        :return: The function_id of this CreateSandboxRequest.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this CreateSandboxRequest.


        :param function_id: The function_id of this CreateSandboxRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and function_id is None:
            raise ValueError("Invalid value for `function_id`, must not be `None`")  # noqa: E501

        self._function_id = function_id

    @property
    def instance_tos_mount_config(self):
        """Gets the instance_tos_mount_config of this CreateSandboxRequest.  # noqa: E501


        :return: The instance_tos_mount_config of this CreateSandboxRequest.  # noqa: E501
        :rtype: InstanceTosMountConfigForCreateSandboxInput
        """
        return self._instance_tos_mount_config

    @instance_tos_mount_config.setter
    def instance_tos_mount_config(self, instance_tos_mount_config):
        """Sets the instance_tos_mount_config of this CreateSandboxRequest.


        :param instance_tos_mount_config: The instance_tos_mount_config of this CreateSandboxRequest.  # noqa: E501
        :type: InstanceTosMountConfigForCreateSandboxInput
        """

        self._instance_tos_mount_config = instance_tos_mount_config

    @property
    def metadata(self):
        """Gets the metadata of this CreateSandboxRequest.  # noqa: E501


        :return: The metadata of this CreateSandboxRequest.  # noqa: E501
        :rtype: MetadataForCreateSandboxInput
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this CreateSandboxRequest.


        :param metadata: The metadata of this CreateSandboxRequest.  # noqa: E501
        :type: MetadataForCreateSandboxInput
        """

        self._metadata = metadata

    @property
    def timeout(self):
        """Gets the timeout of this CreateSandboxRequest.  # noqa: E501


        :return: The timeout of this CreateSandboxRequest.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this CreateSandboxRequest.


        :param timeout: The timeout of this CreateSandboxRequest.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateSandboxRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateSandboxRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateSandboxRequest):
            return True

        return self.to_dict() != other.to_dict()
