# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IPSetForCreateIPSetsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerate_region': 'str',
        'accelerator_id': 'str',
        'ip_set_id': 'str',
        'ip_version': 'str',
        'state': 'str'
    }

    attribute_map = {
        'accelerate_region': 'AccelerateRegion',
        'accelerator_id': 'AcceleratorId',
        'ip_set_id': 'IPSetId',
        'ip_version': 'IPVersion',
        'state': 'State'
    }

    def __init__(self, accelerate_region=None, accelerator_id=None, ip_set_id=None, ip_version=None, state=None, _configuration=None):  # noqa: E501
        """IPSetForCreateIPSetsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerate_region = None
        self._accelerator_id = None
        self._ip_set_id = None
        self._ip_version = None
        self._state = None
        self.discriminator = None

        if accelerate_region is not None:
            self.accelerate_region = accelerate_region
        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if ip_set_id is not None:
            self.ip_set_id = ip_set_id
        if ip_version is not None:
            self.ip_version = ip_version
        if state is not None:
            self.state = state

    @property
    def accelerate_region(self):
        """Gets the accelerate_region of this IPSetForCreateIPSetsInput.  # noqa: E501


        :return: The accelerate_region of this IPSetForCreateIPSetsInput.  # noqa: E501
        :rtype: str
        """
        return self._accelerate_region

    @accelerate_region.setter
    def accelerate_region(self, accelerate_region):
        """Sets the accelerate_region of this IPSetForCreateIPSetsInput.


        :param accelerate_region: The accelerate_region of this IPSetForCreateIPSetsInput.  # noqa: E501
        :type: str
        """

        self._accelerate_region = accelerate_region

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this IPSetForCreateIPSetsInput.  # noqa: E501


        :return: The accelerator_id of this IPSetForCreateIPSetsInput.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this IPSetForCreateIPSetsInput.


        :param accelerator_id: The accelerator_id of this IPSetForCreateIPSetsInput.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this IPSetForCreateIPSetsInput.  # noqa: E501


        :return: The ip_set_id of this IPSetForCreateIPSetsInput.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this IPSetForCreateIPSetsInput.


        :param ip_set_id: The ip_set_id of this IPSetForCreateIPSetsInput.  # noqa: E501
        :type: str
        """

        self._ip_set_id = ip_set_id

    @property
    def ip_version(self):
        """Gets the ip_version of this IPSetForCreateIPSetsInput.  # noqa: E501


        :return: The ip_version of this IPSetForCreateIPSetsInput.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this IPSetForCreateIPSetsInput.


        :param ip_version: The ip_version of this IPSetForCreateIPSetsInput.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def state(self):
        """Gets the state of this IPSetForCreateIPSetsInput.  # noqa: E501


        :return: The state of this IPSetForCreateIPSetsInput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this IPSetForCreateIPSetsInput.


        :param state: The state of this IPSetForCreateIPSetsInput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IPSetForCreateIPSetsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IPSetForCreateIPSetsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IPSetForCreateIPSetsInput):
            return True

        return self.to_dict() != other.to_dict()
