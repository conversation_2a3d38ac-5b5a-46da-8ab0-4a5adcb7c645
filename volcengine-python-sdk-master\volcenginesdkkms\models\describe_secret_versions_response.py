# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSecretVersionsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_info': 'PageInfoForDescribeSecretVersionsOutput',
        'secret_versions': 'list[SecretVersionForDescribeSecretVersionsOutput]'
    }

    attribute_map = {
        'page_info': 'PageInfo',
        'secret_versions': 'SecretVersions'
    }

    def __init__(self, page_info=None, secret_versions=None, _configuration=None):  # noqa: E501
        """DescribeSecretVersionsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_info = None
        self._secret_versions = None
        self.discriminator = None

        if page_info is not None:
            self.page_info = page_info
        if secret_versions is not None:
            self.secret_versions = secret_versions

    @property
    def page_info(self):
        """Gets the page_info of this DescribeSecretVersionsResponse.  # noqa: E501


        :return: The page_info of this DescribeSecretVersionsResponse.  # noqa: E501
        :rtype: PageInfoForDescribeSecretVersionsOutput
        """
        return self._page_info

    @page_info.setter
    def page_info(self, page_info):
        """Sets the page_info of this DescribeSecretVersionsResponse.


        :param page_info: The page_info of this DescribeSecretVersionsResponse.  # noqa: E501
        :type: PageInfoForDescribeSecretVersionsOutput
        """

        self._page_info = page_info

    @property
    def secret_versions(self):
        """Gets the secret_versions of this DescribeSecretVersionsResponse.  # noqa: E501


        :return: The secret_versions of this DescribeSecretVersionsResponse.  # noqa: E501
        :rtype: list[SecretVersionForDescribeSecretVersionsOutput]
        """
        return self._secret_versions

    @secret_versions.setter
    def secret_versions(self, secret_versions):
        """Sets the secret_versions of this DescribeSecretVersionsResponse.


        :param secret_versions: The secret_versions of this DescribeSecretVersionsResponse.  # noqa: E501
        :type: list[SecretVersionForDescribeSecretVersionsOutput]
        """

        self._secret_versions = secret_versions

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSecretVersionsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSecretVersionsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSecretVersionsResponse):
            return True

        return self.to_dict() != other.to_dict()
