# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskForListContentTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'int',
        'end_at': 'int',
        'sub_tasks': 'list[SubTaskForListContentTasksOutput]',
        'task_id': 'str',
        'task_status': 'str',
        'task_type': 'str',
        'vendor': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'end_at': 'EndAt',
        'sub_tasks': 'SubTasks',
        'task_id': 'TaskId',
        'task_status': 'TaskStatus',
        'task_type': 'TaskType',
        'vendor': 'Vendor'
    }

    def __init__(self, created_at=None, end_at=None, sub_tasks=None, task_id=None, task_status=None, task_type=None, vendor=None, _configuration=None):  # noqa: E501
        """TaskForListContentTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._end_at = None
        self._sub_tasks = None
        self._task_id = None
        self._task_status = None
        self._task_type = None
        self._vendor = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if end_at is not None:
            self.end_at = end_at
        if sub_tasks is not None:
            self.sub_tasks = sub_tasks
        if task_id is not None:
            self.task_id = task_id
        if task_status is not None:
            self.task_status = task_status
        if task_type is not None:
            self.task_type = task_type
        if vendor is not None:
            self.vendor = vendor

    @property
    def created_at(self):
        """Gets the created_at of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The created_at of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this TaskForListContentTasksOutput.


        :param created_at: The created_at of this TaskForListContentTasksOutput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def end_at(self):
        """Gets the end_at of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The end_at of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_at

    @end_at.setter
    def end_at(self, end_at):
        """Sets the end_at of this TaskForListContentTasksOutput.


        :param end_at: The end_at of this TaskForListContentTasksOutput.  # noqa: E501
        :type: int
        """

        self._end_at = end_at

    @property
    def sub_tasks(self):
        """Gets the sub_tasks of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The sub_tasks of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: list[SubTaskForListContentTasksOutput]
        """
        return self._sub_tasks

    @sub_tasks.setter
    def sub_tasks(self, sub_tasks):
        """Sets the sub_tasks of this TaskForListContentTasksOutput.


        :param sub_tasks: The sub_tasks of this TaskForListContentTasksOutput.  # noqa: E501
        :type: list[SubTaskForListContentTasksOutput]
        """

        self._sub_tasks = sub_tasks

    @property
    def task_id(self):
        """Gets the task_id of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The task_id of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this TaskForListContentTasksOutput.


        :param task_id: The task_id of this TaskForListContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_status(self):
        """Gets the task_status of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The task_status of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this TaskForListContentTasksOutput.


        :param task_status: The task_status of this TaskForListContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_status = task_status

    @property
    def task_type(self):
        """Gets the task_type of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The task_type of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this TaskForListContentTasksOutput.


        :param task_type: The task_type of this TaskForListContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    @property
    def vendor(self):
        """Gets the vendor of this TaskForListContentTasksOutput.  # noqa: E501


        :return: The vendor of this TaskForListContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this TaskForListContentTasksOutput.


        :param vendor: The vendor of this TaskForListContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskForListContentTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskForListContentTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskForListContentTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
