# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBEndpointAddressRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain_prefix': 'str',
        'endpoint_id': 'str',
        'instance_id': 'str',
        'network_type': 'str',
        'port': 'int'
    }

    attribute_map = {
        'domain_prefix': 'DomainPrefix',
        'endpoint_id': 'EndpointId',
        'instance_id': 'InstanceId',
        'network_type': 'NetworkType',
        'port': 'Port'
    }

    def __init__(self, domain_prefix=None, endpoint_id=None, instance_id=None, network_type=None, port=None, _configuration=None):  # noqa: E501
        """ModifyDBEndpointAddressRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain_prefix = None
        self._endpoint_id = None
        self._instance_id = None
        self._network_type = None
        self._port = None
        self.discriminator = None

        if domain_prefix is not None:
            self.domain_prefix = domain_prefix
        self.endpoint_id = endpoint_id
        self.instance_id = instance_id
        self.network_type = network_type
        if port is not None:
            self.port = port

    @property
    def domain_prefix(self):
        """Gets the domain_prefix of this ModifyDBEndpointAddressRequest.  # noqa: E501


        :return: The domain_prefix of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain_prefix

    @domain_prefix.setter
    def domain_prefix(self, domain_prefix):
        """Sets the domain_prefix of this ModifyDBEndpointAddressRequest.


        :param domain_prefix: The domain_prefix of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :type: str
        """

        self._domain_prefix = domain_prefix

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this ModifyDBEndpointAddressRequest.  # noqa: E501


        :return: The endpoint_id of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this ModifyDBEndpointAddressRequest.


        :param endpoint_id: The endpoint_id of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and endpoint_id is None:
            raise ValueError("Invalid value for `endpoint_id`, must not be `None`")  # noqa: E501

        self._endpoint_id = endpoint_id

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBEndpointAddressRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBEndpointAddressRequest.


        :param instance_id: The instance_id of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def network_type(self):
        """Gets the network_type of this ModifyDBEndpointAddressRequest.  # noqa: E501


        :return: The network_type of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this ModifyDBEndpointAddressRequest.


        :param network_type: The network_type of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and network_type is None:
            raise ValueError("Invalid value for `network_type`, must not be `None`")  # noqa: E501

        self._network_type = network_type

    @property
    def port(self):
        """Gets the port of this ModifyDBEndpointAddressRequest.  # noqa: E501


        :return: The port of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this ModifyDBEndpointAddressRequest.


        :param port: The port of this ModifyDBEndpointAddressRequest.  # noqa: E501
        :type: int
        """

        self._port = port

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBEndpointAddressRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBEndpointAddressRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBEndpointAddressRequest):
            return True

        return self.to_dict() != other.to_dict()
