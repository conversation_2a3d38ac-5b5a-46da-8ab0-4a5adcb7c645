# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListNamespacesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'name': 'str',
        'project': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'name': 'Name',
        'project': 'Project'
    }

    def __init__(self, create_time=None, name=None, project=None, _configuration=None):  # noqa: E501
        """ItemForListNamespacesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._name = None
        self._project = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if name is not None:
            self.name = name
        if project is not None:
            self.project = project

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListNamespacesOutput.  # noqa: E501


        :return: The create_time of this ItemForListNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListNamespacesOutput.


        :param create_time: The create_time of this ItemForListNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def name(self):
        """Gets the name of this ItemForListNamespacesOutput.  # noqa: E501


        :return: The name of this ItemForListNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListNamespacesOutput.


        :param name: The name of this ItemForListNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project(self):
        """Gets the project of this ItemForListNamespacesOutput.  # noqa: E501


        :return: The project of this ItemForListNamespacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ItemForListNamespacesOutput.


        :param project: The project of this ItemForListNamespacesOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListNamespacesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListNamespacesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListNamespacesOutput):
            return True

        return self.to_dict() != other.to_dict()
