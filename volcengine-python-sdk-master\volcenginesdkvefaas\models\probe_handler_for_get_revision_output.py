# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProbeHandlerForGetRevisionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'http_get': 'HTTPGetForGetRevisionOutput',
        'tcp_socket': 'TCPSocketForGetRevisionOutput'
    }

    attribute_map = {
        'http_get': 'HTTPGet',
        'tcp_socket': 'TCPSocket'
    }

    def __init__(self, http_get=None, tcp_socket=None, _configuration=None):  # noqa: E501
        """ProbeHandlerForGetRevisionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._http_get = None
        self._tcp_socket = None
        self.discriminator = None

        if http_get is not None:
            self.http_get = http_get
        if tcp_socket is not None:
            self.tcp_socket = tcp_socket

    @property
    def http_get(self):
        """Gets the http_get of this ProbeHandlerForGetRevisionOutput.  # noqa: E501


        :return: The http_get of this ProbeHandlerForGetRevisionOutput.  # noqa: E501
        :rtype: HTTPGetForGetRevisionOutput
        """
        return self._http_get

    @http_get.setter
    def http_get(self, http_get):
        """Sets the http_get of this ProbeHandlerForGetRevisionOutput.


        :param http_get: The http_get of this ProbeHandlerForGetRevisionOutput.  # noqa: E501
        :type: HTTPGetForGetRevisionOutput
        """

        self._http_get = http_get

    @property
    def tcp_socket(self):
        """Gets the tcp_socket of this ProbeHandlerForGetRevisionOutput.  # noqa: E501


        :return: The tcp_socket of this ProbeHandlerForGetRevisionOutput.  # noqa: E501
        :rtype: TCPSocketForGetRevisionOutput
        """
        return self._tcp_socket

    @tcp_socket.setter
    def tcp_socket(self, tcp_socket):
        """Sets the tcp_socket of this ProbeHandlerForGetRevisionOutput.


        :param tcp_socket: The tcp_socket of this ProbeHandlerForGetRevisionOutput.  # noqa: E501
        :type: TCPSocketForGetRevisionOutput
        """

        self._tcp_socket = tcp_socket

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProbeHandlerForGetRevisionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProbeHandlerForGetRevisionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProbeHandlerForGetRevisionOutput):
            return True

        return self.to_dict() != other.to_dict()
