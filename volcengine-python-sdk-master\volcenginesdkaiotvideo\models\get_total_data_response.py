# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTotalDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'down_chain_growth': 'str',
        'max_down': 'float',
        'max_up': 'float',
        'up_chain_growth': 'str'
    }

    attribute_map = {
        'down_chain_growth': 'DownChainGrowth',
        'max_down': 'MaxDown',
        'max_up': 'MaxUp',
        'up_chain_growth': 'UpChainGrowth'
    }

    def __init__(self, down_chain_growth=None, max_down=None, max_up=None, up_chain_growth=None, _configuration=None):  # noqa: E501
        """GetTotalDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._down_chain_growth = None
        self._max_down = None
        self._max_up = None
        self._up_chain_growth = None
        self.discriminator = None

        if down_chain_growth is not None:
            self.down_chain_growth = down_chain_growth
        if max_down is not None:
            self.max_down = max_down
        if max_up is not None:
            self.max_up = max_up
        if up_chain_growth is not None:
            self.up_chain_growth = up_chain_growth

    @property
    def down_chain_growth(self):
        """Gets the down_chain_growth of this GetTotalDataResponse.  # noqa: E501


        :return: The down_chain_growth of this GetTotalDataResponse.  # noqa: E501
        :rtype: str
        """
        return self._down_chain_growth

    @down_chain_growth.setter
    def down_chain_growth(self, down_chain_growth):
        """Sets the down_chain_growth of this GetTotalDataResponse.


        :param down_chain_growth: The down_chain_growth of this GetTotalDataResponse.  # noqa: E501
        :type: str
        """

        self._down_chain_growth = down_chain_growth

    @property
    def max_down(self):
        """Gets the max_down of this GetTotalDataResponse.  # noqa: E501


        :return: The max_down of this GetTotalDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._max_down

    @max_down.setter
    def max_down(self, max_down):
        """Sets the max_down of this GetTotalDataResponse.


        :param max_down: The max_down of this GetTotalDataResponse.  # noqa: E501
        :type: float
        """

        self._max_down = max_down

    @property
    def max_up(self):
        """Gets the max_up of this GetTotalDataResponse.  # noqa: E501


        :return: The max_up of this GetTotalDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._max_up

    @max_up.setter
    def max_up(self, max_up):
        """Sets the max_up of this GetTotalDataResponse.


        :param max_up: The max_up of this GetTotalDataResponse.  # noqa: E501
        :type: float
        """

        self._max_up = max_up

    @property
    def up_chain_growth(self):
        """Gets the up_chain_growth of this GetTotalDataResponse.  # noqa: E501


        :return: The up_chain_growth of this GetTotalDataResponse.  # noqa: E501
        :rtype: str
        """
        return self._up_chain_growth

    @up_chain_growth.setter
    def up_chain_growth(self, up_chain_growth):
        """Sets the up_chain_growth of this GetTotalDataResponse.


        :param up_chain_growth: The up_chain_growth of this GetTotalDataResponse.  # noqa: E501
        :type: str
        """

        self._up_chain_growth = up_chain_growth

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTotalDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTotalDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTotalDataResponse):
            return True

        return self.to_dict() != other.to_dict()
