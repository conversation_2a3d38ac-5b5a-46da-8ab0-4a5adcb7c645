# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetCisDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audit': 'str',
        'benchmark_type': 'str',
        'check_type': 'str',
        'remediation': 'str',
        'rule_id': 'str',
        'section_desc': 'str',
        'section_id': 'str',
        'severity': 'str'
    }

    attribute_map = {
        'audit': 'Audit',
        'benchmark_type': 'BenchmarkType',
        'check_type': 'CheckType',
        'remediation': 'Remediation',
        'rule_id': 'RuleID',
        'section_desc': 'SectionDesc',
        'section_id': 'SectionID',
        'severity': 'Severity'
    }

    def __init__(self, audit=None, benchmark_type=None, check_type=None, remediation=None, rule_id=None, section_desc=None, section_id=None, severity=None, _configuration=None):  # noqa: E501
        """DataForGetCisDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audit = None
        self._benchmark_type = None
        self._check_type = None
        self._remediation = None
        self._rule_id = None
        self._section_desc = None
        self._section_id = None
        self._severity = None
        self.discriminator = None

        if audit is not None:
            self.audit = audit
        if benchmark_type is not None:
            self.benchmark_type = benchmark_type
        if check_type is not None:
            self.check_type = check_type
        if remediation is not None:
            self.remediation = remediation
        if rule_id is not None:
            self.rule_id = rule_id
        if section_desc is not None:
            self.section_desc = section_desc
        if section_id is not None:
            self.section_id = section_id
        if severity is not None:
            self.severity = severity

    @property
    def audit(self):
        """Gets the audit of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The audit of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._audit

    @audit.setter
    def audit(self, audit):
        """Sets the audit of this DataForGetCisDetailOutput.


        :param audit: The audit of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._audit = audit

    @property
    def benchmark_type(self):
        """Gets the benchmark_type of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The benchmark_type of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._benchmark_type

    @benchmark_type.setter
    def benchmark_type(self, benchmark_type):
        """Sets the benchmark_type of this DataForGetCisDetailOutput.


        :param benchmark_type: The benchmark_type of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._benchmark_type = benchmark_type

    @property
    def check_type(self):
        """Gets the check_type of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The check_type of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_type

    @check_type.setter
    def check_type(self, check_type):
        """Sets the check_type of this DataForGetCisDetailOutput.


        :param check_type: The check_type of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._check_type = check_type

    @property
    def remediation(self):
        """Gets the remediation of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The remediation of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._remediation

    @remediation.setter
    def remediation(self, remediation):
        """Sets the remediation of this DataForGetCisDetailOutput.


        :param remediation: The remediation of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._remediation = remediation

    @property
    def rule_id(self):
        """Gets the rule_id of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The rule_id of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DataForGetCisDetailOutput.


        :param rule_id: The rule_id of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def section_desc(self):
        """Gets the section_desc of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The section_desc of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._section_desc

    @section_desc.setter
    def section_desc(self, section_desc):
        """Sets the section_desc of this DataForGetCisDetailOutput.


        :param section_desc: The section_desc of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._section_desc = section_desc

    @property
    def section_id(self):
        """Gets the section_id of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The section_id of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._section_id

    @section_id.setter
    def section_id(self, section_id):
        """Sets the section_id of this DataForGetCisDetailOutput.


        :param section_id: The section_id of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._section_id = section_id

    @property
    def severity(self):
        """Gets the severity of this DataForGetCisDetailOutput.  # noqa: E501


        :return: The severity of this DataForGetCisDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._severity

    @severity.setter
    def severity(self, severity):
        """Sets the severity of this DataForGetCisDetailOutput.


        :param severity: The severity of this DataForGetCisDetailOutput.  # noqa: E501
        :type: str
        """

        self._severity = severity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetCisDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetCisDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetCisDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
