# coding: utf-8

"""
    sqs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateQueueRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message_retention_period': 'int',
        'queue_display_name': 'str',
        'visibility_timeout': 'int'
    }

    attribute_map = {
        'message_retention_period': 'MessageRetentionPeriod',
        'queue_display_name': 'QueueDisplayName',
        'visibility_timeout': 'VisibilityTimeout'
    }

    def __init__(self, message_retention_period=None, queue_display_name=None, visibility_timeout=None, _configuration=None):  # noqa: E501
        """CreateQueueRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message_retention_period = None
        self._queue_display_name = None
        self._visibility_timeout = None
        self.discriminator = None

        if message_retention_period is not None:
            self.message_retention_period = message_retention_period
        self.queue_display_name = queue_display_name
        if visibility_timeout is not None:
            self.visibility_timeout = visibility_timeout

    @property
    def message_retention_period(self):
        """Gets the message_retention_period of this CreateQueueRequest.  # noqa: E501


        :return: The message_retention_period of this CreateQueueRequest.  # noqa: E501
        :rtype: int
        """
        return self._message_retention_period

    @message_retention_period.setter
    def message_retention_period(self, message_retention_period):
        """Sets the message_retention_period of this CreateQueueRequest.


        :param message_retention_period: The message_retention_period of this CreateQueueRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                message_retention_period is not None and message_retention_period > 604800):  # noqa: E501
            raise ValueError("Invalid value for `message_retention_period`, must be a value less than or equal to `604800`")  # noqa: E501
        if (self._configuration.client_side_validation and
                message_retention_period is not None and message_retention_period < 60):  # noqa: E501
            raise ValueError("Invalid value for `message_retention_period`, must be a value greater than or equal to `60`")  # noqa: E501

        self._message_retention_period = message_retention_period

    @property
    def queue_display_name(self):
        """Gets the queue_display_name of this CreateQueueRequest.  # noqa: E501


        :return: The queue_display_name of this CreateQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._queue_display_name

    @queue_display_name.setter
    def queue_display_name(self, queue_display_name):
        """Sets the queue_display_name of this CreateQueueRequest.


        :param queue_display_name: The queue_display_name of this CreateQueueRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and queue_display_name is None:
            raise ValueError("Invalid value for `queue_display_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                queue_display_name is not None and len(queue_display_name) > 255):
            raise ValueError("Invalid value for `queue_display_name`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                queue_display_name is not None and len(queue_display_name) < 1):
            raise ValueError("Invalid value for `queue_display_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._queue_display_name = queue_display_name

    @property
    def visibility_timeout(self):
        """Gets the visibility_timeout of this CreateQueueRequest.  # noqa: E501


        :return: The visibility_timeout of this CreateQueueRequest.  # noqa: E501
        :rtype: int
        """
        return self._visibility_timeout

    @visibility_timeout.setter
    def visibility_timeout(self, visibility_timeout):
        """Sets the visibility_timeout of this CreateQueueRequest.


        :param visibility_timeout: The visibility_timeout of this CreateQueueRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                visibility_timeout is not None and visibility_timeout > 600):  # noqa: E501
            raise ValueError("Invalid value for `visibility_timeout`, must be a value less than or equal to `600`")  # noqa: E501
        if (self._configuration.client_side_validation and
                visibility_timeout is not None and visibility_timeout < 0):  # noqa: E501
            raise ValueError("Invalid value for `visibility_timeout`, must be a value greater than or equal to `0`")  # noqa: E501

        self._visibility_timeout = visibility_timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateQueueRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateQueueRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateQueueRequest):
            return True

        return self.to_dict() != other.to_dict()
