# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskForDescribeTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'end_at': 'str',
        'id': 'str',
        'process': 'int',
        'resource_id': 'str',
        'status': 'str',
        'type': 'str',
        'updated_at': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'end_at': 'EndAt',
        'id': 'Id',
        'process': 'Process',
        'resource_id': 'ResourceId',
        'status': 'Status',
        'type': 'Type',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, created_at=None, end_at=None, id=None, process=None, resource_id=None, status=None, type=None, updated_at=None, _configuration=None):  # noqa: E501
        """TaskForDescribeTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._end_at = None
        self._id = None
        self._process = None
        self._resource_id = None
        self._status = None
        self._type = None
        self._updated_at = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if end_at is not None:
            self.end_at = end_at
        if id is not None:
            self.id = id
        if process is not None:
            self.process = process
        if resource_id is not None:
            self.resource_id = resource_id
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def created_at(self):
        """Gets the created_at of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The created_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this TaskForDescribeTasksOutput.


        :param created_at: The created_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def end_at(self):
        """Gets the end_at of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The end_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_at

    @end_at.setter
    def end_at(self, end_at):
        """Sets the end_at of this TaskForDescribeTasksOutput.


        :param end_at: The end_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._end_at = end_at

    @property
    def id(self):
        """Gets the id of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The id of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this TaskForDescribeTasksOutput.


        :param id: The id of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def process(self):
        """Gets the process of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The process of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._process

    @process.setter
    def process(self, process):
        """Sets the process of this TaskForDescribeTasksOutput.


        :param process: The process of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: int
        """

        self._process = process

    @property
    def resource_id(self):
        """Gets the resource_id of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The resource_id of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this TaskForDescribeTasksOutput.


        :param resource_id: The resource_id of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def status(self):
        """Gets the status of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The status of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TaskForDescribeTasksOutput.


        :param status: The status of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The type of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this TaskForDescribeTasksOutput.


        :param type: The type of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def updated_at(self):
        """Gets the updated_at of this TaskForDescribeTasksOutput.  # noqa: E501


        :return: The updated_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this TaskForDescribeTasksOutput.


        :param updated_at: The updated_at of this TaskForDescribeTasksOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskForDescribeTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskForDescribeTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskForDescribeTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
