# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeMigrationJobsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'destination_region': 'str',
        'destination_type': 'list[str]',
        'migration_job_id': 'str',
        'migration_job_name': 'str',
        'migration_job_state': 'str',
        'migration_source_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeMigrationJobsInput]'
    }

    attribute_map = {
        'destination_region': 'DestinationRegion',
        'destination_type': 'DestinationType',
        'migration_job_id': 'MigrationJobId',
        'migration_job_name': 'MigrationJobName',
        'migration_job_state': 'MigrationJobState',
        'migration_source_ids': 'MigrationSourceIds',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, destination_region=None, destination_type=None, migration_job_id=None, migration_job_name=None, migration_job_state=None, migration_source_ids=None, page_number=None, page_size=None, project_name=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeMigrationJobsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._destination_region = None
        self._destination_type = None
        self._migration_job_id = None
        self._migration_job_name = None
        self._migration_job_state = None
        self._migration_source_ids = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self.discriminator = None

        if destination_region is not None:
            self.destination_region = destination_region
        if destination_type is not None:
            self.destination_type = destination_type
        if migration_job_id is not None:
            self.migration_job_id = migration_job_id
        if migration_job_name is not None:
            self.migration_job_name = migration_job_name
        if migration_job_state is not None:
            self.migration_job_state = migration_job_state
        if migration_source_ids is not None:
            self.migration_source_ids = migration_source_ids
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def destination_region(self):
        """Gets the destination_region of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The destination_region of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_region

    @destination_region.setter
    def destination_region(self, destination_region):
        """Sets the destination_region of this DescribeMigrationJobsRequest.


        :param destination_region: The destination_region of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: str
        """

        self._destination_region = destination_region

    @property
    def destination_type(self):
        """Gets the destination_type of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The destination_type of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination_type

    @destination_type.setter
    def destination_type(self, destination_type):
        """Sets the destination_type of this DescribeMigrationJobsRequest.


        :param destination_type: The destination_type of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: list[str]
        """

        self._destination_type = destination_type

    @property
    def migration_job_id(self):
        """Gets the migration_job_id of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The migration_job_id of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_id

    @migration_job_id.setter
    def migration_job_id(self, migration_job_id):
        """Sets the migration_job_id of this DescribeMigrationJobsRequest.


        :param migration_job_id: The migration_job_id of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_id = migration_job_id

    @property
    def migration_job_name(self):
        """Gets the migration_job_name of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The migration_job_name of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_name

    @migration_job_name.setter
    def migration_job_name(self, migration_job_name):
        """Sets the migration_job_name of this DescribeMigrationJobsRequest.


        :param migration_job_name: The migration_job_name of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_name = migration_job_name

    @property
    def migration_job_state(self):
        """Gets the migration_job_state of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The migration_job_state of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_state

    @migration_job_state.setter
    def migration_job_state(self, migration_job_state):
        """Sets the migration_job_state of this DescribeMigrationJobsRequest.


        :param migration_job_state: The migration_job_state of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: str
        """

        self._migration_job_state = migration_job_state

    @property
    def migration_source_ids(self):
        """Gets the migration_source_ids of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The migration_source_ids of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._migration_source_ids

    @migration_source_ids.setter
    def migration_source_ids(self, migration_source_ids):
        """Sets the migration_source_ids of this DescribeMigrationJobsRequest.


        :param migration_source_ids: The migration_source_ids of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: list[str]
        """

        self._migration_source_ids = migration_source_ids

    @property
    def page_number(self):
        """Gets the page_number of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The page_number of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeMigrationJobsRequest.


        :param page_number: The page_number of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The page_size of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeMigrationJobsRequest.


        :param page_size: The page_size of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The project_name of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeMigrationJobsRequest.


        :param project_name: The project_name of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeMigrationJobsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeMigrationJobsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeMigrationJobsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeMigrationJobsRequest.


        :param tag_filters: The tag_filters of this DescribeMigrationJobsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeMigrationJobsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeMigrationJobsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeMigrationJobsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeMigrationJobsRequest):
            return True

        return self.to_dict() != other.to_dict()
