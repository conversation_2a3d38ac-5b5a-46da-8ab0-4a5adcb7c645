# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IOCDataForGetAlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ioc_detail': 'list[IOCDetailForGetAlarmDetailOutput]'
    }

    attribute_map = {
        'ioc_detail': 'IOCDetail'
    }

    def __init__(self, ioc_detail=None, _configuration=None):  # noqa: E501
        """IOCDataForGetAlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ioc_detail = None
        self.discriminator = None

        if ioc_detail is not None:
            self.ioc_detail = ioc_detail

    @property
    def ioc_detail(self):
        """Gets the ioc_detail of this IOCDataForGetAlarmDetailOutput.  # noqa: E501


        :return: The ioc_detail of this IOCDataForGetAlarmDetailOutput.  # noqa: E501
        :rtype: list[IOCDetailForGetAlarmDetailOutput]
        """
        return self._ioc_detail

    @ioc_detail.setter
    def ioc_detail(self, ioc_detail):
        """Sets the ioc_detail of this IOCDataForGetAlarmDetailOutput.


        :param ioc_detail: The ioc_detail of this IOCDataForGetAlarmDetailOutput.  # noqa: E501
        :type: list[IOCDetailForGetAlarmDetailOutput]
        """

        self._ioc_detail = ioc_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IOCDataForGetAlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IOCDataForGetAlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IOCDataForGetAlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
