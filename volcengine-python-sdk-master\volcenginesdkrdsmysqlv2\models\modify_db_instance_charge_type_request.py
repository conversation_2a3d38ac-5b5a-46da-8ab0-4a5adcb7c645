# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceChargeTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'charge_type': 'str',
        'instance_id': 'str',
        'period': 'int',
        'period_unit': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'charge_type': 'ChargeType',
        'instance_id': 'InstanceId',
        'period': 'Period',
        'period_unit': 'PeriodUnit'
    }

    def __init__(self, auto_renew=None, charge_type=None, instance_id=None, period=None, period_unit=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceChargeTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._charge_type = None
        self._instance_id = None
        self._period = None
        self._period_unit = None
        self.discriminator = None

        if auto_renew is not None:
            self.auto_renew = auto_renew
        self.charge_type = charge_type
        self.instance_id = instance_id
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit

    @property
    def auto_renew(self):
        """Gets the auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this ModifyDBInstanceChargeTypeRequest.


        :param auto_renew: The auto_renew of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def charge_type(self):
        """Gets the charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ModifyDBInstanceChargeTypeRequest.


        :param charge_type: The charge_type of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceChargeTypeRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def period(self):
        """Gets the period of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The period of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this ModifyDBInstanceChargeTypeRequest.


        :param period: The period of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501


        :return: The period_unit of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this ModifyDBInstanceChargeTypeRequest.


        :param period_unit: The period_unit of this ModifyDBInstanceChargeTypeRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceChargeTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceChargeTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceChargeTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
