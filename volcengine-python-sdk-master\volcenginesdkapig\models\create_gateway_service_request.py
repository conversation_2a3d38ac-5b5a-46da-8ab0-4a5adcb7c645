# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateGatewayServiceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_spec': 'AuthSpecForCreateGatewayServiceInput',
        'comments': 'str',
        'gateway_id': 'str',
        'protocol': 'list[str]',
        'service_name': 'str'
    }

    attribute_map = {
        'auth_spec': 'AuthSpec',
        'comments': 'Comments',
        'gateway_id': 'GatewayId',
        'protocol': 'Protocol',
        'service_name': 'ServiceName'
    }

    def __init__(self, auth_spec=None, comments=None, gateway_id=None, protocol=None, service_name=None, _configuration=None):  # noqa: E501
        """CreateGatewayServiceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_spec = None
        self._comments = None
        self._gateway_id = None
        self._protocol = None
        self._service_name = None
        self.discriminator = None

        if auth_spec is not None:
            self.auth_spec = auth_spec
        if comments is not None:
            self.comments = comments
        self.gateway_id = gateway_id
        if protocol is not None:
            self.protocol = protocol
        self.service_name = service_name

    @property
    def auth_spec(self):
        """Gets the auth_spec of this CreateGatewayServiceRequest.  # noqa: E501


        :return: The auth_spec of this CreateGatewayServiceRequest.  # noqa: E501
        :rtype: AuthSpecForCreateGatewayServiceInput
        """
        return self._auth_spec

    @auth_spec.setter
    def auth_spec(self, auth_spec):
        """Sets the auth_spec of this CreateGatewayServiceRequest.


        :param auth_spec: The auth_spec of this CreateGatewayServiceRequest.  # noqa: E501
        :type: AuthSpecForCreateGatewayServiceInput
        """

        self._auth_spec = auth_spec

    @property
    def comments(self):
        """Gets the comments of this CreateGatewayServiceRequest.  # noqa: E501


        :return: The comments of this CreateGatewayServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this CreateGatewayServiceRequest.


        :param comments: The comments of this CreateGatewayServiceRequest.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def gateway_id(self):
        """Gets the gateway_id of this CreateGatewayServiceRequest.  # noqa: E501


        :return: The gateway_id of this CreateGatewayServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this CreateGatewayServiceRequest.


        :param gateway_id: The gateway_id of this CreateGatewayServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and gateway_id is None:
            raise ValueError("Invalid value for `gateway_id`, must not be `None`")  # noqa: E501

        self._gateway_id = gateway_id

    @property
    def protocol(self):
        """Gets the protocol of this CreateGatewayServiceRequest.  # noqa: E501


        :return: The protocol of this CreateGatewayServiceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateGatewayServiceRequest.


        :param protocol: The protocol of this CreateGatewayServiceRequest.  # noqa: E501
        :type: list[str]
        """

        self._protocol = protocol

    @property
    def service_name(self):
        """Gets the service_name of this CreateGatewayServiceRequest.  # noqa: E501


        :return: The service_name of this CreateGatewayServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this CreateGatewayServiceRequest.


        :param service_name: The service_name of this CreateGatewayServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and service_name is None:
            raise ValueError("Invalid value for `service_name`, must not be `None`")  # noqa: E501

        self._service_name = service_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateGatewayServiceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateGatewayServiceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateGatewayServiceRequest):
            return True

        return self.to_dict() != other.to_dict()
