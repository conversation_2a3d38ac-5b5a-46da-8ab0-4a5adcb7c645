# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuditLogAlarmForGetHidsAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authorization_reason': 'str',
        'binding_roleref': 'str',
        'binding_subject': 'str',
        'body': 'str',
        'exec_command': 'str',
        'exec_container': 'str',
        'images': 'list[str]',
        'impersonated_user_groups': 'str',
        'impersonated_user_name': 'str',
        'node_host': 'str',
        'node_name': 'str',
        'privileged_capabilities': 'list[str]',
        'read_only_mounts': 'list[str]',
        'read_write_mounts': 'list[str]',
        'real_user_groups': 'list[str]',
        'real_user_name': 'str',
        'reason_user_agent': 'str',
        'request_uri': 'str',
        'resource_kind': 'str',
        'resource_name': 'str',
        'resource_namespace': 'str',
        'response_code': 'str',
        'response_reason': 'str',
        'response_status': 'str',
        'shared_namespaces': 'list[str]',
        'source_ip': 'str',
        'source_ip_asset': 'str',
        'user_agent': 'str',
        'user_groups': 'str',
        'user_name': 'str',
        'verb': 'str',
        'workload_asset': 'str'
    }

    attribute_map = {
        'authorization_reason': 'authorization_reason',
        'binding_roleref': 'binding_roleref',
        'binding_subject': 'binding_subject',
        'body': 'body',
        'exec_command': 'exec_command',
        'exec_container': 'exec_container',
        'images': 'images',
        'impersonated_user_groups': 'impersonated_user_groups',
        'impersonated_user_name': 'impersonated_user_name',
        'node_host': 'node_host',
        'node_name': 'node_name',
        'privileged_capabilities': 'privileged_capabilities',
        'read_only_mounts': 'read_only_mounts',
        'read_write_mounts': 'read_write_mounts',
        'real_user_groups': 'real_user_groups',
        'real_user_name': 'real_user_name',
        'reason_user_agent': 'reason_user_agent',
        'request_uri': 'request_uri',
        'resource_kind': 'resource_kind',
        'resource_name': 'resource_name',
        'resource_namespace': 'resource_namespace',
        'response_code': 'response_code',
        'response_reason': 'response_reason',
        'response_status': 'response_status',
        'shared_namespaces': 'shared_namespaces',
        'source_ip': 'source_ip',
        'source_ip_asset': 'source_ip_asset',
        'user_agent': 'user_agent',
        'user_groups': 'user_groups',
        'user_name': 'user_name',
        'verb': 'verb',
        'workload_asset': 'workload_asset'
    }

    def __init__(self, authorization_reason=None, binding_roleref=None, binding_subject=None, body=None, exec_command=None, exec_container=None, images=None, impersonated_user_groups=None, impersonated_user_name=None, node_host=None, node_name=None, privileged_capabilities=None, read_only_mounts=None, read_write_mounts=None, real_user_groups=None, real_user_name=None, reason_user_agent=None, request_uri=None, resource_kind=None, resource_name=None, resource_namespace=None, response_code=None, response_reason=None, response_status=None, shared_namespaces=None, source_ip=None, source_ip_asset=None, user_agent=None, user_groups=None, user_name=None, verb=None, workload_asset=None, _configuration=None):  # noqa: E501
        """AuditLogAlarmForGetHidsAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authorization_reason = None
        self._binding_roleref = None
        self._binding_subject = None
        self._body = None
        self._exec_command = None
        self._exec_container = None
        self._images = None
        self._impersonated_user_groups = None
        self._impersonated_user_name = None
        self._node_host = None
        self._node_name = None
        self._privileged_capabilities = None
        self._read_only_mounts = None
        self._read_write_mounts = None
        self._real_user_groups = None
        self._real_user_name = None
        self._reason_user_agent = None
        self._request_uri = None
        self._resource_kind = None
        self._resource_name = None
        self._resource_namespace = None
        self._response_code = None
        self._response_reason = None
        self._response_status = None
        self._shared_namespaces = None
        self._source_ip = None
        self._source_ip_asset = None
        self._user_agent = None
        self._user_groups = None
        self._user_name = None
        self._verb = None
        self._workload_asset = None
        self.discriminator = None

        if authorization_reason is not None:
            self.authorization_reason = authorization_reason
        if binding_roleref is not None:
            self.binding_roleref = binding_roleref
        if binding_subject is not None:
            self.binding_subject = binding_subject
        if body is not None:
            self.body = body
        if exec_command is not None:
            self.exec_command = exec_command
        if exec_container is not None:
            self.exec_container = exec_container
        if images is not None:
            self.images = images
        if impersonated_user_groups is not None:
            self.impersonated_user_groups = impersonated_user_groups
        if impersonated_user_name is not None:
            self.impersonated_user_name = impersonated_user_name
        if node_host is not None:
            self.node_host = node_host
        if node_name is not None:
            self.node_name = node_name
        if privileged_capabilities is not None:
            self.privileged_capabilities = privileged_capabilities
        if read_only_mounts is not None:
            self.read_only_mounts = read_only_mounts
        if read_write_mounts is not None:
            self.read_write_mounts = read_write_mounts
        if real_user_groups is not None:
            self.real_user_groups = real_user_groups
        if real_user_name is not None:
            self.real_user_name = real_user_name
        if reason_user_agent is not None:
            self.reason_user_agent = reason_user_agent
        if request_uri is not None:
            self.request_uri = request_uri
        if resource_kind is not None:
            self.resource_kind = resource_kind
        if resource_name is not None:
            self.resource_name = resource_name
        if resource_namespace is not None:
            self.resource_namespace = resource_namespace
        if response_code is not None:
            self.response_code = response_code
        if response_reason is not None:
            self.response_reason = response_reason
        if response_status is not None:
            self.response_status = response_status
        if shared_namespaces is not None:
            self.shared_namespaces = shared_namespaces
        if source_ip is not None:
            self.source_ip = source_ip
        if source_ip_asset is not None:
            self.source_ip_asset = source_ip_asset
        if user_agent is not None:
            self.user_agent = user_agent
        if user_groups is not None:
            self.user_groups = user_groups
        if user_name is not None:
            self.user_name = user_name
        if verb is not None:
            self.verb = verb
        if workload_asset is not None:
            self.workload_asset = workload_asset

    @property
    def authorization_reason(self):
        """Gets the authorization_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The authorization_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._authorization_reason

    @authorization_reason.setter
    def authorization_reason(self, authorization_reason):
        """Sets the authorization_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param authorization_reason: The authorization_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._authorization_reason = authorization_reason

    @property
    def binding_roleref(self):
        """Gets the binding_roleref of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The binding_roleref of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._binding_roleref

    @binding_roleref.setter
    def binding_roleref(self, binding_roleref):
        """Sets the binding_roleref of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param binding_roleref: The binding_roleref of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._binding_roleref = binding_roleref

    @property
    def binding_subject(self):
        """Gets the binding_subject of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The binding_subject of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._binding_subject

    @binding_subject.setter
    def binding_subject(self, binding_subject):
        """Sets the binding_subject of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param binding_subject: The binding_subject of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._binding_subject = binding_subject

    @property
    def body(self):
        """Gets the body of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The body of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._body

    @body.setter
    def body(self, body):
        """Sets the body of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param body: The body of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._body = body

    @property
    def exec_command(self):
        """Gets the exec_command of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exec_command of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exec_command

    @exec_command.setter
    def exec_command(self, exec_command):
        """Sets the exec_command of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param exec_command: The exec_command of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exec_command = exec_command

    @property
    def exec_container(self):
        """Gets the exec_container of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exec_container of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exec_container

    @exec_container.setter
    def exec_container(self, exec_container):
        """Sets the exec_container of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param exec_container: The exec_container of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exec_container = exec_container

    @property
    def images(self):
        """Gets the images of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The images of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._images

    @images.setter
    def images(self, images):
        """Sets the images of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param images: The images of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._images = images

    @property
    def impersonated_user_groups(self):
        """Gets the impersonated_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The impersonated_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._impersonated_user_groups

    @impersonated_user_groups.setter
    def impersonated_user_groups(self, impersonated_user_groups):
        """Sets the impersonated_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param impersonated_user_groups: The impersonated_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._impersonated_user_groups = impersonated_user_groups

    @property
    def impersonated_user_name(self):
        """Gets the impersonated_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The impersonated_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._impersonated_user_name

    @impersonated_user_name.setter
    def impersonated_user_name(self, impersonated_user_name):
        """Sets the impersonated_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param impersonated_user_name: The impersonated_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._impersonated_user_name = impersonated_user_name

    @property
    def node_host(self):
        """Gets the node_host of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The node_host of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_host

    @node_host.setter
    def node_host(self, node_host):
        """Sets the node_host of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param node_host: The node_host of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._node_host = node_host

    @property
    def node_name(self):
        """Gets the node_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The node_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param node_name: The node_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def privileged_capabilities(self):
        """Gets the privileged_capabilities of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The privileged_capabilities of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._privileged_capabilities

    @privileged_capabilities.setter
    def privileged_capabilities(self, privileged_capabilities):
        """Sets the privileged_capabilities of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param privileged_capabilities: The privileged_capabilities of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._privileged_capabilities = privileged_capabilities

    @property
    def read_only_mounts(self):
        """Gets the read_only_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The read_only_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._read_only_mounts

    @read_only_mounts.setter
    def read_only_mounts(self, read_only_mounts):
        """Sets the read_only_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param read_only_mounts: The read_only_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._read_only_mounts = read_only_mounts

    @property
    def read_write_mounts(self):
        """Gets the read_write_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The read_write_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._read_write_mounts

    @read_write_mounts.setter
    def read_write_mounts(self, read_write_mounts):
        """Sets the read_write_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param read_write_mounts: The read_write_mounts of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._read_write_mounts = read_write_mounts

    @property
    def real_user_groups(self):
        """Gets the real_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The real_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._real_user_groups

    @real_user_groups.setter
    def real_user_groups(self, real_user_groups):
        """Sets the real_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param real_user_groups: The real_user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._real_user_groups = real_user_groups

    @property
    def real_user_name(self):
        """Gets the real_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The real_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._real_user_name

    @real_user_name.setter
    def real_user_name(self, real_user_name):
        """Sets the real_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param real_user_name: The real_user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._real_user_name = real_user_name

    @property
    def reason_user_agent(self):
        """Gets the reason_user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The reason_user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason_user_agent

    @reason_user_agent.setter
    def reason_user_agent(self, reason_user_agent):
        """Sets the reason_user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param reason_user_agent: The reason_user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._reason_user_agent = reason_user_agent

    @property
    def request_uri(self):
        """Gets the request_uri of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The request_uri of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._request_uri

    @request_uri.setter
    def request_uri(self, request_uri):
        """Sets the request_uri of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param request_uri: The request_uri of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._request_uri = request_uri

    @property
    def resource_kind(self):
        """Gets the resource_kind of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The resource_kind of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_kind

    @resource_kind.setter
    def resource_kind(self, resource_kind):
        """Sets the resource_kind of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param resource_kind: The resource_kind of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._resource_kind = resource_kind

    @property
    def resource_name(self):
        """Gets the resource_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The resource_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_name

    @resource_name.setter
    def resource_name(self, resource_name):
        """Sets the resource_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param resource_name: The resource_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._resource_name = resource_name

    @property
    def resource_namespace(self):
        """Gets the resource_namespace of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The resource_namespace of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_namespace

    @resource_namespace.setter
    def resource_namespace(self, resource_namespace):
        """Sets the resource_namespace of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param resource_namespace: The resource_namespace of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._resource_namespace = resource_namespace

    @property
    def response_code(self):
        """Gets the response_code of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The response_code of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._response_code

    @response_code.setter
    def response_code(self, response_code):
        """Sets the response_code of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param response_code: The response_code of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._response_code = response_code

    @property
    def response_reason(self):
        """Gets the response_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The response_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._response_reason

    @response_reason.setter
    def response_reason(self, response_reason):
        """Sets the response_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param response_reason: The response_reason of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._response_reason = response_reason

    @property
    def response_status(self):
        """Gets the response_status of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The response_status of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._response_status

    @response_status.setter
    def response_status(self, response_status):
        """Sets the response_status of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param response_status: The response_status of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._response_status = response_status

    @property
    def shared_namespaces(self):
        """Gets the shared_namespaces of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The shared_namespaces of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._shared_namespaces

    @shared_namespaces.setter
    def shared_namespaces(self, shared_namespaces):
        """Sets the shared_namespaces of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param shared_namespaces: The shared_namespaces of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._shared_namespaces = shared_namespaces

    @property
    def source_ip(self):
        """Gets the source_ip of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_ip of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_ip

    @source_ip.setter
    def source_ip(self, source_ip):
        """Sets the source_ip of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param source_ip: The source_ip of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_ip = source_ip

    @property
    def source_ip_asset(self):
        """Gets the source_ip_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_ip_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_ip_asset

    @source_ip_asset.setter
    def source_ip_asset(self, source_ip_asset):
        """Sets the source_ip_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param source_ip_asset: The source_ip_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_ip_asset = source_ip_asset

    @property
    def user_agent(self):
        """Gets the user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param user_agent: The user_agent of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_groups(self):
        """Gets the user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_groups

    @user_groups.setter
    def user_groups(self, user_groups):
        """Sets the user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param user_groups: The user_groups of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_groups = user_groups

    @property
    def user_name(self):
        """Gets the user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param user_name: The user_name of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def verb(self):
        """Gets the verb of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The verb of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._verb

    @verb.setter
    def verb(self, verb):
        """Sets the verb of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param verb: The verb of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._verb = verb

    @property
    def workload_asset(self):
        """Gets the workload_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501


        :return: The workload_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._workload_asset

    @workload_asset.setter
    def workload_asset(self, workload_asset):
        """Sets the workload_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.


        :param workload_asset: The workload_asset of this AuditLogAlarmForGetHidsAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._workload_asset = workload_asset

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuditLogAlarmForGetHidsAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuditLogAlarmForGetHidsAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuditLogAlarmForGetHidsAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
