# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_point_id': 'str',
        'backup_type': 'str',
        'client_token': 'str',
        'instance_id': 'str',
        'time_point': 'str'
    }

    attribute_map = {
        'backup_point_id': 'BackupPointId',
        'backup_type': 'BackupType',
        'client_token': 'ClientToken',
        'instance_id': 'InstanceId',
        'time_point': 'TimePoint'
    }

    def __init__(self, backup_point_id=None, backup_type=None, client_token=None, instance_id=None, time_point=None, _configuration=None):  # noqa: E501
        """RestoreDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_point_id = None
        self._backup_type = None
        self._client_token = None
        self._instance_id = None
        self._time_point = None
        self.discriminator = None

        if backup_point_id is not None:
            self.backup_point_id = backup_point_id
        if backup_type is not None:
            self.backup_type = backup_type
        if client_token is not None:
            self.client_token = client_token
        self.instance_id = instance_id
        if time_point is not None:
            self.time_point = time_point

    @property
    def backup_point_id(self):
        """Gets the backup_point_id of this RestoreDBInstanceRequest.  # noqa: E501


        :return: The backup_point_id of this RestoreDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_id

    @backup_point_id.setter
    def backup_point_id(self, backup_point_id):
        """Sets the backup_point_id of this RestoreDBInstanceRequest.


        :param backup_point_id: The backup_point_id of this RestoreDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_id = backup_point_id

    @property
    def backup_type(self):
        """Gets the backup_type of this RestoreDBInstanceRequest.  # noqa: E501


        :return: The backup_type of this RestoreDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_type

    @backup_type.setter
    def backup_type(self, backup_type):
        """Sets the backup_type of this RestoreDBInstanceRequest.


        :param backup_type: The backup_type of this RestoreDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_type = backup_type

    @property
    def client_token(self):
        """Gets the client_token of this RestoreDBInstanceRequest.  # noqa: E501


        :return: The client_token of this RestoreDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this RestoreDBInstanceRequest.


        :param client_token: The client_token of this RestoreDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this RestoreDBInstanceRequest.  # noqa: E501


        :return: The instance_id of this RestoreDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this RestoreDBInstanceRequest.


        :param instance_id: The instance_id of this RestoreDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def time_point(self):
        """Gets the time_point of this RestoreDBInstanceRequest.  # noqa: E501


        :return: The time_point of this RestoreDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._time_point

    @time_point.setter
    def time_point(self, time_point):
        """Sets the time_point of this RestoreDBInstanceRequest.


        :param time_point: The time_point of this RestoreDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._time_point = time_point

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
