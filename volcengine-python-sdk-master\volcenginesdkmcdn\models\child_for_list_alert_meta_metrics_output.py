# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ChildForListAlertMetaMetricsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'format': 'list[str]',
        'id': 'str',
        'name': 'str',
        'protocol_type': 'str',
        'support_algo': 'list[str]',
        'trigger_type': 'str'
    }

    attribute_map = {
        'format': 'Format',
        'id': 'Id',
        'name': 'Name',
        'protocol_type': 'ProtocolType',
        'support_algo': 'SupportAlgo',
        'trigger_type': 'TriggerType'
    }

    def __init__(self, format=None, id=None, name=None, protocol_type=None, support_algo=None, trigger_type=None, _configuration=None):  # noqa: E501
        """ChildForListAlertMetaMetricsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._format = None
        self._id = None
        self._name = None
        self._protocol_type = None
        self._support_algo = None
        self._trigger_type = None
        self.discriminator = None

        if format is not None:
            self.format = format
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if protocol_type is not None:
            self.protocol_type = protocol_type
        if support_algo is not None:
            self.support_algo = support_algo
        if trigger_type is not None:
            self.trigger_type = trigger_type

    @property
    def format(self):
        """Gets the format of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The format of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._format

    @format.setter
    def format(self, format):
        """Sets the format of this ChildForListAlertMetaMetricsOutput.


        :param format: The format of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: list[str]
        """

        self._format = format

    @property
    def id(self):
        """Gets the id of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The id of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ChildForListAlertMetaMetricsOutput.


        :param id: The id of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The name of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ChildForListAlertMetaMetricsOutput.


        :param name: The name of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def protocol_type(self):
        """Gets the protocol_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The protocol_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this ChildForListAlertMetaMetricsOutput.


        :param protocol_type: The protocol_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: str
        """

        self._protocol_type = protocol_type

    @property
    def support_algo(self):
        """Gets the support_algo of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The support_algo of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._support_algo

    @support_algo.setter
    def support_algo(self, support_algo):
        """Sets the support_algo of this ChildForListAlertMetaMetricsOutput.


        :param support_algo: The support_algo of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: list[str]
        """

        self._support_algo = support_algo

    @property
    def trigger_type(self):
        """Gets the trigger_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501


        :return: The trigger_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :rtype: str
        """
        return self._trigger_type

    @trigger_type.setter
    def trigger_type(self, trigger_type):
        """Sets the trigger_type of this ChildForListAlertMetaMetricsOutput.


        :param trigger_type: The trigger_type of this ChildForListAlertMetaMetricsOutput.  # noqa: E501
        :type: str
        """

        self._trigger_type = trigger_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ChildForListAlertMetaMetricsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ChildForListAlertMetaMetricsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ChildForListAlertMetaMetricsOutput):
            return True

        return self.to_dict() != other.to_dict()
