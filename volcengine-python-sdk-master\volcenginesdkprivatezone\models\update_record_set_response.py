# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateRecordSetResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fqdn': 'str',
        'host': 'str',
        'id': 'str',
        'line': 'str',
        'type': 'str',
        'weight_enabled': 'bool'
    }

    attribute_map = {
        'fqdn': 'FQDN',
        'host': 'Host',
        'id': 'ID',
        'line': 'Line',
        'type': 'Type',
        'weight_enabled': 'WeightEnabled'
    }

    def __init__(self, fqdn=None, host=None, id=None, line=None, type=None, weight_enabled=None, _configuration=None):  # noqa: E501
        """UpdateRecordSetResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fqdn = None
        self._host = None
        self._id = None
        self._line = None
        self._type = None
        self._weight_enabled = None
        self.discriminator = None

        if fqdn is not None:
            self.fqdn = fqdn
        if host is not None:
            self.host = host
        if id is not None:
            self.id = id
        if line is not None:
            self.line = line
        if type is not None:
            self.type = type
        if weight_enabled is not None:
            self.weight_enabled = weight_enabled

    @property
    def fqdn(self):
        """Gets the fqdn of this UpdateRecordSetResponse.  # noqa: E501


        :return: The fqdn of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: str
        """
        return self._fqdn

    @fqdn.setter
    def fqdn(self, fqdn):
        """Sets the fqdn of this UpdateRecordSetResponse.


        :param fqdn: The fqdn of this UpdateRecordSetResponse.  # noqa: E501
        :type: str
        """

        self._fqdn = fqdn

    @property
    def host(self):
        """Gets the host of this UpdateRecordSetResponse.  # noqa: E501


        :return: The host of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this UpdateRecordSetResponse.


        :param host: The host of this UpdateRecordSetResponse.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def id(self):
        """Gets the id of this UpdateRecordSetResponse.  # noqa: E501


        :return: The id of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateRecordSetResponse.


        :param id: The id of this UpdateRecordSetResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def line(self):
        """Gets the line of this UpdateRecordSetResponse.  # noqa: E501


        :return: The line of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this UpdateRecordSetResponse.


        :param line: The line of this UpdateRecordSetResponse.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def type(self):
        """Gets the type of this UpdateRecordSetResponse.  # noqa: E501


        :return: The type of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this UpdateRecordSetResponse.


        :param type: The type of this UpdateRecordSetResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def weight_enabled(self):
        """Gets the weight_enabled of this UpdateRecordSetResponse.  # noqa: E501


        :return: The weight_enabled of this UpdateRecordSetResponse.  # noqa: E501
        :rtype: bool
        """
        return self._weight_enabled

    @weight_enabled.setter
    def weight_enabled(self, weight_enabled):
        """Sets the weight_enabled of this UpdateRecordSetResponse.


        :param weight_enabled: The weight_enabled of this UpdateRecordSetResponse.  # noqa: E501
        :type: bool
        """

        self._weight_enabled = weight_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateRecordSetResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateRecordSetResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateRecordSetResponse):
            return True

        return self.to_dict() != other.to_dict()
