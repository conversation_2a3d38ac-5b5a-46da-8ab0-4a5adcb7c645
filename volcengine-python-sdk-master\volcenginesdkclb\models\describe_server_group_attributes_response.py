# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeServerGroupAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address_ip_version': 'str',
        'any_port_enabled': 'str',
        'description': 'str',
        'listeners': 'list[str]',
        'load_balancer_id': 'str',
        'request_id': 'str',
        'server_group_id': 'str',
        'server_group_name': 'str',
        'servers': 'list[ServerForDescribeServerGroupAttributesOutput]',
        'tags': 'list[TagForDescribeServerGroupAttributesOutput]',
        'type': 'str'
    }

    attribute_map = {
        'address_ip_version': 'AddressIpVersion',
        'any_port_enabled': 'AnyPortEnabled',
        'description': 'Description',
        'listeners': 'Listeners',
        'load_balancer_id': 'LoadBalancerId',
        'request_id': 'RequestId',
        'server_group_id': 'ServerGroupId',
        'server_group_name': 'ServerGroupName',
        'servers': 'Servers',
        'tags': 'Tags',
        'type': 'Type'
    }

    def __init__(self, address_ip_version=None, any_port_enabled=None, description=None, listeners=None, load_balancer_id=None, request_id=None, server_group_id=None, server_group_name=None, servers=None, tags=None, type=None, _configuration=None):  # noqa: E501
        """DescribeServerGroupAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address_ip_version = None
        self._any_port_enabled = None
        self._description = None
        self._listeners = None
        self._load_balancer_id = None
        self._request_id = None
        self._server_group_id = None
        self._server_group_name = None
        self._servers = None
        self._tags = None
        self._type = None
        self.discriminator = None

        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if any_port_enabled is not None:
            self.any_port_enabled = any_port_enabled
        if description is not None:
            self.description = description
        if listeners is not None:
            self.listeners = listeners
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if request_id is not None:
            self.request_id = request_id
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if servers is not None:
            self.servers = servers
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The address_ip_version of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this DescribeServerGroupAttributesResponse.


        :param address_ip_version: The address_ip_version of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def any_port_enabled(self):
        """Gets the any_port_enabled of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The any_port_enabled of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._any_port_enabled

    @any_port_enabled.setter
    def any_port_enabled(self, any_port_enabled):
        """Sets the any_port_enabled of this DescribeServerGroupAttributesResponse.


        :param any_port_enabled: The any_port_enabled of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._any_port_enabled = any_port_enabled

    @property
    def description(self):
        """Gets the description of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The description of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeServerGroupAttributesResponse.


        :param description: The description of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def listeners(self):
        """Gets the listeners of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The listeners of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._listeners

    @listeners.setter
    def listeners(self, listeners):
        """Sets the listeners of this DescribeServerGroupAttributesResponse.


        :param listeners: The listeners of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._listeners = listeners

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The load_balancer_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this DescribeServerGroupAttributesResponse.


        :param load_balancer_id: The load_balancer_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def request_id(self):
        """Gets the request_id of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeServerGroupAttributesResponse.


        :param request_id: The request_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def server_group_id(self):
        """Gets the server_group_id of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The server_group_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this DescribeServerGroupAttributesResponse.


        :param server_group_id: The server_group_id of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def server_group_name(self):
        """Gets the server_group_name of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The server_group_name of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this DescribeServerGroupAttributesResponse.


        :param server_group_name: The server_group_name of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def servers(self):
        """Gets the servers of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The servers of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[ServerForDescribeServerGroupAttributesOutput]
        """
        return self._servers

    @servers.setter
    def servers(self, servers):
        """Sets the servers of this DescribeServerGroupAttributesResponse.


        :param servers: The servers of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: list[ServerForDescribeServerGroupAttributesOutput]
        """

        self._servers = servers

    @property
    def tags(self):
        """Gets the tags of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeServerGroupAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeServerGroupAttributesResponse.


        :param tags: The tags of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeServerGroupAttributesOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this DescribeServerGroupAttributesResponse.  # noqa: E501


        :return: The type of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DescribeServerGroupAttributesResponse.


        :param type: The type of this DescribeServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeServerGroupAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeServerGroupAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeServerGroupAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
