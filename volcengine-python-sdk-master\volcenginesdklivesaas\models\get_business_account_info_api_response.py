# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetBusinessAccountInfoAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'business_account_info': 'BusinessAccountInfoForGetBusinessAccountInfoAPIOutput',
        'is_business_account_enable': 'int'
    }

    attribute_map = {
        'business_account_info': 'BusinessAccountInfo',
        'is_business_account_enable': 'IsBusinessAccountEnable'
    }

    def __init__(self, business_account_info=None, is_business_account_enable=None, _configuration=None):  # noqa: E501
        """GetBusinessAccountInfoAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._business_account_info = None
        self._is_business_account_enable = None
        self.discriminator = None

        if business_account_info is not None:
            self.business_account_info = business_account_info
        if is_business_account_enable is not None:
            self.is_business_account_enable = is_business_account_enable

    @property
    def business_account_info(self):
        """Gets the business_account_info of this GetBusinessAccountInfoAPIResponse.  # noqa: E501


        :return: The business_account_info of this GetBusinessAccountInfoAPIResponse.  # noqa: E501
        :rtype: BusinessAccountInfoForGetBusinessAccountInfoAPIOutput
        """
        return self._business_account_info

    @business_account_info.setter
    def business_account_info(self, business_account_info):
        """Sets the business_account_info of this GetBusinessAccountInfoAPIResponse.


        :param business_account_info: The business_account_info of this GetBusinessAccountInfoAPIResponse.  # noqa: E501
        :type: BusinessAccountInfoForGetBusinessAccountInfoAPIOutput
        """

        self._business_account_info = business_account_info

    @property
    def is_business_account_enable(self):
        """Gets the is_business_account_enable of this GetBusinessAccountInfoAPIResponse.  # noqa: E501


        :return: The is_business_account_enable of this GetBusinessAccountInfoAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_business_account_enable

    @is_business_account_enable.setter
    def is_business_account_enable(self, is_business_account_enable):
        """Sets the is_business_account_enable of this GetBusinessAccountInfoAPIResponse.


        :param is_business_account_enable: The is_business_account_enable of this GetBusinessAccountInfoAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_business_account_enable = is_business_account_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetBusinessAccountInfoAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetBusinessAccountInfoAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetBusinessAccountInfoAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
