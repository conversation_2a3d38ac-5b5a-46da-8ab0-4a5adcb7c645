# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDevFingerprintPortRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'cmdline': 'str',
        'comm': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sip': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'sport': 'str',
        'username': 'str'
    }

    attribute_map = {
        'asset_id': 'AssetID',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sip': 'Sip',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'sport': 'Sport',
        'username': 'Username'
    }

    def __init__(self, asset_id=None, cmdline=None, comm=None, page_number=None, page_size=None, sip=None, sort_by=None, sort_order=None, sport=None, username=None, _configuration=None):  # noqa: E501
        """GetDevFingerprintPortRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._cmdline = None
        self._comm = None
        self._page_number = None
        self._page_size = None
        self._sip = None
        self._sort_by = None
        self._sort_order = None
        self._sport = None
        self._username = None
        self.discriminator = None

        if asset_id is not None:
            self.asset_id = asset_id
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        self.page_number = page_number
        self.page_size = page_size
        if sip is not None:
            self.sip = sip
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if sport is not None:
            self.sport = sport
        if username is not None:
            self.username = username

    @property
    def asset_id(self):
        """Gets the asset_id of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The asset_id of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this GetDevFingerprintPortRequest.


        :param asset_id: The asset_id of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def cmdline(self):
        """Gets the cmdline of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The cmdline of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this GetDevFingerprintPortRequest.


        :param cmdline: The cmdline of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The comm of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this GetDevFingerprintPortRequest.


        :param comm: The comm of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def page_number(self):
        """Gets the page_number of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The page_number of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetDevFingerprintPortRequest.


        :param page_number: The page_number of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The page_size of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetDevFingerprintPortRequest.


        :param page_size: The page_size of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sip(self):
        """Gets the sip of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The sip of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._sip

    @sip.setter
    def sip(self, sip):
        """Sets the sip of this GetDevFingerprintPortRequest.


        :param sip: The sip of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._sip = sip

    @property
    def sort_by(self):
        """Gets the sort_by of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The sort_by of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this GetDevFingerprintPortRequest.


        :param sort_by: The sort_by of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The sort_order of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this GetDevFingerprintPortRequest.


        :param sort_order: The sort_order of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Asc", " Desc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def sport(self):
        """Gets the sport of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The sport of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._sport

    @sport.setter
    def sport(self, sport):
        """Sets the sport of this GetDevFingerprintPortRequest.


        :param sport: The sport of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._sport = sport

    @property
    def username(self):
        """Gets the username of this GetDevFingerprintPortRequest.  # noqa: E501


        :return: The username of this GetDevFingerprintPortRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this GetDevFingerprintPortRequest.


        :param username: The username of this GetDevFingerprintPortRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDevFingerprintPortRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDevFingerprintPortRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDevFingerprintPortRequest):
            return True

        return self.to_dict() != other.to_dict()
