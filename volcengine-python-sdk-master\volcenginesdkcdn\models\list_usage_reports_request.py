# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListUsageReportsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'export_type': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'status': 'int',
        'task_name': 'str'
    }

    attribute_map = {
        'export_type': 'ExportType',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'status': 'Status',
        'task_name': 'TaskName'
    }

    def __init__(self, export_type=None, page_num=None, page_size=None, status=None, task_name=None, _configuration=None):  # noqa: E501
        """ListUsageReportsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._export_type = None
        self._page_num = None
        self._page_size = None
        self._status = None
        self._task_name = None
        self.discriminator = None

        if export_type is not None:
            self.export_type = export_type
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if status is not None:
            self.status = status
        if task_name is not None:
            self.task_name = task_name

    @property
    def export_type(self):
        """Gets the export_type of this ListUsageReportsRequest.  # noqa: E501


        :return: The export_type of this ListUsageReportsRequest.  # noqa: E501
        :rtype: str
        """
        return self._export_type

    @export_type.setter
    def export_type(self, export_type):
        """Sets the export_type of this ListUsageReportsRequest.


        :param export_type: The export_type of this ListUsageReportsRequest.  # noqa: E501
        :type: str
        """

        self._export_type = export_type

    @property
    def page_num(self):
        """Gets the page_num of this ListUsageReportsRequest.  # noqa: E501


        :return: The page_num of this ListUsageReportsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListUsageReportsRequest.


        :param page_num: The page_num of this ListUsageReportsRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListUsageReportsRequest.  # noqa: E501


        :return: The page_size of this ListUsageReportsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListUsageReportsRequest.


        :param page_size: The page_size of this ListUsageReportsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def status(self):
        """Gets the status of this ListUsageReportsRequest.  # noqa: E501


        :return: The status of this ListUsageReportsRequest.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListUsageReportsRequest.


        :param status: The status of this ListUsageReportsRequest.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def task_name(self):
        """Gets the task_name of this ListUsageReportsRequest.  # noqa: E501


        :return: The task_name of this ListUsageReportsRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this ListUsageReportsRequest.


        :param task_name: The task_name of this ListUsageReportsRequest.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListUsageReportsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListUsageReportsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListUsageReportsRequest):
            return True

        return self.to_dict() != other.to_dict()
