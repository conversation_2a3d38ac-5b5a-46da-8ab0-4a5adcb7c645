# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCdnDomainsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'exact_match': 'bool',
        'feature_config': 'bool',
        'https': 'bool',
        'ipv6': 'bool',
        'origin_protocol': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'primary_origin': 'str',
        'project': 'str',
        'resource_tags': 'list[str]',
        'service_region': 'str',
        'service_type': 'str',
        'status': 'str',
        'tag_connective': 'str'
    }

    attribute_map = {
        'domain': 'Domain',
        'exact_match': 'ExactMatch',
        'feature_config': 'FeatureConfig',
        'https': 'HTTPS',
        'ipv6': 'IPv6',
        'origin_protocol': 'OriginProtocol',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'primary_origin': 'PrimaryOrigin',
        'project': 'Project',
        'resource_tags': 'ResourceTags',
        'service_region': 'ServiceRegion',
        'service_type': 'ServiceType',
        'status': 'Status',
        'tag_connective': 'TagConnective'
    }

    def __init__(self, domain=None, exact_match=None, feature_config=None, https=None, ipv6=None, origin_protocol=None, page_num=None, page_size=None, primary_origin=None, project=None, resource_tags=None, service_region=None, service_type=None, status=None, tag_connective=None, _configuration=None):  # noqa: E501
        """ListCdnDomainsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._exact_match = None
        self._feature_config = None
        self._https = None
        self._ipv6 = None
        self._origin_protocol = None
        self._page_num = None
        self._page_size = None
        self._primary_origin = None
        self._project = None
        self._resource_tags = None
        self._service_region = None
        self._service_type = None
        self._status = None
        self._tag_connective = None
        self.discriminator = None

        if domain is not None:
            self.domain = domain
        if exact_match is not None:
            self.exact_match = exact_match
        if feature_config is not None:
            self.feature_config = feature_config
        if https is not None:
            self.https = https
        if ipv6 is not None:
            self.ipv6 = ipv6
        if origin_protocol is not None:
            self.origin_protocol = origin_protocol
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if primary_origin is not None:
            self.primary_origin = primary_origin
        if project is not None:
            self.project = project
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if service_region is not None:
            self.service_region = service_region
        if service_type is not None:
            self.service_type = service_type
        if status is not None:
            self.status = status
        if tag_connective is not None:
            self.tag_connective = tag_connective

    @property
    def domain(self):
        """Gets the domain of this ListCdnDomainsRequest.  # noqa: E501


        :return: The domain of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this ListCdnDomainsRequest.


        :param domain: The domain of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def exact_match(self):
        """Gets the exact_match of this ListCdnDomainsRequest.  # noqa: E501


        :return: The exact_match of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._exact_match

    @exact_match.setter
    def exact_match(self, exact_match):
        """Sets the exact_match of this ListCdnDomainsRequest.


        :param exact_match: The exact_match of this ListCdnDomainsRequest.  # noqa: E501
        :type: bool
        """

        self._exact_match = exact_match

    @property
    def feature_config(self):
        """Gets the feature_config of this ListCdnDomainsRequest.  # noqa: E501


        :return: The feature_config of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._feature_config

    @feature_config.setter
    def feature_config(self, feature_config):
        """Sets the feature_config of this ListCdnDomainsRequest.


        :param feature_config: The feature_config of this ListCdnDomainsRequest.  # noqa: E501
        :type: bool
        """

        self._feature_config = feature_config

    @property
    def https(self):
        """Gets the https of this ListCdnDomainsRequest.  # noqa: E501


        :return: The https of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._https

    @https.setter
    def https(self, https):
        """Sets the https of this ListCdnDomainsRequest.


        :param https: The https of this ListCdnDomainsRequest.  # noqa: E501
        :type: bool
        """

        self._https = https

    @property
    def ipv6(self):
        """Gets the ipv6 of this ListCdnDomainsRequest.  # noqa: E501


        :return: The ipv6 of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this ListCdnDomainsRequest.


        :param ipv6: The ipv6 of this ListCdnDomainsRequest.  # noqa: E501
        :type: bool
        """

        self._ipv6 = ipv6

    @property
    def origin_protocol(self):
        """Gets the origin_protocol of this ListCdnDomainsRequest.  # noqa: E501


        :return: The origin_protocol of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_protocol

    @origin_protocol.setter
    def origin_protocol(self, origin_protocol):
        """Sets the origin_protocol of this ListCdnDomainsRequest.


        :param origin_protocol: The origin_protocol of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._origin_protocol = origin_protocol

    @property
    def page_num(self):
        """Gets the page_num of this ListCdnDomainsRequest.  # noqa: E501


        :return: The page_num of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListCdnDomainsRequest.


        :param page_num: The page_num of this ListCdnDomainsRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListCdnDomainsRequest.  # noqa: E501


        :return: The page_size of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCdnDomainsRequest.


        :param page_size: The page_size of this ListCdnDomainsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def primary_origin(self):
        """Gets the primary_origin of this ListCdnDomainsRequest.  # noqa: E501


        :return: The primary_origin of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._primary_origin

    @primary_origin.setter
    def primary_origin(self, primary_origin):
        """Sets the primary_origin of this ListCdnDomainsRequest.


        :param primary_origin: The primary_origin of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._primary_origin = primary_origin

    @property
    def project(self):
        """Gets the project of this ListCdnDomainsRequest.  # noqa: E501


        :return: The project of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ListCdnDomainsRequest.


        :param project: The project of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def resource_tags(self):
        """Gets the resource_tags of this ListCdnDomainsRequest.  # noqa: E501


        :return: The resource_tags of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this ListCdnDomainsRequest.


        :param resource_tags: The resource_tags of this ListCdnDomainsRequest.  # noqa: E501
        :type: list[str]
        """

        self._resource_tags = resource_tags

    @property
    def service_region(self):
        """Gets the service_region of this ListCdnDomainsRequest.  # noqa: E501


        :return: The service_region of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_region

    @service_region.setter
    def service_region(self, service_region):
        """Sets the service_region of this ListCdnDomainsRequest.


        :param service_region: The service_region of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._service_region = service_region

    @property
    def service_type(self):
        """Gets the service_type of this ListCdnDomainsRequest.  # noqa: E501


        :return: The service_type of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_type

    @service_type.setter
    def service_type(self, service_type):
        """Sets the service_type of this ListCdnDomainsRequest.


        :param service_type: The service_type of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._service_type = service_type

    @property
    def status(self):
        """Gets the status of this ListCdnDomainsRequest.  # noqa: E501


        :return: The status of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListCdnDomainsRequest.


        :param status: The status of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_connective(self):
        """Gets the tag_connective of this ListCdnDomainsRequest.  # noqa: E501


        :return: The tag_connective of this ListCdnDomainsRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag_connective

    @tag_connective.setter
    def tag_connective(self, tag_connective):
        """Sets the tag_connective of this ListCdnDomainsRequest.


        :param tag_connective: The tag_connective of this ListCdnDomainsRequest.  # noqa: E501
        :type: str
        """

        self._tag_connective = tag_connective

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCdnDomainsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCdnDomainsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCdnDomainsRequest):
            return True

        return self.to_dict() != other.to_dict()
