# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TableForRestoreToExistedInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'new_table_name': 'str',
        'table_name': 'str'
    }

    attribute_map = {
        'new_table_name': 'NewTableName',
        'table_name': 'TableName'
    }

    def __init__(self, new_table_name=None, table_name=None, _configuration=None):  # noqa: E501
        """TableForRestoreToExistedInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._new_table_name = None
        self._table_name = None
        self.discriminator = None

        if new_table_name is not None:
            self.new_table_name = new_table_name
        if table_name is not None:
            self.table_name = table_name

    @property
    def new_table_name(self):
        """Gets the new_table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501


        :return: The new_table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._new_table_name

    @new_table_name.setter
    def new_table_name(self, new_table_name):
        """Sets the new_table_name of this TableForRestoreToExistedInstanceInput.


        :param new_table_name: The new_table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501
        :type: str
        """

        self._new_table_name = new_table_name

    @property
    def table_name(self):
        """Gets the table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501


        :return: The table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._table_name

    @table_name.setter
    def table_name(self, table_name):
        """Sets the table_name of this TableForRestoreToExistedInstanceInput.


        :param table_name: The table_name of this TableForRestoreToExistedInstanceInput.  # noqa: E501
        :type: str
        """

        self._table_name = table_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TableForRestoreToExistedInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TableForRestoreToExistedInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TableForRestoreToExistedInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
