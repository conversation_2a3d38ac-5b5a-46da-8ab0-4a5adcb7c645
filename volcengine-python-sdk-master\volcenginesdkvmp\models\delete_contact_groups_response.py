# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteContactGroupsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'successful_items': 'list[str]',
        'unsuccessful_items': 'list[UnsuccessfulItemForDeleteContactGroupsOutput]'
    }

    attribute_map = {
        'successful_items': 'SuccessfulItems',
        'unsuccessful_items': 'UnsuccessfulItems'
    }

    def __init__(self, successful_items=None, unsuccessful_items=None, _configuration=None):  # noqa: E501
        """DeleteContactGroupsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._successful_items = None
        self._unsuccessful_items = None
        self.discriminator = None

        if successful_items is not None:
            self.successful_items = successful_items
        if unsuccessful_items is not None:
            self.unsuccessful_items = unsuccessful_items

    @property
    def successful_items(self):
        """Gets the successful_items of this DeleteContactGroupsResponse.  # noqa: E501


        :return: The successful_items of this DeleteContactGroupsResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._successful_items

    @successful_items.setter
    def successful_items(self, successful_items):
        """Sets the successful_items of this DeleteContactGroupsResponse.


        :param successful_items: The successful_items of this DeleteContactGroupsResponse.  # noqa: E501
        :type: list[str]
        """

        self._successful_items = successful_items

    @property
    def unsuccessful_items(self):
        """Gets the unsuccessful_items of this DeleteContactGroupsResponse.  # noqa: E501


        :return: The unsuccessful_items of this DeleteContactGroupsResponse.  # noqa: E501
        :rtype: list[UnsuccessfulItemForDeleteContactGroupsOutput]
        """
        return self._unsuccessful_items

    @unsuccessful_items.setter
    def unsuccessful_items(self, unsuccessful_items):
        """Sets the unsuccessful_items of this DeleteContactGroupsResponse.


        :param unsuccessful_items: The unsuccessful_items of this DeleteContactGroupsResponse.  # noqa: E501
        :type: list[UnsuccessfulItemForDeleteContactGroupsOutput]
        """

        self._unsuccessful_items = unsuccessful_items

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteContactGroupsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteContactGroupsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteContactGroupsResponse):
            return True

        return self.to_dict() != other.to_dict()
