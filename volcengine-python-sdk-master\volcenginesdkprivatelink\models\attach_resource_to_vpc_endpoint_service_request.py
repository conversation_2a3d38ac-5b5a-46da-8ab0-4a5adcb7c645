# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AttachResourceToVpcEndpointServiceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_id': 'str',
        'service_id': 'str',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'resource_id': 'ResourceId',
        'service_id': 'ServiceId',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, resource_id=None, service_id=None, zone_ids=None, _configuration=None):  # noqa: E501
        """AttachResourceToVpcEndpointServiceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_id = None
        self._service_id = None
        self._zone_ids = None
        self.discriminator = None

        self.resource_id = resource_id
        self.service_id = service_id
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def resource_id(self):
        """Gets the resource_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501


        :return: The resource_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this AttachResourceToVpcEndpointServiceRequest.


        :param resource_id: The resource_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_id is None:
            raise ValueError("Invalid value for `resource_id`, must not be `None`")  # noqa: E501

        self._resource_id = resource_id

    @property
    def service_id(self):
        """Gets the service_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501


        :return: The service_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this AttachResourceToVpcEndpointServiceRequest.


        :param service_id: The service_id of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and service_id is None:
            raise ValueError("Invalid value for `service_id`, must not be `None`")  # noqa: E501

        self._service_id = service_id

    @property
    def zone_ids(self):
        """Gets the zone_ids of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501


        :return: The zone_ids of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this AttachResourceToVpcEndpointServiceRequest.


        :param zone_ids: The zone_ids of this AttachResourceToVpcEndpointServiceRequest.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AttachResourceToVpcEndpointServiceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AttachResourceToVpcEndpointServiceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AttachResourceToVpcEndpointServiceRequest):
            return True

        return self.to_dict() != other.to_dict()
