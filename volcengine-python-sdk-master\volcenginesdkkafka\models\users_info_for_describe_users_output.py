# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UsersInfoForDescribeUsersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all_authority': 'bool',
        'create_time': 'str',
        'description': 'str',
        'password_type': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'all_authority': 'AllAuthority',
        'create_time': 'CreateTime',
        'description': 'Description',
        'password_type': 'PasswordType',
        'user_name': 'UserName'
    }

    def __init__(self, all_authority=None, create_time=None, description=None, password_type=None, user_name=None, _configuration=None):  # noqa: E501
        """UsersInfoForDescribeUsersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all_authority = None
        self._create_time = None
        self._description = None
        self._password_type = None
        self._user_name = None
        self.discriminator = None

        if all_authority is not None:
            self.all_authority = all_authority
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if password_type is not None:
            self.password_type = password_type
        if user_name is not None:
            self.user_name = user_name

    @property
    def all_authority(self):
        """Gets the all_authority of this UsersInfoForDescribeUsersOutput.  # noqa: E501


        :return: The all_authority of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this UsersInfoForDescribeUsersOutput.


        :param all_authority: The all_authority of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :type: bool
        """

        self._all_authority = all_authority

    @property
    def create_time(self):
        """Gets the create_time of this UsersInfoForDescribeUsersOutput.  # noqa: E501


        :return: The create_time of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this UsersInfoForDescribeUsersOutput.


        :param create_time: The create_time of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this UsersInfoForDescribeUsersOutput.  # noqa: E501


        :return: The description of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UsersInfoForDescribeUsersOutput.


        :param description: The description of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def password_type(self):
        """Gets the password_type of this UsersInfoForDescribeUsersOutput.  # noqa: E501


        :return: The password_type of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._password_type

    @password_type.setter
    def password_type(self, password_type):
        """Sets the password_type of this UsersInfoForDescribeUsersOutput.


        :param password_type: The password_type of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :type: str
        """

        self._password_type = password_type

    @property
    def user_name(self):
        """Gets the user_name of this UsersInfoForDescribeUsersOutput.  # noqa: E501


        :return: The user_name of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this UsersInfoForDescribeUsersOutput.


        :param user_name: The user_name of this UsersInfoForDescribeUsersOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UsersInfoForDescribeUsersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UsersInfoForDescribeUsersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UsersInfoForDescribeUsersOutput):
            return True

        return self.to_dict() != other.to_dict()
