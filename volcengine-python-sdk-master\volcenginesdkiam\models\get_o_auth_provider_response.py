# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetOAuthProviderResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authorize_template': 'str',
        'authorize_url': 'str',
        'client_id': 'str',
        'client_secret': 'str',
        'create_date': 'str',
        'description': 'str',
        'identity_map_type': 'int',
        'idp_identity_key': 'str',
        'o_auth_provider_name': 'str',
        'provider_id': 'str',
        'sso_type': 'int',
        'scope': 'str',
        'status': 'int',
        'token_url': 'str',
        'trn': 'str',
        'update_date': 'str',
        'user_info_url': 'str'
    }

    attribute_map = {
        'authorize_template': 'AuthorizeTemplate',
        'authorize_url': 'AuthorizeURL',
        'client_id': 'ClientId',
        'client_secret': 'ClientSecret',
        'create_date': 'CreateDate',
        'description': 'Description',
        'identity_map_type': 'IdentityMapType',
        'idp_identity_key': 'IdpIdentityKey',
        'o_auth_provider_name': 'OAuthProviderName',
        'provider_id': 'ProviderId',
        'sso_type': 'SSOType',
        'scope': 'Scope',
        'status': 'Status',
        'token_url': 'TokenURL',
        'trn': 'Trn',
        'update_date': 'UpdateDate',
        'user_info_url': 'UserInfoURL'
    }

    def __init__(self, authorize_template=None, authorize_url=None, client_id=None, client_secret=None, create_date=None, description=None, identity_map_type=None, idp_identity_key=None, o_auth_provider_name=None, provider_id=None, sso_type=None, scope=None, status=None, token_url=None, trn=None, update_date=None, user_info_url=None, _configuration=None):  # noqa: E501
        """GetOAuthProviderResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authorize_template = None
        self._authorize_url = None
        self._client_id = None
        self._client_secret = None
        self._create_date = None
        self._description = None
        self._identity_map_type = None
        self._idp_identity_key = None
        self._o_auth_provider_name = None
        self._provider_id = None
        self._sso_type = None
        self._scope = None
        self._status = None
        self._token_url = None
        self._trn = None
        self._update_date = None
        self._user_info_url = None
        self.discriminator = None

        if authorize_template is not None:
            self.authorize_template = authorize_template
        if authorize_url is not None:
            self.authorize_url = authorize_url
        if client_id is not None:
            self.client_id = client_id
        if client_secret is not None:
            self.client_secret = client_secret
        if create_date is not None:
            self.create_date = create_date
        if description is not None:
            self.description = description
        if identity_map_type is not None:
            self.identity_map_type = identity_map_type
        if idp_identity_key is not None:
            self.idp_identity_key = idp_identity_key
        if o_auth_provider_name is not None:
            self.o_auth_provider_name = o_auth_provider_name
        if provider_id is not None:
            self.provider_id = provider_id
        if sso_type is not None:
            self.sso_type = sso_type
        if scope is not None:
            self.scope = scope
        if status is not None:
            self.status = status
        if token_url is not None:
            self.token_url = token_url
        if trn is not None:
            self.trn = trn
        if update_date is not None:
            self.update_date = update_date
        if user_info_url is not None:
            self.user_info_url = user_info_url

    @property
    def authorize_template(self):
        """Gets the authorize_template of this GetOAuthProviderResponse.  # noqa: E501


        :return: The authorize_template of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._authorize_template

    @authorize_template.setter
    def authorize_template(self, authorize_template):
        """Sets the authorize_template of this GetOAuthProviderResponse.


        :param authorize_template: The authorize_template of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._authorize_template = authorize_template

    @property
    def authorize_url(self):
        """Gets the authorize_url of this GetOAuthProviderResponse.  # noqa: E501


        :return: The authorize_url of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._authorize_url

    @authorize_url.setter
    def authorize_url(self, authorize_url):
        """Sets the authorize_url of this GetOAuthProviderResponse.


        :param authorize_url: The authorize_url of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._authorize_url = authorize_url

    @property
    def client_id(self):
        """Gets the client_id of this GetOAuthProviderResponse.  # noqa: E501


        :return: The client_id of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._client_id

    @client_id.setter
    def client_id(self, client_id):
        """Sets the client_id of this GetOAuthProviderResponse.


        :param client_id: The client_id of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._client_id = client_id

    @property
    def client_secret(self):
        """Gets the client_secret of this GetOAuthProviderResponse.  # noqa: E501


        :return: The client_secret of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._client_secret

    @client_secret.setter
    def client_secret(self, client_secret):
        """Sets the client_secret of this GetOAuthProviderResponse.


        :param client_secret: The client_secret of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._client_secret = client_secret

    @property
    def create_date(self):
        """Gets the create_date of this GetOAuthProviderResponse.  # noqa: E501


        :return: The create_date of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_date

    @create_date.setter
    def create_date(self, create_date):
        """Sets the create_date of this GetOAuthProviderResponse.


        :param create_date: The create_date of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._create_date = create_date

    @property
    def description(self):
        """Gets the description of this GetOAuthProviderResponse.  # noqa: E501


        :return: The description of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetOAuthProviderResponse.


        :param description: The description of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def identity_map_type(self):
        """Gets the identity_map_type of this GetOAuthProviderResponse.  # noqa: E501


        :return: The identity_map_type of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: int
        """
        return self._identity_map_type

    @identity_map_type.setter
    def identity_map_type(self, identity_map_type):
        """Sets the identity_map_type of this GetOAuthProviderResponse.


        :param identity_map_type: The identity_map_type of this GetOAuthProviderResponse.  # noqa: E501
        :type: int
        """

        self._identity_map_type = identity_map_type

    @property
    def idp_identity_key(self):
        """Gets the idp_identity_key of this GetOAuthProviderResponse.  # noqa: E501


        :return: The idp_identity_key of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._idp_identity_key

    @idp_identity_key.setter
    def idp_identity_key(self, idp_identity_key):
        """Sets the idp_identity_key of this GetOAuthProviderResponse.


        :param idp_identity_key: The idp_identity_key of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._idp_identity_key = idp_identity_key

    @property
    def o_auth_provider_name(self):
        """Gets the o_auth_provider_name of this GetOAuthProviderResponse.  # noqa: E501


        :return: The o_auth_provider_name of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._o_auth_provider_name

    @o_auth_provider_name.setter
    def o_auth_provider_name(self, o_auth_provider_name):
        """Sets the o_auth_provider_name of this GetOAuthProviderResponse.


        :param o_auth_provider_name: The o_auth_provider_name of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._o_auth_provider_name = o_auth_provider_name

    @property
    def provider_id(self):
        """Gets the provider_id of this GetOAuthProviderResponse.  # noqa: E501


        :return: The provider_id of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._provider_id

    @provider_id.setter
    def provider_id(self, provider_id):
        """Sets the provider_id of this GetOAuthProviderResponse.


        :param provider_id: The provider_id of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._provider_id = provider_id

    @property
    def sso_type(self):
        """Gets the sso_type of this GetOAuthProviderResponse.  # noqa: E501


        :return: The sso_type of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: int
        """
        return self._sso_type

    @sso_type.setter
    def sso_type(self, sso_type):
        """Sets the sso_type of this GetOAuthProviderResponse.


        :param sso_type: The sso_type of this GetOAuthProviderResponse.  # noqa: E501
        :type: int
        """

        self._sso_type = sso_type

    @property
    def scope(self):
        """Gets the scope of this GetOAuthProviderResponse.  # noqa: E501


        :return: The scope of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._scope

    @scope.setter
    def scope(self, scope):
        """Sets the scope of this GetOAuthProviderResponse.


        :param scope: The scope of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._scope = scope

    @property
    def status(self):
        """Gets the status of this GetOAuthProviderResponse.  # noqa: E501


        :return: The status of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetOAuthProviderResponse.


        :param status: The status of this GetOAuthProviderResponse.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def token_url(self):
        """Gets the token_url of this GetOAuthProviderResponse.  # noqa: E501


        :return: The token_url of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._token_url

    @token_url.setter
    def token_url(self, token_url):
        """Sets the token_url of this GetOAuthProviderResponse.


        :param token_url: The token_url of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._token_url = token_url

    @property
    def trn(self):
        """Gets the trn of this GetOAuthProviderResponse.  # noqa: E501


        :return: The trn of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._trn

    @trn.setter
    def trn(self, trn):
        """Sets the trn of this GetOAuthProviderResponse.


        :param trn: The trn of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._trn = trn

    @property
    def update_date(self):
        """Gets the update_date of this GetOAuthProviderResponse.  # noqa: E501


        :return: The update_date of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this GetOAuthProviderResponse.


        :param update_date: The update_date of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    @property
    def user_info_url(self):
        """Gets the user_info_url of this GetOAuthProviderResponse.  # noqa: E501


        :return: The user_info_url of this GetOAuthProviderResponse.  # noqa: E501
        :rtype: str
        """
        return self._user_info_url

    @user_info_url.setter
    def user_info_url(self, user_info_url):
        """Sets the user_info_url of this GetOAuthProviderResponse.


        :param user_info_url: The user_info_url of this GetOAuthProviderResponse.  # noqa: E501
        :type: str
        """

        self._user_info_url = user_info_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetOAuthProviderResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetOAuthProviderResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetOAuthProviderResponse):
            return True

        return self.to_dict() != other.to_dict()
