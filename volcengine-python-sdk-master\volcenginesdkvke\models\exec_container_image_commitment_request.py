# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExecContainerImageCommitmentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_config': 'AuthConfigForExecContainerImageCommitmentInput',
        'container_name': 'str',
        'image_spec': 'ImageSpecForExecContainerImageCommitmentInput',
        'instance_id': 'str',
        'pause': 'bool'
    }

    attribute_map = {
        'auth_config': 'AuthConfig',
        'container_name': 'ContainerName',
        'image_spec': 'ImageSpec',
        'instance_id': 'InstanceId',
        'pause': 'Pause'
    }

    def __init__(self, auth_config=None, container_name=None, image_spec=None, instance_id=None, pause=None, _configuration=None):  # noqa: E501
        """ExecContainerImageCommitmentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_config = None
        self._container_name = None
        self._image_spec = None
        self._instance_id = None
        self._pause = None
        self.discriminator = None

        if auth_config is not None:
            self.auth_config = auth_config
        self.container_name = container_name
        if image_spec is not None:
            self.image_spec = image_spec
        self.instance_id = instance_id
        if pause is not None:
            self.pause = pause

    @property
    def auth_config(self):
        """Gets the auth_config of this ExecContainerImageCommitmentRequest.  # noqa: E501


        :return: The auth_config of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :rtype: AuthConfigForExecContainerImageCommitmentInput
        """
        return self._auth_config

    @auth_config.setter
    def auth_config(self, auth_config):
        """Sets the auth_config of this ExecContainerImageCommitmentRequest.


        :param auth_config: The auth_config of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :type: AuthConfigForExecContainerImageCommitmentInput
        """

        self._auth_config = auth_config

    @property
    def container_name(self):
        """Gets the container_name of this ExecContainerImageCommitmentRequest.  # noqa: E501


        :return: The container_name of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this ExecContainerImageCommitmentRequest.


        :param container_name: The container_name of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and container_name is None:
            raise ValueError("Invalid value for `container_name`, must not be `None`")  # noqa: E501

        self._container_name = container_name

    @property
    def image_spec(self):
        """Gets the image_spec of this ExecContainerImageCommitmentRequest.  # noqa: E501


        :return: The image_spec of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :rtype: ImageSpecForExecContainerImageCommitmentInput
        """
        return self._image_spec

    @image_spec.setter
    def image_spec(self, image_spec):
        """Sets the image_spec of this ExecContainerImageCommitmentRequest.


        :param image_spec: The image_spec of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :type: ImageSpecForExecContainerImageCommitmentInput
        """

        self._image_spec = image_spec

    @property
    def instance_id(self):
        """Gets the instance_id of this ExecContainerImageCommitmentRequest.  # noqa: E501


        :return: The instance_id of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ExecContainerImageCommitmentRequest.


        :param instance_id: The instance_id of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def pause(self):
        """Gets the pause of this ExecContainerImageCommitmentRequest.  # noqa: E501


        :return: The pause of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :rtype: bool
        """
        return self._pause

    @pause.setter
    def pause(self, pause):
        """Sets the pause of this ExecContainerImageCommitmentRequest.


        :param pause: The pause of this ExecContainerImageCommitmentRequest.  # noqa: E501
        :type: bool
        """

        self._pause = pause

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExecContainerImageCommitmentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExecContainerImageCommitmentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExecContainerImageCommitmentRequest):
            return True

        return self.to_dict() != other.to_dict()
