# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListCloudPlatformsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'key': 'str',
        'name': 'str',
        'online': 'bool'
    }

    attribute_map = {
        'key': 'key',
        'name': 'name',
        'online': 'online'
    }

    def __init__(self, key=None, name=None, online=None, _configuration=None):  # noqa: E501
        """DataForListCloudPlatformsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._key = None
        self._name = None
        self._online = None
        self.discriminator = None

        if key is not None:
            self.key = key
        if name is not None:
            self.name = name
        if online is not None:
            self.online = online

    @property
    def key(self):
        """Gets the key of this DataForListCloudPlatformsOutput.  # noqa: E501


        :return: The key of this DataForListCloudPlatformsOutput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this DataForListCloudPlatformsOutput.


        :param key: The key of this DataForListCloudPlatformsOutput.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def name(self):
        """Gets the name of this DataForListCloudPlatformsOutput.  # noqa: E501


        :return: The name of this DataForListCloudPlatformsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListCloudPlatformsOutput.


        :param name: The name of this DataForListCloudPlatformsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def online(self):
        """Gets the online of this DataForListCloudPlatformsOutput.  # noqa: E501


        :return: The online of this DataForListCloudPlatformsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._online

    @online.setter
    def online(self, online):
        """Sets the online of this DataForListCloudPlatformsOutput.


        :param online: The online of this DataForListCloudPlatformsOutput.  # noqa: E501
        :type: bool
        """

        self._online = online

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListCloudPlatformsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListCloudPlatformsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListCloudPlatformsOutput):
            return True

        return self.to_dict() != other.to_dict()
