# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaseInfoForGetAssetSyncTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time_milli': 'int',
        'err_code': 'str',
        'err_msg': 'str',
        'start_time_milli': 'int',
        'task_id': 'str',
        'task_status': 'str',
        'trigger_type': 'str'
    }

    attribute_map = {
        'end_time_milli': 'EndTimeMilli',
        'err_code': 'ErrCode',
        'err_msg': 'ErrMsg',
        'start_time_milli': 'StartTimeMilli',
        'task_id': 'TaskID',
        'task_status': 'TaskStatus',
        'trigger_type': 'TriggerType'
    }

    def __init__(self, end_time_milli=None, err_code=None, err_msg=None, start_time_milli=None, task_id=None, task_status=None, trigger_type=None, _configuration=None):  # noqa: E501
        """BaseInfoForGetAssetSyncTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time_milli = None
        self._err_code = None
        self._err_msg = None
        self._start_time_milli = None
        self._task_id = None
        self._task_status = None
        self._trigger_type = None
        self.discriminator = None

        if end_time_milli is not None:
            self.end_time_milli = end_time_milli
        if err_code is not None:
            self.err_code = err_code
        if err_msg is not None:
            self.err_msg = err_msg
        if start_time_milli is not None:
            self.start_time_milli = start_time_milli
        if task_id is not None:
            self.task_id = task_id
        if task_status is not None:
            self.task_status = task_status
        if trigger_type is not None:
            self.trigger_type = trigger_type

    @property
    def end_time_milli(self):
        """Gets the end_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The end_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time_milli

    @end_time_milli.setter
    def end_time_milli(self, end_time_milli):
        """Sets the end_time_milli of this BaseInfoForGetAssetSyncTaskOutput.


        :param end_time_milli: The end_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: int
        """

        self._end_time_milli = end_time_milli

    @property
    def err_code(self):
        """Gets the err_code of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The err_code of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_code

    @err_code.setter
    def err_code(self, err_code):
        """Sets the err_code of this BaseInfoForGetAssetSyncTaskOutput.


        :param err_code: The err_code of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: str
        """

        self._err_code = err_code

    @property
    def err_msg(self):
        """Gets the err_msg of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The err_msg of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_msg

    @err_msg.setter
    def err_msg(self, err_msg):
        """Sets the err_msg of this BaseInfoForGetAssetSyncTaskOutput.


        :param err_msg: The err_msg of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: str
        """

        self._err_msg = err_msg

    @property
    def start_time_milli(self):
        """Gets the start_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The start_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time_milli

    @start_time_milli.setter
    def start_time_milli(self, start_time_milli):
        """Sets the start_time_milli of this BaseInfoForGetAssetSyncTaskOutput.


        :param start_time_milli: The start_time_milli of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: int
        """

        self._start_time_milli = start_time_milli

    @property
    def task_id(self):
        """Gets the task_id of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The task_id of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this BaseInfoForGetAssetSyncTaskOutput.


        :param task_id: The task_id of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_status(self):
        """Gets the task_status of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The task_status of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this BaseInfoForGetAssetSyncTaskOutput.


        :param task_status: The task_status of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: str
        """

        self._task_status = task_status

    @property
    def trigger_type(self):
        """Gets the trigger_type of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501


        :return: The trigger_type of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._trigger_type

    @trigger_type.setter
    def trigger_type(self, trigger_type):
        """Sets the trigger_type of this BaseInfoForGetAssetSyncTaskOutput.


        :param trigger_type: The trigger_type of this BaseInfoForGetAssetSyncTaskOutput.  # noqa: E501
        :type: str
        """

        self._trigger_type = trigger_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaseInfoForGetAssetSyncTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaseInfoForGetAssetSyncTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaseInfoForGetAssetSyncTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
