# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetPermissionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'name': 'str',
        'permission': 'str',
        'permission_type': 'str',
        'resource_type': 'str',
        'trn': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'name': 'Name',
        'permission': 'Permission',
        'permission_type': 'PermissionType',
        'resource_type': 'ResourceType',
        'trn': 'Trn'
    }

    def __init__(self, description=None, name=None, permission=None, permission_type=None, resource_type=None, trn=None, _configuration=None):  # noqa: E501
        """GetPermissionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._name = None
        self._permission = None
        self._permission_type = None
        self._resource_type = None
        self._trn = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if name is not None:
            self.name = name
        if permission is not None:
            self.permission = permission
        if permission_type is not None:
            self.permission_type = permission_type
        if resource_type is not None:
            self.resource_type = resource_type
        if trn is not None:
            self.trn = trn

    @property
    def description(self):
        """Gets the description of this GetPermissionResponse.  # noqa: E501


        :return: The description of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetPermissionResponse.


        :param description: The description of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this GetPermissionResponse.  # noqa: E501


        :return: The name of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetPermissionResponse.


        :param name: The name of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def permission(self):
        """Gets the permission of this GetPermissionResponse.  # noqa: E501


        :return: The permission of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._permission

    @permission.setter
    def permission(self, permission):
        """Sets the permission of this GetPermissionResponse.


        :param permission: The permission of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._permission = permission

    @property
    def permission_type(self):
        """Gets the permission_type of this GetPermissionResponse.  # noqa: E501


        :return: The permission_type of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._permission_type

    @permission_type.setter
    def permission_type(self, permission_type):
        """Sets the permission_type of this GetPermissionResponse.


        :param permission_type: The permission_type of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._permission_type = permission_type

    @property
    def resource_type(self):
        """Gets the resource_type of this GetPermissionResponse.  # noqa: E501


        :return: The resource_type of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this GetPermissionResponse.


        :param resource_type: The resource_type of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def trn(self):
        """Gets the trn of this GetPermissionResponse.  # noqa: E501


        :return: The trn of this GetPermissionResponse.  # noqa: E501
        :rtype: str
        """
        return self._trn

    @trn.setter
    def trn(self, trn):
        """Sets the trn of this GetPermissionResponse.


        :param trn: The trn of this GetPermissionResponse.  # noqa: E501
        :type: str
        """

        self._trn = trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetPermissionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetPermissionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetPermissionResponse):
            return True

        return self.to_dict() != other.to_dict()
