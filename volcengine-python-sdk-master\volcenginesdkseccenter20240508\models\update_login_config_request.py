# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateLoginConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'description': 'str',
        'id': 'str',
        'ip_list': 'list[str]',
        'if_all_host': 'bool',
        'leaf_group_ids': 'list[str]',
        'legal_user': 'list[str]',
        'login_config_rules': 'list[str]',
        'time_intervals': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'description': 'Description',
        'id': 'ID',
        'ip_list': 'IPList',
        'if_all_host': 'IfAllHost',
        'leaf_group_ids': 'LeafGroupIDs',
        'legal_user': 'LegalUser',
        'login_config_rules': 'LoginConfigRules',
        'time_intervals': 'TimeIntervals',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id_list=None, description=None, id=None, ip_list=None, if_all_host=None, leaf_group_ids=None, legal_user=None, login_config_rules=None, time_intervals=None, top_group_id=None, _configuration=None):  # noqa: E501
        """UpdateLoginConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._description = None
        self._id = None
        self._ip_list = None
        self._if_all_host = None
        self._leaf_group_ids = None
        self._legal_user = None
        self._login_config_rules = None
        self._time_intervals = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if description is not None:
            self.description = description
        self.id = id
        if ip_list is not None:
            self.ip_list = ip_list
        if if_all_host is not None:
            self.if_all_host = if_all_host
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if legal_user is not None:
            self.legal_user = legal_user
        if login_config_rules is not None:
            self.login_config_rules = login_config_rules
        if time_intervals is not None:
            self.time_intervals = time_intervals
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The agent_id_list of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this UpdateLoginConfigRequest.


        :param agent_id_list: The agent_id_list of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def description(self):
        """Gets the description of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The description of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateLoginConfigRequest.


        :param description: The description of this UpdateLoginConfigRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The id of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateLoginConfigRequest.


        :param id: The id of this UpdateLoginConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def ip_list(self):
        """Gets the ip_list of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The ip_list of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this UpdateLoginConfigRequest.


        :param ip_list: The ip_list of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._ip_list = ip_list

    @property
    def if_all_host(self):
        """Gets the if_all_host of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The if_all_host of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_all_host

    @if_all_host.setter
    def if_all_host(self, if_all_host):
        """Sets the if_all_host of this UpdateLoginConfigRequest.


        :param if_all_host: The if_all_host of this UpdateLoginConfigRequest.  # noqa: E501
        :type: bool
        """

        self._if_all_host = if_all_host

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The leaf_group_ids of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this UpdateLoginConfigRequest.


        :param leaf_group_ids: The leaf_group_ids of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def legal_user(self):
        """Gets the legal_user of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The legal_user of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._legal_user

    @legal_user.setter
    def legal_user(self, legal_user):
        """Sets the legal_user of this UpdateLoginConfigRequest.


        :param legal_user: The legal_user of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._legal_user = legal_user

    @property
    def login_config_rules(self):
        """Gets the login_config_rules of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The login_config_rules of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._login_config_rules

    @login_config_rules.setter
    def login_config_rules(self, login_config_rules):
        """Sets the login_config_rules of this UpdateLoginConfigRequest.


        :param login_config_rules: The login_config_rules of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._login_config_rules = login_config_rules

    @property
    def time_intervals(self):
        """Gets the time_intervals of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The time_intervals of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._time_intervals

    @time_intervals.setter
    def time_intervals(self, time_intervals):
        """Sets the time_intervals of this UpdateLoginConfigRequest.


        :param time_intervals: The time_intervals of this UpdateLoginConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._time_intervals = time_intervals

    @property
    def top_group_id(self):
        """Gets the top_group_id of this UpdateLoginConfigRequest.  # noqa: E501


        :return: The top_group_id of this UpdateLoginConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this UpdateLoginConfigRequest.


        :param top_group_id: The top_group_id of this UpdateLoginConfigRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateLoginConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateLoginConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateLoginConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
