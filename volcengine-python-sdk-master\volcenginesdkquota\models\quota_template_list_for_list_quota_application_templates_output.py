# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotaTemplateListForListQuotaApplicationTemplatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_time': 'str',
        'description': 'str',
        'desire_value': 'str',
        'dimensions': 'str',
        'dimensions_with_cn': 'list[DimensionsWithCnForListQuotaApplicationTemplatesOutput]',
        'manager_account_id': 'int',
        'product_name': 'str',
        'provider_code': 'str',
        'quota_code': 'str',
        'quota_type': 'str',
        'quota_unit': 'str',
        'updated_time': 'str'
    }

    attribute_map = {
        'created_time': 'CreatedTime',
        'description': 'Description',
        'desire_value': 'DesireValue',
        'dimensions': 'Dimensions',
        'dimensions_with_cn': 'DimensionsWithCn',
        'manager_account_id': 'ManagerAccountID',
        'product_name': 'ProductName',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode',
        'quota_type': 'QuotaType',
        'quota_unit': 'QuotaUnit',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, created_time=None, description=None, desire_value=None, dimensions=None, dimensions_with_cn=None, manager_account_id=None, product_name=None, provider_code=None, quota_code=None, quota_type=None, quota_unit=None, updated_time=None, _configuration=None):  # noqa: E501
        """QuotaTemplateListForListQuotaApplicationTemplatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_time = None
        self._description = None
        self._desire_value = None
        self._dimensions = None
        self._dimensions_with_cn = None
        self._manager_account_id = None
        self._product_name = None
        self._provider_code = None
        self._quota_code = None
        self._quota_type = None
        self._quota_unit = None
        self._updated_time = None
        self.discriminator = None

        if created_time is not None:
            self.created_time = created_time
        if description is not None:
            self.description = description
        if desire_value is not None:
            self.desire_value = desire_value
        if dimensions is not None:
            self.dimensions = dimensions
        if dimensions_with_cn is not None:
            self.dimensions_with_cn = dimensions_with_cn
        if manager_account_id is not None:
            self.manager_account_id = manager_account_id
        if product_name is not None:
            self.product_name = product_name
        if provider_code is not None:
            self.provider_code = provider_code
        if quota_code is not None:
            self.quota_code = quota_code
        if quota_type is not None:
            self.quota_type = quota_type
        if quota_unit is not None:
            self.quota_unit = quota_unit
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def created_time(self):
        """Gets the created_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The created_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param created_time: The created_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def description(self):
        """Gets the description of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The description of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param description: The description of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def desire_value(self):
        """Gets the desire_value of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The desire_value of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._desire_value

    @desire_value.setter
    def desire_value(self, desire_value):
        """Sets the desire_value of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param desire_value: The desire_value of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._desire_value = desire_value

    @property
    def dimensions(self):
        """Gets the dimensions of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The dimensions of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param dimensions: The dimensions of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._dimensions = dimensions

    @property
    def dimensions_with_cn(self):
        """Gets the dimensions_with_cn of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The dimensions_with_cn of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: list[DimensionsWithCnForListQuotaApplicationTemplatesOutput]
        """
        return self._dimensions_with_cn

    @dimensions_with_cn.setter
    def dimensions_with_cn(self, dimensions_with_cn):
        """Sets the dimensions_with_cn of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param dimensions_with_cn: The dimensions_with_cn of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: list[DimensionsWithCnForListQuotaApplicationTemplatesOutput]
        """

        self._dimensions_with_cn = dimensions_with_cn

    @property
    def manager_account_id(self):
        """Gets the manager_account_id of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The manager_account_id of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._manager_account_id

    @manager_account_id.setter
    def manager_account_id(self, manager_account_id):
        """Sets the manager_account_id of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param manager_account_id: The manager_account_id of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._manager_account_id = manager_account_id

    @property
    def product_name(self):
        """Gets the product_name of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The product_name of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param product_name: The product_name of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def provider_code(self):
        """Gets the provider_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The provider_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param provider_code: The provider_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The quota_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param quota_code: The quota_code of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._quota_code = quota_code

    @property
    def quota_type(self):
        """Gets the quota_type of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The quota_type of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param quota_type: The quota_type of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    @property
    def quota_unit(self):
        """Gets the quota_unit of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The quota_unit of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_unit

    @quota_unit.setter
    def quota_unit(self, quota_unit):
        """Sets the quota_unit of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param quota_unit: The quota_unit of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._quota_unit = quota_unit

    @property
    def updated_time(self):
        """Gets the updated_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501


        :return: The updated_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.


        :param updated_time: The updated_time of this QuotaTemplateListForListQuotaApplicationTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotaTemplateListForListQuotaApplicationTemplatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotaTemplateListForListQuotaApplicationTemplatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotaTemplateListForListQuotaApplicationTemplatesOutput):
            return True

        return self.to_dict() != other.to_dict()
