# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubtitleForDownloadLocalMediaInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'font': 'FontForDownloadLocalMediaInput',
        'subtitle_list': 'list[SubtitleListForDownloadLocalMediaInput]'
    }

    attribute_map = {
        'font': 'Font',
        'subtitle_list': 'SubtitleList'
    }

    def __init__(self, font=None, subtitle_list=None, _configuration=None):  # noqa: E501
        """SubtitleForDownloadLocalMediaInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._font = None
        self._subtitle_list = None
        self.discriminator = None

        if font is not None:
            self.font = font
        if subtitle_list is not None:
            self.subtitle_list = subtitle_list

    @property
    def font(self):
        """Gets the font of this SubtitleForDownloadLocalMediaInput.  # noqa: E501


        :return: The font of this SubtitleForDownloadLocalMediaInput.  # noqa: E501
        :rtype: FontForDownloadLocalMediaInput
        """
        return self._font

    @font.setter
    def font(self, font):
        """Sets the font of this SubtitleForDownloadLocalMediaInput.


        :param font: The font of this SubtitleForDownloadLocalMediaInput.  # noqa: E501
        :type: FontForDownloadLocalMediaInput
        """

        self._font = font

    @property
    def subtitle_list(self):
        """Gets the subtitle_list of this SubtitleForDownloadLocalMediaInput.  # noqa: E501


        :return: The subtitle_list of this SubtitleForDownloadLocalMediaInput.  # noqa: E501
        :rtype: list[SubtitleListForDownloadLocalMediaInput]
        """
        return self._subtitle_list

    @subtitle_list.setter
    def subtitle_list(self, subtitle_list):
        """Sets the subtitle_list of this SubtitleForDownloadLocalMediaInput.


        :param subtitle_list: The subtitle_list of this SubtitleForDownloadLocalMediaInput.  # noqa: E501
        :type: list[SubtitleListForDownloadLocalMediaInput]
        """

        self._subtitle_list = subtitle_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubtitleForDownloadLocalMediaInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubtitleForDownloadLocalMediaInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubtitleForDownloadLocalMediaInput):
            return True

        return self.to_dict() != other.to_dict()
