# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CustomHostConfigForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_hosts': 'list[CustomHostForCreateTaskInput]',
        'strategy': 'int'
    }

    attribute_map = {
        'custom_hosts': 'CustomHosts',
        'strategy': 'Strategy'
    }

    def __init__(self, custom_hosts=None, strategy=None, _configuration=None):  # noqa: E501
        """CustomHostConfigForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_hosts = None
        self._strategy = None
        self.discriminator = None

        if custom_hosts is not None:
            self.custom_hosts = custom_hosts
        if strategy is not None:
            self.strategy = strategy

    @property
    def custom_hosts(self):
        """Gets the custom_hosts of this CustomHostConfigForCreateTaskInput.  # noqa: E501


        :return: The custom_hosts of this CustomHostConfigForCreateTaskInput.  # noqa: E501
        :rtype: list[CustomHostForCreateTaskInput]
        """
        return self._custom_hosts

    @custom_hosts.setter
    def custom_hosts(self, custom_hosts):
        """Sets the custom_hosts of this CustomHostConfigForCreateTaskInput.


        :param custom_hosts: The custom_hosts of this CustomHostConfigForCreateTaskInput.  # noqa: E501
        :type: list[CustomHostForCreateTaskInput]
        """

        self._custom_hosts = custom_hosts

    @property
    def strategy(self):
        """Gets the strategy of this CustomHostConfigForCreateTaskInput.  # noqa: E501


        :return: The strategy of this CustomHostConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._strategy

    @strategy.setter
    def strategy(self, strategy):
        """Sets the strategy of this CustomHostConfigForCreateTaskInput.


        :param strategy: The strategy of this CustomHostConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._strategy = strategy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomHostConfigForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomHostConfigForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CustomHostConfigForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
