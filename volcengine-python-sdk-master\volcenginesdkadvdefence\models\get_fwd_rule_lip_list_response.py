# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetFwdRuleLipListResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'lip_list': 'list[str]',
        'update_time': 'int'
    }

    attribute_map = {
        'lip_list': 'LipList',
        'update_time': 'UpdateTime'
    }

    def __init__(self, lip_list=None, update_time=None, _configuration=None):  # noqa: E501
        """GetFwdRuleLipListResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._lip_list = None
        self._update_time = None
        self.discriminator = None

        if lip_list is not None:
            self.lip_list = lip_list
        if update_time is not None:
            self.update_time = update_time

    @property
    def lip_list(self):
        """Gets the lip_list of this GetFwdRuleLipListResponse.  # noqa: E501


        :return: The lip_list of this GetFwdRuleLipListResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._lip_list

    @lip_list.setter
    def lip_list(self, lip_list):
        """Sets the lip_list of this GetFwdRuleLipListResponse.


        :param lip_list: The lip_list of this GetFwdRuleLipListResponse.  # noqa: E501
        :type: list[str]
        """

        self._lip_list = lip_list

    @property
    def update_time(self):
        """Gets the update_time of this GetFwdRuleLipListResponse.  # noqa: E501


        :return: The update_time of this GetFwdRuleLipListResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetFwdRuleLipListResponse.


        :param update_time: The update_time of this GetFwdRuleLipListResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetFwdRuleLipListResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetFwdRuleLipListResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetFwdRuleLipListResponse):
            return True

        return self.to_dict() != other.to_dict()
