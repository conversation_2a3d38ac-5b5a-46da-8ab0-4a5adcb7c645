# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HealthyConfigForUpdateEndpointGroupInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'health_check_enable': 'bool',
        'health_check_interval': 'int',
        'health_check_port': 'int',
        'health_check_protocol': 'str',
        'health_response_time_out': 'int',
        'healthy_threshold': 'int'
    }

    attribute_map = {
        'health_check_enable': 'HealthCheckEnable',
        'health_check_interval': 'HealthCheckInterval',
        'health_check_port': 'HealthCheckPort',
        'health_check_protocol': 'HealthCheckProtocol',
        'health_response_time_out': 'HealthResponseTimeOut',
        'healthy_threshold': 'HealthyThreshold'
    }

    def __init__(self, health_check_enable=None, health_check_interval=None, health_check_port=None, health_check_protocol=None, health_response_time_out=None, healthy_threshold=None, _configuration=None):  # noqa: E501
        """HealthyConfigForUpdateEndpointGroupInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._health_check_enable = None
        self._health_check_interval = None
        self._health_check_port = None
        self._health_check_protocol = None
        self._health_response_time_out = None
        self._healthy_threshold = None
        self.discriminator = None

        if health_check_enable is not None:
            self.health_check_enable = health_check_enable
        if health_check_interval is not None:
            self.health_check_interval = health_check_interval
        if health_check_port is not None:
            self.health_check_port = health_check_port
        if health_check_protocol is not None:
            self.health_check_protocol = health_check_protocol
        if health_response_time_out is not None:
            self.health_response_time_out = health_response_time_out
        if healthy_threshold is not None:
            self.healthy_threshold = healthy_threshold

    @property
    def health_check_enable(self):
        """Gets the health_check_enable of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The health_check_enable of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: bool
        """
        return self._health_check_enable

    @health_check_enable.setter
    def health_check_enable(self, health_check_enable):
        """Sets the health_check_enable of this HealthyConfigForUpdateEndpointGroupInput.


        :param health_check_enable: The health_check_enable of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: bool
        """

        self._health_check_enable = health_check_enable

    @property
    def health_check_interval(self):
        """Gets the health_check_interval of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The health_check_interval of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._health_check_interval

    @health_check_interval.setter
    def health_check_interval(self, health_check_interval):
        """Sets the health_check_interval of this HealthyConfigForUpdateEndpointGroupInput.


        :param health_check_interval: The health_check_interval of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: int
        """

        self._health_check_interval = health_check_interval

    @property
    def health_check_port(self):
        """Gets the health_check_port of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The health_check_port of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._health_check_port

    @health_check_port.setter
    def health_check_port(self, health_check_port):
        """Sets the health_check_port of this HealthyConfigForUpdateEndpointGroupInput.


        :param health_check_port: The health_check_port of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: int
        """

        self._health_check_port = health_check_port

    @property
    def health_check_protocol(self):
        """Gets the health_check_protocol of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The health_check_protocol of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_protocol

    @health_check_protocol.setter
    def health_check_protocol(self, health_check_protocol):
        """Sets the health_check_protocol of this HealthyConfigForUpdateEndpointGroupInput.


        :param health_check_protocol: The health_check_protocol of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: str
        """

        self._health_check_protocol = health_check_protocol

    @property
    def health_response_time_out(self):
        """Gets the health_response_time_out of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The health_response_time_out of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._health_response_time_out

    @health_response_time_out.setter
    def health_response_time_out(self, health_response_time_out):
        """Sets the health_response_time_out of this HealthyConfigForUpdateEndpointGroupInput.


        :param health_response_time_out: The health_response_time_out of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: int
        """

        self._health_response_time_out = health_response_time_out

    @property
    def healthy_threshold(self):
        """Gets the healthy_threshold of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501


        :return: The healthy_threshold of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :rtype: int
        """
        return self._healthy_threshold

    @healthy_threshold.setter
    def healthy_threshold(self, healthy_threshold):
        """Sets the healthy_threshold of this HealthyConfigForUpdateEndpointGroupInput.


        :param healthy_threshold: The healthy_threshold of this HealthyConfigForUpdateEndpointGroupInput.  # noqa: E501
        :type: int
        """

        self._healthy_threshold = healthy_threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HealthyConfigForUpdateEndpointGroupInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthyConfigForUpdateEndpointGroupInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthyConfigForUpdateEndpointGroupInput):
            return True

        return self.to_dict() != other.to_dict()
