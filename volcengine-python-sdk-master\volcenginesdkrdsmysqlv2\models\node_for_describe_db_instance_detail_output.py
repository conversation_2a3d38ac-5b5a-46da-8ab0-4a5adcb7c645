# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'delay_replication_time': 'int',
        'instance_id': 'str',
        'memory': 'int',
        'node_id': 'str',
        'node_spec': 'str',
        'node_status': 'str',
        'node_type': 'str',
        'region_id': 'str',
        'sync_delay': 'int',
        'update_time': 'str',
        'vcpu': 'int',
        'zone_id': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'delay_replication_time': 'DelayReplicationTime',
        'instance_id': 'InstanceId',
        'memory': 'Memory',
        'node_id': 'NodeId',
        'node_spec': 'NodeSpec',
        'node_status': 'NodeStatus',
        'node_type': 'NodeType',
        'region_id': 'RegionId',
        'sync_delay': 'SyncDelay',
        'update_time': 'UpdateTime',
        'vcpu': 'VCPU',
        'zone_id': 'ZoneId'
    }

    def __init__(self, create_time=None, delay_replication_time=None, instance_id=None, memory=None, node_id=None, node_spec=None, node_status=None, node_type=None, region_id=None, sync_delay=None, update_time=None, vcpu=None, zone_id=None, _configuration=None):  # noqa: E501
        """NodeForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._delay_replication_time = None
        self._instance_id = None
        self._memory = None
        self._node_id = None
        self._node_spec = None
        self._node_status = None
        self._node_type = None
        self._region_id = None
        self._sync_delay = None
        self._update_time = None
        self._vcpu = None
        self._zone_id = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if delay_replication_time is not None:
            self.delay_replication_time = delay_replication_time
        if instance_id is not None:
            self.instance_id = instance_id
        if memory is not None:
            self.memory = memory
        if node_id is not None:
            self.node_id = node_id
        if node_spec is not None:
            self.node_spec = node_spec
        if node_status is not None:
            self.node_status = node_status
        if node_type is not None:
            self.node_type = node_type
        if region_id is not None:
            self.region_id = region_id
        if sync_delay is not None:
            self.sync_delay = sync_delay
        if update_time is not None:
            self.update_time = update_time
        if vcpu is not None:
            self.vcpu = vcpu
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def create_time(self):
        """Gets the create_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this NodeForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def delay_replication_time(self):
        """Gets the delay_replication_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The delay_replication_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._delay_replication_time

    @delay_replication_time.setter
    def delay_replication_time(self, delay_replication_time):
        """Sets the delay_replication_time of this NodeForDescribeDBInstanceDetailOutput.


        :param delay_replication_time: The delay_replication_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._delay_replication_time = delay_replication_time

    @property
    def instance_id(self):
        """Gets the instance_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this NodeForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def memory(self):
        """Gets the memory of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The memory of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this NodeForDescribeDBInstanceDetailOutput.


        :param memory: The memory of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def node_id(self):
        """Gets the node_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this NodeForDescribeDBInstanceDetailOutput.


        :param node_id: The node_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def node_spec(self):
        """Gets the node_spec of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_spec of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this NodeForDescribeDBInstanceDetailOutput.


        :param node_spec: The node_spec of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_spec = node_spec

    @property
    def node_status(self):
        """Gets the node_status of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_status of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_status

    @node_status.setter
    def node_status(self, node_status):
        """Sets the node_status of this NodeForDescribeDBInstanceDetailOutput.


        :param node_status: The node_status of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_status = node_status

    @property
    def node_type(self):
        """Gets the node_type of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The node_type of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_type

    @node_type.setter
    def node_type(self, node_type):
        """Sets the node_type of this NodeForDescribeDBInstanceDetailOutput.


        :param node_type: The node_type of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._node_type = node_type

    @property
    def region_id(self):
        """Gets the region_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this NodeForDescribeDBInstanceDetailOutput.


        :param region_id: The region_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def sync_delay(self):
        """Gets the sync_delay of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The sync_delay of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._sync_delay

    @sync_delay.setter
    def sync_delay(self, sync_delay):
        """Sets the sync_delay of this NodeForDescribeDBInstanceDetailOutput.


        :param sync_delay: The sync_delay of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._sync_delay = sync_delay

    @property
    def update_time(self):
        """Gets the update_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The update_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this NodeForDescribeDBInstanceDetailOutput.


        :param update_time: The update_time of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vcpu(self):
        """Gets the vcpu of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vcpu of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this NodeForDescribeDBInstanceDetailOutput.


        :param vcpu: The vcpu of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def zone_id(self):
        """Gets the zone_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this NodeForDescribeDBInstanceDetailOutput.


        :param zone_id: The zone_id of this NodeForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
