# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceTypeForDescribeInstanceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affinity_group_sizes': 'list[int]',
        'baseline_credit': 'int',
        'gpu': 'GpuForDescribeInstanceTypesOutput',
        'initial_credit': 'int',
        'instance_type_family': 'str',
        'instance_type_id': 'str',
        'is_support_affinity_group': 'bool',
        'local_volumes': 'list[LocalVolumeForDescribeInstanceTypesOutput]',
        'memory': 'MemoryForDescribeInstanceTypesOutput',
        'network': 'NetworkForDescribeInstanceTypesOutput',
        'processor': 'ProcessorForDescribeInstanceTypesOutput',
        'rdma': 'RdmaForDescribeInstanceTypesOutput',
        'volume': 'VolumeForDescribeInstanceTypesOutput'
    }

    attribute_map = {
        'affinity_group_sizes': 'AffinityGroupSizes',
        'baseline_credit': 'BaselineCredit',
        'gpu': 'Gpu',
        'initial_credit': 'InitialCredit',
        'instance_type_family': 'InstanceTypeFamily',
        'instance_type_id': 'InstanceTypeId',
        'is_support_affinity_group': 'IsSupportAffinityGroup',
        'local_volumes': 'LocalVolumes',
        'memory': 'Memory',
        'network': 'Network',
        'processor': 'Processor',
        'rdma': 'Rdma',
        'volume': 'Volume'
    }

    def __init__(self, affinity_group_sizes=None, baseline_credit=None, gpu=None, initial_credit=None, instance_type_family=None, instance_type_id=None, is_support_affinity_group=None, local_volumes=None, memory=None, network=None, processor=None, rdma=None, volume=None, _configuration=None):  # noqa: E501
        """InstanceTypeForDescribeInstanceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affinity_group_sizes = None
        self._baseline_credit = None
        self._gpu = None
        self._initial_credit = None
        self._instance_type_family = None
        self._instance_type_id = None
        self._is_support_affinity_group = None
        self._local_volumes = None
        self._memory = None
        self._network = None
        self._processor = None
        self._rdma = None
        self._volume = None
        self.discriminator = None

        if affinity_group_sizes is not None:
            self.affinity_group_sizes = affinity_group_sizes
        if baseline_credit is not None:
            self.baseline_credit = baseline_credit
        if gpu is not None:
            self.gpu = gpu
        if initial_credit is not None:
            self.initial_credit = initial_credit
        if instance_type_family is not None:
            self.instance_type_family = instance_type_family
        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if is_support_affinity_group is not None:
            self.is_support_affinity_group = is_support_affinity_group
        if local_volumes is not None:
            self.local_volumes = local_volumes
        if memory is not None:
            self.memory = memory
        if network is not None:
            self.network = network
        if processor is not None:
            self.processor = processor
        if rdma is not None:
            self.rdma = rdma
        if volume is not None:
            self.volume = volume

    @property
    def affinity_group_sizes(self):
        """Gets the affinity_group_sizes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The affinity_group_sizes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._affinity_group_sizes

    @affinity_group_sizes.setter
    def affinity_group_sizes(self, affinity_group_sizes):
        """Sets the affinity_group_sizes of this InstanceTypeForDescribeInstanceTypesOutput.


        :param affinity_group_sizes: The affinity_group_sizes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: list[int]
        """

        self._affinity_group_sizes = affinity_group_sizes

    @property
    def baseline_credit(self):
        """Gets the baseline_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The baseline_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline_credit

    @baseline_credit.setter
    def baseline_credit(self, baseline_credit):
        """Sets the baseline_credit of this InstanceTypeForDescribeInstanceTypesOutput.


        :param baseline_credit: The baseline_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._baseline_credit = baseline_credit

    @property
    def gpu(self):
        """Gets the gpu of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The gpu of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: GpuForDescribeInstanceTypesOutput
        """
        return self._gpu

    @gpu.setter
    def gpu(self, gpu):
        """Sets the gpu of this InstanceTypeForDescribeInstanceTypesOutput.


        :param gpu: The gpu of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: GpuForDescribeInstanceTypesOutput
        """

        self._gpu = gpu

    @property
    def initial_credit(self):
        """Gets the initial_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The initial_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._initial_credit

    @initial_credit.setter
    def initial_credit(self, initial_credit):
        """Sets the initial_credit of this InstanceTypeForDescribeInstanceTypesOutput.


        :param initial_credit: The initial_credit of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._initial_credit = initial_credit

    @property
    def instance_type_family(self):
        """Gets the instance_type_family of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The instance_type_family of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_family

    @instance_type_family.setter
    def instance_type_family(self, instance_type_family):
        """Sets the instance_type_family of this InstanceTypeForDescribeInstanceTypesOutput.


        :param instance_type_family: The instance_type_family of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_family = instance_type_family

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The instance_type_id of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this InstanceTypeForDescribeInstanceTypesOutput.


        :param instance_type_id: The instance_type_id of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def is_support_affinity_group(self):
        """Gets the is_support_affinity_group of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The is_support_affinity_group of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_support_affinity_group

    @is_support_affinity_group.setter
    def is_support_affinity_group(self, is_support_affinity_group):
        """Sets the is_support_affinity_group of this InstanceTypeForDescribeInstanceTypesOutput.


        :param is_support_affinity_group: The is_support_affinity_group of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: bool
        """

        self._is_support_affinity_group = is_support_affinity_group

    @property
    def local_volumes(self):
        """Gets the local_volumes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The local_volumes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: list[LocalVolumeForDescribeInstanceTypesOutput]
        """
        return self._local_volumes

    @local_volumes.setter
    def local_volumes(self, local_volumes):
        """Sets the local_volumes of this InstanceTypeForDescribeInstanceTypesOutput.


        :param local_volumes: The local_volumes of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: list[LocalVolumeForDescribeInstanceTypesOutput]
        """

        self._local_volumes = local_volumes

    @property
    def memory(self):
        """Gets the memory of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The memory of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: MemoryForDescribeInstanceTypesOutput
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this InstanceTypeForDescribeInstanceTypesOutput.


        :param memory: The memory of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: MemoryForDescribeInstanceTypesOutput
        """

        self._memory = memory

    @property
    def network(self):
        """Gets the network of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The network of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: NetworkForDescribeInstanceTypesOutput
        """
        return self._network

    @network.setter
    def network(self, network):
        """Sets the network of this InstanceTypeForDescribeInstanceTypesOutput.


        :param network: The network of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: NetworkForDescribeInstanceTypesOutput
        """

        self._network = network

    @property
    def processor(self):
        """Gets the processor of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The processor of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: ProcessorForDescribeInstanceTypesOutput
        """
        return self._processor

    @processor.setter
    def processor(self, processor):
        """Sets the processor of this InstanceTypeForDescribeInstanceTypesOutput.


        :param processor: The processor of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: ProcessorForDescribeInstanceTypesOutput
        """

        self._processor = processor

    @property
    def rdma(self):
        """Gets the rdma of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The rdma of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: RdmaForDescribeInstanceTypesOutput
        """
        return self._rdma

    @rdma.setter
    def rdma(self, rdma):
        """Sets the rdma of this InstanceTypeForDescribeInstanceTypesOutput.


        :param rdma: The rdma of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: RdmaForDescribeInstanceTypesOutput
        """

        self._rdma = rdma

    @property
    def volume(self):
        """Gets the volume of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The volume of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: VolumeForDescribeInstanceTypesOutput
        """
        return self._volume

    @volume.setter
    def volume(self, volume):
        """Sets the volume of this InstanceTypeForDescribeInstanceTypesOutput.


        :param volume: The volume of this InstanceTypeForDescribeInstanceTypesOutput.  # noqa: E501
        :type: VolumeForDescribeInstanceTypesOutput
        """

        self._volume = volume

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceTypeForDescribeInstanceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceTypeForDescribeInstanceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceTypeForDescribeInstanceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
