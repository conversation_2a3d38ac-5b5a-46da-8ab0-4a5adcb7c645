# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'consume_status': 'str',
        'consume_success': 'bool',
        'consumer_host': 'str',
        'end_process_timestamp': 'int',
        'group_name': 'str',
        'process_cost_time_ms': 'int',
        'reconsume_times': 'int',
        'start_process_timestamp': 'int'
    }

    attribute_map = {
        'consume_status': 'ConsumeStatus',
        'consume_success': 'ConsumeSuccess',
        'consumer_host': 'ConsumerHost',
        'end_process_timestamp': 'EndProcessTimestamp',
        'group_name': 'GroupName',
        'process_cost_time_ms': 'ProcessCostTimeMs',
        'reconsume_times': 'ReconsumeTimes',
        'start_process_timestamp': 'StartProcessTimestamp'
    }

    def __init__(self, consume_status=None, consume_success=None, consumer_host=None, end_process_timestamp=None, group_name=None, process_cost_time_ms=None, reconsume_times=None, start_process_timestamp=None, _configuration=None):  # noqa: E501
        """ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._consume_status = None
        self._consume_success = None
        self._consumer_host = None
        self._end_process_timestamp = None
        self._group_name = None
        self._process_cost_time_ms = None
        self._reconsume_times = None
        self._start_process_timestamp = None
        self.discriminator = None

        if consume_status is not None:
            self.consume_status = consume_status
        if consume_success is not None:
            self.consume_success = consume_success
        if consumer_host is not None:
            self.consumer_host = consumer_host
        if end_process_timestamp is not None:
            self.end_process_timestamp = end_process_timestamp
        if group_name is not None:
            self.group_name = group_name
        if process_cost_time_ms is not None:
            self.process_cost_time_ms = process_cost_time_ms
        if reconsume_times is not None:
            self.reconsume_times = reconsume_times
        if start_process_timestamp is not None:
            self.start_process_timestamp = start_process_timestamp

    @property
    def consume_status(self):
        """Gets the consume_status of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The consume_status of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: str
        """
        return self._consume_status

    @consume_status.setter
    def consume_status(self, consume_status):
        """Sets the consume_status of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param consume_status: The consume_status of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: str
        """

        self._consume_status = consume_status

    @property
    def consume_success(self):
        """Gets the consume_success of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The consume_success of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: bool
        """
        return self._consume_success

    @consume_success.setter
    def consume_success(self, consume_success):
        """Sets the consume_success of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param consume_success: The consume_success of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: bool
        """

        self._consume_success = consume_success

    @property
    def consumer_host(self):
        """Gets the consumer_host of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The consumer_host of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: str
        """
        return self._consumer_host

    @consumer_host.setter
    def consumer_host(self, consumer_host):
        """Sets the consumer_host of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param consumer_host: The consumer_host of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: str
        """

        self._consumer_host = consumer_host

    @property
    def end_process_timestamp(self):
        """Gets the end_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The end_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_process_timestamp

    @end_process_timestamp.setter
    def end_process_timestamp(self, end_process_timestamp):
        """Sets the end_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param end_process_timestamp: The end_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._end_process_timestamp = end_process_timestamp

    @property
    def group_name(self):
        """Gets the group_name of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The group_name of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param group_name: The group_name of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def process_cost_time_ms(self):
        """Gets the process_cost_time_ms of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The process_cost_time_ms of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._process_cost_time_ms

    @process_cost_time_ms.setter
    def process_cost_time_ms(self, process_cost_time_ms):
        """Sets the process_cost_time_ms of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param process_cost_time_ms: The process_cost_time_ms of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._process_cost_time_ms = process_cost_time_ms

    @property
    def reconsume_times(self):
        """Gets the reconsume_times of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The reconsume_times of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._reconsume_times

    @reconsume_times.setter
    def reconsume_times(self, reconsume_times):
        """Sets the reconsume_times of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param reconsume_times: The reconsume_times of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._reconsume_times = reconsume_times

    @property
    def start_process_timestamp(self):
        """Gets the start_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The start_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_process_timestamp

    @start_process_timestamp.setter
    def start_process_timestamp(self, start_process_timestamp):
        """Sets the start_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param start_process_timestamp: The start_process_timestamp of this ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._start_process_timestamp = start_process_timestamp

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsumerTraceInfoForQueryMessageTraceByMessageIdOutput):
            return True

        return self.to_dict() != other.to_dict()
