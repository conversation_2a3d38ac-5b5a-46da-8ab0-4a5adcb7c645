# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PortForCreateServiceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'expose_port': 'str',
        'listen_port': 'str',
        'path': 'str',
        'type': 'str'
    }

    attribute_map = {
        'expose_port': 'ExposePort',
        'listen_port': 'ListenPort',
        'path': 'Path',
        'type': 'Type'
    }

    def __init__(self, expose_port=None, listen_port=None, path=None, type=None, _configuration=None):  # noqa: E501
        """PortForCreateServiceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._expose_port = None
        self._listen_port = None
        self._path = None
        self._type = None
        self.discriminator = None

        if expose_port is not None:
            self.expose_port = expose_port
        if listen_port is not None:
            self.listen_port = listen_port
        if path is not None:
            self.path = path
        if type is not None:
            self.type = type

    @property
    def expose_port(self):
        """Gets the expose_port of this PortForCreateServiceInput.  # noqa: E501


        :return: The expose_port of this PortForCreateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._expose_port

    @expose_port.setter
    def expose_port(self, expose_port):
        """Sets the expose_port of this PortForCreateServiceInput.


        :param expose_port: The expose_port of this PortForCreateServiceInput.  # noqa: E501
        :type: str
        """

        self._expose_port = expose_port

    @property
    def listen_port(self):
        """Gets the listen_port of this PortForCreateServiceInput.  # noqa: E501


        :return: The listen_port of this PortForCreateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._listen_port

    @listen_port.setter
    def listen_port(self, listen_port):
        """Sets the listen_port of this PortForCreateServiceInput.


        :param listen_port: The listen_port of this PortForCreateServiceInput.  # noqa: E501
        :type: str
        """

        self._listen_port = listen_port

    @property
    def path(self):
        """Gets the path of this PortForCreateServiceInput.  # noqa: E501


        :return: The path of this PortForCreateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this PortForCreateServiceInput.


        :param path: The path of this PortForCreateServiceInput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def type(self):
        """Gets the type of this PortForCreateServiceInput.  # noqa: E501


        :return: The type of this PortForCreateServiceInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this PortForCreateServiceInput.


        :param type: The type of this PortForCreateServiceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["HTTP/1.1", "HTTP2", "GRPC", "Metrics", "Other"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PortForCreateServiceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PortForCreateServiceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PortForCreateServiceInput):
            return True

        return self.to_dict() != other.to_dict()
