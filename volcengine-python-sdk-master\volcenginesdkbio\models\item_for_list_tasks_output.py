# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'duration': 'int',
        'execute_duration': 'int',
        'finish_time': 'int',
        'job_name': 'str',
        'log': 'str',
        'name': 'str',
        'run_id': 'str',
        'start_time': 'int',
        'status': 'str',
        'stderr': 'str',
        'stdout': 'str',
        'resource_claimed': 'ResourceClaimedForListTasksOutput',
        'resource_used': 'ResourceUsedForListTasksOutput'
    }

    attribute_map = {
        'duration': 'Duration',
        'execute_duration': 'ExecuteDuration',
        'finish_time': 'FinishTime',
        'job_name': 'JobName',
        'log': 'Log',
        'name': 'Name',
        'run_id': 'RunID',
        'start_time': 'StartTime',
        'status': 'Status',
        'stderr': 'Stderr',
        'stdout': 'Stdout',
        'resource_claimed': 'resourceClaimed',
        'resource_used': 'resourceUsed'
    }

    def __init__(self, duration=None, execute_duration=None, finish_time=None, job_name=None, log=None, name=None, run_id=None, start_time=None, status=None, stderr=None, stdout=None, resource_claimed=None, resource_used=None, _configuration=None):  # noqa: E501
        """ItemForListTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._duration = None
        self._execute_duration = None
        self._finish_time = None
        self._job_name = None
        self._log = None
        self._name = None
        self._run_id = None
        self._start_time = None
        self._status = None
        self._stderr = None
        self._stdout = None
        self._resource_claimed = None
        self._resource_used = None
        self.discriminator = None

        if duration is not None:
            self.duration = duration
        if execute_duration is not None:
            self.execute_duration = execute_duration
        if finish_time is not None:
            self.finish_time = finish_time
        if job_name is not None:
            self.job_name = job_name
        if log is not None:
            self.log = log
        if name is not None:
            self.name = name
        if run_id is not None:
            self.run_id = run_id
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if stderr is not None:
            self.stderr = stderr
        if stdout is not None:
            self.stdout = stdout
        if resource_claimed is not None:
            self.resource_claimed = resource_claimed
        if resource_used is not None:
            self.resource_used = resource_used

    @property
    def duration(self):
        """Gets the duration of this ItemForListTasksOutput.  # noqa: E501


        :return: The duration of this ItemForListTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ItemForListTasksOutput.


        :param duration: The duration of this ItemForListTasksOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def execute_duration(self):
        """Gets the execute_duration of this ItemForListTasksOutput.  # noqa: E501


        :return: The execute_duration of this ItemForListTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._execute_duration

    @execute_duration.setter
    def execute_duration(self, execute_duration):
        """Sets the execute_duration of this ItemForListTasksOutput.


        :param execute_duration: The execute_duration of this ItemForListTasksOutput.  # noqa: E501
        :type: int
        """

        self._execute_duration = execute_duration

    @property
    def finish_time(self):
        """Gets the finish_time of this ItemForListTasksOutput.  # noqa: E501


        :return: The finish_time of this ItemForListTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this ItemForListTasksOutput.


        :param finish_time: The finish_time of this ItemForListTasksOutput.  # noqa: E501
        :type: int
        """

        self._finish_time = finish_time

    @property
    def job_name(self):
        """Gets the job_name of this ItemForListTasksOutput.  # noqa: E501


        :return: The job_name of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._job_name

    @job_name.setter
    def job_name(self, job_name):
        """Sets the job_name of this ItemForListTasksOutput.


        :param job_name: The job_name of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._job_name = job_name

    @property
    def log(self):
        """Gets the log of this ItemForListTasksOutput.  # noqa: E501


        :return: The log of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._log

    @log.setter
    def log(self, log):
        """Sets the log of this ItemForListTasksOutput.


        :param log: The log of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._log = log

    @property
    def name(self):
        """Gets the name of this ItemForListTasksOutput.  # noqa: E501


        :return: The name of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListTasksOutput.


        :param name: The name of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def run_id(self):
        """Gets the run_id of this ItemForListTasksOutput.  # noqa: E501


        :return: The run_id of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._run_id

    @run_id.setter
    def run_id(self, run_id):
        """Sets the run_id of this ItemForListTasksOutput.


        :param run_id: The run_id of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._run_id = run_id

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListTasksOutput.  # noqa: E501


        :return: The start_time of this ItemForListTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListTasksOutput.


        :param start_time: The start_time of this ItemForListTasksOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ItemForListTasksOutput.  # noqa: E501


        :return: The status of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListTasksOutput.


        :param status: The status of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stderr(self):
        """Gets the stderr of this ItemForListTasksOutput.  # noqa: E501


        :return: The stderr of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._stderr

    @stderr.setter
    def stderr(self, stderr):
        """Sets the stderr of this ItemForListTasksOutput.


        :param stderr: The stderr of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._stderr = stderr

    @property
    def stdout(self):
        """Gets the stdout of this ItemForListTasksOutput.  # noqa: E501


        :return: The stdout of this ItemForListTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._stdout

    @stdout.setter
    def stdout(self, stdout):
        """Sets the stdout of this ItemForListTasksOutput.


        :param stdout: The stdout of this ItemForListTasksOutput.  # noqa: E501
        :type: str
        """

        self._stdout = stdout

    @property
    def resource_claimed(self):
        """Gets the resource_claimed of this ItemForListTasksOutput.  # noqa: E501


        :return: The resource_claimed of this ItemForListTasksOutput.  # noqa: E501
        :rtype: ResourceClaimedForListTasksOutput
        """
        return self._resource_claimed

    @resource_claimed.setter
    def resource_claimed(self, resource_claimed):
        """Sets the resource_claimed of this ItemForListTasksOutput.


        :param resource_claimed: The resource_claimed of this ItemForListTasksOutput.  # noqa: E501
        :type: ResourceClaimedForListTasksOutput
        """

        self._resource_claimed = resource_claimed

    @property
    def resource_used(self):
        """Gets the resource_used of this ItemForListTasksOutput.  # noqa: E501


        :return: The resource_used of this ItemForListTasksOutput.  # noqa: E501
        :rtype: ResourceUsedForListTasksOutput
        """
        return self._resource_used

    @resource_used.setter
    def resource_used(self, resource_used):
        """Sets the resource_used of this ItemForListTasksOutput.


        :param resource_used: The resource_used of this ItemForListTasksOutput.  # noqa: E501
        :type: ResourceUsedForListTasksOutput
        """

        self._resource_used = resource_used

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
