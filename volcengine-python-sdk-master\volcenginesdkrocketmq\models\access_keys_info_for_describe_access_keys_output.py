# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccessKeysInfoForDescribeAccessKeysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'acl_config_json': 'str',
        'actived': 'bool',
        'all_authority': 'str',
        'create_time': 'str',
        'description': 'str'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'acl_config_json': 'AclConfigJson',
        'actived': 'Actived',
        'all_authority': 'AllAuthority',
        'create_time': 'CreateTime',
        'description': 'Description'
    }

    def __init__(self, access_key=None, acl_config_json=None, actived=None, all_authority=None, create_time=None, description=None, _configuration=None):  # noqa: E501
        """AccessKeysInfoForDescribeAccessKeysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._acl_config_json = None
        self._actived = None
        self._all_authority = None
        self._create_time = None
        self._description = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if acl_config_json is not None:
            self.acl_config_json = acl_config_json
        if actived is not None:
            self.actived = actived
        if all_authority is not None:
            self.all_authority = all_authority
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description

    @property
    def access_key(self):
        """Gets the access_key of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The access_key of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param access_key: The access_key of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def acl_config_json(self):
        """Gets the acl_config_json of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The acl_config_json of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._acl_config_json

    @acl_config_json.setter
    def acl_config_json(self, acl_config_json):
        """Sets the acl_config_json of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param acl_config_json: The acl_config_json of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: str
        """

        self._acl_config_json = acl_config_json

    @property
    def actived(self):
        """Gets the actived of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The actived of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: bool
        """
        return self._actived

    @actived.setter
    def actived(self, actived):
        """Sets the actived of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param actived: The actived of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: bool
        """

        self._actived = actived

    @property
    def all_authority(self):
        """Gets the all_authority of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The all_authority of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param all_authority: The all_authority of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: str
        """

        self._all_authority = all_authority

    @property
    def create_time(self):
        """Gets the create_time of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The create_time of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param create_time: The create_time of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501


        :return: The description of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this AccessKeysInfoForDescribeAccessKeysOutput.


        :param description: The description of this AccessKeysInfoForDescribeAccessKeysOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccessKeysInfoForDescribeAccessKeysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccessKeysInfoForDescribeAccessKeysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccessKeysInfoForDescribeAccessKeysOutput):
            return True

        return self.to_dict() != other.to_dict()
