# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeFailoverLogsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'context': 'str',
        'failover_logs': 'list[FailoverLogForDescribeFailoverLogsOutput]',
        'instance_id': 'str',
        'total': 'int'
    }

    attribute_map = {
        'context': 'Context',
        'failover_logs': 'FailoverLogs',
        'instance_id': 'InstanceId',
        'total': 'Total'
    }

    def __init__(self, context=None, failover_logs=None, instance_id=None, total=None, _configuration=None):  # noqa: E501
        """DescribeFailoverLogsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._context = None
        self._failover_logs = None
        self._instance_id = None
        self._total = None
        self.discriminator = None

        if context is not None:
            self.context = context
        if failover_logs is not None:
            self.failover_logs = failover_logs
        if instance_id is not None:
            self.instance_id = instance_id
        if total is not None:
            self.total = total

    @property
    def context(self):
        """Gets the context of this DescribeFailoverLogsResponse.  # noqa: E501


        :return: The context of this DescribeFailoverLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._context

    @context.setter
    def context(self, context):
        """Sets the context of this DescribeFailoverLogsResponse.


        :param context: The context of this DescribeFailoverLogsResponse.  # noqa: E501
        :type: str
        """

        self._context = context

    @property
    def failover_logs(self):
        """Gets the failover_logs of this DescribeFailoverLogsResponse.  # noqa: E501


        :return: The failover_logs of this DescribeFailoverLogsResponse.  # noqa: E501
        :rtype: list[FailoverLogForDescribeFailoverLogsOutput]
        """
        return self._failover_logs

    @failover_logs.setter
    def failover_logs(self, failover_logs):
        """Sets the failover_logs of this DescribeFailoverLogsResponse.


        :param failover_logs: The failover_logs of this DescribeFailoverLogsResponse.  # noqa: E501
        :type: list[FailoverLogForDescribeFailoverLogsOutput]
        """

        self._failover_logs = failover_logs

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeFailoverLogsResponse.  # noqa: E501


        :return: The instance_id of this DescribeFailoverLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeFailoverLogsResponse.


        :param instance_id: The instance_id of this DescribeFailoverLogsResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def total(self):
        """Gets the total of this DescribeFailoverLogsResponse.  # noqa: E501


        :return: The total of this DescribeFailoverLogsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeFailoverLogsResponse.


        :param total: The total of this DescribeFailoverLogsResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeFailoverLogsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeFailoverLogsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeFailoverLogsResponse):
            return True

        return self.to_dict() != other.to_dict()
