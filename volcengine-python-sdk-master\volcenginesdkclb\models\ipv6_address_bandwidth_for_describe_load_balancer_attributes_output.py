# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'bandwidth_package_id': 'str',
        'billing_type': 'int',
        'isp': 'str',
        'network_type': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'bandwidth_package_id': 'BandwidthPackageId',
        'billing_type': 'BillingType',
        'isp': 'ISP',
        'network_type': 'NetworkType'
    }

    def __init__(self, bandwidth=None, bandwidth_package_id=None, billing_type=None, isp=None, network_type=None, _configuration=None):  # noqa: E501
        """Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._bandwidth_package_id = None
        self._billing_type = None
        self._isp = None
        self._network_type = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if billing_type is not None:
            self.billing_type = billing_type
        if isp is not None:
            self.isp = isp
        if network_type is not None:
            self.network_type = network_type

    @property
    def bandwidth(self):
        """Gets the bandwidth of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The bandwidth of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.


        :param bandwidth: The bandwidth of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The bandwidth_package_id of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.


        :param bandwidth_package_id: The bandwidth_package_id of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def billing_type(self):
        """Gets the billing_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The billing_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.


        :param billing_type: The billing_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def isp(self):
        """Gets the isp of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The isp of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.


        :param isp: The isp of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def network_type(self):
        """Gets the network_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The network_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.


        :param network_type: The network_type of this Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._network_type = network_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
