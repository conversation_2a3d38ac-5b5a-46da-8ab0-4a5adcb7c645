# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsumedQueueInfoForDescribeConsumedTopicDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'consume_offset': 'int',
        'diff_count': 'int',
        'end_offset': 'int',
        'last_time_stamp': 'int',
        'queue_id': 'str'
    }

    attribute_map = {
        'consume_offset': 'ConsumeOffset',
        'diff_count': 'DiffCount',
        'end_offset': 'EndOffset',
        'last_time_stamp': 'LastTimeStamp',
        'queue_id': 'QueueId'
    }

    def __init__(self, consume_offset=None, diff_count=None, end_offset=None, last_time_stamp=None, queue_id=None, _configuration=None):  # noqa: E501
        """ConsumedQueueInfoForDescribeConsumedTopicDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._consume_offset = None
        self._diff_count = None
        self._end_offset = None
        self._last_time_stamp = None
        self._queue_id = None
        self.discriminator = None

        if consume_offset is not None:
            self.consume_offset = consume_offset
        if diff_count is not None:
            self.diff_count = diff_count
        if end_offset is not None:
            self.end_offset = end_offset
        if last_time_stamp is not None:
            self.last_time_stamp = last_time_stamp
        if queue_id is not None:
            self.queue_id = queue_id

    @property
    def consume_offset(self):
        """Gets the consume_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501


        :return: The consume_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._consume_offset

    @consume_offset.setter
    def consume_offset(self, consume_offset):
        """Sets the consume_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.


        :param consume_offset: The consume_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :type: int
        """

        self._consume_offset = consume_offset

    @property
    def diff_count(self):
        """Gets the diff_count of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501


        :return: The diff_count of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._diff_count

    @diff_count.setter
    def diff_count(self, diff_count):
        """Sets the diff_count of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.


        :param diff_count: The diff_count of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :type: int
        """

        self._diff_count = diff_count

    @property
    def end_offset(self):
        """Gets the end_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501


        :return: The end_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_offset

    @end_offset.setter
    def end_offset(self, end_offset):
        """Sets the end_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.


        :param end_offset: The end_offset of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :type: int
        """

        self._end_offset = end_offset

    @property
    def last_time_stamp(self):
        """Gets the last_time_stamp of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501


        :return: The last_time_stamp of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_time_stamp

    @last_time_stamp.setter
    def last_time_stamp(self, last_time_stamp):
        """Sets the last_time_stamp of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.


        :param last_time_stamp: The last_time_stamp of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :type: int
        """

        self._last_time_stamp = last_time_stamp

    @property
    def queue_id(self):
        """Gets the queue_id of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501


        :return: The queue_id of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._queue_id

    @queue_id.setter
    def queue_id(self, queue_id):
        """Sets the queue_id of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.


        :param queue_id: The queue_id of this ConsumedQueueInfoForDescribeConsumedTopicDetailOutput.  # noqa: E501
        :type: str
        """

        self._queue_id = queue_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsumedQueueInfoForDescribeConsumedTopicDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsumedQueueInfoForDescribeConsumedTopicDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsumedQueueInfoForDescribeConsumedTopicDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
