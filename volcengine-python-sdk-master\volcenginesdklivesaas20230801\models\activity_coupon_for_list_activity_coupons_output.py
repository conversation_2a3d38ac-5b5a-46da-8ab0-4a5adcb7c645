# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActivityCouponForListActivityCouponsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_close_icon': 'bool',
        'count': 'int',
        'coupon': 'CouponForListActivityCouponsOutput',
        'cut_off_time': 'int',
        'duration': 'int',
        'end_time': 'int',
        'id': 'int',
        'pickup_count': 'int',
        'pickup_people_count': 'int',
        'rule': 'int',
        'send_time': 'int',
        'status': 'int'
    }

    attribute_map = {
        'allow_close_icon': 'AllowCloseIcon',
        'count': 'Count',
        'coupon': 'Coupon',
        'cut_off_time': 'CutOffTime',
        'duration': 'Duration',
        'end_time': 'EndTime',
        'id': 'Id',
        'pickup_count': 'PickupCount',
        'pickup_people_count': 'PickupPeopleCount',
        'rule': 'Rule',
        'send_time': 'SendTime',
        'status': 'Status'
    }

    def __init__(self, allow_close_icon=None, count=None, coupon=None, cut_off_time=None, duration=None, end_time=None, id=None, pickup_count=None, pickup_people_count=None, rule=None, send_time=None, status=None, _configuration=None):  # noqa: E501
        """ActivityCouponForListActivityCouponsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_close_icon = None
        self._count = None
        self._coupon = None
        self._cut_off_time = None
        self._duration = None
        self._end_time = None
        self._id = None
        self._pickup_count = None
        self._pickup_people_count = None
        self._rule = None
        self._send_time = None
        self._status = None
        self.discriminator = None

        if allow_close_icon is not None:
            self.allow_close_icon = allow_close_icon
        if count is not None:
            self.count = count
        if coupon is not None:
            self.coupon = coupon
        if cut_off_time is not None:
            self.cut_off_time = cut_off_time
        if duration is not None:
            self.duration = duration
        if end_time is not None:
            self.end_time = end_time
        if id is not None:
            self.id = id
        if pickup_count is not None:
            self.pickup_count = pickup_count
        if pickup_people_count is not None:
            self.pickup_people_count = pickup_people_count
        if rule is not None:
            self.rule = rule
        if send_time is not None:
            self.send_time = send_time
        if status is not None:
            self.status = status

    @property
    def allow_close_icon(self):
        """Gets the allow_close_icon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The allow_close_icon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._allow_close_icon

    @allow_close_icon.setter
    def allow_close_icon(self, allow_close_icon):
        """Sets the allow_close_icon of this ActivityCouponForListActivityCouponsOutput.


        :param allow_close_icon: The allow_close_icon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: bool
        """

        self._allow_close_icon = allow_close_icon

    @property
    def count(self):
        """Gets the count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this ActivityCouponForListActivityCouponsOutput.


        :param count: The count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def coupon(self):
        """Gets the coupon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The coupon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: CouponForListActivityCouponsOutput
        """
        return self._coupon

    @coupon.setter
    def coupon(self, coupon):
        """Sets the coupon of this ActivityCouponForListActivityCouponsOutput.


        :param coupon: The coupon of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: CouponForListActivityCouponsOutput
        """

        self._coupon = coupon

    @property
    def cut_off_time(self):
        """Gets the cut_off_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The cut_off_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._cut_off_time

    @cut_off_time.setter
    def cut_off_time(self, cut_off_time):
        """Sets the cut_off_time of this ActivityCouponForListActivityCouponsOutput.


        :param cut_off_time: The cut_off_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._cut_off_time = cut_off_time

    @property
    def duration(self):
        """Gets the duration of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The duration of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ActivityCouponForListActivityCouponsOutput.


        :param duration: The duration of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def end_time(self):
        """Gets the end_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The end_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ActivityCouponForListActivityCouponsOutput.


        :param end_time: The end_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def id(self):
        """Gets the id of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The id of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ActivityCouponForListActivityCouponsOutput.


        :param id: The id of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def pickup_count(self):
        """Gets the pickup_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The pickup_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._pickup_count

    @pickup_count.setter
    def pickup_count(self, pickup_count):
        """Sets the pickup_count of this ActivityCouponForListActivityCouponsOutput.


        :param pickup_count: The pickup_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._pickup_count = pickup_count

    @property
    def pickup_people_count(self):
        """Gets the pickup_people_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The pickup_people_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._pickup_people_count

    @pickup_people_count.setter
    def pickup_people_count(self, pickup_people_count):
        """Sets the pickup_people_count of this ActivityCouponForListActivityCouponsOutput.


        :param pickup_people_count: The pickup_people_count of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._pickup_people_count = pickup_people_count

    @property
    def rule(self):
        """Gets the rule of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The rule of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rule

    @rule.setter
    def rule(self, rule):
        """Sets the rule of this ActivityCouponForListActivityCouponsOutput.


        :param rule: The rule of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._rule = rule

    @property
    def send_time(self):
        """Gets the send_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The send_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this ActivityCouponForListActivityCouponsOutput.


        :param send_time: The send_time of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def status(self):
        """Gets the status of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501


        :return: The status of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ActivityCouponForListActivityCouponsOutput.


        :param status: The status of this ActivityCouponForListActivityCouponsOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActivityCouponForListActivityCouponsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActivityCouponForListActivityCouponsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActivityCouponForListActivityCouponsOutput):
            return True

        return self.to_dict() != other.to_dict()
