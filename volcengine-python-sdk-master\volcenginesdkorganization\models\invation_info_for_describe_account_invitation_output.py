# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InvationInfoForDescribeAccountInvitationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'account_name': 'str',
        'allow_exit': 'int',
        'created_time': 'str',
        'delete_uk': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'id': 'str',
        'link_id': 'str',
        'main_name': 'str',
        'org_id': 'str',
        'org_unit_id': 'str',
        'owner': 'str',
        'owner_name': 'str',
        'reject_reason': 'str',
        'show_name': 'str',
        'status': 'int',
        'updated_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'account_name': 'AccountName',
        'allow_exit': 'AllowExit',
        'created_time': 'CreatedTime',
        'delete_uk': 'DeleteUk',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'id': 'ID',
        'link_id': 'LinkID',
        'main_name': 'MainName',
        'org_id': 'OrgID',
        'org_unit_id': 'OrgUnitID',
        'owner': 'Owner',
        'owner_name': 'OwnerName',
        'reject_reason': 'RejectReason',
        'show_name': 'ShowName',
        'status': 'Status',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, account_id=None, account_name=None, allow_exit=None, created_time=None, delete_uk=None, deleted_time=None, description=None, id=None, link_id=None, main_name=None, org_id=None, org_unit_id=None, owner=None, owner_name=None, reject_reason=None, show_name=None, status=None, updated_time=None, _configuration=None):  # noqa: E501
        """InvationInfoForDescribeAccountInvitationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._account_name = None
        self._allow_exit = None
        self._created_time = None
        self._delete_uk = None
        self._deleted_time = None
        self._description = None
        self._id = None
        self._link_id = None
        self._main_name = None
        self._org_id = None
        self._org_unit_id = None
        self._owner = None
        self._owner_name = None
        self._reject_reason = None
        self._show_name = None
        self._status = None
        self._updated_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if account_name is not None:
            self.account_name = account_name
        if allow_exit is not None:
            self.allow_exit = allow_exit
        if created_time is not None:
            self.created_time = created_time
        if delete_uk is not None:
            self.delete_uk = delete_uk
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if link_id is not None:
            self.link_id = link_id
        if main_name is not None:
            self.main_name = main_name
        if org_id is not None:
            self.org_id = org_id
        if org_unit_id is not None:
            self.org_unit_id = org_unit_id
        if owner is not None:
            self.owner = owner
        if owner_name is not None:
            self.owner_name = owner_name
        if reject_reason is not None:
            self.reject_reason = reject_reason
        if show_name is not None:
            self.show_name = show_name
        if status is not None:
            self.status = status
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def account_id(self):
        """Gets the account_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The account_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this InvationInfoForDescribeAccountInvitationOutput.


        :param account_id: The account_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def account_name(self):
        """Gets the account_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The account_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this InvationInfoForDescribeAccountInvitationOutput.


        :param account_name: The account_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def allow_exit(self):
        """Gets the allow_exit of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The allow_exit of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._allow_exit

    @allow_exit.setter
    def allow_exit(self, allow_exit):
        """Sets the allow_exit of this InvationInfoForDescribeAccountInvitationOutput.


        :param allow_exit: The allow_exit of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: int
        """

        self._allow_exit = allow_exit

    @property
    def created_time(self):
        """Gets the created_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The created_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this InvationInfoForDescribeAccountInvitationOutput.


        :param created_time: The created_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def delete_uk(self):
        """Gets the delete_uk of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The delete_uk of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._delete_uk

    @delete_uk.setter
    def delete_uk(self, delete_uk):
        """Sets the delete_uk of this InvationInfoForDescribeAccountInvitationOutput.


        :param delete_uk: The delete_uk of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._delete_uk = delete_uk

    @property
    def deleted_time(self):
        """Gets the deleted_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The deleted_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this InvationInfoForDescribeAccountInvitationOutput.


        :param deleted_time: The deleted_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The description of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this InvationInfoForDescribeAccountInvitationOutput.


        :param description: The description of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this InvationInfoForDescribeAccountInvitationOutput.


        :param id: The id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def link_id(self):
        """Gets the link_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The link_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._link_id

    @link_id.setter
    def link_id(self, link_id):
        """Sets the link_id of this InvationInfoForDescribeAccountInvitationOutput.


        :param link_id: The link_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._link_id = link_id

    @property
    def main_name(self):
        """Gets the main_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The main_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._main_name

    @main_name.setter
    def main_name(self, main_name):
        """Sets the main_name of this InvationInfoForDescribeAccountInvitationOutput.


        :param main_name: The main_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._main_name = main_name

    @property
    def org_id(self):
        """Gets the org_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The org_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_id

    @org_id.setter
    def org_id(self, org_id):
        """Sets the org_id of this InvationInfoForDescribeAccountInvitationOutput.


        :param org_id: The org_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._org_id = org_id

    @property
    def org_unit_id(self):
        """Gets the org_unit_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The org_unit_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_unit_id

    @org_unit_id.setter
    def org_unit_id(self, org_unit_id):
        """Sets the org_unit_id of this InvationInfoForDescribeAccountInvitationOutput.


        :param org_unit_id: The org_unit_id of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._org_unit_id = org_unit_id

    @property
    def owner(self):
        """Gets the owner of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The owner of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this InvationInfoForDescribeAccountInvitationOutput.


        :param owner: The owner of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def owner_name(self):
        """Gets the owner_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The owner_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_name

    @owner_name.setter
    def owner_name(self, owner_name):
        """Sets the owner_name of this InvationInfoForDescribeAccountInvitationOutput.


        :param owner_name: The owner_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._owner_name = owner_name

    @property
    def reject_reason(self):
        """Gets the reject_reason of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The reject_reason of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._reject_reason

    @reject_reason.setter
    def reject_reason(self, reject_reason):
        """Sets the reject_reason of this InvationInfoForDescribeAccountInvitationOutput.


        :param reject_reason: The reject_reason of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._reject_reason = reject_reason

    @property
    def show_name(self):
        """Gets the show_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The show_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._show_name

    @show_name.setter
    def show_name(self, show_name):
        """Sets the show_name of this InvationInfoForDescribeAccountInvitationOutput.


        :param show_name: The show_name of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._show_name = show_name

    @property
    def status(self):
        """Gets the status of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The status of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InvationInfoForDescribeAccountInvitationOutput.


        :param status: The status of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def updated_time(self):
        """Gets the updated_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501


        :return: The updated_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this InvationInfoForDescribeAccountInvitationOutput.


        :param updated_time: The updated_time of this InvationInfoForDescribeAccountInvitationOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InvationInfoForDescribeAccountInvitationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InvationInfoForDescribeAccountInvitationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InvationInfoForDescribeAccountInvitationOutput):
            return True

        return self.to_dict() != other.to_dict()
