# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetDevFingerprintSoftwareOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'id': 'str',
        'name': 'str',
        'private_ip': 'str',
        'public_ip': 'str',
        'start_time': 'int',
        'status': 'str',
        'type': 'str',
        'update_time': 'int',
        'version': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'id': 'ID',
        'name': 'Name',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'start_time': 'StartTime',
        'status': 'Status',
        'type': 'Type',
        'update_time': 'UpdateTime',
        'version': 'Version'
    }

    def __init__(self, account_id=None, asset_id=None, asset_name=None, id=None, name=None, private_ip=None, public_ip=None, start_time=None, status=None, type=None, update_time=None, version=None, _configuration=None):  # noqa: E501
        """DataForGetDevFingerprintSoftwareOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asset_id = None
        self._asset_name = None
        self._id = None
        self._name = None
        self._private_ip = None
        self._public_ip = None
        self._start_time = None
        self._status = None
        self._type = None
        self._update_time = None
        self._version = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time
        if version is not None:
            self.version = version

    @property
    def account_id(self):
        """Gets the account_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The account_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForGetDevFingerprintSoftwareOutput.


        :param account_id: The account_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The asset_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForGetDevFingerprintSoftwareOutput.


        :param asset_id: The asset_id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The asset_name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this DataForGetDevFingerprintSoftwareOutput.


        :param asset_name: The asset_name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def id(self):
        """Gets the id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetDevFingerprintSoftwareOutput.


        :param id: The id of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForGetDevFingerprintSoftwareOutput.


        :param name: The name of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The private_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForGetDevFingerprintSoftwareOutput.


        :param private_ip: The private_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The public_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForGetDevFingerprintSoftwareOutput.


        :param public_ip: The public_ip of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The start_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetDevFingerprintSoftwareOutput.


        :param start_time: The start_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The status of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForGetDevFingerprintSoftwareOutput.


        :param status: The status of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The type of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForGetDevFingerprintSoftwareOutput.


        :param type: The type of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The update_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetDevFingerprintSoftwareOutput.


        :param update_time: The update_time of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def version(self):
        """Gets the version of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501


        :return: The version of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this DataForGetDevFingerprintSoftwareOutput.


        :param version: The version of this DataForGetDevFingerprintSoftwareOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetDevFingerprintSoftwareOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetDevFingerprintSoftwareOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetDevFingerprintSoftwareOutput):
            return True

        return self.to_dict() != other.to_dict()
