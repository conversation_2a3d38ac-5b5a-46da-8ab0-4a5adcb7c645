# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetHidsAlarmStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_critical_count': 'int',
        'alarm_high_count': 'int',
        'alarm_low_count': 'int',
        'alarm_medium_count': 'int',
        'alarm_processed_count': 'int',
        'alarm_total': 'int',
        'alarm_white_count': 'int',
        'isolate_file_count': 'int',
        'isolate_today_count': 'int',
        'precision_defense_count': 'int',
        'precision_defense_today_count': 'int',
        'unhandled_today_count': 'int'
    }

    attribute_map = {
        'alarm_critical_count': 'AlarmCriticalCount',
        'alarm_high_count': 'AlarmHighCount',
        'alarm_low_count': 'AlarmLowCount',
        'alarm_medium_count': 'AlarmMediumCount',
        'alarm_processed_count': 'AlarmProcessedCount',
        'alarm_total': 'AlarmTotal',
        'alarm_white_count': 'AlarmWhiteCount',
        'isolate_file_count': 'IsolateFileCount',
        'isolate_today_count': 'IsolateTodayCount',
        'precision_defense_count': 'PrecisionDefenseCount',
        'precision_defense_today_count': 'PrecisionDefenseTodayCount',
        'unhandled_today_count': 'UnhandledTodayCount'
    }

    def __init__(self, alarm_critical_count=None, alarm_high_count=None, alarm_low_count=None, alarm_medium_count=None, alarm_processed_count=None, alarm_total=None, alarm_white_count=None, isolate_file_count=None, isolate_today_count=None, precision_defense_count=None, precision_defense_today_count=None, unhandled_today_count=None, _configuration=None):  # noqa: E501
        """GetHidsAlarmStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_critical_count = None
        self._alarm_high_count = None
        self._alarm_low_count = None
        self._alarm_medium_count = None
        self._alarm_processed_count = None
        self._alarm_total = None
        self._alarm_white_count = None
        self._isolate_file_count = None
        self._isolate_today_count = None
        self._precision_defense_count = None
        self._precision_defense_today_count = None
        self._unhandled_today_count = None
        self.discriminator = None

        if alarm_critical_count is not None:
            self.alarm_critical_count = alarm_critical_count
        if alarm_high_count is not None:
            self.alarm_high_count = alarm_high_count
        if alarm_low_count is not None:
            self.alarm_low_count = alarm_low_count
        if alarm_medium_count is not None:
            self.alarm_medium_count = alarm_medium_count
        if alarm_processed_count is not None:
            self.alarm_processed_count = alarm_processed_count
        if alarm_total is not None:
            self.alarm_total = alarm_total
        if alarm_white_count is not None:
            self.alarm_white_count = alarm_white_count
        if isolate_file_count is not None:
            self.isolate_file_count = isolate_file_count
        if isolate_today_count is not None:
            self.isolate_today_count = isolate_today_count
        if precision_defense_count is not None:
            self.precision_defense_count = precision_defense_count
        if precision_defense_today_count is not None:
            self.precision_defense_today_count = precision_defense_today_count
        if unhandled_today_count is not None:
            self.unhandled_today_count = unhandled_today_count

    @property
    def alarm_critical_count(self):
        """Gets the alarm_critical_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_critical_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_critical_count

    @alarm_critical_count.setter
    def alarm_critical_count(self, alarm_critical_count):
        """Sets the alarm_critical_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_critical_count: The alarm_critical_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_critical_count = alarm_critical_count

    @property
    def alarm_high_count(self):
        """Gets the alarm_high_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_high_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_high_count

    @alarm_high_count.setter
    def alarm_high_count(self, alarm_high_count):
        """Sets the alarm_high_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_high_count: The alarm_high_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_high_count = alarm_high_count

    @property
    def alarm_low_count(self):
        """Gets the alarm_low_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_low_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_low_count

    @alarm_low_count.setter
    def alarm_low_count(self, alarm_low_count):
        """Sets the alarm_low_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_low_count: The alarm_low_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_low_count = alarm_low_count

    @property
    def alarm_medium_count(self):
        """Gets the alarm_medium_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_medium_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_medium_count

    @alarm_medium_count.setter
    def alarm_medium_count(self, alarm_medium_count):
        """Sets the alarm_medium_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_medium_count: The alarm_medium_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_medium_count = alarm_medium_count

    @property
    def alarm_processed_count(self):
        """Gets the alarm_processed_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_processed_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_processed_count

    @alarm_processed_count.setter
    def alarm_processed_count(self, alarm_processed_count):
        """Sets the alarm_processed_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_processed_count: The alarm_processed_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_processed_count = alarm_processed_count

    @property
    def alarm_total(self):
        """Gets the alarm_total of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_total of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_total

    @alarm_total.setter
    def alarm_total(self, alarm_total):
        """Sets the alarm_total of this GetHidsAlarmStatisticsResponse.


        :param alarm_total: The alarm_total of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_total = alarm_total

    @property
    def alarm_white_count(self):
        """Gets the alarm_white_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The alarm_white_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm_white_count

    @alarm_white_count.setter
    def alarm_white_count(self, alarm_white_count):
        """Sets the alarm_white_count of this GetHidsAlarmStatisticsResponse.


        :param alarm_white_count: The alarm_white_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._alarm_white_count = alarm_white_count

    @property
    def isolate_file_count(self):
        """Gets the isolate_file_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The isolate_file_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._isolate_file_count

    @isolate_file_count.setter
    def isolate_file_count(self, isolate_file_count):
        """Sets the isolate_file_count of this GetHidsAlarmStatisticsResponse.


        :param isolate_file_count: The isolate_file_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._isolate_file_count = isolate_file_count

    @property
    def isolate_today_count(self):
        """Gets the isolate_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The isolate_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._isolate_today_count

    @isolate_today_count.setter
    def isolate_today_count(self, isolate_today_count):
        """Sets the isolate_today_count of this GetHidsAlarmStatisticsResponse.


        :param isolate_today_count: The isolate_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._isolate_today_count = isolate_today_count

    @property
    def precision_defense_count(self):
        """Gets the precision_defense_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The precision_defense_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._precision_defense_count

    @precision_defense_count.setter
    def precision_defense_count(self, precision_defense_count):
        """Sets the precision_defense_count of this GetHidsAlarmStatisticsResponse.


        :param precision_defense_count: The precision_defense_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._precision_defense_count = precision_defense_count

    @property
    def precision_defense_today_count(self):
        """Gets the precision_defense_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The precision_defense_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._precision_defense_today_count

    @precision_defense_today_count.setter
    def precision_defense_today_count(self, precision_defense_today_count):
        """Sets the precision_defense_today_count of this GetHidsAlarmStatisticsResponse.


        :param precision_defense_today_count: The precision_defense_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._precision_defense_today_count = precision_defense_today_count

    @property
    def unhandled_today_count(self):
        """Gets the unhandled_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501


        :return: The unhandled_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._unhandled_today_count

    @unhandled_today_count.setter
    def unhandled_today_count(self, unhandled_today_count):
        """Sets the unhandled_today_count of this GetHidsAlarmStatisticsResponse.


        :param unhandled_today_count: The unhandled_today_count of this GetHidsAlarmStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._unhandled_today_count = unhandled_today_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetHidsAlarmStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetHidsAlarmStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetHidsAlarmStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
