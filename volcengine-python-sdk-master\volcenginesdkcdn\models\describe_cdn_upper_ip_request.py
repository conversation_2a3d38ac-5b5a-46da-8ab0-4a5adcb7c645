# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnUpperIpRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'ip_version': 'str',
        'rs_ip': 'bool'
    }

    attribute_map = {
        'domain': 'Domain',
        'ip_version': 'IpVersion',
        'rs_ip': 'RsIp'
    }

    def __init__(self, domain=None, ip_version=None, rs_ip=None, _configuration=None):  # noqa: E501
        """DescribeCdnUpperIpRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._ip_version = None
        self._rs_ip = None
        self.discriminator = None

        self.domain = domain
        if ip_version is not None:
            self.ip_version = ip_version
        if rs_ip is not None:
            self.rs_ip = rs_ip

    @property
    def domain(self):
        """Gets the domain of this DescribeCdnUpperIpRequest.  # noqa: E501


        :return: The domain of this DescribeCdnUpperIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeCdnUpperIpRequest.


        :param domain: The domain of this DescribeCdnUpperIpRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def ip_version(self):
        """Gets the ip_version of this DescribeCdnUpperIpRequest.  # noqa: E501


        :return: The ip_version of this DescribeCdnUpperIpRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this DescribeCdnUpperIpRequest.


        :param ip_version: The ip_version of this DescribeCdnUpperIpRequest.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def rs_ip(self):
        """Gets the rs_ip of this DescribeCdnUpperIpRequest.  # noqa: E501


        :return: The rs_ip of this DescribeCdnUpperIpRequest.  # noqa: E501
        :rtype: bool
        """
        return self._rs_ip

    @rs_ip.setter
    def rs_ip(self, rs_ip):
        """Sets the rs_ip of this DescribeCdnUpperIpRequest.


        :param rs_ip: The rs_ip of this DescribeCdnUpperIpRequest.  # noqa: E501
        :type: bool
        """

        self._rs_ip = rs_ip

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnUpperIpRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnUpperIpRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnUpperIpRequest):
            return True

        return self.to_dict() != other.to_dict()
