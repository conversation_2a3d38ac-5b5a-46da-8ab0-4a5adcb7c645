# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertConvertStorylineForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all_tags': 'list[AllTagForGetExecutionOutput]',
        'clips': 'list[ConvertClipForGetExecutionOutput]',
        'duration': 'float',
        'storylines': 'list[ConvertStorylineForGetExecutionOutput]',
        'summaries': 'list[str]',
        'titles': 'list[str]'
    }

    attribute_map = {
        'all_tags': 'AllTags',
        'clips': 'Clips',
        'duration': 'Duration',
        'storylines': 'Storylines',
        'summaries': 'Summaries',
        'titles': 'Titles'
    }

    def __init__(self, all_tags=None, clips=None, duration=None, storylines=None, summaries=None, titles=None, _configuration=None):  # noqa: E501
        """ConvertConvertStorylineForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all_tags = None
        self._clips = None
        self._duration = None
        self._storylines = None
        self._summaries = None
        self._titles = None
        self.discriminator = None

        if all_tags is not None:
            self.all_tags = all_tags
        if clips is not None:
            self.clips = clips
        if duration is not None:
            self.duration = duration
        if storylines is not None:
            self.storylines = storylines
        if summaries is not None:
            self.summaries = summaries
        if titles is not None:
            self.titles = titles

    @property
    def all_tags(self):
        """Gets the all_tags of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The all_tags of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: list[AllTagForGetExecutionOutput]
        """
        return self._all_tags

    @all_tags.setter
    def all_tags(self, all_tags):
        """Sets the all_tags of this ConvertConvertStorylineForGetExecutionOutput.


        :param all_tags: The all_tags of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: list[AllTagForGetExecutionOutput]
        """

        self._all_tags = all_tags

    @property
    def clips(self):
        """Gets the clips of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The clips of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: list[ConvertClipForGetExecutionOutput]
        """
        return self._clips

    @clips.setter
    def clips(self, clips):
        """Sets the clips of this ConvertConvertStorylineForGetExecutionOutput.


        :param clips: The clips of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: list[ConvertClipForGetExecutionOutput]
        """

        self._clips = clips

    @property
    def duration(self):
        """Gets the duration of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertConvertStorylineForGetExecutionOutput.


        :param duration: The duration of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def storylines(self):
        """Gets the storylines of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The storylines of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: list[ConvertStorylineForGetExecutionOutput]
        """
        return self._storylines

    @storylines.setter
    def storylines(self, storylines):
        """Sets the storylines of this ConvertConvertStorylineForGetExecutionOutput.


        :param storylines: The storylines of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: list[ConvertStorylineForGetExecutionOutput]
        """

        self._storylines = storylines

    @property
    def summaries(self):
        """Gets the summaries of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The summaries of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._summaries

    @summaries.setter
    def summaries(self, summaries):
        """Sets the summaries of this ConvertConvertStorylineForGetExecutionOutput.


        :param summaries: The summaries of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: list[str]
        """

        self._summaries = summaries

    @property
    def titles(self):
        """Gets the titles of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501


        :return: The titles of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._titles

    @titles.setter
    def titles(self, titles):
        """Sets the titles of this ConvertConvertStorylineForGetExecutionOutput.


        :param titles: The titles of this ConvertConvertStorylineForGetExecutionOutput.  # noqa: E501
        :type: list[str]
        """

        self._titles = titles

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertConvertStorylineForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertConvertStorylineForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertConvertStorylineForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
