# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNatFirewallListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'firewall_status': 'list[str]',
        'nat_firewall_id': 'str',
        'nat_firewall_name': 'str',
        'nat_gateway_id': 'str',
        'nat_gateway_name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'region_code': 'list[str]',
        'vpc_id': 'str',
        'vpc_name': 'str'
    }

    attribute_map = {
        'firewall_status': 'FirewallStatus',
        'nat_firewall_id': 'NatFirewallId',
        'nat_firewall_name': 'NatFirewallName',
        'nat_gateway_id': 'NatGatewayId',
        'nat_gateway_name': 'NatGatewayName',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'region_code': 'RegionCode',
        'vpc_id': 'VpcId',
        'vpc_name': 'VpcName'
    }

    def __init__(self, firewall_status=None, nat_firewall_id=None, nat_firewall_name=None, nat_gateway_id=None, nat_gateway_name=None, page_number=None, page_size=None, region_code=None, vpc_id=None, vpc_name=None, _configuration=None):  # noqa: E501
        """DescribeNatFirewallListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._firewall_status = None
        self._nat_firewall_id = None
        self._nat_firewall_name = None
        self._nat_gateway_id = None
        self._nat_gateway_name = None
        self._page_number = None
        self._page_size = None
        self._region_code = None
        self._vpc_id = None
        self._vpc_name = None
        self.discriminator = None

        if firewall_status is not None:
            self.firewall_status = firewall_status
        if nat_firewall_id is not None:
            self.nat_firewall_id = nat_firewall_id
        if nat_firewall_name is not None:
            self.nat_firewall_name = nat_firewall_name
        if nat_gateway_id is not None:
            self.nat_gateway_id = nat_gateway_id
        if nat_gateway_name is not None:
            self.nat_gateway_name = nat_gateway_name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if region_code is not None:
            self.region_code = region_code
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_name is not None:
            self.vpc_name = vpc_name

    @property
    def firewall_status(self):
        """Gets the firewall_status of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The firewall_status of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._firewall_status

    @firewall_status.setter
    def firewall_status(self, firewall_status):
        """Sets the firewall_status of this DescribeNatFirewallListRequest.


        :param firewall_status: The firewall_status of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: list[str]
        """

        self._firewall_status = firewall_status

    @property
    def nat_firewall_id(self):
        """Gets the nat_firewall_id of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The nat_firewall_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_firewall_id

    @nat_firewall_id.setter
    def nat_firewall_id(self, nat_firewall_id):
        """Sets the nat_firewall_id of this DescribeNatFirewallListRequest.


        :param nat_firewall_id: The nat_firewall_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._nat_firewall_id = nat_firewall_id

    @property
    def nat_firewall_name(self):
        """Gets the nat_firewall_name of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The nat_firewall_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_firewall_name

    @nat_firewall_name.setter
    def nat_firewall_name(self, nat_firewall_name):
        """Sets the nat_firewall_name of this DescribeNatFirewallListRequest.


        :param nat_firewall_name: The nat_firewall_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._nat_firewall_name = nat_firewall_name

    @property
    def nat_gateway_id(self):
        """Gets the nat_gateway_id of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The nat_gateway_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_gateway_id

    @nat_gateway_id.setter
    def nat_gateway_id(self, nat_gateway_id):
        """Sets the nat_gateway_id of this DescribeNatFirewallListRequest.


        :param nat_gateway_id: The nat_gateway_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._nat_gateway_id = nat_gateway_id

    @property
    def nat_gateway_name(self):
        """Gets the nat_gateway_name of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The nat_gateway_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_gateway_name

    @nat_gateway_name.setter
    def nat_gateway_name(self, nat_gateway_name):
        """Sets the nat_gateway_name of this DescribeNatFirewallListRequest.


        :param nat_gateway_name: The nat_gateway_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._nat_gateway_name = nat_gateway_name

    @property
    def page_number(self):
        """Gets the page_number of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The page_number of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeNatFirewallListRequest.


        :param page_number: The page_number of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The page_size of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeNatFirewallListRequest.


        :param page_size: The page_size of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def region_code(self):
        """Gets the region_code of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The region_code of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._region_code

    @region_code.setter
    def region_code(self, region_code):
        """Sets the region_code of this DescribeNatFirewallListRequest.


        :param region_code: The region_code of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: list[str]
        """

        self._region_code = region_code

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The vpc_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeNatFirewallListRequest.


        :param vpc_id: The vpc_id of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_name(self):
        """Gets the vpc_name of this DescribeNatFirewallListRequest.  # noqa: E501


        :return: The vpc_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_name

    @vpc_name.setter
    def vpc_name(self, vpc_name):
        """Sets the vpc_name of this DescribeNatFirewallListRequest.


        :param vpc_name: The vpc_name of this DescribeNatFirewallListRequest.  # noqa: E501
        :type: str
        """

        self._vpc_name = vpc_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNatFirewallListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNatFirewallListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNatFirewallListRequest):
            return True

        return self.to_dict() != other.to_dict()
