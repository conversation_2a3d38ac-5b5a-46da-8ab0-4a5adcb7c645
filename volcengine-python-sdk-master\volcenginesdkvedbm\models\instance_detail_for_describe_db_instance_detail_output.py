# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceDetailForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'db_engine_version': 'str',
        'deletion_protection': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_structures': 'list[InstanceStructureForDescribeDBInstanceDetailOutput]',
        'lower_case_table_names': 'str',
        'maintenance_window': 'MaintenanceWindowForDescribeDBInstanceDetailOutput',
        'pre_paid_storage_in_gb': 'int',
        'project_name': 'str',
        'region_id': 'str',
        'spec_family': 'str',
        'storage_charge_type': 'str',
        'storage_used_gi_b': 'float',
        'subnet_id': 'str',
        'time_zone': 'str',
        'vpc_id': 'str',
        'zone_ids': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'db_engine_version': 'DBEngineVersion',
        'deletion_protection': 'DeletionProtection',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_structures': 'InstanceStructures',
        'lower_case_table_names': 'LowerCaseTableNames',
        'maintenance_window': 'MaintenanceWindow',
        'pre_paid_storage_in_gb': 'PrePaidStorageInGB',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'spec_family': 'SpecFamily',
        'storage_charge_type': 'StorageChargeType',
        'storage_used_gi_b': 'StorageUsedGiB',
        'subnet_id': 'SubnetId',
        'time_zone': 'TimeZone',
        'vpc_id': 'VpcId',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, create_time=None, db_engine_version=None, deletion_protection=None, instance_id=None, instance_name=None, instance_status=None, instance_structures=None, lower_case_table_names=None, maintenance_window=None, pre_paid_storage_in_gb=None, project_name=None, region_id=None, spec_family=None, storage_charge_type=None, storage_used_gi_b=None, subnet_id=None, time_zone=None, vpc_id=None, zone_ids=None, _configuration=None):  # noqa: E501
        """InstanceDetailForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._db_engine_version = None
        self._deletion_protection = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_structures = None
        self._lower_case_table_names = None
        self._maintenance_window = None
        self._pre_paid_storage_in_gb = None
        self._project_name = None
        self._region_id = None
        self._spec_family = None
        self._storage_charge_type = None
        self._storage_used_gi_b = None
        self._subnet_id = None
        self._time_zone = None
        self._vpc_id = None
        self._zone_ids = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_structures is not None:
            self.instance_structures = instance_structures
        if lower_case_table_names is not None:
            self.lower_case_table_names = lower_case_table_names
        if maintenance_window is not None:
            self.maintenance_window = maintenance_window
        if pre_paid_storage_in_gb is not None:
            self.pre_paid_storage_in_gb = pre_paid_storage_in_gb
        if project_name is not None:
            self.project_name = project_name
        if region_id is not None:
            self.region_id = region_id
        if spec_family is not None:
            self.spec_family = spec_family
        if storage_charge_type is not None:
            self.storage_charge_type = storage_charge_type
        if storage_used_gi_b is not None:
            self.storage_used_gi_b = storage_used_gi_b
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if time_zone is not None:
            self.time_zone = time_zone
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def create_time(self):
        """Gets the create_time of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The create_time of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param create_time: The create_time of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The db_engine_version of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param db_engine_version: The db_engine_version of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The deletion_protection of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param deletion_protection: The deletion_protection of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._deletion_protection = deletion_protection

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param instance_id: The instance_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param instance_name: The instance_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_status of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param instance_status: The instance_status of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_structures(self):
        """Gets the instance_structures of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The instance_structures of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[InstanceStructureForDescribeDBInstanceDetailOutput]
        """
        return self._instance_structures

    @instance_structures.setter
    def instance_structures(self, instance_structures):
        """Sets the instance_structures of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param instance_structures: The instance_structures of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[InstanceStructureForDescribeDBInstanceDetailOutput]
        """

        self._instance_structures = instance_structures

    @property
    def lower_case_table_names(self):
        """Gets the lower_case_table_names of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The lower_case_table_names of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._lower_case_table_names

    @lower_case_table_names.setter
    def lower_case_table_names(self, lower_case_table_names):
        """Sets the lower_case_table_names of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param lower_case_table_names: The lower_case_table_names of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._lower_case_table_names = lower_case_table_names

    @property
    def maintenance_window(self):
        """Gets the maintenance_window of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The maintenance_window of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: MaintenanceWindowForDescribeDBInstanceDetailOutput
        """
        return self._maintenance_window

    @maintenance_window.setter
    def maintenance_window(self, maintenance_window):
        """Sets the maintenance_window of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param maintenance_window: The maintenance_window of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: MaintenanceWindowForDescribeDBInstanceDetailOutput
        """

        self._maintenance_window = maintenance_window

    @property
    def pre_paid_storage_in_gb(self):
        """Gets the pre_paid_storage_in_gb of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The pre_paid_storage_in_gb of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._pre_paid_storage_in_gb

    @pre_paid_storage_in_gb.setter
    def pre_paid_storage_in_gb(self, pre_paid_storage_in_gb):
        """Sets the pre_paid_storage_in_gb of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param pre_paid_storage_in_gb: The pre_paid_storage_in_gb of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._pre_paid_storage_in_gb = pre_paid_storage_in_gb

    @property
    def project_name(self):
        """Gets the project_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The project_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param project_name: The project_name of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The region_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param region_id: The region_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def spec_family(self):
        """Gets the spec_family of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The spec_family of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_family

    @spec_family.setter
    def spec_family(self, spec_family):
        """Sets the spec_family of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param spec_family: The spec_family of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._spec_family = spec_family

    @property
    def storage_charge_type(self):
        """Gets the storage_charge_type of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_charge_type of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_charge_type

    @storage_charge_type.setter
    def storage_charge_type(self, storage_charge_type):
        """Sets the storage_charge_type of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param storage_charge_type: The storage_charge_type of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._storage_charge_type = storage_charge_type

    @property
    def storage_used_gi_b(self):
        """Gets the storage_used_gi_b of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The storage_used_gi_b of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: float
        """
        return self._storage_used_gi_b

    @storage_used_gi_b.setter
    def storage_used_gi_b(self, storage_used_gi_b):
        """Sets the storage_used_gi_b of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param storage_used_gi_b: The storage_used_gi_b of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: float
        """

        self._storage_used_gi_b = storage_used_gi_b

    @property
    def subnet_id(self):
        """Gets the subnet_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The subnet_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param subnet_id: The subnet_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def time_zone(self):
        """Gets the time_zone of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The time_zone of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param time_zone: The time_zone of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    @property
    def vpc_id(self):
        """Gets the vpc_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The vpc_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param vpc_id: The vpc_id of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_ids(self):
        """Gets the zone_ids of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The zone_ids of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this InstanceDetailForDescribeDBInstanceDetailOutput.


        :param zone_ids: The zone_ids of this InstanceDetailForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceDetailForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceDetailForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceDetailForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
