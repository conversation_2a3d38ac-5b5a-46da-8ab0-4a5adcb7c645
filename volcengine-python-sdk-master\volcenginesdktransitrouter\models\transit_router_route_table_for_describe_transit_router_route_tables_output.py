# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'description': 'str',
        'in_route_policy_table_id': 'str',
        'out_route_policy_table_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeTransitRouterRouteTablesOutput]',
        'transit_router_route_table_id': 'str',
        'transit_router_route_table_name': 'str',
        'transit_router_route_table_type': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'description': 'Description',
        'in_route_policy_table_id': 'InRoutePolicyTableId',
        'out_route_policy_table_id': 'OutRoutePolicyTableId',
        'status': 'Status',
        'tags': 'Tags',
        'transit_router_route_table_id': 'TransitRouterRouteTableId',
        'transit_router_route_table_name': 'TransitRouterRouteTableName',
        'transit_router_route_table_type': 'TransitRouterRouteTableType',
        'update_time': 'UpdateTime'
    }

    def __init__(self, creation_time=None, description=None, in_route_policy_table_id=None, out_route_policy_table_id=None, status=None, tags=None, transit_router_route_table_id=None, transit_router_route_table_name=None, transit_router_route_table_type=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._description = None
        self._in_route_policy_table_id = None
        self._out_route_policy_table_id = None
        self._status = None
        self._tags = None
        self._transit_router_route_table_id = None
        self._transit_router_route_table_name = None
        self._transit_router_route_table_type = None
        self._update_time = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if in_route_policy_table_id is not None:
            self.in_route_policy_table_id = in_route_policy_table_id
        if out_route_policy_table_id is not None:
            self.out_route_policy_table_id = out_route_policy_table_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id
        if transit_router_route_table_name is not None:
            self.transit_router_route_table_name = transit_router_route_table_name
        if transit_router_route_table_type is not None:
            self.transit_router_route_table_type = transit_router_route_table_type
        if update_time is not None:
            self.update_time = update_time

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param creation_time: The creation_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The description of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param description: The description of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def in_route_policy_table_id(self):
        """Gets the in_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The in_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._in_route_policy_table_id

    @in_route_policy_table_id.setter
    def in_route_policy_table_id(self, in_route_policy_table_id):
        """Sets the in_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param in_route_policy_table_id: The in_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._in_route_policy_table_id = in_route_policy_table_id

    @property
    def out_route_policy_table_id(self):
        """Gets the out_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The out_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._out_route_policy_table_id

    @out_route_policy_table_id.setter
    def out_route_policy_table_id(self, out_route_policy_table_id):
        """Sets the out_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param out_route_policy_table_id: The out_route_policy_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._out_route_policy_table_id = out_route_policy_table_id

    @property
    def status(self):
        """Gets the status of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The status of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param status: The status of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The tags of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: list[TagForDescribeTransitRouterRouteTablesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param tags: The tags of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: list[TagForDescribeTransitRouterRouteTablesOutput]
        """

        self._tags = tags

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The transit_router_route_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param transit_router_route_table_id: The transit_router_route_table_id of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    @property
    def transit_router_route_table_name(self):
        """Gets the transit_router_route_table_name of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The transit_router_route_table_name of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_name

    @transit_router_route_table_name.setter
    def transit_router_route_table_name(self, transit_router_route_table_name):
        """Sets the transit_router_route_table_name of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param transit_router_route_table_name: The transit_router_route_table_name of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_name = transit_router_route_table_name

    @property
    def transit_router_route_table_type(self):
        """Gets the transit_router_route_table_type of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The transit_router_route_table_type of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_type

    @transit_router_route_table_type.setter
    def transit_router_route_table_type(self, transit_router_route_table_type):
        """Sets the transit_router_route_table_type of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param transit_router_route_table_type: The transit_router_route_table_type of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_type = transit_router_route_table_type

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.


        :param update_time: The update_time of this TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterRouteTableForDescribeTransitRouterRouteTablesOutput):
            return True

        return self.to_dict() != other.to_dict()
