# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DNSDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'a_record': 'list[str]',
        'aaaa_record': 'list[str]',
        'cname_record': 'list[str]',
        'dns_cost': 'int',
        'detail': 'str',
        'diagnose_detail': 'DiagnoseDetailForGetTaskResultOutput',
        'record_count': 'int'
    }

    attribute_map = {
        'a_record': 'ARecord',
        'aaaa_record': 'AaaaRecord',
        'cname_record': 'CnameRecord',
        'dns_cost': 'DNSCost',
        'detail': 'Detail',
        'diagnose_detail': 'DiagnoseDetail',
        'record_count': 'RecordCount'
    }

    def __init__(self, a_record=None, aaaa_record=None, cname_record=None, dns_cost=None, detail=None, diagnose_detail=None, record_count=None, _configuration=None):  # noqa: E501
        """DNSDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._a_record = None
        self._aaaa_record = None
        self._cname_record = None
        self._dns_cost = None
        self._detail = None
        self._diagnose_detail = None
        self._record_count = None
        self.discriminator = None

        if a_record is not None:
            self.a_record = a_record
        if aaaa_record is not None:
            self.aaaa_record = aaaa_record
        if cname_record is not None:
            self.cname_record = cname_record
        if dns_cost is not None:
            self.dns_cost = dns_cost
        if detail is not None:
            self.detail = detail
        if diagnose_detail is not None:
            self.diagnose_detail = diagnose_detail
        if record_count is not None:
            self.record_count = record_count

    @property
    def a_record(self):
        """Gets the a_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The a_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._a_record

    @a_record.setter
    def a_record(self, a_record):
        """Sets the a_record of this DNSDetailForGetTaskResultOutput.


        :param a_record: The a_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[str]
        """

        self._a_record = a_record

    @property
    def aaaa_record(self):
        """Gets the aaaa_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The aaaa_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._aaaa_record

    @aaaa_record.setter
    def aaaa_record(self, aaaa_record):
        """Sets the aaaa_record of this DNSDetailForGetTaskResultOutput.


        :param aaaa_record: The aaaa_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[str]
        """

        self._aaaa_record = aaaa_record

    @property
    def cname_record(self):
        """Gets the cname_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The cname_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cname_record

    @cname_record.setter
    def cname_record(self, cname_record):
        """Sets the cname_record of this DNSDetailForGetTaskResultOutput.


        :param cname_record: The cname_record of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[str]
        """

        self._cname_record = cname_record

    @property
    def dns_cost(self):
        """Gets the dns_cost of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The dns_cost of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._dns_cost

    @dns_cost.setter
    def dns_cost(self, dns_cost):
        """Sets the dns_cost of this DNSDetailForGetTaskResultOutput.


        :param dns_cost: The dns_cost of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._dns_cost = dns_cost

    @property
    def detail(self):
        """Gets the detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._detail

    @detail.setter
    def detail(self, detail):
        """Sets the detail of this DNSDetailForGetTaskResultOutput.


        :param detail: The detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._detail = detail

    @property
    def diagnose_detail(self):
        """Gets the diagnose_detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The diagnose_detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: DiagnoseDetailForGetTaskResultOutput
        """
        return self._diagnose_detail

    @diagnose_detail.setter
    def diagnose_detail(self, diagnose_detail):
        """Sets the diagnose_detail of this DNSDetailForGetTaskResultOutput.


        :param diagnose_detail: The diagnose_detail of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: DiagnoseDetailForGetTaskResultOutput
        """

        self._diagnose_detail = diagnose_detail

    @property
    def record_count(self):
        """Gets the record_count of this DNSDetailForGetTaskResultOutput.  # noqa: E501


        :return: The record_count of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._record_count

    @record_count.setter
    def record_count(self, record_count):
        """Sets the record_count of this DNSDetailForGetTaskResultOutput.


        :param record_count: The record_count of this DNSDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._record_count = record_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DNSDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DNSDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DNSDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
