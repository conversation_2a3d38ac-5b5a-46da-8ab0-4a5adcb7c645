# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsumedClientsInfoForDescribeConsumedClientsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_address': 'str',
        'client_id': 'str',
        'diff': 'int',
        'language': 'str',
        'version': 'str'
    }

    attribute_map = {
        'client_address': 'ClientAddress',
        'client_id': 'ClientId',
        'diff': 'Diff',
        'language': 'Language',
        'version': 'Version'
    }

    def __init__(self, client_address=None, client_id=None, diff=None, language=None, version=None, _configuration=None):  # noqa: E501
        """ConsumedClientsInfoForDescribeConsumedClientsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_address = None
        self._client_id = None
        self._diff = None
        self._language = None
        self._version = None
        self.discriminator = None

        if client_address is not None:
            self.client_address = client_address
        if client_id is not None:
            self.client_id = client_id
        if diff is not None:
            self.diff = diff
        if language is not None:
            self.language = language
        if version is not None:
            self.version = version

    @property
    def client_address(self):
        """Gets the client_address of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501


        :return: The client_address of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :rtype: str
        """
        return self._client_address

    @client_address.setter
    def client_address(self, client_address):
        """Sets the client_address of this ConsumedClientsInfoForDescribeConsumedClientsOutput.


        :param client_address: The client_address of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :type: str
        """

        self._client_address = client_address

    @property
    def client_id(self):
        """Gets the client_id of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501


        :return: The client_id of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :rtype: str
        """
        return self._client_id

    @client_id.setter
    def client_id(self, client_id):
        """Sets the client_id of this ConsumedClientsInfoForDescribeConsumedClientsOutput.


        :param client_id: The client_id of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :type: str
        """

        self._client_id = client_id

    @property
    def diff(self):
        """Gets the diff of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501


        :return: The diff of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :rtype: int
        """
        return self._diff

    @diff.setter
    def diff(self, diff):
        """Sets the diff of this ConsumedClientsInfoForDescribeConsumedClientsOutput.


        :param diff: The diff of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :type: int
        """

        self._diff = diff

    @property
    def language(self):
        """Gets the language of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501


        :return: The language of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this ConsumedClientsInfoForDescribeConsumedClientsOutput.


        :param language: The language of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :type: str
        """

        self._language = language

    @property
    def version(self):
        """Gets the version of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501


        :return: The version of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this ConsumedClientsInfoForDescribeConsumedClientsOutput.


        :param version: The version of this ConsumedClientsInfoForDescribeConsumedClientsOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsumedClientsInfoForDescribeConsumedClientsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsumedClientsInfoForDescribeConsumedClientsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsumedClientsInfoForDescribeConsumedClientsOutput):
            return True

        return self.to_dict() != other.to_dict()
