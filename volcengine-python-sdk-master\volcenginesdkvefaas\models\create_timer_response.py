# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTimerResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_time': 'str',
        'crontab': 'str',
        'description': 'str',
        'enable_concurrency': 'bool',
        'enabled': 'bool',
        'function_id': 'str',
        'id': 'str',
        'last_update_time': 'str',
        'name': 'str',
        'payload': 'str',
        'retries': 'int'
    }

    attribute_map = {
        'creation_time': 'CreationTime',
        'crontab': 'Crontab',
        'description': 'Description',
        'enable_concurrency': 'EnableConcurrency',
        'enabled': 'Enabled',
        'function_id': 'FunctionId',
        'id': 'Id',
        'last_update_time': 'LastUpdateTime',
        'name': 'Name',
        'payload': 'Payload',
        'retries': 'Retries'
    }

    def __init__(self, creation_time=None, crontab=None, description=None, enable_concurrency=None, enabled=None, function_id=None, id=None, last_update_time=None, name=None, payload=None, retries=None, _configuration=None):  # noqa: E501
        """CreateTimerResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_time = None
        self._crontab = None
        self._description = None
        self._enable_concurrency = None
        self._enabled = None
        self._function_id = None
        self._id = None
        self._last_update_time = None
        self._name = None
        self._payload = None
        self._retries = None
        self.discriminator = None

        if creation_time is not None:
            self.creation_time = creation_time
        if crontab is not None:
            self.crontab = crontab
        if description is not None:
            self.description = description
        if enable_concurrency is not None:
            self.enable_concurrency = enable_concurrency
        if enabled is not None:
            self.enabled = enabled
        if function_id is not None:
            self.function_id = function_id
        if id is not None:
            self.id = id
        if last_update_time is not None:
            self.last_update_time = last_update_time
        if name is not None:
            self.name = name
        if payload is not None:
            self.payload = payload
        if retries is not None:
            self.retries = retries

    @property
    def creation_time(self):
        """Gets the creation_time of this CreateTimerResponse.  # noqa: E501


        :return: The creation_time of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this CreateTimerResponse.


        :param creation_time: The creation_time of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def crontab(self):
        """Gets the crontab of this CreateTimerResponse.  # noqa: E501


        :return: The crontab of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._crontab

    @crontab.setter
    def crontab(self, crontab):
        """Sets the crontab of this CreateTimerResponse.


        :param crontab: The crontab of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._crontab = crontab

    @property
    def description(self):
        """Gets the description of this CreateTimerResponse.  # noqa: E501


        :return: The description of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTimerResponse.


        :param description: The description of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable_concurrency(self):
        """Gets the enable_concurrency of this CreateTimerResponse.  # noqa: E501


        :return: The enable_concurrency of this CreateTimerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enable_concurrency

    @enable_concurrency.setter
    def enable_concurrency(self, enable_concurrency):
        """Sets the enable_concurrency of this CreateTimerResponse.


        :param enable_concurrency: The enable_concurrency of this CreateTimerResponse.  # noqa: E501
        :type: bool
        """

        self._enable_concurrency = enable_concurrency

    @property
    def enabled(self):
        """Gets the enabled of this CreateTimerResponse.  # noqa: E501


        :return: The enabled of this CreateTimerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this CreateTimerResponse.


        :param enabled: The enabled of this CreateTimerResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def function_id(self):
        """Gets the function_id of this CreateTimerResponse.  # noqa: E501


        :return: The function_id of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this CreateTimerResponse.


        :param function_id: The function_id of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._function_id = function_id

    @property
    def id(self):
        """Gets the id of this CreateTimerResponse.  # noqa: E501


        :return: The id of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CreateTimerResponse.


        :param id: The id of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def last_update_time(self):
        """Gets the last_update_time of this CreateTimerResponse.  # noqa: E501


        :return: The last_update_time of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_update_time

    @last_update_time.setter
    def last_update_time(self, last_update_time):
        """Sets the last_update_time of this CreateTimerResponse.


        :param last_update_time: The last_update_time of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._last_update_time = last_update_time

    @property
    def name(self):
        """Gets the name of this CreateTimerResponse.  # noqa: E501


        :return: The name of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateTimerResponse.


        :param name: The name of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def payload(self):
        """Gets the payload of this CreateTimerResponse.  # noqa: E501


        :return: The payload of this CreateTimerResponse.  # noqa: E501
        :rtype: str
        """
        return self._payload

    @payload.setter
    def payload(self, payload):
        """Sets the payload of this CreateTimerResponse.


        :param payload: The payload of this CreateTimerResponse.  # noqa: E501
        :type: str
        """

        self._payload = payload

    @property
    def retries(self):
        """Gets the retries of this CreateTimerResponse.  # noqa: E501


        :return: The retries of this CreateTimerResponse.  # noqa: E501
        :rtype: int
        """
        return self._retries

    @retries.setter
    def retries(self, retries):
        """Sets the retries of this CreateTimerResponse.


        :param retries: The retries of this CreateTimerResponse.  # noqa: E501
        :type: int
        """

        self._retries = retries

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTimerResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTimerResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTimerResponse):
            return True

        return self.to_dict() != other.to_dict()
