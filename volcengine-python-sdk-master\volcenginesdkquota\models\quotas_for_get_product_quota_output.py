# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotasForGetProductQuotaOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'applicable_type': 'str',
        'applicable_value': 'str',
        'description': 'str',
        'dimensions': 'list[DimensionForGetProductQuotaOutput]',
        'provider_code': 'str',
        'quota_code': 'str',
        'quota_type': 'str',
        'total_quota': 'float',
        'total_usage': 'TotalUsageForGetProductQuotaOutput',
        'trn': 'str'
    }

    attribute_map = {
        'applicable_type': 'ApplicableType',
        'applicable_value': 'ApplicableValue',
        'description': 'Description',
        'dimensions': 'Dimensions',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode',
        'quota_type': 'QuotaType',
        'total_quota': 'TotalQuota',
        'total_usage': 'TotalUsage',
        'trn': 'Trn'
    }

    def __init__(self, applicable_type=None, applicable_value=None, description=None, dimensions=None, provider_code=None, quota_code=None, quota_type=None, total_quota=None, total_usage=None, trn=None, _configuration=None):  # noqa: E501
        """QuotasForGetProductQuotaOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._applicable_type = None
        self._applicable_value = None
        self._description = None
        self._dimensions = None
        self._provider_code = None
        self._quota_code = None
        self._quota_type = None
        self._total_quota = None
        self._total_usage = None
        self._trn = None
        self.discriminator = None

        if applicable_type is not None:
            self.applicable_type = applicable_type
        if applicable_value is not None:
            self.applicable_value = applicable_value
        if description is not None:
            self.description = description
        if dimensions is not None:
            self.dimensions = dimensions
        if provider_code is not None:
            self.provider_code = provider_code
        if quota_code is not None:
            self.quota_code = quota_code
        if quota_type is not None:
            self.quota_type = quota_type
        if total_quota is not None:
            self.total_quota = total_quota
        if total_usage is not None:
            self.total_usage = total_usage
        if trn is not None:
            self.trn = trn

    @property
    def applicable_type(self):
        """Gets the applicable_type of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The applicable_type of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._applicable_type

    @applicable_type.setter
    def applicable_type(self, applicable_type):
        """Sets the applicable_type of this QuotasForGetProductQuotaOutput.


        :param applicable_type: The applicable_type of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._applicable_type = applicable_type

    @property
    def applicable_value(self):
        """Gets the applicable_value of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The applicable_value of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._applicable_value

    @applicable_value.setter
    def applicable_value(self, applicable_value):
        """Sets the applicable_value of this QuotasForGetProductQuotaOutput.


        :param applicable_value: The applicable_value of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._applicable_value = applicable_value

    @property
    def description(self):
        """Gets the description of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The description of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this QuotasForGetProductQuotaOutput.


        :param description: The description of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dimensions(self):
        """Gets the dimensions of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The dimensions of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: list[DimensionForGetProductQuotaOutput]
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this QuotasForGetProductQuotaOutput.


        :param dimensions: The dimensions of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: list[DimensionForGetProductQuotaOutput]
        """

        self._dimensions = dimensions

    @property
    def provider_code(self):
        """Gets the provider_code of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The provider_code of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this QuotasForGetProductQuotaOutput.


        :param provider_code: The provider_code of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The quota_code of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this QuotasForGetProductQuotaOutput.


        :param quota_code: The quota_code of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._quota_code = quota_code

    @property
    def quota_type(self):
        """Gets the quota_type of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The quota_type of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._quota_type

    @quota_type.setter
    def quota_type(self, quota_type):
        """Sets the quota_type of this QuotasForGetProductQuotaOutput.


        :param quota_type: The quota_type of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._quota_type = quota_type

    @property
    def total_quota(self):
        """Gets the total_quota of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The total_quota of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: float
        """
        return self._total_quota

    @total_quota.setter
    def total_quota(self, total_quota):
        """Sets the total_quota of this QuotasForGetProductQuotaOutput.


        :param total_quota: The total_quota of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: float
        """

        self._total_quota = total_quota

    @property
    def total_usage(self):
        """Gets the total_usage of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The total_usage of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: TotalUsageForGetProductQuotaOutput
        """
        return self._total_usage

    @total_usage.setter
    def total_usage(self, total_usage):
        """Sets the total_usage of this QuotasForGetProductQuotaOutput.


        :param total_usage: The total_usage of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: TotalUsageForGetProductQuotaOutput
        """

        self._total_usage = total_usage

    @property
    def trn(self):
        """Gets the trn of this QuotasForGetProductQuotaOutput.  # noqa: E501


        :return: The trn of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :rtype: str
        """
        return self._trn

    @trn.setter
    def trn(self, trn):
        """Sets the trn of this QuotasForGetProductQuotaOutput.


        :param trn: The trn of this QuotasForGetProductQuotaOutput.  # noqa: E501
        :type: str
        """

        self._trn = trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotasForGetProductQuotaOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotasForGetProductQuotaOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotasForGetProductQuotaOutput):
            return True

        return self.to_dict() != other.to_dict()
