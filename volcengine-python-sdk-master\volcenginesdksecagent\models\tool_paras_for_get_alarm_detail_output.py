# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ToolParasForGetAlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'search_key': 'str',
        'type': 'str'
    }

    attribute_map = {
        'id': 'ID',
        'search_key': 'SearchKey',
        'type': 'Type'
    }

    def __init__(self, id=None, search_key=None, type=None, _configuration=None):  # noqa: E501
        """ToolParasForGetAlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._search_key = None
        self._type = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if search_key is not None:
            self.search_key = search_key
        if type is not None:
            self.type = type

    @property
    def id(self):
        """Gets the id of this ToolParasForGetAlarmDetailOutput.  # noqa: E501


        :return: The id of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ToolParasForGetAlarmDetailOutput.


        :param id: The id of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def search_key(self):
        """Gets the search_key of this ToolParasForGetAlarmDetailOutput.  # noqa: E501


        :return: The search_key of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._search_key

    @search_key.setter
    def search_key(self, search_key):
        """Sets the search_key of this ToolParasForGetAlarmDetailOutput.


        :param search_key: The search_key of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._search_key = search_key

    @property
    def type(self):
        """Gets the type of this ToolParasForGetAlarmDetailOutput.  # noqa: E501


        :return: The type of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ToolParasForGetAlarmDetailOutput.


        :param type: The type of this ToolParasForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ToolParasForGetAlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ToolParasForGetAlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ToolParasForGetAlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
