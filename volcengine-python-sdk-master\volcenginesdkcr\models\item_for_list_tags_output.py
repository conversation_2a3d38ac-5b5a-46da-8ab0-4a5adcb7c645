# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListTagsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'chart_attribute': 'ChartAttributeForListTagsOutput',
        'digest': 'str',
        'image_attributes': 'list[ImageAttributeForListTagsOutput]',
        'name': 'str',
        'push_time': 'str',
        'size': 'int',
        'type': 'str'
    }

    attribute_map = {
        'chart_attribute': 'ChartAttribute',
        'digest': 'Digest',
        'image_attributes': 'ImageAttributes',
        'name': 'Name',
        'push_time': 'PushTime',
        'size': 'Size',
        'type': 'Type'
    }

    def __init__(self, chart_attribute=None, digest=None, image_attributes=None, name=None, push_time=None, size=None, type=None, _configuration=None):  # noqa: E501
        """ItemForListTagsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._chart_attribute = None
        self._digest = None
        self._image_attributes = None
        self._name = None
        self._push_time = None
        self._size = None
        self._type = None
        self.discriminator = None

        if chart_attribute is not None:
            self.chart_attribute = chart_attribute
        if digest is not None:
            self.digest = digest
        if image_attributes is not None:
            self.image_attributes = image_attributes
        if name is not None:
            self.name = name
        if push_time is not None:
            self.push_time = push_time
        if size is not None:
            self.size = size
        if type is not None:
            self.type = type

    @property
    def chart_attribute(self):
        """Gets the chart_attribute of this ItemForListTagsOutput.  # noqa: E501


        :return: The chart_attribute of this ItemForListTagsOutput.  # noqa: E501
        :rtype: ChartAttributeForListTagsOutput
        """
        return self._chart_attribute

    @chart_attribute.setter
    def chart_attribute(self, chart_attribute):
        """Sets the chart_attribute of this ItemForListTagsOutput.


        :param chart_attribute: The chart_attribute of this ItemForListTagsOutput.  # noqa: E501
        :type: ChartAttributeForListTagsOutput
        """

        self._chart_attribute = chart_attribute

    @property
    def digest(self):
        """Gets the digest of this ItemForListTagsOutput.  # noqa: E501


        :return: The digest of this ItemForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._digest

    @digest.setter
    def digest(self, digest):
        """Sets the digest of this ItemForListTagsOutput.


        :param digest: The digest of this ItemForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._digest = digest

    @property
    def image_attributes(self):
        """Gets the image_attributes of this ItemForListTagsOutput.  # noqa: E501


        :return: The image_attributes of this ItemForListTagsOutput.  # noqa: E501
        :rtype: list[ImageAttributeForListTagsOutput]
        """
        return self._image_attributes

    @image_attributes.setter
    def image_attributes(self, image_attributes):
        """Sets the image_attributes of this ItemForListTagsOutput.


        :param image_attributes: The image_attributes of this ItemForListTagsOutput.  # noqa: E501
        :type: list[ImageAttributeForListTagsOutput]
        """

        self._image_attributes = image_attributes

    @property
    def name(self):
        """Gets the name of this ItemForListTagsOutput.  # noqa: E501


        :return: The name of this ItemForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListTagsOutput.


        :param name: The name of this ItemForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def push_time(self):
        """Gets the push_time of this ItemForListTagsOutput.  # noqa: E501


        :return: The push_time of this ItemForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._push_time

    @push_time.setter
    def push_time(self, push_time):
        """Sets the push_time of this ItemForListTagsOutput.


        :param push_time: The push_time of this ItemForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._push_time = push_time

    @property
    def size(self):
        """Gets the size of this ItemForListTagsOutput.  # noqa: E501


        :return: The size of this ItemForListTagsOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ItemForListTagsOutput.


        :param size: The size of this ItemForListTagsOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def type(self):
        """Gets the type of this ItemForListTagsOutput.  # noqa: E501


        :return: The type of this ItemForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListTagsOutput.


        :param type: The type of this ItemForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListTagsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListTagsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListTagsOutput):
            return True

        return self.to_dict() != other.to_dict()
