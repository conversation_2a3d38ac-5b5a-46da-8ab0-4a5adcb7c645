# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LabelForListTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'key': 'str',
        'key_id': 'int',
        'option': 'str',
        'option_id': 'int'
    }

    attribute_map = {
        'key': 'Key',
        'key_id': 'KeyID',
        'option': 'Option',
        'option_id': 'OptionID'
    }

    def __init__(self, key=None, key_id=None, option=None, option_id=None, _configuration=None):  # noqa: E501
        """LabelForListTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._key = None
        self._key_id = None
        self._option = None
        self._option_id = None
        self.discriminator = None

        if key is not None:
            self.key = key
        if key_id is not None:
            self.key_id = key_id
        if option is not None:
            self.option = option
        if option_id is not None:
            self.option_id = option_id

    @property
    def key(self):
        """Gets the key of this LabelForListTaskOutput.  # noqa: E501


        :return: The key of this LabelForListTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this LabelForListTaskOutput.


        :param key: The key of this LabelForListTaskOutput.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def key_id(self):
        """Gets the key_id of this LabelForListTaskOutput.  # noqa: E501


        :return: The key_id of this LabelForListTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._key_id

    @key_id.setter
    def key_id(self, key_id):
        """Sets the key_id of this LabelForListTaskOutput.


        :param key_id: The key_id of this LabelForListTaskOutput.  # noqa: E501
        :type: int
        """

        self._key_id = key_id

    @property
    def option(self):
        """Gets the option of this LabelForListTaskOutput.  # noqa: E501


        :return: The option of this LabelForListTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._option

    @option.setter
    def option(self, option):
        """Sets the option of this LabelForListTaskOutput.


        :param option: The option of this LabelForListTaskOutput.  # noqa: E501
        :type: str
        """

        self._option = option

    @property
    def option_id(self):
        """Gets the option_id of this LabelForListTaskOutput.  # noqa: E501


        :return: The option_id of this LabelForListTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._option_id

    @option_id.setter
    def option_id(self, option_id):
        """Sets the option_id of this LabelForListTaskOutput.


        :param option_id: The option_id of this LabelForListTaskOutput.  # noqa: E501
        :type: int
        """

        self._option_id = option_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LabelForListTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LabelForListTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LabelForListTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
