# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopicPermissionForDescribeAccessKeyDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'permission': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'permission': 'Permission',
        'topic_name': 'TopicName'
    }

    def __init__(self, permission=None, topic_name=None, _configuration=None):  # noqa: E501
        """TopicPermissionForDescribeAccessKeyDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._permission = None
        self._topic_name = None
        self.discriminator = None

        if permission is not None:
            self.permission = permission
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def permission(self):
        """Gets the permission of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501


        :return: The permission of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._permission

    @permission.setter
    def permission(self, permission):
        """Sets the permission of this TopicPermissionForDescribeAccessKeyDetailOutput.


        :param permission: The permission of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501
        :type: str
        """

        self._permission = permission

    @property
    def topic_name(self):
        """Gets the topic_name of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501


        :return: The topic_name of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this TopicPermissionForDescribeAccessKeyDetailOutput.


        :param topic_name: The topic_name of this TopicPermissionForDescribeAccessKeyDetailOutput.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopicPermissionForDescribeAccessKeyDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopicPermissionForDescribeAccessKeyDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopicPermissionForDescribeAccessKeyDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
