# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_layout': 'str',
        'flash_capacity_max': 'int',
        'flash_capacity_min': 'int',
        'flash_capacity_step': 'int',
        'ram_capacity': 'int',
        'shard_number_specs': 'list[ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput]'
    }

    attribute_map = {
        'data_layout': 'DataLayout',
        'flash_capacity_max': 'FlashCapacityMax',
        'flash_capacity_min': 'FlashCapacityMin',
        'flash_capacity_step': 'FlashCapacityStep',
        'ram_capacity': 'RamCapacity',
        'shard_number_specs': 'ShardNumberSpecs'
    }

    def __init__(self, data_layout=None, flash_capacity_max=None, flash_capacity_min=None, flash_capacity_step=None, ram_capacity=None, shard_number_specs=None, _configuration=None):  # noqa: E501
        """InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_layout = None
        self._flash_capacity_max = None
        self._flash_capacity_min = None
        self._flash_capacity_step = None
        self._ram_capacity = None
        self._shard_number_specs = None
        self.discriminator = None

        if data_layout is not None:
            self.data_layout = data_layout
        if flash_capacity_max is not None:
            self.flash_capacity_max = flash_capacity_max
        if flash_capacity_min is not None:
            self.flash_capacity_min = flash_capacity_min
        if flash_capacity_step is not None:
            self.flash_capacity_step = flash_capacity_step
        if ram_capacity is not None:
            self.ram_capacity = ram_capacity
        if shard_number_specs is not None:
            self.shard_number_specs = shard_number_specs

    @property
    def data_layout(self):
        """Gets the data_layout of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The data_layout of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_layout

    @data_layout.setter
    def data_layout(self, data_layout):
        """Sets the data_layout of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param data_layout: The data_layout of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._data_layout = data_layout

    @property
    def flash_capacity_max(self):
        """Gets the flash_capacity_max of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The flash_capacity_max of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._flash_capacity_max

    @flash_capacity_max.setter
    def flash_capacity_max(self, flash_capacity_max):
        """Sets the flash_capacity_max of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param flash_capacity_max: The flash_capacity_max of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._flash_capacity_max = flash_capacity_max

    @property
    def flash_capacity_min(self):
        """Gets the flash_capacity_min of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The flash_capacity_min of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._flash_capacity_min

    @flash_capacity_min.setter
    def flash_capacity_min(self, flash_capacity_min):
        """Sets the flash_capacity_min of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param flash_capacity_min: The flash_capacity_min of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._flash_capacity_min = flash_capacity_min

    @property
    def flash_capacity_step(self):
        """Gets the flash_capacity_step of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The flash_capacity_step of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._flash_capacity_step

    @flash_capacity_step.setter
    def flash_capacity_step(self, flash_capacity_step):
        """Sets the flash_capacity_step of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param flash_capacity_step: The flash_capacity_step of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._flash_capacity_step = flash_capacity_step

    @property
    def ram_capacity(self):
        """Gets the ram_capacity of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The ram_capacity of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._ram_capacity

    @ram_capacity.setter
    def ram_capacity(self, ram_capacity):
        """Sets the ram_capacity of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param ram_capacity: The ram_capacity of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._ram_capacity = ram_capacity

    @property
    def shard_number_specs(self):
        """Gets the shard_number_specs of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The shard_number_specs of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: list[ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput]
        """
        return self._shard_number_specs

    @shard_number_specs.setter
    def shard_number_specs(self, shard_number_specs):
        """Sets the shard_number_specs of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param shard_number_specs: The shard_number_specs of this InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: list[ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput]
        """

        self._shard_number_specs = shard_number_specs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceSpecForDescribeEnterpriseDBInstanceSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
