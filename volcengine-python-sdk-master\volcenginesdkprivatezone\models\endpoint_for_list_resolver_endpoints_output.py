# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointForListResolverEndpointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'direction': 'str',
        'id': 'int',
        'ip_configs': 'list[IpConfigForListResolverEndpointsOutput]',
        'name': 'str',
        'project_name': 'str',
        'security_group_id': 'str',
        'status': 'str',
        'tags': 'list[TagForListResolverEndpointsOutput]',
        'updated_at': 'str',
        'vpc_id': 'str',
        'vpc_region': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'direction': 'Direction',
        'id': 'ID',
        'ip_configs': 'IpConfigs',
        'name': 'Name',
        'project_name': 'ProjectName',
        'security_group_id': 'SecurityGroupID',
        'status': 'Status',
        'tags': 'Tags',
        'updated_at': 'UpdatedAt',
        'vpc_id': 'VpcID',
        'vpc_region': 'VpcRegion'
    }

    def __init__(self, created_at=None, direction=None, id=None, ip_configs=None, name=None, project_name=None, security_group_id=None, status=None, tags=None, updated_at=None, vpc_id=None, vpc_region=None, _configuration=None):  # noqa: E501
        """EndpointForListResolverEndpointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._direction = None
        self._id = None
        self._ip_configs = None
        self._name = None
        self._project_name = None
        self._security_group_id = None
        self._status = None
        self._tags = None
        self._updated_at = None
        self._vpc_id = None
        self._vpc_region = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if direction is not None:
            self.direction = direction
        if id is not None:
            self.id = id
        if ip_configs is not None:
            self.ip_configs = ip_configs
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if security_group_id is not None:
            self.security_group_id = security_group_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if updated_at is not None:
            self.updated_at = updated_at
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_region is not None:
            self.vpc_region = vpc_region

    @property
    def created_at(self):
        """Gets the created_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The created_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this EndpointForListResolverEndpointsOutput.


        :param created_at: The created_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def direction(self):
        """Gets the direction of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The direction of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this EndpointForListResolverEndpointsOutput.


        :param direction: The direction of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._direction = direction

    @property
    def id(self):
        """Gets the id of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this EndpointForListResolverEndpointsOutput.


        :param id: The id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def ip_configs(self):
        """Gets the ip_configs of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The ip_configs of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: list[IpConfigForListResolverEndpointsOutput]
        """
        return self._ip_configs

    @ip_configs.setter
    def ip_configs(self, ip_configs):
        """Sets the ip_configs of this EndpointForListResolverEndpointsOutput.


        :param ip_configs: The ip_configs of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: list[IpConfigForListResolverEndpointsOutput]
        """

        self._ip_configs = ip_configs

    @property
    def name(self):
        """Gets the name of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The name of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this EndpointForListResolverEndpointsOutput.


        :param name: The name of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The project_name of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this EndpointForListResolverEndpointsOutput.


        :param project_name: The project_name of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def security_group_id(self):
        """Gets the security_group_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The security_group_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this EndpointForListResolverEndpointsOutput.


        :param security_group_id: The security_group_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    @property
    def status(self):
        """Gets the status of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The status of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this EndpointForListResolverEndpointsOutput.


        :param status: The status of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The tags of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: list[TagForListResolverEndpointsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this EndpointForListResolverEndpointsOutput.


        :param tags: The tags of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: list[TagForListResolverEndpointsOutput]
        """

        self._tags = tags

    @property
    def updated_at(self):
        """Gets the updated_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The updated_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this EndpointForListResolverEndpointsOutput.


        :param updated_at: The updated_at of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def vpc_id(self):
        """Gets the vpc_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The vpc_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this EndpointForListResolverEndpointsOutput.


        :param vpc_id: The vpc_id of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_region(self):
        """Gets the vpc_region of this EndpointForListResolverEndpointsOutput.  # noqa: E501


        :return: The vpc_region of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_region

    @vpc_region.setter
    def vpc_region(self, vpc_region):
        """Sets the vpc_region of this EndpointForListResolverEndpointsOutput.


        :param vpc_region: The vpc_region of this EndpointForListResolverEndpointsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_region = vpc_region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointForListResolverEndpointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointForListResolverEndpointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointForListResolverEndpointsOutput):
            return True

        return self.to_dict() != other.to_dict()
