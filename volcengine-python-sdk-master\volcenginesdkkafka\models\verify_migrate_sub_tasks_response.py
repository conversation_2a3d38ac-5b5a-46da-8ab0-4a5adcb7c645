# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VerifyMigrateSubTasksResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sub_task_results': 'list[SubTaskResultForVerifyMigrateSubTasksOutput]',
        'summary': 'SummaryForVerifyMigrateSubTasksOutput'
    }

    attribute_map = {
        'sub_task_results': 'SubTaskResults',
        'summary': 'Summary'
    }

    def __init__(self, sub_task_results=None, summary=None, _configuration=None):  # noqa: E501
        """VerifyMigrateSubTasksResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sub_task_results = None
        self._summary = None
        self.discriminator = None

        if sub_task_results is not None:
            self.sub_task_results = sub_task_results
        if summary is not None:
            self.summary = summary

    @property
    def sub_task_results(self):
        """Gets the sub_task_results of this VerifyMigrateSubTasksResponse.  # noqa: E501


        :return: The sub_task_results of this VerifyMigrateSubTasksResponse.  # noqa: E501
        :rtype: list[SubTaskResultForVerifyMigrateSubTasksOutput]
        """
        return self._sub_task_results

    @sub_task_results.setter
    def sub_task_results(self, sub_task_results):
        """Sets the sub_task_results of this VerifyMigrateSubTasksResponse.


        :param sub_task_results: The sub_task_results of this VerifyMigrateSubTasksResponse.  # noqa: E501
        :type: list[SubTaskResultForVerifyMigrateSubTasksOutput]
        """

        self._sub_task_results = sub_task_results

    @property
    def summary(self):
        """Gets the summary of this VerifyMigrateSubTasksResponse.  # noqa: E501


        :return: The summary of this VerifyMigrateSubTasksResponse.  # noqa: E501
        :rtype: SummaryForVerifyMigrateSubTasksOutput
        """
        return self._summary

    @summary.setter
    def summary(self, summary):
        """Sets the summary of this VerifyMigrateSubTasksResponse.


        :param summary: The summary of this VerifyMigrateSubTasksResponse.  # noqa: E501
        :type: SummaryForVerifyMigrateSubTasksOutput
        """

        self._summary = summary

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VerifyMigrateSubTasksResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VerifyMigrateSubTasksResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VerifyMigrateSubTasksResponse):
            return True

        return self.to_dict() != other.to_dict()
