# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBSystemSecurityPoliciesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'next_token': 'str',
        'security_policy_ids': 'list[str]'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'security_policy_ids': 'SecurityPolicyIds'
    }

    def __init__(self, max_results=None, next_token=None, security_policy_ids=None, _configuration=None):  # noqa: E501
        """DescribeNLBSystemSecurityPoliciesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._security_policy_ids = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if security_policy_ids is not None:
            self.security_policy_ids = security_policy_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501


        :return: The max_results of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeNLBSystemSecurityPoliciesRequest.


        :param max_results: The max_results of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501


        :return: The next_token of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeNLBSystemSecurityPoliciesRequest.


        :param next_token: The next_token of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def security_policy_ids(self):
        """Gets the security_policy_ids of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501


        :return: The security_policy_ids of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_policy_ids

    @security_policy_ids.setter
    def security_policy_ids(self, security_policy_ids):
        """Sets the security_policy_ids of this DescribeNLBSystemSecurityPoliciesRequest.


        :param security_policy_ids: The security_policy_ids of this DescribeNLBSystemSecurityPoliciesRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_policy_ids = security_policy_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBSystemSecurityPoliciesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBSystemSecurityPoliciesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBSystemSecurityPoliciesRequest):
            return True

        return self.to_dict() != other.to_dict()
