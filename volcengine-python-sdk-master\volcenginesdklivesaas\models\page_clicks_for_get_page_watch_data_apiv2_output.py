# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PageClicksForGetPageWatchDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'avg_number': 'float',
        'avg_time': 'float',
        'pcu': 'int',
        'pv': 'int',
        'total_time': 'int',
        'uv': 'int'
    }

    attribute_map = {
        'avg_number': 'AvgNumber',
        'avg_time': 'AvgTime',
        'pcu': 'PCU',
        'pv': 'PV',
        'total_time': 'TotalTime',
        'uv': 'UV'
    }

    def __init__(self, avg_number=None, avg_time=None, pcu=None, pv=None, total_time=None, uv=None, _configuration=None):  # noqa: E501
        """PageClicksForGetPageWatchDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._avg_number = None
        self._avg_time = None
        self._pcu = None
        self._pv = None
        self._total_time = None
        self._uv = None
        self.discriminator = None

        if avg_number is not None:
            self.avg_number = avg_number
        if avg_time is not None:
            self.avg_time = avg_time
        if pcu is not None:
            self.pcu = pcu
        if pv is not None:
            self.pv = pv
        if total_time is not None:
            self.total_time = total_time
        if uv is not None:
            self.uv = uv

    @property
    def avg_number(self):
        """Gets the avg_number of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The avg_number of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: float
        """
        return self._avg_number

    @avg_number.setter
    def avg_number(self, avg_number):
        """Sets the avg_number of this PageClicksForGetPageWatchDataAPIV2Output.


        :param avg_number: The avg_number of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: float
        """

        self._avg_number = avg_number

    @property
    def avg_time(self):
        """Gets the avg_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The avg_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: float
        """
        return self._avg_time

    @avg_time.setter
    def avg_time(self, avg_time):
        """Sets the avg_time of this PageClicksForGetPageWatchDataAPIV2Output.


        :param avg_time: The avg_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: float
        """

        self._avg_time = avg_time

    @property
    def pcu(self):
        """Gets the pcu of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The pcu of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._pcu

    @pcu.setter
    def pcu(self, pcu):
        """Sets the pcu of this PageClicksForGetPageWatchDataAPIV2Output.


        :param pcu: The pcu of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._pcu = pcu

    @property
    def pv(self):
        """Gets the pv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The pv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._pv

    @pv.setter
    def pv(self, pv):
        """Sets the pv of this PageClicksForGetPageWatchDataAPIV2Output.


        :param pv: The pv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._pv = pv

    @property
    def total_time(self):
        """Gets the total_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The total_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._total_time

    @total_time.setter
    def total_time(self, total_time):
        """Sets the total_time of this PageClicksForGetPageWatchDataAPIV2Output.


        :param total_time: The total_time of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._total_time = total_time

    @property
    def uv(self):
        """Gets the uv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501


        :return: The uv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._uv

    @uv.setter
    def uv(self, uv):
        """Sets the uv of this PageClicksForGetPageWatchDataAPIV2Output.


        :param uv: The uv of this PageClicksForGetPageWatchDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._uv = uv

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PageClicksForGetPageWatchDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PageClicksForGetPageWatchDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PageClicksForGetPageWatchDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
