# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeScalingPoliciesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'scaling_group_id': 'str',
        'scaling_policy_ids': 'list[str]',
        'scaling_policy_names': 'list[str]',
        'scaling_policy_type': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'scaling_group_id': 'ScalingGroupId',
        'scaling_policy_ids': 'ScalingPolicyIds',
        'scaling_policy_names': 'ScalingPolicyNames',
        'scaling_policy_type': 'ScalingPolicyType'
    }

    def __init__(self, page_number=None, page_size=None, scaling_group_id=None, scaling_policy_ids=None, scaling_policy_names=None, scaling_policy_type=None, _configuration=None):  # noqa: E501
        """DescribeScalingPoliciesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._scaling_group_id = None
        self._scaling_policy_ids = None
        self._scaling_policy_names = None
        self._scaling_policy_type = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        self.scaling_group_id = scaling_group_id
        if scaling_policy_ids is not None:
            self.scaling_policy_ids = scaling_policy_ids
        if scaling_policy_names is not None:
            self.scaling_policy_names = scaling_policy_names
        if scaling_policy_type is not None:
            self.scaling_policy_type = scaling_policy_type

    @property
    def page_number(self):
        """Gets the page_number of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The page_number of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeScalingPoliciesRequest.


        :param page_number: The page_number of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The page_size of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeScalingPoliciesRequest.


        :param page_size: The page_size of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The scaling_group_id of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this DescribeScalingPoliciesRequest.


        :param scaling_group_id: The scaling_group_id of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scaling_group_id is None:
            raise ValueError("Invalid value for `scaling_group_id`, must not be `None`")  # noqa: E501

        self._scaling_group_id = scaling_group_id

    @property
    def scaling_policy_ids(self):
        """Gets the scaling_policy_ids of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The scaling_policy_ids of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._scaling_policy_ids

    @scaling_policy_ids.setter
    def scaling_policy_ids(self, scaling_policy_ids):
        """Sets the scaling_policy_ids of this DescribeScalingPoliciesRequest.


        :param scaling_policy_ids: The scaling_policy_ids of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: list[str]
        """

        self._scaling_policy_ids = scaling_policy_ids

    @property
    def scaling_policy_names(self):
        """Gets the scaling_policy_names of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The scaling_policy_names of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._scaling_policy_names

    @scaling_policy_names.setter
    def scaling_policy_names(self, scaling_policy_names):
        """Sets the scaling_policy_names of this DescribeScalingPoliciesRequest.


        :param scaling_policy_names: The scaling_policy_names of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: list[str]
        """

        self._scaling_policy_names = scaling_policy_names

    @property
    def scaling_policy_type(self):
        """Gets the scaling_policy_type of this DescribeScalingPoliciesRequest.  # noqa: E501


        :return: The scaling_policy_type of this DescribeScalingPoliciesRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_policy_type

    @scaling_policy_type.setter
    def scaling_policy_type(self, scaling_policy_type):
        """Sets the scaling_policy_type of this DescribeScalingPoliciesRequest.


        :param scaling_policy_type: The scaling_policy_type of this DescribeScalingPoliciesRequest.  # noqa: E501
        :type: str
        """

        self._scaling_policy_type = scaling_policy_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeScalingPoliciesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeScalingPoliciesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeScalingPoliciesRequest):
            return True

        return self.to_dict() != other.to_dict()
