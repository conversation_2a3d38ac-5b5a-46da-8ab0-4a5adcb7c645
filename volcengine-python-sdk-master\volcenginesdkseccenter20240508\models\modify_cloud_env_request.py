# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyCloudEnvRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comment': 'str',
        'id': 'str',
        'sync_period': 'int'
    }

    attribute_map = {
        'comment': 'Comment',
        'id': 'ID',
        'sync_period': 'SyncPeriod'
    }

    def __init__(self, comment=None, id=None, sync_period=None, _configuration=None):  # noqa: E501
        """ModifyCloudEnvRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comment = None
        self._id = None
        self._sync_period = None
        self.discriminator = None

        if comment is not None:
            self.comment = comment
        self.id = id
        if sync_period is not None:
            self.sync_period = sync_period

    @property
    def comment(self):
        """Gets the comment of this ModifyCloudEnvRequest.  # noqa: E501


        :return: The comment of this ModifyCloudEnvRequest.  # noqa: E501
        :rtype: str
        """
        return self._comment

    @comment.setter
    def comment(self, comment):
        """Sets the comment of this ModifyCloudEnvRequest.


        :param comment: The comment of this ModifyCloudEnvRequest.  # noqa: E501
        :type: str
        """

        self._comment = comment

    @property
    def id(self):
        """Gets the id of this ModifyCloudEnvRequest.  # noqa: E501


        :return: The id of this ModifyCloudEnvRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ModifyCloudEnvRequest.


        :param id: The id of this ModifyCloudEnvRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def sync_period(self):
        """Gets the sync_period of this ModifyCloudEnvRequest.  # noqa: E501


        :return: The sync_period of this ModifyCloudEnvRequest.  # noqa: E501
        :rtype: int
        """
        return self._sync_period

    @sync_period.setter
    def sync_period(self, sync_period):
        """Sets the sync_period of this ModifyCloudEnvRequest.


        :param sync_period: The sync_period of this ModifyCloudEnvRequest.  # noqa: E501
        :type: int
        """

        self._sync_period = sync_period

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyCloudEnvRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyCloudEnvRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyCloudEnvRequest):
            return True

        return self.to_dict() != other.to_dict()
