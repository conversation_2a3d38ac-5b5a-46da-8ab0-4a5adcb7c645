# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OperateAgentRaspConfigSwitchResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'reason': 'str',
        'success': 'bool'
    }

    attribute_map = {
        'reason': 'Reason',
        'success': 'Success'
    }

    def __init__(self, reason=None, success=None, _configuration=None):  # noqa: E501
        """OperateAgentRaspConfigSwitchResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._reason = None
        self._success = None
        self.discriminator = None

        if reason is not None:
            self.reason = reason
        if success is not None:
            self.success = success

    @property
    def reason(self):
        """Gets the reason of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501


        :return: The reason of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this OperateAgentRaspConfigSwitchResponse.


        :param reason: The reason of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def success(self):
        """Gets the success of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501


        :return: The success of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501
        :rtype: bool
        """
        return self._success

    @success.setter
    def success(self, success):
        """Sets the success of this OperateAgentRaspConfigSwitchResponse.


        :param success: The success of this OperateAgentRaspConfigSwitchResponse.  # noqa: E501
        :type: bool
        """

        self._success = success

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OperateAgentRaspConfigSwitchResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OperateAgentRaspConfigSwitchResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OperateAgentRaspConfigSwitchResponse):
            return True

        return self.to_dict() != other.to_dict()
