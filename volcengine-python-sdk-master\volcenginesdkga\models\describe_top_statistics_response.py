# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTopStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sort_metric': 'str',
        'top_statistics': 'list[TopStatisticForDescribeTopStatisticsOutput]'
    }

    attribute_map = {
        'sort_metric': 'SortMetric',
        'top_statistics': 'TopStatistics'
    }

    def __init__(self, sort_metric=None, top_statistics=None, _configuration=None):  # noqa: E501
        """DescribeTopStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sort_metric = None
        self._top_statistics = None
        self.discriminator = None

        if sort_metric is not None:
            self.sort_metric = sort_metric
        if top_statistics is not None:
            self.top_statistics = top_statistics

    @property
    def sort_metric(self):
        """Gets the sort_metric of this DescribeTopStatisticsResponse.  # noqa: E501


        :return: The sort_metric of this DescribeTopStatisticsResponse.  # noqa: E501
        :rtype: str
        """
        return self._sort_metric

    @sort_metric.setter
    def sort_metric(self, sort_metric):
        """Sets the sort_metric of this DescribeTopStatisticsResponse.


        :param sort_metric: The sort_metric of this DescribeTopStatisticsResponse.  # noqa: E501
        :type: str
        """

        self._sort_metric = sort_metric

    @property
    def top_statistics(self):
        """Gets the top_statistics of this DescribeTopStatisticsResponse.  # noqa: E501


        :return: The top_statistics of this DescribeTopStatisticsResponse.  # noqa: E501
        :rtype: list[TopStatisticForDescribeTopStatisticsOutput]
        """
        return self._top_statistics

    @top_statistics.setter
    def top_statistics(self, top_statistics):
        """Sets the top_statistics of this DescribeTopStatisticsResponse.


        :param top_statistics: The top_statistics of this DescribeTopStatisticsResponse.  # noqa: E501
        :type: list[TopStatisticForDescribeTopStatisticsOutput]
        """

        self._top_statistics = top_statistics

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTopStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTopStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTopStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
