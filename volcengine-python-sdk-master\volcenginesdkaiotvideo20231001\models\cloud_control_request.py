# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CloudControlRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'cmd': 'str',
        'para': 'int',
        'stream_id': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'cmd': 'Cmd',
        'para': 'Para',
        'stream_id': 'StreamID'
    }

    def __init__(self, action=None, cmd=None, para=None, stream_id=None, _configuration=None):  # noqa: E501
        """CloudControlRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._cmd = None
        self._para = None
        self._stream_id = None
        self.discriminator = None

        self.action = action
        self.cmd = cmd
        self.para = para
        self.stream_id = stream_id

    @property
    def action(self):
        """Gets the action of this CloudControlRequest.  # noqa: E501


        :return: The action of this CloudControlRequest.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this CloudControlRequest.


        :param action: The action of this CloudControlRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and action is None:
            raise ValueError("Invalid value for `action`, must not be `None`")  # noqa: E501

        self._action = action

    @property
    def cmd(self):
        """Gets the cmd of this CloudControlRequest.  # noqa: E501


        :return: The cmd of this CloudControlRequest.  # noqa: E501
        :rtype: str
        """
        return self._cmd

    @cmd.setter
    def cmd(self, cmd):
        """Sets the cmd of this CloudControlRequest.


        :param cmd: The cmd of this CloudControlRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cmd is None:
            raise ValueError("Invalid value for `cmd`, must not be `None`")  # noqa: E501

        self._cmd = cmd

    @property
    def para(self):
        """Gets the para of this CloudControlRequest.  # noqa: E501


        :return: The para of this CloudControlRequest.  # noqa: E501
        :rtype: int
        """
        return self._para

    @para.setter
    def para(self, para):
        """Sets the para of this CloudControlRequest.


        :param para: The para of this CloudControlRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and para is None:
            raise ValueError("Invalid value for `para`, must not be `None`")  # noqa: E501

        self._para = para

    @property
    def stream_id(self):
        """Gets the stream_id of this CloudControlRequest.  # noqa: E501


        :return: The stream_id of this CloudControlRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this CloudControlRequest.


        :param stream_id: The stream_id of this CloudControlRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_id is None:
            raise ValueError("Invalid value for `stream_id`, must not be `None`")  # noqa: E501

        self._stream_id = stream_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CloudControlRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CloudControlRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CloudControlRequest):
            return True

        return self.to_dict() != other.to_dict()
