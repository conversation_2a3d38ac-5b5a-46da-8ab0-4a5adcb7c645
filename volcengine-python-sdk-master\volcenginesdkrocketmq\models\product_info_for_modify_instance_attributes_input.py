# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProductInfoForModifyInstanceAttributesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'send_receive_ratio': 'int'
    }

    attribute_map = {
        'send_receive_ratio': 'SendReceiveRatio'
    }

    def __init__(self, send_receive_ratio=None, _configuration=None):  # noqa: E501
        """ProductInfoForModifyInstanceAttributesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._send_receive_ratio = None
        self.discriminator = None

        if send_receive_ratio is not None:
            self.send_receive_ratio = send_receive_ratio

    @property
    def send_receive_ratio(self):
        """Gets the send_receive_ratio of this ProductInfoForModifyInstanceAttributesInput.  # noqa: E501


        :return: The send_receive_ratio of this ProductInfoForModifyInstanceAttributesInput.  # noqa: E501
        :rtype: int
        """
        return self._send_receive_ratio

    @send_receive_ratio.setter
    def send_receive_ratio(self, send_receive_ratio):
        """Sets the send_receive_ratio of this ProductInfoForModifyInstanceAttributesInput.


        :param send_receive_ratio: The send_receive_ratio of this ProductInfoForModifyInstanceAttributesInput.  # noqa: E501
        :type: int
        """

        self._send_receive_ratio = send_receive_ratio

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProductInfoForModifyInstanceAttributesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProductInfoForModifyInstanceAttributesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProductInfoForModifyInstanceAttributesInput):
            return True

        return self.to_dict() != other.to_dict()
