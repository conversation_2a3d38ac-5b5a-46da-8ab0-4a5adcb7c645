# coding: utf-8

"""
    sqs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetQueueAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'message_retention_period': 'int',
        'queue_display_name': 'str',
        'queue_trn': 'str',
        'visibility_timeout': 'int'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'message_retention_period': 'MessageRetentionPeriod',
        'queue_display_name': 'QueueDisplayName',
        'queue_trn': 'QueueTrn',
        'visibility_timeout': 'VisibilityTimeout'
    }

    def __init__(self, created_at=None, message_retention_period=None, queue_display_name=None, queue_trn=None, visibility_timeout=None, _configuration=None):  # noqa: E501
        """GetQueueAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._message_retention_period = None
        self._queue_display_name = None
        self._queue_trn = None
        self._visibility_timeout = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if message_retention_period is not None:
            self.message_retention_period = message_retention_period
        if queue_display_name is not None:
            self.queue_display_name = queue_display_name
        if queue_trn is not None:
            self.queue_trn = queue_trn
        if visibility_timeout is not None:
            self.visibility_timeout = visibility_timeout

    @property
    def created_at(self):
        """Gets the created_at of this GetQueueAttributesResponse.  # noqa: E501


        :return: The created_at of this GetQueueAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this GetQueueAttributesResponse.


        :param created_at: The created_at of this GetQueueAttributesResponse.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def message_retention_period(self):
        """Gets the message_retention_period of this GetQueueAttributesResponse.  # noqa: E501


        :return: The message_retention_period of this GetQueueAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._message_retention_period

    @message_retention_period.setter
    def message_retention_period(self, message_retention_period):
        """Sets the message_retention_period of this GetQueueAttributesResponse.


        :param message_retention_period: The message_retention_period of this GetQueueAttributesResponse.  # noqa: E501
        :type: int
        """

        self._message_retention_period = message_retention_period

    @property
    def queue_display_name(self):
        """Gets the queue_display_name of this GetQueueAttributesResponse.  # noqa: E501


        :return: The queue_display_name of this GetQueueAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._queue_display_name

    @queue_display_name.setter
    def queue_display_name(self, queue_display_name):
        """Sets the queue_display_name of this GetQueueAttributesResponse.


        :param queue_display_name: The queue_display_name of this GetQueueAttributesResponse.  # noqa: E501
        :type: str
        """

        self._queue_display_name = queue_display_name

    @property
    def queue_trn(self):
        """Gets the queue_trn of this GetQueueAttributesResponse.  # noqa: E501


        :return: The queue_trn of this GetQueueAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._queue_trn

    @queue_trn.setter
    def queue_trn(self, queue_trn):
        """Sets the queue_trn of this GetQueueAttributesResponse.


        :param queue_trn: The queue_trn of this GetQueueAttributesResponse.  # noqa: E501
        :type: str
        """

        self._queue_trn = queue_trn

    @property
    def visibility_timeout(self):
        """Gets the visibility_timeout of this GetQueueAttributesResponse.  # noqa: E501


        :return: The visibility_timeout of this GetQueueAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._visibility_timeout

    @visibility_timeout.setter
    def visibility_timeout(self, visibility_timeout):
        """Sets the visibility_timeout of this GetQueueAttributesResponse.


        :param visibility_timeout: The visibility_timeout of this GetQueueAttributesResponse.  # noqa: E501
        :type: int
        """

        self._visibility_timeout = visibility_timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetQueueAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetQueueAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetQueueAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
