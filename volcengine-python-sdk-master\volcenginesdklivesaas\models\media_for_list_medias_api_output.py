# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MediaForListMediasAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_image': 'str',
        'create_time': 'int',
        'duration': 'int',
        'media_id': 'str',
        'name': 'str',
        'online_status': 'int',
        'source_type': 'int',
        'vid': 'str'
    }

    attribute_map = {
        'cover_image': 'CoverImage',
        'create_time': 'CreateTime',
        'duration': 'Duration',
        'media_id': 'MediaId',
        'name': 'Name',
        'online_status': 'OnlineStatus',
        'source_type': 'SourceType',
        'vid': 'Vid'
    }

    def __init__(self, cover_image=None, create_time=None, duration=None, media_id=None, name=None, online_status=None, source_type=None, vid=None, _configuration=None):  # noqa: E501
        """MediaForListMediasAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_image = None
        self._create_time = None
        self._duration = None
        self._media_id = None
        self._name = None
        self._online_status = None
        self._source_type = None
        self._vid = None
        self.discriminator = None

        if cover_image is not None:
            self.cover_image = cover_image
        if create_time is not None:
            self.create_time = create_time
        if duration is not None:
            self.duration = duration
        if media_id is not None:
            self.media_id = media_id
        if name is not None:
            self.name = name
        if online_status is not None:
            self.online_status = online_status
        if source_type is not None:
            self.source_type = source_type
        if vid is not None:
            self.vid = vid

    @property
    def cover_image(self):
        """Gets the cover_image of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The cover_image of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this MediaForListMediasAPIOutput.


        :param cover_image: The cover_image of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def create_time(self):
        """Gets the create_time of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The create_time of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this MediaForListMediasAPIOutput.


        :param create_time: The create_time of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def duration(self):
        """Gets the duration of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The duration of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this MediaForListMediasAPIOutput.


        :param duration: The duration of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def media_id(self):
        """Gets the media_id of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The media_id of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._media_id

    @media_id.setter
    def media_id(self, media_id):
        """Sets the media_id of this MediaForListMediasAPIOutput.


        :param media_id: The media_id of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: str
        """

        self._media_id = media_id

    @property
    def name(self):
        """Gets the name of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The name of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this MediaForListMediasAPIOutput.


        :param name: The name of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def online_status(self):
        """Gets the online_status of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The online_status of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._online_status

    @online_status.setter
    def online_status(self, online_status):
        """Sets the online_status of this MediaForListMediasAPIOutput.


        :param online_status: The online_status of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: int
        """

        self._online_status = online_status

    @property
    def source_type(self):
        """Gets the source_type of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The source_type of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this MediaForListMediasAPIOutput.


        :param source_type: The source_type of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: int
        """

        self._source_type = source_type

    @property
    def vid(self):
        """Gets the vid of this MediaForListMediasAPIOutput.  # noqa: E501


        :return: The vid of this MediaForListMediasAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this MediaForListMediasAPIOutput.


        :param vid: The vid of this MediaForListMediasAPIOutput.  # noqa: E501
        :type: str
        """

        self._vid = vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MediaForListMediasAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MediaForListMediasAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MediaForListMediasAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
