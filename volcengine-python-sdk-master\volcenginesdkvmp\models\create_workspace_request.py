# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateWorkspaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'instance_type_id': 'str',
        'name': 'str',
        'password': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateWorkspaceInput]',
        'username': 'str'
    }

    attribute_map = {
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'instance_type_id': 'InstanceTypeId',
        'name': 'Name',
        'password': 'Password',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'username': 'Username'
    }

    def __init__(self, delete_protection_enabled=None, description=None, instance_type_id=None, name=None, password=None, project_name=None, tags=None, username=None, _configuration=None):  # noqa: E501
        """CreateWorkspaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_protection_enabled = None
        self._description = None
        self._instance_type_id = None
        self._name = None
        self._password = None
        self._project_name = None
        self._tags = None
        self._username = None
        self.discriminator = None

        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        self.instance_type_id = instance_type_id
        self.name = name
        if password is not None:
            self.password = password
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if username is not None:
            self.username = username

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this CreateWorkspaceRequest.  # noqa: E501


        :return: The delete_protection_enabled of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this CreateWorkspaceRequest.


        :param delete_protection_enabled: The delete_protection_enabled of this CreateWorkspaceRequest.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this CreateWorkspaceRequest.  # noqa: E501


        :return: The description of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateWorkspaceRequest.


        :param description: The description of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this CreateWorkspaceRequest.  # noqa: E501


        :return: The instance_type_id of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this CreateWorkspaceRequest.


        :param instance_type_id: The instance_type_id of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_type_id is None:
            raise ValueError("Invalid value for `instance_type_id`, must not be `None`")  # noqa: E501

        self._instance_type_id = instance_type_id

    @property
    def name(self):
        """Gets the name of this CreateWorkspaceRequest.  # noqa: E501


        :return: The name of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateWorkspaceRequest.


        :param name: The name of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def password(self):
        """Gets the password of this CreateWorkspaceRequest.  # noqa: E501


        :return: The password of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this CreateWorkspaceRequest.


        :param password: The password of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def project_name(self):
        """Gets the project_name of this CreateWorkspaceRequest.  # noqa: E501


        :return: The project_name of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateWorkspaceRequest.


        :param project_name: The project_name of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateWorkspaceRequest.  # noqa: E501


        :return: The tags of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: list[TagForCreateWorkspaceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateWorkspaceRequest.


        :param tags: The tags of this CreateWorkspaceRequest.  # noqa: E501
        :type: list[TagForCreateWorkspaceInput]
        """

        self._tags = tags

    @property
    def username(self):
        """Gets the username of this CreateWorkspaceRequest.  # noqa: E501


        :return: The username of this CreateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this CreateWorkspaceRequest.


        :param username: The username of this CreateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateWorkspaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateWorkspaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateWorkspaceRequest):
            return True

        return self.to_dict() != other.to_dict()
