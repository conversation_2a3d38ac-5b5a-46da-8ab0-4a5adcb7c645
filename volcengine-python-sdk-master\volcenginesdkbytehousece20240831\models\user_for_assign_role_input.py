# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserForAssignRoleInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'admin_option': 'bool',
        'created_at': 'int',
        'username': 'str'
    }

    attribute_map = {
        'admin_option': 'AdminOption',
        'created_at': 'CreatedAt',
        'username': 'Username'
    }

    def __init__(self, admin_option=None, created_at=None, username=None, _configuration=None):  # noqa: E501
        """UserForAssignRoleInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._admin_option = None
        self._created_at = None
        self._username = None
        self.discriminator = None

        if admin_option is not None:
            self.admin_option = admin_option
        if created_at is not None:
            self.created_at = created_at
        if username is not None:
            self.username = username

    @property
    def admin_option(self):
        """Gets the admin_option of this UserForAssignRoleInput.  # noqa: E501


        :return: The admin_option of this UserForAssignRoleInput.  # noqa: E501
        :rtype: bool
        """
        return self._admin_option

    @admin_option.setter
    def admin_option(self, admin_option):
        """Sets the admin_option of this UserForAssignRoleInput.


        :param admin_option: The admin_option of this UserForAssignRoleInput.  # noqa: E501
        :type: bool
        """

        self._admin_option = admin_option

    @property
    def created_at(self):
        """Gets the created_at of this UserForAssignRoleInput.  # noqa: E501


        :return: The created_at of this UserForAssignRoleInput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this UserForAssignRoleInput.


        :param created_at: The created_at of this UserForAssignRoleInput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def username(self):
        """Gets the username of this UserForAssignRoleInput.  # noqa: E501


        :return: The username of this UserForAssignRoleInput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this UserForAssignRoleInput.


        :param username: The username of this UserForAssignRoleInput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserForAssignRoleInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserForAssignRoleInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserForAssignRoleInput):
            return True

        return self.to_dict() != other.to_dict()
