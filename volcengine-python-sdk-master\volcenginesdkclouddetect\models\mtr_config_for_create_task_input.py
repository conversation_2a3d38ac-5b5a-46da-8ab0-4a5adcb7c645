# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MtrConfigForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_reverse_resolution': 'bool',
        'interval': 'float',
        'max_hops': 'int',
        'max_unknown_host': 'int',
        'package_num': 'int',
        'package_size': 'int',
        'package_type': 'int',
        'timeout': 'int'
    }

    attribute_map = {
        'dns_reverse_resolution': 'DNSReverseResolution',
        'interval': 'Interval',
        'max_hops': 'MaxHops',
        'max_unknown_host': 'MaxUnknownHost',
        'package_num': 'PackageNum',
        'package_size': 'PackageSize',
        'package_type': 'PackageType',
        'timeout': 'Timeout'
    }

    def __init__(self, dns_reverse_resolution=None, interval=None, max_hops=None, max_unknown_host=None, package_num=None, package_size=None, package_type=None, timeout=None, _configuration=None):  # noqa: E501
        """MtrConfigForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_reverse_resolution = None
        self._interval = None
        self._max_hops = None
        self._max_unknown_host = None
        self._package_num = None
        self._package_size = None
        self._package_type = None
        self._timeout = None
        self.discriminator = None

        if dns_reverse_resolution is not None:
            self.dns_reverse_resolution = dns_reverse_resolution
        if interval is not None:
            self.interval = interval
        if max_hops is not None:
            self.max_hops = max_hops
        if max_unknown_host is not None:
            self.max_unknown_host = max_unknown_host
        if package_num is not None:
            self.package_num = package_num
        if package_size is not None:
            self.package_size = package_size
        if package_type is not None:
            self.package_type = package_type
        if timeout is not None:
            self.timeout = timeout

    @property
    def dns_reverse_resolution(self):
        """Gets the dns_reverse_resolution of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The dns_reverse_resolution of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: bool
        """
        return self._dns_reverse_resolution

    @dns_reverse_resolution.setter
    def dns_reverse_resolution(self, dns_reverse_resolution):
        """Sets the dns_reverse_resolution of this MtrConfigForCreateTaskInput.


        :param dns_reverse_resolution: The dns_reverse_resolution of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: bool
        """

        self._dns_reverse_resolution = dns_reverse_resolution

    @property
    def interval(self):
        """Gets the interval of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The interval of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: float
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this MtrConfigForCreateTaskInput.


        :param interval: The interval of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: float
        """

        self._interval = interval

    @property
    def max_hops(self):
        """Gets the max_hops of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The max_hops of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._max_hops

    @max_hops.setter
    def max_hops(self, max_hops):
        """Sets the max_hops of this MtrConfigForCreateTaskInput.


        :param max_hops: The max_hops of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._max_hops = max_hops

    @property
    def max_unknown_host(self):
        """Gets the max_unknown_host of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The max_unknown_host of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._max_unknown_host

    @max_unknown_host.setter
    def max_unknown_host(self, max_unknown_host):
        """Sets the max_unknown_host of this MtrConfigForCreateTaskInput.


        :param max_unknown_host: The max_unknown_host of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._max_unknown_host = max_unknown_host

    @property
    def package_num(self):
        """Gets the package_num of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The package_num of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._package_num

    @package_num.setter
    def package_num(self, package_num):
        """Sets the package_num of this MtrConfigForCreateTaskInput.


        :param package_num: The package_num of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._package_num = package_num

    @property
    def package_size(self):
        """Gets the package_size of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The package_size of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._package_size

    @package_size.setter
    def package_size(self, package_size):
        """Sets the package_size of this MtrConfigForCreateTaskInput.


        :param package_size: The package_size of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._package_size = package_size

    @property
    def package_type(self):
        """Gets the package_type of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The package_type of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._package_type

    @package_type.setter
    def package_type(self, package_type):
        """Sets the package_type of this MtrConfigForCreateTaskInput.


        :param package_type: The package_type of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._package_type = package_type

    @property
    def timeout(self):
        """Gets the timeout of this MtrConfigForCreateTaskInput.  # noqa: E501


        :return: The timeout of this MtrConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this MtrConfigForCreateTaskInput.


        :param timeout: The timeout of this MtrConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MtrConfigForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MtrConfigForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MtrConfigForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
