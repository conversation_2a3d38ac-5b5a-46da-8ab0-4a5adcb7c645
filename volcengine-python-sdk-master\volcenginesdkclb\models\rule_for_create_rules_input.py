# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RuleForCreateRulesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_type': 'str',
        'description': 'str',
        'domain': 'str',
        'redirect_config': 'RedirectConfigForCreateRulesInput',
        'server_group_id': 'str',
        'tags': 'list[RulesTagForCreateRulesInput]',
        'url': 'str'
    }

    attribute_map = {
        'action_type': 'ActionType',
        'description': 'Description',
        'domain': 'Domain',
        'redirect_config': 'RedirectConfig',
        'server_group_id': 'ServerGroupId',
        'tags': 'Tags',
        'url': 'Url'
    }

    def __init__(self, action_type=None, description=None, domain=None, redirect_config=None, server_group_id=None, tags=None, url=None, _configuration=None):  # noqa: E501
        """RuleForCreateRulesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_type = None
        self._description = None
        self._domain = None
        self._redirect_config = None
        self._server_group_id = None
        self._tags = None
        self._url = None
        self.discriminator = None

        if action_type is not None:
            self.action_type = action_type
        if description is not None:
            self.description = description
        if domain is not None:
            self.domain = domain
        if redirect_config is not None:
            self.redirect_config = redirect_config
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if tags is not None:
            self.tags = tags
        if url is not None:
            self.url = url

    @property
    def action_type(self):
        """Gets the action_type of this RuleForCreateRulesInput.  # noqa: E501


        :return: The action_type of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._action_type

    @action_type.setter
    def action_type(self, action_type):
        """Sets the action_type of this RuleForCreateRulesInput.


        :param action_type: The action_type of this RuleForCreateRulesInput.  # noqa: E501
        :type: str
        """

        self._action_type = action_type

    @property
    def description(self):
        """Gets the description of this RuleForCreateRulesInput.  # noqa: E501


        :return: The description of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this RuleForCreateRulesInput.


        :param description: The description of this RuleForCreateRulesInput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def domain(self):
        """Gets the domain of this RuleForCreateRulesInput.  # noqa: E501


        :return: The domain of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this RuleForCreateRulesInput.


        :param domain: The domain of this RuleForCreateRulesInput.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def redirect_config(self):
        """Gets the redirect_config of this RuleForCreateRulesInput.  # noqa: E501


        :return: The redirect_config of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: RedirectConfigForCreateRulesInput
        """
        return self._redirect_config

    @redirect_config.setter
    def redirect_config(self, redirect_config):
        """Sets the redirect_config of this RuleForCreateRulesInput.


        :param redirect_config: The redirect_config of this RuleForCreateRulesInput.  # noqa: E501
        :type: RedirectConfigForCreateRulesInput
        """

        self._redirect_config = redirect_config

    @property
    def server_group_id(self):
        """Gets the server_group_id of this RuleForCreateRulesInput.  # noqa: E501


        :return: The server_group_id of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this RuleForCreateRulesInput.


        :param server_group_id: The server_group_id of this RuleForCreateRulesInput.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def tags(self):
        """Gets the tags of this RuleForCreateRulesInput.  # noqa: E501


        :return: The tags of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: list[RulesTagForCreateRulesInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this RuleForCreateRulesInput.


        :param tags: The tags of this RuleForCreateRulesInput.  # noqa: E501
        :type: list[RulesTagForCreateRulesInput]
        """

        self._tags = tags

    @property
    def url(self):
        """Gets the url of this RuleForCreateRulesInput.  # noqa: E501


        :return: The url of this RuleForCreateRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this RuleForCreateRulesInput.


        :param url: The url of this RuleForCreateRulesInput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RuleForCreateRulesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RuleForCreateRulesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RuleForCreateRulesInput):
            return True

        return self.to_dict() != other.to_dict()
