# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SummaryMapForGetAccountSummaryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_keys_per_account_quota': 'int',
        'access_keys_per_user_quota': 'int',
        'attached_policies_per_group_quota': 'int',
        'attached_policies_per_role_quota': 'int',
        'attached_policies_per_user_quota': 'int',
        'attached_system_policies_per_group_quota': 'int',
        'attached_system_policies_per_role_quota': 'int',
        'attached_system_policies_per_user_quota': 'int',
        'groups_per_user_quota': 'int',
        'groups_quota': 'int',
        'groups_usage': 'int',
        'policies_quota': 'int',
        'policies_usage': 'int',
        'policy_size': 'int',
        'roles_quota': 'int',
        'roles_usage': 'int',
        'users_quota': 'int',
        'users_usage': 'int'
    }

    attribute_map = {
        'access_keys_per_account_quota': 'AccessKeysPerAccountQuota',
        'access_keys_per_user_quota': 'AccessKeysPerUserQuota',
        'attached_policies_per_group_quota': 'AttachedPoliciesPerGroupQuota',
        'attached_policies_per_role_quota': 'AttachedPoliciesPerRoleQuota',
        'attached_policies_per_user_quota': 'AttachedPoliciesPerUserQuota',
        'attached_system_policies_per_group_quota': 'AttachedSystemPoliciesPerGroupQuota',
        'attached_system_policies_per_role_quota': 'AttachedSystemPoliciesPerRoleQuota',
        'attached_system_policies_per_user_quota': 'AttachedSystemPoliciesPerUserQuota',
        'groups_per_user_quota': 'GroupsPerUserQuota',
        'groups_quota': 'GroupsQuota',
        'groups_usage': 'GroupsUsage',
        'policies_quota': 'PoliciesQuota',
        'policies_usage': 'PoliciesUsage',
        'policy_size': 'PolicySize',
        'roles_quota': 'RolesQuota',
        'roles_usage': 'RolesUsage',
        'users_quota': 'UsersQuota',
        'users_usage': 'UsersUsage'
    }

    def __init__(self, access_keys_per_account_quota=None, access_keys_per_user_quota=None, attached_policies_per_group_quota=None, attached_policies_per_role_quota=None, attached_policies_per_user_quota=None, attached_system_policies_per_group_quota=None, attached_system_policies_per_role_quota=None, attached_system_policies_per_user_quota=None, groups_per_user_quota=None, groups_quota=None, groups_usage=None, policies_quota=None, policies_usage=None, policy_size=None, roles_quota=None, roles_usage=None, users_quota=None, users_usage=None, _configuration=None):  # noqa: E501
        """SummaryMapForGetAccountSummaryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_keys_per_account_quota = None
        self._access_keys_per_user_quota = None
        self._attached_policies_per_group_quota = None
        self._attached_policies_per_role_quota = None
        self._attached_policies_per_user_quota = None
        self._attached_system_policies_per_group_quota = None
        self._attached_system_policies_per_role_quota = None
        self._attached_system_policies_per_user_quota = None
        self._groups_per_user_quota = None
        self._groups_quota = None
        self._groups_usage = None
        self._policies_quota = None
        self._policies_usage = None
        self._policy_size = None
        self._roles_quota = None
        self._roles_usage = None
        self._users_quota = None
        self._users_usage = None
        self.discriminator = None

        if access_keys_per_account_quota is not None:
            self.access_keys_per_account_quota = access_keys_per_account_quota
        if access_keys_per_user_quota is not None:
            self.access_keys_per_user_quota = access_keys_per_user_quota
        if attached_policies_per_group_quota is not None:
            self.attached_policies_per_group_quota = attached_policies_per_group_quota
        if attached_policies_per_role_quota is not None:
            self.attached_policies_per_role_quota = attached_policies_per_role_quota
        if attached_policies_per_user_quota is not None:
            self.attached_policies_per_user_quota = attached_policies_per_user_quota
        if attached_system_policies_per_group_quota is not None:
            self.attached_system_policies_per_group_quota = attached_system_policies_per_group_quota
        if attached_system_policies_per_role_quota is not None:
            self.attached_system_policies_per_role_quota = attached_system_policies_per_role_quota
        if attached_system_policies_per_user_quota is not None:
            self.attached_system_policies_per_user_quota = attached_system_policies_per_user_quota
        if groups_per_user_quota is not None:
            self.groups_per_user_quota = groups_per_user_quota
        if groups_quota is not None:
            self.groups_quota = groups_quota
        if groups_usage is not None:
            self.groups_usage = groups_usage
        if policies_quota is not None:
            self.policies_quota = policies_quota
        if policies_usage is not None:
            self.policies_usage = policies_usage
        if policy_size is not None:
            self.policy_size = policy_size
        if roles_quota is not None:
            self.roles_quota = roles_quota
        if roles_usage is not None:
            self.roles_usage = roles_usage
        if users_quota is not None:
            self.users_quota = users_quota
        if users_usage is not None:
            self.users_usage = users_usage

    @property
    def access_keys_per_account_quota(self):
        """Gets the access_keys_per_account_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The access_keys_per_account_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._access_keys_per_account_quota

    @access_keys_per_account_quota.setter
    def access_keys_per_account_quota(self, access_keys_per_account_quota):
        """Sets the access_keys_per_account_quota of this SummaryMapForGetAccountSummaryOutput.


        :param access_keys_per_account_quota: The access_keys_per_account_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._access_keys_per_account_quota = access_keys_per_account_quota

    @property
    def access_keys_per_user_quota(self):
        """Gets the access_keys_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The access_keys_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._access_keys_per_user_quota

    @access_keys_per_user_quota.setter
    def access_keys_per_user_quota(self, access_keys_per_user_quota):
        """Sets the access_keys_per_user_quota of this SummaryMapForGetAccountSummaryOutput.


        :param access_keys_per_user_quota: The access_keys_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._access_keys_per_user_quota = access_keys_per_user_quota

    @property
    def attached_policies_per_group_quota(self):
        """Gets the attached_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_policies_per_group_quota

    @attached_policies_per_group_quota.setter
    def attached_policies_per_group_quota(self, attached_policies_per_group_quota):
        """Sets the attached_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_policies_per_group_quota: The attached_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_policies_per_group_quota = attached_policies_per_group_quota

    @property
    def attached_policies_per_role_quota(self):
        """Gets the attached_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_policies_per_role_quota

    @attached_policies_per_role_quota.setter
    def attached_policies_per_role_quota(self, attached_policies_per_role_quota):
        """Sets the attached_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_policies_per_role_quota: The attached_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_policies_per_role_quota = attached_policies_per_role_quota

    @property
    def attached_policies_per_user_quota(self):
        """Gets the attached_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_policies_per_user_quota

    @attached_policies_per_user_quota.setter
    def attached_policies_per_user_quota(self, attached_policies_per_user_quota):
        """Sets the attached_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_policies_per_user_quota: The attached_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_policies_per_user_quota = attached_policies_per_user_quota

    @property
    def attached_system_policies_per_group_quota(self):
        """Gets the attached_system_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_system_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_system_policies_per_group_quota

    @attached_system_policies_per_group_quota.setter
    def attached_system_policies_per_group_quota(self, attached_system_policies_per_group_quota):
        """Sets the attached_system_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_system_policies_per_group_quota: The attached_system_policies_per_group_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_system_policies_per_group_quota = attached_system_policies_per_group_quota

    @property
    def attached_system_policies_per_role_quota(self):
        """Gets the attached_system_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_system_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_system_policies_per_role_quota

    @attached_system_policies_per_role_quota.setter
    def attached_system_policies_per_role_quota(self, attached_system_policies_per_role_quota):
        """Sets the attached_system_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_system_policies_per_role_quota: The attached_system_policies_per_role_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_system_policies_per_role_quota = attached_system_policies_per_role_quota

    @property
    def attached_system_policies_per_user_quota(self):
        """Gets the attached_system_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The attached_system_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._attached_system_policies_per_user_quota

    @attached_system_policies_per_user_quota.setter
    def attached_system_policies_per_user_quota(self, attached_system_policies_per_user_quota):
        """Sets the attached_system_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.


        :param attached_system_policies_per_user_quota: The attached_system_policies_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._attached_system_policies_per_user_quota = attached_system_policies_per_user_quota

    @property
    def groups_per_user_quota(self):
        """Gets the groups_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The groups_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._groups_per_user_quota

    @groups_per_user_quota.setter
    def groups_per_user_quota(self, groups_per_user_quota):
        """Sets the groups_per_user_quota of this SummaryMapForGetAccountSummaryOutput.


        :param groups_per_user_quota: The groups_per_user_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._groups_per_user_quota = groups_per_user_quota

    @property
    def groups_quota(self):
        """Gets the groups_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The groups_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._groups_quota

    @groups_quota.setter
    def groups_quota(self, groups_quota):
        """Sets the groups_quota of this SummaryMapForGetAccountSummaryOutput.


        :param groups_quota: The groups_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._groups_quota = groups_quota

    @property
    def groups_usage(self):
        """Gets the groups_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The groups_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._groups_usage

    @groups_usage.setter
    def groups_usage(self, groups_usage):
        """Sets the groups_usage of this SummaryMapForGetAccountSummaryOutput.


        :param groups_usage: The groups_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._groups_usage = groups_usage

    @property
    def policies_quota(self):
        """Gets the policies_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The policies_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._policies_quota

    @policies_quota.setter
    def policies_quota(self, policies_quota):
        """Sets the policies_quota of this SummaryMapForGetAccountSummaryOutput.


        :param policies_quota: The policies_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._policies_quota = policies_quota

    @property
    def policies_usage(self):
        """Gets the policies_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The policies_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._policies_usage

    @policies_usage.setter
    def policies_usage(self, policies_usage):
        """Sets the policies_usage of this SummaryMapForGetAccountSummaryOutput.


        :param policies_usage: The policies_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._policies_usage = policies_usage

    @property
    def policy_size(self):
        """Gets the policy_size of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The policy_size of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._policy_size

    @policy_size.setter
    def policy_size(self, policy_size):
        """Sets the policy_size of this SummaryMapForGetAccountSummaryOutput.


        :param policy_size: The policy_size of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._policy_size = policy_size

    @property
    def roles_quota(self):
        """Gets the roles_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The roles_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._roles_quota

    @roles_quota.setter
    def roles_quota(self, roles_quota):
        """Sets the roles_quota of this SummaryMapForGetAccountSummaryOutput.


        :param roles_quota: The roles_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._roles_quota = roles_quota

    @property
    def roles_usage(self):
        """Gets the roles_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The roles_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._roles_usage

    @roles_usage.setter
    def roles_usage(self, roles_usage):
        """Sets the roles_usage of this SummaryMapForGetAccountSummaryOutput.


        :param roles_usage: The roles_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._roles_usage = roles_usage

    @property
    def users_quota(self):
        """Gets the users_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The users_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._users_quota

    @users_quota.setter
    def users_quota(self, users_quota):
        """Sets the users_quota of this SummaryMapForGetAccountSummaryOutput.


        :param users_quota: The users_quota of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._users_quota = users_quota

    @property
    def users_usage(self):
        """Gets the users_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501


        :return: The users_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._users_usage

    @users_usage.setter
    def users_usage(self, users_usage):
        """Sets the users_usage of this SummaryMapForGetAccountSummaryOutput.


        :param users_usage: The users_usage of this SummaryMapForGetAccountSummaryOutput.  # noqa: E501
        :type: int
        """

        self._users_usage = users_usage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SummaryMapForGetAccountSummaryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SummaryMapForGetAccountSummaryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SummaryMapForGetAccountSummaryOutput):
            return True

        return self.to_dict() != other.to_dict()
