# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAccountUserTrackDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter_user_track': 'FilterUserTrackForGetAccountUserTrackDataOutput',
        'user_track_data': 'UserTrackDataForGetAccountUserTrackDataOutput'
    }

    attribute_map = {
        'filter_user_track': 'FilterUserTrack',
        'user_track_data': 'UserTrackData'
    }

    def __init__(self, filter_user_track=None, user_track_data=None, _configuration=None):  # noqa: E501
        """GetAccountUserTrackDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter_user_track = None
        self._user_track_data = None
        self.discriminator = None

        if filter_user_track is not None:
            self.filter_user_track = filter_user_track
        if user_track_data is not None:
            self.user_track_data = user_track_data

    @property
    def filter_user_track(self):
        """Gets the filter_user_track of this GetAccountUserTrackDataResponse.  # noqa: E501


        :return: The filter_user_track of this GetAccountUserTrackDataResponse.  # noqa: E501
        :rtype: FilterUserTrackForGetAccountUserTrackDataOutput
        """
        return self._filter_user_track

    @filter_user_track.setter
    def filter_user_track(self, filter_user_track):
        """Sets the filter_user_track of this GetAccountUserTrackDataResponse.


        :param filter_user_track: The filter_user_track of this GetAccountUserTrackDataResponse.  # noqa: E501
        :type: FilterUserTrackForGetAccountUserTrackDataOutput
        """

        self._filter_user_track = filter_user_track

    @property
    def user_track_data(self):
        """Gets the user_track_data of this GetAccountUserTrackDataResponse.  # noqa: E501


        :return: The user_track_data of this GetAccountUserTrackDataResponse.  # noqa: E501
        :rtype: UserTrackDataForGetAccountUserTrackDataOutput
        """
        return self._user_track_data

    @user_track_data.setter
    def user_track_data(self, user_track_data):
        """Sets the user_track_data of this GetAccountUserTrackDataResponse.


        :param user_track_data: The user_track_data of this GetAccountUserTrackDataResponse.  # noqa: E501
        :type: UserTrackDataForGetAccountUserTrackDataOutput
        """

        self._user_track_data = user_track_data

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAccountUserTrackDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAccountUserTrackDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAccountUserTrackDataResponse):
            return True

        return self.to_dict() != other.to_dict()
