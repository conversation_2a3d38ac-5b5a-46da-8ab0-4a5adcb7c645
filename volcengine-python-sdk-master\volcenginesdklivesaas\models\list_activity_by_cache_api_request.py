# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListActivityByCacheAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'follower_user_name': 'str',
        'host_account_id': 'int',
        'host_account_name': 'str',
        'is_lock_preview': 'int',
        'live_layout': 'int',
        'live_mode': 'int',
        'live_review_status': 'int',
        'live_time': 'int',
        'name': 'str',
        'page_item_count': 'int',
        'page_no': 'int',
        'site_tag_news': 'list[SiteTagNewForListActivityByCacheAPIInput]',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'int'
    }

    attribute_map = {
        'follower_user_name': 'FollowerUserName',
        'host_account_id': 'HostAccountId',
        'host_account_name': 'HostAccountName',
        'is_lock_preview': 'IsLockPreview',
        'live_layout': 'LiveLayout',
        'live_mode': 'LiveMode',
        'live_review_status': 'LiveReviewStatus',
        'live_time': 'LiveTime',
        'name': 'Name',
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo',
        'site_tag_news': 'SiteTagNews',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status'
    }

    def __init__(self, follower_user_name=None, host_account_id=None, host_account_name=None, is_lock_preview=None, live_layout=None, live_mode=None, live_review_status=None, live_time=None, name=None, page_item_count=None, page_no=None, site_tag_news=None, sort_by=None, sort_order=None, status=None, _configuration=None):  # noqa: E501
        """ListActivityByCacheAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._follower_user_name = None
        self._host_account_id = None
        self._host_account_name = None
        self._is_lock_preview = None
        self._live_layout = None
        self._live_mode = None
        self._live_review_status = None
        self._live_time = None
        self._name = None
        self._page_item_count = None
        self._page_no = None
        self._site_tag_news = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self.discriminator = None

        if follower_user_name is not None:
            self.follower_user_name = follower_user_name
        if host_account_id is not None:
            self.host_account_id = host_account_id
        if host_account_name is not None:
            self.host_account_name = host_account_name
        if is_lock_preview is not None:
            self.is_lock_preview = is_lock_preview
        if live_layout is not None:
            self.live_layout = live_layout
        if live_mode is not None:
            self.live_mode = live_mode
        if live_review_status is not None:
            self.live_review_status = live_review_status
        if live_time is not None:
            self.live_time = live_time
        if name is not None:
            self.name = name
        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no
        if site_tag_news is not None:
            self.site_tag_news = site_tag_news
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status

    @property
    def follower_user_name(self):
        """Gets the follower_user_name of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The follower_user_name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._follower_user_name

    @follower_user_name.setter
    def follower_user_name(self, follower_user_name):
        """Sets the follower_user_name of this ListActivityByCacheAPIRequest.


        :param follower_user_name: The follower_user_name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: str
        """

        self._follower_user_name = follower_user_name

    @property
    def host_account_id(self):
        """Gets the host_account_id of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The host_account_id of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._host_account_id

    @host_account_id.setter
    def host_account_id(self, host_account_id):
        """Sets the host_account_id of this ListActivityByCacheAPIRequest.


        :param host_account_id: The host_account_id of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._host_account_id = host_account_id

    @property
    def host_account_name(self):
        """Gets the host_account_name of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The host_account_name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._host_account_name

    @host_account_name.setter
    def host_account_name(self, host_account_name):
        """Sets the host_account_name of this ListActivityByCacheAPIRequest.


        :param host_account_name: The host_account_name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: str
        """

        self._host_account_name = host_account_name

    @property
    def is_lock_preview(self):
        """Gets the is_lock_preview of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The is_lock_preview of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_lock_preview

    @is_lock_preview.setter
    def is_lock_preview(self, is_lock_preview):
        """Sets the is_lock_preview of this ListActivityByCacheAPIRequest.


        :param is_lock_preview: The is_lock_preview of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_lock_preview = is_lock_preview

    @property
    def live_layout(self):
        """Gets the live_layout of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The live_layout of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_layout

    @live_layout.setter
    def live_layout(self, live_layout):
        """Sets the live_layout of this ListActivityByCacheAPIRequest.


        :param live_layout: The live_layout of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_layout = live_layout

    @property
    def live_mode(self):
        """Gets the live_mode of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The live_mode of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_mode

    @live_mode.setter
    def live_mode(self, live_mode):
        """Sets the live_mode of this ListActivityByCacheAPIRequest.


        :param live_mode: The live_mode of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_mode = live_mode

    @property
    def live_review_status(self):
        """Gets the live_review_status of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The live_review_status of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_review_status

    @live_review_status.setter
    def live_review_status(self, live_review_status):
        """Sets the live_review_status of this ListActivityByCacheAPIRequest.


        :param live_review_status: The live_review_status of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_review_status = live_review_status

    @property
    def live_time(self):
        """Gets the live_time of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The live_time of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this ListActivityByCacheAPIRequest.


        :param live_time: The live_time of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def name(self):
        """Gets the name of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListActivityByCacheAPIRequest.


        :param name: The name of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_item_count(self):
        """Gets the page_item_count of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The page_item_count of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this ListActivityByCacheAPIRequest.


        :param page_item_count: The page_item_count of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The page_no of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this ListActivityByCacheAPIRequest.


        :param page_no: The page_no of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def site_tag_news(self):
        """Gets the site_tag_news of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The site_tag_news of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: list[SiteTagNewForListActivityByCacheAPIInput]
        """
        return self._site_tag_news

    @site_tag_news.setter
    def site_tag_news(self, site_tag_news):
        """Sets the site_tag_news of this ListActivityByCacheAPIRequest.


        :param site_tag_news: The site_tag_news of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: list[SiteTagNewForListActivityByCacheAPIInput]
        """

        self._site_tag_news = site_tag_news

    @property
    def sort_by(self):
        """Gets the sort_by of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The sort_by of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListActivityByCacheAPIRequest.


        :param sort_by: The sort_by of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The sort_order of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListActivityByCacheAPIRequest.


        :param sort_order: The sort_order of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListActivityByCacheAPIRequest.  # noqa: E501


        :return: The status of this ListActivityByCacheAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListActivityByCacheAPIRequest.


        :param status: The status of this ListActivityByCacheAPIRequest.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListActivityByCacheAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListActivityByCacheAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListActivityByCacheAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
