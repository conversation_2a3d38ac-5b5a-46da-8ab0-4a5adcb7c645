# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListComponentsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_name': 'str',
        'cluster_id': 'str',
        'component_name': 'str',
        'component_state': 'str',
        'component_type': 'str',
        'create_time': 'str',
        'start_time': 'str'
    }

    attribute_map = {
        'application_name': 'ApplicationName',
        'cluster_id': 'ClusterId',
        'component_name': 'ComponentName',
        'component_state': 'ComponentState',
        'component_type': 'ComponentType',
        'create_time': 'CreateTime',
        'start_time': 'StartTime'
    }

    def __init__(self, application_name=None, cluster_id=None, component_name=None, component_state=None, component_type=None, create_time=None, start_time=None, _configuration=None):  # noqa: E501
        """ItemForListComponentsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_name = None
        self._cluster_id = None
        self._component_name = None
        self._component_state = None
        self._component_type = None
        self._create_time = None
        self._start_time = None
        self.discriminator = None

        if application_name is not None:
            self.application_name = application_name
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if component_name is not None:
            self.component_name = component_name
        if component_state is not None:
            self.component_state = component_state
        if component_type is not None:
            self.component_type = component_type
        if create_time is not None:
            self.create_time = create_time
        if start_time is not None:
            self.start_time = start_time

    @property
    def application_name(self):
        """Gets the application_name of this ItemForListComponentsOutput.  # noqa: E501


        :return: The application_name of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ItemForListComponentsOutput.


        :param application_name: The application_name of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListComponentsOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListComponentsOutput.


        :param cluster_id: The cluster_id of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def component_name(self):
        """Gets the component_name of this ItemForListComponentsOutput.  # noqa: E501


        :return: The component_name of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._component_name

    @component_name.setter
    def component_name(self, component_name):
        """Sets the component_name of this ItemForListComponentsOutput.


        :param component_name: The component_name of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._component_name = component_name

    @property
    def component_state(self):
        """Gets the component_state of this ItemForListComponentsOutput.  # noqa: E501


        :return: The component_state of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._component_state

    @component_state.setter
    def component_state(self, component_state):
        """Sets the component_state of this ItemForListComponentsOutput.


        :param component_state: The component_state of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._component_state = component_state

    @property
    def component_type(self):
        """Gets the component_type of this ItemForListComponentsOutput.  # noqa: E501


        :return: The component_type of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._component_type

    @component_type.setter
    def component_type(self, component_type):
        """Sets the component_type of this ItemForListComponentsOutput.


        :param component_type: The component_type of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._component_type = component_type

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListComponentsOutput.  # noqa: E501


        :return: The create_time of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListComponentsOutput.


        :param create_time: The create_time of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListComponentsOutput.  # noqa: E501


        :return: The start_time of this ItemForListComponentsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListComponentsOutput.


        :param start_time: The start_time of this ItemForListComponentsOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListComponentsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListComponentsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListComponentsOutput):
            return True

        return self.to_dict() != other.to_dict()
