# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetTaskAwardRecordStatisticsAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_item_count': 'int',
        'page_no': 'int',
        'page_total_count': 'int',
        'task_award_record_list': 'list[TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput]'
    }

    attribute_map = {
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo',
        'page_total_count': 'PageTotalCount',
        'task_award_record_list': 'TaskAwardRecordList'
    }

    def __init__(self, page_item_count=None, page_no=None, page_total_count=None, task_award_record_list=None, _configuration=None):  # noqa: E501
        """GetTaskAwardRecordStatisticsAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_item_count = None
        self._page_no = None
        self._page_total_count = None
        self._task_award_record_list = None
        self.discriminator = None

        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no
        if page_total_count is not None:
            self.page_total_count = page_total_count
        if task_award_record_list is not None:
            self.task_award_record_list = task_award_record_list

    @property
    def page_item_count(self):
        """Gets the page_item_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501


        :return: The page_item_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this GetTaskAwardRecordStatisticsAPIResponse.


        :param page_item_count: The page_item_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501


        :return: The page_no of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this GetTaskAwardRecordStatisticsAPIResponse.


        :param page_no: The page_no of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def page_total_count(self):
        """Gets the page_total_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501


        :return: The page_total_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_total_count

    @page_total_count.setter
    def page_total_count(self, page_total_count):
        """Sets the page_total_count of this GetTaskAwardRecordStatisticsAPIResponse.


        :param page_total_count: The page_total_count of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :type: int
        """

        self._page_total_count = page_total_count

    @property
    def task_award_record_list(self):
        """Gets the task_award_record_list of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501


        :return: The task_award_record_list of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :rtype: list[TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput]
        """
        return self._task_award_record_list

    @task_award_record_list.setter
    def task_award_record_list(self, task_award_record_list):
        """Sets the task_award_record_list of this GetTaskAwardRecordStatisticsAPIResponse.


        :param task_award_record_list: The task_award_record_list of this GetTaskAwardRecordStatisticsAPIResponse.  # noqa: E501
        :type: list[TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput]
        """

        self._task_award_record_list = task_award_record_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetTaskAwardRecordStatisticsAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetTaskAwardRecordStatisticsAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetTaskAwardRecordStatisticsAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
