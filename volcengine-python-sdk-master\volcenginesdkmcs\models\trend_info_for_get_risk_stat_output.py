# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TrendInfoForGetRiskStatOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        '_date': 'str',
        'hour': 'str',
        'new_added': 'TotalForGetRiskStatOutput',
        'stat': 'TotalForGetRiskStatOutput'
    }

    attribute_map = {
        '_date': 'Date',
        'hour': 'Hour',
        'new_added': 'NewAdded',
        'stat': 'Stat'
    }

    def __init__(self, _date=None, hour=None, new_added=None, stat=None, _configuration=None):  # noqa: E501
        """TrendInfoForGetRiskStatOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self.__date = None
        self._hour = None
        self._new_added = None
        self._stat = None
        self.discriminator = None

        if _date is not None:
            self._date = _date
        if hour is not None:
            self.hour = hour
        if new_added is not None:
            self.new_added = new_added
        if stat is not None:
            self.stat = stat

    @property
    def _date(self):
        """Gets the _date of this TrendInfoForGetRiskStatOutput.  # noqa: E501


        :return: The _date of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :rtype: str
        """
        return self.__date

    @_date.setter
    def _date(self, _date):
        """Sets the _date of this TrendInfoForGetRiskStatOutput.


        :param _date: The _date of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :type: str
        """

        self.__date = _date

    @property
    def hour(self):
        """Gets the hour of this TrendInfoForGetRiskStatOutput.  # noqa: E501


        :return: The hour of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :rtype: str
        """
        return self._hour

    @hour.setter
    def hour(self, hour):
        """Sets the hour of this TrendInfoForGetRiskStatOutput.


        :param hour: The hour of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :type: str
        """

        self._hour = hour

    @property
    def new_added(self):
        """Gets the new_added of this TrendInfoForGetRiskStatOutput.  # noqa: E501


        :return: The new_added of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :rtype: TotalForGetRiskStatOutput
        """
        return self._new_added

    @new_added.setter
    def new_added(self, new_added):
        """Sets the new_added of this TrendInfoForGetRiskStatOutput.


        :param new_added: The new_added of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :type: TotalForGetRiskStatOutput
        """

        self._new_added = new_added

    @property
    def stat(self):
        """Gets the stat of this TrendInfoForGetRiskStatOutput.  # noqa: E501


        :return: The stat of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :rtype: TotalForGetRiskStatOutput
        """
        return self._stat

    @stat.setter
    def stat(self, stat):
        """Sets the stat of this TrendInfoForGetRiskStatOutput.


        :param stat: The stat of this TrendInfoForGetRiskStatOutput.  # noqa: E501
        :type: TotalForGetRiskStatOutput
        """

        self._stat = stat

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrendInfoForGetRiskStatOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrendInfoForGetRiskStatOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TrendInfoForGetRiskStatOutput):
            return True

        return self.to_dict() != other.to_dict()
