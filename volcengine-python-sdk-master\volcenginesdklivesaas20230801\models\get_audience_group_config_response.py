# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAudienceGroupConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audience_groups_basic_info': 'list[AudienceGroupsBasicInfoForGetAudienceGroupConfigOutput]',
        'cannot_watch_live_when_not_in_group': 'int',
        'group_enable_status': 'int',
        'group_type': 'int',
        'view_page_show_group_name': 'int'
    }

    attribute_map = {
        'audience_groups_basic_info': 'AudienceGroupsBasicInfo',
        'cannot_watch_live_when_not_in_group': 'CannotWatchLiveWhenNotInGroup',
        'group_enable_status': 'GroupEnableStatus',
        'group_type': 'GroupType',
        'view_page_show_group_name': 'ViewPageShowGroupName'
    }

    def __init__(self, audience_groups_basic_info=None, cannot_watch_live_when_not_in_group=None, group_enable_status=None, group_type=None, view_page_show_group_name=None, _configuration=None):  # noqa: E501
        """GetAudienceGroupConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audience_groups_basic_info = None
        self._cannot_watch_live_when_not_in_group = None
        self._group_enable_status = None
        self._group_type = None
        self._view_page_show_group_name = None
        self.discriminator = None

        if audience_groups_basic_info is not None:
            self.audience_groups_basic_info = audience_groups_basic_info
        if cannot_watch_live_when_not_in_group is not None:
            self.cannot_watch_live_when_not_in_group = cannot_watch_live_when_not_in_group
        if group_enable_status is not None:
            self.group_enable_status = group_enable_status
        if group_type is not None:
            self.group_type = group_type
        if view_page_show_group_name is not None:
            self.view_page_show_group_name = view_page_show_group_name

    @property
    def audience_groups_basic_info(self):
        """Gets the audience_groups_basic_info of this GetAudienceGroupConfigResponse.  # noqa: E501


        :return: The audience_groups_basic_info of this GetAudienceGroupConfigResponse.  # noqa: E501
        :rtype: list[AudienceGroupsBasicInfoForGetAudienceGroupConfigOutput]
        """
        return self._audience_groups_basic_info

    @audience_groups_basic_info.setter
    def audience_groups_basic_info(self, audience_groups_basic_info):
        """Sets the audience_groups_basic_info of this GetAudienceGroupConfigResponse.


        :param audience_groups_basic_info: The audience_groups_basic_info of this GetAudienceGroupConfigResponse.  # noqa: E501
        :type: list[AudienceGroupsBasicInfoForGetAudienceGroupConfigOutput]
        """

        self._audience_groups_basic_info = audience_groups_basic_info

    @property
    def cannot_watch_live_when_not_in_group(self):
        """Gets the cannot_watch_live_when_not_in_group of this GetAudienceGroupConfigResponse.  # noqa: E501


        :return: The cannot_watch_live_when_not_in_group of this GetAudienceGroupConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._cannot_watch_live_when_not_in_group

    @cannot_watch_live_when_not_in_group.setter
    def cannot_watch_live_when_not_in_group(self, cannot_watch_live_when_not_in_group):
        """Sets the cannot_watch_live_when_not_in_group of this GetAudienceGroupConfigResponse.


        :param cannot_watch_live_when_not_in_group: The cannot_watch_live_when_not_in_group of this GetAudienceGroupConfigResponse.  # noqa: E501
        :type: int
        """

        self._cannot_watch_live_when_not_in_group = cannot_watch_live_when_not_in_group

    @property
    def group_enable_status(self):
        """Gets the group_enable_status of this GetAudienceGroupConfigResponse.  # noqa: E501


        :return: The group_enable_status of this GetAudienceGroupConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._group_enable_status

    @group_enable_status.setter
    def group_enable_status(self, group_enable_status):
        """Sets the group_enable_status of this GetAudienceGroupConfigResponse.


        :param group_enable_status: The group_enable_status of this GetAudienceGroupConfigResponse.  # noqa: E501
        :type: int
        """

        self._group_enable_status = group_enable_status

    @property
    def group_type(self):
        """Gets the group_type of this GetAudienceGroupConfigResponse.  # noqa: E501


        :return: The group_type of this GetAudienceGroupConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._group_type

    @group_type.setter
    def group_type(self, group_type):
        """Sets the group_type of this GetAudienceGroupConfigResponse.


        :param group_type: The group_type of this GetAudienceGroupConfigResponse.  # noqa: E501
        :type: int
        """

        self._group_type = group_type

    @property
    def view_page_show_group_name(self):
        """Gets the view_page_show_group_name of this GetAudienceGroupConfigResponse.  # noqa: E501


        :return: The view_page_show_group_name of this GetAudienceGroupConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._view_page_show_group_name

    @view_page_show_group_name.setter
    def view_page_show_group_name(self, view_page_show_group_name):
        """Sets the view_page_show_group_name of this GetAudienceGroupConfigResponse.


        :param view_page_show_group_name: The view_page_show_group_name of this GetAudienceGroupConfigResponse.  # noqa: E501
        :type: int
        """

        self._view_page_show_group_name = view_page_show_group_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAudienceGroupConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAudienceGroupConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAudienceGroupConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
