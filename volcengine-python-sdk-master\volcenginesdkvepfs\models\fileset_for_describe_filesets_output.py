# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilesetForDescribeFilesetsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_qos': 'int',
        'capacity_limit': 'int',
        'capacity_used': 'int',
        'create_time': 'str',
        'file_limit': 'int',
        'file_system_id': 'str',
        'file_used': 'int',
        'fileset_id': 'str',
        'fileset_name': 'str',
        'fileset_path': 'str',
        'iops_qos': 'int',
        'max_inode_num': 'int',
        'status': 'str'
    }

    attribute_map = {
        'bandwidth_qos': 'BandwidthQos',
        'capacity_limit': 'CapacityLimit',
        'capacity_used': 'CapacityUsed',
        'create_time': 'CreateTime',
        'file_limit': 'FileLimit',
        'file_system_id': 'FileSystemId',
        'file_used': 'FileUsed',
        'fileset_id': 'FilesetId',
        'fileset_name': 'FilesetName',
        'fileset_path': 'FilesetPath',
        'iops_qos': 'IOPSQos',
        'max_inode_num': 'MaxInodeNum',
        'status': 'Status'
    }

    def __init__(self, bandwidth_qos=None, capacity_limit=None, capacity_used=None, create_time=None, file_limit=None, file_system_id=None, file_used=None, fileset_id=None, fileset_name=None, fileset_path=None, iops_qos=None, max_inode_num=None, status=None, _configuration=None):  # noqa: E501
        """FilesetForDescribeFilesetsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_qos = None
        self._capacity_limit = None
        self._capacity_used = None
        self._create_time = None
        self._file_limit = None
        self._file_system_id = None
        self._file_used = None
        self._fileset_id = None
        self._fileset_name = None
        self._fileset_path = None
        self._iops_qos = None
        self._max_inode_num = None
        self._status = None
        self.discriminator = None

        if bandwidth_qos is not None:
            self.bandwidth_qos = bandwidth_qos
        if capacity_limit is not None:
            self.capacity_limit = capacity_limit
        if capacity_used is not None:
            self.capacity_used = capacity_used
        if create_time is not None:
            self.create_time = create_time
        if file_limit is not None:
            self.file_limit = file_limit
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if file_used is not None:
            self.file_used = file_used
        if fileset_id is not None:
            self.fileset_id = fileset_id
        if fileset_name is not None:
            self.fileset_name = fileset_name
        if fileset_path is not None:
            self.fileset_path = fileset_path
        if iops_qos is not None:
            self.iops_qos = iops_qos
        if max_inode_num is not None:
            self.max_inode_num = max_inode_num
        if status is not None:
            self.status = status

    @property
    def bandwidth_qos(self):
        """Gets the bandwidth_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The bandwidth_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_qos

    @bandwidth_qos.setter
    def bandwidth_qos(self, bandwidth_qos):
        """Sets the bandwidth_qos of this FilesetForDescribeFilesetsOutput.


        :param bandwidth_qos: The bandwidth_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth_qos = bandwidth_qos

    @property
    def capacity_limit(self):
        """Gets the capacity_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The capacity_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._capacity_limit

    @capacity_limit.setter
    def capacity_limit(self, capacity_limit):
        """Sets the capacity_limit of this FilesetForDescribeFilesetsOutput.


        :param capacity_limit: The capacity_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._capacity_limit = capacity_limit

    @property
    def capacity_used(self):
        """Gets the capacity_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The capacity_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._capacity_used

    @capacity_used.setter
    def capacity_used(self, capacity_used):
        """Sets the capacity_used of this FilesetForDescribeFilesetsOutput.


        :param capacity_used: The capacity_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._capacity_used = capacity_used

    @property
    def create_time(self):
        """Gets the create_time of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The create_time of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this FilesetForDescribeFilesetsOutput.


        :param create_time: The create_time of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def file_limit(self):
        """Gets the file_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The file_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_limit

    @file_limit.setter
    def file_limit(self, file_limit):
        """Sets the file_limit of this FilesetForDescribeFilesetsOutput.


        :param file_limit: The file_limit of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._file_limit = file_limit

    @property
    def file_system_id(self):
        """Gets the file_system_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The file_system_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this FilesetForDescribeFilesetsOutput.


        :param file_system_id: The file_system_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def file_used(self):
        """Gets the file_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The file_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_used

    @file_used.setter
    def file_used(self, file_used):
        """Sets the file_used of this FilesetForDescribeFilesetsOutput.


        :param file_used: The file_used of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._file_used = file_used

    @property
    def fileset_id(self):
        """Gets the fileset_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The fileset_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._fileset_id

    @fileset_id.setter
    def fileset_id(self, fileset_id):
        """Sets the fileset_id of this FilesetForDescribeFilesetsOutput.


        :param fileset_id: The fileset_id of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._fileset_id = fileset_id

    @property
    def fileset_name(self):
        """Gets the fileset_name of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The fileset_name of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._fileset_name

    @fileset_name.setter
    def fileset_name(self, fileset_name):
        """Sets the fileset_name of this FilesetForDescribeFilesetsOutput.


        :param fileset_name: The fileset_name of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._fileset_name = fileset_name

    @property
    def fileset_path(self):
        """Gets the fileset_path of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The fileset_path of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._fileset_path

    @fileset_path.setter
    def fileset_path(self, fileset_path):
        """Sets the fileset_path of this FilesetForDescribeFilesetsOutput.


        :param fileset_path: The fileset_path of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._fileset_path = fileset_path

    @property
    def iops_qos(self):
        """Gets the iops_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The iops_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._iops_qos

    @iops_qos.setter
    def iops_qos(self, iops_qos):
        """Sets the iops_qos of this FilesetForDescribeFilesetsOutput.


        :param iops_qos: The iops_qos of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._iops_qos = iops_qos

    @property
    def max_inode_num(self):
        """Gets the max_inode_num of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The max_inode_num of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_inode_num

    @max_inode_num.setter
    def max_inode_num(self, max_inode_num):
        """Sets the max_inode_num of this FilesetForDescribeFilesetsOutput.


        :param max_inode_num: The max_inode_num of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: int
        """

        self._max_inode_num = max_inode_num

    @property
    def status(self):
        """Gets the status of this FilesetForDescribeFilesetsOutput.  # noqa: E501


        :return: The status of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilesetForDescribeFilesetsOutput.


        :param status: The status of this FilesetForDescribeFilesetsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilesetForDescribeFilesetsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilesetForDescribeFilesetsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilesetForDescribeFilesetsOutput):
            return True

        return self.to_dict() != other.to_dict()
