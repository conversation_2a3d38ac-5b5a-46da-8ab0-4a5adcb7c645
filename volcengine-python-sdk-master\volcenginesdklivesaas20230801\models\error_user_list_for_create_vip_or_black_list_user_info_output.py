# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ErrorUserListForCreateVipOrBlackListUserInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'error_reason': 'str',
        'user_id': 'str',
        'user_name': 'str',
        'user_tel': 'str'
    }

    attribute_map = {
        'error_reason': 'ErrorReason',
        'user_id': 'UserId',
        'user_name': 'UserName',
        'user_tel': 'UserTel'
    }

    def __init__(self, error_reason=None, user_id=None, user_name=None, user_tel=None, _configuration=None):  # noqa: E501
        """ErrorUserListForCreateVipOrBlackListUserInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._error_reason = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self.discriminator = None

        if error_reason is not None:
            self.error_reason = error_reason
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel

    @property
    def error_reason(self):
        """Gets the error_reason of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501


        :return: The error_reason of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_reason

    @error_reason.setter
    def error_reason(self, error_reason):
        """Sets the error_reason of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.


        :param error_reason: The error_reason of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :type: str
        """

        self._error_reason = error_reason

    @property
    def user_id(self):
        """Gets the user_id of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501


        :return: The user_id of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.


        :param user_id: The user_id of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501


        :return: The user_name of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.


        :param user_name: The user_name of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501


        :return: The user_tel of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.


        :param user_tel: The user_tel of this ErrorUserListForCreateVipOrBlackListUserInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ErrorUserListForCreateVipOrBlackListUserInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ErrorUserListForCreateVipOrBlackListUserInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ErrorUserListForCreateVipOrBlackListUserInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
