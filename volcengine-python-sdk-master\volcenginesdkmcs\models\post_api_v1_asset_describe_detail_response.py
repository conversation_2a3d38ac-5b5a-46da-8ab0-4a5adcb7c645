# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PostApiV1AssetDescribeDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'business_tag_ids': 'list[str]',
        'cloud_product_type': 'str',
        'created_time_milli': 'int',
        'region': 'str',
        'resource_cloud_account_id': 'str',
        'resource_cloud_account_name': 'str',
        'resource_id': 'str',
        'resource_name': 'str',
        'resource_type': 'str',
        'resource_vendor': 'str',
        'security_labels_tag_ids': 'list[str]',
        'security_situation_tag_ids': 'list[str]',
        'updated_op_records': 'list[UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput]'
    }

    attribute_map = {
        'business_tag_ids': 'business_tag_ids',
        'cloud_product_type': 'cloud_product_type',
        'created_time_milli': 'created_time_milli',
        'region': 'region',
        'resource_cloud_account_id': 'resource_cloud_account_id',
        'resource_cloud_account_name': 'resource_cloud_account_name',
        'resource_id': 'resource_id',
        'resource_name': 'resource_name',
        'resource_type': 'resource_type',
        'resource_vendor': 'resource_vendor',
        'security_labels_tag_ids': 'security_labels_tag_ids',
        'security_situation_tag_ids': 'security_situation_tag_ids',
        'updated_op_records': 'updated_op_records'
    }

    def __init__(self, business_tag_ids=None, cloud_product_type=None, created_time_milli=None, region=None, resource_cloud_account_id=None, resource_cloud_account_name=None, resource_id=None, resource_name=None, resource_type=None, resource_vendor=None, security_labels_tag_ids=None, security_situation_tag_ids=None, updated_op_records=None, _configuration=None):  # noqa: E501
        """PostApiV1AssetDescribeDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._business_tag_ids = None
        self._cloud_product_type = None
        self._created_time_milli = None
        self._region = None
        self._resource_cloud_account_id = None
        self._resource_cloud_account_name = None
        self._resource_id = None
        self._resource_name = None
        self._resource_type = None
        self._resource_vendor = None
        self._security_labels_tag_ids = None
        self._security_situation_tag_ids = None
        self._updated_op_records = None
        self.discriminator = None

        if business_tag_ids is not None:
            self.business_tag_ids = business_tag_ids
        if cloud_product_type is not None:
            self.cloud_product_type = cloud_product_type
        if created_time_milli is not None:
            self.created_time_milli = created_time_milli
        if region is not None:
            self.region = region
        if resource_cloud_account_id is not None:
            self.resource_cloud_account_id = resource_cloud_account_id
        if resource_cloud_account_name is not None:
            self.resource_cloud_account_name = resource_cloud_account_name
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_name is not None:
            self.resource_name = resource_name
        if resource_type is not None:
            self.resource_type = resource_type
        if resource_vendor is not None:
            self.resource_vendor = resource_vendor
        if security_labels_tag_ids is not None:
            self.security_labels_tag_ids = security_labels_tag_ids
        if security_situation_tag_ids is not None:
            self.security_situation_tag_ids = security_situation_tag_ids
        if updated_op_records is not None:
            self.updated_op_records = updated_op_records

    @property
    def business_tag_ids(self):
        """Gets the business_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The business_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._business_tag_ids

    @business_tag_ids.setter
    def business_tag_ids(self, business_tag_ids):
        """Sets the business_tag_ids of this PostApiV1AssetDescribeDetailResponse.


        :param business_tag_ids: The business_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: list[str]
        """

        self._business_tag_ids = business_tag_ids

    @property
    def cloud_product_type(self):
        """Gets the cloud_product_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The cloud_product_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._cloud_product_type

    @cloud_product_type.setter
    def cloud_product_type(self, cloud_product_type):
        """Sets the cloud_product_type of this PostApiV1AssetDescribeDetailResponse.


        :param cloud_product_type: The cloud_product_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._cloud_product_type = cloud_product_type

    @property
    def created_time_milli(self):
        """Gets the created_time_milli of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The created_time_milli of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._created_time_milli

    @created_time_milli.setter
    def created_time_milli(self, created_time_milli):
        """Sets the created_time_milli of this PostApiV1AssetDescribeDetailResponse.


        :param created_time_milli: The created_time_milli of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: int
        """

        self._created_time_milli = created_time_milli

    @property
    def region(self):
        """Gets the region of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The region of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this PostApiV1AssetDescribeDetailResponse.


        :param region: The region of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def resource_cloud_account_id(self):
        """Gets the resource_cloud_account_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_cloud_account_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_cloud_account_id

    @resource_cloud_account_id.setter
    def resource_cloud_account_id(self, resource_cloud_account_id):
        """Sets the resource_cloud_account_id of this PostApiV1AssetDescribeDetailResponse.


        :param resource_cloud_account_id: The resource_cloud_account_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_cloud_account_id = resource_cloud_account_id

    @property
    def resource_cloud_account_name(self):
        """Gets the resource_cloud_account_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_cloud_account_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_cloud_account_name

    @resource_cloud_account_name.setter
    def resource_cloud_account_name(self, resource_cloud_account_name):
        """Sets the resource_cloud_account_name of this PostApiV1AssetDescribeDetailResponse.


        :param resource_cloud_account_name: The resource_cloud_account_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_cloud_account_name = resource_cloud_account_name

    @property
    def resource_id(self):
        """Gets the resource_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this PostApiV1AssetDescribeDetailResponse.


        :param resource_id: The resource_id of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_name(self):
        """Gets the resource_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_name

    @resource_name.setter
    def resource_name(self, resource_name):
        """Sets the resource_name of this PostApiV1AssetDescribeDetailResponse.


        :param resource_name: The resource_name of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_name = resource_name

    @property
    def resource_type(self):
        """Gets the resource_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this PostApiV1AssetDescribeDetailResponse.


        :param resource_type: The resource_type of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def resource_vendor(self):
        """Gets the resource_vendor of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The resource_vendor of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_vendor

    @resource_vendor.setter
    def resource_vendor(self, resource_vendor):
        """Sets the resource_vendor of this PostApiV1AssetDescribeDetailResponse.


        :param resource_vendor: The resource_vendor of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: str
        """

        self._resource_vendor = resource_vendor

    @property
    def security_labels_tag_ids(self):
        """Gets the security_labels_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The security_labels_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_labels_tag_ids

    @security_labels_tag_ids.setter
    def security_labels_tag_ids(self, security_labels_tag_ids):
        """Sets the security_labels_tag_ids of this PostApiV1AssetDescribeDetailResponse.


        :param security_labels_tag_ids: The security_labels_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: list[str]
        """

        self._security_labels_tag_ids = security_labels_tag_ids

    @property
    def security_situation_tag_ids(self):
        """Gets the security_situation_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The security_situation_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_situation_tag_ids

    @security_situation_tag_ids.setter
    def security_situation_tag_ids(self, security_situation_tag_ids):
        """Sets the security_situation_tag_ids of this PostApiV1AssetDescribeDetailResponse.


        :param security_situation_tag_ids: The security_situation_tag_ids of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: list[str]
        """

        self._security_situation_tag_ids = security_situation_tag_ids

    @property
    def updated_op_records(self):
        """Gets the updated_op_records of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501


        :return: The updated_op_records of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :rtype: list[UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput]
        """
        return self._updated_op_records

    @updated_op_records.setter
    def updated_op_records(self, updated_op_records):
        """Sets the updated_op_records of this PostApiV1AssetDescribeDetailResponse.


        :param updated_op_records: The updated_op_records of this PostApiV1AssetDescribeDetailResponse.  # noqa: E501
        :type: list[UpdatedOpRecordForPostApiV1AssetDescribeDetailOutput]
        """

        self._updated_op_records = updated_op_records

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PostApiV1AssetDescribeDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PostApiV1AssetDescribeDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PostApiV1AssetDescribeDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
