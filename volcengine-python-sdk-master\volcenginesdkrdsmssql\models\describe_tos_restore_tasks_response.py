# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTosRestoreTasksResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'restore_tasks': 'list[RestoreTaskForDescribeTosRestoreTasksOutput]',
        'total': 'int'
    }

    attribute_map = {
        'restore_tasks': 'RestoreTasks',
        'total': 'Total'
    }

    def __init__(self, restore_tasks=None, total=None, _configuration=None):  # noqa: E501
        """DescribeTosRestoreTasksResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._restore_tasks = None
        self._total = None
        self.discriminator = None

        if restore_tasks is not None:
            self.restore_tasks = restore_tasks
        if total is not None:
            self.total = total

    @property
    def restore_tasks(self):
        """Gets the restore_tasks of this DescribeTosRestoreTasksResponse.  # noqa: E501


        :return: The restore_tasks of this DescribeTosRestoreTasksResponse.  # noqa: E501
        :rtype: list[RestoreTaskForDescribeTosRestoreTasksOutput]
        """
        return self._restore_tasks

    @restore_tasks.setter
    def restore_tasks(self, restore_tasks):
        """Sets the restore_tasks of this DescribeTosRestoreTasksResponse.


        :param restore_tasks: The restore_tasks of this DescribeTosRestoreTasksResponse.  # noqa: E501
        :type: list[RestoreTaskForDescribeTosRestoreTasksOutput]
        """

        self._restore_tasks = restore_tasks

    @property
    def total(self):
        """Gets the total of this DescribeTosRestoreTasksResponse.  # noqa: E501


        :return: The total of this DescribeTosRestoreTasksResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeTosRestoreTasksResponse.


        :param total: The total of this DescribeTosRestoreTasksResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTosRestoreTasksResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTosRestoreTasksResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTosRestoreTasksResponse):
            return True

        return self.to_dict() != other.to_dict()
