# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForDeleteWhiteListsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_providers': 'list[str]',
        'desc': 'str',
        'leaf_group_ids': 'list[str]',
        'match_alarm_name': 'str',
        'match_content': 'str',
        'match_key': 'str',
        'name': 'str',
        'range': 'RangeForDeleteWhiteListsInput',
        'range_include_global': 'RangeIncludeGlobalForDeleteWhiteListsInput',
        'top_group_id': 'str',
        'update_time_end': 'int',
        'update_time_start': 'int',
        'user': 'str',
        'white_list_id': 'str'
    }

    attribute_map = {
        'cloud_providers': 'CloudProviders',
        'desc': 'Desc',
        'leaf_group_ids': 'LeafGroupIDs',
        'match_alarm_name': 'MatchAlarmName',
        'match_content': 'MatchContent',
        'match_key': 'MatchKey',
        'name': 'Name',
        'range': 'Range',
        'range_include_global': 'RangeIncludeGlobal',
        'top_group_id': 'TopGroupID',
        'update_time_end': 'UpdateTimeEnd',
        'update_time_start': 'UpdateTimeStart',
        'user': 'User',
        'white_list_id': 'WhiteListID'
    }

    def __init__(self, cloud_providers=None, desc=None, leaf_group_ids=None, match_alarm_name=None, match_content=None, match_key=None, name=None, range=None, range_include_global=None, top_group_id=None, update_time_end=None, update_time_start=None, user=None, white_list_id=None, _configuration=None):  # noqa: E501
        """ConditionsForDeleteWhiteListsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_providers = None
        self._desc = None
        self._leaf_group_ids = None
        self._match_alarm_name = None
        self._match_content = None
        self._match_key = None
        self._name = None
        self._range = None
        self._range_include_global = None
        self._top_group_id = None
        self._update_time_end = None
        self._update_time_start = None
        self._user = None
        self._white_list_id = None
        self.discriminator = None

        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if desc is not None:
            self.desc = desc
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if match_alarm_name is not None:
            self.match_alarm_name = match_alarm_name
        if match_content is not None:
            self.match_content = match_content
        if match_key is not None:
            self.match_key = match_key
        if name is not None:
            self.name = name
        if range is not None:
            self.range = range
        if range_include_global is not None:
            self.range_include_global = range_include_global
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if update_time_end is not None:
            self.update_time_end = update_time_end
        if update_time_start is not None:
            self.update_time_start = update_time_start
        if user is not None:
            self.user = user
        if white_list_id is not None:
            self.white_list_id = white_list_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The cloud_providers of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ConditionsForDeleteWhiteListsInput.


        :param cloud_providers: The cloud_providers of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def desc(self):
        """Gets the desc of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The desc of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this ConditionsForDeleteWhiteListsInput.


        :param desc: The desc of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The leaf_group_ids of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ConditionsForDeleteWhiteListsInput.


        :param leaf_group_ids: The leaf_group_ids of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def match_alarm_name(self):
        """Gets the match_alarm_name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The match_alarm_name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._match_alarm_name

    @match_alarm_name.setter
    def match_alarm_name(self, match_alarm_name):
        """Sets the match_alarm_name of this ConditionsForDeleteWhiteListsInput.


        :param match_alarm_name: The match_alarm_name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._match_alarm_name = match_alarm_name

    @property
    def match_content(self):
        """Gets the match_content of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The match_content of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._match_content

    @match_content.setter
    def match_content(self, match_content):
        """Sets the match_content of this ConditionsForDeleteWhiteListsInput.


        :param match_content: The match_content of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._match_content = match_content

    @property
    def match_key(self):
        """Gets the match_key of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The match_key of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._match_key

    @match_key.setter
    def match_key(self, match_key):
        """Sets the match_key of this ConditionsForDeleteWhiteListsInput.


        :param match_key: The match_key of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._match_key = match_key

    @property
    def name(self):
        """Gets the name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ConditionsForDeleteWhiteListsInput.


        :param name: The name of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def range(self):
        """Gets the range of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The range of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: RangeForDeleteWhiteListsInput
        """
        return self._range

    @range.setter
    def range(self, range):
        """Sets the range of this ConditionsForDeleteWhiteListsInput.


        :param range: The range of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: RangeForDeleteWhiteListsInput
        """

        self._range = range

    @property
    def range_include_global(self):
        """Gets the range_include_global of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The range_include_global of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: RangeIncludeGlobalForDeleteWhiteListsInput
        """
        return self._range_include_global

    @range_include_global.setter
    def range_include_global(self, range_include_global):
        """Sets the range_include_global of this ConditionsForDeleteWhiteListsInput.


        :param range_include_global: The range_include_global of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: RangeIncludeGlobalForDeleteWhiteListsInput
        """

        self._range_include_global = range_include_global

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The top_group_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ConditionsForDeleteWhiteListsInput.


        :param top_group_id: The top_group_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def update_time_end(self):
        """Gets the update_time_end of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The update_time_end of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: int
        """
        return self._update_time_end

    @update_time_end.setter
    def update_time_end(self, update_time_end):
        """Sets the update_time_end of this ConditionsForDeleteWhiteListsInput.


        :param update_time_end: The update_time_end of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: int
        """

        self._update_time_end = update_time_end

    @property
    def update_time_start(self):
        """Gets the update_time_start of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The update_time_start of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: int
        """
        return self._update_time_start

    @update_time_start.setter
    def update_time_start(self, update_time_start):
        """Sets the update_time_start of this ConditionsForDeleteWhiteListsInput.


        :param update_time_start: The update_time_start of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: int
        """

        self._update_time_start = update_time_start

    @property
    def user(self):
        """Gets the user of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The user of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this ConditionsForDeleteWhiteListsInput.


        :param user: The user of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._user = user

    @property
    def white_list_id(self):
        """Gets the white_list_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501


        :return: The white_list_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :rtype: str
        """
        return self._white_list_id

    @white_list_id.setter
    def white_list_id(self, white_list_id):
        """Sets the white_list_id of this ConditionsForDeleteWhiteListsInput.


        :param white_list_id: The white_list_id of this ConditionsForDeleteWhiteListsInput.  # noqa: E501
        :type: str
        """

        self._white_list_id = white_list_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForDeleteWhiteListsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForDeleteWhiteListsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForDeleteWhiteListsInput):
            return True

        return self.to_dict() != other.to_dict()
