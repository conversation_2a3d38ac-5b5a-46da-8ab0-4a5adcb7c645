# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigForGetInteractionScriptRecordConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_comment': 'bool',
        'enable_product_card': 'bool'
    }

    attribute_map = {
        'enable_comment': 'EnableComment',
        'enable_product_card': 'EnableProductCard'
    }

    def __init__(self, enable_comment=None, enable_product_card=None, _configuration=None):  # noqa: E501
        """ConfigForGetInteractionScriptRecordConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_comment = None
        self._enable_product_card = None
        self.discriminator = None

        if enable_comment is not None:
            self.enable_comment = enable_comment
        if enable_product_card is not None:
            self.enable_product_card = enable_product_card

    @property
    def enable_comment(self):
        """Gets the enable_comment of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501


        :return: The enable_comment of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_comment

    @enable_comment.setter
    def enable_comment(self, enable_comment):
        """Sets the enable_comment of this ConfigForGetInteractionScriptRecordConfigOutput.


        :param enable_comment: The enable_comment of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501
        :type: bool
        """

        self._enable_comment = enable_comment

    @property
    def enable_product_card(self):
        """Gets the enable_product_card of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501


        :return: The enable_product_card of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_product_card

    @enable_product_card.setter
    def enable_product_card(self, enable_product_card):
        """Sets the enable_product_card of this ConfigForGetInteractionScriptRecordConfigOutput.


        :param enable_product_card: The enable_product_card of this ConfigForGetInteractionScriptRecordConfigOutput.  # noqa: E501
        :type: bool
        """

        self._enable_product_card = enable_product_card

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigForGetInteractionScriptRecordConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigForGetInteractionScriptRecordConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigForGetInteractionScriptRecordConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
