# coding: utf-8

"""
    tis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AgentForGetAgentListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'name': 'str',
        'speaker_id': 'str',
        'speaker_type': 'str'
    }

    attribute_map = {
        'id': 'ID',
        'name': 'Name',
        'speaker_id': 'SpeakerID',
        'speaker_type': 'SpeakerType'
    }

    def __init__(self, id=None, name=None, speaker_id=None, speaker_type=None, _configuration=None):  # noqa: E501
        """AgentForGetAgentListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._speaker_id = None
        self._speaker_type = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if speaker_id is not None:
            self.speaker_id = speaker_id
        if speaker_type is not None:
            self.speaker_type = speaker_type

    @property
    def id(self):
        """Gets the id of this AgentForGetAgentListOutput.  # noqa: E501


        :return: The id of this AgentForGetAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this AgentForGetAgentListOutput.


        :param id: The id of this AgentForGetAgentListOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this AgentForGetAgentListOutput.  # noqa: E501


        :return: The name of this AgentForGetAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this AgentForGetAgentListOutput.


        :param name: The name of this AgentForGetAgentListOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def speaker_id(self):
        """Gets the speaker_id of this AgentForGetAgentListOutput.  # noqa: E501


        :return: The speaker_id of this AgentForGetAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._speaker_id

    @speaker_id.setter
    def speaker_id(self, speaker_id):
        """Sets the speaker_id of this AgentForGetAgentListOutput.


        :param speaker_id: The speaker_id of this AgentForGetAgentListOutput.  # noqa: E501
        :type: str
        """

        self._speaker_id = speaker_id

    @property
    def speaker_type(self):
        """Gets the speaker_type of this AgentForGetAgentListOutput.  # noqa: E501


        :return: The speaker_type of this AgentForGetAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._speaker_type

    @speaker_type.setter
    def speaker_type(self, speaker_type):
        """Sets the speaker_type of this AgentForGetAgentListOutput.


        :param speaker_type: The speaker_type of this AgentForGetAgentListOutput.  # noqa: E501
        :type: str
        """

        self._speaker_type = speaker_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AgentForGetAgentListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AgentForGetAgentListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AgentForGetAgentListOutput):
            return True

        return self.to_dict() != other.to_dict()
