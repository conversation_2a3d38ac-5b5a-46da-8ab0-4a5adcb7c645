# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatForGetApiV1OverviewAlarmStatsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'count': 'int',
        '_date': 'str'
    }

    attribute_map = {
        'count': 'count',
        '_date': 'date'
    }

    def __init__(self, count=None, _date=None, _configuration=None):  # noqa: E501
        """StatForGetApiV1OverviewAlarmStatsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._count = None
        self.__date = None
        self.discriminator = None

        if count is not None:
            self.count = count
        if _date is not None:
            self._date = _date

    @property
    def count(self):
        """Gets the count of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501


        :return: The count of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this StatForGetApiV1OverviewAlarmStatsOutput.


        :param count: The count of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def _date(self):
        """Gets the _date of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501


        :return: The _date of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501
        :rtype: str
        """
        return self.__date

    @_date.setter
    def _date(self, _date):
        """Sets the _date of this StatForGetApiV1OverviewAlarmStatsOutput.


        :param _date: The _date of this StatForGetApiV1OverviewAlarmStatsOutput.  # noqa: E501
        :type: str
        """

        self.__date = _date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatForGetApiV1OverviewAlarmStatsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatForGetApiV1OverviewAlarmStatsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatForGetApiV1OverviewAlarmStatsOutput):
            return True

        return self.to_dict() != other.to_dict()
