# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCloudAccountsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'pagination': 'PaginationForListCloudAccountsInput',
        'read_only': 'bool',
        'sort_by': 'list[str]',
        'sync_status': 'str',
        'vendor': 'str',
        'vendor_type': 'str'
    }

    attribute_map = {
        'name': 'Name',
        'pagination': 'Pagination',
        'read_only': 'ReadOnly',
        'sort_by': 'SortBy',
        'sync_status': 'SyncStatus',
        'vendor': 'Vendor',
        'vendor_type': 'VendorType'
    }

    def __init__(self, name=None, pagination=None, read_only=None, sort_by=None, sync_status=None, vendor=None, vendor_type=None, _configuration=None):  # noqa: E501
        """ListCloudAccountsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._pagination = None
        self._read_only = None
        self._sort_by = None
        self._sync_status = None
        self._vendor = None
        self._vendor_type = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if pagination is not None:
            self.pagination = pagination
        if read_only is not None:
            self.read_only = read_only
        if sort_by is not None:
            self.sort_by = sort_by
        if sync_status is not None:
            self.sync_status = sync_status
        if vendor is not None:
            self.vendor = vendor
        if vendor_type is not None:
            self.vendor_type = vendor_type

    @property
    def name(self):
        """Gets the name of this ListCloudAccountsRequest.  # noqa: E501


        :return: The name of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListCloudAccountsRequest.


        :param name: The name of this ListCloudAccountsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def pagination(self):
        """Gets the pagination of this ListCloudAccountsRequest.  # noqa: E501


        :return: The pagination of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: PaginationForListCloudAccountsInput
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this ListCloudAccountsRequest.


        :param pagination: The pagination of this ListCloudAccountsRequest.  # noqa: E501
        :type: PaginationForListCloudAccountsInput
        """

        self._pagination = pagination

    @property
    def read_only(self):
        """Gets the read_only of this ListCloudAccountsRequest.  # noqa: E501


        :return: The read_only of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._read_only

    @read_only.setter
    def read_only(self, read_only):
        """Sets the read_only of this ListCloudAccountsRequest.


        :param read_only: The read_only of this ListCloudAccountsRequest.  # noqa: E501
        :type: bool
        """

        self._read_only = read_only

    @property
    def sort_by(self):
        """Gets the sort_by of this ListCloudAccountsRequest.  # noqa: E501


        :return: The sort_by of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListCloudAccountsRequest.


        :param sort_by: The sort_by of this ListCloudAccountsRequest.  # noqa: E501
        :type: list[str]
        """

        self._sort_by = sort_by

    @property
    def sync_status(self):
        """Gets the sync_status of this ListCloudAccountsRequest.  # noqa: E501


        :return: The sync_status of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sync_status

    @sync_status.setter
    def sync_status(self, sync_status):
        """Sets the sync_status of this ListCloudAccountsRequest.


        :param sync_status: The sync_status of this ListCloudAccountsRequest.  # noqa: E501
        :type: str
        """

        self._sync_status = sync_status

    @property
    def vendor(self):
        """Gets the vendor of this ListCloudAccountsRequest.  # noqa: E501


        :return: The vendor of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this ListCloudAccountsRequest.


        :param vendor: The vendor of this ListCloudAccountsRequest.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    @property
    def vendor_type(self):
        """Gets the vendor_type of this ListCloudAccountsRequest.  # noqa: E501


        :return: The vendor_type of this ListCloudAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vendor_type

    @vendor_type.setter
    def vendor_type(self, vendor_type):
        """Sets the vendor_type of this ListCloudAccountsRequest.


        :param vendor_type: The vendor_type of this ListCloudAccountsRequest.  # noqa: E501
        :type: str
        """

        self._vendor_type = vendor_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCloudAccountsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCloudAccountsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCloudAccountsRequest):
            return True

        return self.to_dict() != other.to_dict()
