# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RewardsPointsConfigForCreateActivityRedPacketInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'amount_calculation_type': 'int',
        'fixed_reward_point_configs': 'list[FixedRewardPointConfigForCreateActivityRedPacketInput]',
        'rewards_points_batch': 'str',
        'rewards_points_unit': 'str'
    }

    attribute_map = {
        'amount_calculation_type': 'AmountCalculationType',
        'fixed_reward_point_configs': 'FixedRewardPointConfigs',
        'rewards_points_batch': 'RewardsPointsBatch',
        'rewards_points_unit': 'RewardsPointsUnit'
    }

    def __init__(self, amount_calculation_type=None, fixed_reward_point_configs=None, rewards_points_batch=None, rewards_points_unit=None, _configuration=None):  # noqa: E501
        """RewardsPointsConfigForCreateActivityRedPacketInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._amount_calculation_type = None
        self._fixed_reward_point_configs = None
        self._rewards_points_batch = None
        self._rewards_points_unit = None
        self.discriminator = None

        if amount_calculation_type is not None:
            self.amount_calculation_type = amount_calculation_type
        if fixed_reward_point_configs is not None:
            self.fixed_reward_point_configs = fixed_reward_point_configs
        if rewards_points_batch is not None:
            self.rewards_points_batch = rewards_points_batch
        if rewards_points_unit is not None:
            self.rewards_points_unit = rewards_points_unit

    @property
    def amount_calculation_type(self):
        """Gets the amount_calculation_type of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501


        :return: The amount_calculation_type of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :rtype: int
        """
        return self._amount_calculation_type

    @amount_calculation_type.setter
    def amount_calculation_type(self, amount_calculation_type):
        """Sets the amount_calculation_type of this RewardsPointsConfigForCreateActivityRedPacketInput.


        :param amount_calculation_type: The amount_calculation_type of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :type: int
        """

        self._amount_calculation_type = amount_calculation_type

    @property
    def fixed_reward_point_configs(self):
        """Gets the fixed_reward_point_configs of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501


        :return: The fixed_reward_point_configs of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :rtype: list[FixedRewardPointConfigForCreateActivityRedPacketInput]
        """
        return self._fixed_reward_point_configs

    @fixed_reward_point_configs.setter
    def fixed_reward_point_configs(self, fixed_reward_point_configs):
        """Sets the fixed_reward_point_configs of this RewardsPointsConfigForCreateActivityRedPacketInput.


        :param fixed_reward_point_configs: The fixed_reward_point_configs of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :type: list[FixedRewardPointConfigForCreateActivityRedPacketInput]
        """

        self._fixed_reward_point_configs = fixed_reward_point_configs

    @property
    def rewards_points_batch(self):
        """Gets the rewards_points_batch of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501


        :return: The rewards_points_batch of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :rtype: str
        """
        return self._rewards_points_batch

    @rewards_points_batch.setter
    def rewards_points_batch(self, rewards_points_batch):
        """Sets the rewards_points_batch of this RewardsPointsConfigForCreateActivityRedPacketInput.


        :param rewards_points_batch: The rewards_points_batch of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :type: str
        """

        self._rewards_points_batch = rewards_points_batch

    @property
    def rewards_points_unit(self):
        """Gets the rewards_points_unit of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501


        :return: The rewards_points_unit of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :rtype: str
        """
        return self._rewards_points_unit

    @rewards_points_unit.setter
    def rewards_points_unit(self, rewards_points_unit):
        """Sets the rewards_points_unit of this RewardsPointsConfigForCreateActivityRedPacketInput.


        :param rewards_points_unit: The rewards_points_unit of this RewardsPointsConfigForCreateActivityRedPacketInput.  # noqa: E501
        :type: str
        """

        self._rewards_points_unit = rewards_points_unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RewardsPointsConfigForCreateActivityRedPacketInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RewardsPointsConfigForCreateActivityRedPacketInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RewardsPointsConfigForCreateActivityRedPacketInput):
            return True

        return self.to_dict() != other.to_dict()
