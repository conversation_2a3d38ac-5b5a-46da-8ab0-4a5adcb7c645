# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetSubAccountResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'nick_name': 'str',
        'organization_id': 'int',
        'organization_name': 'str',
        'roles': 'list[RoleForGetSubAccountOutput]',
        'type': 'int'
    }

    attribute_map = {
        'name': 'Name',
        'nick_name': 'NickName',
        'organization_id': 'OrganizationId',
        'organization_name': 'OrganizationName',
        'roles': 'Roles',
        'type': 'Type'
    }

    def __init__(self, name=None, nick_name=None, organization_id=None, organization_name=None, roles=None, type=None, _configuration=None):  # noqa: E501
        """GetSubAccountResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._nick_name = None
        self._organization_id = None
        self._organization_name = None
        self._roles = None
        self._type = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if nick_name is not None:
            self.nick_name = nick_name
        if organization_id is not None:
            self.organization_id = organization_id
        if organization_name is not None:
            self.organization_name = organization_name
        if roles is not None:
            self.roles = roles
        if type is not None:
            self.type = type

    @property
    def name(self):
        """Gets the name of this GetSubAccountResponse.  # noqa: E501


        :return: The name of this GetSubAccountResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetSubAccountResponse.


        :param name: The name of this GetSubAccountResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def nick_name(self):
        """Gets the nick_name of this GetSubAccountResponse.  # noqa: E501


        :return: The nick_name of this GetSubAccountResponse.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this GetSubAccountResponse.


        :param nick_name: The nick_name of this GetSubAccountResponse.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def organization_id(self):
        """Gets the organization_id of this GetSubAccountResponse.  # noqa: E501


        :return: The organization_id of this GetSubAccountResponse.  # noqa: E501
        :rtype: int
        """
        return self._organization_id

    @organization_id.setter
    def organization_id(self, organization_id):
        """Sets the organization_id of this GetSubAccountResponse.


        :param organization_id: The organization_id of this GetSubAccountResponse.  # noqa: E501
        :type: int
        """

        self._organization_id = organization_id

    @property
    def organization_name(self):
        """Gets the organization_name of this GetSubAccountResponse.  # noqa: E501


        :return: The organization_name of this GetSubAccountResponse.  # noqa: E501
        :rtype: str
        """
        return self._organization_name

    @organization_name.setter
    def organization_name(self, organization_name):
        """Sets the organization_name of this GetSubAccountResponse.


        :param organization_name: The organization_name of this GetSubAccountResponse.  # noqa: E501
        :type: str
        """

        self._organization_name = organization_name

    @property
    def roles(self):
        """Gets the roles of this GetSubAccountResponse.  # noqa: E501


        :return: The roles of this GetSubAccountResponse.  # noqa: E501
        :rtype: list[RoleForGetSubAccountOutput]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this GetSubAccountResponse.


        :param roles: The roles of this GetSubAccountResponse.  # noqa: E501
        :type: list[RoleForGetSubAccountOutput]
        """

        self._roles = roles

    @property
    def type(self):
        """Gets the type of this GetSubAccountResponse.  # noqa: E501


        :return: The type of this GetSubAccountResponse.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this GetSubAccountResponse.


        :param type: The type of this GetSubAccountResponse.  # noqa: E501
        :type: int
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetSubAccountResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetSubAccountResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetSubAccountResponse):
            return True

        return self.to_dict() != other.to_dict()
