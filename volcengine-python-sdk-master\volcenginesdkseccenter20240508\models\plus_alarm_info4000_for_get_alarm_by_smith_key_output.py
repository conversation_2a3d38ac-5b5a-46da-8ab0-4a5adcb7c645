# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sip': 'str',
        'sport': 'str',
        'types': 'str',
        'user': 'str'
    }

    attribute_map = {
        'sip': 'Sip',
        'sport': 'Sport',
        'types': 'Types',
        'user': 'User'
    }

    def __init__(self, sip=None, sport=None, types=None, user=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sip = None
        self._sport = None
        self._types = None
        self._user = None
        self.discriminator = None

        if sip is not None:
            self.sip = sip
        if sport is not None:
            self.sport = sport
        if types is not None:
            self.types = types
        if user is not None:
            self.user = user

    @property
    def sip(self):
        """Gets the sip of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The sip of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip

    @sip.setter
    def sip(self, sip):
        """Sets the sip of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.


        :param sip: The sip of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._sip = sip

    @property
    def sport(self):
        """Gets the sport of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The sport of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._sport

    @sport.setter
    def sport(self, sport):
        """Sets the sport of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.


        :param sport: The sport of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._sport = sport

    @property
    def types(self):
        """Gets the types of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The types of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.


        :param types: The types of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._types = types

    @property
    def user(self):
        """Gets the user of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The user of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.


        :param user: The user of this PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo4000ForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
