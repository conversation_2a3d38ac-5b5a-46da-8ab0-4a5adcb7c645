# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDirectConnectConnectionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'client_token': 'str',
        'customer_contact_email': 'str',
        'customer_contact_phone': 'str',
        'customer_name': 'str',
        'description': 'str',
        'direct_connect_access_point_id': 'str',
        'direct_connect_connection_name': 'str',
        'driect_connect_access_point_id': 'str',
        'line_operator': 'str',
        'peer_location': 'str',
        'port_spec': 'str',
        'port_type': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateDirectConnectConnectionInput]'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'client_token': 'ClientToken',
        'customer_contact_email': 'CustomerContactEmail',
        'customer_contact_phone': 'CustomerContactPhone',
        'customer_name': 'CustomerName',
        'description': 'Description',
        'direct_connect_access_point_id': 'DirectConnectAccessPointId',
        'direct_connect_connection_name': 'DirectConnectConnectionName',
        'driect_connect_access_point_id': 'DriectConnectAccessPointId',
        'line_operator': 'LineOperator',
        'peer_location': 'PeerLocation',
        'port_spec': 'PortSpec',
        'port_type': 'PortType',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, bandwidth=None, client_token=None, customer_contact_email=None, customer_contact_phone=None, customer_name=None, description=None, direct_connect_access_point_id=None, direct_connect_connection_name=None, driect_connect_access_point_id=None, line_operator=None, peer_location=None, port_spec=None, port_type=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """CreateDirectConnectConnectionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._client_token = None
        self._customer_contact_email = None
        self._customer_contact_phone = None
        self._customer_name = None
        self._description = None
        self._direct_connect_access_point_id = None
        self._direct_connect_connection_name = None
        self._driect_connect_access_point_id = None
        self._line_operator = None
        self._peer_location = None
        self._port_spec = None
        self._port_type = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        self.bandwidth = bandwidth
        if client_token is not None:
            self.client_token = client_token
        self.customer_contact_email = customer_contact_email
        self.customer_contact_phone = customer_contact_phone
        self.customer_name = customer_name
        if description is not None:
            self.description = description
        self.direct_connect_access_point_id = direct_connect_access_point_id
        if direct_connect_connection_name is not None:
            self.direct_connect_connection_name = direct_connect_connection_name
        if driect_connect_access_point_id is not None:
            self.driect_connect_access_point_id = driect_connect_access_point_id
        self.line_operator = line_operator
        self.peer_location = peer_location
        self.port_spec = port_spec
        self.port_type = port_type
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def bandwidth(self):
        """Gets the bandwidth of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The bandwidth of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this CreateDirectConnectConnectionRequest.


        :param bandwidth: The bandwidth of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth is None:
            raise ValueError("Invalid value for `bandwidth`, must not be `None`")  # noqa: E501

        self._bandwidth = bandwidth

    @property
    def client_token(self):
        """Gets the client_token of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The client_token of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateDirectConnectConnectionRequest.


        :param client_token: The client_token of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def customer_contact_email(self):
        """Gets the customer_contact_email of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The customer_contact_email of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_email

    @customer_contact_email.setter
    def customer_contact_email(self, customer_contact_email):
        """Sets the customer_contact_email of this CreateDirectConnectConnectionRequest.


        :param customer_contact_email: The customer_contact_email of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and customer_contact_email is None:
            raise ValueError("Invalid value for `customer_contact_email`, must not be `None`")  # noqa: E501

        self._customer_contact_email = customer_contact_email

    @property
    def customer_contact_phone(self):
        """Gets the customer_contact_phone of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The customer_contact_phone of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_phone

    @customer_contact_phone.setter
    def customer_contact_phone(self, customer_contact_phone):
        """Sets the customer_contact_phone of this CreateDirectConnectConnectionRequest.


        :param customer_contact_phone: The customer_contact_phone of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and customer_contact_phone is None:
            raise ValueError("Invalid value for `customer_contact_phone`, must not be `None`")  # noqa: E501

        self._customer_contact_phone = customer_contact_phone

    @property
    def customer_name(self):
        """Gets the customer_name of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The customer_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_name

    @customer_name.setter
    def customer_name(self, customer_name):
        """Sets the customer_name of this CreateDirectConnectConnectionRequest.


        :param customer_name: The customer_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and customer_name is None:
            raise ValueError("Invalid value for `customer_name`, must not be `None`")  # noqa: E501

        self._customer_name = customer_name

    @property
    def description(self):
        """Gets the description of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The description of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDirectConnectConnectionRequest.


        :param description: The description of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_access_point_id(self):
        """Gets the direct_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The direct_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_access_point_id

    @direct_connect_access_point_id.setter
    def direct_connect_access_point_id(self, direct_connect_access_point_id):
        """Sets the direct_connect_access_point_id of this CreateDirectConnectConnectionRequest.


        :param direct_connect_access_point_id: The direct_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direct_connect_access_point_id is None:
            raise ValueError("Invalid value for `direct_connect_access_point_id`, must not be `None`")  # noqa: E501

        self._direct_connect_access_point_id = direct_connect_access_point_id

    @property
    def direct_connect_connection_name(self):
        """Gets the direct_connect_connection_name of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The direct_connect_connection_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_name

    @direct_connect_connection_name.setter
    def direct_connect_connection_name(self, direct_connect_connection_name):
        """Sets the direct_connect_connection_name of this CreateDirectConnectConnectionRequest.


        :param direct_connect_connection_name: The direct_connect_connection_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_connection_name = direct_connect_connection_name

    @property
    def driect_connect_access_point_id(self):
        """Gets the driect_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The driect_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._driect_connect_access_point_id

    @driect_connect_access_point_id.setter
    def driect_connect_access_point_id(self, driect_connect_access_point_id):
        """Sets the driect_connect_access_point_id of this CreateDirectConnectConnectionRequest.


        :param driect_connect_access_point_id: The driect_connect_access_point_id of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """

        self._driect_connect_access_point_id = driect_connect_access_point_id

    @property
    def line_operator(self):
        """Gets the line_operator of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The line_operator of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._line_operator

    @line_operator.setter
    def line_operator(self, line_operator):
        """Sets the line_operator of this CreateDirectConnectConnectionRequest.


        :param line_operator: The line_operator of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and line_operator is None:
            raise ValueError("Invalid value for `line_operator`, must not be `None`")  # noqa: E501

        self._line_operator = line_operator

    @property
    def peer_location(self):
        """Gets the peer_location of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The peer_location of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_location

    @peer_location.setter
    def peer_location(self, peer_location):
        """Sets the peer_location of this CreateDirectConnectConnectionRequest.


        :param peer_location: The peer_location of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_location is None:
            raise ValueError("Invalid value for `peer_location`, must not be `None`")  # noqa: E501

        self._peer_location = peer_location

    @property
    def port_spec(self):
        """Gets the port_spec of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The port_spec of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._port_spec

    @port_spec.setter
    def port_spec(self, port_spec):
        """Sets the port_spec of this CreateDirectConnectConnectionRequest.


        :param port_spec: The port_spec of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and port_spec is None:
            raise ValueError("Invalid value for `port_spec`, must not be `None`")  # noqa: E501

        self._port_spec = port_spec

    @property
    def port_type(self):
        """Gets the port_type of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The port_type of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._port_type

    @port_type.setter
    def port_type(self, port_type):
        """Sets the port_type of this CreateDirectConnectConnectionRequest.


        :param port_type: The port_type of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and port_type is None:
            raise ValueError("Invalid value for `port_type`, must not be `None`")  # noqa: E501

        self._port_type = port_type

    @property
    def project_name(self):
        """Gets the project_name of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The project_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateDirectConnectConnectionRequest.


        :param project_name: The project_name of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateDirectConnectConnectionRequest.  # noqa: E501


        :return: The tags of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :rtype: list[TagForCreateDirectConnectConnectionInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateDirectConnectConnectionRequest.


        :param tags: The tags of this CreateDirectConnectConnectionRequest.  # noqa: E501
        :type: list[TagForCreateDirectConnectConnectionInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDirectConnectConnectionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDirectConnectConnectionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDirectConnectConnectionRequest):
            return True

        return self.to_dict() != other.to_dict()
