# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateEDXPeerLinkRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_pkg_id': 'str',
        'bandwidth_size': 'int',
        'edx_instance_id': 'str',
        'end_vgw_instance_id': 'str',
        'forced': 'bool',
        'start_vgw_instance_id': 'str'
    }

    attribute_map = {
        'bandwidth_pkg_id': 'BandwidthPkgId',
        'bandwidth_size': 'BandwidthSize',
        'edx_instance_id': 'EDXInstanceId',
        'end_vgw_instance_id': 'EndVGWInstanceId',
        'forced': 'Forced',
        'start_vgw_instance_id': 'StartVGWInstanceId'
    }

    def __init__(self, bandwidth_pkg_id=None, bandwidth_size=None, edx_instance_id=None, end_vgw_instance_id=None, forced=None, start_vgw_instance_id=None, _configuration=None):  # noqa: E501
        """CreateEDXPeerLinkRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_pkg_id = None
        self._bandwidth_size = None
        self._edx_instance_id = None
        self._end_vgw_instance_id = None
        self._forced = None
        self._start_vgw_instance_id = None
        self.discriminator = None

        self.bandwidth_pkg_id = bandwidth_pkg_id
        self.bandwidth_size = bandwidth_size
        self.edx_instance_id = edx_instance_id
        self.end_vgw_instance_id = end_vgw_instance_id
        self.forced = forced
        self.start_vgw_instance_id = start_vgw_instance_id

    @property
    def bandwidth_pkg_id(self):
        """Gets the bandwidth_pkg_id of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The bandwidth_pkg_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_pkg_id

    @bandwidth_pkg_id.setter
    def bandwidth_pkg_id(self, bandwidth_pkg_id):
        """Sets the bandwidth_pkg_id of this CreateEDXPeerLinkRequest.


        :param bandwidth_pkg_id: The bandwidth_pkg_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and bandwidth_pkg_id is None:
            raise ValueError("Invalid value for `bandwidth_pkg_id`, must not be `None`")  # noqa: E501

        self._bandwidth_pkg_id = bandwidth_pkg_id

    @property
    def bandwidth_size(self):
        """Gets the bandwidth_size of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The bandwidth_size of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_size

    @bandwidth_size.setter
    def bandwidth_size(self, bandwidth_size):
        """Sets the bandwidth_size of this CreateEDXPeerLinkRequest.


        :param bandwidth_size: The bandwidth_size of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth_size is None:
            raise ValueError("Invalid value for `bandwidth_size`, must not be `None`")  # noqa: E501

        self._bandwidth_size = bandwidth_size

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The edx_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this CreateEDXPeerLinkRequest.


        :param edx_instance_id: The edx_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and edx_instance_id is None:
            raise ValueError("Invalid value for `edx_instance_id`, must not be `None`")  # noqa: E501

        self._edx_instance_id = edx_instance_id

    @property
    def end_vgw_instance_id(self):
        """Gets the end_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The end_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_vgw_instance_id

    @end_vgw_instance_id.setter
    def end_vgw_instance_id(self, end_vgw_instance_id):
        """Sets the end_vgw_instance_id of this CreateEDXPeerLinkRequest.


        :param end_vgw_instance_id: The end_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_vgw_instance_id is None:
            raise ValueError("Invalid value for `end_vgw_instance_id`, must not be `None`")  # noqa: E501

        self._end_vgw_instance_id = end_vgw_instance_id

    @property
    def forced(self):
        """Gets the forced of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The forced of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: bool
        """
        return self._forced

    @forced.setter
    def forced(self, forced):
        """Sets the forced of this CreateEDXPeerLinkRequest.


        :param forced: The forced of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and forced is None:
            raise ValueError("Invalid value for `forced`, must not be `None`")  # noqa: E501

        self._forced = forced

    @property
    def start_vgw_instance_id(self):
        """Gets the start_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501


        :return: The start_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_vgw_instance_id

    @start_vgw_instance_id.setter
    def start_vgw_instance_id(self, start_vgw_instance_id):
        """Sets the start_vgw_instance_id of this CreateEDXPeerLinkRequest.


        :param start_vgw_instance_id: The start_vgw_instance_id of this CreateEDXPeerLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_vgw_instance_id is None:
            raise ValueError("Invalid value for `start_vgw_instance_id`, must not be `None`")  # noqa: E501

        self._start_vgw_instance_id = start_vgw_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateEDXPeerLinkRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateEDXPeerLinkRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateEDXPeerLinkRequest):
            return True

        return self.to_dict() != other.to_dict()
