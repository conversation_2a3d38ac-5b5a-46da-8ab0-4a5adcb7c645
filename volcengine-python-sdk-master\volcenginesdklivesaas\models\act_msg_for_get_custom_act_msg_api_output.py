# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActMsgForGetCustomActMsgAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_image': 'str',
        'id': 'int',
        'is_lock_preview': 'int',
        'live_review_status': 'int',
        'live_time': 'int',
        'name': 'str',
        'owner_sub_account': 'str',
        'status': 'int',
        'vertical_cover_image': 'str',
        'view_url': 'str'
    }

    attribute_map = {
        'cover_image': 'CoverImage',
        'id': 'Id',
        'is_lock_preview': 'IsLockPreview',
        'live_review_status': 'LiveReviewStatus',
        'live_time': 'LiveTime',
        'name': 'Name',
        'owner_sub_account': 'OwnerSubAccount',
        'status': 'Status',
        'vertical_cover_image': 'VerticalCoverImage',
        'view_url': 'ViewUrl'
    }

    def __init__(self, cover_image=None, id=None, is_lock_preview=None, live_review_status=None, live_time=None, name=None, owner_sub_account=None, status=None, vertical_cover_image=None, view_url=None, _configuration=None):  # noqa: E501
        """ActMsgForGetCustomActMsgAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_image = None
        self._id = None
        self._is_lock_preview = None
        self._live_review_status = None
        self._live_time = None
        self._name = None
        self._owner_sub_account = None
        self._status = None
        self._vertical_cover_image = None
        self._view_url = None
        self.discriminator = None

        if cover_image is not None:
            self.cover_image = cover_image
        if id is not None:
            self.id = id
        if is_lock_preview is not None:
            self.is_lock_preview = is_lock_preview
        if live_review_status is not None:
            self.live_review_status = live_review_status
        if live_time is not None:
            self.live_time = live_time
        if name is not None:
            self.name = name
        if owner_sub_account is not None:
            self.owner_sub_account = owner_sub_account
        if status is not None:
            self.status = status
        if vertical_cover_image is not None:
            self.vertical_cover_image = vertical_cover_image
        if view_url is not None:
            self.view_url = view_url

    @property
    def cover_image(self):
        """Gets the cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this ActMsgForGetCustomActMsgAPIOutput.


        :param cover_image: The cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def id(self):
        """Gets the id of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The id of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ActMsgForGetCustomActMsgAPIOutput.


        :param id: The id of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_lock_preview(self):
        """Gets the is_lock_preview of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The is_lock_preview of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_lock_preview

    @is_lock_preview.setter
    def is_lock_preview(self, is_lock_preview):
        """Sets the is_lock_preview of this ActMsgForGetCustomActMsgAPIOutput.


        :param is_lock_preview: The is_lock_preview of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_lock_preview = is_lock_preview

    @property
    def live_review_status(self):
        """Gets the live_review_status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The live_review_status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_review_status

    @live_review_status.setter
    def live_review_status(self, live_review_status):
        """Sets the live_review_status of this ActMsgForGetCustomActMsgAPIOutput.


        :param live_review_status: The live_review_status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_review_status = live_review_status

    @property
    def live_time(self):
        """Gets the live_time of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The live_time of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this ActMsgForGetCustomActMsgAPIOutput.


        :param live_time: The live_time of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def name(self):
        """Gets the name of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The name of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ActMsgForGetCustomActMsgAPIOutput.


        :param name: The name of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner_sub_account(self):
        """Gets the owner_sub_account of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The owner_sub_account of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_sub_account

    @owner_sub_account.setter
    def owner_sub_account(self, owner_sub_account):
        """Sets the owner_sub_account of this ActMsgForGetCustomActMsgAPIOutput.


        :param owner_sub_account: The owner_sub_account of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._owner_sub_account = owner_sub_account

    @property
    def status(self):
        """Gets the status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ActMsgForGetCustomActMsgAPIOutput.


        :param status: The status of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def vertical_cover_image(self):
        """Gets the vertical_cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The vertical_cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._vertical_cover_image

    @vertical_cover_image.setter
    def vertical_cover_image(self, vertical_cover_image):
        """Sets the vertical_cover_image of this ActMsgForGetCustomActMsgAPIOutput.


        :param vertical_cover_image: The vertical_cover_image of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._vertical_cover_image = vertical_cover_image

    @property
    def view_url(self):
        """Gets the view_url of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The view_url of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._view_url

    @view_url.setter
    def view_url(self, view_url):
        """Sets the view_url of this ActMsgForGetCustomActMsgAPIOutput.


        :param view_url: The view_url of this ActMsgForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._view_url = view_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActMsgForGetCustomActMsgAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActMsgForGetCustomActMsgAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActMsgForGetCustomActMsgAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
