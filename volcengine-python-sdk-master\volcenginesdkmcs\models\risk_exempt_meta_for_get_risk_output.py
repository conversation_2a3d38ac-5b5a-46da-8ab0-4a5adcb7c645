# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RiskExemptMetaForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'exempt_desc': 'str',
        'exempt_expire_time_milli': 'int',
        'exempt_reason_type': 'str'
    }

    attribute_map = {
        'exempt_desc': 'ExemptDesc',
        'exempt_expire_time_milli': 'ExemptExpireTimeMilli',
        'exempt_reason_type': 'ExemptReasonType'
    }

    def __init__(self, exempt_desc=None, exempt_expire_time_milli=None, exempt_reason_type=None, _configuration=None):  # noqa: E501
        """RiskExemptMetaForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._exempt_desc = None
        self._exempt_expire_time_milli = None
        self._exempt_reason_type = None
        self.discriminator = None

        if exempt_desc is not None:
            self.exempt_desc = exempt_desc
        if exempt_expire_time_milli is not None:
            self.exempt_expire_time_milli = exempt_expire_time_milli
        if exempt_reason_type is not None:
            self.exempt_reason_type = exempt_reason_type

    @property
    def exempt_desc(self):
        """Gets the exempt_desc of this RiskExemptMetaForGetRiskOutput.  # noqa: E501


        :return: The exempt_desc of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._exempt_desc

    @exempt_desc.setter
    def exempt_desc(self, exempt_desc):
        """Sets the exempt_desc of this RiskExemptMetaForGetRiskOutput.


        :param exempt_desc: The exempt_desc of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._exempt_desc = exempt_desc

    @property
    def exempt_expire_time_milli(self):
        """Gets the exempt_expire_time_milli of this RiskExemptMetaForGetRiskOutput.  # noqa: E501


        :return: The exempt_expire_time_milli of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :rtype: int
        """
        return self._exempt_expire_time_milli

    @exempt_expire_time_milli.setter
    def exempt_expire_time_milli(self, exempt_expire_time_milli):
        """Sets the exempt_expire_time_milli of this RiskExemptMetaForGetRiskOutput.


        :param exempt_expire_time_milli: The exempt_expire_time_milli of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :type: int
        """

        self._exempt_expire_time_milli = exempt_expire_time_milli

    @property
    def exempt_reason_type(self):
        """Gets the exempt_reason_type of this RiskExemptMetaForGetRiskOutput.  # noqa: E501


        :return: The exempt_reason_type of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._exempt_reason_type

    @exempt_reason_type.setter
    def exempt_reason_type(self, exempt_reason_type):
        """Sets the exempt_reason_type of this RiskExemptMetaForGetRiskOutput.


        :param exempt_reason_type: The exempt_reason_type of this RiskExemptMetaForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._exempt_reason_type = exempt_reason_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RiskExemptMetaForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RiskExemptMetaForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RiskExemptMetaForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
