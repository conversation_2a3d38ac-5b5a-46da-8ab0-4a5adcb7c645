# coding: utf-8

"""
    volc_observe

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateEventRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'contact_group_ids': 'list[str]',
        'contact_methods': 'list[str]',
        'description': 'str',
        'effective_time': 'EffectiveTimeForUpdateEventRuleInput',
        'endpoint': 'str',
        'event_bus_name': 'str',
        'event_source': 'str',
        'event_type': 'list[str]',
        'filter_pattern': 'dict(str, object)',
        'level': 'str',
        'message_queue': 'list[MessageQueueForUpdateEventRuleInput]',
        'rule_id': 'str',
        'rule_name': 'str',
        'status': 'str',
        'tls_target': 'list[TLSTargetForUpdateEventRuleInput]',
        'webhook_ids': 'list[str]'
    }

    attribute_map = {
        'contact_group_ids': 'ContactGroupIds',
        'contact_methods': 'ContactMethods',
        'description': 'Description',
        'effective_time': 'EffectiveTime',
        'endpoint': 'Endpoint',
        'event_bus_name': 'EventBusName',
        'event_source': 'EventSource',
        'event_type': 'EventType',
        'filter_pattern': 'FilterPattern',
        'level': 'Level',
        'message_queue': 'MessageQueue',
        'rule_id': 'RuleId',
        'rule_name': 'RuleName',
        'status': 'Status',
        'tls_target': 'TLSTarget',
        'webhook_ids': 'WebhookIds'
    }

    def __init__(self, contact_group_ids=None, contact_methods=None, description=None, effective_time=None, endpoint=None, event_bus_name=None, event_source=None, event_type=None, filter_pattern=None, level=None, message_queue=None, rule_id=None, rule_name=None, status=None, tls_target=None, webhook_ids=None, _configuration=None):  # noqa: E501
        """UpdateEventRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._contact_group_ids = None
        self._contact_methods = None
        self._description = None
        self._effective_time = None
        self._endpoint = None
        self._event_bus_name = None
        self._event_source = None
        self._event_type = None
        self._filter_pattern = None
        self._level = None
        self._message_queue = None
        self._rule_id = None
        self._rule_name = None
        self._status = None
        self._tls_target = None
        self._webhook_ids = None
        self.discriminator = None

        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if contact_methods is not None:
            self.contact_methods = contact_methods
        if description is not None:
            self.description = description
        if effective_time is not None:
            self.effective_time = effective_time
        if endpoint is not None:
            self.endpoint = endpoint
        self.event_bus_name = event_bus_name
        self.event_source = event_source
        if event_type is not None:
            self.event_type = event_type
        if filter_pattern is not None:
            self.filter_pattern = filter_pattern
        self.level = level
        if message_queue is not None:
            self.message_queue = message_queue
        self.rule_id = rule_id
        self.rule_name = rule_name
        if status is not None:
            self.status = status
        if tls_target is not None:
            self.tls_target = tls_target
        if webhook_ids is not None:
            self.webhook_ids = webhook_ids

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this UpdateEventRuleRequest.  # noqa: E501


        :return: The contact_group_ids of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this UpdateEventRuleRequest.


        :param contact_group_ids: The contact_group_ids of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def contact_methods(self):
        """Gets the contact_methods of this UpdateEventRuleRequest.  # noqa: E501


        :return: The contact_methods of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_methods

    @contact_methods.setter
    def contact_methods(self, contact_methods):
        """Sets the contact_methods of this UpdateEventRuleRequest.


        :param contact_methods: The contact_methods of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._contact_methods = contact_methods

    @property
    def description(self):
        """Gets the description of this UpdateEventRuleRequest.  # noqa: E501


        :return: The description of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateEventRuleRequest.


        :param description: The description of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def effective_time(self):
        """Gets the effective_time of this UpdateEventRuleRequest.  # noqa: E501


        :return: The effective_time of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: EffectiveTimeForUpdateEventRuleInput
        """
        return self._effective_time

    @effective_time.setter
    def effective_time(self, effective_time):
        """Sets the effective_time of this UpdateEventRuleRequest.


        :param effective_time: The effective_time of this UpdateEventRuleRequest.  # noqa: E501
        :type: EffectiveTimeForUpdateEventRuleInput
        """

        self._effective_time = effective_time

    @property
    def endpoint(self):
        """Gets the endpoint of this UpdateEventRuleRequest.  # noqa: E501


        :return: The endpoint of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this UpdateEventRuleRequest.


        :param endpoint: The endpoint of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def event_bus_name(self):
        """Gets the event_bus_name of this UpdateEventRuleRequest.  # noqa: E501


        :return: The event_bus_name of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_bus_name

    @event_bus_name.setter
    def event_bus_name(self, event_bus_name):
        """Sets the event_bus_name of this UpdateEventRuleRequest.


        :param event_bus_name: The event_bus_name of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and event_bus_name is None:
            raise ValueError("Invalid value for `event_bus_name`, must not be `None`")  # noqa: E501
        allowed_values = ["default"]  # noqa: E501
        if (self._configuration.client_side_validation and
                event_bus_name not in allowed_values):
            raise ValueError(
                "Invalid value for `event_bus_name` ({0}), must be one of {1}"  # noqa: E501
                .format(event_bus_name, allowed_values)
            )

        self._event_bus_name = event_bus_name

    @property
    def event_source(self):
        """Gets the event_source of this UpdateEventRuleRequest.  # noqa: E501


        :return: The event_source of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_source

    @event_source.setter
    def event_source(self, event_source):
        """Sets the event_source of this UpdateEventRuleRequest.


        :param event_source: The event_source of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and event_source is None:
            raise ValueError("Invalid value for `event_source`, must not be `None`")  # noqa: E501

        self._event_source = event_source

    @property
    def event_type(self):
        """Gets the event_type of this UpdateEventRuleRequest.  # noqa: E501


        :return: The event_type of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this UpdateEventRuleRequest.


        :param event_type: The event_type of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._event_type = event_type

    @property
    def filter_pattern(self):
        """Gets the filter_pattern of this UpdateEventRuleRequest.  # noqa: E501


        :return: The filter_pattern of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: dict(str, object)
        """
        return self._filter_pattern

    @filter_pattern.setter
    def filter_pattern(self, filter_pattern):
        """Sets the filter_pattern of this UpdateEventRuleRequest.


        :param filter_pattern: The filter_pattern of this UpdateEventRuleRequest.  # noqa: E501
        :type: dict(str, object)
        """

        self._filter_pattern = filter_pattern

    @property
    def level(self):
        """Gets the level of this UpdateEventRuleRequest.  # noqa: E501


        :return: The level of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this UpdateEventRuleRequest.


        :param level: The level of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and level is None:
            raise ValueError("Invalid value for `level`, must not be `None`")  # noqa: E501

        self._level = level

    @property
    def message_queue(self):
        """Gets the message_queue of this UpdateEventRuleRequest.  # noqa: E501


        :return: The message_queue of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[MessageQueueForUpdateEventRuleInput]
        """
        return self._message_queue

    @message_queue.setter
    def message_queue(self, message_queue):
        """Sets the message_queue of this UpdateEventRuleRequest.


        :param message_queue: The message_queue of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[MessageQueueForUpdateEventRuleInput]
        """

        self._message_queue = message_queue

    @property
    def rule_id(self):
        """Gets the rule_id of this UpdateEventRuleRequest.  # noqa: E501


        :return: The rule_id of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this UpdateEventRuleRequest.


        :param rule_id: The rule_id of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this UpdateEventRuleRequest.  # noqa: E501


        :return: The rule_name of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this UpdateEventRuleRequest.


        :param rule_name: The rule_name of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_name is None:
            raise ValueError("Invalid value for `rule_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                rule_name is not None and len(rule_name) > 127):
            raise ValueError("Invalid value for `rule_name`, length must be less than or equal to `127`")  # noqa: E501
        if (self._configuration.client_side_validation and
                rule_name is not None and len(rule_name) < 2):
            raise ValueError("Invalid value for `rule_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._rule_name = rule_name

    @property
    def status(self):
        """Gets the status of this UpdateEventRuleRequest.  # noqa: E501


        :return: The status of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this UpdateEventRuleRequest.


        :param status: The status of this UpdateEventRuleRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["enable", "disable"]  # noqa: E501
        if (self._configuration.client_side_validation and
                status not in allowed_values):
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    @property
    def tls_target(self):
        """Gets the tls_target of this UpdateEventRuleRequest.  # noqa: E501


        :return: The tls_target of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[TLSTargetForUpdateEventRuleInput]
        """
        return self._tls_target

    @tls_target.setter
    def tls_target(self, tls_target):
        """Sets the tls_target of this UpdateEventRuleRequest.


        :param tls_target: The tls_target of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[TLSTargetForUpdateEventRuleInput]
        """

        self._tls_target = tls_target

    @property
    def webhook_ids(self):
        """Gets the webhook_ids of this UpdateEventRuleRequest.  # noqa: E501


        :return: The webhook_ids of this UpdateEventRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._webhook_ids

    @webhook_ids.setter
    def webhook_ids(self, webhook_ids):
        """Sets the webhook_ids of this UpdateEventRuleRequest.


        :param webhook_ids: The webhook_ids of this UpdateEventRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._webhook_ids = webhook_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateEventRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateEventRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateEventRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
