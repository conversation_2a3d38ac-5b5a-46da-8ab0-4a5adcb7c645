# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListTeachAssistantAccountsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_teach_assistant_account_number': 'int',
        'teach_assistant_accounts': 'list[TeachAssistantAccountForListTeachAssistantAccountsOutput]'
    }

    attribute_map = {
        'max_teach_assistant_account_number': 'MaxTeachAssistantAccountNumber',
        'teach_assistant_accounts': 'TeachAssistantAccounts'
    }

    def __init__(self, max_teach_assistant_account_number=None, teach_assistant_accounts=None, _configuration=None):  # noqa: E501
        """ListTeachAssistantAccountsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_teach_assistant_account_number = None
        self._teach_assistant_accounts = None
        self.discriminator = None

        if max_teach_assistant_account_number is not None:
            self.max_teach_assistant_account_number = max_teach_assistant_account_number
        if teach_assistant_accounts is not None:
            self.teach_assistant_accounts = teach_assistant_accounts

    @property
    def max_teach_assistant_account_number(self):
        """Gets the max_teach_assistant_account_number of this ListTeachAssistantAccountsResponse.  # noqa: E501


        :return: The max_teach_assistant_account_number of this ListTeachAssistantAccountsResponse.  # noqa: E501
        :rtype: int
        """
        return self._max_teach_assistant_account_number

    @max_teach_assistant_account_number.setter
    def max_teach_assistant_account_number(self, max_teach_assistant_account_number):
        """Sets the max_teach_assistant_account_number of this ListTeachAssistantAccountsResponse.


        :param max_teach_assistant_account_number: The max_teach_assistant_account_number of this ListTeachAssistantAccountsResponse.  # noqa: E501
        :type: int
        """

        self._max_teach_assistant_account_number = max_teach_assistant_account_number

    @property
    def teach_assistant_accounts(self):
        """Gets the teach_assistant_accounts of this ListTeachAssistantAccountsResponse.  # noqa: E501


        :return: The teach_assistant_accounts of this ListTeachAssistantAccountsResponse.  # noqa: E501
        :rtype: list[TeachAssistantAccountForListTeachAssistantAccountsOutput]
        """
        return self._teach_assistant_accounts

    @teach_assistant_accounts.setter
    def teach_assistant_accounts(self, teach_assistant_accounts):
        """Sets the teach_assistant_accounts of this ListTeachAssistantAccountsResponse.


        :param teach_assistant_accounts: The teach_assistant_accounts of this ListTeachAssistantAccountsResponse.  # noqa: E501
        :type: list[TeachAssistantAccountForListTeachAssistantAccountsOutput]
        """

        self._teach_assistant_accounts = teach_assistant_accounts

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListTeachAssistantAccountsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListTeachAssistantAccountsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListTeachAssistantAccountsResponse):
            return True

        return self.to_dict() != other.to_dict()
