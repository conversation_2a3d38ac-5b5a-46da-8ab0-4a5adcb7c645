# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertLoadBalancerBillingTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'load_balancer_billing_type': 'int',
        'load_balancer_id': 'str',
        'load_balancer_spec': 'str',
        'period': 'int',
        'period_unit': 'str'
    }

    attribute_map = {
        'load_balancer_billing_type': 'LoadBalancerBillingType',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_spec': 'LoadBalancerSpec',
        'period': 'Period',
        'period_unit': 'PeriodUnit'
    }

    def __init__(self, load_balancer_billing_type=None, load_balancer_id=None, load_balancer_spec=None, period=None, period_unit=None, _configuration=None):  # noqa: E501
        """ConvertLoadBalancerBillingTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._load_balancer_billing_type = None
        self._load_balancer_id = None
        self._load_balancer_spec = None
        self._period = None
        self._period_unit = None
        self.discriminator = None

        self.load_balancer_billing_type = load_balancer_billing_type
        self.load_balancer_id = load_balancer_id
        if load_balancer_spec is not None:
            self.load_balancer_spec = load_balancer_spec
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit

    @property
    def load_balancer_billing_type(self):
        """Gets the load_balancer_billing_type of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501


        :return: The load_balancer_billing_type of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._load_balancer_billing_type

    @load_balancer_billing_type.setter
    def load_balancer_billing_type(self, load_balancer_billing_type):
        """Sets the load_balancer_billing_type of this ConvertLoadBalancerBillingTypeRequest.


        :param load_balancer_billing_type: The load_balancer_billing_type of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and load_balancer_billing_type is None:
            raise ValueError("Invalid value for `load_balancer_billing_type`, must not be `None`")  # noqa: E501

        self._load_balancer_billing_type = load_balancer_billing_type

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501


        :return: The load_balancer_id of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this ConvertLoadBalancerBillingTypeRequest.


        :param load_balancer_id: The load_balancer_id of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_spec(self):
        """Gets the load_balancer_spec of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501


        :return: The load_balancer_spec of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_spec

    @load_balancer_spec.setter
    def load_balancer_spec(self, load_balancer_spec):
        """Sets the load_balancer_spec of this ConvertLoadBalancerBillingTypeRequest.


        :param load_balancer_spec: The load_balancer_spec of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_spec = load_balancer_spec

    @property
    def period(self):
        """Gets the period of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501


        :return: The period of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this ConvertLoadBalancerBillingTypeRequest.


        :param period: The period of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501


        :return: The period_unit of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this ConvertLoadBalancerBillingTypeRequest.


        :param period_unit: The period_unit of this ConvertLoadBalancerBillingTypeRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertLoadBalancerBillingTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertLoadBalancerBillingTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertLoadBalancerBillingTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
