# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EmojiSetForGetActivityCustomEmojiSetDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'emoji_num': 'int',
        'emoji_set_icon': 'str',
        'emoji_set_id': 'int',
        'emoji_set_location': 'int',
        'emoji_set_name': 'str',
        'emoji_set_status': 'int',
        'emoji_set_type': 'int',
        'emojis': 'list[EmojiForGetActivityCustomEmojiSetDetailOutput]',
        'is_system_emoji_set': 'int'
    }

    attribute_map = {
        'emoji_num': 'EmojiNum',
        'emoji_set_icon': 'EmojiSetIcon',
        'emoji_set_id': 'EmojiSetId',
        'emoji_set_location': 'EmojiSetLocation',
        'emoji_set_name': 'EmojiSetName',
        'emoji_set_status': 'EmojiSetStatus',
        'emoji_set_type': 'EmojiSetType',
        'emojis': 'Emojis',
        'is_system_emoji_set': 'IsSystemEmojiSet'
    }

    def __init__(self, emoji_num=None, emoji_set_icon=None, emoji_set_id=None, emoji_set_location=None, emoji_set_name=None, emoji_set_status=None, emoji_set_type=None, emojis=None, is_system_emoji_set=None, _configuration=None):  # noqa: E501
        """EmojiSetForGetActivityCustomEmojiSetDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._emoji_num = None
        self._emoji_set_icon = None
        self._emoji_set_id = None
        self._emoji_set_location = None
        self._emoji_set_name = None
        self._emoji_set_status = None
        self._emoji_set_type = None
        self._emojis = None
        self._is_system_emoji_set = None
        self.discriminator = None

        if emoji_num is not None:
            self.emoji_num = emoji_num
        if emoji_set_icon is not None:
            self.emoji_set_icon = emoji_set_icon
        if emoji_set_id is not None:
            self.emoji_set_id = emoji_set_id
        if emoji_set_location is not None:
            self.emoji_set_location = emoji_set_location
        if emoji_set_name is not None:
            self.emoji_set_name = emoji_set_name
        if emoji_set_status is not None:
            self.emoji_set_status = emoji_set_status
        if emoji_set_type is not None:
            self.emoji_set_type = emoji_set_type
        if emojis is not None:
            self.emojis = emojis
        if is_system_emoji_set is not None:
            self.is_system_emoji_set = is_system_emoji_set

    @property
    def emoji_num(self):
        """Gets the emoji_num of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_num of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_num

    @emoji_num.setter
    def emoji_num(self, emoji_num):
        """Sets the emoji_num of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_num: The emoji_num of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_num = emoji_num

    @property
    def emoji_set_icon(self):
        """Gets the emoji_set_icon of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_icon of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._emoji_set_icon

    @emoji_set_icon.setter
    def emoji_set_icon(self, emoji_set_icon):
        """Sets the emoji_set_icon of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_icon: The emoji_set_icon of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: str
        """

        self._emoji_set_icon = emoji_set_icon

    @property
    def emoji_set_id(self):
        """Gets the emoji_set_id of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_id of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_set_id

    @emoji_set_id.setter
    def emoji_set_id(self, emoji_set_id):
        """Sets the emoji_set_id of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_id: The emoji_set_id of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_set_id = emoji_set_id

    @property
    def emoji_set_location(self):
        """Gets the emoji_set_location of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_location of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_set_location

    @emoji_set_location.setter
    def emoji_set_location(self, emoji_set_location):
        """Sets the emoji_set_location of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_location: The emoji_set_location of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_set_location = emoji_set_location

    @property
    def emoji_set_name(self):
        """Gets the emoji_set_name of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_name of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._emoji_set_name

    @emoji_set_name.setter
    def emoji_set_name(self, emoji_set_name):
        """Sets the emoji_set_name of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_name: The emoji_set_name of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: str
        """

        self._emoji_set_name = emoji_set_name

    @property
    def emoji_set_status(self):
        """Gets the emoji_set_status of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_status of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_set_status

    @emoji_set_status.setter
    def emoji_set_status(self, emoji_set_status):
        """Sets the emoji_set_status of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_status: The emoji_set_status of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_set_status = emoji_set_status

    @property
    def emoji_set_type(self):
        """Gets the emoji_set_type of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_set_type of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_set_type

    @emoji_set_type.setter
    def emoji_set_type(self, emoji_set_type):
        """Sets the emoji_set_type of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_set_type: The emoji_set_type of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_set_type = emoji_set_type

    @property
    def emojis(self):
        """Gets the emojis of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emojis of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: list[EmojiForGetActivityCustomEmojiSetDetailOutput]
        """
        return self._emojis

    @emojis.setter
    def emojis(self, emojis):
        """Sets the emojis of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param emojis: The emojis of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: list[EmojiForGetActivityCustomEmojiSetDetailOutput]
        """

        self._emojis = emojis

    @property
    def is_system_emoji_set(self):
        """Gets the is_system_emoji_set of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The is_system_emoji_set of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_system_emoji_set

    @is_system_emoji_set.setter
    def is_system_emoji_set(self, is_system_emoji_set):
        """Sets the is_system_emoji_set of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.


        :param is_system_emoji_set: The is_system_emoji_set of this EmojiSetForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._is_system_emoji_set = is_system_emoji_set

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EmojiSetForGetActivityCustomEmojiSetDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EmojiSetForGetActivityCustomEmojiSetDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EmojiSetForGetActivityCustomEmojiSetDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
