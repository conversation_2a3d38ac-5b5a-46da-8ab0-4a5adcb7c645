# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetTopChatAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audience_group_id': 'int',
        'chat_id': 'int',
        'create_time': 'int',
        'is_delete': 'bool',
        'is_presenter': 'bool',
        'is_tip_off': 'int',
        'llm_analyze_result': 'str',
        'like_count': 'int',
        'text_content': 'str',
        'user': 'UserForGetTopChatAPIOutput',
        'review_status': 'int'
    }

    attribute_map = {
        'audience_group_id': 'AudienceGroupId',
        'chat_id': 'ChatId',
        'create_time': 'CreateTime',
        'is_delete': 'IsDelete',
        'is_presenter': 'IsPresenter',
        'is_tip_off': 'IsTipOff',
        'llm_analyze_result': 'LLMAnalyzeResult',
        'like_count': 'LikeCount',
        'text_content': 'TextContent',
        'user': 'User',
        'review_status': 'reviewStatus'
    }

    def __init__(self, audience_group_id=None, chat_id=None, create_time=None, is_delete=None, is_presenter=None, is_tip_off=None, llm_analyze_result=None, like_count=None, text_content=None, user=None, review_status=None, _configuration=None):  # noqa: E501
        """DataForGetTopChatAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audience_group_id = None
        self._chat_id = None
        self._create_time = None
        self._is_delete = None
        self._is_presenter = None
        self._is_tip_off = None
        self._llm_analyze_result = None
        self._like_count = None
        self._text_content = None
        self._user = None
        self._review_status = None
        self.discriminator = None

        if audience_group_id is not None:
            self.audience_group_id = audience_group_id
        if chat_id is not None:
            self.chat_id = chat_id
        if create_time is not None:
            self.create_time = create_time
        if is_delete is not None:
            self.is_delete = is_delete
        if is_presenter is not None:
            self.is_presenter = is_presenter
        if is_tip_off is not None:
            self.is_tip_off = is_tip_off
        if llm_analyze_result is not None:
            self.llm_analyze_result = llm_analyze_result
        if like_count is not None:
            self.like_count = like_count
        if text_content is not None:
            self.text_content = text_content
        if user is not None:
            self.user = user
        if review_status is not None:
            self.review_status = review_status

    @property
    def audience_group_id(self):
        """Gets the audience_group_id of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The audience_group_id of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._audience_group_id

    @audience_group_id.setter
    def audience_group_id(self, audience_group_id):
        """Sets the audience_group_id of this DataForGetTopChatAPIOutput.


        :param audience_group_id: The audience_group_id of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._audience_group_id = audience_group_id

    @property
    def chat_id(self):
        """Gets the chat_id of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The chat_id of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._chat_id

    @chat_id.setter
    def chat_id(self, chat_id):
        """Sets the chat_id of this DataForGetTopChatAPIOutput.


        :param chat_id: The chat_id of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._chat_id = chat_id

    @property
    def create_time(self):
        """Gets the create_time of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The create_time of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForGetTopChatAPIOutput.


        :param create_time: The create_time of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def is_delete(self):
        """Gets the is_delete of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The is_delete of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_delete

    @is_delete.setter
    def is_delete(self, is_delete):
        """Sets the is_delete of this DataForGetTopChatAPIOutput.


        :param is_delete: The is_delete of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_delete = is_delete

    @property
    def is_presenter(self):
        """Gets the is_presenter of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The is_presenter of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_presenter

    @is_presenter.setter
    def is_presenter(self, is_presenter):
        """Sets the is_presenter of this DataForGetTopChatAPIOutput.


        :param is_presenter: The is_presenter of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_presenter = is_presenter

    @property
    def is_tip_off(self):
        """Gets the is_tip_off of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The is_tip_off of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_tip_off

    @is_tip_off.setter
    def is_tip_off(self, is_tip_off):
        """Sets the is_tip_off of this DataForGetTopChatAPIOutput.


        :param is_tip_off: The is_tip_off of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_tip_off = is_tip_off

    @property
    def llm_analyze_result(self):
        """Gets the llm_analyze_result of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The llm_analyze_result of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._llm_analyze_result

    @llm_analyze_result.setter
    def llm_analyze_result(self, llm_analyze_result):
        """Sets the llm_analyze_result of this DataForGetTopChatAPIOutput.


        :param llm_analyze_result: The llm_analyze_result of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: str
        """

        self._llm_analyze_result = llm_analyze_result

    @property
    def like_count(self):
        """Gets the like_count of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The like_count of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._like_count

    @like_count.setter
    def like_count(self, like_count):
        """Sets the like_count of this DataForGetTopChatAPIOutput.


        :param like_count: The like_count of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._like_count = like_count

    @property
    def text_content(self):
        """Gets the text_content of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The text_content of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._text_content

    @text_content.setter
    def text_content(self, text_content):
        """Sets the text_content of this DataForGetTopChatAPIOutput.


        :param text_content: The text_content of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: str
        """

        self._text_content = text_content

    @property
    def user(self):
        """Gets the user of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The user of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: UserForGetTopChatAPIOutput
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this DataForGetTopChatAPIOutput.


        :param user: The user of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: UserForGetTopChatAPIOutput
        """

        self._user = user

    @property
    def review_status(self):
        """Gets the review_status of this DataForGetTopChatAPIOutput.  # noqa: E501


        :return: The review_status of this DataForGetTopChatAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._review_status

    @review_status.setter
    def review_status(self, review_status):
        """Sets the review_status of this DataForGetTopChatAPIOutput.


        :param review_status: The review_status of this DataForGetTopChatAPIOutput.  # noqa: E501
        :type: int
        """

        self._review_status = review_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetTopChatAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetTopChatAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetTopChatAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
