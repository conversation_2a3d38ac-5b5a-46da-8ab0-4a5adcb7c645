# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'create_time_end': 'str',
        'create_time_start': 'str',
        'db_engine_version': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'node_spec': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'storage_charge_type': 'str',
        'tag_filters': 'list[TagFilterForDescribeDBInstancesInput]',
        'zone_id': 'str'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'create_time_end': 'CreateTimeEnd',
        'create_time_start': 'CreateTimeStart',
        'db_engine_version': 'DBEngineVersion',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'node_spec': 'NodeSpec',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'storage_charge_type': 'StorageChargeType',
        'tag_filters': 'TagFilters',
        'zone_id': 'ZoneId'
    }

    def __init__(self, charge_type=None, create_time_end=None, create_time_start=None, db_engine_version=None, instance_id=None, instance_name=None, instance_status=None, node_spec=None, page_number=None, page_size=None, project_name=None, storage_charge_type=None, tag_filters=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeDBInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._create_time_end = None
        self._create_time_start = None
        self._db_engine_version = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._node_spec = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._storage_charge_type = None
        self._tag_filters = None
        self._zone_id = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if create_time_end is not None:
            self.create_time_end = create_time_end
        if create_time_start is not None:
            self.create_time_start = create_time_start
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if node_spec is not None:
            self.node_spec = node_spec
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if storage_charge_type is not None:
            self.storage_charge_type = storage_charge_type
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def charge_type(self):
        """Gets the charge_type of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The charge_type of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this DescribeDBInstancesRequest.


        :param charge_type: The charge_type of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["PostPaid", "PrePaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def create_time_end(self):
        """Gets the create_time_end of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The create_time_end of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_end

    @create_time_end.setter
    def create_time_end(self, create_time_end):
        """Sets the create_time_end of this DescribeDBInstancesRequest.


        :param create_time_end: The create_time_end of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_time_end = create_time_end

    @property
    def create_time_start(self):
        """Gets the create_time_start of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The create_time_start of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_start

    @create_time_start.setter
    def create_time_start(self, create_time_start):
        """Sets the create_time_start of this DescribeDBInstancesRequest.


        :param create_time_start: The create_time_start of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._create_time_start = create_time_start

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this DescribeDBInstancesRequest.


        :param db_engine_version: The db_engine_version of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["MySQL_5_7", "MySQL_8_0"]  # noqa: E501
        if (self._configuration.client_side_validation and
                db_engine_version not in allowed_values):
            raise ValueError(
                "Invalid value for `db_engine_version` ({0}), must be one of {1}"  # noqa: E501
                .format(db_engine_version, allowed_values)
            )

        self._db_engine_version = db_engine_version

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_id of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBInstancesRequest.


        :param instance_id: The instance_id of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_name of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DescribeDBInstancesRequest.


        :param instance_name: The instance_name of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The instance_status of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this DescribeDBInstancesRequest.


        :param instance_status: The instance_status of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["AddressModifying", "AllowListMaintaining", "CreateFailed", "Creating", "Deleting", "Error", "Expired", "Owing", "PrimaryChanging", "Reclaiming", "Restarting", "Restoring", "Resuming", "Running", "Scaling", "Upgrading", "WaitingPaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_status not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_status` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_status, allowed_values)
            )

        self._instance_status = instance_status

    @property
    def node_spec(self):
        """Gets the node_spec of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The node_spec of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this DescribeDBInstancesRequest.


        :param node_spec: The node_spec of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["vedb.mysql.g4.2xlarge", "vedb.mysql.g4.4xlarge", "vedb.mysql.g4.large", "vedb.mysql.g4.xlarge", "vedb.mysql.g8.2xlarge", "vedb.mysql.x4.2xlarge", "vedb.mysql.x4.4xlarge", "vedb.mysql.x4.8xlarge", "vedb.mysql.x4.large", "vedb.mysql.x4.xlarge", "vedb.mysql.x8.2xlarge", "vedb.mysql.x8.4xlarge", "vedb.mysql.x8.6xlarge", "vedb.mysql.x8.8xlarge", "vedb.mysql.x8.large", "vedb.mysql.x8.xlarge"]  # noqa: E501
        if (self._configuration.client_side_validation and
                node_spec not in allowed_values):
            raise ValueError(
                "Invalid value for `node_spec` ({0}), must be one of {1}"  # noqa: E501
                .format(node_spec, allowed_values)
            )

        self._node_spec = node_spec

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The page_number of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDBInstancesRequest.


        :param page_number: The page_number of this DescribeDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The page_size of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDBInstancesRequest.


        :param page_size: The page_size of this DescribeDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The project_name of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeDBInstancesRequest.


        :param project_name: The project_name of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def storage_charge_type(self):
        """Gets the storage_charge_type of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The storage_charge_type of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._storage_charge_type

    @storage_charge_type.setter
    def storage_charge_type(self, storage_charge_type):
        """Sets the storage_charge_type of this DescribeDBInstancesRequest.


        :param storage_charge_type: The storage_charge_type of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["PostPaid", "PrePaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                storage_charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `storage_charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(storage_charge_type, allowed_values)
            )

        self._storage_charge_type = storage_charge_type

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The tag_filters of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeDBInstancesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeDBInstancesRequest.


        :param tag_filters: The tag_filters of this DescribeDBInstancesRequest.  # noqa: E501
        :type: list[TagFilterForDescribeDBInstancesInput]
        """

        self._tag_filters = tag_filters

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeDBInstancesRequest.  # noqa: E501


        :return: The zone_id of this DescribeDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeDBInstancesRequest.


        :param zone_id: The zone_id of this DescribeDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
