# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListSupportedResourceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_scope': 'str',
        'resource_specifications': 'list[str]',
        'resource_type': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'resource_scope': 'ResourceScope',
        'resource_specifications': 'ResourceSpecifications',
        'resource_type': 'ResourceType',
        'zone_id': 'ZoneId'
    }

    def __init__(self, resource_scope=None, resource_specifications=None, resource_type=None, zone_id=None, _configuration=None):  # noqa: E501
        """ItemForListSupportedResourceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_scope = None
        self._resource_specifications = None
        self._resource_type = None
        self._zone_id = None
        self.discriminator = None

        if resource_scope is not None:
            self.resource_scope = resource_scope
        if resource_specifications is not None:
            self.resource_specifications = resource_specifications
        if resource_type is not None:
            self.resource_type = resource_type
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def resource_scope(self):
        """Gets the resource_scope of this ItemForListSupportedResourceTypesOutput.  # noqa: E501


        :return: The resource_scope of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_scope

    @resource_scope.setter
    def resource_scope(self, resource_scope):
        """Sets the resource_scope of this ItemForListSupportedResourceTypesOutput.


        :param resource_scope: The resource_scope of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._resource_scope = resource_scope

    @property
    def resource_specifications(self):
        """Gets the resource_specifications of this ItemForListSupportedResourceTypesOutput.  # noqa: E501


        :return: The resource_specifications of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_specifications

    @resource_specifications.setter
    def resource_specifications(self, resource_specifications):
        """Sets the resource_specifications of this ItemForListSupportedResourceTypesOutput.


        :param resource_specifications: The resource_specifications of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :type: list[str]
        """

        self._resource_specifications = resource_specifications

    @property
    def resource_type(self):
        """Gets the resource_type of this ItemForListSupportedResourceTypesOutput.  # noqa: E501


        :return: The resource_type of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ItemForListSupportedResourceTypesOutput.


        :param resource_type: The resource_type of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def zone_id(self):
        """Gets the zone_id of this ItemForListSupportedResourceTypesOutput.  # noqa: E501


        :return: The zone_id of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ItemForListSupportedResourceTypesOutput.


        :param zone_id: The zone_id of this ItemForListSupportedResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListSupportedResourceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListSupportedResourceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListSupportedResourceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
