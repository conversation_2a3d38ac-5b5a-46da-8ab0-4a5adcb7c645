# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAlarmArchiveRecordsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'alarm_count': 'int',
        'alarm_type': 'str',
        'archive_time': 'int',
        'file_name': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'alarm_count': 'AlarmCount',
        'alarm_type': 'AlarmType',
        'archive_time': 'ArchiveTime',
        'file_name': 'FileName'
    }

    def __init__(self, account_id=None, alarm_count=None, alarm_type=None, archive_time=None, file_name=None, _configuration=None):  # noqa: E501
        """DataForListAlarmArchiveRecordsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._alarm_count = None
        self._alarm_type = None
        self._archive_time = None
        self._file_name = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if alarm_count is not None:
            self.alarm_count = alarm_count
        if alarm_type is not None:
            self.alarm_type = alarm_type
        if archive_time is not None:
            self.archive_time = archive_time
        if file_name is not None:
            self.file_name = file_name

    @property
    def account_id(self):
        """Gets the account_id of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501


        :return: The account_id of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListAlarmArchiveRecordsOutput.


        :param account_id: The account_id of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def alarm_count(self):
        """Gets the alarm_count of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501


        :return: The alarm_count of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_count

    @alarm_count.setter
    def alarm_count(self, alarm_count):
        """Sets the alarm_count of this DataForListAlarmArchiveRecordsOutput.


        :param alarm_count: The alarm_count of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :type: int
        """

        self._alarm_count = alarm_count

    @property
    def alarm_type(self):
        """Gets the alarm_type of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501


        :return: The alarm_type of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this DataForListAlarmArchiveRecordsOutput.


        :param alarm_type: The alarm_type of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def archive_time(self):
        """Gets the archive_time of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501


        :return: The archive_time of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :rtype: int
        """
        return self._archive_time

    @archive_time.setter
    def archive_time(self, archive_time):
        """Sets the archive_time of this DataForListAlarmArchiveRecordsOutput.


        :param archive_time: The archive_time of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :type: int
        """

        self._archive_time = archive_time

    @property
    def file_name(self):
        """Gets the file_name of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501


        :return: The file_name of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this DataForListAlarmArchiveRecordsOutput.


        :param file_name: The file_name of this DataForListAlarmArchiveRecordsOutput.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAlarmArchiveRecordsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAlarmArchiveRecordsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAlarmArchiveRecordsOutput):
            return True

        return self.to_dict() != other.to_dict()
