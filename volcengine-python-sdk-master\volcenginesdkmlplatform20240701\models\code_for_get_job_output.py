# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CodeForGetJobOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'local_path': 'str',
        'mount_path': 'str',
        'saved_path': 'str'
    }

    attribute_map = {
        'local_path': 'LocalPath',
        'mount_path': 'MountPath',
        'saved_path': 'SavedPath'
    }

    def __init__(self, local_path=None, mount_path=None, saved_path=None, _configuration=None):  # noqa: E501
        """CodeForGetJobOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._local_path = None
        self._mount_path = None
        self._saved_path = None
        self.discriminator = None

        if local_path is not None:
            self.local_path = local_path
        if mount_path is not None:
            self.mount_path = mount_path
        if saved_path is not None:
            self.saved_path = saved_path

    @property
    def local_path(self):
        """Gets the local_path of this CodeForGetJobOutput.  # noqa: E501


        :return: The local_path of this CodeForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._local_path

    @local_path.setter
    def local_path(self, local_path):
        """Sets the local_path of this CodeForGetJobOutput.


        :param local_path: The local_path of this CodeForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._local_path = local_path

    @property
    def mount_path(self):
        """Gets the mount_path of this CodeForGetJobOutput.  # noqa: E501


        :return: The mount_path of this CodeForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_path

    @mount_path.setter
    def mount_path(self, mount_path):
        """Sets the mount_path of this CodeForGetJobOutput.


        :param mount_path: The mount_path of this CodeForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._mount_path = mount_path

    @property
    def saved_path(self):
        """Gets the saved_path of this CodeForGetJobOutput.  # noqa: E501


        :return: The saved_path of this CodeForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._saved_path

    @saved_path.setter
    def saved_path(self, saved_path):
        """Sets the saved_path of this CodeForGetJobOutput.


        :param saved_path: The saved_path of this CodeForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._saved_path = saved_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CodeForGetJobOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CodeForGetJobOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CodeForGetJobOutput):
            return True

        return self.to_dict() != other.to_dict()
