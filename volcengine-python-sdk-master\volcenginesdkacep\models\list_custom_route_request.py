# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCustomRouteRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_route_id': 'str',
        'custom_route_name': 'str',
        'dst_ip': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'product_id': 'str',
        'zone': 'str'
    }

    attribute_map = {
        'custom_route_id': 'CustomRouteId',
        'custom_route_name': 'CustomRouteName',
        'dst_ip': 'DstIP',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'product_id': 'ProductId',
        'zone': 'Zone'
    }

    def __init__(self, custom_route_id=None, custom_route_name=None, dst_ip=None, max_results=None, next_token=None, product_id=None, zone=None, _configuration=None):  # noqa: E501
        """ListCustomRouteRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_route_id = None
        self._custom_route_name = None
        self._dst_ip = None
        self._max_results = None
        self._next_token = None
        self._product_id = None
        self._zone = None
        self.discriminator = None

        if custom_route_id is not None:
            self.custom_route_id = custom_route_id
        if custom_route_name is not None:
            self.custom_route_name = custom_route_name
        if dst_ip is not None:
            self.dst_ip = dst_ip
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        self.product_id = product_id
        if zone is not None:
            self.zone = zone

    @property
    def custom_route_id(self):
        """Gets the custom_route_id of this ListCustomRouteRequest.  # noqa: E501


        :return: The custom_route_id of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_id

    @custom_route_id.setter
    def custom_route_id(self, custom_route_id):
        """Sets the custom_route_id of this ListCustomRouteRequest.


        :param custom_route_id: The custom_route_id of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._custom_route_id = custom_route_id

    @property
    def custom_route_name(self):
        """Gets the custom_route_name of this ListCustomRouteRequest.  # noqa: E501


        :return: The custom_route_name of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_name

    @custom_route_name.setter
    def custom_route_name(self, custom_route_name):
        """Sets the custom_route_name of this ListCustomRouteRequest.


        :param custom_route_name: The custom_route_name of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._custom_route_name = custom_route_name

    @property
    def dst_ip(self):
        """Gets the dst_ip of this ListCustomRouteRequest.  # noqa: E501


        :return: The dst_ip of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._dst_ip

    @dst_ip.setter
    def dst_ip(self, dst_ip):
        """Sets the dst_ip of this ListCustomRouteRequest.


        :param dst_ip: The dst_ip of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._dst_ip = dst_ip

    @property
    def max_results(self):
        """Gets the max_results of this ListCustomRouteRequest.  # noqa: E501


        :return: The max_results of this ListCustomRouteRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListCustomRouteRequest.


        :param max_results: The max_results of this ListCustomRouteRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListCustomRouteRequest.  # noqa: E501


        :return: The next_token of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListCustomRouteRequest.


        :param next_token: The next_token of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def product_id(self):
        """Gets the product_id of this ListCustomRouteRequest.  # noqa: E501


        :return: The product_id of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this ListCustomRouteRequest.


        :param product_id: The product_id of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product_id is None:
            raise ValueError("Invalid value for `product_id`, must not be `None`")  # noqa: E501

        self._product_id = product_id

    @property
    def zone(self):
        """Gets the zone of this ListCustomRouteRequest.  # noqa: E501


        :return: The zone of this ListCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this ListCustomRouteRequest.


        :param zone: The zone of this ListCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCustomRouteRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCustomRouteRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCustomRouteRequest):
            return True

        return self.to_dict() != other.to_dict()
