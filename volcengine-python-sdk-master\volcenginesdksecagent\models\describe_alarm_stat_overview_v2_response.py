# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAlarmStatOverviewV2Response(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'analysis_attempted_count': 'int',
        'analysis_false_count': 'int',
        'analysis_pending_count': 'int',
        'analysis_suspicious_count': 'int',
        'analysis_true_count': 'int',
        'auto_decide_rate': 'float',
        'discover_hidden_count': 'int',
        'ioc_intel_count': 'int',
        'priority_focus_count': 'int',
        'reduce_rate': 'float',
        'risk_stage_automatic_process_complete_count': 'int',
        'risk_total_count': 'int',
        'valid_alarm_count': 'int'
    }

    attribute_map = {
        'analysis_attempted_count': 'AnalysisAttemptedCount',
        'analysis_false_count': 'AnalysisFalseCount',
        'analysis_pending_count': 'AnalysisPendingCount',
        'analysis_suspicious_count': 'AnalysisSuspiciousCount',
        'analysis_true_count': 'AnalysisTrueCount',
        'auto_decide_rate': 'AutoDecideRate',
        'discover_hidden_count': 'DiscoverHiddenCount',
        'ioc_intel_count': 'IOCIntelCount',
        'priority_focus_count': 'PriorityFocusCount',
        'reduce_rate': 'ReduceRate',
        'risk_stage_automatic_process_complete_count': 'RiskStageAutomaticProcessCompleteCount',
        'risk_total_count': 'RiskTotalCount',
        'valid_alarm_count': 'ValidAlarmCount'
    }

    def __init__(self, analysis_attempted_count=None, analysis_false_count=None, analysis_pending_count=None, analysis_suspicious_count=None, analysis_true_count=None, auto_decide_rate=None, discover_hidden_count=None, ioc_intel_count=None, priority_focus_count=None, reduce_rate=None, risk_stage_automatic_process_complete_count=None, risk_total_count=None, valid_alarm_count=None, _configuration=None):  # noqa: E501
        """DescribeAlarmStatOverviewV2Response - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._analysis_attempted_count = None
        self._analysis_false_count = None
        self._analysis_pending_count = None
        self._analysis_suspicious_count = None
        self._analysis_true_count = None
        self._auto_decide_rate = None
        self._discover_hidden_count = None
        self._ioc_intel_count = None
        self._priority_focus_count = None
        self._reduce_rate = None
        self._risk_stage_automatic_process_complete_count = None
        self._risk_total_count = None
        self._valid_alarm_count = None
        self.discriminator = None

        if analysis_attempted_count is not None:
            self.analysis_attempted_count = analysis_attempted_count
        if analysis_false_count is not None:
            self.analysis_false_count = analysis_false_count
        if analysis_pending_count is not None:
            self.analysis_pending_count = analysis_pending_count
        if analysis_suspicious_count is not None:
            self.analysis_suspicious_count = analysis_suspicious_count
        if analysis_true_count is not None:
            self.analysis_true_count = analysis_true_count
        if auto_decide_rate is not None:
            self.auto_decide_rate = auto_decide_rate
        if discover_hidden_count is not None:
            self.discover_hidden_count = discover_hidden_count
        if ioc_intel_count is not None:
            self.ioc_intel_count = ioc_intel_count
        if priority_focus_count is not None:
            self.priority_focus_count = priority_focus_count
        if reduce_rate is not None:
            self.reduce_rate = reduce_rate
        if risk_stage_automatic_process_complete_count is not None:
            self.risk_stage_automatic_process_complete_count = risk_stage_automatic_process_complete_count
        if risk_total_count is not None:
            self.risk_total_count = risk_total_count
        if valid_alarm_count is not None:
            self.valid_alarm_count = valid_alarm_count

    @property
    def analysis_attempted_count(self):
        """Gets the analysis_attempted_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The analysis_attempted_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._analysis_attempted_count

    @analysis_attempted_count.setter
    def analysis_attempted_count(self, analysis_attempted_count):
        """Sets the analysis_attempted_count of this DescribeAlarmStatOverviewV2Response.


        :param analysis_attempted_count: The analysis_attempted_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._analysis_attempted_count = analysis_attempted_count

    @property
    def analysis_false_count(self):
        """Gets the analysis_false_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The analysis_false_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._analysis_false_count

    @analysis_false_count.setter
    def analysis_false_count(self, analysis_false_count):
        """Sets the analysis_false_count of this DescribeAlarmStatOverviewV2Response.


        :param analysis_false_count: The analysis_false_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._analysis_false_count = analysis_false_count

    @property
    def analysis_pending_count(self):
        """Gets the analysis_pending_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The analysis_pending_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._analysis_pending_count

    @analysis_pending_count.setter
    def analysis_pending_count(self, analysis_pending_count):
        """Sets the analysis_pending_count of this DescribeAlarmStatOverviewV2Response.


        :param analysis_pending_count: The analysis_pending_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._analysis_pending_count = analysis_pending_count

    @property
    def analysis_suspicious_count(self):
        """Gets the analysis_suspicious_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The analysis_suspicious_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._analysis_suspicious_count

    @analysis_suspicious_count.setter
    def analysis_suspicious_count(self, analysis_suspicious_count):
        """Sets the analysis_suspicious_count of this DescribeAlarmStatOverviewV2Response.


        :param analysis_suspicious_count: The analysis_suspicious_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._analysis_suspicious_count = analysis_suspicious_count

    @property
    def analysis_true_count(self):
        """Gets the analysis_true_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The analysis_true_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._analysis_true_count

    @analysis_true_count.setter
    def analysis_true_count(self, analysis_true_count):
        """Sets the analysis_true_count of this DescribeAlarmStatOverviewV2Response.


        :param analysis_true_count: The analysis_true_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._analysis_true_count = analysis_true_count

    @property
    def auto_decide_rate(self):
        """Gets the auto_decide_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The auto_decide_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: float
        """
        return self._auto_decide_rate

    @auto_decide_rate.setter
    def auto_decide_rate(self, auto_decide_rate):
        """Sets the auto_decide_rate of this DescribeAlarmStatOverviewV2Response.


        :param auto_decide_rate: The auto_decide_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: float
        """

        self._auto_decide_rate = auto_decide_rate

    @property
    def discover_hidden_count(self):
        """Gets the discover_hidden_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The discover_hidden_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._discover_hidden_count

    @discover_hidden_count.setter
    def discover_hidden_count(self, discover_hidden_count):
        """Sets the discover_hidden_count of this DescribeAlarmStatOverviewV2Response.


        :param discover_hidden_count: The discover_hidden_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._discover_hidden_count = discover_hidden_count

    @property
    def ioc_intel_count(self):
        """Gets the ioc_intel_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The ioc_intel_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._ioc_intel_count

    @ioc_intel_count.setter
    def ioc_intel_count(self, ioc_intel_count):
        """Sets the ioc_intel_count of this DescribeAlarmStatOverviewV2Response.


        :param ioc_intel_count: The ioc_intel_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._ioc_intel_count = ioc_intel_count

    @property
    def priority_focus_count(self):
        """Gets the priority_focus_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The priority_focus_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._priority_focus_count

    @priority_focus_count.setter
    def priority_focus_count(self, priority_focus_count):
        """Sets the priority_focus_count of this DescribeAlarmStatOverviewV2Response.


        :param priority_focus_count: The priority_focus_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._priority_focus_count = priority_focus_count

    @property
    def reduce_rate(self):
        """Gets the reduce_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The reduce_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: float
        """
        return self._reduce_rate

    @reduce_rate.setter
    def reduce_rate(self, reduce_rate):
        """Sets the reduce_rate of this DescribeAlarmStatOverviewV2Response.


        :param reduce_rate: The reduce_rate of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: float
        """

        self._reduce_rate = reduce_rate

    @property
    def risk_stage_automatic_process_complete_count(self):
        """Gets the risk_stage_automatic_process_complete_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The risk_stage_automatic_process_complete_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._risk_stage_automatic_process_complete_count

    @risk_stage_automatic_process_complete_count.setter
    def risk_stage_automatic_process_complete_count(self, risk_stage_automatic_process_complete_count):
        """Sets the risk_stage_automatic_process_complete_count of this DescribeAlarmStatOverviewV2Response.


        :param risk_stage_automatic_process_complete_count: The risk_stage_automatic_process_complete_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._risk_stage_automatic_process_complete_count = risk_stage_automatic_process_complete_count

    @property
    def risk_total_count(self):
        """Gets the risk_total_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The risk_total_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._risk_total_count

    @risk_total_count.setter
    def risk_total_count(self, risk_total_count):
        """Sets the risk_total_count of this DescribeAlarmStatOverviewV2Response.


        :param risk_total_count: The risk_total_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._risk_total_count = risk_total_count

    @property
    def valid_alarm_count(self):
        """Gets the valid_alarm_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501


        :return: The valid_alarm_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :rtype: int
        """
        return self._valid_alarm_count

    @valid_alarm_count.setter
    def valid_alarm_count(self, valid_alarm_count):
        """Sets the valid_alarm_count of this DescribeAlarmStatOverviewV2Response.


        :param valid_alarm_count: The valid_alarm_count of this DescribeAlarmStatOverviewV2Response.  # noqa: E501
        :type: int
        """

        self._valid_alarm_count = valid_alarm_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAlarmStatOverviewV2Response, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAlarmStatOverviewV2Response):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAlarmStatOverviewV2Response):
            return True

        return self.to_dict() != other.to_dict()
