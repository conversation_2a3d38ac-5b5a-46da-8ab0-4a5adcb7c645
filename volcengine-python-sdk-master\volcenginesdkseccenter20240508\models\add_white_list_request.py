# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddWhiteListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'desc': 'str',
        'is_killchain': 'bool',
        'lang': 'str',
        'match_alarm_name': 'str',
        'range': 'RangeForAddWhiteListInput',
        'rule_list': 'list[RuleListForAddWhiteListInput]',
        'type': 'str',
        'user': 'str'
    }

    attribute_map = {
        'desc': 'Desc',
        'is_killchain': 'IsKillchain',
        'lang': 'Lang',
        'match_alarm_name': 'MatchAlarmName',
        'range': 'Range',
        'rule_list': 'RuleList',
        'type': 'Type',
        'user': 'User'
    }

    def __init__(self, desc=None, is_killchain=None, lang=None, match_alarm_name=None, range=None, rule_list=None, type=None, user=None, _configuration=None):  # noqa: E501
        """AddWhiteListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._desc = None
        self._is_killchain = None
        self._lang = None
        self._match_alarm_name = None
        self._range = None
        self._rule_list = None
        self._type = None
        self._user = None
        self.discriminator = None

        if desc is not None:
            self.desc = desc
        if is_killchain is not None:
            self.is_killchain = is_killchain
        if lang is not None:
            self.lang = lang
        if match_alarm_name is not None:
            self.match_alarm_name = match_alarm_name
        if range is not None:
            self.range = range
        if rule_list is not None:
            self.rule_list = rule_list
        if type is not None:
            self.type = type
        if user is not None:
            self.user = user

    @property
    def desc(self):
        """Gets the desc of this AddWhiteListRequest.  # noqa: E501


        :return: The desc of this AddWhiteListRequest.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this AddWhiteListRequest.


        :param desc: The desc of this AddWhiteListRequest.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def is_killchain(self):
        """Gets the is_killchain of this AddWhiteListRequest.  # noqa: E501


        :return: The is_killchain of this AddWhiteListRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_killchain

    @is_killchain.setter
    def is_killchain(self, is_killchain):
        """Sets the is_killchain of this AddWhiteListRequest.


        :param is_killchain: The is_killchain of this AddWhiteListRequest.  # noqa: E501
        :type: bool
        """

        self._is_killchain = is_killchain

    @property
    def lang(self):
        """Gets the lang of this AddWhiteListRequest.  # noqa: E501


        :return: The lang of this AddWhiteListRequest.  # noqa: E501
        :rtype: str
        """
        return self._lang

    @lang.setter
    def lang(self, lang):
        """Sets the lang of this AddWhiteListRequest.


        :param lang: The lang of this AddWhiteListRequest.  # noqa: E501
        :type: str
        """

        self._lang = lang

    @property
    def match_alarm_name(self):
        """Gets the match_alarm_name of this AddWhiteListRequest.  # noqa: E501


        :return: The match_alarm_name of this AddWhiteListRequest.  # noqa: E501
        :rtype: str
        """
        return self._match_alarm_name

    @match_alarm_name.setter
    def match_alarm_name(self, match_alarm_name):
        """Sets the match_alarm_name of this AddWhiteListRequest.


        :param match_alarm_name: The match_alarm_name of this AddWhiteListRequest.  # noqa: E501
        :type: str
        """

        self._match_alarm_name = match_alarm_name

    @property
    def range(self):
        """Gets the range of this AddWhiteListRequest.  # noqa: E501


        :return: The range of this AddWhiteListRequest.  # noqa: E501
        :rtype: RangeForAddWhiteListInput
        """
        return self._range

    @range.setter
    def range(self, range):
        """Sets the range of this AddWhiteListRequest.


        :param range: The range of this AddWhiteListRequest.  # noqa: E501
        :type: RangeForAddWhiteListInput
        """

        self._range = range

    @property
    def rule_list(self):
        """Gets the rule_list of this AddWhiteListRequest.  # noqa: E501


        :return: The rule_list of this AddWhiteListRequest.  # noqa: E501
        :rtype: list[RuleListForAddWhiteListInput]
        """
        return self._rule_list

    @rule_list.setter
    def rule_list(self, rule_list):
        """Sets the rule_list of this AddWhiteListRequest.


        :param rule_list: The rule_list of this AddWhiteListRequest.  # noqa: E501
        :type: list[RuleListForAddWhiteListInput]
        """

        self._rule_list = rule_list

    @property
    def type(self):
        """Gets the type of this AddWhiteListRequest.  # noqa: E501


        :return: The type of this AddWhiteListRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this AddWhiteListRequest.


        :param type: The type of this AddWhiteListRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def user(self):
        """Gets the user of this AddWhiteListRequest.  # noqa: E501


        :return: The user of this AddWhiteListRequest.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this AddWhiteListRequest.


        :param user: The user of this AddWhiteListRequest.  # noqa: E501
        :type: str
        """

        self._user = user

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddWhiteListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddWhiteListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddWhiteListRequest):
            return True

        return self.to_dict() != other.to_dict()
