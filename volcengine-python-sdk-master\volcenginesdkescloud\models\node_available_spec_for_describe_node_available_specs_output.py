# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeAvailableSpecForDescribeNodeAvailableSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_spec_names': 'list[str]',
        'storage_spec_names': 'list[str]',
        'type': 'str'
    }

    attribute_map = {
        'resource_spec_names': 'ResourceSpecNames',
        'storage_spec_names': 'StorageSpecNames',
        'type': 'Type'
    }

    def __init__(self, resource_spec_names=None, storage_spec_names=None, type=None, _configuration=None):  # noqa: E501
        """NodeAvailableSpecForDescribeNodeAvailableSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_spec_names = None
        self._storage_spec_names = None
        self._type = None
        self.discriminator = None

        if resource_spec_names is not None:
            self.resource_spec_names = resource_spec_names
        if storage_spec_names is not None:
            self.storage_spec_names = storage_spec_names
        if type is not None:
            self.type = type

    @property
    def resource_spec_names(self):
        """Gets the resource_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501


        :return: The resource_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_spec_names

    @resource_spec_names.setter
    def resource_spec_names(self, resource_spec_names):
        """Sets the resource_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.


        :param resource_spec_names: The resource_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :type: list[str]
        """

        self._resource_spec_names = resource_spec_names

    @property
    def storage_spec_names(self):
        """Gets the storage_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501


        :return: The storage_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._storage_spec_names

    @storage_spec_names.setter
    def storage_spec_names(self, storage_spec_names):
        """Sets the storage_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.


        :param storage_spec_names: The storage_spec_names of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :type: list[str]
        """

        self._storage_spec_names = storage_spec_names

    @property
    def type(self):
        """Gets the type of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501


        :return: The type of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.


        :param type: The type of this NodeAvailableSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeAvailableSpecForDescribeNodeAvailableSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeAvailableSpecForDescribeNodeAvailableSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeAvailableSpecForDescribeNodeAvailableSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
