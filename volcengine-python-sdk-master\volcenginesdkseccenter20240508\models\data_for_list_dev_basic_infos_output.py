# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListDevBasicInfosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'private_ip': 'str',
        'public_ip': 'str',
        'status': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'status': 'Status'
    }

    def __init__(self, account_id=None, asset_id=None, asset_name=None, private_ip=None, public_ip=None, status=None, _configuration=None):  # noqa: E501
        """DataForListDevBasicInfosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asset_id = None
        self._asset_name = None
        self._private_ip = None
        self._public_ip = None
        self._status = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if status is not None:
            self.status = status

    @property
    def account_id(self):
        """Gets the account_id of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The account_id of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListDevBasicInfosOutput.


        :param account_id: The account_id of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The asset_id of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForListDevBasicInfosOutput.


        :param asset_id: The asset_id of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The asset_name of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this DataForListDevBasicInfosOutput.


        :param asset_name: The asset_name of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The private_ip of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForListDevBasicInfosOutput.


        :param private_ip: The private_ip of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The public_ip of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForListDevBasicInfosOutput.


        :param public_ip: The public_ip of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def status(self):
        """Gets the status of this DataForListDevBasicInfosOutput.  # noqa: E501


        :return: The status of this DataForListDevBasicInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListDevBasicInfosOutput.


        :param status: The status of this DataForListDevBasicInfosOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListDevBasicInfosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListDevBasicInfosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListDevBasicInfosOutput):
            return True

        return self.to_dict() != other.to_dict()
