# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointConnectionForDescribeVpcEndpointConnectionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'connection_status': 'str',
        'creation_time': 'str',
        'endpoint_id': 'str',
        'endpoint_owner_account_id': 'str',
        'endpoint_vpc_id': 'str',
        'replace_resource_id': 'str',
        'service_id': 'str',
        'update_time': 'str',
        'zones': 'list[ZoneForDescribeVpcEndpointConnectionsOutput]'
    }

    attribute_map = {
        'connection_status': 'ConnectionStatus',
        'creation_time': 'CreationTime',
        'endpoint_id': 'EndpointId',
        'endpoint_owner_account_id': 'EndpointOwnerAccountId',
        'endpoint_vpc_id': 'EndpointVpcId',
        'replace_resource_id': 'ReplaceResourceId',
        'service_id': 'ServiceId',
        'update_time': 'UpdateTime',
        'zones': 'Zones'
    }

    def __init__(self, connection_status=None, creation_time=None, endpoint_id=None, endpoint_owner_account_id=None, endpoint_vpc_id=None, replace_resource_id=None, service_id=None, update_time=None, zones=None, _configuration=None):  # noqa: E501
        """EndpointConnectionForDescribeVpcEndpointConnectionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._connection_status = None
        self._creation_time = None
        self._endpoint_id = None
        self._endpoint_owner_account_id = None
        self._endpoint_vpc_id = None
        self._replace_resource_id = None
        self._service_id = None
        self._update_time = None
        self._zones = None
        self.discriminator = None

        if connection_status is not None:
            self.connection_status = connection_status
        if creation_time is not None:
            self.creation_time = creation_time
        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_owner_account_id is not None:
            self.endpoint_owner_account_id = endpoint_owner_account_id
        if endpoint_vpc_id is not None:
            self.endpoint_vpc_id = endpoint_vpc_id
        if replace_resource_id is not None:
            self.replace_resource_id = replace_resource_id
        if service_id is not None:
            self.service_id = service_id
        if update_time is not None:
            self.update_time = update_time
        if zones is not None:
            self.zones = zones

    @property
    def connection_status(self):
        """Gets the connection_status of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The connection_status of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._connection_status

    @connection_status.setter
    def connection_status(self, connection_status):
        """Sets the connection_status of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param connection_status: The connection_status of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._connection_status = connection_status

    @property
    def creation_time(self):
        """Gets the creation_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The creation_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param creation_time: The creation_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The endpoint_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param endpoint_id: The endpoint_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_owner_account_id(self):
        """Gets the endpoint_owner_account_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The endpoint_owner_account_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_owner_account_id

    @endpoint_owner_account_id.setter
    def endpoint_owner_account_id(self, endpoint_owner_account_id):
        """Sets the endpoint_owner_account_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param endpoint_owner_account_id: The endpoint_owner_account_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_owner_account_id = endpoint_owner_account_id

    @property
    def endpoint_vpc_id(self):
        """Gets the endpoint_vpc_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The endpoint_vpc_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_vpc_id

    @endpoint_vpc_id.setter
    def endpoint_vpc_id(self, endpoint_vpc_id):
        """Sets the endpoint_vpc_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param endpoint_vpc_id: The endpoint_vpc_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_vpc_id = endpoint_vpc_id

    @property
    def replace_resource_id(self):
        """Gets the replace_resource_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The replace_resource_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._replace_resource_id

    @replace_resource_id.setter
    def replace_resource_id(self, replace_resource_id):
        """Sets the replace_resource_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param replace_resource_id: The replace_resource_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._replace_resource_id = replace_resource_id

    @property
    def service_id(self):
        """Gets the service_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The service_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param service_id: The service_id of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def update_time(self):
        """Gets the update_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The update_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param update_time: The update_time of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def zones(self):
        """Gets the zones of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501


        :return: The zones of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :rtype: list[ZoneForDescribeVpcEndpointConnectionsOutput]
        """
        return self._zones

    @zones.setter
    def zones(self, zones):
        """Sets the zones of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.


        :param zones: The zones of this EndpointConnectionForDescribeVpcEndpointConnectionsOutput.  # noqa: E501
        :type: list[ZoneForDescribeVpcEndpointConnectionsOutput]
        """

        self._zones = zones

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointConnectionForDescribeVpcEndpointConnectionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointConnectionForDescribeVpcEndpointConnectionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointConnectionForDescribeVpcEndpointConnectionsOutput):
            return True

        return self.to_dict() != other.to_dict()
