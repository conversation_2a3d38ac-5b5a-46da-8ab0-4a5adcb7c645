# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDataFlowBandwidthResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'bandwidth_max': 'int',
        'bandwidth_min': 'int',
        'bandwidth_per_node': 'int',
        'create_time': 'str',
        'file_system_id': 'str',
        'status': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'bandwidth_max': 'BandwidthMax',
        'bandwidth_min': 'BandwidthMin',
        'bandwidth_per_node': 'BandwidthPerNode',
        'create_time': 'CreateTime',
        'file_system_id': 'FileSystemId',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, bandwidth=None, bandwidth_max=None, bandwidth_min=None, bandwidth_per_node=None, create_time=None, file_system_id=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeDataFlowBandwidthResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._bandwidth_max = None
        self._bandwidth_min = None
        self._bandwidth_per_node = None
        self._create_time = None
        self._file_system_id = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if bandwidth_max is not None:
            self.bandwidth_max = bandwidth_max
        if bandwidth_min is not None:
            self.bandwidth_min = bandwidth_min
        if bandwidth_per_node is not None:
            self.bandwidth_per_node = bandwidth_per_node
        if create_time is not None:
            self.create_time = create_time
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The bandwidth of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DescribeDataFlowBandwidthResponse.


        :param bandwidth: The bandwidth of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def bandwidth_max(self):
        """Gets the bandwidth_max of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The bandwidth_max of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_max

    @bandwidth_max.setter
    def bandwidth_max(self, bandwidth_max):
        """Sets the bandwidth_max of this DescribeDataFlowBandwidthResponse.


        :param bandwidth_max: The bandwidth_max of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth_max = bandwidth_max

    @property
    def bandwidth_min(self):
        """Gets the bandwidth_min of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The bandwidth_min of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_min

    @bandwidth_min.setter
    def bandwidth_min(self, bandwidth_min):
        """Sets the bandwidth_min of this DescribeDataFlowBandwidthResponse.


        :param bandwidth_min: The bandwidth_min of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth_min = bandwidth_min

    @property
    def bandwidth_per_node(self):
        """Gets the bandwidth_per_node of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The bandwidth_per_node of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_per_node

    @bandwidth_per_node.setter
    def bandwidth_per_node(self, bandwidth_per_node):
        """Sets the bandwidth_per_node of this DescribeDataFlowBandwidthResponse.


        :param bandwidth_per_node: The bandwidth_per_node of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth_per_node = bandwidth_per_node

    @property
    def create_time(self):
        """Gets the create_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The create_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeDataFlowBandwidthResponse.


        :param create_time: The create_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def file_system_id(self):
        """Gets the file_system_id of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The file_system_id of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this DescribeDataFlowBandwidthResponse.


        :param file_system_id: The file_system_id of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def status(self):
        """Gets the status of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The status of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeDataFlowBandwidthResponse.


        :param status: The status of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501


        :return: The update_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeDataFlowBandwidthResponse.


        :param update_time: The update_time of this DescribeDataFlowBandwidthResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDataFlowBandwidthResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDataFlowBandwidthResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDataFlowBandwidthResponse):
            return True

        return self.to_dict() != other.to_dict()
