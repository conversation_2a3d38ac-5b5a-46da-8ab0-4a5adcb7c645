# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDirectConnectGatewayAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'associate_cens': 'list[AssociateCenForDescribeDirectConnectGatewayAttributesOutput]',
        'business_status': 'str',
        'creation_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'direct_connect_gateway_id': 'str',
        'direct_connect_gateway_name': 'str',
        'enable_ipv6': 'bool',
        'lock_reason': 'str',
        'overdue_time': 'str',
        'request_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeDirectConnectGatewayAttributesOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'associate_cens': 'AssociateCens',
        'business_status': 'BusinessStatus',
        'creation_time': 'CreationTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'direct_connect_gateway_name': 'DirectConnectGatewayName',
        'enable_ipv6': 'EnableIpv6',
        'lock_reason': 'LockReason',
        'overdue_time': 'OverdueTime',
        'request_id': 'RequestId',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, associate_cens=None, business_status=None, creation_time=None, deleted_time=None, description=None, direct_connect_gateway_id=None, direct_connect_gateway_name=None, enable_ipv6=None, lock_reason=None, overdue_time=None, request_id=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeDirectConnectGatewayAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._associate_cens = None
        self._business_status = None
        self._creation_time = None
        self._deleted_time = None
        self._description = None
        self._direct_connect_gateway_id = None
        self._direct_connect_gateway_name = None
        self._enable_ipv6 = None
        self._lock_reason = None
        self._overdue_time = None
        self._request_id = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if associate_cens is not None:
            self.associate_cens = associate_cens
        if business_status is not None:
            self.business_status = business_status
        if creation_time is not None:
            self.creation_time = creation_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if direct_connect_gateway_name is not None:
            self.direct_connect_gateway_name = direct_connect_gateway_name
        if enable_ipv6 is not None:
            self.enable_ipv6 = enable_ipv6
        if lock_reason is not None:
            self.lock_reason = lock_reason
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if request_id is not None:
            self.request_id = request_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeDirectConnectGatewayAttributesResponse.


        :param account_id: The account_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def associate_cens(self):
        """Gets the associate_cens of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The associate_cens of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: list[AssociateCenForDescribeDirectConnectGatewayAttributesOutput]
        """
        return self._associate_cens

    @associate_cens.setter
    def associate_cens(self, associate_cens):
        """Sets the associate_cens of this DescribeDirectConnectGatewayAttributesResponse.


        :param associate_cens: The associate_cens of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: list[AssociateCenForDescribeDirectConnectGatewayAttributesOutput]
        """

        self._associate_cens = associate_cens

    @property
    def business_status(self):
        """Gets the business_status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The business_status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DescribeDirectConnectGatewayAttributesResponse.


        :param business_status: The business_status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def creation_time(self):
        """Gets the creation_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The creation_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DescribeDirectConnectGatewayAttributesResponse.


        :param creation_time: The creation_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The deleted_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this DescribeDirectConnectGatewayAttributesResponse.


        :param deleted_time: The deleted_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The description of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeDirectConnectGatewayAttributesResponse.


        :param description: The description of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The direct_connect_gateway_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this DescribeDirectConnectGatewayAttributesResponse.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def direct_connect_gateway_name(self):
        """Gets the direct_connect_gateway_name of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The direct_connect_gateway_name of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_name

    @direct_connect_gateway_name.setter
    def direct_connect_gateway_name(self, direct_connect_gateway_name):
        """Sets the direct_connect_gateway_name of this DescribeDirectConnectGatewayAttributesResponse.


        :param direct_connect_gateway_name: The direct_connect_gateway_name of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_name = direct_connect_gateway_name

    @property
    def enable_ipv6(self):
        """Gets the enable_ipv6 of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The enable_ipv6 of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enable_ipv6

    @enable_ipv6.setter
    def enable_ipv6(self, enable_ipv6):
        """Sets the enable_ipv6 of this DescribeDirectConnectGatewayAttributesResponse.


        :param enable_ipv6: The enable_ipv6 of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._enable_ipv6 = enable_ipv6

    @property
    def lock_reason(self):
        """Gets the lock_reason of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The lock_reason of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._lock_reason

    @lock_reason.setter
    def lock_reason(self, lock_reason):
        """Sets the lock_reason of this DescribeDirectConnectGatewayAttributesResponse.


        :param lock_reason: The lock_reason of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._lock_reason = lock_reason

    @property
    def overdue_time(self):
        """Gets the overdue_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The overdue_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this DescribeDirectConnectGatewayAttributesResponse.


        :param overdue_time: The overdue_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def request_id(self):
        """Gets the request_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeDirectConnectGatewayAttributesResponse.


        :param request_id: The request_id of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def status(self):
        """Gets the status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeDirectConnectGatewayAttributesResponse.


        :param status: The status of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeDirectConnectGatewayAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeDirectConnectGatewayAttributesResponse.


        :param tags: The tags of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeDirectConnectGatewayAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeDirectConnectGatewayAttributesResponse.


        :param update_time: The update_time of this DescribeDirectConnectGatewayAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDirectConnectGatewayAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDirectConnectGatewayAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDirectConnectGatewayAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
