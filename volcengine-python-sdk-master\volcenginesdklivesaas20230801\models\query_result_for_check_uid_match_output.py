# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryResultForCheckUidMatchOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'douyin_id': 'str',
        'match': 'bool',
        'uid': 'str'
    }

    attribute_map = {
        'douyin_id': 'DouyinId',
        'match': 'Match',
        'uid': 'Uid'
    }

    def __init__(self, douyin_id=None, match=None, uid=None, _configuration=None):  # noqa: E501
        """QueryResultForCheckUidMatchOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._douyin_id = None
        self._match = None
        self._uid = None
        self.discriminator = None

        if douyin_id is not None:
            self.douyin_id = douyin_id
        if match is not None:
            self.match = match
        if uid is not None:
            self.uid = uid

    @property
    def douyin_id(self):
        """Gets the douyin_id of this QueryResultForCheckUidMatchOutput.  # noqa: E501


        :return: The douyin_id of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :rtype: str
        """
        return self._douyin_id

    @douyin_id.setter
    def douyin_id(self, douyin_id):
        """Sets the douyin_id of this QueryResultForCheckUidMatchOutput.


        :param douyin_id: The douyin_id of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :type: str
        """

        self._douyin_id = douyin_id

    @property
    def match(self):
        """Gets the match of this QueryResultForCheckUidMatchOutput.  # noqa: E501


        :return: The match of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :rtype: bool
        """
        return self._match

    @match.setter
    def match(self, match):
        """Sets the match of this QueryResultForCheckUidMatchOutput.


        :param match: The match of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :type: bool
        """

        self._match = match

    @property
    def uid(self):
        """Gets the uid of this QueryResultForCheckUidMatchOutput.  # noqa: E501


        :return: The uid of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this QueryResultForCheckUidMatchOutput.


        :param uid: The uid of this QueryResultForCheckUidMatchOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryResultForCheckUidMatchOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryResultForCheckUidMatchOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryResultForCheckUidMatchOutput):
            return True

        return self.to_dict() != other.to_dict()
