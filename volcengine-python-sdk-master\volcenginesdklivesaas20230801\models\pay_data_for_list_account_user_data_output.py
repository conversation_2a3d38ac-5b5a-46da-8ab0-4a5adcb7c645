# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PayDataForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pay_count': 'int',
        'total_amount': 'str'
    }

    attribute_map = {
        'pay_count': 'PayCount',
        'total_amount': 'TotalAmount'
    }

    def __init__(self, pay_count=None, total_amount=None, _configuration=None):  # noqa: E501
        """PayDataForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pay_count = None
        self._total_amount = None
        self.discriminator = None

        if pay_count is not None:
            self.pay_count = pay_count
        if total_amount is not None:
            self.total_amount = total_amount

    @property
    def pay_count(self):
        """Gets the pay_count of this PayDataForListAccountUserDataOutput.  # noqa: E501


        :return: The pay_count of this PayDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._pay_count

    @pay_count.setter
    def pay_count(self, pay_count):
        """Sets the pay_count of this PayDataForListAccountUserDataOutput.


        :param pay_count: The pay_count of this PayDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._pay_count = pay_count

    @property
    def total_amount(self):
        """Gets the total_amount of this PayDataForListAccountUserDataOutput.  # noqa: E501


        :return: The total_amount of this PayDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._total_amount

    @total_amount.setter
    def total_amount(self, total_amount):
        """Sets the total_amount of this PayDataForListAccountUserDataOutput.


        :param total_amount: The total_amount of this PayDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._total_amount = total_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PayDataForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PayDataForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PayDataForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
