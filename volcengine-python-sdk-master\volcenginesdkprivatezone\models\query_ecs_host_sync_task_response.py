# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryEcsHostSyncTaskResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ecs_regions': 'list[EcsRegionForQueryEcsHostSyncTaskOutput]',
        'status': 'str',
        'zid': 'int'
    }

    attribute_map = {
        'ecs_regions': 'EcsRegions',
        'status': 'Status',
        'zid': 'ZID'
    }

    def __init__(self, ecs_regions=None, status=None, zid=None, _configuration=None):  # noqa: E501
        """QueryEcsHostSyncTaskResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ecs_regions = None
        self._status = None
        self._zid = None
        self.discriminator = None

        if ecs_regions is not None:
            self.ecs_regions = ecs_regions
        if status is not None:
            self.status = status
        if zid is not None:
            self.zid = zid

    @property
    def ecs_regions(self):
        """Gets the ecs_regions of this QueryEcsHostSyncTaskResponse.  # noqa: E501


        :return: The ecs_regions of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :rtype: list[EcsRegionForQueryEcsHostSyncTaskOutput]
        """
        return self._ecs_regions

    @ecs_regions.setter
    def ecs_regions(self, ecs_regions):
        """Sets the ecs_regions of this QueryEcsHostSyncTaskResponse.


        :param ecs_regions: The ecs_regions of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :type: list[EcsRegionForQueryEcsHostSyncTaskOutput]
        """

        self._ecs_regions = ecs_regions

    @property
    def status(self):
        """Gets the status of this QueryEcsHostSyncTaskResponse.  # noqa: E501


        :return: The status of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this QueryEcsHostSyncTaskResponse.


        :param status: The status of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def zid(self):
        """Gets the zid of this QueryEcsHostSyncTaskResponse.  # noqa: E501


        :return: The zid of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this QueryEcsHostSyncTaskResponse.


        :param zid: The zid of this QueryEcsHostSyncTaskResponse.  # noqa: E501
        :type: int
        """

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryEcsHostSyncTaskResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryEcsHostSyncTaskResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryEcsHostSyncTaskResponse):
            return True

        return self.to_dict() != other.to_dict()
