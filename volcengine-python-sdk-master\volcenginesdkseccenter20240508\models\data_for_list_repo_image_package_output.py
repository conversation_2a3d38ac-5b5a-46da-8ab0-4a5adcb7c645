# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRepoImagePackageOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'license': 'list[str]',
        'pkg_name': 'str',
        'pkg_path': 'str',
        'pkg_type': 'str',
        'pkg_ver': 'str'
    }

    attribute_map = {
        'id': 'ID',
        'license': 'License',
        'pkg_name': 'PkgName',
        'pkg_path': 'PkgPath',
        'pkg_type': 'PkgType',
        'pkg_ver': 'PkgVer'
    }

    def __init__(self, id=None, license=None, pkg_name=None, pkg_path=None, pkg_type=None, pkg_ver=None, _configuration=None):  # noqa: E501
        """DataForListRepoImagePackageOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._license = None
        self._pkg_name = None
        self._pkg_path = None
        self._pkg_type = None
        self._pkg_ver = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if license is not None:
            self.license = license
        if pkg_name is not None:
            self.pkg_name = pkg_name
        if pkg_path is not None:
            self.pkg_path = pkg_path
        if pkg_type is not None:
            self.pkg_type = pkg_type
        if pkg_ver is not None:
            self.pkg_ver = pkg_ver

    @property
    def id(self):
        """Gets the id of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The id of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRepoImagePackageOutput.


        :param id: The id of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def license(self):
        """Gets the license of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The license of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._license

    @license.setter
    def license(self, license):
        """Sets the license of this DataForListRepoImagePackageOutput.


        :param license: The license of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: list[str]
        """

        self._license = license

    @property
    def pkg_name(self):
        """Gets the pkg_name of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The pkg_name of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, pkg_name):
        """Sets the pkg_name of this DataForListRepoImagePackageOutput.


        :param pkg_name: The pkg_name of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: str
        """

        self._pkg_name = pkg_name

    @property
    def pkg_path(self):
        """Gets the pkg_path of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The pkg_path of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_path

    @pkg_path.setter
    def pkg_path(self, pkg_path):
        """Sets the pkg_path of this DataForListRepoImagePackageOutput.


        :param pkg_path: The pkg_path of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: str
        """

        self._pkg_path = pkg_path

    @property
    def pkg_type(self):
        """Gets the pkg_type of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The pkg_type of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_type

    @pkg_type.setter
    def pkg_type(self, pkg_type):
        """Sets the pkg_type of this DataForListRepoImagePackageOutput.


        :param pkg_type: The pkg_type of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: str
        """

        self._pkg_type = pkg_type

    @property
    def pkg_ver(self):
        """Gets the pkg_ver of this DataForListRepoImagePackageOutput.  # noqa: E501


        :return: The pkg_ver of this DataForListRepoImagePackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_ver

    @pkg_ver.setter
    def pkg_ver(self, pkg_ver):
        """Sets the pkg_ver of this DataForListRepoImagePackageOutput.


        :param pkg_ver: The pkg_ver of this DataForListRepoImagePackageOutput.  # noqa: E501
        :type: str
        """

        self._pkg_ver = pkg_ver

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRepoImagePackageOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRepoImagePackageOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRepoImagePackageOutput):
            return True

        return self.to_dict() != other.to_dict()
