# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListUserBehaviorDataAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'end_time': 'int',
        'extra': 'str',
        'is_merge': 'int',
        'is_merge_user_id': 'int',
        'name': 'str',
        'page_item_count': 'int',
        'page_no': 'int',
        'play_status': 'int',
        'scroll_id': 'str',
        'source': 'int',
        'start_time': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'end_time': 'EndTime',
        'extra': 'Extra',
        'is_merge': 'IsMerge',
        'is_merge_user_id': 'IsMergeUserId',
        'name': 'Name',
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo',
        'play_status': 'PlayStatus',
        'scroll_id': 'ScrollId',
        'source': 'Source',
        'start_time': 'StartTime'
    }

    def __init__(self, activity_id=None, end_time=None, extra=None, is_merge=None, is_merge_user_id=None, name=None, page_item_count=None, page_no=None, play_status=None, scroll_id=None, source=None, start_time=None, _configuration=None):  # noqa: E501
        """ListUserBehaviorDataAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._end_time = None
        self._extra = None
        self._is_merge = None
        self._is_merge_user_id = None
        self._name = None
        self._page_item_count = None
        self._page_no = None
        self._play_status = None
        self._scroll_id = None
        self._source = None
        self._start_time = None
        self.discriminator = None

        self.activity_id = activity_id
        if end_time is not None:
            self.end_time = end_time
        if extra is not None:
            self.extra = extra
        if is_merge is not None:
            self.is_merge = is_merge
        if is_merge_user_id is not None:
            self.is_merge_user_id = is_merge_user_id
        if name is not None:
            self.name = name
        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no
        if play_status is not None:
            self.play_status = play_status
        if scroll_id is not None:
            self.scroll_id = scroll_id
        if source is not None:
            self.source = source
        if start_time is not None:
            self.start_time = start_time

    @property
    def activity_id(self):
        """Gets the activity_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The activity_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListUserBehaviorDataAPIRequest.


        :param activity_id: The activity_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def end_time(self):
        """Gets the end_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The end_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ListUserBehaviorDataAPIRequest.


        :param end_time: The end_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def extra(self):
        """Gets the extra of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The extra of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this ListUserBehaviorDataAPIRequest.


        :param extra: The extra of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def is_merge(self):
        """Gets the is_merge of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The is_merge of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_merge

    @is_merge.setter
    def is_merge(self, is_merge):
        """Sets the is_merge of this ListUserBehaviorDataAPIRequest.


        :param is_merge: The is_merge of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_merge = is_merge

    @property
    def is_merge_user_id(self):
        """Gets the is_merge_user_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The is_merge_user_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_merge_user_id

    @is_merge_user_id.setter
    def is_merge_user_id(self, is_merge_user_id):
        """Sets the is_merge_user_id of this ListUserBehaviorDataAPIRequest.


        :param is_merge_user_id: The is_merge_user_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._is_merge_user_id = is_merge_user_id

    @property
    def name(self):
        """Gets the name of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The name of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListUserBehaviorDataAPIRequest.


        :param name: The name of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_item_count(self):
        """Gets the page_item_count of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The page_item_count of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this ListUserBehaviorDataAPIRequest.


        :param page_item_count: The page_item_count of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The page_no of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this ListUserBehaviorDataAPIRequest.


        :param page_no: The page_no of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def play_status(self):
        """Gets the play_status of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The play_status of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._play_status

    @play_status.setter
    def play_status(self, play_status):
        """Sets the play_status of this ListUserBehaviorDataAPIRequest.


        :param play_status: The play_status of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._play_status = play_status

    @property
    def scroll_id(self):
        """Gets the scroll_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The scroll_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._scroll_id

    @scroll_id.setter
    def scroll_id(self, scroll_id):
        """Sets the scroll_id of this ListUserBehaviorDataAPIRequest.


        :param scroll_id: The scroll_id of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: str
        """

        self._scroll_id = scroll_id

    @property
    def source(self):
        """Gets the source of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The source of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ListUserBehaviorDataAPIRequest.


        :param source: The source of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._source = source

    @property
    def start_time(self):
        """Gets the start_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501


        :return: The start_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ListUserBehaviorDataAPIRequest.


        :param start_time: The start_time of this ListUserBehaviorDataAPIRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListUserBehaviorDataAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListUserBehaviorDataAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListUserBehaviorDataAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
