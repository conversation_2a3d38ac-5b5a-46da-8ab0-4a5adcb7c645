# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GeneralIdleRuleForUpdateResourceQueueInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enabled': 'bool',
        'id': 'str',
        'idle_shutdown_rule_types': 'list[str]',
        'minute_period': 'int',
        'period': 'int',
        'threshold': 'float'
    }

    attribute_map = {
        'enabled': 'Enabled',
        'id': 'Id',
        'idle_shutdown_rule_types': 'IdleShutdownRuleTypes',
        'minute_period': 'MinutePeriod',
        'period': 'Period',
        'threshold': 'Threshold'
    }

    def __init__(self, enabled=None, id=None, idle_shutdown_rule_types=None, minute_period=None, period=None, threshold=None, _configuration=None):  # noqa: E501
        """GeneralIdleRuleForUpdateResourceQueueInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enabled = None
        self._id = None
        self._idle_shutdown_rule_types = None
        self._minute_period = None
        self._period = None
        self._threshold = None
        self.discriminator = None

        if enabled is not None:
            self.enabled = enabled
        if id is not None:
            self.id = id
        if idle_shutdown_rule_types is not None:
            self.idle_shutdown_rule_types = idle_shutdown_rule_types
        if minute_period is not None:
            self.minute_period = minute_period
        if period is not None:
            self.period = period
        if threshold is not None:
            self.threshold = threshold

    @property
    def enabled(self):
        """Gets the enabled of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The enabled of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param enabled: The enabled of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def id(self):
        """Gets the id of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The id of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param id: The id of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def idle_shutdown_rule_types(self):
        """Gets the idle_shutdown_rule_types of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The idle_shutdown_rule_types of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._idle_shutdown_rule_types

    @idle_shutdown_rule_types.setter
    def idle_shutdown_rule_types(self, idle_shutdown_rule_types):
        """Sets the idle_shutdown_rule_types of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param idle_shutdown_rule_types: The idle_shutdown_rule_types of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Cpu", " Gpu"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(idle_shutdown_rule_types).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `idle_shutdown_rule_types` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(idle_shutdown_rule_types) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._idle_shutdown_rule_types = idle_shutdown_rule_types

    @property
    def minute_period(self):
        """Gets the minute_period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The minute_period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: int
        """
        return self._minute_period

    @minute_period.setter
    def minute_period(self, minute_period):
        """Sets the minute_period of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param minute_period: The minute_period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: int
        """

        self._minute_period = minute_period

    @property
    def period(self):
        """Gets the period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param period: The period of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def threshold(self):
        """Gets the threshold of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501


        :return: The threshold of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :rtype: float
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this GeneralIdleRuleForUpdateResourceQueueInput.


        :param threshold: The threshold of this GeneralIdleRuleForUpdateResourceQueueInput.  # noqa: E501
        :type: float
        """

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GeneralIdleRuleForUpdateResourceQueueInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GeneralIdleRuleForUpdateResourceQueueInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GeneralIdleRuleForUpdateResourceQueueInput):
            return True

        return self.to_dict() != other.to_dict()
