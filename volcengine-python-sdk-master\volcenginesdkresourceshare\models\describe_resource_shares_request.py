# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeResourceSharesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'int',
        'name': 'str',
        'next_token': 'str',
        'permission_trn': 'str',
        'resource_owner': 'str',
        'resource_share_status': 'str',
        'resource_share_trns': 'str'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'name': 'Name',
        'next_token': 'NextToken',
        'permission_trn': 'PermissionTrn',
        'resource_owner': 'ResourceOwner',
        'resource_share_status': 'ResourceShareStatus',
        'resource_share_trns': 'ResourceShareTrns'
    }

    def __init__(self, max_results=None, name=None, next_token=None, permission_trn=None, resource_owner=None, resource_share_status=None, resource_share_trns=None, _configuration=None):  # noqa: E501
        """DescribeResourceSharesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._name = None
        self._next_token = None
        self._permission_trn = None
        self._resource_owner = None
        self._resource_share_status = None
        self._resource_share_trns = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if name is not None:
            self.name = name
        if next_token is not None:
            self.next_token = next_token
        if permission_trn is not None:
            self.permission_trn = permission_trn
        self.resource_owner = resource_owner
        if resource_share_status is not None:
            self.resource_share_status = resource_share_status
        if resource_share_trns is not None:
            self.resource_share_trns = resource_share_trns

    @property
    def max_results(self):
        """Gets the max_results of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The max_results of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeResourceSharesRequest.


        :param max_results: The max_results of this DescribeResourceSharesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def name(self):
        """Gets the name of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The name of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeResourceSharesRequest.


        :param name: The name of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def next_token(self):
        """Gets the next_token of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The next_token of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeResourceSharesRequest.


        :param next_token: The next_token of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def permission_trn(self):
        """Gets the permission_trn of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The permission_trn of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._permission_trn

    @permission_trn.setter
    def permission_trn(self, permission_trn):
        """Sets the permission_trn of this DescribeResourceSharesRequest.


        :param permission_trn: The permission_trn of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """

        self._permission_trn = permission_trn

    @property
    def resource_owner(self):
        """Gets the resource_owner of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The resource_owner of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_owner

    @resource_owner.setter
    def resource_owner(self, resource_owner):
        """Sets the resource_owner of this DescribeResourceSharesRequest.


        :param resource_owner: The resource_owner of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_owner is None:
            raise ValueError("Invalid value for `resource_owner`, must not be `None`")  # noqa: E501

        self._resource_owner = resource_owner

    @property
    def resource_share_status(self):
        """Gets the resource_share_status of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The resource_share_status of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_status

    @resource_share_status.setter
    def resource_share_status(self, resource_share_status):
        """Sets the resource_share_status of this DescribeResourceSharesRequest.


        :param resource_share_status: The resource_share_status of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """

        self._resource_share_status = resource_share_status

    @property
    def resource_share_trns(self):
        """Gets the resource_share_trns of this DescribeResourceSharesRequest.  # noqa: E501


        :return: The resource_share_trns of this DescribeResourceSharesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trns

    @resource_share_trns.setter
    def resource_share_trns(self, resource_share_trns):
        """Sets the resource_share_trns of this DescribeResourceSharesRequest.


        :param resource_share_trns: The resource_share_trns of this DescribeResourceSharesRequest.  # noqa: E501
        :type: str
        """

        self._resource_share_trns = resource_share_trns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeResourceSharesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeResourceSharesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeResourceSharesRequest):
            return True

        return self.to_dict() != other.to_dict()
