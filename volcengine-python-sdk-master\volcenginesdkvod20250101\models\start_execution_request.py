# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartExecutionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'control': 'ControlForStartExecutionInput',
        'input': 'InputForStartExecutionInput',
        'multi_inputs': 'list[MultiInputForStartExecutionInput]',
        'operation': 'OperationForStartExecutionInput'
    }

    attribute_map = {
        'control': 'Control',
        'input': 'Input',
        'multi_inputs': 'MultiInputs',
        'operation': 'Operation'
    }

    def __init__(self, control=None, input=None, multi_inputs=None, operation=None, _configuration=None):  # noqa: E501
        """StartExecutionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._control = None
        self._input = None
        self._multi_inputs = None
        self._operation = None
        self.discriminator = None

        if control is not None:
            self.control = control
        if input is not None:
            self.input = input
        if multi_inputs is not None:
            self.multi_inputs = multi_inputs
        if operation is not None:
            self.operation = operation

    @property
    def control(self):
        """Gets the control of this StartExecutionRequest.  # noqa: E501


        :return: The control of this StartExecutionRequest.  # noqa: E501
        :rtype: ControlForStartExecutionInput
        """
        return self._control

    @control.setter
    def control(self, control):
        """Sets the control of this StartExecutionRequest.


        :param control: The control of this StartExecutionRequest.  # noqa: E501
        :type: ControlForStartExecutionInput
        """

        self._control = control

    @property
    def input(self):
        """Gets the input of this StartExecutionRequest.  # noqa: E501


        :return: The input of this StartExecutionRequest.  # noqa: E501
        :rtype: InputForStartExecutionInput
        """
        return self._input

    @input.setter
    def input(self, input):
        """Sets the input of this StartExecutionRequest.


        :param input: The input of this StartExecutionRequest.  # noqa: E501
        :type: InputForStartExecutionInput
        """

        self._input = input

    @property
    def multi_inputs(self):
        """Gets the multi_inputs of this StartExecutionRequest.  # noqa: E501


        :return: The multi_inputs of this StartExecutionRequest.  # noqa: E501
        :rtype: list[MultiInputForStartExecutionInput]
        """
        return self._multi_inputs

    @multi_inputs.setter
    def multi_inputs(self, multi_inputs):
        """Sets the multi_inputs of this StartExecutionRequest.


        :param multi_inputs: The multi_inputs of this StartExecutionRequest.  # noqa: E501
        :type: list[MultiInputForStartExecutionInput]
        """

        self._multi_inputs = multi_inputs

    @property
    def operation(self):
        """Gets the operation of this StartExecutionRequest.  # noqa: E501


        :return: The operation of this StartExecutionRequest.  # noqa: E501
        :rtype: OperationForStartExecutionInput
        """
        return self._operation

    @operation.setter
    def operation(self, operation):
        """Sets the operation of this StartExecutionRequest.


        :param operation: The operation of this StartExecutionRequest.  # noqa: E501
        :type: OperationForStartExecutionInput
        """

        self._operation = operation

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartExecutionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartExecutionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartExecutionRequest):
            return True

        return self.to_dict() != other.to_dict()
