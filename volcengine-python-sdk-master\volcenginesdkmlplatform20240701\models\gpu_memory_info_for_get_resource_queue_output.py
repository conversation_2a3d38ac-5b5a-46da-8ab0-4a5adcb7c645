# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GpuMemoryInfoForGetResourceQueueOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'gpu_type': 'str',
        'memory_gi_b': 'float'
    }

    attribute_map = {
        'gpu_type': 'GpuType',
        'memory_gi_b': 'MemoryGiB'
    }

    def __init__(self, gpu_type=None, memory_gi_b=None, _configuration=None):  # noqa: E501
        """GpuMemoryInfoForGetResourceQueueOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._gpu_type = None
        self._memory_gi_b = None
        self.discriminator = None

        if gpu_type is not None:
            self.gpu_type = gpu_type
        if memory_gi_b is not None:
            self.memory_gi_b = memory_gi_b

    @property
    def gpu_type(self):
        """Gets the gpu_type of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501


        :return: The gpu_type of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._gpu_type

    @gpu_type.setter
    def gpu_type(self, gpu_type):
        """Sets the gpu_type of this GpuMemoryInfoForGetResourceQueueOutput.


        :param gpu_type: The gpu_type of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._gpu_type = gpu_type

    @property
    def memory_gi_b(self):
        """Gets the memory_gi_b of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501


        :return: The memory_gi_b of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501
        :rtype: float
        """
        return self._memory_gi_b

    @memory_gi_b.setter
    def memory_gi_b(self, memory_gi_b):
        """Sets the memory_gi_b of this GpuMemoryInfoForGetResourceQueueOutput.


        :param memory_gi_b: The memory_gi_b of this GpuMemoryInfoForGetResourceQueueOutput.  # noqa: E501
        :type: float
        """

        self._memory_gi_b = memory_gi_b

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GpuMemoryInfoForGetResourceQueueOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GpuMemoryInfoForGetResourceQueueOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GpuMemoryInfoForGetResourceQueueOutput):
            return True

        return self.to_dict() != other.to_dict()
