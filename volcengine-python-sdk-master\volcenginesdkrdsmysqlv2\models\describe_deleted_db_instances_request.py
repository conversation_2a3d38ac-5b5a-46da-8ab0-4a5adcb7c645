# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDeletedDBInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deletion_after_time': 'str',
        'deletion_before_time': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str'
    }

    attribute_map = {
        'deletion_after_time': 'DeletionAfterTime',
        'deletion_before_time': 'DeletionBeforeTime',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName'
    }

    def __init__(self, deletion_after_time=None, deletion_before_time=None, instance_id=None, instance_name=None, instance_status=None, page_number=None, page_size=None, project_name=None, _configuration=None):  # noqa: E501
        """DescribeDeletedDBInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deletion_after_time = None
        self._deletion_before_time = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self.discriminator = None

        if deletion_after_time is not None:
            self.deletion_after_time = deletion_after_time
        if deletion_before_time is not None:
            self.deletion_before_time = deletion_before_time
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name

    @property
    def deletion_after_time(self):
        """Gets the deletion_after_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The deletion_after_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._deletion_after_time

    @deletion_after_time.setter
    def deletion_after_time(self, deletion_after_time):
        """Sets the deletion_after_time of this DescribeDeletedDBInstancesRequest.


        :param deletion_after_time: The deletion_after_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._deletion_after_time = deletion_after_time

    @property
    def deletion_before_time(self):
        """Gets the deletion_before_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The deletion_before_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._deletion_before_time

    @deletion_before_time.setter
    def deletion_before_time(self, deletion_before_time):
        """Sets the deletion_before_time of this DescribeDeletedDBInstancesRequest.


        :param deletion_before_time: The deletion_before_time of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._deletion_before_time = deletion_before_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The instance_id of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDeletedDBInstancesRequest.


        :param instance_id: The instance_id of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The instance_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DescribeDeletedDBInstancesRequest.


        :param instance_name: The instance_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The instance_status of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this DescribeDeletedDBInstancesRequest.


        :param instance_status: The instance_status of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def page_number(self):
        """Gets the page_number of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The page_number of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeDeletedDBInstancesRequest.


        :param page_number: The page_number of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The page_size of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeDeletedDBInstancesRequest.


        :param page_size: The page_size of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501


        :return: The project_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeDeletedDBInstancesRequest.


        :param project_name: The project_name of this DescribeDeletedDBInstancesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDeletedDBInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDeletedDBInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDeletedDBInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
