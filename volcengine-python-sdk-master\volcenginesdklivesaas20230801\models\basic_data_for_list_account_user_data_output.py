# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicDataForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ban_status': 'int',
        'country': 'str',
        'external_id': 'str',
        'ip': 'str',
        'nick_name': 'str',
        'province': 'str',
        'telephone': 'str',
        'user_id': 'int',
        'watch_device': 'str',
        'watch_type': 'str'
    }

    attribute_map = {
        'ban_status': 'BanStatus',
        'country': 'Country',
        'external_id': 'ExternalId',
        'ip': 'IP',
        'nick_name': 'NickName',
        'province': 'Province',
        'telephone': 'Telephone',
        'user_id': 'UserId',
        'watch_device': 'WatchDevice',
        'watch_type': 'WatchType'
    }

    def __init__(self, ban_status=None, country=None, external_id=None, ip=None, nick_name=None, province=None, telephone=None, user_id=None, watch_device=None, watch_type=None, _configuration=None):  # noqa: E501
        """BasicDataForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ban_status = None
        self._country = None
        self._external_id = None
        self._ip = None
        self._nick_name = None
        self._province = None
        self._telephone = None
        self._user_id = None
        self._watch_device = None
        self._watch_type = None
        self.discriminator = None

        if ban_status is not None:
            self.ban_status = ban_status
        if country is not None:
            self.country = country
        if external_id is not None:
            self.external_id = external_id
        if ip is not None:
            self.ip = ip
        if nick_name is not None:
            self.nick_name = nick_name
        if province is not None:
            self.province = province
        if telephone is not None:
            self.telephone = telephone
        if user_id is not None:
            self.user_id = user_id
        if watch_device is not None:
            self.watch_device = watch_device
        if watch_type is not None:
            self.watch_type = watch_type

    @property
    def ban_status(self):
        """Gets the ban_status of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The ban_status of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._ban_status

    @ban_status.setter
    def ban_status(self, ban_status):
        """Sets the ban_status of this BasicDataForListAccountUserDataOutput.


        :param ban_status: The ban_status of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._ban_status = ban_status

    @property
    def country(self):
        """Gets the country of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The country of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this BasicDataForListAccountUserDataOutput.


        :param country: The country of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def external_id(self):
        """Gets the external_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The external_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this BasicDataForListAccountUserDataOutput.


        :param external_id: The external_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def ip(self):
        """Gets the ip of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The ip of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this BasicDataForListAccountUserDataOutput.


        :param ip: The ip of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def nick_name(self):
        """Gets the nick_name of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The nick_name of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this BasicDataForListAccountUserDataOutput.


        :param nick_name: The nick_name of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def province(self):
        """Gets the province of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The province of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this BasicDataForListAccountUserDataOutput.


        :param province: The province of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._province = province

    @property
    def telephone(self):
        """Gets the telephone of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The telephone of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._telephone

    @telephone.setter
    def telephone(self, telephone):
        """Sets the telephone of this BasicDataForListAccountUserDataOutput.


        :param telephone: The telephone of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._telephone = telephone

    @property
    def user_id(self):
        """Gets the user_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The user_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this BasicDataForListAccountUserDataOutput.


        :param user_id: The user_id of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def watch_device(self):
        """Gets the watch_device of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_device of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._watch_device

    @watch_device.setter
    def watch_device(self, watch_device):
        """Sets the watch_device of this BasicDataForListAccountUserDataOutput.


        :param watch_device: The watch_device of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._watch_device = watch_device

    @property
    def watch_type(self):
        """Gets the watch_type of this BasicDataForListAccountUserDataOutput.  # noqa: E501


        :return: The watch_type of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._watch_type

    @watch_type.setter
    def watch_type(self, watch_type):
        """Sets the watch_type of this BasicDataForListAccountUserDataOutput.


        :param watch_type: The watch_type of this BasicDataForListAccountUserDataOutput.  # noqa: E501
        :type: str
        """

        self._watch_type = watch_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicDataForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicDataForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicDataForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
