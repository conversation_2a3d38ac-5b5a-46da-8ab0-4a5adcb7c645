# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ThreatenForGetMultiLevelInstitutionDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert': 'int',
        'baseline': 'int',
        'hosts': 'int',
        'virus': 'int',
        'vuln': 'int'
    }

    attribute_map = {
        'alert': 'Alert',
        'baseline': 'Baseline',
        'hosts': 'Hosts',
        'virus': 'Virus',
        'vuln': 'Vuln'
    }

    def __init__(self, alert=None, baseline=None, hosts=None, virus=None, vuln=None, _configuration=None):  # noqa: E501
        """ThreatenForGetMultiLevelInstitutionDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert = None
        self._baseline = None
        self._hosts = None
        self._virus = None
        self._vuln = None
        self.discriminator = None

        if alert is not None:
            self.alert = alert
        if baseline is not None:
            self.baseline = baseline
        if hosts is not None:
            self.hosts = hosts
        if virus is not None:
            self.virus = virus
        if vuln is not None:
            self.vuln = vuln

    @property
    def alert(self):
        """Gets the alert of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The alert of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._alert

    @alert.setter
    def alert(self, alert):
        """Sets the alert of this ThreatenForGetMultiLevelInstitutionDetailOutput.


        :param alert: The alert of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._alert = alert

    @property
    def baseline(self):
        """Gets the baseline of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The baseline of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline

    @baseline.setter
    def baseline(self, baseline):
        """Sets the baseline of this ThreatenForGetMultiLevelInstitutionDetailOutput.


        :param baseline: The baseline of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._baseline = baseline

    @property
    def hosts(self):
        """Gets the hosts of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The hosts of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._hosts

    @hosts.setter
    def hosts(self, hosts):
        """Sets the hosts of this ThreatenForGetMultiLevelInstitutionDetailOutput.


        :param hosts: The hosts of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._hosts = hosts

    @property
    def virus(self):
        """Gets the virus of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The virus of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._virus

    @virus.setter
    def virus(self, virus):
        """Sets the virus of this ThreatenForGetMultiLevelInstitutionDetailOutput.


        :param virus: The virus of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._virus = virus

    @property
    def vuln(self):
        """Gets the vuln of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The vuln of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._vuln

    @vuln.setter
    def vuln(self, vuln):
        """Sets the vuln of this ThreatenForGetMultiLevelInstitutionDetailOutput.


        :param vuln: The vuln of this ThreatenForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._vuln = vuln

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ThreatenForGetMultiLevelInstitutionDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ThreatenForGetMultiLevelInstitutionDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ThreatenForGetMultiLevelInstitutionDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
