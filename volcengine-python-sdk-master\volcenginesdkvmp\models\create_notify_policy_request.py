# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNotifyPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_notify_template_ids': 'list[str]',
        'description': 'str',
        'levels': 'list[LevelForCreateNotifyPolicyInput]',
        'name': 'str'
    }

    attribute_map = {
        'channel_notify_template_ids': 'ChannelNotifyTemplateIds',
        'description': 'Description',
        'levels': 'Levels',
        'name': 'Name'
    }

    def __init__(self, channel_notify_template_ids=None, description=None, levels=None, name=None, _configuration=None):  # noqa: E501
        """CreateNotifyPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_notify_template_ids = None
        self._description = None
        self._levels = None
        self._name = None
        self.discriminator = None

        if channel_notify_template_ids is not None:
            self.channel_notify_template_ids = channel_notify_template_ids
        if description is not None:
            self.description = description
        if levels is not None:
            self.levels = levels
        self.name = name

    @property
    def channel_notify_template_ids(self):
        """Gets the channel_notify_template_ids of this CreateNotifyPolicyRequest.  # noqa: E501


        :return: The channel_notify_template_ids of this CreateNotifyPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._channel_notify_template_ids

    @channel_notify_template_ids.setter
    def channel_notify_template_ids(self, channel_notify_template_ids):
        """Sets the channel_notify_template_ids of this CreateNotifyPolicyRequest.


        :param channel_notify_template_ids: The channel_notify_template_ids of this CreateNotifyPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._channel_notify_template_ids = channel_notify_template_ids

    @property
    def description(self):
        """Gets the description of this CreateNotifyPolicyRequest.  # noqa: E501


        :return: The description of this CreateNotifyPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNotifyPolicyRequest.


        :param description: The description of this CreateNotifyPolicyRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def levels(self):
        """Gets the levels of this CreateNotifyPolicyRequest.  # noqa: E501


        :return: The levels of this CreateNotifyPolicyRequest.  # noqa: E501
        :rtype: list[LevelForCreateNotifyPolicyInput]
        """
        return self._levels

    @levels.setter
    def levels(self, levels):
        """Sets the levels of this CreateNotifyPolicyRequest.


        :param levels: The levels of this CreateNotifyPolicyRequest.  # noqa: E501
        :type: list[LevelForCreateNotifyPolicyInput]
        """

        self._levels = levels

    @property
    def name(self):
        """Gets the name of this CreateNotifyPolicyRequest.  # noqa: E501


        :return: The name of this CreateNotifyPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateNotifyPolicyRequest.


        :param name: The name of this CreateNotifyPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNotifyPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNotifyPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNotifyPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
