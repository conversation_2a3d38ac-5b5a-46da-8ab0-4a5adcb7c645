# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LensOprRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binary_data_base64': 'list[str]',
        'if_color': 'int',
        'image_urls': 'list[str]',
        'req_key': 'str'
    }

    attribute_map = {
        'binary_data_base64': 'binary_data_base64',
        'if_color': 'if_color',
        'image_urls': 'image_urls',
        'req_key': 'req_key'
    }

    def __init__(self, binary_data_base64=None, if_color=None, image_urls=None, req_key=None, _configuration=None):  # noqa: E501
        """LensOprRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binary_data_base64 = None
        self._if_color = None
        self._image_urls = None
        self._req_key = None
        self.discriminator = None

        if binary_data_base64 is not None:
            self.binary_data_base64 = binary_data_base64
        if if_color is not None:
            self.if_color = if_color
        if image_urls is not None:
            self.image_urls = image_urls
        self.req_key = req_key

    @property
    def binary_data_base64(self):
        """Gets the binary_data_base64 of this LensOprRequest.  # noqa: E501


        :return: The binary_data_base64 of this LensOprRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._binary_data_base64

    @binary_data_base64.setter
    def binary_data_base64(self, binary_data_base64):
        """Sets the binary_data_base64 of this LensOprRequest.


        :param binary_data_base64: The binary_data_base64 of this LensOprRequest.  # noqa: E501
        :type: list[str]
        """

        self._binary_data_base64 = binary_data_base64

    @property
    def if_color(self):
        """Gets the if_color of this LensOprRequest.  # noqa: E501


        :return: The if_color of this LensOprRequest.  # noqa: E501
        :rtype: int
        """
        return self._if_color

    @if_color.setter
    def if_color(self, if_color):
        """Sets the if_color of this LensOprRequest.


        :param if_color: The if_color of this LensOprRequest.  # noqa: E501
        :type: int
        """

        self._if_color = if_color

    @property
    def image_urls(self):
        """Gets the image_urls of this LensOprRequest.  # noqa: E501


        :return: The image_urls of this LensOprRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._image_urls

    @image_urls.setter
    def image_urls(self, image_urls):
        """Sets the image_urls of this LensOprRequest.


        :param image_urls: The image_urls of this LensOprRequest.  # noqa: E501
        :type: list[str]
        """

        self._image_urls = image_urls

    @property
    def req_key(self):
        """Gets the req_key of this LensOprRequest.  # noqa: E501


        :return: The req_key of this LensOprRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this LensOprRequest.


        :param req_key: The req_key of this LensOprRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LensOprRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LensOprRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LensOprRequest):
            return True

        return self.to_dict() != other.to_dict()
