# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BootstrapScriptForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'effective_scope': 'EffectiveScopeForCreateClusterInput',
        'execution_fail_strategy': 'str',
        'execution_moment': 'str',
        'priority': 'str',
        'script_args': 'str',
        'script_name': 'str',
        'script_path': 'str',
        'script_type': 'str'
    }

    attribute_map = {
        'effective_scope': 'EffectiveScope',
        'execution_fail_strategy': 'ExecutionFailStrategy',
        'execution_moment': 'ExecutionMoment',
        'priority': 'Priority',
        'script_args': 'ScriptArgs',
        'script_name': 'ScriptName',
        'script_path': 'ScriptPath',
        'script_type': 'ScriptType'
    }

    def __init__(self, effective_scope=None, execution_fail_strategy=None, execution_moment=None, priority=None, script_args=None, script_name=None, script_path=None, script_type=None, _configuration=None):  # noqa: E501
        """BootstrapScriptForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._effective_scope = None
        self._execution_fail_strategy = None
        self._execution_moment = None
        self._priority = None
        self._script_args = None
        self._script_name = None
        self._script_path = None
        self._script_type = None
        self.discriminator = None

        if effective_scope is not None:
            self.effective_scope = effective_scope
        if execution_fail_strategy is not None:
            self.execution_fail_strategy = execution_fail_strategy
        if execution_moment is not None:
            self.execution_moment = execution_moment
        if priority is not None:
            self.priority = priority
        if script_args is not None:
            self.script_args = script_args
        if script_name is not None:
            self.script_name = script_name
        if script_path is not None:
            self.script_path = script_path
        if script_type is not None:
            self.script_type = script_type

    @property
    def effective_scope(self):
        """Gets the effective_scope of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The effective_scope of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: EffectiveScopeForCreateClusterInput
        """
        return self._effective_scope

    @effective_scope.setter
    def effective_scope(self, effective_scope):
        """Sets the effective_scope of this BootstrapScriptForCreateClusterInput.


        :param effective_scope: The effective_scope of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: EffectiveScopeForCreateClusterInput
        """

        self._effective_scope = effective_scope

    @property
    def execution_fail_strategy(self):
        """Gets the execution_fail_strategy of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The execution_fail_strategy of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._execution_fail_strategy

    @execution_fail_strategy.setter
    def execution_fail_strategy(self, execution_fail_strategy):
        """Sets the execution_fail_strategy of this BootstrapScriptForCreateClusterInput.


        :param execution_fail_strategy: The execution_fail_strategy of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["FAILED_CONTINUE", "FAILED_BLOCK"]  # noqa: E501
        if (self._configuration.client_side_validation and
                execution_fail_strategy not in allowed_values):
            raise ValueError(
                "Invalid value for `execution_fail_strategy` ({0}), must be one of {1}"  # noqa: E501
                .format(execution_fail_strategy, allowed_values)
            )

        self._execution_fail_strategy = execution_fail_strategy

    @property
    def execution_moment(self):
        """Gets the execution_moment of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The execution_moment of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._execution_moment

    @execution_moment.setter
    def execution_moment(self, execution_moment):
        """Sets the execution_moment of this BootstrapScriptForCreateClusterInput.


        :param execution_moment: The execution_moment of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["BEFORE_APPLICATION_INSTALL", "AFTER_APPLICATION_STARTED"]  # noqa: E501
        if (self._configuration.client_side_validation and
                execution_moment not in allowed_values):
            raise ValueError(
                "Invalid value for `execution_moment` ({0}), must be one of {1}"  # noqa: E501
                .format(execution_moment, allowed_values)
            )

        self._execution_moment = execution_moment

    @property
    def priority(self):
        """Gets the priority of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The priority of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this BootstrapScriptForCreateClusterInput.


        :param priority: The priority of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                priority is not None and len(priority) > 1000):
            raise ValueError("Invalid value for `priority`, length must be less than or equal to `1000`")  # noqa: E501
        if (self._configuration.client_side_validation and
                priority is not None and len(priority) < 1):
            raise ValueError("Invalid value for `priority`, length must be greater than or equal to `1`")  # noqa: E501

        self._priority = priority

    @property
    def script_args(self):
        """Gets the script_args of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The script_args of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._script_args

    @script_args.setter
    def script_args(self, script_args):
        """Sets the script_args of this BootstrapScriptForCreateClusterInput.


        :param script_args: The script_args of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._script_args = script_args

    @property
    def script_name(self):
        """Gets the script_name of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The script_name of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._script_name

    @script_name.setter
    def script_name(self, script_name):
        """Sets the script_name of this BootstrapScriptForCreateClusterInput.


        :param script_name: The script_name of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._script_name = script_name

    @property
    def script_path(self):
        """Gets the script_path of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The script_path of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._script_path

    @script_path.setter
    def script_path(self, script_path):
        """Sets the script_path of this BootstrapScriptForCreateClusterInput.


        :param script_path: The script_path of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._script_path = script_path

    @property
    def script_type(self):
        """Gets the script_type of this BootstrapScriptForCreateClusterInput.  # noqa: E501


        :return: The script_type of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._script_type

    @script_type.setter
    def script_type(self, script_type):
        """Sets the script_type of this BootstrapScriptForCreateClusterInput.


        :param script_type: The script_type of this BootstrapScriptForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["NORMAL", "BOOTSTRAP"]  # noqa: E501
        if (self._configuration.client_side_validation and
                script_type not in allowed_values):
            raise ValueError(
                "Invalid value for `script_type` ({0}), must be one of {1}"  # noqa: E501
                .format(script_type, allowed_values)
            )

        self._script_type = script_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BootstrapScriptForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BootstrapScriptForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BootstrapScriptForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
