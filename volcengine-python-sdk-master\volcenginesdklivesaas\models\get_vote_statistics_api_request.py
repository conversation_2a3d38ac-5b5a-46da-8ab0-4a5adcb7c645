# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVoteStatisticsAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'page_count': 'int',
        'page_no': 'int',
        'vote_id': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'page_count': 'PageCount',
        'page_no': 'PageNo',
        'vote_id': 'VoteId'
    }

    def __init__(self, activity_id=None, page_count=None, page_no=None, vote_id=None, _configuration=None):  # noqa: E501
        """GetVoteStatisticsAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._page_count = None
        self._page_no = None
        self._vote_id = None
        self.discriminator = None

        self.activity_id = activity_id
        if page_count is not None:
            self.page_count = page_count
        if page_no is not None:
            self.page_no = page_no
        self.vote_id = vote_id

    @property
    def activity_id(self):
        """Gets the activity_id of this GetVoteStatisticsAPIRequest.  # noqa: E501


        :return: The activity_id of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetVoteStatisticsAPIRequest.


        :param activity_id: The activity_id of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def page_count(self):
        """Gets the page_count of this GetVoteStatisticsAPIRequest.  # noqa: E501


        :return: The page_count of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_count

    @page_count.setter
    def page_count(self, page_count):
        """Sets the page_count of this GetVoteStatisticsAPIRequest.


        :param page_count: The page_count of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_count = page_count

    @property
    def page_no(self):
        """Gets the page_no of this GetVoteStatisticsAPIRequest.  # noqa: E501


        :return: The page_no of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this GetVoteStatisticsAPIRequest.


        :param page_no: The page_no of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def vote_id(self):
        """Gets the vote_id of this GetVoteStatisticsAPIRequest.  # noqa: E501


        :return: The vote_id of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._vote_id

    @vote_id.setter
    def vote_id(self, vote_id):
        """Sets the vote_id of this GetVoteStatisticsAPIRequest.


        :param vote_id: The vote_id of this GetVoteStatisticsAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vote_id is None:
            raise ValueError("Invalid value for `vote_id`, must not be `None`")  # noqa: E501

        self._vote_id = vote_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVoteStatisticsAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVoteStatisticsAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVoteStatisticsAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
