# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackupForDescribeBackupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_end_time': 'str',
        'backup_file_name': 'str',
        'backup_file_size': 'int',
        'backup_id': 'str',
        'backup_method': 'str',
        'backup_region': 'str',
        'backup_start_time': 'str',
        'backup_status': 'str',
        'backup_type': 'str',
        'consistent_time': 'str',
        'create_type': 'str',
        'db_engine_version': 'str',
        'db_table_infos': 'list[DBTableInfoForDescribeBackupsOutput]',
        'download_status': 'str',
        'error_message': 'str',
        'expired_time': 'str',
        'is_encrypted': 'bool',
        'is_expired': 'bool'
    }

    attribute_map = {
        'backup_end_time': 'BackupEndTime',
        'backup_file_name': 'BackupFileName',
        'backup_file_size': 'BackupFileSize',
        'backup_id': 'BackupId',
        'backup_method': 'BackupMethod',
        'backup_region': 'BackupRegion',
        'backup_start_time': 'BackupStartTime',
        'backup_status': 'BackupStatus',
        'backup_type': 'BackupType',
        'consistent_time': 'ConsistentTime',
        'create_type': 'CreateType',
        'db_engine_version': 'DBEngineVersion',
        'db_table_infos': 'DBTableInfos',
        'download_status': 'DownloadStatus',
        'error_message': 'ErrorMessage',
        'expired_time': 'ExpiredTime',
        'is_encrypted': 'IsEncrypted',
        'is_expired': 'IsExpired'
    }

    def __init__(self, backup_end_time=None, backup_file_name=None, backup_file_size=None, backup_id=None, backup_method=None, backup_region=None, backup_start_time=None, backup_status=None, backup_type=None, consistent_time=None, create_type=None, db_engine_version=None, db_table_infos=None, download_status=None, error_message=None, expired_time=None, is_encrypted=None, is_expired=None, _configuration=None):  # noqa: E501
        """BackupForDescribeBackupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_end_time = None
        self._backup_file_name = None
        self._backup_file_size = None
        self._backup_id = None
        self._backup_method = None
        self._backup_region = None
        self._backup_start_time = None
        self._backup_status = None
        self._backup_type = None
        self._consistent_time = None
        self._create_type = None
        self._db_engine_version = None
        self._db_table_infos = None
        self._download_status = None
        self._error_message = None
        self._expired_time = None
        self._is_encrypted = None
        self._is_expired = None
        self.discriminator = None

        if backup_end_time is not None:
            self.backup_end_time = backup_end_time
        if backup_file_name is not None:
            self.backup_file_name = backup_file_name
        if backup_file_size is not None:
            self.backup_file_size = backup_file_size
        if backup_id is not None:
            self.backup_id = backup_id
        if backup_method is not None:
            self.backup_method = backup_method
        if backup_region is not None:
            self.backup_region = backup_region
        if backup_start_time is not None:
            self.backup_start_time = backup_start_time
        if backup_status is not None:
            self.backup_status = backup_status
        if backup_type is not None:
            self.backup_type = backup_type
        if consistent_time is not None:
            self.consistent_time = consistent_time
        if create_type is not None:
            self.create_type = create_type
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if db_table_infos is not None:
            self.db_table_infos = db_table_infos
        if download_status is not None:
            self.download_status = download_status
        if error_message is not None:
            self.error_message = error_message
        if expired_time is not None:
            self.expired_time = expired_time
        if is_encrypted is not None:
            self.is_encrypted = is_encrypted
        if is_expired is not None:
            self.is_expired = is_expired

    @property
    def backup_end_time(self):
        """Gets the backup_end_time of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_end_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_end_time

    @backup_end_time.setter
    def backup_end_time(self, backup_end_time):
        """Sets the backup_end_time of this BackupForDescribeBackupsOutput.


        :param backup_end_time: The backup_end_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_end_time = backup_end_time

    @property
    def backup_file_name(self):
        """Gets the backup_file_name of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_file_name of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_file_name

    @backup_file_name.setter
    def backup_file_name(self, backup_file_name):
        """Sets the backup_file_name of this BackupForDescribeBackupsOutput.


        :param backup_file_name: The backup_file_name of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_file_name = backup_file_name

    @property
    def backup_file_size(self):
        """Gets the backup_file_size of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_file_size of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._backup_file_size

    @backup_file_size.setter
    def backup_file_size(self, backup_file_size):
        """Sets the backup_file_size of this BackupForDescribeBackupsOutput.


        :param backup_file_size: The backup_file_size of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: int
        """

        self._backup_file_size = backup_file_size

    @property
    def backup_id(self):
        """Gets the backup_id of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_id of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this BackupForDescribeBackupsOutput.


        :param backup_id: The backup_id of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def backup_method(self):
        """Gets the backup_method of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_method of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_method

    @backup_method.setter
    def backup_method(self, backup_method):
        """Sets the backup_method of this BackupForDescribeBackupsOutput.


        :param backup_method: The backup_method of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_method = backup_method

    @property
    def backup_region(self):
        """Gets the backup_region of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_region of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_region

    @backup_region.setter
    def backup_region(self, backup_region):
        """Sets the backup_region of this BackupForDescribeBackupsOutput.


        :param backup_region: The backup_region of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_region = backup_region

    @property
    def backup_start_time(self):
        """Gets the backup_start_time of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_start_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_start_time

    @backup_start_time.setter
    def backup_start_time(self, backup_start_time):
        """Sets the backup_start_time of this BackupForDescribeBackupsOutput.


        :param backup_start_time: The backup_start_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_start_time = backup_start_time

    @property
    def backup_status(self):
        """Gets the backup_status of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_status of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_status

    @backup_status.setter
    def backup_status(self, backup_status):
        """Sets the backup_status of this BackupForDescribeBackupsOutput.


        :param backup_status: The backup_status of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_status = backup_status

    @property
    def backup_type(self):
        """Gets the backup_type of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The backup_type of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_type

    @backup_type.setter
    def backup_type(self, backup_type):
        """Sets the backup_type of this BackupForDescribeBackupsOutput.


        :param backup_type: The backup_type of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_type = backup_type

    @property
    def consistent_time(self):
        """Gets the consistent_time of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The consistent_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._consistent_time

    @consistent_time.setter
    def consistent_time(self, consistent_time):
        """Sets the consistent_time of this BackupForDescribeBackupsOutput.


        :param consistent_time: The consistent_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._consistent_time = consistent_time

    @property
    def create_type(self):
        """Gets the create_type of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The create_type of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_type

    @create_type.setter
    def create_type(self, create_type):
        """Sets the create_type of this BackupForDescribeBackupsOutput.


        :param create_type: The create_type of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._create_type = create_type

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The db_engine_version of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this BackupForDescribeBackupsOutput.


        :param db_engine_version: The db_engine_version of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def db_table_infos(self):
        """Gets the db_table_infos of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The db_table_infos of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: list[DBTableInfoForDescribeBackupsOutput]
        """
        return self._db_table_infos

    @db_table_infos.setter
    def db_table_infos(self, db_table_infos):
        """Sets the db_table_infos of this BackupForDescribeBackupsOutput.


        :param db_table_infos: The db_table_infos of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: list[DBTableInfoForDescribeBackupsOutput]
        """

        self._db_table_infos = db_table_infos

    @property
    def download_status(self):
        """Gets the download_status of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The download_status of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._download_status

    @download_status.setter
    def download_status(self, download_status):
        """Sets the download_status of this BackupForDescribeBackupsOutput.


        :param download_status: The download_status of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._download_status = download_status

    @property
    def error_message(self):
        """Gets the error_message of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The error_message of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_message

    @error_message.setter
    def error_message(self, error_message):
        """Sets the error_message of this BackupForDescribeBackupsOutput.


        :param error_message: The error_message of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._error_message = error_message

    @property
    def expired_time(self):
        """Gets the expired_time of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The expired_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this BackupForDescribeBackupsOutput.


        :param expired_time: The expired_time of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def is_encrypted(self):
        """Gets the is_encrypted of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The is_encrypted of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_encrypted

    @is_encrypted.setter
    def is_encrypted(self, is_encrypted):
        """Sets the is_encrypted of this BackupForDescribeBackupsOutput.


        :param is_encrypted: The is_encrypted of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: bool
        """

        self._is_encrypted = is_encrypted

    @property
    def is_expired(self):
        """Gets the is_expired of this BackupForDescribeBackupsOutput.  # noqa: E501


        :return: The is_expired of this BackupForDescribeBackupsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_expired

    @is_expired.setter
    def is_expired(self, is_expired):
        """Sets the is_expired of this BackupForDescribeBackupsOutput.


        :param is_expired: The is_expired of this BackupForDescribeBackupsOutput.  # noqa: E501
        :type: bool
        """

        self._is_expired = is_expired

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackupForDescribeBackupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupForDescribeBackupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupForDescribeBackupsOutput):
            return True

        return self.to_dict() != other.to_dict()
