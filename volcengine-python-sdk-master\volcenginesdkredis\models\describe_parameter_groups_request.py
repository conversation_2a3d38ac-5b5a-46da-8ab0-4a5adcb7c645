# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeParameterGroupsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'engine_version': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'source': 'str'
    }

    attribute_map = {
        'engine_version': 'EngineVersion',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'source': 'Source'
    }

    def __init__(self, engine_version=None, page_number=None, page_size=None, source=None, _configuration=None):  # noqa: E501
        """DescribeParameterGroupsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._engine_version = None
        self._page_number = None
        self._page_size = None
        self._source = None
        self.discriminator = None

        if engine_version is not None:
            self.engine_version = engine_version
        self.page_number = page_number
        self.page_size = page_size
        if source is not None:
            self.source = source

    @property
    def engine_version(self):
        """Gets the engine_version of this DescribeParameterGroupsRequest.  # noqa: E501


        :return: The engine_version of this DescribeParameterGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._engine_version

    @engine_version.setter
    def engine_version(self, engine_version):
        """Sets the engine_version of this DescribeParameterGroupsRequest.


        :param engine_version: The engine_version of this DescribeParameterGroupsRequest.  # noqa: E501
        :type: str
        """

        self._engine_version = engine_version

    @property
    def page_number(self):
        """Gets the page_number of this DescribeParameterGroupsRequest.  # noqa: E501


        :return: The page_number of this DescribeParameterGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeParameterGroupsRequest.


        :param page_number: The page_number of this DescribeParameterGroupsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeParameterGroupsRequest.  # noqa: E501


        :return: The page_size of this DescribeParameterGroupsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeParameterGroupsRequest.


        :param page_size: The page_size of this DescribeParameterGroupsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def source(self):
        """Gets the source of this DescribeParameterGroupsRequest.  # noqa: E501


        :return: The source of this DescribeParameterGroupsRequest.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this DescribeParameterGroupsRequest.


        :param source: The source of this DescribeParameterGroupsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["System", "User"]  # noqa: E501
        if (self._configuration.client_side_validation and
                source not in allowed_values):
            raise ValueError(
                "Invalid value for `source` ({0}), must be one of {1}"  # noqa: E501
                .format(source, allowed_values)
            )

        self._source = source

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeParameterGroupsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeParameterGroupsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeParameterGroupsRequest):
            return True

        return self.to_dict() != other.to_dict()
