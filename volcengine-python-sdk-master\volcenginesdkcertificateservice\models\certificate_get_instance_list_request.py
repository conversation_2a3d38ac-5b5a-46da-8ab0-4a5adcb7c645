# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateGetInstanceListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_expire_after': 'str',
        'certificate_expire_before': 'str',
        'common_name': 'str',
        'domain': 'str',
        'instance_ids': 'list[str]',
        'instance_type': 'str',
        'is_revoked': 'bool',
        'is_valid': 'bool',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'status': 'list[str]',
        'tag': 'str',
        'tag_filters': 'list[TagFilterForCertificateGetInstanceListInput]'
    }

    attribute_map = {
        'certificate_expire_after': 'CertificateExpireAfter',
        'certificate_expire_before': 'CertificateExpireBefore',
        'common_name': 'CommonName',
        'domain': 'Domain',
        'instance_ids': 'InstanceIds',
        'instance_type': 'InstanceType',
        'is_revoked': 'IsRevoked',
        'is_valid': 'IsValid',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tag': 'Tag',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, certificate_expire_after=None, certificate_expire_before=None, common_name=None, domain=None, instance_ids=None, instance_type=None, is_revoked=None, is_valid=None, page_number=None, page_size=None, project_name=None, status=None, tag=None, tag_filters=None, _configuration=None):  # noqa: E501
        """CertificateGetInstanceListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_expire_after = None
        self._certificate_expire_before = None
        self._common_name = None
        self._domain = None
        self._instance_ids = None
        self._instance_type = None
        self._is_revoked = None
        self._is_valid = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._status = None
        self._tag = None
        self._tag_filters = None
        self.discriminator = None

        if certificate_expire_after is not None:
            self.certificate_expire_after = certificate_expire_after
        if certificate_expire_before is not None:
            self.certificate_expire_before = certificate_expire_before
        if common_name is not None:
            self.common_name = common_name
        if domain is not None:
            self.domain = domain
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if instance_type is not None:
            self.instance_type = instance_type
        if is_revoked is not None:
            self.is_revoked = is_revoked
        if is_valid is not None:
            self.is_valid = is_valid
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def certificate_expire_after(self):
        """Gets the certificate_expire_after of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The certificate_expire_after of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_expire_after

    @certificate_expire_after.setter
    def certificate_expire_after(self, certificate_expire_after):
        """Sets the certificate_expire_after of this CertificateGetInstanceListRequest.


        :param certificate_expire_after: The certificate_expire_after of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._certificate_expire_after = certificate_expire_after

    @property
    def certificate_expire_before(self):
        """Gets the certificate_expire_before of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The certificate_expire_before of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._certificate_expire_before

    @certificate_expire_before.setter
    def certificate_expire_before(self, certificate_expire_before):
        """Sets the certificate_expire_before of this CertificateGetInstanceListRequest.


        :param certificate_expire_before: The certificate_expire_before of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._certificate_expire_before = certificate_expire_before

    @property
    def common_name(self):
        """Gets the common_name of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The common_name of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this CertificateGetInstanceListRequest.


        :param common_name: The common_name of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def domain(self):
        """Gets the domain of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The domain of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this CertificateGetInstanceListRequest.


        :param domain: The domain of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def instance_ids(self):
        """Gets the instance_ids of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The instance_ids of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this CertificateGetInstanceListRequest.


        :param instance_ids: The instance_ids of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def instance_type(self):
        """Gets the instance_type of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The instance_type of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this CertificateGetInstanceListRequest.


        :param instance_type: The instance_type of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Free", "Test", "Paid", "Imported"]  # noqa: E501
        if (self._configuration.client_side_validation and
                instance_type not in allowed_values):
            raise ValueError(
                "Invalid value for `instance_type` ({0}), must be one of {1}"  # noqa: E501
                .format(instance_type, allowed_values)
            )

        self._instance_type = instance_type

    @property
    def is_revoked(self):
        """Gets the is_revoked of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The is_revoked of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_revoked

    @is_revoked.setter
    def is_revoked(self, is_revoked):
        """Sets the is_revoked of this CertificateGetInstanceListRequest.


        :param is_revoked: The is_revoked of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: bool
        """

        self._is_revoked = is_revoked

    @property
    def is_valid(self):
        """Gets the is_valid of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The is_valid of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_valid

    @is_valid.setter
    def is_valid(self, is_valid):
        """Sets the is_valid of this CertificateGetInstanceListRequest.


        :param is_valid: The is_valid of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: bool
        """

        self._is_valid = is_valid

    @property
    def page_number(self):
        """Gets the page_number of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The page_number of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this CertificateGetInstanceListRequest.


        :param page_number: The page_number of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The page_size of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this CertificateGetInstanceListRequest.


        :param page_size: The page_size of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The project_name of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CertificateGetInstanceListRequest.


        :param project_name: The project_name of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The status of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CertificateGetInstanceListRequest.


        :param status: The status of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["NotSubmitted", "Pending", "Issued", "Cancelling", "Canceled", "Revoking", "Revoked", "Failed", "Unknown"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(status).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `status` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(status) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The tag of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this CertificateGetInstanceListRequest.


        :param tag: The tag of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tag_filters(self):
        """Gets the tag_filters of this CertificateGetInstanceListRequest.  # noqa: E501


        :return: The tag_filters of this CertificateGetInstanceListRequest.  # noqa: E501
        :rtype: list[TagFilterForCertificateGetInstanceListInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this CertificateGetInstanceListRequest.


        :param tag_filters: The tag_filters of this CertificateGetInstanceListRequest.  # noqa: E501
        :type: list[TagFilterForCertificateGetInstanceListInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateGetInstanceListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateGetInstanceListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateGetInstanceListRequest):
            return True

        return self.to_dict() != other.to_dict()
