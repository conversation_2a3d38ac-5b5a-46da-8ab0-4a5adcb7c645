# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'diagnose_config': 'list[DiagnoseConfigForCreateJobInput]',
        'dry_run': 'bool',
        'name': 'str',
        'observable_config': 'ObservableConfigForCreateJobInput',
        'resource_config': 'ResourceConfigForCreateJobInput',
        'retry_config': 'RetryConfigForCreateJobInput',
        'runtime_config': 'RuntimeConfigForCreateJobInput',
        'storage_config': 'StorageConfigForCreateJobInput'
    }

    attribute_map = {
        'description': 'Description',
        'diagnose_config': 'DiagnoseConfig',
        'dry_run': 'DryRun',
        'name': 'Name',
        'observable_config': 'ObservableConfig',
        'resource_config': 'ResourceConfig',
        'retry_config': 'RetryConfig',
        'runtime_config': 'RuntimeConfig',
        'storage_config': 'StorageConfig'
    }

    def __init__(self, description=None, diagnose_config=None, dry_run=None, name=None, observable_config=None, resource_config=None, retry_config=None, runtime_config=None, storage_config=None, _configuration=None):  # noqa: E501
        """CreateJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._diagnose_config = None
        self._dry_run = None
        self._name = None
        self._observable_config = None
        self._resource_config = None
        self._retry_config = None
        self._runtime_config = None
        self._storage_config = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if diagnose_config is not None:
            self.diagnose_config = diagnose_config
        if dry_run is not None:
            self.dry_run = dry_run
        if name is not None:
            self.name = name
        if observable_config is not None:
            self.observable_config = observable_config
        if resource_config is not None:
            self.resource_config = resource_config
        if retry_config is not None:
            self.retry_config = retry_config
        if runtime_config is not None:
            self.runtime_config = runtime_config
        if storage_config is not None:
            self.storage_config = storage_config

    @property
    def description(self):
        """Gets the description of this CreateJobRequest.  # noqa: E501


        :return: The description of this CreateJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateJobRequest.


        :param description: The description of this CreateJobRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def diagnose_config(self):
        """Gets the diagnose_config of this CreateJobRequest.  # noqa: E501


        :return: The diagnose_config of this CreateJobRequest.  # noqa: E501
        :rtype: list[DiagnoseConfigForCreateJobInput]
        """
        return self._diagnose_config

    @diagnose_config.setter
    def diagnose_config(self, diagnose_config):
        """Sets the diagnose_config of this CreateJobRequest.


        :param diagnose_config: The diagnose_config of this CreateJobRequest.  # noqa: E501
        :type: list[DiagnoseConfigForCreateJobInput]
        """

        self._diagnose_config = diagnose_config

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateJobRequest.  # noqa: E501


        :return: The dry_run of this CreateJobRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateJobRequest.


        :param dry_run: The dry_run of this CreateJobRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def name(self):
        """Gets the name of this CreateJobRequest.  # noqa: E501


        :return: The name of this CreateJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateJobRequest.


        :param name: The name of this CreateJobRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def observable_config(self):
        """Gets the observable_config of this CreateJobRequest.  # noqa: E501


        :return: The observable_config of this CreateJobRequest.  # noqa: E501
        :rtype: ObservableConfigForCreateJobInput
        """
        return self._observable_config

    @observable_config.setter
    def observable_config(self, observable_config):
        """Sets the observable_config of this CreateJobRequest.


        :param observable_config: The observable_config of this CreateJobRequest.  # noqa: E501
        :type: ObservableConfigForCreateJobInput
        """

        self._observable_config = observable_config

    @property
    def resource_config(self):
        """Gets the resource_config of this CreateJobRequest.  # noqa: E501


        :return: The resource_config of this CreateJobRequest.  # noqa: E501
        :rtype: ResourceConfigForCreateJobInput
        """
        return self._resource_config

    @resource_config.setter
    def resource_config(self, resource_config):
        """Sets the resource_config of this CreateJobRequest.


        :param resource_config: The resource_config of this CreateJobRequest.  # noqa: E501
        :type: ResourceConfigForCreateJobInput
        """

        self._resource_config = resource_config

    @property
    def retry_config(self):
        """Gets the retry_config of this CreateJobRequest.  # noqa: E501


        :return: The retry_config of this CreateJobRequest.  # noqa: E501
        :rtype: RetryConfigForCreateJobInput
        """
        return self._retry_config

    @retry_config.setter
    def retry_config(self, retry_config):
        """Sets the retry_config of this CreateJobRequest.


        :param retry_config: The retry_config of this CreateJobRequest.  # noqa: E501
        :type: RetryConfigForCreateJobInput
        """

        self._retry_config = retry_config

    @property
    def runtime_config(self):
        """Gets the runtime_config of this CreateJobRequest.  # noqa: E501


        :return: The runtime_config of this CreateJobRequest.  # noqa: E501
        :rtype: RuntimeConfigForCreateJobInput
        """
        return self._runtime_config

    @runtime_config.setter
    def runtime_config(self, runtime_config):
        """Sets the runtime_config of this CreateJobRequest.


        :param runtime_config: The runtime_config of this CreateJobRequest.  # noqa: E501
        :type: RuntimeConfigForCreateJobInput
        """

        self._runtime_config = runtime_config

    @property
    def storage_config(self):
        """Gets the storage_config of this CreateJobRequest.  # noqa: E501


        :return: The storage_config of this CreateJobRequest.  # noqa: E501
        :rtype: StorageConfigForCreateJobInput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this CreateJobRequest.


        :param storage_config: The storage_config of this CreateJobRequest.  # noqa: E501
        :type: StorageConfigForCreateJobInput
        """

        self._storage_config = storage_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateJobRequest):
            return True

        return self.to_dict() != other.to_dict()
