# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRecordSetsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'record_set_id': 'str',
        'search_mode': 'str',
        'zid': 'int'
    }

    attribute_map = {
        'host': 'Host',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'record_set_id': 'RecordSetID',
        'search_mode': 'SearchMode',
        'zid': 'ZID'
    }

    def __init__(self, host=None, page_number=None, page_size=None, record_set_id=None, search_mode=None, zid=None, _configuration=None):  # noqa: E501
        """ListRecordSetsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host = None
        self._page_number = None
        self._page_size = None
        self._record_set_id = None
        self._search_mode = None
        self._zid = None
        self.discriminator = None

        if host is not None:
            self.host = host
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if record_set_id is not None:
            self.record_set_id = record_set_id
        if search_mode is not None:
            self.search_mode = search_mode
        self.zid = zid

    @property
    def host(self):
        """Gets the host of this ListRecordSetsRequest.  # noqa: E501


        :return: The host of this ListRecordSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this ListRecordSetsRequest.


        :param host: The host of this ListRecordSetsRequest.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def page_number(self):
        """Gets the page_number of this ListRecordSetsRequest.  # noqa: E501


        :return: The page_number of this ListRecordSetsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRecordSetsRequest.


        :param page_number: The page_number of this ListRecordSetsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRecordSetsRequest.  # noqa: E501


        :return: The page_size of this ListRecordSetsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRecordSetsRequest.


        :param page_size: The page_size of this ListRecordSetsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def record_set_id(self):
        """Gets the record_set_id of this ListRecordSetsRequest.  # noqa: E501


        :return: The record_set_id of this ListRecordSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._record_set_id

    @record_set_id.setter
    def record_set_id(self, record_set_id):
        """Sets the record_set_id of this ListRecordSetsRequest.


        :param record_set_id: The record_set_id of this ListRecordSetsRequest.  # noqa: E501
        :type: str
        """

        self._record_set_id = record_set_id

    @property
    def search_mode(self):
        """Gets the search_mode of this ListRecordSetsRequest.  # noqa: E501


        :return: The search_mode of this ListRecordSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_mode

    @search_mode.setter
    def search_mode(self, search_mode):
        """Sets the search_mode of this ListRecordSetsRequest.


        :param search_mode: The search_mode of this ListRecordSetsRequest.  # noqa: E501
        :type: str
        """

        self._search_mode = search_mode

    @property
    def zid(self):
        """Gets the zid of this ListRecordSetsRequest.  # noqa: E501


        :return: The zid of this ListRecordSetsRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this ListRecordSetsRequest.


        :param zid: The zid of this ListRecordSetsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and zid is None:
            raise ValueError("Invalid value for `zid`, must not be `None`")  # noqa: E501

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRecordSetsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRecordSetsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRecordSetsRequest):
            return True

        return self.to_dict() != other.to_dict()
