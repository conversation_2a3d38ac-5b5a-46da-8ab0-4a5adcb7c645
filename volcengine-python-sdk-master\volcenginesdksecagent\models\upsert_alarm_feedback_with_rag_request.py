# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpsertAlarmFeedbackWithRagRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_id': 'str',
        'analysis_record_uuid': 'str',
        'feedback_complement': 'str',
        'feedback_ioc_feature': 'str',
        'feedback_result': 'str',
        'feedback_tag': 'list[str]'
    }

    attribute_map = {
        'alarm_id': 'AlarmID',
        'analysis_record_uuid': 'AnalysisRecordUUID',
        'feedback_complement': 'FeedbackComplement',
        'feedback_ioc_feature': 'FeedbackIOCFeature',
        'feedback_result': 'FeedbackResult',
        'feedback_tag': 'FeedbackTag'
    }

    def __init__(self, alarm_id=None, analysis_record_uuid=None, feedback_complement=None, feedback_ioc_feature=None, feedback_result=None, feedback_tag=None, _configuration=None):  # noqa: E501
        """UpsertAlarmFeedbackWithRagRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_id = None
        self._analysis_record_uuid = None
        self._feedback_complement = None
        self._feedback_ioc_feature = None
        self._feedback_result = None
        self._feedback_tag = None
        self.discriminator = None

        self.alarm_id = alarm_id
        self.analysis_record_uuid = analysis_record_uuid
        self.feedback_complement = feedback_complement
        self.feedback_ioc_feature = feedback_ioc_feature
        self.feedback_result = feedback_result
        if feedback_tag is not None:
            self.feedback_tag = feedback_tag

    @property
    def alarm_id(self):
        """Gets the alarm_id of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The alarm_id of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this UpsertAlarmFeedbackWithRagRequest.


        :param alarm_id: The alarm_id of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and alarm_id is None:
            raise ValueError("Invalid value for `alarm_id`, must not be `None`")  # noqa: E501

        self._alarm_id = alarm_id

    @property
    def analysis_record_uuid(self):
        """Gets the analysis_record_uuid of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The analysis_record_uuid of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: str
        """
        return self._analysis_record_uuid

    @analysis_record_uuid.setter
    def analysis_record_uuid(self, analysis_record_uuid):
        """Sets the analysis_record_uuid of this UpsertAlarmFeedbackWithRagRequest.


        :param analysis_record_uuid: The analysis_record_uuid of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and analysis_record_uuid is None:
            raise ValueError("Invalid value for `analysis_record_uuid`, must not be `None`")  # noqa: E501

        self._analysis_record_uuid = analysis_record_uuid

    @property
    def feedback_complement(self):
        """Gets the feedback_complement of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The feedback_complement of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: str
        """
        return self._feedback_complement

    @feedback_complement.setter
    def feedback_complement(self, feedback_complement):
        """Sets the feedback_complement of this UpsertAlarmFeedbackWithRagRequest.


        :param feedback_complement: The feedback_complement of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and feedback_complement is None:
            raise ValueError("Invalid value for `feedback_complement`, must not be `None`")  # noqa: E501

        self._feedback_complement = feedback_complement

    @property
    def feedback_ioc_feature(self):
        """Gets the feedback_ioc_feature of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The feedback_ioc_feature of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: str
        """
        return self._feedback_ioc_feature

    @feedback_ioc_feature.setter
    def feedback_ioc_feature(self, feedback_ioc_feature):
        """Sets the feedback_ioc_feature of this UpsertAlarmFeedbackWithRagRequest.


        :param feedback_ioc_feature: The feedback_ioc_feature of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and feedback_ioc_feature is None:
            raise ValueError("Invalid value for `feedback_ioc_feature`, must not be `None`")  # noqa: E501

        self._feedback_ioc_feature = feedback_ioc_feature

    @property
    def feedback_result(self):
        """Gets the feedback_result of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The feedback_result of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: str
        """
        return self._feedback_result

    @feedback_result.setter
    def feedback_result(self, feedback_result):
        """Sets the feedback_result of this UpsertAlarmFeedbackWithRagRequest.


        :param feedback_result: The feedback_result of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and feedback_result is None:
            raise ValueError("Invalid value for `feedback_result`, must not be `None`")  # noqa: E501

        self._feedback_result = feedback_result

    @property
    def feedback_tag(self):
        """Gets the feedback_tag of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501


        :return: The feedback_tag of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._feedback_tag

    @feedback_tag.setter
    def feedback_tag(self, feedback_tag):
        """Sets the feedback_tag of this UpsertAlarmFeedbackWithRagRequest.


        :param feedback_tag: The feedback_tag of this UpsertAlarmFeedbackWithRagRequest.  # noqa: E501
        :type: list[str]
        """

        self._feedback_tag = feedback_tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpsertAlarmFeedbackWithRagRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpsertAlarmFeedbackWithRagRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpsertAlarmFeedbackWithRagRequest):
            return True

        return self.to_dict() != other.to_dict()
