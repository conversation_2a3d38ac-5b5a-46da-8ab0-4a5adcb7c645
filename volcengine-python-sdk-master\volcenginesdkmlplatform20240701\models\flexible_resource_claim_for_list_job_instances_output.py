# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FlexibleResourceClaimForListJobInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu': 'float',
        'family': 'str',
        'gpu_count': 'float',
        'gpu_type': 'str',
        'memory_gi_b': 'float',
        'rdma_eni_count': 'int'
    }

    attribute_map = {
        'cpu': 'Cpu',
        'family': 'Family',
        'gpu_count': 'GpuCount',
        'gpu_type': 'GpuType',
        'memory_gi_b': 'MemoryGiB',
        'rdma_eni_count': 'RdmaEniCount'
    }

    def __init__(self, cpu=None, family=None, gpu_count=None, gpu_type=None, memory_gi_b=None, rdma_eni_count=None, _configuration=None):  # noqa: E501
        """FlexibleResourceClaimForListJobInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu = None
        self._family = None
        self._gpu_count = None
        self._gpu_type = None
        self._memory_gi_b = None
        self._rdma_eni_count = None
        self.discriminator = None

        if cpu is not None:
            self.cpu = cpu
        if family is not None:
            self.family = family
        if gpu_count is not None:
            self.gpu_count = gpu_count
        if gpu_type is not None:
            self.gpu_type = gpu_type
        if memory_gi_b is not None:
            self.memory_gi_b = memory_gi_b
        if rdma_eni_count is not None:
            self.rdma_eni_count = rdma_eni_count

    @property
    def cpu(self):
        """Gets the cpu of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The cpu of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this FlexibleResourceClaimForListJobInstancesOutput.


        :param cpu: The cpu of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: float
        """

        self._cpu = cpu

    @property
    def family(self):
        """Gets the family of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The family of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._family

    @family.setter
    def family(self, family):
        """Sets the family of this FlexibleResourceClaimForListJobInstancesOutput.


        :param family: The family of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._family = family

    @property
    def gpu_count(self):
        """Gets the gpu_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The gpu_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: float
        """
        return self._gpu_count

    @gpu_count.setter
    def gpu_count(self, gpu_count):
        """Sets the gpu_count of this FlexibleResourceClaimForListJobInstancesOutput.


        :param gpu_count: The gpu_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: float
        """

        self._gpu_count = gpu_count

    @property
    def gpu_type(self):
        """Gets the gpu_type of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The gpu_type of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._gpu_type

    @gpu_type.setter
    def gpu_type(self, gpu_type):
        """Sets the gpu_type of this FlexibleResourceClaimForListJobInstancesOutput.


        :param gpu_type: The gpu_type of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._gpu_type = gpu_type

    @property
    def memory_gi_b(self):
        """Gets the memory_gi_b of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The memory_gi_b of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: float
        """
        return self._memory_gi_b

    @memory_gi_b.setter
    def memory_gi_b(self, memory_gi_b):
        """Sets the memory_gi_b of this FlexibleResourceClaimForListJobInstancesOutput.


        :param memory_gi_b: The memory_gi_b of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: float
        """

        self._memory_gi_b = memory_gi_b

    @property
    def rdma_eni_count(self):
        """Gets the rdma_eni_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501


        :return: The rdma_eni_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._rdma_eni_count

    @rdma_eni_count.setter
    def rdma_eni_count(self, rdma_eni_count):
        """Sets the rdma_eni_count of this FlexibleResourceClaimForListJobInstancesOutput.


        :param rdma_eni_count: The rdma_eni_count of this FlexibleResourceClaimForListJobInstancesOutput.  # noqa: E501
        :type: int
        """

        self._rdma_eni_count = rdma_eni_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FlexibleResourceClaimForListJobInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FlexibleResourceClaimForListJobInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FlexibleResourceClaimForListJobInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
