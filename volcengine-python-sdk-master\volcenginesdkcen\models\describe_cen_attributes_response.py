# coding: utf-8

"""
    cen

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCenAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'cen_bandwidth_package_ids': 'list[str]',
        'cen_id': 'str',
        'cen_name': 'str',
        'creation_time': 'str',
        'description': 'str',
        'project_name': 'str',
        'request_id': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeCenAttributesOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'cen_bandwidth_package_ids': 'CenBandwidthPackageIds',
        'cen_id': 'CenId',
        'cen_name': 'CenName',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'project_name': 'ProjectName',
        'request_id': 'RequestId',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, cen_bandwidth_package_ids=None, cen_id=None, cen_name=None, creation_time=None, description=None, project_name=None, request_id=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """DescribeCenAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._cen_bandwidth_package_ids = None
        self._cen_id = None
        self._cen_name = None
        self._creation_time = None
        self._description = None
        self._project_name = None
        self._request_id = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if cen_bandwidth_package_ids is not None:
            self.cen_bandwidth_package_ids = cen_bandwidth_package_ids
        if cen_id is not None:
            self.cen_id = cen_id
        if cen_name is not None:
            self.cen_name = cen_name
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if project_name is not None:
            self.project_name = project_name
        if request_id is not None:
            self.request_id = request_id
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeCenAttributesResponse.


        :param account_id: The account_id of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def cen_bandwidth_package_ids(self):
        """Gets the cen_bandwidth_package_ids of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The cen_bandwidth_package_ids of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cen_bandwidth_package_ids

    @cen_bandwidth_package_ids.setter
    def cen_bandwidth_package_ids(self, cen_bandwidth_package_ids):
        """Sets the cen_bandwidth_package_ids of this DescribeCenAttributesResponse.


        :param cen_bandwidth_package_ids: The cen_bandwidth_package_ids of this DescribeCenAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._cen_bandwidth_package_ids = cen_bandwidth_package_ids

    @property
    def cen_id(self):
        """Gets the cen_id of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The cen_id of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._cen_id

    @cen_id.setter
    def cen_id(self, cen_id):
        """Sets the cen_id of this DescribeCenAttributesResponse.


        :param cen_id: The cen_id of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._cen_id = cen_id

    @property
    def cen_name(self):
        """Gets the cen_name of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The cen_name of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._cen_name

    @cen_name.setter
    def cen_name(self, cen_name):
        """Sets the cen_name of this DescribeCenAttributesResponse.


        :param cen_name: The cen_name of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._cen_name = cen_name

    @property
    def creation_time(self):
        """Gets the creation_time of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The creation_time of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DescribeCenAttributesResponse.


        :param creation_time: The creation_time of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The description of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeCenAttributesResponse.


        :param description: The description of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def project_name(self):
        """Gets the project_name of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeCenAttributesResponse.


        :param project_name: The project_name of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def request_id(self):
        """Gets the request_id of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeCenAttributesResponse.


        :param request_id: The request_id of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def status(self):
        """Gets the status of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The status of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeCenAttributesResponse.


        :param status: The status of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeCenAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeCenAttributesResponse.


        :param tags: The tags of this DescribeCenAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeCenAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeCenAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeCenAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeCenAttributesResponse.


        :param update_time: The update_time of this DescribeCenAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCenAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCenAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCenAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
