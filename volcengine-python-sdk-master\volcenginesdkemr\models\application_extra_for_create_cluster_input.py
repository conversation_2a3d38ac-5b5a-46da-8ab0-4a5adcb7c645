# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplicationExtraForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_component_layouts': 'list[ApplicationComponentLayoutForCreateClusterInput]',
        'application_configs': 'list[ApplicationConfigForCreateClusterInput]',
        'application_name': 'str',
        'connection_id': 'str',
        'connection_type': 'str'
    }

    attribute_map = {
        'application_component_layouts': 'ApplicationComponentLayouts',
        'application_configs': 'ApplicationConfigs',
        'application_name': 'ApplicationName',
        'connection_id': 'ConnectionId',
        'connection_type': 'ConnectionType'
    }

    def __init__(self, application_component_layouts=None, application_configs=None, application_name=None, connection_id=None, connection_type=None, _configuration=None):  # noqa: E501
        """ApplicationExtraForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_component_layouts = None
        self._application_configs = None
        self._application_name = None
        self._connection_id = None
        self._connection_type = None
        self.discriminator = None

        if application_component_layouts is not None:
            self.application_component_layouts = application_component_layouts
        if application_configs is not None:
            self.application_configs = application_configs
        if application_name is not None:
            self.application_name = application_name
        if connection_id is not None:
            self.connection_id = connection_id
        if connection_type is not None:
            self.connection_type = connection_type

    @property
    def application_component_layouts(self):
        """Gets the application_component_layouts of this ApplicationExtraForCreateClusterInput.  # noqa: E501


        :return: The application_component_layouts of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :rtype: list[ApplicationComponentLayoutForCreateClusterInput]
        """
        return self._application_component_layouts

    @application_component_layouts.setter
    def application_component_layouts(self, application_component_layouts):
        """Sets the application_component_layouts of this ApplicationExtraForCreateClusterInput.


        :param application_component_layouts: The application_component_layouts of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :type: list[ApplicationComponentLayoutForCreateClusterInput]
        """

        self._application_component_layouts = application_component_layouts

    @property
    def application_configs(self):
        """Gets the application_configs of this ApplicationExtraForCreateClusterInput.  # noqa: E501


        :return: The application_configs of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :rtype: list[ApplicationConfigForCreateClusterInput]
        """
        return self._application_configs

    @application_configs.setter
    def application_configs(self, application_configs):
        """Sets the application_configs of this ApplicationExtraForCreateClusterInput.


        :param application_configs: The application_configs of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :type: list[ApplicationConfigForCreateClusterInput]
        """

        self._application_configs = application_configs

    @property
    def application_name(self):
        """Gets the application_name of this ApplicationExtraForCreateClusterInput.  # noqa: E501


        :return: The application_name of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._application_name

    @application_name.setter
    def application_name(self, application_name):
        """Sets the application_name of this ApplicationExtraForCreateClusterInput.


        :param application_name: The application_name of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._application_name = application_name

    @property
    def connection_id(self):
        """Gets the connection_id of this ApplicationExtraForCreateClusterInput.  # noqa: E501


        :return: The connection_id of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._connection_id

    @connection_id.setter
    def connection_id(self, connection_id):
        """Sets the connection_id of this ApplicationExtraForCreateClusterInput.


        :param connection_id: The connection_id of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._connection_id = connection_id

    @property
    def connection_type(self):
        """Gets the connection_type of this ApplicationExtraForCreateClusterInput.  # noqa: E501


        :return: The connection_type of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._connection_type

    @connection_type.setter
    def connection_type(self, connection_type):
        """Sets the connection_type of this ApplicationExtraForCreateClusterInput.


        :param connection_type: The connection_type of this ApplicationExtraForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._connection_type = connection_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplicationExtraForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplicationExtraForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplicationExtraForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
