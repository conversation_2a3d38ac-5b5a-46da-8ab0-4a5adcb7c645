# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WeightInfoItemForDescribeDnsScheduleOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'actual_value': 'int',
        'domain_id': 'str',
        'value': 'int'
    }

    attribute_map = {
        'actual_value': 'ActualValue',
        'domain_id': 'DomainId',
        'value': 'Value'
    }

    def __init__(self, actual_value=None, domain_id=None, value=None, _configuration=None):  # noqa: E501
        """WeightInfoItemForDescribeDnsScheduleOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._actual_value = None
        self._domain_id = None
        self._value = None
        self.discriminator = None

        if actual_value is not None:
            self.actual_value = actual_value
        if domain_id is not None:
            self.domain_id = domain_id
        if value is not None:
            self.value = value

    @property
    def actual_value(self):
        """Gets the actual_value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501


        :return: The actual_value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :rtype: int
        """
        return self._actual_value

    @actual_value.setter
    def actual_value(self, actual_value):
        """Sets the actual_value of this WeightInfoItemForDescribeDnsScheduleOutput.


        :param actual_value: The actual_value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :type: int
        """

        self._actual_value = actual_value

    @property
    def domain_id(self):
        """Gets the domain_id of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501


        :return: The domain_id of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain_id

    @domain_id.setter
    def domain_id(self, domain_id):
        """Sets the domain_id of this WeightInfoItemForDescribeDnsScheduleOutput.


        :param domain_id: The domain_id of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :type: str
        """

        self._domain_id = domain_id

    @property
    def value(self):
        """Gets the value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501


        :return: The value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :rtype: int
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this WeightInfoItemForDescribeDnsScheduleOutput.


        :param value: The value of this WeightInfoItemForDescribeDnsScheduleOutput.  # noqa: E501
        :type: int
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WeightInfoItemForDescribeDnsScheduleOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WeightInfoItemForDescribeDnsScheduleOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WeightInfoItemForDescribeDnsScheduleOutput):
            return True

        return self.to_dict() != other.to_dict()
