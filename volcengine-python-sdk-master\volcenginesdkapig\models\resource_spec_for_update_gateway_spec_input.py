# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceSpecForUpdateGatewaySpecInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_spec_code': 'str',
        'replicas': 'int'
    }

    attribute_map = {
        'instance_spec_code': 'InstanceSpecCode',
        'replicas': 'Replicas'
    }

    def __init__(self, instance_spec_code=None, replicas=None, _configuration=None):  # noqa: E501
        """ResourceSpecForUpdateGatewaySpecInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_spec_code = None
        self._replicas = None
        self.discriminator = None

        if instance_spec_code is not None:
            self.instance_spec_code = instance_spec_code
        if replicas is not None:
            self.replicas = replicas

    @property
    def instance_spec_code(self):
        """Gets the instance_spec_code of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501


        :return: The instance_spec_code of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501
        :rtype: str
        """
        return self._instance_spec_code

    @instance_spec_code.setter
    def instance_spec_code(self, instance_spec_code):
        """Sets the instance_spec_code of this ResourceSpecForUpdateGatewaySpecInput.


        :param instance_spec_code: The instance_spec_code of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501
        :type: str
        """

        self._instance_spec_code = instance_spec_code

    @property
    def replicas(self):
        """Gets the replicas of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501


        :return: The replicas of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this ResourceSpecForUpdateGatewaySpecInput.


        :param replicas: The replicas of this ResourceSpecForUpdateGatewaySpecInput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceSpecForUpdateGatewaySpecInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceSpecForUpdateGatewaySpecInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceSpecForUpdateGatewaySpecInput):
            return True

        return self.to_dict() != other.to_dict()
