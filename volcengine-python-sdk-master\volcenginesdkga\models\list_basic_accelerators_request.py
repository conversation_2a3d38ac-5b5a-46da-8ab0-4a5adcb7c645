# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBasicAcceleratorsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'charge_type': 'str',
        'end_point_group_region': 'str',
        'ip_set_region': 'str',
        'name': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'resource_tag_filter': 'ResourceTagFilterForListBasicAcceleratorsInput',
        'state': 'str',
        'tags': 'list[TagForListBasicAcceleratorsInput]',
        'with_bandwidth_package': 'bool'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'charge_type': 'ChargeType',
        'end_point_group_region': 'EndPointGroupRegion',
        'ip_set_region': 'IPSetRegion',
        'name': 'Name',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'resource_tag_filter': 'ResourceTagFilter',
        'state': 'State',
        'tags': 'Tags',
        'with_bandwidth_package': 'WithBandwidthPackage'
    }

    def __init__(self, accelerator_id=None, charge_type=None, end_point_group_region=None, ip_set_region=None, name=None, page_num=None, page_size=None, project_name=None, resource_tag_filter=None, state=None, tags=None, with_bandwidth_package=None, _configuration=None):  # noqa: E501
        """ListBasicAcceleratorsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._charge_type = None
        self._end_point_group_region = None
        self._ip_set_region = None
        self._name = None
        self._page_num = None
        self._page_size = None
        self._project_name = None
        self._resource_tag_filter = None
        self._state = None
        self._tags = None
        self._with_bandwidth_package = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        self.charge_type = charge_type
        self.end_point_group_region = end_point_group_region
        self.ip_set_region = ip_set_region
        if name is not None:
            self.name = name
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if resource_tag_filter is not None:
            self.resource_tag_filter = resource_tag_filter
        if state is not None:
            self.state = state
        if tags is not None:
            self.tags = tags
        if with_bandwidth_package is not None:
            self.with_bandwidth_package = with_bandwidth_package

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The accelerator_id of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this ListBasicAcceleratorsRequest.


        :param accelerator_id: The accelerator_id of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def charge_type(self):
        """Gets the charge_type of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The charge_type of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ListBasicAcceleratorsRequest.


        :param charge_type: The charge_type of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def end_point_group_region(self):
        """Gets the end_point_group_region of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The end_point_group_region of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_point_group_region

    @end_point_group_region.setter
    def end_point_group_region(self, end_point_group_region):
        """Sets the end_point_group_region of this ListBasicAcceleratorsRequest.


        :param end_point_group_region: The end_point_group_region of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_point_group_region is None:
            raise ValueError("Invalid value for `end_point_group_region`, must not be `None`")  # noqa: E501

        self._end_point_group_region = end_point_group_region

    @property
    def ip_set_region(self):
        """Gets the ip_set_region of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The ip_set_region of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_region

    @ip_set_region.setter
    def ip_set_region(self, ip_set_region):
        """Sets the ip_set_region of this ListBasicAcceleratorsRequest.


        :param ip_set_region: The ip_set_region of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_set_region is None:
            raise ValueError("Invalid value for `ip_set_region`, must not be `None`")  # noqa: E501

        self._ip_set_region = ip_set_region

    @property
    def name(self):
        """Gets the name of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The name of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListBasicAcceleratorsRequest.


        :param name: The name of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_num(self):
        """Gets the page_num of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The page_num of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListBasicAcceleratorsRequest.


        :param page_num: The page_num of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The page_size of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListBasicAcceleratorsRequest.


        :param page_size: The page_size of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The project_name of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListBasicAcceleratorsRequest.


        :param project_name: The project_name of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_tag_filter(self):
        """Gets the resource_tag_filter of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The resource_tag_filter of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: ResourceTagFilterForListBasicAcceleratorsInput
        """
        return self._resource_tag_filter

    @resource_tag_filter.setter
    def resource_tag_filter(self, resource_tag_filter):
        """Sets the resource_tag_filter of this ListBasicAcceleratorsRequest.


        :param resource_tag_filter: The resource_tag_filter of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: ResourceTagFilterForListBasicAcceleratorsInput
        """

        self._resource_tag_filter = resource_tag_filter

    @property
    def state(self):
        """Gets the state of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The state of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListBasicAcceleratorsRequest.


        :param state: The state of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def tags(self):
        """Gets the tags of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The tags of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: list[TagForListBasicAcceleratorsInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListBasicAcceleratorsRequest.


        :param tags: The tags of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: list[TagForListBasicAcceleratorsInput]
        """

        self._tags = tags

    @property
    def with_bandwidth_package(self):
        """Gets the with_bandwidth_package of this ListBasicAcceleratorsRequest.  # noqa: E501


        :return: The with_bandwidth_package of this ListBasicAcceleratorsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._with_bandwidth_package

    @with_bandwidth_package.setter
    def with_bandwidth_package(self, with_bandwidth_package):
        """Sets the with_bandwidth_package of this ListBasicAcceleratorsRequest.


        :param with_bandwidth_package: The with_bandwidth_package of this ListBasicAcceleratorsRequest.  # noqa: E501
        :type: bool
        """

        self._with_bandwidth_package = with_bandwidth_package

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBasicAcceleratorsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBasicAcceleratorsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBasicAcceleratorsRequest):
            return True

        return self.to_dict() != other.to_dict()
