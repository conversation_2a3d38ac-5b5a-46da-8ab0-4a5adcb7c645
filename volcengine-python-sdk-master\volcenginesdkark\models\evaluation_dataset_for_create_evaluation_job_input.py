# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EvaluationDatasetForCreateEvaluationJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'admin_evaluation_dataset_id': 'str',
        'dataset_type': 'str',
        'evaluation_ability': 'str',
        'method': 'str',
        'scoring_weight': 'int',
        'tos_location': 'TosLocationForCreateEvaluationJobInput'
    }

    attribute_map = {
        'admin_evaluation_dataset_id': 'AdminEvaluationDatasetId',
        'dataset_type': 'DatasetType',
        'evaluation_ability': 'EvaluationAbility',
        'method': 'Method',
        'scoring_weight': 'ScoringWeight',
        'tos_location': 'TosLocation'
    }

    def __init__(self, admin_evaluation_dataset_id=None, dataset_type=None, evaluation_ability=None, method=None, scoring_weight=None, tos_location=None, _configuration=None):  # noqa: E501
        """EvaluationDatasetForCreateEvaluationJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._admin_evaluation_dataset_id = None
        self._dataset_type = None
        self._evaluation_ability = None
        self._method = None
        self._scoring_weight = None
        self._tos_location = None
        self.discriminator = None

        if admin_evaluation_dataset_id is not None:
            self.admin_evaluation_dataset_id = admin_evaluation_dataset_id
        if dataset_type is not None:
            self.dataset_type = dataset_type
        if evaluation_ability is not None:
            self.evaluation_ability = evaluation_ability
        if method is not None:
            self.method = method
        if scoring_weight is not None:
            self.scoring_weight = scoring_weight
        if tos_location is not None:
            self.tos_location = tos_location

    @property
    def admin_evaluation_dataset_id(self):
        """Gets the admin_evaluation_dataset_id of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The admin_evaluation_dataset_id of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._admin_evaluation_dataset_id

    @admin_evaluation_dataset_id.setter
    def admin_evaluation_dataset_id(self, admin_evaluation_dataset_id):
        """Sets the admin_evaluation_dataset_id of this EvaluationDatasetForCreateEvaluationJobInput.


        :param admin_evaluation_dataset_id: The admin_evaluation_dataset_id of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: str
        """

        self._admin_evaluation_dataset_id = admin_evaluation_dataset_id

    @property
    def dataset_type(self):
        """Gets the dataset_type of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The dataset_type of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._dataset_type

    @dataset_type.setter
    def dataset_type(self, dataset_type):
        """Sets the dataset_type of this EvaluationDatasetForCreateEvaluationJobInput.


        :param dataset_type: The dataset_type of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["AdminDataset", "UserDataset"]  # noqa: E501
        if (self._configuration.client_side_validation and
                dataset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `dataset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(dataset_type, allowed_values)
            )

        self._dataset_type = dataset_type

    @property
    def evaluation_ability(self):
        """Gets the evaluation_ability of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The evaluation_ability of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._evaluation_ability

    @evaluation_ability.setter
    def evaluation_ability(self, evaluation_ability):
        """Sets the evaluation_ability of this EvaluationDatasetForCreateEvaluationJobInput.


        :param evaluation_ability: The evaluation_ability of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: str
        """

        self._evaluation_ability = evaluation_ability

    @property
    def method(self):
        """Gets the method of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The method of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._method

    @method.setter
    def method(self, method):
        """Sets the method of this EvaluationDatasetForCreateEvaluationJobInput.


        :param method: The method of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Include", "BuiltIn", "Match", "InferenceOnly"]  # noqa: E501
        if (self._configuration.client_side_validation and
                method not in allowed_values):
            raise ValueError(
                "Invalid value for `method` ({0}), must be one of {1}"  # noqa: E501
                .format(method, allowed_values)
            )

        self._method = method

    @property
    def scoring_weight(self):
        """Gets the scoring_weight of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The scoring_weight of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: int
        """
        return self._scoring_weight

    @scoring_weight.setter
    def scoring_weight(self, scoring_weight):
        """Sets the scoring_weight of this EvaluationDatasetForCreateEvaluationJobInput.


        :param scoring_weight: The scoring_weight of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: int
        """

        self._scoring_weight = scoring_weight

    @property
    def tos_location(self):
        """Gets the tos_location of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501


        :return: The tos_location of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :rtype: TosLocationForCreateEvaluationJobInput
        """
        return self._tos_location

    @tos_location.setter
    def tos_location(self, tos_location):
        """Sets the tos_location of this EvaluationDatasetForCreateEvaluationJobInput.


        :param tos_location: The tos_location of this EvaluationDatasetForCreateEvaluationJobInput.  # noqa: E501
        :type: TosLocationForCreateEvaluationJobInput
        """

        self._tos_location = tos_location

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EvaluationDatasetForCreateEvaluationJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EvaluationDatasetForCreateEvaluationJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EvaluationDatasetForCreateEvaluationJobInput):
            return True

        return self.to_dict() != other.to_dict()
