# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BatchUpdateCdnConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregate': 'bool',
        'area_access_rule': 'AreaAccessRuleForBatchUpdateCdnConfigInput',
        'bandwidth_limit': 'BandwidthLimitForBatchUpdateCdnConfigInput',
        'browser_cache': 'list[BrowserCacheForBatchUpdateCdnConfigInput]',
        'cache': 'list[CacheForBatchUpdateCdnConfigInput]',
        'cache_host': 'CacheHostForBatchUpdateCdnConfigInput',
        'cache_key': 'list[CacheKeyForBatchUpdateCdnConfigInput]',
        'compression': 'CompressionForBatchUpdateCdnConfigInput',
        'conditional_origin': 'ConditionalOriginForBatchUpdateCdnConfigInput',
        'custom_error_page': 'CustomErrorPageForBatchUpdateCdnConfigInput',
        'customize_access_rule': 'CustomizeAccessRuleForBatchUpdateCdnConfigInput',
        'domains': 'list[str]',
        'download_speed_limit': 'DownloadSpeedLimitForBatchUpdateCdnConfigInput',
        'follow_redirect': 'bool',
        'https': 'HTTPSForBatchUpdateCdnConfigInput',
        'http_forced_redirect': 'HttpForcedRedirectForBatchUpdateCdnConfigInput',
        'ipv6': 'IPv6ForBatchUpdateCdnConfigInput',
        'ip_access_rule': 'IpAccessRuleForBatchUpdateCdnConfigInput',
        'ip_freq_limit': 'IpFreqLimitForBatchUpdateCdnConfigInput',
        'method_denied_rule': 'MethodDeniedRuleForBatchUpdateCdnConfigInput',
        'multi_range': 'MultiRangeForBatchUpdateCdnConfigInput',
        'negative_cache': 'list[NegativeCacheForBatchUpdateCdnConfigInput]',
        'offline_cache': 'OfflineCacheForBatchUpdateCdnConfigInput',
        'origin': 'list[OriginForBatchUpdateCdnConfigInput]',
        'origin_access_rule': 'OriginAccessRuleForBatchUpdateCdnConfigInput',
        'origin_arg': 'list[OriginArgForBatchUpdateCdnConfigInput]',
        'origin_cert_check': 'OriginCertCheckForBatchUpdateCdnConfigInput',
        'origin_host': 'str',
        'origin_ipv6': 'str',
        'origin_protocol': 'str',
        'origin_range': 'bool',
        'origin_retry': 'OriginRetryForBatchUpdateCdnConfigInput',
        'origin_rewrite': 'OriginRewriteForBatchUpdateCdnConfigInput',
        'origin_sni': 'OriginSniForBatchUpdateCdnConfigInput',
        'page_optimization': 'PageOptimizationForBatchUpdateCdnConfigInput',
        'quic': 'QuicForBatchUpdateCdnConfigInput',
        'redirection_rewrite': 'RedirectionRewriteForBatchUpdateCdnConfigInput',
        'referer_access_rule': 'RefererAccessRuleForBatchUpdateCdnConfigInput',
        'remote_auth': 'RemoteAuthForBatchUpdateCdnConfigInput',
        'request_block_rule': 'RequestBlockRuleForBatchUpdateCdnConfigInput',
        'request_header': 'list[RequestHeaderForBatchUpdateCdnConfigInput]',
        'response_header': 'list[ResponseHeaderForBatchUpdateCdnConfigInput]',
        'rewrite_hls': 'RewriteHLSForBatchUpdateCdnConfigInput',
        'service_region': 'str',
        'signed_url_auth': 'SignedUrlAuthForBatchUpdateCdnConfigInput',
        'timeout': 'TimeoutForBatchUpdateCdnConfigInput',
        'ua_access_rule': 'UaAccessRuleForBatchUpdateCdnConfigInput',
        'url_normalize': 'UrlNormalizeForBatchUpdateCdnConfigInput',
        'video_drag': 'VideoDragForBatchUpdateCdnConfigInput'
    }

    attribute_map = {
        'aggregate': 'Aggregate',
        'area_access_rule': 'AreaAccessRule',
        'bandwidth_limit': 'BandwidthLimit',
        'browser_cache': 'BrowserCache',
        'cache': 'Cache',
        'cache_host': 'CacheHost',
        'cache_key': 'CacheKey',
        'compression': 'Compression',
        'conditional_origin': 'ConditionalOrigin',
        'custom_error_page': 'CustomErrorPage',
        'customize_access_rule': 'CustomizeAccessRule',
        'domains': 'Domains',
        'download_speed_limit': 'DownloadSpeedLimit',
        'follow_redirect': 'FollowRedirect',
        'https': 'HTTPS',
        'http_forced_redirect': 'HttpForcedRedirect',
        'ipv6': 'IPv6',
        'ip_access_rule': 'IpAccessRule',
        'ip_freq_limit': 'IpFreqLimit',
        'method_denied_rule': 'MethodDeniedRule',
        'multi_range': 'MultiRange',
        'negative_cache': 'NegativeCache',
        'offline_cache': 'OfflineCache',
        'origin': 'Origin',
        'origin_access_rule': 'OriginAccessRule',
        'origin_arg': 'OriginArg',
        'origin_cert_check': 'OriginCertCheck',
        'origin_host': 'OriginHost',
        'origin_ipv6': 'OriginIPv6',
        'origin_protocol': 'OriginProtocol',
        'origin_range': 'OriginRange',
        'origin_retry': 'OriginRetry',
        'origin_rewrite': 'OriginRewrite',
        'origin_sni': 'OriginSni',
        'page_optimization': 'PageOptimization',
        'quic': 'Quic',
        'redirection_rewrite': 'RedirectionRewrite',
        'referer_access_rule': 'RefererAccessRule',
        'remote_auth': 'RemoteAuth',
        'request_block_rule': 'RequestBlockRule',
        'request_header': 'RequestHeader',
        'response_header': 'ResponseHeader',
        'rewrite_hls': 'RewriteHLS',
        'service_region': 'ServiceRegion',
        'signed_url_auth': 'SignedUrlAuth',
        'timeout': 'Timeout',
        'ua_access_rule': 'UaAccessRule',
        'url_normalize': 'UrlNormalize',
        'video_drag': 'VideoDrag'
    }

    def __init__(self, aggregate=None, area_access_rule=None, bandwidth_limit=None, browser_cache=None, cache=None, cache_host=None, cache_key=None, compression=None, conditional_origin=None, custom_error_page=None, customize_access_rule=None, domains=None, download_speed_limit=None, follow_redirect=None, https=None, http_forced_redirect=None, ipv6=None, ip_access_rule=None, ip_freq_limit=None, method_denied_rule=None, multi_range=None, negative_cache=None, offline_cache=None, origin=None, origin_access_rule=None, origin_arg=None, origin_cert_check=None, origin_host=None, origin_ipv6=None, origin_protocol=None, origin_range=None, origin_retry=None, origin_rewrite=None, origin_sni=None, page_optimization=None, quic=None, redirection_rewrite=None, referer_access_rule=None, remote_auth=None, request_block_rule=None, request_header=None, response_header=None, rewrite_hls=None, service_region=None, signed_url_auth=None, timeout=None, ua_access_rule=None, url_normalize=None, video_drag=None, _configuration=None):  # noqa: E501
        """BatchUpdateCdnConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregate = None
        self._area_access_rule = None
        self._bandwidth_limit = None
        self._browser_cache = None
        self._cache = None
        self._cache_host = None
        self._cache_key = None
        self._compression = None
        self._conditional_origin = None
        self._custom_error_page = None
        self._customize_access_rule = None
        self._domains = None
        self._download_speed_limit = None
        self._follow_redirect = None
        self._https = None
        self._http_forced_redirect = None
        self._ipv6 = None
        self._ip_access_rule = None
        self._ip_freq_limit = None
        self._method_denied_rule = None
        self._multi_range = None
        self._negative_cache = None
        self._offline_cache = None
        self._origin = None
        self._origin_access_rule = None
        self._origin_arg = None
        self._origin_cert_check = None
        self._origin_host = None
        self._origin_ipv6 = None
        self._origin_protocol = None
        self._origin_range = None
        self._origin_retry = None
        self._origin_rewrite = None
        self._origin_sni = None
        self._page_optimization = None
        self._quic = None
        self._redirection_rewrite = None
        self._referer_access_rule = None
        self._remote_auth = None
        self._request_block_rule = None
        self._request_header = None
        self._response_header = None
        self._rewrite_hls = None
        self._service_region = None
        self._signed_url_auth = None
        self._timeout = None
        self._ua_access_rule = None
        self._url_normalize = None
        self._video_drag = None
        self.discriminator = None

        if aggregate is not None:
            self.aggregate = aggregate
        if area_access_rule is not None:
            self.area_access_rule = area_access_rule
        if bandwidth_limit is not None:
            self.bandwidth_limit = bandwidth_limit
        if browser_cache is not None:
            self.browser_cache = browser_cache
        if cache is not None:
            self.cache = cache
        if cache_host is not None:
            self.cache_host = cache_host
        if cache_key is not None:
            self.cache_key = cache_key
        if compression is not None:
            self.compression = compression
        if conditional_origin is not None:
            self.conditional_origin = conditional_origin
        if custom_error_page is not None:
            self.custom_error_page = custom_error_page
        if customize_access_rule is not None:
            self.customize_access_rule = customize_access_rule
        if domains is not None:
            self.domains = domains
        if download_speed_limit is not None:
            self.download_speed_limit = download_speed_limit
        if follow_redirect is not None:
            self.follow_redirect = follow_redirect
        if https is not None:
            self.https = https
        if http_forced_redirect is not None:
            self.http_forced_redirect = http_forced_redirect
        if ipv6 is not None:
            self.ipv6 = ipv6
        if ip_access_rule is not None:
            self.ip_access_rule = ip_access_rule
        if ip_freq_limit is not None:
            self.ip_freq_limit = ip_freq_limit
        if method_denied_rule is not None:
            self.method_denied_rule = method_denied_rule
        if multi_range is not None:
            self.multi_range = multi_range
        if negative_cache is not None:
            self.negative_cache = negative_cache
        if offline_cache is not None:
            self.offline_cache = offline_cache
        if origin is not None:
            self.origin = origin
        if origin_access_rule is not None:
            self.origin_access_rule = origin_access_rule
        if origin_arg is not None:
            self.origin_arg = origin_arg
        if origin_cert_check is not None:
            self.origin_cert_check = origin_cert_check
        if origin_host is not None:
            self.origin_host = origin_host
        if origin_ipv6 is not None:
            self.origin_ipv6 = origin_ipv6
        if origin_protocol is not None:
            self.origin_protocol = origin_protocol
        if origin_range is not None:
            self.origin_range = origin_range
        if origin_retry is not None:
            self.origin_retry = origin_retry
        if origin_rewrite is not None:
            self.origin_rewrite = origin_rewrite
        if origin_sni is not None:
            self.origin_sni = origin_sni
        if page_optimization is not None:
            self.page_optimization = page_optimization
        if quic is not None:
            self.quic = quic
        if redirection_rewrite is not None:
            self.redirection_rewrite = redirection_rewrite
        if referer_access_rule is not None:
            self.referer_access_rule = referer_access_rule
        if remote_auth is not None:
            self.remote_auth = remote_auth
        if request_block_rule is not None:
            self.request_block_rule = request_block_rule
        if request_header is not None:
            self.request_header = request_header
        if response_header is not None:
            self.response_header = response_header
        if rewrite_hls is not None:
            self.rewrite_hls = rewrite_hls
        if service_region is not None:
            self.service_region = service_region
        if signed_url_auth is not None:
            self.signed_url_auth = signed_url_auth
        if timeout is not None:
            self.timeout = timeout
        if ua_access_rule is not None:
            self.ua_access_rule = ua_access_rule
        if url_normalize is not None:
            self.url_normalize = url_normalize
        if video_drag is not None:
            self.video_drag = video_drag

    @property
    def aggregate(self):
        """Gets the aggregate of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The aggregate of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._aggregate

    @aggregate.setter
    def aggregate(self, aggregate):
        """Sets the aggregate of this BatchUpdateCdnConfigRequest.


        :param aggregate: The aggregate of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._aggregate = aggregate

    @property
    def area_access_rule(self):
        """Gets the area_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The area_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: AreaAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._area_access_rule

    @area_access_rule.setter
    def area_access_rule(self, area_access_rule):
        """Sets the area_access_rule of this BatchUpdateCdnConfigRequest.


        :param area_access_rule: The area_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: AreaAccessRuleForBatchUpdateCdnConfigInput
        """

        self._area_access_rule = area_access_rule

    @property
    def bandwidth_limit(self):
        """Gets the bandwidth_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The bandwidth_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: BandwidthLimitForBatchUpdateCdnConfigInput
        """
        return self._bandwidth_limit

    @bandwidth_limit.setter
    def bandwidth_limit(self, bandwidth_limit):
        """Sets the bandwidth_limit of this BatchUpdateCdnConfigRequest.


        :param bandwidth_limit: The bandwidth_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: BandwidthLimitForBatchUpdateCdnConfigInput
        """

        self._bandwidth_limit = bandwidth_limit

    @property
    def browser_cache(self):
        """Gets the browser_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The browser_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[BrowserCacheForBatchUpdateCdnConfigInput]
        """
        return self._browser_cache

    @browser_cache.setter
    def browser_cache(self, browser_cache):
        """Sets the browser_cache of this BatchUpdateCdnConfigRequest.


        :param browser_cache: The browser_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[BrowserCacheForBatchUpdateCdnConfigInput]
        """

        self._browser_cache = browser_cache

    @property
    def cache(self):
        """Gets the cache of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[CacheForBatchUpdateCdnConfigInput]
        """
        return self._cache

    @cache.setter
    def cache(self, cache):
        """Sets the cache of this BatchUpdateCdnConfigRequest.


        :param cache: The cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[CacheForBatchUpdateCdnConfigInput]
        """

        self._cache = cache

    @property
    def cache_host(self):
        """Gets the cache_host of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The cache_host of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: CacheHostForBatchUpdateCdnConfigInput
        """
        return self._cache_host

    @cache_host.setter
    def cache_host(self, cache_host):
        """Sets the cache_host of this BatchUpdateCdnConfigRequest.


        :param cache_host: The cache_host of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: CacheHostForBatchUpdateCdnConfigInput
        """

        self._cache_host = cache_host

    @property
    def cache_key(self):
        """Gets the cache_key of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The cache_key of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[CacheKeyForBatchUpdateCdnConfigInput]
        """
        return self._cache_key

    @cache_key.setter
    def cache_key(self, cache_key):
        """Sets the cache_key of this BatchUpdateCdnConfigRequest.


        :param cache_key: The cache_key of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[CacheKeyForBatchUpdateCdnConfigInput]
        """

        self._cache_key = cache_key

    @property
    def compression(self):
        """Gets the compression of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The compression of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: CompressionForBatchUpdateCdnConfigInput
        """
        return self._compression

    @compression.setter
    def compression(self, compression):
        """Sets the compression of this BatchUpdateCdnConfigRequest.


        :param compression: The compression of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: CompressionForBatchUpdateCdnConfigInput
        """

        self._compression = compression

    @property
    def conditional_origin(self):
        """Gets the conditional_origin of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The conditional_origin of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: ConditionalOriginForBatchUpdateCdnConfigInput
        """
        return self._conditional_origin

    @conditional_origin.setter
    def conditional_origin(self, conditional_origin):
        """Sets the conditional_origin of this BatchUpdateCdnConfigRequest.


        :param conditional_origin: The conditional_origin of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: ConditionalOriginForBatchUpdateCdnConfigInput
        """

        self._conditional_origin = conditional_origin

    @property
    def custom_error_page(self):
        """Gets the custom_error_page of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The custom_error_page of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: CustomErrorPageForBatchUpdateCdnConfigInput
        """
        return self._custom_error_page

    @custom_error_page.setter
    def custom_error_page(self, custom_error_page):
        """Sets the custom_error_page of this BatchUpdateCdnConfigRequest.


        :param custom_error_page: The custom_error_page of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: CustomErrorPageForBatchUpdateCdnConfigInput
        """

        self._custom_error_page = custom_error_page

    @property
    def customize_access_rule(self):
        """Gets the customize_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The customize_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: CustomizeAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._customize_access_rule

    @customize_access_rule.setter
    def customize_access_rule(self, customize_access_rule):
        """Sets the customize_access_rule of this BatchUpdateCdnConfigRequest.


        :param customize_access_rule: The customize_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: CustomizeAccessRuleForBatchUpdateCdnConfigInput
        """

        self._customize_access_rule = customize_access_rule

    @property
    def domains(self):
        """Gets the domains of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The domains of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._domains

    @domains.setter
    def domains(self, domains):
        """Sets the domains of this BatchUpdateCdnConfigRequest.


        :param domains: The domains of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._domains = domains

    @property
    def download_speed_limit(self):
        """Gets the download_speed_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The download_speed_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: DownloadSpeedLimitForBatchUpdateCdnConfigInput
        """
        return self._download_speed_limit

    @download_speed_limit.setter
    def download_speed_limit(self, download_speed_limit):
        """Sets the download_speed_limit of this BatchUpdateCdnConfigRequest.


        :param download_speed_limit: The download_speed_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: DownloadSpeedLimitForBatchUpdateCdnConfigInput
        """

        self._download_speed_limit = download_speed_limit

    @property
    def follow_redirect(self):
        """Gets the follow_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The follow_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._follow_redirect

    @follow_redirect.setter
    def follow_redirect(self, follow_redirect):
        """Sets the follow_redirect of this BatchUpdateCdnConfigRequest.


        :param follow_redirect: The follow_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._follow_redirect = follow_redirect

    @property
    def https(self):
        """Gets the https of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The https of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: HTTPSForBatchUpdateCdnConfigInput
        """
        return self._https

    @https.setter
    def https(self, https):
        """Sets the https of this BatchUpdateCdnConfigRequest.


        :param https: The https of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: HTTPSForBatchUpdateCdnConfigInput
        """

        self._https = https

    @property
    def http_forced_redirect(self):
        """Gets the http_forced_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The http_forced_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: HttpForcedRedirectForBatchUpdateCdnConfigInput
        """
        return self._http_forced_redirect

    @http_forced_redirect.setter
    def http_forced_redirect(self, http_forced_redirect):
        """Sets the http_forced_redirect of this BatchUpdateCdnConfigRequest.


        :param http_forced_redirect: The http_forced_redirect of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: HttpForcedRedirectForBatchUpdateCdnConfigInput
        """

        self._http_forced_redirect = http_forced_redirect

    @property
    def ipv6(self):
        """Gets the ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: IPv6ForBatchUpdateCdnConfigInput
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this BatchUpdateCdnConfigRequest.


        :param ipv6: The ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: IPv6ForBatchUpdateCdnConfigInput
        """

        self._ipv6 = ipv6

    @property
    def ip_access_rule(self):
        """Gets the ip_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The ip_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: IpAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._ip_access_rule

    @ip_access_rule.setter
    def ip_access_rule(self, ip_access_rule):
        """Sets the ip_access_rule of this BatchUpdateCdnConfigRequest.


        :param ip_access_rule: The ip_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: IpAccessRuleForBatchUpdateCdnConfigInput
        """

        self._ip_access_rule = ip_access_rule

    @property
    def ip_freq_limit(self):
        """Gets the ip_freq_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The ip_freq_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: IpFreqLimitForBatchUpdateCdnConfigInput
        """
        return self._ip_freq_limit

    @ip_freq_limit.setter
    def ip_freq_limit(self, ip_freq_limit):
        """Sets the ip_freq_limit of this BatchUpdateCdnConfigRequest.


        :param ip_freq_limit: The ip_freq_limit of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: IpFreqLimitForBatchUpdateCdnConfigInput
        """

        self._ip_freq_limit = ip_freq_limit

    @property
    def method_denied_rule(self):
        """Gets the method_denied_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The method_denied_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: MethodDeniedRuleForBatchUpdateCdnConfigInput
        """
        return self._method_denied_rule

    @method_denied_rule.setter
    def method_denied_rule(self, method_denied_rule):
        """Sets the method_denied_rule of this BatchUpdateCdnConfigRequest.


        :param method_denied_rule: The method_denied_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: MethodDeniedRuleForBatchUpdateCdnConfigInput
        """

        self._method_denied_rule = method_denied_rule

    @property
    def multi_range(self):
        """Gets the multi_range of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The multi_range of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: MultiRangeForBatchUpdateCdnConfigInput
        """
        return self._multi_range

    @multi_range.setter
    def multi_range(self, multi_range):
        """Sets the multi_range of this BatchUpdateCdnConfigRequest.


        :param multi_range: The multi_range of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: MultiRangeForBatchUpdateCdnConfigInput
        """

        self._multi_range = multi_range

    @property
    def negative_cache(self):
        """Gets the negative_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The negative_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[NegativeCacheForBatchUpdateCdnConfigInput]
        """
        return self._negative_cache

    @negative_cache.setter
    def negative_cache(self, negative_cache):
        """Sets the negative_cache of this BatchUpdateCdnConfigRequest.


        :param negative_cache: The negative_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[NegativeCacheForBatchUpdateCdnConfigInput]
        """

        self._negative_cache = negative_cache

    @property
    def offline_cache(self):
        """Gets the offline_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The offline_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OfflineCacheForBatchUpdateCdnConfigInput
        """
        return self._offline_cache

    @offline_cache.setter
    def offline_cache(self, offline_cache):
        """Sets the offline_cache of this BatchUpdateCdnConfigRequest.


        :param offline_cache: The offline_cache of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OfflineCacheForBatchUpdateCdnConfigInput
        """

        self._offline_cache = offline_cache

    @property
    def origin(self):
        """Gets the origin of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[OriginForBatchUpdateCdnConfigInput]
        """
        return self._origin

    @origin.setter
    def origin(self, origin):
        """Sets the origin of this BatchUpdateCdnConfigRequest.


        :param origin: The origin of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[OriginForBatchUpdateCdnConfigInput]
        """

        self._origin = origin

    @property
    def origin_access_rule(self):
        """Gets the origin_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._origin_access_rule

    @origin_access_rule.setter
    def origin_access_rule(self, origin_access_rule):
        """Sets the origin_access_rule of this BatchUpdateCdnConfigRequest.


        :param origin_access_rule: The origin_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OriginAccessRuleForBatchUpdateCdnConfigInput
        """

        self._origin_access_rule = origin_access_rule

    @property
    def origin_arg(self):
        """Gets the origin_arg of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_arg of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[OriginArgForBatchUpdateCdnConfigInput]
        """
        return self._origin_arg

    @origin_arg.setter
    def origin_arg(self, origin_arg):
        """Sets the origin_arg of this BatchUpdateCdnConfigRequest.


        :param origin_arg: The origin_arg of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[OriginArgForBatchUpdateCdnConfigInput]
        """

        self._origin_arg = origin_arg

    @property
    def origin_cert_check(self):
        """Gets the origin_cert_check of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_cert_check of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginCertCheckForBatchUpdateCdnConfigInput
        """
        return self._origin_cert_check

    @origin_cert_check.setter
    def origin_cert_check(self, origin_cert_check):
        """Sets the origin_cert_check of this BatchUpdateCdnConfigRequest.


        :param origin_cert_check: The origin_cert_check of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OriginCertCheckForBatchUpdateCdnConfigInput
        """

        self._origin_cert_check = origin_cert_check

    @property
    def origin_host(self):
        """Gets the origin_host of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_host of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_host

    @origin_host.setter
    def origin_host(self, origin_host):
        """Sets the origin_host of this BatchUpdateCdnConfigRequest.


        :param origin_host: The origin_host of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_host = origin_host

    @property
    def origin_ipv6(self):
        """Gets the origin_ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_ipv6

    @origin_ipv6.setter
    def origin_ipv6(self, origin_ipv6):
        """Sets the origin_ipv6 of this BatchUpdateCdnConfigRequest.


        :param origin_ipv6: The origin_ipv6 of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_ipv6 = origin_ipv6

    @property
    def origin_protocol(self):
        """Gets the origin_protocol of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_protocol of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._origin_protocol

    @origin_protocol.setter
    def origin_protocol(self, origin_protocol):
        """Sets the origin_protocol of this BatchUpdateCdnConfigRequest.


        :param origin_protocol: The origin_protocol of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._origin_protocol = origin_protocol

    @property
    def origin_range(self):
        """Gets the origin_range of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_range of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._origin_range

    @origin_range.setter
    def origin_range(self, origin_range):
        """Sets the origin_range of this BatchUpdateCdnConfigRequest.


        :param origin_range: The origin_range of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._origin_range = origin_range

    @property
    def origin_retry(self):
        """Gets the origin_retry of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_retry of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginRetryForBatchUpdateCdnConfigInput
        """
        return self._origin_retry

    @origin_retry.setter
    def origin_retry(self, origin_retry):
        """Sets the origin_retry of this BatchUpdateCdnConfigRequest.


        :param origin_retry: The origin_retry of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OriginRetryForBatchUpdateCdnConfigInput
        """

        self._origin_retry = origin_retry

    @property
    def origin_rewrite(self):
        """Gets the origin_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginRewriteForBatchUpdateCdnConfigInput
        """
        return self._origin_rewrite

    @origin_rewrite.setter
    def origin_rewrite(self, origin_rewrite):
        """Sets the origin_rewrite of this BatchUpdateCdnConfigRequest.


        :param origin_rewrite: The origin_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OriginRewriteForBatchUpdateCdnConfigInput
        """

        self._origin_rewrite = origin_rewrite

    @property
    def origin_sni(self):
        """Gets the origin_sni of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The origin_sni of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: OriginSniForBatchUpdateCdnConfigInput
        """
        return self._origin_sni

    @origin_sni.setter
    def origin_sni(self, origin_sni):
        """Sets the origin_sni of this BatchUpdateCdnConfigRequest.


        :param origin_sni: The origin_sni of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: OriginSniForBatchUpdateCdnConfigInput
        """

        self._origin_sni = origin_sni

    @property
    def page_optimization(self):
        """Gets the page_optimization of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The page_optimization of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: PageOptimizationForBatchUpdateCdnConfigInput
        """
        return self._page_optimization

    @page_optimization.setter
    def page_optimization(self, page_optimization):
        """Sets the page_optimization of this BatchUpdateCdnConfigRequest.


        :param page_optimization: The page_optimization of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: PageOptimizationForBatchUpdateCdnConfigInput
        """

        self._page_optimization = page_optimization

    @property
    def quic(self):
        """Gets the quic of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The quic of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: QuicForBatchUpdateCdnConfigInput
        """
        return self._quic

    @quic.setter
    def quic(self, quic):
        """Sets the quic of this BatchUpdateCdnConfigRequest.


        :param quic: The quic of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: QuicForBatchUpdateCdnConfigInput
        """

        self._quic = quic

    @property
    def redirection_rewrite(self):
        """Gets the redirection_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The redirection_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: RedirectionRewriteForBatchUpdateCdnConfigInput
        """
        return self._redirection_rewrite

    @redirection_rewrite.setter
    def redirection_rewrite(self, redirection_rewrite):
        """Sets the redirection_rewrite of this BatchUpdateCdnConfigRequest.


        :param redirection_rewrite: The redirection_rewrite of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: RedirectionRewriteForBatchUpdateCdnConfigInput
        """

        self._redirection_rewrite = redirection_rewrite

    @property
    def referer_access_rule(self):
        """Gets the referer_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The referer_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: RefererAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._referer_access_rule

    @referer_access_rule.setter
    def referer_access_rule(self, referer_access_rule):
        """Sets the referer_access_rule of this BatchUpdateCdnConfigRequest.


        :param referer_access_rule: The referer_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: RefererAccessRuleForBatchUpdateCdnConfigInput
        """

        self._referer_access_rule = referer_access_rule

    @property
    def remote_auth(self):
        """Gets the remote_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The remote_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: RemoteAuthForBatchUpdateCdnConfigInput
        """
        return self._remote_auth

    @remote_auth.setter
    def remote_auth(self, remote_auth):
        """Sets the remote_auth of this BatchUpdateCdnConfigRequest.


        :param remote_auth: The remote_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: RemoteAuthForBatchUpdateCdnConfigInput
        """

        self._remote_auth = remote_auth

    @property
    def request_block_rule(self):
        """Gets the request_block_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The request_block_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: RequestBlockRuleForBatchUpdateCdnConfigInput
        """
        return self._request_block_rule

    @request_block_rule.setter
    def request_block_rule(self, request_block_rule):
        """Sets the request_block_rule of this BatchUpdateCdnConfigRequest.


        :param request_block_rule: The request_block_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: RequestBlockRuleForBatchUpdateCdnConfigInput
        """

        self._request_block_rule = request_block_rule

    @property
    def request_header(self):
        """Gets the request_header of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The request_header of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[RequestHeaderForBatchUpdateCdnConfigInput]
        """
        return self._request_header

    @request_header.setter
    def request_header(self, request_header):
        """Sets the request_header of this BatchUpdateCdnConfigRequest.


        :param request_header: The request_header of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[RequestHeaderForBatchUpdateCdnConfigInput]
        """

        self._request_header = request_header

    @property
    def response_header(self):
        """Gets the response_header of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The response_header of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: list[ResponseHeaderForBatchUpdateCdnConfigInput]
        """
        return self._response_header

    @response_header.setter
    def response_header(self, response_header):
        """Sets the response_header of this BatchUpdateCdnConfigRequest.


        :param response_header: The response_header of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: list[ResponseHeaderForBatchUpdateCdnConfigInput]
        """

        self._response_header = response_header

    @property
    def rewrite_hls(self):
        """Gets the rewrite_hls of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The rewrite_hls of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: RewriteHLSForBatchUpdateCdnConfigInput
        """
        return self._rewrite_hls

    @rewrite_hls.setter
    def rewrite_hls(self, rewrite_hls):
        """Sets the rewrite_hls of this BatchUpdateCdnConfigRequest.


        :param rewrite_hls: The rewrite_hls of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: RewriteHLSForBatchUpdateCdnConfigInput
        """

        self._rewrite_hls = rewrite_hls

    @property
    def service_region(self):
        """Gets the service_region of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The service_region of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_region

    @service_region.setter
    def service_region(self, service_region):
        """Sets the service_region of this BatchUpdateCdnConfigRequest.


        :param service_region: The service_region of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: str
        """

        self._service_region = service_region

    @property
    def signed_url_auth(self):
        """Gets the signed_url_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The signed_url_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: SignedUrlAuthForBatchUpdateCdnConfigInput
        """
        return self._signed_url_auth

    @signed_url_auth.setter
    def signed_url_auth(self, signed_url_auth):
        """Sets the signed_url_auth of this BatchUpdateCdnConfigRequest.


        :param signed_url_auth: The signed_url_auth of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: SignedUrlAuthForBatchUpdateCdnConfigInput
        """

        self._signed_url_auth = signed_url_auth

    @property
    def timeout(self):
        """Gets the timeout of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The timeout of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: TimeoutForBatchUpdateCdnConfigInput
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this BatchUpdateCdnConfigRequest.


        :param timeout: The timeout of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: TimeoutForBatchUpdateCdnConfigInput
        """

        self._timeout = timeout

    @property
    def ua_access_rule(self):
        """Gets the ua_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The ua_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: UaAccessRuleForBatchUpdateCdnConfigInput
        """
        return self._ua_access_rule

    @ua_access_rule.setter
    def ua_access_rule(self, ua_access_rule):
        """Sets the ua_access_rule of this BatchUpdateCdnConfigRequest.


        :param ua_access_rule: The ua_access_rule of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: UaAccessRuleForBatchUpdateCdnConfigInput
        """

        self._ua_access_rule = ua_access_rule

    @property
    def url_normalize(self):
        """Gets the url_normalize of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The url_normalize of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: UrlNormalizeForBatchUpdateCdnConfigInput
        """
        return self._url_normalize

    @url_normalize.setter
    def url_normalize(self, url_normalize):
        """Sets the url_normalize of this BatchUpdateCdnConfigRequest.


        :param url_normalize: The url_normalize of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: UrlNormalizeForBatchUpdateCdnConfigInput
        """

        self._url_normalize = url_normalize

    @property
    def video_drag(self):
        """Gets the video_drag of this BatchUpdateCdnConfigRequest.  # noqa: E501


        :return: The video_drag of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :rtype: VideoDragForBatchUpdateCdnConfigInput
        """
        return self._video_drag

    @video_drag.setter
    def video_drag(self, video_drag):
        """Sets the video_drag of this BatchUpdateCdnConfigRequest.


        :param video_drag: The video_drag of this BatchUpdateCdnConfigRequest.  # noqa: E501
        :type: VideoDragForBatchUpdateCdnConfigInput
        """

        self._video_drag = video_drag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BatchUpdateCdnConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BatchUpdateCdnConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BatchUpdateCdnConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
