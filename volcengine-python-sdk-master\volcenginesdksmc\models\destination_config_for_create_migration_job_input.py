# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DestinationConfigForCreateMigrationJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_install_blk_none': 'bool',
        'auto_install_virtio11': 'bool',
        'auto_replicate': 'bool',
        'destination_system': 'str',
        'destination_type': 'str',
        'image_name': 'str',
        'instance_id': 'str',
        'region': 'str'
    }

    attribute_map = {
        'auto_install_blk_none': 'AutoInstallBlkNone',
        'auto_install_virtio11': 'AutoInstallVirtio11',
        'auto_replicate': 'AutoReplicate',
        'destination_system': 'DestinationSystem',
        'destination_type': 'DestinationType',
        'image_name': 'ImageName',
        'instance_id': 'InstanceId',
        'region': 'Region'
    }

    def __init__(self, auto_install_blk_none=None, auto_install_virtio11=None, auto_replicate=None, destination_system=None, destination_type=None, image_name=None, instance_id=None, region=None, _configuration=None):  # noqa: E501
        """DestinationConfigForCreateMigrationJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_install_blk_none = None
        self._auto_install_virtio11 = None
        self._auto_replicate = None
        self._destination_system = None
        self._destination_type = None
        self._image_name = None
        self._instance_id = None
        self._region = None
        self.discriminator = None

        if auto_install_blk_none is not None:
            self.auto_install_blk_none = auto_install_blk_none
        if auto_install_virtio11 is not None:
            self.auto_install_virtio11 = auto_install_virtio11
        if auto_replicate is not None:
            self.auto_replicate = auto_replicate
        if destination_system is not None:
            self.destination_system = destination_system
        self.destination_type = destination_type
        if image_name is not None:
            self.image_name = image_name
        if instance_id is not None:
            self.instance_id = instance_id
        self.region = region

    @property
    def auto_install_blk_none(self):
        """Gets the auto_install_blk_none of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The auto_install_blk_none of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_install_blk_none

    @auto_install_blk_none.setter
    def auto_install_blk_none(self, auto_install_blk_none):
        """Sets the auto_install_blk_none of this DestinationConfigForCreateMigrationJobInput.


        :param auto_install_blk_none: The auto_install_blk_none of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: bool
        """

        self._auto_install_blk_none = auto_install_blk_none

    @property
    def auto_install_virtio11(self):
        """Gets the auto_install_virtio11 of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The auto_install_virtio11 of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_install_virtio11

    @auto_install_virtio11.setter
    def auto_install_virtio11(self, auto_install_virtio11):
        """Sets the auto_install_virtio11 of this DestinationConfigForCreateMigrationJobInput.


        :param auto_install_virtio11: The auto_install_virtio11 of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: bool
        """

        self._auto_install_virtio11 = auto_install_virtio11

    @property
    def auto_replicate(self):
        """Gets the auto_replicate of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The auto_replicate of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_replicate

    @auto_replicate.setter
    def auto_replicate(self, auto_replicate):
        """Sets the auto_replicate of this DestinationConfigForCreateMigrationJobInput.


        :param auto_replicate: The auto_replicate of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: bool
        """

        self._auto_replicate = auto_replicate

    @property
    def destination_system(self):
        """Gets the destination_system of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The destination_system of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._destination_system

    @destination_system.setter
    def destination_system(self, destination_system):
        """Sets the destination_system of this DestinationConfigForCreateMigrationJobInput.


        :param destination_system: The destination_system of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """

        self._destination_system = destination_system

    @property
    def destination_type(self):
        """Gets the destination_type of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The destination_type of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._destination_type

    @destination_type.setter
    def destination_type(self, destination_type):
        """Sets the destination_type of this DestinationConfigForCreateMigrationJobInput.


        :param destination_type: The destination_type of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and destination_type is None:
            raise ValueError("Invalid value for `destination_type`, must not be `None`")  # noqa: E501

        self._destination_type = destination_type

    @property
    def image_name(self):
        """Gets the image_name of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The image_name of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this DestinationConfigForCreateMigrationJobInput.


        :param image_name: The image_name of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def instance_id(self):
        """Gets the instance_id of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The instance_id of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DestinationConfigForCreateMigrationJobInput.


        :param instance_id: The instance_id of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def region(self):
        """Gets the region of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The region of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DestinationConfigForCreateMigrationJobInput.


        :param region: The region of this DestinationConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region is None:
            raise ValueError("Invalid value for `region`, must not be `None`")  # noqa: E501

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DestinationConfigForCreateMigrationJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DestinationConfigForCreateMigrationJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DestinationConfigForCreateMigrationJobInput):
            return True

        return self.to_dict() != other.to_dict()
