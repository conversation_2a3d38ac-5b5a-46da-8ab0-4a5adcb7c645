# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IspForDescribeCdnRegionAndIspOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cn_name': 'str',
        'en_name': 'str'
    }

    attribute_map = {
        'cn_name': 'CnName',
        'en_name': 'EnName'
    }

    def __init__(self, cn_name=None, en_name=None, _configuration=None):  # noqa: E501
        """IspForDescribeCdnRegionAndIspOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cn_name = None
        self._en_name = None
        self.discriminator = None

        if cn_name is not None:
            self.cn_name = cn_name
        if en_name is not None:
            self.en_name = en_name

    @property
    def cn_name(self):
        """Gets the cn_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501


        :return: The cn_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501
        :rtype: str
        """
        return self._cn_name

    @cn_name.setter
    def cn_name(self, cn_name):
        """Sets the cn_name of this IspForDescribeCdnRegionAndIspOutput.


        :param cn_name: The cn_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501
        :type: str
        """

        self._cn_name = cn_name

    @property
    def en_name(self):
        """Gets the en_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501


        :return: The en_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501
        :rtype: str
        """
        return self._en_name

    @en_name.setter
    def en_name(self, en_name):
        """Sets the en_name of this IspForDescribeCdnRegionAndIspOutput.


        :param en_name: The en_name of this IspForDescribeCdnRegionAndIspOutput.  # noqa: E501
        :type: str
        """

        self._en_name = en_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IspForDescribeCdnRegionAndIspOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IspForDescribeCdnRegionAndIspOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IspForDescribeCdnRegionAndIspOutput):
            return True

        return self.to_dict() != other.to_dict()
