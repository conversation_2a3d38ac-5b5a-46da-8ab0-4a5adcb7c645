# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssetInfoForGetVirusAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_info': 'ClusterInfoForGetVirusAlarmSummaryInfoOutput',
        'host_info': 'HostInfoForGetVirusAlarmSummaryInfoOutput'
    }

    attribute_map = {
        'cluster_info': 'ClusterInfo',
        'host_info': 'HostInfo'
    }

    def __init__(self, cluster_info=None, host_info=None, _configuration=None):  # noqa: E501
        """AssetInfoForGetVirusAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_info = None
        self._host_info = None
        self.discriminator = None

        if cluster_info is not None:
            self.cluster_info = cluster_info
        if host_info is not None:
            self.host_info = host_info

    @property
    def cluster_info(self):
        """Gets the cluster_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cluster_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: ClusterInfoForGetVirusAlarmSummaryInfoOutput
        """
        return self._cluster_info

    @cluster_info.setter
    def cluster_info(self, cluster_info):
        """Sets the cluster_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.


        :param cluster_info: The cluster_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: ClusterInfoForGetVirusAlarmSummaryInfoOutput
        """

        self._cluster_info = cluster_info

    @property
    def host_info(self):
        """Gets the host_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The host_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: HostInfoForGetVirusAlarmSummaryInfoOutput
        """
        return self._host_info

    @host_info.setter
    def host_info(self, host_info):
        """Sets the host_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.


        :param host_info: The host_info of this AssetInfoForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: HostInfoForGetVirusAlarmSummaryInfoOutput
        """

        self._host_info = host_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssetInfoForGetVirusAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssetInfoForGetVirusAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssetInfoForGetVirusAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
