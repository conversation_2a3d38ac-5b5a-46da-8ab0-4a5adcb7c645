# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateResourceQueueRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'compute_resources': 'list[ComputeResourceForUpdateResourceQueueInput]',
        'description': 'str',
        'dry_run': 'bool',
        'id': 'str',
        'name': 'str',
        'quota': 'QuotaForUpdateResourceQueueInput',
        'rules': 'RulesForUpdateResourceQueueInput',
        'shareable': 'bool',
        'volume_resources': 'list[VolumeResourceForUpdateResourceQueueInput]',
        'workload_infos': 'list[WorkloadInfoForUpdateResourceQueueInput]'
    }

    attribute_map = {
        'compute_resources': 'ComputeResources',
        'description': 'Description',
        'dry_run': 'DryRun',
        'id': 'Id',
        'name': 'Name',
        'quota': 'Quota',
        'rules': 'Rules',
        'shareable': 'Shareable',
        'volume_resources': 'VolumeResources',
        'workload_infos': 'WorkloadInfos'
    }

    def __init__(self, compute_resources=None, description=None, dry_run=None, id=None, name=None, quota=None, rules=None, shareable=None, volume_resources=None, workload_infos=None, _configuration=None):  # noqa: E501
        """UpdateResourceQueueRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._compute_resources = None
        self._description = None
        self._dry_run = None
        self._id = None
        self._name = None
        self._quota = None
        self._rules = None
        self._shareable = None
        self._volume_resources = None
        self._workload_infos = None
        self.discriminator = None

        if compute_resources is not None:
            self.compute_resources = compute_resources
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        self.id = id
        if name is not None:
            self.name = name
        if quota is not None:
            self.quota = quota
        if rules is not None:
            self.rules = rules
        if shareable is not None:
            self.shareable = shareable
        if volume_resources is not None:
            self.volume_resources = volume_resources
        if workload_infos is not None:
            self.workload_infos = workload_infos

    @property
    def compute_resources(self):
        """Gets the compute_resources of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The compute_resources of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: list[ComputeResourceForUpdateResourceQueueInput]
        """
        return self._compute_resources

    @compute_resources.setter
    def compute_resources(self, compute_resources):
        """Sets the compute_resources of this UpdateResourceQueueRequest.


        :param compute_resources: The compute_resources of this UpdateResourceQueueRequest.  # noqa: E501
        :type: list[ComputeResourceForUpdateResourceQueueInput]
        """

        self._compute_resources = compute_resources

    @property
    def description(self):
        """Gets the description of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The description of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateResourceQueueRequest.


        :param description: The description of this UpdateResourceQueueRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The dry_run of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this UpdateResourceQueueRequest.


        :param dry_run: The dry_run of this UpdateResourceQueueRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def id(self):
        """Gets the id of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The id of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateResourceQueueRequest.


        :param id: The id of this UpdateResourceQueueRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The name of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateResourceQueueRequest.


        :param name: The name of this UpdateResourceQueueRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def quota(self):
        """Gets the quota of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The quota of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: QuotaForUpdateResourceQueueInput
        """
        return self._quota

    @quota.setter
    def quota(self, quota):
        """Sets the quota of this UpdateResourceQueueRequest.


        :param quota: The quota of this UpdateResourceQueueRequest.  # noqa: E501
        :type: QuotaForUpdateResourceQueueInput
        """

        self._quota = quota

    @property
    def rules(self):
        """Gets the rules of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The rules of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: RulesForUpdateResourceQueueInput
        """
        return self._rules

    @rules.setter
    def rules(self, rules):
        """Sets the rules of this UpdateResourceQueueRequest.


        :param rules: The rules of this UpdateResourceQueueRequest.  # noqa: E501
        :type: RulesForUpdateResourceQueueInput
        """

        self._rules = rules

    @property
    def shareable(self):
        """Gets the shareable of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The shareable of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this UpdateResourceQueueRequest.


        :param shareable: The shareable of this UpdateResourceQueueRequest.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    @property
    def volume_resources(self):
        """Gets the volume_resources of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The volume_resources of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: list[VolumeResourceForUpdateResourceQueueInput]
        """
        return self._volume_resources

    @volume_resources.setter
    def volume_resources(self, volume_resources):
        """Sets the volume_resources of this UpdateResourceQueueRequest.


        :param volume_resources: The volume_resources of this UpdateResourceQueueRequest.  # noqa: E501
        :type: list[VolumeResourceForUpdateResourceQueueInput]
        """

        self._volume_resources = volume_resources

    @property
    def workload_infos(self):
        """Gets the workload_infos of this UpdateResourceQueueRequest.  # noqa: E501


        :return: The workload_infos of this UpdateResourceQueueRequest.  # noqa: E501
        :rtype: list[WorkloadInfoForUpdateResourceQueueInput]
        """
        return self._workload_infos

    @workload_infos.setter
    def workload_infos(self, workload_infos):
        """Sets the workload_infos of this UpdateResourceQueueRequest.


        :param workload_infos: The workload_infos of this UpdateResourceQueueRequest.  # noqa: E501
        :type: list[WorkloadInfoForUpdateResourceQueueInput]
        """

        self._workload_infos = workload_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateResourceQueueRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateResourceQueueRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateResourceQueueRequest):
            return True

        return self.to_dict() != other.to_dict()
