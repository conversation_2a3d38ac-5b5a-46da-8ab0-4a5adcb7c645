# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDeploymentSetsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deployment_set_ids': 'list[str]',
        'deployment_set_name': 'str',
        'granularity': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'strategy': 'str'
    }

    attribute_map = {
        'deployment_set_ids': 'DeploymentSetIds',
        'deployment_set_name': 'DeploymentSetName',
        'granularity': 'Granularity',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'strategy': 'Strategy'
    }

    def __init__(self, deployment_set_ids=None, deployment_set_name=None, granularity=None, max_results=None, next_token=None, strategy=None, _configuration=None):  # noqa: E501
        """DescribeDeploymentSetsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deployment_set_ids = None
        self._deployment_set_name = None
        self._granularity = None
        self._max_results = None
        self._next_token = None
        self._strategy = None
        self.discriminator = None

        if deployment_set_ids is not None:
            self.deployment_set_ids = deployment_set_ids
        if deployment_set_name is not None:
            self.deployment_set_name = deployment_set_name
        if granularity is not None:
            self.granularity = granularity
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if strategy is not None:
            self.strategy = strategy

    @property
    def deployment_set_ids(self):
        """Gets the deployment_set_ids of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The deployment_set_ids of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._deployment_set_ids

    @deployment_set_ids.setter
    def deployment_set_ids(self, deployment_set_ids):
        """Sets the deployment_set_ids of this DescribeDeploymentSetsRequest.


        :param deployment_set_ids: The deployment_set_ids of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: list[str]
        """

        self._deployment_set_ids = deployment_set_ids

    @property
    def deployment_set_name(self):
        """Gets the deployment_set_name of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The deployment_set_name of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._deployment_set_name

    @deployment_set_name.setter
    def deployment_set_name(self, deployment_set_name):
        """Sets the deployment_set_name of this DescribeDeploymentSetsRequest.


        :param deployment_set_name: The deployment_set_name of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: str
        """

        self._deployment_set_name = deployment_set_name

    @property
    def granularity(self):
        """Gets the granularity of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The granularity of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._granularity

    @granularity.setter
    def granularity(self, granularity):
        """Sets the granularity of this DescribeDeploymentSetsRequest.


        :param granularity: The granularity of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: str
        """

        self._granularity = granularity

    @property
    def max_results(self):
        """Gets the max_results of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The max_results of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeDeploymentSetsRequest.


        :param max_results: The max_results of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The next_token of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeDeploymentSetsRequest.


        :param next_token: The next_token of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def strategy(self):
        """Gets the strategy of this DescribeDeploymentSetsRequest.  # noqa: E501


        :return: The strategy of this DescribeDeploymentSetsRequest.  # noqa: E501
        :rtype: str
        """
        return self._strategy

    @strategy.setter
    def strategy(self, strategy):
        """Sets the strategy of this DescribeDeploymentSetsRequest.


        :param strategy: The strategy of this DescribeDeploymentSetsRequest.  # noqa: E501
        :type: str
        """

        self._strategy = strategy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDeploymentSetsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDeploymentSetsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDeploymentSetsRequest):
            return True

        return self.to_dict() != other.to_dict()
