# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVirusTaskInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'config': 'ConfigForGetVirusTaskInfoOutput',
        'create_time': 'int',
        'distributed_count': 'int',
        'id_count': 'float',
        'id_list': 'list[str]',
        'sub_task_created': 'int',
        'sub_task_failed': 'int',
        'sub_task_running': 'int',
        'sub_task_succeed': 'int',
        'tag': 'str',
        'task_id': 'str',
        'task_name': 'str',
        'task_status': 'str',
        'task_type': 'str',
        'task_user': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'action': 'Action',
        'config': 'Config',
        'create_time': 'CreateTime',
        'distributed_count': 'DistributedCount',
        'id_count': 'IDCount',
        'id_list': 'IDList',
        'sub_task_created': 'SubTaskCreated',
        'sub_task_failed': 'SubTaskFailed',
        'sub_task_running': 'SubTaskRunning',
        'sub_task_succeed': 'SubTaskSucceed',
        'tag': 'Tag',
        'task_id': 'TaskID',
        'task_name': 'TaskName',
        'task_status': 'TaskStatus',
        'task_type': 'TaskType',
        'task_user': 'TaskUser',
        'update_time': 'UpdateTime'
    }

    def __init__(self, action=None, config=None, create_time=None, distributed_count=None, id_count=None, id_list=None, sub_task_created=None, sub_task_failed=None, sub_task_running=None, sub_task_succeed=None, tag=None, task_id=None, task_name=None, task_status=None, task_type=None, task_user=None, update_time=None, _configuration=None):  # noqa: E501
        """GetVirusTaskInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._config = None
        self._create_time = None
        self._distributed_count = None
        self._id_count = None
        self._id_list = None
        self._sub_task_created = None
        self._sub_task_failed = None
        self._sub_task_running = None
        self._sub_task_succeed = None
        self._tag = None
        self._task_id = None
        self._task_name = None
        self._task_status = None
        self._task_type = None
        self._task_user = None
        self._update_time = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if config is not None:
            self.config = config
        if create_time is not None:
            self.create_time = create_time
        if distributed_count is not None:
            self.distributed_count = distributed_count
        if id_count is not None:
            self.id_count = id_count
        if id_list is not None:
            self.id_list = id_list
        if sub_task_created is not None:
            self.sub_task_created = sub_task_created
        if sub_task_failed is not None:
            self.sub_task_failed = sub_task_failed
        if sub_task_running is not None:
            self.sub_task_running = sub_task_running
        if sub_task_succeed is not None:
            self.sub_task_succeed = sub_task_succeed
        if tag is not None:
            self.tag = tag
        if task_id is not None:
            self.task_id = task_id
        if task_name is not None:
            self.task_name = task_name
        if task_status is not None:
            self.task_status = task_status
        if task_type is not None:
            self.task_type = task_type
        if task_user is not None:
            self.task_user = task_user
        if update_time is not None:
            self.update_time = update_time

    @property
    def action(self):
        """Gets the action of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The action of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this GetVirusTaskInfoResponse.


        :param action: The action of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def config(self):
        """Gets the config of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The config of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: ConfigForGetVirusTaskInfoOutput
        """
        return self._config

    @config.setter
    def config(self, config):
        """Sets the config of this GetVirusTaskInfoResponse.


        :param config: The config of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: ConfigForGetVirusTaskInfoOutput
        """

        self._config = config

    @property
    def create_time(self):
        """Gets the create_time of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The create_time of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetVirusTaskInfoResponse.


        :param create_time: The create_time of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def distributed_count(self):
        """Gets the distributed_count of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The distributed_count of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._distributed_count

    @distributed_count.setter
    def distributed_count(self, distributed_count):
        """Sets the distributed_count of this GetVirusTaskInfoResponse.


        :param distributed_count: The distributed_count of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._distributed_count = distributed_count

    @property
    def id_count(self):
        """Gets the id_count of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The id_count of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: float
        """
        return self._id_count

    @id_count.setter
    def id_count(self, id_count):
        """Sets the id_count of this GetVirusTaskInfoResponse.


        :param id_count: The id_count of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: float
        """

        self._id_count = id_count

    @property
    def id_list(self):
        """Gets the id_list of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The id_list of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._id_list

    @id_list.setter
    def id_list(self, id_list):
        """Sets the id_list of this GetVirusTaskInfoResponse.


        :param id_list: The id_list of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: list[str]
        """

        self._id_list = id_list

    @property
    def sub_task_created(self):
        """Gets the sub_task_created of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The sub_task_created of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_created

    @sub_task_created.setter
    def sub_task_created(self, sub_task_created):
        """Sets the sub_task_created of this GetVirusTaskInfoResponse.


        :param sub_task_created: The sub_task_created of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._sub_task_created = sub_task_created

    @property
    def sub_task_failed(self):
        """Gets the sub_task_failed of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The sub_task_failed of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_failed

    @sub_task_failed.setter
    def sub_task_failed(self, sub_task_failed):
        """Sets the sub_task_failed of this GetVirusTaskInfoResponse.


        :param sub_task_failed: The sub_task_failed of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._sub_task_failed = sub_task_failed

    @property
    def sub_task_running(self):
        """Gets the sub_task_running of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The sub_task_running of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_running

    @sub_task_running.setter
    def sub_task_running(self, sub_task_running):
        """Sets the sub_task_running of this GetVirusTaskInfoResponse.


        :param sub_task_running: The sub_task_running of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._sub_task_running = sub_task_running

    @property
    def sub_task_succeed(self):
        """Gets the sub_task_succeed of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The sub_task_succeed of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_succeed

    @sub_task_succeed.setter
    def sub_task_succeed(self, sub_task_succeed):
        """Sets the sub_task_succeed of this GetVirusTaskInfoResponse.


        :param sub_task_succeed: The sub_task_succeed of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._sub_task_succeed = sub_task_succeed

    @property
    def tag(self):
        """Gets the tag of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The tag of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this GetVirusTaskInfoResponse.


        :param tag: The tag of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def task_id(self):
        """Gets the task_id of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The task_id of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this GetVirusTaskInfoResponse.


        :param task_id: The task_id of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_name(self):
        """Gets the task_name of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The task_name of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this GetVirusTaskInfoResponse.


        :param task_name: The task_name of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    @property
    def task_status(self):
        """Gets the task_status of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The task_status of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this GetVirusTaskInfoResponse.


        :param task_status: The task_status of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._task_status = task_status

    @property
    def task_type(self):
        """Gets the task_type of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The task_type of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this GetVirusTaskInfoResponse.


        :param task_type: The task_type of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    @property
    def task_user(self):
        """Gets the task_user of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The task_user of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._task_user

    @task_user.setter
    def task_user(self, task_user):
        """Sets the task_user of this GetVirusTaskInfoResponse.


        :param task_user: The task_user of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: str
        """

        self._task_user = task_user

    @property
    def update_time(self):
        """Gets the update_time of this GetVirusTaskInfoResponse.  # noqa: E501


        :return: The update_time of this GetVirusTaskInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetVirusTaskInfoResponse.


        :param update_time: The update_time of this GetVirusTaskInfoResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVirusTaskInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVirusTaskInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVirusTaskInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
