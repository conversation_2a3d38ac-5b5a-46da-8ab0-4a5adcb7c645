# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListWorkspacesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_download_url': 'str',
        'create_time': 'int',
        'description': 'str',
        'id': 'str',
        'labels': 'list[str]',
        'name': 'str',
        'owner_name': 'str',
        'public_meta': 'PublicMetaForListWorkspacesOutput',
        'role': 'str',
        's3_bucket': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'cover_download_url': 'CoverDownloadURL',
        'create_time': 'CreateTime',
        'description': 'Description',
        'id': 'ID',
        'labels': 'Labels',
        'name': 'Name',
        'owner_name': 'OwnerName',
        'public_meta': 'PublicMeta',
        'role': 'Role',
        's3_bucket': 'S3Bucket',
        'update_time': 'UpdateTime'
    }

    def __init__(self, cover_download_url=None, create_time=None, description=None, id=None, labels=None, name=None, owner_name=None, public_meta=None, role=None, s3_bucket=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListWorkspacesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_download_url = None
        self._create_time = None
        self._description = None
        self._id = None
        self._labels = None
        self._name = None
        self._owner_name = None
        self._public_meta = None
        self._role = None
        self._s3_bucket = None
        self._update_time = None
        self.discriminator = None

        if cover_download_url is not None:
            self.cover_download_url = cover_download_url
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if labels is not None:
            self.labels = labels
        if name is not None:
            self.name = name
        if owner_name is not None:
            self.owner_name = owner_name
        if public_meta is not None:
            self.public_meta = public_meta
        if role is not None:
            self.role = role
        if s3_bucket is not None:
            self.s3_bucket = s3_bucket
        if update_time is not None:
            self.update_time = update_time

    @property
    def cover_download_url(self):
        """Gets the cover_download_url of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The cover_download_url of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_download_url

    @cover_download_url.setter
    def cover_download_url(self, cover_download_url):
        """Sets the cover_download_url of this ItemForListWorkspacesOutput.


        :param cover_download_url: The cover_download_url of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._cover_download_url = cover_download_url

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The create_time of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListWorkspacesOutput.


        :param create_time: The create_time of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The description of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListWorkspacesOutput.


        :param description: The description of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The id of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListWorkspacesOutput.


        :param id: The id of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def labels(self):
        """Gets the labels of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The labels of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this ItemForListWorkspacesOutput.


        :param labels: The labels of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: list[str]
        """

        self._labels = labels

    @property
    def name(self):
        """Gets the name of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The name of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListWorkspacesOutput.


        :param name: The name of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner_name(self):
        """Gets the owner_name of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The owner_name of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_name

    @owner_name.setter
    def owner_name(self, owner_name):
        """Sets the owner_name of this ItemForListWorkspacesOutput.


        :param owner_name: The owner_name of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._owner_name = owner_name

    @property
    def public_meta(self):
        """Gets the public_meta of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The public_meta of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: PublicMetaForListWorkspacesOutput
        """
        return self._public_meta

    @public_meta.setter
    def public_meta(self, public_meta):
        """Sets the public_meta of this ItemForListWorkspacesOutput.


        :param public_meta: The public_meta of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: PublicMetaForListWorkspacesOutput
        """

        self._public_meta = public_meta

    @property
    def role(self):
        """Gets the role of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The role of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this ItemForListWorkspacesOutput.


        :param role: The role of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._role = role

    @property
    def s3_bucket(self):
        """Gets the s3_bucket of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The s3_bucket of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: str
        """
        return self._s3_bucket

    @s3_bucket.setter
    def s3_bucket(self, s3_bucket):
        """Sets the s3_bucket of this ItemForListWorkspacesOutput.


        :param s3_bucket: The s3_bucket of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: str
        """

        self._s3_bucket = s3_bucket

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListWorkspacesOutput.  # noqa: E501


        :return: The update_time of this ItemForListWorkspacesOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListWorkspacesOutput.


        :param update_time: The update_time of this ItemForListWorkspacesOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListWorkspacesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListWorkspacesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListWorkspacesOutput):
            return True

        return self.to_dict() != other.to_dict()
