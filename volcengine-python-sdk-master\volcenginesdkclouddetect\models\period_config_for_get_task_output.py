# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PeriodConfigForGetTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'days': 'list[int]',
        'period': 'int',
        'time_range_config': 'list[TimeRangeConfigForGetTaskOutput]'
    }

    attribute_map = {
        'days': 'Days',
        'period': 'Period',
        'time_range_config': 'TimeRangeConfig'
    }

    def __init__(self, days=None, period=None, time_range_config=None, _configuration=None):  # noqa: E501
        """PeriodConfigForGetTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._days = None
        self._period = None
        self._time_range_config = None
        self.discriminator = None

        if days is not None:
            self.days = days
        if period is not None:
            self.period = period
        if time_range_config is not None:
            self.time_range_config = time_range_config

    @property
    def days(self):
        """Gets the days of this PeriodConfigForGetTaskOutput.  # noqa: E501


        :return: The days of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._days

    @days.setter
    def days(self, days):
        """Sets the days of this PeriodConfigForGetTaskOutput.


        :param days: The days of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :type: list[int]
        """

        self._days = days

    @property
    def period(self):
        """Gets the period of this PeriodConfigForGetTaskOutput.  # noqa: E501


        :return: The period of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this PeriodConfigForGetTaskOutput.


        :param period: The period of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def time_range_config(self):
        """Gets the time_range_config of this PeriodConfigForGetTaskOutput.  # noqa: E501


        :return: The time_range_config of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :rtype: list[TimeRangeConfigForGetTaskOutput]
        """
        return self._time_range_config

    @time_range_config.setter
    def time_range_config(self, time_range_config):
        """Sets the time_range_config of this PeriodConfigForGetTaskOutput.


        :param time_range_config: The time_range_config of this PeriodConfigForGetTaskOutput.  # noqa: E501
        :type: list[TimeRangeConfigForGetTaskOutput]
        """

        self._time_range_config = time_range_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PeriodConfigForGetTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PeriodConfigForGetTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PeriodConfigForGetTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
