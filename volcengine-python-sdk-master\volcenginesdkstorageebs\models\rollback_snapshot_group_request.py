# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RollbackSnapshotGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'instance_id': 'str',
        'snapshot_group_id': 'str',
        'snapshot_ids': 'list[str]',
        'volume_ids': 'list[str]'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'instance_id': 'InstanceId',
        'snapshot_group_id': 'SnapshotGroupId',
        'snapshot_ids': 'SnapshotIds',
        'volume_ids': 'VolumeIds'
    }

    def __init__(self, client_token=None, instance_id=None, snapshot_group_id=None, snapshot_ids=None, volume_ids=None, _configuration=None):  # noqa: E501
        """RollbackSnapshotGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._instance_id = None
        self._snapshot_group_id = None
        self._snapshot_ids = None
        self._volume_ids = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if instance_id is not None:
            self.instance_id = instance_id
        self.snapshot_group_id = snapshot_group_id
        if snapshot_ids is not None:
            self.snapshot_ids = snapshot_ids
        if volume_ids is not None:
            self.volume_ids = volume_ids

    @property
    def client_token(self):
        """Gets the client_token of this RollbackSnapshotGroupRequest.  # noqa: E501


        :return: The client_token of this RollbackSnapshotGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this RollbackSnapshotGroupRequest.


        :param client_token: The client_token of this RollbackSnapshotGroupRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this RollbackSnapshotGroupRequest.  # noqa: E501


        :return: The instance_id of this RollbackSnapshotGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this RollbackSnapshotGroupRequest.


        :param instance_id: The instance_id of this RollbackSnapshotGroupRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def snapshot_group_id(self):
        """Gets the snapshot_group_id of this RollbackSnapshotGroupRequest.  # noqa: E501


        :return: The snapshot_group_id of this RollbackSnapshotGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_group_id

    @snapshot_group_id.setter
    def snapshot_group_id(self, snapshot_group_id):
        """Sets the snapshot_group_id of this RollbackSnapshotGroupRequest.


        :param snapshot_group_id: The snapshot_group_id of this RollbackSnapshotGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and snapshot_group_id is None:
            raise ValueError("Invalid value for `snapshot_group_id`, must not be `None`")  # noqa: E501

        self._snapshot_group_id = snapshot_group_id

    @property
    def snapshot_ids(self):
        """Gets the snapshot_ids of this RollbackSnapshotGroupRequest.  # noqa: E501


        :return: The snapshot_ids of this RollbackSnapshotGroupRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._snapshot_ids

    @snapshot_ids.setter
    def snapshot_ids(self, snapshot_ids):
        """Sets the snapshot_ids of this RollbackSnapshotGroupRequest.


        :param snapshot_ids: The snapshot_ids of this RollbackSnapshotGroupRequest.  # noqa: E501
        :type: list[str]
        """

        self._snapshot_ids = snapshot_ids

    @property
    def volume_ids(self):
        """Gets the volume_ids of this RollbackSnapshotGroupRequest.  # noqa: E501


        :return: The volume_ids of this RollbackSnapshotGroupRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._volume_ids

    @volume_ids.setter
    def volume_ids(self, volume_ids):
        """Sets the volume_ids of this RollbackSnapshotGroupRequest.


        :param volume_ids: The volume_ids of this RollbackSnapshotGroupRequest.  # noqa: E501
        :type: list[str]
        """

        self._volume_ids = volume_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RollbackSnapshotGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RollbackSnapshotGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RollbackSnapshotGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
