# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryMessageByTimestampResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'has_more_message': 'bool',
        'message_list': 'list[MessageListForQueryMessageByTimestampOutput]'
    }

    attribute_map = {
        'has_more_message': 'HasMoreMessage',
        'message_list': 'MessageList'
    }

    def __init__(self, has_more_message=None, message_list=None, _configuration=None):  # noqa: E501
        """QueryMessageByTimestampResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._has_more_message = None
        self._message_list = None
        self.discriminator = None

        if has_more_message is not None:
            self.has_more_message = has_more_message
        if message_list is not None:
            self.message_list = message_list

    @property
    def has_more_message(self):
        """Gets the has_more_message of this QueryMessageByTimestampResponse.  # noqa: E501


        :return: The has_more_message of this QueryMessageByTimestampResponse.  # noqa: E501
        :rtype: bool
        """
        return self._has_more_message

    @has_more_message.setter
    def has_more_message(self, has_more_message):
        """Sets the has_more_message of this QueryMessageByTimestampResponse.


        :param has_more_message: The has_more_message of this QueryMessageByTimestampResponse.  # noqa: E501
        :type: bool
        """

        self._has_more_message = has_more_message

    @property
    def message_list(self):
        """Gets the message_list of this QueryMessageByTimestampResponse.  # noqa: E501


        :return: The message_list of this QueryMessageByTimestampResponse.  # noqa: E501
        :rtype: list[MessageListForQueryMessageByTimestampOutput]
        """
        return self._message_list

    @message_list.setter
    def message_list(self, message_list):
        """Sets the message_list of this QueryMessageByTimestampResponse.


        :param message_list: The message_list of this QueryMessageByTimestampResponse.  # noqa: E501
        :type: list[MessageListForQueryMessageByTimestampOutput]
        """

        self._message_list = message_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryMessageByTimestampResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryMessageByTimestampResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryMessageByTimestampResponse):
            return True

        return self.to_dict() != other.to_dict()
