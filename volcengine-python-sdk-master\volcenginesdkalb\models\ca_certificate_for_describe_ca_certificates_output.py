# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CACertificateForDescribeCACertificatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ca_certificate_id': 'str',
        'ca_certificate_name': 'str',
        'certificate_type': 'str',
        'create_time': 'str',
        'description': 'str',
        'domain_name': 'str',
        'expired_at': 'str',
        'listeners': 'list[str]',
        'project_name': 'str',
        'san': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeCACertificatesOutput]'
    }

    attribute_map = {
        'ca_certificate_id': 'CACertificateId',
        'ca_certificate_name': 'CACertificateName',
        'certificate_type': 'CertificateType',
        'create_time': 'CreateTime',
        'description': 'Description',
        'domain_name': 'DomainName',
        'expired_at': 'ExpiredAt',
        'listeners': 'Listeners',
        'project_name': 'ProjectName',
        'san': 'San',
        'status': 'Status',
        'tags': 'Tags'
    }

    def __init__(self, ca_certificate_id=None, ca_certificate_name=None, certificate_type=None, create_time=None, description=None, domain_name=None, expired_at=None, listeners=None, project_name=None, san=None, status=None, tags=None, _configuration=None):  # noqa: E501
        """CACertificateForDescribeCACertificatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ca_certificate_id = None
        self._ca_certificate_name = None
        self._certificate_type = None
        self._create_time = None
        self._description = None
        self._domain_name = None
        self._expired_at = None
        self._listeners = None
        self._project_name = None
        self._san = None
        self._status = None
        self._tags = None
        self.discriminator = None

        if ca_certificate_id is not None:
            self.ca_certificate_id = ca_certificate_id
        if ca_certificate_name is not None:
            self.ca_certificate_name = ca_certificate_name
        if certificate_type is not None:
            self.certificate_type = certificate_type
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if domain_name is not None:
            self.domain_name = domain_name
        if expired_at is not None:
            self.expired_at = expired_at
        if listeners is not None:
            self.listeners = listeners
        if project_name is not None:
            self.project_name = project_name
        if san is not None:
            self.san = san
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags

    @property
    def ca_certificate_id(self):
        """Gets the ca_certificate_id of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The ca_certificate_id of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ca_certificate_id

    @ca_certificate_id.setter
    def ca_certificate_id(self, ca_certificate_id):
        """Sets the ca_certificate_id of this CACertificateForDescribeCACertificatesOutput.


        :param ca_certificate_id: The ca_certificate_id of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._ca_certificate_id = ca_certificate_id

    @property
    def ca_certificate_name(self):
        """Gets the ca_certificate_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The ca_certificate_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ca_certificate_name

    @ca_certificate_name.setter
    def ca_certificate_name(self, ca_certificate_name):
        """Sets the ca_certificate_name of this CACertificateForDescribeCACertificatesOutput.


        :param ca_certificate_name: The ca_certificate_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._ca_certificate_name = ca_certificate_name

    @property
    def certificate_type(self):
        """Gets the certificate_type of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The certificate_type of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_type

    @certificate_type.setter
    def certificate_type(self, certificate_type):
        """Sets the certificate_type of this CACertificateForDescribeCACertificatesOutput.


        :param certificate_type: The certificate_type of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._certificate_type = certificate_type

    @property
    def create_time(self):
        """Gets the create_time of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The create_time of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this CACertificateForDescribeCACertificatesOutput.


        :param create_time: The create_time of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The description of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CACertificateForDescribeCACertificatesOutput.


        :param description: The description of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def domain_name(self):
        """Gets the domain_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The domain_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain_name

    @domain_name.setter
    def domain_name(self, domain_name):
        """Sets the domain_name of this CACertificateForDescribeCACertificatesOutput.


        :param domain_name: The domain_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._domain_name = domain_name

    @property
    def expired_at(self):
        """Gets the expired_at of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The expired_at of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_at

    @expired_at.setter
    def expired_at(self, expired_at):
        """Sets the expired_at of this CACertificateForDescribeCACertificatesOutput.


        :param expired_at: The expired_at of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._expired_at = expired_at

    @property
    def listeners(self):
        """Gets the listeners of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The listeners of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._listeners

    @listeners.setter
    def listeners(self, listeners):
        """Sets the listeners of this CACertificateForDescribeCACertificatesOutput.


        :param listeners: The listeners of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: list[str]
        """

        self._listeners = listeners

    @property
    def project_name(self):
        """Gets the project_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The project_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CACertificateForDescribeCACertificatesOutput.


        :param project_name: The project_name of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def san(self):
        """Gets the san of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The san of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._san

    @san.setter
    def san(self, san):
        """Sets the san of this CACertificateForDescribeCACertificatesOutput.


        :param san: The san of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._san = san

    @property
    def status(self):
        """Gets the status of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The status of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CACertificateForDescribeCACertificatesOutput.


        :param status: The status of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501


        :return: The tags of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :rtype: list[TagForDescribeCACertificatesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CACertificateForDescribeCACertificatesOutput.


        :param tags: The tags of this CACertificateForDescribeCACertificatesOutput.  # noqa: E501
        :type: list[TagForDescribeCACertificatesOutput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CACertificateForDescribeCACertificatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CACertificateForDescribeCACertificatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CACertificateForDescribeCACertificatesOutput):
            return True

        return self.to_dict() != other.to_dict()
