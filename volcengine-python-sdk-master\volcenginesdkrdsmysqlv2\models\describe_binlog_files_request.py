# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBinlogFilesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_file_number': 'int',
        'context': 'str',
        'end_time': 'str',
        'instance_id': 'str',
        'result_sorter': 'str',
        'start_time': 'str'
    }

    attribute_map = {
        'backup_file_number': 'BackupFileNumber',
        'context': 'Context',
        'end_time': 'EndTime',
        'instance_id': 'InstanceId',
        'result_sorter': 'ResultSorter',
        'start_time': 'StartTime'
    }

    def __init__(self, backup_file_number=None, context=None, end_time=None, instance_id=None, result_sorter=None, start_time=None, _configuration=None):  # noqa: E501
        """DescribeBinlogFilesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_file_number = None
        self._context = None
        self._end_time = None
        self._instance_id = None
        self._result_sorter = None
        self._start_time = None
        self.discriminator = None

        if backup_file_number is not None:
            self.backup_file_number = backup_file_number
        if context is not None:
            self.context = context
        self.end_time = end_time
        self.instance_id = instance_id
        if result_sorter is not None:
            self.result_sorter = result_sorter
        self.start_time = start_time

    @property
    def backup_file_number(self):
        """Gets the backup_file_number of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The backup_file_number of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: int
        """
        return self._backup_file_number

    @backup_file_number.setter
    def backup_file_number(self, backup_file_number):
        """Sets the backup_file_number of this DescribeBinlogFilesRequest.


        :param backup_file_number: The backup_file_number of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: int
        """

        self._backup_file_number = backup_file_number

    @property
    def context(self):
        """Gets the context of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The context of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._context

    @context.setter
    def context(self, context):
        """Sets the context of this DescribeBinlogFilesRequest.


        :param context: The context of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: str
        """

        self._context = context

    @property
    def end_time(self):
        """Gets the end_time of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The end_time of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeBinlogFilesRequest.


        :param end_time: The end_time of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The instance_id of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeBinlogFilesRequest.


        :param instance_id: The instance_id of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def result_sorter(self):
        """Gets the result_sorter of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The result_sorter of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._result_sorter

    @result_sorter.setter
    def result_sorter(self, result_sorter):
        """Sets the result_sorter of this DescribeBinlogFilesRequest.


        :param result_sorter: The result_sorter of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: str
        """

        self._result_sorter = result_sorter

    @property
    def start_time(self):
        """Gets the start_time of this DescribeBinlogFilesRequest.  # noqa: E501


        :return: The start_time of this DescribeBinlogFilesRequest.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeBinlogFilesRequest.


        :param start_time: The start_time of this DescribeBinlogFilesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBinlogFilesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBinlogFilesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBinlogFilesRequest):
            return True

        return self.to_dict() != other.to_dict()
