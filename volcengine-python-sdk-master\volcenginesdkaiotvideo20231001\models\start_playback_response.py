# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartPlaybackResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'playback_id': 'str',
        'pull_urls': 'list[str]',
        'push_url': 'str'
    }

    attribute_map = {
        'playback_id': 'PlaybackID',
        'pull_urls': 'PullUrls',
        'push_url': 'PushUrl'
    }

    def __init__(self, playback_id=None, pull_urls=None, push_url=None, _configuration=None):  # noqa: E501
        """StartPlaybackResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._playback_id = None
        self._pull_urls = None
        self._push_url = None
        self.discriminator = None

        if playback_id is not None:
            self.playback_id = playback_id
        if pull_urls is not None:
            self.pull_urls = pull_urls
        if push_url is not None:
            self.push_url = push_url

    @property
    def playback_id(self):
        """Gets the playback_id of this StartPlaybackResponse.  # noqa: E501


        :return: The playback_id of this StartPlaybackResponse.  # noqa: E501
        :rtype: str
        """
        return self._playback_id

    @playback_id.setter
    def playback_id(self, playback_id):
        """Sets the playback_id of this StartPlaybackResponse.


        :param playback_id: The playback_id of this StartPlaybackResponse.  # noqa: E501
        :type: str
        """

        self._playback_id = playback_id

    @property
    def pull_urls(self):
        """Gets the pull_urls of this StartPlaybackResponse.  # noqa: E501


        :return: The pull_urls of this StartPlaybackResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._pull_urls

    @pull_urls.setter
    def pull_urls(self, pull_urls):
        """Sets the pull_urls of this StartPlaybackResponse.


        :param pull_urls: The pull_urls of this StartPlaybackResponse.  # noqa: E501
        :type: list[str]
        """

        self._pull_urls = pull_urls

    @property
    def push_url(self):
        """Gets the push_url of this StartPlaybackResponse.  # noqa: E501


        :return: The push_url of this StartPlaybackResponse.  # noqa: E501
        :rtype: str
        """
        return self._push_url

    @push_url.setter
    def push_url(self, push_url):
        """Sets the push_url of this StartPlaybackResponse.


        :param push_url: The push_url of this StartPlaybackResponse.  # noqa: E501
        :type: str
        """

        self._push_url = push_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartPlaybackResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartPlaybackResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartPlaybackResponse):
            return True

        return self.to_dict() != other.to_dict()
