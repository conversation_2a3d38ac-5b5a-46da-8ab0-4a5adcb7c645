# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo701ForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'module_name': 'str',
        'syscall_number': 'str'
    }

    attribute_map = {
        'module_name': 'ModuleName',
        'syscall_number': 'SyscallNumber'
    }

    def __init__(self, module_name=None, syscall_number=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo701ForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._module_name = None
        self._syscall_number = None
        self.discriminator = None

        if module_name is not None:
            self.module_name = module_name
        if syscall_number is not None:
            self.syscall_number = syscall_number

    @property
    def module_name(self):
        """Gets the module_name of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The module_name of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._module_name

    @module_name.setter
    def module_name(self, module_name):
        """Sets the module_name of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.


        :param module_name: The module_name of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._module_name = module_name

    @property
    def syscall_number(self):
        """Gets the syscall_number of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The syscall_number of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._syscall_number

    @syscall_number.setter
    def syscall_number(self, syscall_number):
        """Sets the syscall_number of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.


        :param syscall_number: The syscall_number of this PlusAlarmInfo701ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._syscall_number = syscall_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo701ForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo701ForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo701ForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
