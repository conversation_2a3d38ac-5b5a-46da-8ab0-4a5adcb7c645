# coding: utf-8

"""
    translate20250301

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubmitAudioRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'source_language': 'str',
        'target_language': 'str',
        'uri': 'str'
    }

    attribute_map = {
        'source_language': 'SourceLanguage',
        'target_language': 'TargetLanguage',
        'uri': 'Uri'
    }

    def __init__(self, source_language=None, target_language=None, uri=None, _configuration=None):  # noqa: E501
        """SubmitAudioRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._source_language = None
        self._target_language = None
        self._uri = None
        self.discriminator = None

        self.source_language = source_language
        self.target_language = target_language
        self.uri = uri

    @property
    def source_language(self):
        """Gets the source_language of this SubmitAudioRequest.  # noqa: E501


        :return: The source_language of this SubmitAudioRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_language

    @source_language.setter
    def source_language(self, source_language):
        """Sets the source_language of this SubmitAudioRequest.


        :param source_language: The source_language of this SubmitAudioRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_language is None:
            raise ValueError("Invalid value for `source_language`, must not be `None`")  # noqa: E501

        self._source_language = source_language

    @property
    def target_language(self):
        """Gets the target_language of this SubmitAudioRequest.  # noqa: E501


        :return: The target_language of this SubmitAudioRequest.  # noqa: E501
        :rtype: str
        """
        return self._target_language

    @target_language.setter
    def target_language(self, target_language):
        """Sets the target_language of this SubmitAudioRequest.


        :param target_language: The target_language of this SubmitAudioRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and target_language is None:
            raise ValueError("Invalid value for `target_language`, must not be `None`")  # noqa: E501

        self._target_language = target_language

    @property
    def uri(self):
        """Gets the uri of this SubmitAudioRequest.  # noqa: E501


        :return: The uri of this SubmitAudioRequest.  # noqa: E501
        :rtype: str
        """
        return self._uri

    @uri.setter
    def uri(self, uri):
        """Sets the uri of this SubmitAudioRequest.


        :param uri: The uri of this SubmitAudioRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and uri is None:
            raise ValueError("Invalid value for `uri`, must not be `None`")  # noqa: E501

        self._uri = uri

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubmitAudioRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubmitAudioRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubmitAudioRequest):
            return True

        return self.to_dict() != other.to_dict()
