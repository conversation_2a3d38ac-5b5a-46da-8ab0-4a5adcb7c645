# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TargetForListTargetsForPolicyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attached_time': 'str',
        'target_id': 'str',
        'target_name': 'str',
        'target_type': 'str'
    }

    attribute_map = {
        'attached_time': 'AttachedTime',
        'target_id': 'TargetID',
        'target_name': 'TargetName',
        'target_type': 'TargetType'
    }

    def __init__(self, attached_time=None, target_id=None, target_name=None, target_type=None, _configuration=None):  # noqa: E501
        """TargetForListTargetsForPolicyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attached_time = None
        self._target_id = None
        self._target_name = None
        self._target_type = None
        self.discriminator = None

        if attached_time is not None:
            self.attached_time = attached_time
        if target_id is not None:
            self.target_id = target_id
        if target_name is not None:
            self.target_name = target_name
        if target_type is not None:
            self.target_type = target_type

    @property
    def attached_time(self):
        """Gets the attached_time of this TargetForListTargetsForPolicyOutput.  # noqa: E501


        :return: The attached_time of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._attached_time

    @attached_time.setter
    def attached_time(self, attached_time):
        """Sets the attached_time of this TargetForListTargetsForPolicyOutput.


        :param attached_time: The attached_time of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :type: str
        """

        self._attached_time = attached_time

    @property
    def target_id(self):
        """Gets the target_id of this TargetForListTargetsForPolicyOutput.  # noqa: E501


        :return: The target_id of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_id

    @target_id.setter
    def target_id(self, target_id):
        """Sets the target_id of this TargetForListTargetsForPolicyOutput.


        :param target_id: The target_id of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :type: str
        """

        self._target_id = target_id

    @property
    def target_name(self):
        """Gets the target_name of this TargetForListTargetsForPolicyOutput.  # noqa: E501


        :return: The target_name of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_name

    @target_name.setter
    def target_name(self, target_name):
        """Sets the target_name of this TargetForListTargetsForPolicyOutput.


        :param target_name: The target_name of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :type: str
        """

        self._target_name = target_name

    @property
    def target_type(self):
        """Gets the target_type of this TargetForListTargetsForPolicyOutput.  # noqa: E501


        :return: The target_type of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_type

    @target_type.setter
    def target_type(self, target_type):
        """Sets the target_type of this TargetForListTargetsForPolicyOutput.


        :param target_type: The target_type of this TargetForListTargetsForPolicyOutput.  # noqa: E501
        :type: str
        """

        self._target_type = target_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TargetForListTargetsForPolicyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TargetForListTargetsForPolicyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TargetForListTargetsForPolicyOutput):
            return True

        return self.to_dict() != other.to_dict()
