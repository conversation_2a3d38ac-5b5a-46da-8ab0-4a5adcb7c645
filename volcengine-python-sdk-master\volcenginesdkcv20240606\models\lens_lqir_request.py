# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LensLqirRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binary_data_base64': 'list[str]',
        'enable_hdr': 'bool',
        'enable_wb': 'bool',
        'hdr_strength': 'float',
        'image_urls': 'list[str]',
        'jpg_quality': 'int',
        'req_key': 'str',
        'resolution_boundary': 'str',
        'result_format': 'int'
    }

    attribute_map = {
        'binary_data_base64': 'binary_data_base64',
        'enable_hdr': 'enable_hdr',
        'enable_wb': 'enable_wb',
        'hdr_strength': 'hdr_strength',
        'image_urls': 'image_urls',
        'jpg_quality': 'jpg_quality',
        'req_key': 'req_key',
        'resolution_boundary': 'resolution_boundary',
        'result_format': 'result_format'
    }

    def __init__(self, binary_data_base64=None, enable_hdr=None, enable_wb=None, hdr_strength=None, image_urls=None, jpg_quality=None, req_key=None, resolution_boundary=None, result_format=None, _configuration=None):  # noqa: E501
        """LensLqirRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binary_data_base64 = None
        self._enable_hdr = None
        self._enable_wb = None
        self._hdr_strength = None
        self._image_urls = None
        self._jpg_quality = None
        self._req_key = None
        self._resolution_boundary = None
        self._result_format = None
        self.discriminator = None

        if binary_data_base64 is not None:
            self.binary_data_base64 = binary_data_base64
        if enable_hdr is not None:
            self.enable_hdr = enable_hdr
        if enable_wb is not None:
            self.enable_wb = enable_wb
        if hdr_strength is not None:
            self.hdr_strength = hdr_strength
        if image_urls is not None:
            self.image_urls = image_urls
        if jpg_quality is not None:
            self.jpg_quality = jpg_quality
        self.req_key = req_key
        if resolution_boundary is not None:
            self.resolution_boundary = resolution_boundary
        if result_format is not None:
            self.result_format = result_format

    @property
    def binary_data_base64(self):
        """Gets the binary_data_base64 of this LensLqirRequest.  # noqa: E501


        :return: The binary_data_base64 of this LensLqirRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._binary_data_base64

    @binary_data_base64.setter
    def binary_data_base64(self, binary_data_base64):
        """Sets the binary_data_base64 of this LensLqirRequest.


        :param binary_data_base64: The binary_data_base64 of this LensLqirRequest.  # noqa: E501
        :type: list[str]
        """

        self._binary_data_base64 = binary_data_base64

    @property
    def enable_hdr(self):
        """Gets the enable_hdr of this LensLqirRequest.  # noqa: E501


        :return: The enable_hdr of this LensLqirRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_hdr

    @enable_hdr.setter
    def enable_hdr(self, enable_hdr):
        """Sets the enable_hdr of this LensLqirRequest.


        :param enable_hdr: The enable_hdr of this LensLqirRequest.  # noqa: E501
        :type: bool
        """

        self._enable_hdr = enable_hdr

    @property
    def enable_wb(self):
        """Gets the enable_wb of this LensLqirRequest.  # noqa: E501


        :return: The enable_wb of this LensLqirRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_wb

    @enable_wb.setter
    def enable_wb(self, enable_wb):
        """Sets the enable_wb of this LensLqirRequest.


        :param enable_wb: The enable_wb of this LensLqirRequest.  # noqa: E501
        :type: bool
        """

        self._enable_wb = enable_wb

    @property
    def hdr_strength(self):
        """Gets the hdr_strength of this LensLqirRequest.  # noqa: E501


        :return: The hdr_strength of this LensLqirRequest.  # noqa: E501
        :rtype: float
        """
        return self._hdr_strength

    @hdr_strength.setter
    def hdr_strength(self, hdr_strength):
        """Sets the hdr_strength of this LensLqirRequest.


        :param hdr_strength: The hdr_strength of this LensLqirRequest.  # noqa: E501
        :type: float
        """

        self._hdr_strength = hdr_strength

    @property
    def image_urls(self):
        """Gets the image_urls of this LensLqirRequest.  # noqa: E501


        :return: The image_urls of this LensLqirRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._image_urls

    @image_urls.setter
    def image_urls(self, image_urls):
        """Sets the image_urls of this LensLqirRequest.


        :param image_urls: The image_urls of this LensLqirRequest.  # noqa: E501
        :type: list[str]
        """

        self._image_urls = image_urls

    @property
    def jpg_quality(self):
        """Gets the jpg_quality of this LensLqirRequest.  # noqa: E501


        :return: The jpg_quality of this LensLqirRequest.  # noqa: E501
        :rtype: int
        """
        return self._jpg_quality

    @jpg_quality.setter
    def jpg_quality(self, jpg_quality):
        """Sets the jpg_quality of this LensLqirRequest.


        :param jpg_quality: The jpg_quality of this LensLqirRequest.  # noqa: E501
        :type: int
        """

        self._jpg_quality = jpg_quality

    @property
    def req_key(self):
        """Gets the req_key of this LensLqirRequest.  # noqa: E501


        :return: The req_key of this LensLqirRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this LensLqirRequest.


        :param req_key: The req_key of this LensLqirRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    @property
    def resolution_boundary(self):
        """Gets the resolution_boundary of this LensLqirRequest.  # noqa: E501


        :return: The resolution_boundary of this LensLqirRequest.  # noqa: E501
        :rtype: str
        """
        return self._resolution_boundary

    @resolution_boundary.setter
    def resolution_boundary(self, resolution_boundary):
        """Sets the resolution_boundary of this LensLqirRequest.


        :param resolution_boundary: The resolution_boundary of this LensLqirRequest.  # noqa: E501
        :type: str
        """

        self._resolution_boundary = resolution_boundary

    @property
    def result_format(self):
        """Gets the result_format of this LensLqirRequest.  # noqa: E501


        :return: The result_format of this LensLqirRequest.  # noqa: E501
        :rtype: int
        """
        return self._result_format

    @result_format.setter
    def result_format(self, result_format):
        """Sets the result_format of this LensLqirRequest.


        :param result_format: The result_format of this LensLqirRequest.  # noqa: E501
        :type: int
        """

        self._result_format = result_format

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LensLqirRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LensLqirRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LensLqirRequest):
            return True

        return self.to_dict() != other.to_dict()
