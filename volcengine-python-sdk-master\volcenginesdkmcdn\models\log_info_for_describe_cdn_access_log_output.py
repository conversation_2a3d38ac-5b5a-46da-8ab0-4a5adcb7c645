# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LogInfoForDescribeCdnAccessLogOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'file_name': 'str',
        'size': 'int',
        'start_time': 'int',
        'url': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'file_name': 'FileName',
        'size': 'Size',
        'start_time': 'StartTime',
        'url': 'Url'
    }

    def __init__(self, end_time=None, file_name=None, size=None, start_time=None, url=None, _configuration=None):  # noqa: E501
        """LogInfoForDescribeCdnAccessLogOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._file_name = None
        self._size = None
        self._start_time = None
        self._url = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if file_name is not None:
            self.file_name = file_name
        if size is not None:
            self.size = size
        if start_time is not None:
            self.start_time = start_time
        if url is not None:
            self.url = url

    @property
    def end_time(self):
        """Gets the end_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The end_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this LogInfoForDescribeCdnAccessLogOutput.


        :param end_time: The end_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def file_name(self):
        """Gets the file_name of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The file_name of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this LogInfoForDescribeCdnAccessLogOutput.


        :param file_name: The file_name of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def size(self):
        """Gets the size of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The size of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this LogInfoForDescribeCdnAccessLogOutput.


        :param size: The size of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def start_time(self):
        """Gets the start_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The start_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this LogInfoForDescribeCdnAccessLogOutput.


        :param start_time: The start_time of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def url(self):
        """Gets the url of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The url of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this LogInfoForDescribeCdnAccessLogOutput.


        :param url: The url of this LogInfoForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LogInfoForDescribeCdnAccessLogOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LogInfoForDescribeCdnAccessLogOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LogInfoForDescribeCdnAccessLogOutput):
            return True

        return self.to_dict() != other.to_dict()
