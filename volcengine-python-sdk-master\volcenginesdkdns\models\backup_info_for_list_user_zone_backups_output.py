# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackupInfoForListUserZoneBackupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_id': 'str',
        'backup_time': 'str',
        'record_count': 'int'
    }

    attribute_map = {
        'backup_id': 'BackupID',
        'backup_time': 'BackupTime',
        'record_count': 'RecordCount'
    }

    def __init__(self, backup_id=None, backup_time=None, record_count=None, _configuration=None):  # noqa: E501
        """BackupInfoForListUserZoneBackupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_id = None
        self._backup_time = None
        self._record_count = None
        self.discriminator = None

        if backup_id is not None:
            self.backup_id = backup_id
        if backup_time is not None:
            self.backup_time = backup_time
        if record_count is not None:
            self.record_count = record_count

    @property
    def backup_id(self):
        """Gets the backup_id of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501


        :return: The backup_id of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this BackupInfoForListUserZoneBackupsOutput.


        :param backup_id: The backup_id of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def backup_time(self):
        """Gets the backup_time of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501


        :return: The backup_time of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_time

    @backup_time.setter
    def backup_time(self, backup_time):
        """Sets the backup_time of this BackupInfoForListUserZoneBackupsOutput.


        :param backup_time: The backup_time of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :type: str
        """

        self._backup_time = backup_time

    @property
    def record_count(self):
        """Gets the record_count of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501


        :return: The record_count of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._record_count

    @record_count.setter
    def record_count(self, record_count):
        """Sets the record_count of this BackupInfoForListUserZoneBackupsOutput.


        :param record_count: The record_count of this BackupInfoForListUserZoneBackupsOutput.  # noqa: E501
        :type: int
        """

        self._record_count = record_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackupInfoForListUserZoneBackupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackupInfoForListUserZoneBackupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackupInfoForListUserZoneBackupsOutput):
            return True

        return self.to_dict() != other.to_dict()
