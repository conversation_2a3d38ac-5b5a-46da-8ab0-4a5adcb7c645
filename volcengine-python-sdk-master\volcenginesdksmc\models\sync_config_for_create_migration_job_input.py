# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SyncConfigForCreateMigrationJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sync_interval': 'int',
        'sync_times': 'int',
        'sync_type': 'str',
        'trigger_last_sync': 'bool'
    }

    attribute_map = {
        'sync_interval': 'SyncInterval',
        'sync_times': 'SyncTimes',
        'sync_type': 'SyncType',
        'trigger_last_sync': 'TriggerLastSync'
    }

    def __init__(self, sync_interval=None, sync_times=None, sync_type=None, trigger_last_sync=None, _configuration=None):  # noqa: E501
        """SyncConfigForCreateMigrationJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sync_interval = None
        self._sync_times = None
        self._sync_type = None
        self._trigger_last_sync = None
        self.discriminator = None

        if sync_interval is not None:
            self.sync_interval = sync_interval
        if sync_times is not None:
            self.sync_times = sync_times
        if sync_type is not None:
            self.sync_type = sync_type
        if trigger_last_sync is not None:
            self.trigger_last_sync = trigger_last_sync

    @property
    def sync_interval(self):
        """Gets the sync_interval of this SyncConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The sync_interval of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: int
        """
        return self._sync_interval

    @sync_interval.setter
    def sync_interval(self, sync_interval):
        """Sets the sync_interval of this SyncConfigForCreateMigrationJobInput.


        :param sync_interval: The sync_interval of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :type: int
        """

        self._sync_interval = sync_interval

    @property
    def sync_times(self):
        """Gets the sync_times of this SyncConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The sync_times of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: int
        """
        return self._sync_times

    @sync_times.setter
    def sync_times(self, sync_times):
        """Sets the sync_times of this SyncConfigForCreateMigrationJobInput.


        :param sync_times: The sync_times of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :type: int
        """

        self._sync_times = sync_times

    @property
    def sync_type(self):
        """Gets the sync_type of this SyncConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The sync_type of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: str
        """
        return self._sync_type

    @sync_type.setter
    def sync_type(self, sync_type):
        """Sets the sync_type of this SyncConfigForCreateMigrationJobInput.


        :param sync_type: The sync_type of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :type: str
        """

        self._sync_type = sync_type

    @property
    def trigger_last_sync(self):
        """Gets the trigger_last_sync of this SyncConfigForCreateMigrationJobInput.  # noqa: E501


        :return: The trigger_last_sync of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :rtype: bool
        """
        return self._trigger_last_sync

    @trigger_last_sync.setter
    def trigger_last_sync(self, trigger_last_sync):
        """Sets the trigger_last_sync of this SyncConfigForCreateMigrationJobInput.


        :param trigger_last_sync: The trigger_last_sync of this SyncConfigForCreateMigrationJobInput.  # noqa: E501
        :type: bool
        """

        self._trigger_last_sync = trigger_last_sync

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SyncConfigForCreateMigrationJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SyncConfigForCreateMigrationJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SyncConfigForCreateMigrationJobInput):
            return True

        return self.to_dict() != other.to_dict()
