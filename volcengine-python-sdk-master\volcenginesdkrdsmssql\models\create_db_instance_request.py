# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_list_ids': 'list[str]',
        'charge_info': 'ChargeInfoForCreateDBInstanceInput',
        'db_engine_version': 'str',
        'db_time_zone': 'str',
        'instance_name': 'str',
        'instance_type': 'str',
        'maintenance_time': 'str',
        'node_spec': 'str',
        'project_name': 'str',
        'server_collation': 'str',
        'storage_space': 'int',
        'subnet_id': 'str',
        'super_account_password': 'str',
        'tags': 'list[TagForCreateDBInstanceInput]',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'allow_list_ids': 'AllowListIds',
        'charge_info': 'ChargeInfo',
        'db_engine_version': 'DBEngineVersion',
        'db_time_zone': 'DBTimeZone',
        'instance_name': 'InstanceName',
        'instance_type': 'InstanceType',
        'maintenance_time': 'MaintenanceTime',
        'node_spec': 'NodeSpec',
        'project_name': 'ProjectName',
        'server_collation': 'ServerCollation',
        'storage_space': 'StorageSpace',
        'subnet_id': 'SubnetId',
        'super_account_password': 'SuperAccountPassword',
        'tags': 'Tags',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, allow_list_ids=None, charge_info=None, db_engine_version=None, db_time_zone=None, instance_name=None, instance_type=None, maintenance_time=None, node_spec=None, project_name=None, server_collation=None, storage_space=None, subnet_id=None, super_account_password=None, tags=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """CreateDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_list_ids = None
        self._charge_info = None
        self._db_engine_version = None
        self._db_time_zone = None
        self._instance_name = None
        self._instance_type = None
        self._maintenance_time = None
        self._node_spec = None
        self._project_name = None
        self._server_collation = None
        self._storage_space = None
        self._subnet_id = None
        self._super_account_password = None
        self._tags = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if allow_list_ids is not None:
            self.allow_list_ids = allow_list_ids
        if charge_info is not None:
            self.charge_info = charge_info
        self.db_engine_version = db_engine_version
        self.db_time_zone = db_time_zone
        if instance_name is not None:
            self.instance_name = instance_name
        self.instance_type = instance_type
        if maintenance_time is not None:
            self.maintenance_time = maintenance_time
        self.node_spec = node_spec
        if project_name is not None:
            self.project_name = project_name
        if server_collation is not None:
            self.server_collation = server_collation
        self.storage_space = storage_space
        self.subnet_id = subnet_id
        self.super_account_password = super_account_password
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id
        self.zone_id = zone_id

    @property
    def allow_list_ids(self):
        """Gets the allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501


        :return: The allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allow_list_ids

    @allow_list_ids.setter
    def allow_list_ids(self, allow_list_ids):
        """Sets the allow_list_ids of this CreateDBInstanceRequest.


        :param allow_list_ids: The allow_list_ids of this CreateDBInstanceRequest.  # noqa: E501
        :type: list[str]
        """

        self._allow_list_ids = allow_list_ids

    @property
    def charge_info(self):
        """Gets the charge_info of this CreateDBInstanceRequest.  # noqa: E501


        :return: The charge_info of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: ChargeInfoForCreateDBInstanceInput
        """
        return self._charge_info

    @charge_info.setter
    def charge_info(self, charge_info):
        """Sets the charge_info of this CreateDBInstanceRequest.


        :param charge_info: The charge_info of this CreateDBInstanceRequest.  # noqa: E501
        :type: ChargeInfoForCreateDBInstanceInput
        """

        self._charge_info = charge_info

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this CreateDBInstanceRequest.  # noqa: E501


        :return: The db_engine_version of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this CreateDBInstanceRequest.


        :param db_engine_version: The db_engine_version of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and db_engine_version is None:
            raise ValueError("Invalid value for `db_engine_version`, must not be `None`")  # noqa: E501

        self._db_engine_version = db_engine_version

    @property
    def db_time_zone(self):
        """Gets the db_time_zone of this CreateDBInstanceRequest.  # noqa: E501


        :return: The db_time_zone of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_time_zone

    @db_time_zone.setter
    def db_time_zone(self, db_time_zone):
        """Sets the db_time_zone of this CreateDBInstanceRequest.


        :param db_time_zone: The db_time_zone of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and db_time_zone is None:
            raise ValueError("Invalid value for `db_time_zone`, must not be `None`")  # noqa: E501

        self._db_time_zone = db_time_zone

    @property
    def instance_name(self):
        """Gets the instance_name of this CreateDBInstanceRequest.  # noqa: E501


        :return: The instance_name of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this CreateDBInstanceRequest.


        :param instance_name: The instance_name of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_type(self):
        """Gets the instance_type of this CreateDBInstanceRequest.  # noqa: E501


        :return: The instance_type of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this CreateDBInstanceRequest.


        :param instance_type: The instance_type of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_type is None:
            raise ValueError("Invalid value for `instance_type`, must not be `None`")  # noqa: E501

        self._instance_type = instance_type

    @property
    def maintenance_time(self):
        """Gets the maintenance_time of this CreateDBInstanceRequest.  # noqa: E501


        :return: The maintenance_time of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._maintenance_time

    @maintenance_time.setter
    def maintenance_time(self, maintenance_time):
        """Sets the maintenance_time of this CreateDBInstanceRequest.


        :param maintenance_time: The maintenance_time of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._maintenance_time = maintenance_time

    @property
    def node_spec(self):
        """Gets the node_spec of this CreateDBInstanceRequest.  # noqa: E501


        :return: The node_spec of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_spec

    @node_spec.setter
    def node_spec(self, node_spec):
        """Sets the node_spec of this CreateDBInstanceRequest.


        :param node_spec: The node_spec of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_spec is None:
            raise ValueError("Invalid value for `node_spec`, must not be `None`")  # noqa: E501

        self._node_spec = node_spec

    @property
    def project_name(self):
        """Gets the project_name of this CreateDBInstanceRequest.  # noqa: E501


        :return: The project_name of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateDBInstanceRequest.


        :param project_name: The project_name of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def server_collation(self):
        """Gets the server_collation of this CreateDBInstanceRequest.  # noqa: E501


        :return: The server_collation of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_collation

    @server_collation.setter
    def server_collation(self, server_collation):
        """Sets the server_collation of this CreateDBInstanceRequest.


        :param server_collation: The server_collation of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._server_collation = server_collation

    @property
    def storage_space(self):
        """Gets the storage_space of this CreateDBInstanceRequest.  # noqa: E501


        :return: The storage_space of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this CreateDBInstanceRequest.


        :param storage_space: The storage_space of this CreateDBInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and storage_space is None:
            raise ValueError("Invalid value for `storage_space`, must not be `None`")  # noqa: E501

        self._storage_space = storage_space

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The subnet_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateDBInstanceRequest.


        :param subnet_id: The subnet_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def super_account_password(self):
        """Gets the super_account_password of this CreateDBInstanceRequest.  # noqa: E501


        :return: The super_account_password of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._super_account_password

    @super_account_password.setter
    def super_account_password(self, super_account_password):
        """Sets the super_account_password of this CreateDBInstanceRequest.


        :param super_account_password: The super_account_password of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and super_account_password is None:
            raise ValueError("Invalid value for `super_account_password`, must not be `None`")  # noqa: E501

        self._super_account_password = super_account_password

    @property
    def tags(self):
        """Gets the tags of this CreateDBInstanceRequest.  # noqa: E501


        :return: The tags of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: list[TagForCreateDBInstanceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateDBInstanceRequest.


        :param tags: The tags of this CreateDBInstanceRequest.  # noqa: E501
        :type: list[TagForCreateDBInstanceInput]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The vpc_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateDBInstanceRequest.


        :param vpc_id: The vpc_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this CreateDBInstanceRequest.  # noqa: E501


        :return: The zone_id of this CreateDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this CreateDBInstanceRequest.


        :param zone_id: The zone_id of this CreateDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_id is None:
            raise ValueError("Invalid value for `zone_id`, must not be `None`")  # noqa: E501

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
