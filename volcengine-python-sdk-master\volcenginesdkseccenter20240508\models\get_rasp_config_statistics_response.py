# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRaspConfigStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'process_white_list_count': 'int',
        'rasp_close_count': 'int'
    }

    attribute_map = {
        'process_white_list_count': 'ProcessWhiteListCount',
        'rasp_close_count': 'RaspCloseCount'
    }

    def __init__(self, process_white_list_count=None, rasp_close_count=None, _configuration=None):  # noqa: E501
        """GetRaspConfigStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._process_white_list_count = None
        self._rasp_close_count = None
        self.discriminator = None

        if process_white_list_count is not None:
            self.process_white_list_count = process_white_list_count
        if rasp_close_count is not None:
            self.rasp_close_count = rasp_close_count

    @property
    def process_white_list_count(self):
        """Gets the process_white_list_count of this GetRaspConfigStatisticsResponse.  # noqa: E501


        :return: The process_white_list_count of this GetRaspConfigStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._process_white_list_count

    @process_white_list_count.setter
    def process_white_list_count(self, process_white_list_count):
        """Sets the process_white_list_count of this GetRaspConfigStatisticsResponse.


        :param process_white_list_count: The process_white_list_count of this GetRaspConfigStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._process_white_list_count = process_white_list_count

    @property
    def rasp_close_count(self):
        """Gets the rasp_close_count of this GetRaspConfigStatisticsResponse.  # noqa: E501


        :return: The rasp_close_count of this GetRaspConfigStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._rasp_close_count

    @rasp_close_count.setter
    def rasp_close_count(self, rasp_close_count):
        """Sets the rasp_close_count of this GetRaspConfigStatisticsResponse.


        :param rasp_close_count: The rasp_close_count of this GetRaspConfigStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._rasp_close_count = rasp_close_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRaspConfigStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRaspConfigStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRaspConfigStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
