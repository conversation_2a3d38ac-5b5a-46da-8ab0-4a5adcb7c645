# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddCustomRouteRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_route_name': 'str',
        'dst_ip': 'str',
        'product_id': 'str',
        'proxy_cipher': 'str',
        'proxy_password': 'str',
        'proxy_port': 'int',
        'proxy_protocol': 'str',
        'proxy_user_name': 'str',
        'zone': 'str'
    }

    attribute_map = {
        'custom_route_name': 'CustomRouteName',
        'dst_ip': 'DstIP',
        'product_id': 'ProductId',
        'proxy_cipher': 'ProxyCipher',
        'proxy_password': 'ProxyPassword',
        'proxy_port': 'ProxyPort',
        'proxy_protocol': 'ProxyProtocol',
        'proxy_user_name': 'ProxyUserName',
        'zone': 'Zone'
    }

    def __init__(self, custom_route_name=None, dst_ip=None, product_id=None, proxy_cipher=None, proxy_password=None, proxy_port=None, proxy_protocol=None, proxy_user_name=None, zone=None, _configuration=None):  # noqa: E501
        """AddCustomRouteRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_route_name = None
        self._dst_ip = None
        self._product_id = None
        self._proxy_cipher = None
        self._proxy_password = None
        self._proxy_port = None
        self._proxy_protocol = None
        self._proxy_user_name = None
        self._zone = None
        self.discriminator = None

        if custom_route_name is not None:
            self.custom_route_name = custom_route_name
        self.dst_ip = dst_ip
        self.product_id = product_id
        if proxy_cipher is not None:
            self.proxy_cipher = proxy_cipher
        if proxy_password is not None:
            self.proxy_password = proxy_password
        self.proxy_port = proxy_port
        self.proxy_protocol = proxy_protocol
        if proxy_user_name is not None:
            self.proxy_user_name = proxy_user_name
        self.zone = zone

    @property
    def custom_route_name(self):
        """Gets the custom_route_name of this AddCustomRouteRequest.  # noqa: E501


        :return: The custom_route_name of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_name

    @custom_route_name.setter
    def custom_route_name(self, custom_route_name):
        """Sets the custom_route_name of this AddCustomRouteRequest.


        :param custom_route_name: The custom_route_name of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._custom_route_name = custom_route_name

    @property
    def dst_ip(self):
        """Gets the dst_ip of this AddCustomRouteRequest.  # noqa: E501


        :return: The dst_ip of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._dst_ip

    @dst_ip.setter
    def dst_ip(self, dst_ip):
        """Sets the dst_ip of this AddCustomRouteRequest.


        :param dst_ip: The dst_ip of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and dst_ip is None:
            raise ValueError("Invalid value for `dst_ip`, must not be `None`")  # noqa: E501

        self._dst_ip = dst_ip

    @property
    def product_id(self):
        """Gets the product_id of this AddCustomRouteRequest.  # noqa: E501


        :return: The product_id of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this AddCustomRouteRequest.


        :param product_id: The product_id of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product_id is None:
            raise ValueError("Invalid value for `product_id`, must not be `None`")  # noqa: E501

        self._product_id = product_id

    @property
    def proxy_cipher(self):
        """Gets the proxy_cipher of this AddCustomRouteRequest.  # noqa: E501


        :return: The proxy_cipher of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_cipher

    @proxy_cipher.setter
    def proxy_cipher(self, proxy_cipher):
        """Sets the proxy_cipher of this AddCustomRouteRequest.


        :param proxy_cipher: The proxy_cipher of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._proxy_cipher = proxy_cipher

    @property
    def proxy_password(self):
        """Gets the proxy_password of this AddCustomRouteRequest.  # noqa: E501


        :return: The proxy_password of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_password

    @proxy_password.setter
    def proxy_password(self, proxy_password):
        """Sets the proxy_password of this AddCustomRouteRequest.


        :param proxy_password: The proxy_password of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._proxy_password = proxy_password

    @property
    def proxy_port(self):
        """Gets the proxy_port of this AddCustomRouteRequest.  # noqa: E501


        :return: The proxy_port of this AddCustomRouteRequest.  # noqa: E501
        :rtype: int
        """
        return self._proxy_port

    @proxy_port.setter
    def proxy_port(self, proxy_port):
        """Sets the proxy_port of this AddCustomRouteRequest.


        :param proxy_port: The proxy_port of this AddCustomRouteRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and proxy_port is None:
            raise ValueError("Invalid value for `proxy_port`, must not be `None`")  # noqa: E501

        self._proxy_port = proxy_port

    @property
    def proxy_protocol(self):
        """Gets the proxy_protocol of this AddCustomRouteRequest.  # noqa: E501


        :return: The proxy_protocol of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_protocol

    @proxy_protocol.setter
    def proxy_protocol(self, proxy_protocol):
        """Sets the proxy_protocol of this AddCustomRouteRequest.


        :param proxy_protocol: The proxy_protocol of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and proxy_protocol is None:
            raise ValueError("Invalid value for `proxy_protocol`, must not be `None`")  # noqa: E501

        self._proxy_protocol = proxy_protocol

    @property
    def proxy_user_name(self):
        """Gets the proxy_user_name of this AddCustomRouteRequest.  # noqa: E501


        :return: The proxy_user_name of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_user_name

    @proxy_user_name.setter
    def proxy_user_name(self, proxy_user_name):
        """Sets the proxy_user_name of this AddCustomRouteRequest.


        :param proxy_user_name: The proxy_user_name of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """

        self._proxy_user_name = proxy_user_name

    @property
    def zone(self):
        """Gets the zone of this AddCustomRouteRequest.  # noqa: E501


        :return: The zone of this AddCustomRouteRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this AddCustomRouteRequest.


        :param zone: The zone of this AddCustomRouteRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone is None:
            raise ValueError("Invalid value for `zone`, must not be `None`")  # noqa: E501

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddCustomRouteRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddCustomRouteRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddCustomRouteRequest):
            return True

        return self.to_dict() != other.to_dict()
