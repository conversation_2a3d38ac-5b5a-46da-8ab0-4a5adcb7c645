# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creator': 'CreatorForGetVideoTrafficPayDataOutput',
        'vid': 'str',
        'vod_traffic': 'str'
    }

    attribute_map = {
        'creator': 'Creator',
        'vid': 'Vid',
        'vod_traffic': 'VodTraffic'
    }

    def __init__(self, creator=None, vid=None, vod_traffic=None, _configuration=None):  # noqa: E501
        """PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creator = None
        self._vid = None
        self._vod_traffic = None
        self.discriminator = None

        if creator is not None:
            self.creator = creator
        if vid is not None:
            self.vid = vid
        if vod_traffic is not None:
            self.vod_traffic = vod_traffic

    @property
    def creator(self):
        """Gets the creator of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501


        :return: The creator of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :rtype: CreatorForGetVideoTrafficPayDataOutput
        """
        return self._creator

    @creator.setter
    def creator(self, creator):
        """Sets the creator of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.


        :param creator: The creator of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :type: CreatorForGetVideoTrafficPayDataOutput
        """

        self._creator = creator

    @property
    def vid(self):
        """Gets the vid of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501


        :return: The vid of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.


        :param vid: The vid of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :type: str
        """

        self._vid = vid

    @property
    def vod_traffic(self):
        """Gets the vod_traffic of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501


        :return: The vod_traffic of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :rtype: str
        """
        return self._vod_traffic

    @vod_traffic.setter
    def vod_traffic(self, vod_traffic):
        """Sets the vod_traffic of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.


        :param vod_traffic: The vod_traffic of this PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput.  # noqa: E501
        :type: str
        """

        self._vod_traffic = vod_traffic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput):
            return True

        return self.to_dict() != other.to_dict()
