# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDXPConstructionLOAResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'loadata': 'str',
        'loa_name': 'str'
    }

    attribute_map = {
        'loadata': 'LOADATA',
        'loa_name': 'LOAName'
    }

    def __init__(self, loadata=None, loa_name=None, _configuration=None):  # noqa: E501
        """GetDXPConstructionLOAResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._loadata = None
        self._loa_name = None
        self.discriminator = None

        if loadata is not None:
            self.loadata = loadata
        if loa_name is not None:
            self.loa_name = loa_name

    @property
    def loadata(self):
        """Gets the loadata of this GetDXPConstructionLOAResponse.  # noqa: E501


        :return: The loadata of this GetDXPConstructionLOAResponse.  # noqa: E501
        :rtype: str
        """
        return self._loadata

    @loadata.setter
    def loadata(self, loadata):
        """Sets the loadata of this GetDXPConstructionLOAResponse.


        :param loadata: The loadata of this GetDXPConstructionLOAResponse.  # noqa: E501
        :type: str
        """

        self._loadata = loadata

    @property
    def loa_name(self):
        """Gets the loa_name of this GetDXPConstructionLOAResponse.  # noqa: E501


        :return: The loa_name of this GetDXPConstructionLOAResponse.  # noqa: E501
        :rtype: str
        """
        return self._loa_name

    @loa_name.setter
    def loa_name(self, loa_name):
        """Sets the loa_name of this GetDXPConstructionLOAResponse.


        :param loa_name: The loa_name of this GetDXPConstructionLOAResponse.  # noqa: E501
        :type: str
        """

        self._loa_name = loa_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDXPConstructionLOAResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDXPConstructionLOAResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDXPConstructionLOAResponse):
            return True

        return self.to_dict() != other.to_dict()
