# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointInfoForDescribeDiagnosticsInfosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_id': 'str',
        'endpoint_name': 'str',
        'priave_domain': 'str',
        'private_ipv4_address': 'str',
        'private_ipv6_address': 'str',
        'public_domain': 'str',
        'public_ip_address': 'str',
        'vpc_id': 'str',
        'vpc_name': 'str'
    }

    attribute_map = {
        'endpoint_id': 'EndpointId',
        'endpoint_name': 'EndpointName',
        'priave_domain': 'PriaveDomain',
        'private_ipv4_address': 'PrivateIpv4Address',
        'private_ipv6_address': 'PrivateIpv6Address',
        'public_domain': 'PublicDomain',
        'public_ip_address': 'PublicIpAddress',
        'vpc_id': 'VpcId',
        'vpc_name': 'VpcName'
    }

    def __init__(self, endpoint_id=None, endpoint_name=None, priave_domain=None, private_ipv4_address=None, private_ipv6_address=None, public_domain=None, public_ip_address=None, vpc_id=None, vpc_name=None, _configuration=None):  # noqa: E501
        """EndpointInfoForDescribeDiagnosticsInfosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_id = None
        self._endpoint_name = None
        self._priave_domain = None
        self._private_ipv4_address = None
        self._private_ipv6_address = None
        self._public_domain = None
        self._public_ip_address = None
        self._vpc_id = None
        self._vpc_name = None
        self.discriminator = None

        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        if priave_domain is not None:
            self.priave_domain = priave_domain
        if private_ipv4_address is not None:
            self.private_ipv4_address = private_ipv4_address
        if private_ipv6_address is not None:
            self.private_ipv6_address = private_ipv6_address
        if public_domain is not None:
            self.public_domain = public_domain
        if public_ip_address is not None:
            self.public_ip_address = public_ip_address
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_name is not None:
            self.vpc_name = vpc_name

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The endpoint_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param endpoint_id: The endpoint_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The endpoint_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param endpoint_name: The endpoint_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def priave_domain(self):
        """Gets the priave_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The priave_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._priave_domain

    @priave_domain.setter
    def priave_domain(self, priave_domain):
        """Sets the priave_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param priave_domain: The priave_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._priave_domain = priave_domain

    @property
    def private_ipv4_address(self):
        """Gets the private_ipv4_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The private_ipv4_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ipv4_address

    @private_ipv4_address.setter
    def private_ipv4_address(self, private_ipv4_address):
        """Sets the private_ipv4_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param private_ipv4_address: The private_ipv4_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._private_ipv4_address = private_ipv4_address

    @property
    def private_ipv6_address(self):
        """Gets the private_ipv6_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The private_ipv6_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ipv6_address

    @private_ipv6_address.setter
    def private_ipv6_address(self, private_ipv6_address):
        """Sets the private_ipv6_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param private_ipv6_address: The private_ipv6_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._private_ipv6_address = private_ipv6_address

    @property
    def public_domain(self):
        """Gets the public_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The public_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_domain

    @public_domain.setter
    def public_domain(self, public_domain):
        """Sets the public_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param public_domain: The public_domain of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._public_domain = public_domain

    @property
    def public_ip_address(self):
        """Gets the public_ip_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The public_ip_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip_address

    @public_ip_address.setter
    def public_ip_address(self, public_ip_address):
        """Sets the public_ip_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param public_ip_address: The public_ip_address of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._public_ip_address = public_ip_address

    @property
    def vpc_id(self):
        """Gets the vpc_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The vpc_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param vpc_id: The vpc_id of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_name(self):
        """Gets the vpc_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The vpc_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_name

    @vpc_name.setter
    def vpc_name(self, vpc_name):
        """Sets the vpc_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.


        :param vpc_name: The vpc_name of this EndpointInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._vpc_name = vpc_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointInfoForDescribeDiagnosticsInfosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointInfoForDescribeDiagnosticsInfosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointInfoForDescribeDiagnosticsInfosOutput):
            return True

        return self.to_dict() != other.to_dict()
