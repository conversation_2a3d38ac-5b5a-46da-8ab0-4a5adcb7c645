# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCrossAccountVIFAuthorityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'state': 'str',
        'vgw_account_id': 'int',
        'vif_instance_id': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'state': 'State',
        'vgw_account_id': 'VGWAccountID',
        'vif_instance_id': 'VIFInstanceID'
    }

    def __init__(self, page_number=None, page_size=None, state=None, vgw_account_id=None, vif_instance_id=None, _configuration=None):  # noqa: E501
        """ListCrossAccountVIFAuthorityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._state = None
        self._vgw_account_id = None
        self._vif_instance_id = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if state is not None:
            self.state = state
        if vgw_account_id is not None:
            self.vgw_account_id = vgw_account_id
        if vif_instance_id is not None:
            self.vif_instance_id = vif_instance_id

    @property
    def page_number(self):
        """Gets the page_number of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The page_number of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListCrossAccountVIFAuthorityRequest.


        :param page_number: The page_number of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The page_size of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCrossAccountVIFAuthorityRequest.


        :param page_size: The page_size of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def state(self):
        """Gets the state of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The state of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListCrossAccountVIFAuthorityRequest.


        :param state: The state of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def vgw_account_id(self):
        """Gets the vgw_account_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The vgw_account_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: int
        """
        return self._vgw_account_id

    @vgw_account_id.setter
    def vgw_account_id(self, vgw_account_id):
        """Sets the vgw_account_id of this ListCrossAccountVIFAuthorityRequest.


        :param vgw_account_id: The vgw_account_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: int
        """

        self._vgw_account_id = vgw_account_id

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The vif_instance_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this ListCrossAccountVIFAuthorityRequest.


        :param vif_instance_id: The vif_instance_id of this ListCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: str
        """

        self._vif_instance_id = vif_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCrossAccountVIFAuthorityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCrossAccountVIFAuthorityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCrossAccountVIFAuthorityRequest):
            return True

        return self.to_dict() != other.to_dict()
