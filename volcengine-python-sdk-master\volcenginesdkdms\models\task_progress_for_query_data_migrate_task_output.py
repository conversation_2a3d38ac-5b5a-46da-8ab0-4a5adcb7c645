# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskProgressForQueryDataMigrateTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'failed_bytes': 'int',
        'failed_objects': 'int',
        'not_exist_bytes': 'int',
        'not_exist_object_count': 'int',
        'remaining_bytes': 'int',
        'remaining_objects': 'int',
        'skip_bytes': 'int',
        'skip_object_count': 'int',
        'total_bytes': 'int',
        'total_objects': 'int',
        'transfer_bytes_speed': 'int',
        'transfer_count_speed': 'int',
        'transferred_bytes': 'int',
        'transferred_objects': 'int'
    }

    attribute_map = {
        'failed_bytes': 'FailedBytes',
        'failed_objects': 'FailedObjects',
        'not_exist_bytes': 'NotExistBytes',
        'not_exist_object_count': 'NotExistObjectCount',
        'remaining_bytes': 'RemainingBytes',
        'remaining_objects': 'RemainingObjects',
        'skip_bytes': 'SkipBytes',
        'skip_object_count': 'SkipObjectCount',
        'total_bytes': 'TotalBytes',
        'total_objects': 'TotalObjects',
        'transfer_bytes_speed': 'TransferBytesSpeed',
        'transfer_count_speed': 'TransferCountSpeed',
        'transferred_bytes': 'TransferredBytes',
        'transferred_objects': 'TransferredObjects'
    }

    def __init__(self, failed_bytes=None, failed_objects=None, not_exist_bytes=None, not_exist_object_count=None, remaining_bytes=None, remaining_objects=None, skip_bytes=None, skip_object_count=None, total_bytes=None, total_objects=None, transfer_bytes_speed=None, transfer_count_speed=None, transferred_bytes=None, transferred_objects=None, _configuration=None):  # noqa: E501
        """TaskProgressForQueryDataMigrateTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._failed_bytes = None
        self._failed_objects = None
        self._not_exist_bytes = None
        self._not_exist_object_count = None
        self._remaining_bytes = None
        self._remaining_objects = None
        self._skip_bytes = None
        self._skip_object_count = None
        self._total_bytes = None
        self._total_objects = None
        self._transfer_bytes_speed = None
        self._transfer_count_speed = None
        self._transferred_bytes = None
        self._transferred_objects = None
        self.discriminator = None

        if failed_bytes is not None:
            self.failed_bytes = failed_bytes
        if failed_objects is not None:
            self.failed_objects = failed_objects
        if not_exist_bytes is not None:
            self.not_exist_bytes = not_exist_bytes
        if not_exist_object_count is not None:
            self.not_exist_object_count = not_exist_object_count
        if remaining_bytes is not None:
            self.remaining_bytes = remaining_bytes
        if remaining_objects is not None:
            self.remaining_objects = remaining_objects
        if skip_bytes is not None:
            self.skip_bytes = skip_bytes
        if skip_object_count is not None:
            self.skip_object_count = skip_object_count
        if total_bytes is not None:
            self.total_bytes = total_bytes
        if total_objects is not None:
            self.total_objects = total_objects
        if transfer_bytes_speed is not None:
            self.transfer_bytes_speed = transfer_bytes_speed
        if transfer_count_speed is not None:
            self.transfer_count_speed = transfer_count_speed
        if transferred_bytes is not None:
            self.transferred_bytes = transferred_bytes
        if transferred_objects is not None:
            self.transferred_objects = transferred_objects

    @property
    def failed_bytes(self):
        """Gets the failed_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The failed_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._failed_bytes

    @failed_bytes.setter
    def failed_bytes(self, failed_bytes):
        """Sets the failed_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param failed_bytes: The failed_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._failed_bytes = failed_bytes

    @property
    def failed_objects(self):
        """Gets the failed_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The failed_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._failed_objects

    @failed_objects.setter
    def failed_objects(self, failed_objects):
        """Sets the failed_objects of this TaskProgressForQueryDataMigrateTaskOutput.


        :param failed_objects: The failed_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._failed_objects = failed_objects

    @property
    def not_exist_bytes(self):
        """Gets the not_exist_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The not_exist_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._not_exist_bytes

    @not_exist_bytes.setter
    def not_exist_bytes(self, not_exist_bytes):
        """Sets the not_exist_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param not_exist_bytes: The not_exist_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._not_exist_bytes = not_exist_bytes

    @property
    def not_exist_object_count(self):
        """Gets the not_exist_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The not_exist_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._not_exist_object_count

    @not_exist_object_count.setter
    def not_exist_object_count(self, not_exist_object_count):
        """Sets the not_exist_object_count of this TaskProgressForQueryDataMigrateTaskOutput.


        :param not_exist_object_count: The not_exist_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._not_exist_object_count = not_exist_object_count

    @property
    def remaining_bytes(self):
        """Gets the remaining_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The remaining_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._remaining_bytes

    @remaining_bytes.setter
    def remaining_bytes(self, remaining_bytes):
        """Sets the remaining_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param remaining_bytes: The remaining_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._remaining_bytes = remaining_bytes

    @property
    def remaining_objects(self):
        """Gets the remaining_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The remaining_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._remaining_objects

    @remaining_objects.setter
    def remaining_objects(self, remaining_objects):
        """Sets the remaining_objects of this TaskProgressForQueryDataMigrateTaskOutput.


        :param remaining_objects: The remaining_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._remaining_objects = remaining_objects

    @property
    def skip_bytes(self):
        """Gets the skip_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The skip_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._skip_bytes

    @skip_bytes.setter
    def skip_bytes(self, skip_bytes):
        """Sets the skip_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param skip_bytes: The skip_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._skip_bytes = skip_bytes

    @property
    def skip_object_count(self):
        """Gets the skip_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The skip_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._skip_object_count

    @skip_object_count.setter
    def skip_object_count(self, skip_object_count):
        """Sets the skip_object_count of this TaskProgressForQueryDataMigrateTaskOutput.


        :param skip_object_count: The skip_object_count of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._skip_object_count = skip_object_count

    @property
    def total_bytes(self):
        """Gets the total_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The total_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_bytes

    @total_bytes.setter
    def total_bytes(self, total_bytes):
        """Sets the total_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param total_bytes: The total_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._total_bytes = total_bytes

    @property
    def total_objects(self):
        """Gets the total_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The total_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_objects

    @total_objects.setter
    def total_objects(self, total_objects):
        """Sets the total_objects of this TaskProgressForQueryDataMigrateTaskOutput.


        :param total_objects: The total_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._total_objects = total_objects

    @property
    def transfer_bytes_speed(self):
        """Gets the transfer_bytes_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The transfer_bytes_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._transfer_bytes_speed

    @transfer_bytes_speed.setter
    def transfer_bytes_speed(self, transfer_bytes_speed):
        """Sets the transfer_bytes_speed of this TaskProgressForQueryDataMigrateTaskOutput.


        :param transfer_bytes_speed: The transfer_bytes_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._transfer_bytes_speed = transfer_bytes_speed

    @property
    def transfer_count_speed(self):
        """Gets the transfer_count_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The transfer_count_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._transfer_count_speed

    @transfer_count_speed.setter
    def transfer_count_speed(self, transfer_count_speed):
        """Sets the transfer_count_speed of this TaskProgressForQueryDataMigrateTaskOutput.


        :param transfer_count_speed: The transfer_count_speed of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._transfer_count_speed = transfer_count_speed

    @property
    def transferred_bytes(self):
        """Gets the transferred_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The transferred_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._transferred_bytes

    @transferred_bytes.setter
    def transferred_bytes(self, transferred_bytes):
        """Sets the transferred_bytes of this TaskProgressForQueryDataMigrateTaskOutput.


        :param transferred_bytes: The transferred_bytes of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._transferred_bytes = transferred_bytes

    @property
    def transferred_objects(self):
        """Gets the transferred_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The transferred_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._transferred_objects

    @transferred_objects.setter
    def transferred_objects(self, transferred_objects):
        """Sets the transferred_objects of this TaskProgressForQueryDataMigrateTaskOutput.


        :param transferred_objects: The transferred_objects of this TaskProgressForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: int
        """

        self._transferred_objects = transferred_objects

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskProgressForQueryDataMigrateTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskProgressForQueryDataMigrateTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskProgressForQueryDataMigrateTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
