# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListResourceReservationPlansOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'creator_trn': 'str',
        'description': 'str',
        'desired_compute_resource': 'DesiredComputeResourceForListResourceReservationPlansOutput',
        'id': 'str',
        'name': 'str',
        'reservation_config': 'ReservationConfigForListResourceReservationPlansOutput',
        'status': 'StatusForListResourceReservationPlansOutput',
        'storage_config': 'StorageConfigForListResourceReservationPlansOutput',
        'update_time': 'str',
        'workload_network_config': 'WorkloadNetworkConfigForListResourceReservationPlansOutput'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'creator_trn': 'CreatorTrn',
        'description': 'Description',
        'desired_compute_resource': 'DesiredComputeResource',
        'id': 'Id',
        'name': 'Name',
        'reservation_config': 'ReservationConfig',
        'status': 'Status',
        'storage_config': 'StorageConfig',
        'update_time': 'UpdateTime',
        'workload_network_config': 'WorkloadNetworkConfig'
    }

    def __init__(self, create_time=None, creator_trn=None, description=None, desired_compute_resource=None, id=None, name=None, reservation_config=None, status=None, storage_config=None, update_time=None, workload_network_config=None, _configuration=None):  # noqa: E501
        """ItemForListResourceReservationPlansOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._creator_trn = None
        self._description = None
        self._desired_compute_resource = None
        self._id = None
        self._name = None
        self._reservation_config = None
        self._status = None
        self._storage_config = None
        self._update_time = None
        self._workload_network_config = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if creator_trn is not None:
            self.creator_trn = creator_trn
        if description is not None:
            self.description = description
        if desired_compute_resource is not None:
            self.desired_compute_resource = desired_compute_resource
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if reservation_config is not None:
            self.reservation_config = reservation_config
        if status is not None:
            self.status = status
        if storage_config is not None:
            self.storage_config = storage_config
        if update_time is not None:
            self.update_time = update_time
        if workload_network_config is not None:
            self.workload_network_config = workload_network_config

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The create_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListResourceReservationPlansOutput.


        :param create_time: The create_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def creator_trn(self):
        """Gets the creator_trn of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The creator_trn of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._creator_trn

    @creator_trn.setter
    def creator_trn(self, creator_trn):
        """Sets the creator_trn of this ItemForListResourceReservationPlansOutput.


        :param creator_trn: The creator_trn of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._creator_trn = creator_trn

    @property
    def description(self):
        """Gets the description of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The description of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListResourceReservationPlansOutput.


        :param description: The description of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def desired_compute_resource(self):
        """Gets the desired_compute_resource of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The desired_compute_resource of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: DesiredComputeResourceForListResourceReservationPlansOutput
        """
        return self._desired_compute_resource

    @desired_compute_resource.setter
    def desired_compute_resource(self, desired_compute_resource):
        """Sets the desired_compute_resource of this ItemForListResourceReservationPlansOutput.


        :param desired_compute_resource: The desired_compute_resource of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: DesiredComputeResourceForListResourceReservationPlansOutput
        """

        self._desired_compute_resource = desired_compute_resource

    @property
    def id(self):
        """Gets the id of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The id of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListResourceReservationPlansOutput.


        :param id: The id of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The name of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListResourceReservationPlansOutput.


        :param name: The name of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def reservation_config(self):
        """Gets the reservation_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The reservation_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: ReservationConfigForListResourceReservationPlansOutput
        """
        return self._reservation_config

    @reservation_config.setter
    def reservation_config(self, reservation_config):
        """Sets the reservation_config of this ItemForListResourceReservationPlansOutput.


        :param reservation_config: The reservation_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: ReservationConfigForListResourceReservationPlansOutput
        """

        self._reservation_config = reservation_config

    @property
    def status(self):
        """Gets the status of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The status of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: StatusForListResourceReservationPlansOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListResourceReservationPlansOutput.


        :param status: The status of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: StatusForListResourceReservationPlansOutput
        """

        self._status = status

    @property
    def storage_config(self):
        """Gets the storage_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The storage_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: StorageConfigForListResourceReservationPlansOutput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this ItemForListResourceReservationPlansOutput.


        :param storage_config: The storage_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: StorageConfigForListResourceReservationPlansOutput
        """

        self._storage_config = storage_config

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The update_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListResourceReservationPlansOutput.


        :param update_time: The update_time of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def workload_network_config(self):
        """Gets the workload_network_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501


        :return: The workload_network_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :rtype: WorkloadNetworkConfigForListResourceReservationPlansOutput
        """
        return self._workload_network_config

    @workload_network_config.setter
    def workload_network_config(self, workload_network_config):
        """Sets the workload_network_config of this ItemForListResourceReservationPlansOutput.


        :param workload_network_config: The workload_network_config of this ItemForListResourceReservationPlansOutput.  # noqa: E501
        :type: WorkloadNetworkConfigForListResourceReservationPlansOutput
        """

        self._workload_network_config = workload_network_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListResourceReservationPlansOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListResourceReservationPlansOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListResourceReservationPlansOutput):
            return True

        return self.to_dict() != other.to_dict()
