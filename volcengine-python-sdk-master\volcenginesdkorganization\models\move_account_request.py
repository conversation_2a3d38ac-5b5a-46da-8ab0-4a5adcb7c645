# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MoveAccountRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'to_org_unit_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'to_org_unit_id': 'ToOrgUnitId'
    }

    def __init__(self, account_id=None, to_org_unit_id=None, _configuration=None):  # noqa: E501
        """MoveAccountRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._to_org_unit_id = None
        self.discriminator = None

        self.account_id = account_id
        self.to_org_unit_id = to_org_unit_id

    @property
    def account_id(self):
        """Gets the account_id of this MoveAccountRequest.  # noqa: E501


        :return: The account_id of this MoveAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this MoveAccountRequest.


        :param account_id: The account_id of this MoveAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_id is None:
            raise ValueError("Invalid value for `account_id`, must not be `None`")  # noqa: E501

        self._account_id = account_id

    @property
    def to_org_unit_id(self):
        """Gets the to_org_unit_id of this MoveAccountRequest.  # noqa: E501


        :return: The to_org_unit_id of this MoveAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._to_org_unit_id

    @to_org_unit_id.setter
    def to_org_unit_id(self, to_org_unit_id):
        """Sets the to_org_unit_id of this MoveAccountRequest.


        :param to_org_unit_id: The to_org_unit_id of this MoveAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and to_org_unit_id is None:
            raise ValueError("Invalid value for `to_org_unit_id`, must not be `None`")  # noqa: E501

        self._to_org_unit_id = to_org_unit_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MoveAccountRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MoveAccountRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MoveAccountRequest):
            return True

        return self.to_dict() != other.to_dict()
