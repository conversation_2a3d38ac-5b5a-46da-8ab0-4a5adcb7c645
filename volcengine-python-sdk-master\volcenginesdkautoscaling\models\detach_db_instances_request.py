# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DetachDBInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_instance_ids': 'list[str]',
        'force_detach': 'bool',
        'scaling_group_id': 'str'
    }

    attribute_map = {
        'db_instance_ids': 'DBInstanceIds',
        'force_detach': 'ForceDetach',
        'scaling_group_id': 'ScalingGroupId'
    }

    def __init__(self, db_instance_ids=None, force_detach=None, scaling_group_id=None, _configuration=None):  # noqa: E501
        """DetachDBInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_instance_ids = None
        self._force_detach = None
        self._scaling_group_id = None
        self.discriminator = None

        if db_instance_ids is not None:
            self.db_instance_ids = db_instance_ids
        if force_detach is not None:
            self.force_detach = force_detach
        self.scaling_group_id = scaling_group_id

    @property
    def db_instance_ids(self):
        """Gets the db_instance_ids of this DetachDBInstancesRequest.  # noqa: E501


        :return: The db_instance_ids of this DetachDBInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._db_instance_ids

    @db_instance_ids.setter
    def db_instance_ids(self, db_instance_ids):
        """Sets the db_instance_ids of this DetachDBInstancesRequest.


        :param db_instance_ids: The db_instance_ids of this DetachDBInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._db_instance_ids = db_instance_ids

    @property
    def force_detach(self):
        """Gets the force_detach of this DetachDBInstancesRequest.  # noqa: E501


        :return: The force_detach of this DetachDBInstancesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._force_detach

    @force_detach.setter
    def force_detach(self, force_detach):
        """Sets the force_detach of this DetachDBInstancesRequest.


        :param force_detach: The force_detach of this DetachDBInstancesRequest.  # noqa: E501
        :type: bool
        """

        self._force_detach = force_detach

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this DetachDBInstancesRequest.  # noqa: E501


        :return: The scaling_group_id of this DetachDBInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this DetachDBInstancesRequest.


        :param scaling_group_id: The scaling_group_id of this DetachDBInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scaling_group_id is None:
            raise ValueError("Invalid value for `scaling_group_id`, must not be `None`")  # noqa: E501

        self._scaling_group_id = scaling_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DetachDBInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DetachDBInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DetachDBInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
