# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResolverEndpointsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direction': 'str',
        'name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForListResolverEndpointsInput]',
        'vpc_id': 'str',
        'vpc_trns': 'list[str]'
    }

    attribute_map = {
        'direction': 'Direction',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcID',
        'vpc_trns': 'VpcTrns'
    }

    def __init__(self, direction=None, name=None, page_number=None, page_size=None, project_name=None, status=None, tag_filters=None, vpc_id=None, vpc_trns=None, _configuration=None):  # noqa: E501
        """ListResolverEndpointsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direction = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._status = None
        self._tag_filters = None
        self._vpc_id = None
        self._vpc_trns = None
        self.discriminator = None

        if direction is not None:
            self.direction = direction
        if name is not None:
            self.name = name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns

    @property
    def direction(self):
        """Gets the direction of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The direction of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this ListResolverEndpointsRequest.


        :param direction: The direction of this ListResolverEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._direction = direction

    @property
    def name(self):
        """Gets the name of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The name of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListResolverEndpointsRequest.


        :param name: The name of this ListResolverEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The page_number of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListResolverEndpointsRequest.


        :param page_number: The page_number of this ListResolverEndpointsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The page_size of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListResolverEndpointsRequest.


        :param page_size: The page_size of this ListResolverEndpointsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The project_name of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListResolverEndpointsRequest.


        :param project_name: The project_name of this ListResolverEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The status of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListResolverEndpointsRequest.


        :param status: The status of this ListResolverEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The tag_filters of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: list[TagFilterForListResolverEndpointsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this ListResolverEndpointsRequest.


        :param tag_filters: The tag_filters of this ListResolverEndpointsRequest.  # noqa: E501
        :type: list[TagFilterForListResolverEndpointsInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The vpc_id of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this ListResolverEndpointsRequest.


        :param vpc_id: The vpc_id of this ListResolverEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this ListResolverEndpointsRequest.  # noqa: E501


        :return: The vpc_trns of this ListResolverEndpointsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this ListResolverEndpointsRequest.


        :param vpc_trns: The vpc_trns of this ListResolverEndpointsRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResolverEndpointsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResolverEndpointsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResolverEndpointsRequest):
            return True

        return self.to_dict() != other.to_dict()
