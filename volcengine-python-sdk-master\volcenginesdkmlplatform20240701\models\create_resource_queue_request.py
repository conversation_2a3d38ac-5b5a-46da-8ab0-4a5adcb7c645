# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResourceQueueRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'compute_resources': 'list[ComputeResourceForCreateResourceQueueInput]',
        'description': 'str',
        'dry_run': 'bool',
        'name': 'str',
        'quota': 'QuotaForCreateResourceQueueInput',
        'resource_group_id': 'str',
        'rules': 'RulesForCreateResourceQueueInput',
        'shareable': 'bool',
        'volume_resources': 'list[VolumeResourceForCreateResourceQueueInput]',
        'workload_infos': 'list[WorkloadInfoForCreateResourceQueueInput]'
    }

    attribute_map = {
        'compute_resources': 'ComputeResources',
        'description': 'Description',
        'dry_run': 'DryRun',
        'name': 'Name',
        'quota': 'Quota',
        'resource_group_id': 'ResourceGroupId',
        'rules': 'Rules',
        'shareable': 'Shareable',
        'volume_resources': 'VolumeResources',
        'workload_infos': 'WorkloadInfos'
    }

    def __init__(self, compute_resources=None, description=None, dry_run=None, name=None, quota=None, resource_group_id=None, rules=None, shareable=None, volume_resources=None, workload_infos=None, _configuration=None):  # noqa: E501
        """CreateResourceQueueRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._compute_resources = None
        self._description = None
        self._dry_run = None
        self._name = None
        self._quota = None
        self._resource_group_id = None
        self._rules = None
        self._shareable = None
        self._volume_resources = None
        self._workload_infos = None
        self.discriminator = None

        if compute_resources is not None:
            self.compute_resources = compute_resources
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        self.name = name
        if quota is not None:
            self.quota = quota
        self.resource_group_id = resource_group_id
        if rules is not None:
            self.rules = rules
        if shareable is not None:
            self.shareable = shareable
        if volume_resources is not None:
            self.volume_resources = volume_resources
        if workload_infos is not None:
            self.workload_infos = workload_infos

    @property
    def compute_resources(self):
        """Gets the compute_resources of this CreateResourceQueueRequest.  # noqa: E501


        :return: The compute_resources of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: list[ComputeResourceForCreateResourceQueueInput]
        """
        return self._compute_resources

    @compute_resources.setter
    def compute_resources(self, compute_resources):
        """Sets the compute_resources of this CreateResourceQueueRequest.


        :param compute_resources: The compute_resources of this CreateResourceQueueRequest.  # noqa: E501
        :type: list[ComputeResourceForCreateResourceQueueInput]
        """

        self._compute_resources = compute_resources

    @property
    def description(self):
        """Gets the description of this CreateResourceQueueRequest.  # noqa: E501


        :return: The description of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateResourceQueueRequest.


        :param description: The description of this CreateResourceQueueRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateResourceQueueRequest.  # noqa: E501


        :return: The dry_run of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateResourceQueueRequest.


        :param dry_run: The dry_run of this CreateResourceQueueRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def name(self):
        """Gets the name of this CreateResourceQueueRequest.  # noqa: E501


        :return: The name of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResourceQueueRequest.


        :param name: The name of this CreateResourceQueueRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def quota(self):
        """Gets the quota of this CreateResourceQueueRequest.  # noqa: E501


        :return: The quota of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: QuotaForCreateResourceQueueInput
        """
        return self._quota

    @quota.setter
    def quota(self, quota):
        """Sets the quota of this CreateResourceQueueRequest.


        :param quota: The quota of this CreateResourceQueueRequest.  # noqa: E501
        :type: QuotaForCreateResourceQueueInput
        """

        self._quota = quota

    @property
    def resource_group_id(self):
        """Gets the resource_group_id of this CreateResourceQueueRequest.  # noqa: E501


        :return: The resource_group_id of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_group_id

    @resource_group_id.setter
    def resource_group_id(self, resource_group_id):
        """Sets the resource_group_id of this CreateResourceQueueRequest.


        :param resource_group_id: The resource_group_id of this CreateResourceQueueRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_group_id is None:
            raise ValueError("Invalid value for `resource_group_id`, must not be `None`")  # noqa: E501

        self._resource_group_id = resource_group_id

    @property
    def rules(self):
        """Gets the rules of this CreateResourceQueueRequest.  # noqa: E501


        :return: The rules of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: RulesForCreateResourceQueueInput
        """
        return self._rules

    @rules.setter
    def rules(self, rules):
        """Sets the rules of this CreateResourceQueueRequest.


        :param rules: The rules of this CreateResourceQueueRequest.  # noqa: E501
        :type: RulesForCreateResourceQueueInput
        """

        self._rules = rules

    @property
    def shareable(self):
        """Gets the shareable of this CreateResourceQueueRequest.  # noqa: E501


        :return: The shareable of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this CreateResourceQueueRequest.


        :param shareable: The shareable of this CreateResourceQueueRequest.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    @property
    def volume_resources(self):
        """Gets the volume_resources of this CreateResourceQueueRequest.  # noqa: E501


        :return: The volume_resources of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: list[VolumeResourceForCreateResourceQueueInput]
        """
        return self._volume_resources

    @volume_resources.setter
    def volume_resources(self, volume_resources):
        """Sets the volume_resources of this CreateResourceQueueRequest.


        :param volume_resources: The volume_resources of this CreateResourceQueueRequest.  # noqa: E501
        :type: list[VolumeResourceForCreateResourceQueueInput]
        """

        self._volume_resources = volume_resources

    @property
    def workload_infos(self):
        """Gets the workload_infos of this CreateResourceQueueRequest.  # noqa: E501


        :return: The workload_infos of this CreateResourceQueueRequest.  # noqa: E501
        :rtype: list[WorkloadInfoForCreateResourceQueueInput]
        """
        return self._workload_infos

    @workload_infos.setter
    def workload_infos(self, workload_infos):
        """Sets the workload_infos of this CreateResourceQueueRequest.


        :param workload_infos: The workload_infos of this CreateResourceQueueRequest.  # noqa: E501
        :type: list[WorkloadInfoForCreateResourceQueueInput]
        """

        self._workload_infos = workload_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResourceQueueRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResourceQueueRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResourceQueueRequest):
            return True

        return self.to_dict() != other.to_dict()
