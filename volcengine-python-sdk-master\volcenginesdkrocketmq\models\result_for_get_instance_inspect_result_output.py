# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResultForGetInstanceInspectResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'describe': 'str',
        'inspect_name': 'str',
        'level': 'str',
        'resource': 'list[str]',
        'result_id': 'str',
        'status': 'str',
        'suggestion': 'list[str]',
        'time_stamp': 'str',
        'type': 'str'
    }

    attribute_map = {
        'describe': 'Describe',
        'inspect_name': 'InspectName',
        'level': 'Level',
        'resource': 'Resource',
        'result_id': 'ResultId',
        'status': 'Status',
        'suggestion': 'Suggestion',
        'time_stamp': 'TimeStamp',
        'type': 'Type'
    }

    def __init__(self, describe=None, inspect_name=None, level=None, resource=None, result_id=None, status=None, suggestion=None, time_stamp=None, type=None, _configuration=None):  # noqa: E501
        """ResultForGetInstanceInspectResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._describe = None
        self._inspect_name = None
        self._level = None
        self._resource = None
        self._result_id = None
        self._status = None
        self._suggestion = None
        self._time_stamp = None
        self._type = None
        self.discriminator = None

        if describe is not None:
            self.describe = describe
        if inspect_name is not None:
            self.inspect_name = inspect_name
        if level is not None:
            self.level = level
        if resource is not None:
            self.resource = resource
        if result_id is not None:
            self.result_id = result_id
        if status is not None:
            self.status = status
        if suggestion is not None:
            self.suggestion = suggestion
        if time_stamp is not None:
            self.time_stamp = time_stamp
        if type is not None:
            self.type = type

    @property
    def describe(self):
        """Gets the describe of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The describe of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._describe

    @describe.setter
    def describe(self, describe):
        """Sets the describe of this ResultForGetInstanceInspectResultOutput.


        :param describe: The describe of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._describe = describe

    @property
    def inspect_name(self):
        """Gets the inspect_name of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The inspect_name of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._inspect_name

    @inspect_name.setter
    def inspect_name(self, inspect_name):
        """Sets the inspect_name of this ResultForGetInstanceInspectResultOutput.


        :param inspect_name: The inspect_name of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._inspect_name = inspect_name

    @property
    def level(self):
        """Gets the level of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The level of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ResultForGetInstanceInspectResultOutput.


        :param level: The level of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def resource(self):
        """Gets the resource of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The resource of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource

    @resource.setter
    def resource(self, resource):
        """Sets the resource of this ResultForGetInstanceInspectResultOutput.


        :param resource: The resource of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: list[str]
        """

        self._resource = resource

    @property
    def result_id(self):
        """Gets the result_id of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The result_id of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._result_id

    @result_id.setter
    def result_id(self, result_id):
        """Sets the result_id of this ResultForGetInstanceInspectResultOutput.


        :param result_id: The result_id of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._result_id = result_id

    @property
    def status(self):
        """Gets the status of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The status of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResultForGetInstanceInspectResultOutput.


        :param status: The status of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def suggestion(self):
        """Gets the suggestion of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The suggestion of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._suggestion

    @suggestion.setter
    def suggestion(self, suggestion):
        """Sets the suggestion of this ResultForGetInstanceInspectResultOutput.


        :param suggestion: The suggestion of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: list[str]
        """

        self._suggestion = suggestion

    @property
    def time_stamp(self):
        """Gets the time_stamp of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The time_stamp of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_stamp

    @time_stamp.setter
    def time_stamp(self, time_stamp):
        """Sets the time_stamp of this ResultForGetInstanceInspectResultOutput.


        :param time_stamp: The time_stamp of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._time_stamp = time_stamp

    @property
    def type(self):
        """Gets the type of this ResultForGetInstanceInspectResultOutput.  # noqa: E501


        :return: The type of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ResultForGetInstanceInspectResultOutput.


        :param type: The type of this ResultForGetInstanceInspectResultOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResultForGetInstanceInspectResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResultForGetInstanceInspectResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResultForGetInstanceInspectResultOutput):
            return True

        return self.to_dict() != other.to_dict()
