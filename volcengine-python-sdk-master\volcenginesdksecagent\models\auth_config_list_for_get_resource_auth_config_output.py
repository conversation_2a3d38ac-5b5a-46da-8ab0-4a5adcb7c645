# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthConfigListForGetResourceAuthConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_params': 'list[AuthParamForGetResourceAuthConfigOutput]',
        'auth_type': 'str',
        'auth_type_name': 'str'
    }

    attribute_map = {
        'auth_params': 'AuthParams',
        'auth_type': 'AuthType',
        'auth_type_name': 'AuthTypeName'
    }

    def __init__(self, auth_params=None, auth_type=None, auth_type_name=None, _configuration=None):  # noqa: E501
        """AuthConfigListForGetResourceAuthConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_params = None
        self._auth_type = None
        self._auth_type_name = None
        self.discriminator = None

        if auth_params is not None:
            self.auth_params = auth_params
        if auth_type is not None:
            self.auth_type = auth_type
        if auth_type_name is not None:
            self.auth_type_name = auth_type_name

    @property
    def auth_params(self):
        """Gets the auth_params of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The auth_params of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: list[AuthParamForGetResourceAuthConfigOutput]
        """
        return self._auth_params

    @auth_params.setter
    def auth_params(self, auth_params):
        """Sets the auth_params of this AuthConfigListForGetResourceAuthConfigOutput.


        :param auth_params: The auth_params of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :type: list[AuthParamForGetResourceAuthConfigOutput]
        """

        self._auth_params = auth_params

    @property
    def auth_type(self):
        """Gets the auth_type of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The auth_type of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._auth_type

    @auth_type.setter
    def auth_type(self, auth_type):
        """Sets the auth_type of this AuthConfigListForGetResourceAuthConfigOutput.


        :param auth_type: The auth_type of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :type: str
        """

        self._auth_type = auth_type

    @property
    def auth_type_name(self):
        """Gets the auth_type_name of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The auth_type_name of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._auth_type_name

    @auth_type_name.setter
    def auth_type_name(self, auth_type_name):
        """Sets the auth_type_name of this AuthConfigListForGetResourceAuthConfigOutput.


        :param auth_type_name: The auth_type_name of this AuthConfigListForGetResourceAuthConfigOutput.  # noqa: E501
        :type: str
        """

        self._auth_type_name = auth_type_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthConfigListForGetResourceAuthConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthConfigListForGetResourceAuthConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthConfigListForGetResourceAuthConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
