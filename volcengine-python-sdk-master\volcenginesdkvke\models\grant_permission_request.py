# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GrantPermissionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'grantee_id': 'int',
        'grantee_type': 'str',
        'is_custom_role': 'bool',
        'namespace': 'str',
        'role_domain': 'str',
        'role_name': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'grantee_id': 'GranteeId',
        'grantee_type': 'GranteeType',
        'is_custom_role': 'IsCustomRole',
        'namespace': 'Namespace',
        'role_domain': 'RoleDomain',
        'role_name': 'RoleName'
    }

    def __init__(self, cluster_id=None, grantee_id=None, grantee_type=None, is_custom_role=None, namespace=None, role_domain=None, role_name=None, _configuration=None):  # noqa: E501
        """GrantPermissionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._grantee_id = None
        self._grantee_type = None
        self._is_custom_role = None
        self._namespace = None
        self._role_domain = None
        self._role_name = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if grantee_id is not None:
            self.grantee_id = grantee_id
        if grantee_type is not None:
            self.grantee_type = grantee_type
        if is_custom_role is not None:
            self.is_custom_role = is_custom_role
        if namespace is not None:
            self.namespace = namespace
        if role_domain is not None:
            self.role_domain = role_domain
        if role_name is not None:
            self.role_name = role_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this GrantPermissionRequest.  # noqa: E501


        :return: The cluster_id of this GrantPermissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this GrantPermissionRequest.


        :param cluster_id: The cluster_id of this GrantPermissionRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def grantee_id(self):
        """Gets the grantee_id of this GrantPermissionRequest.  # noqa: E501


        :return: The grantee_id of this GrantPermissionRequest.  # noqa: E501
        :rtype: int
        """
        return self._grantee_id

    @grantee_id.setter
    def grantee_id(self, grantee_id):
        """Sets the grantee_id of this GrantPermissionRequest.


        :param grantee_id: The grantee_id of this GrantPermissionRequest.  # noqa: E501
        :type: int
        """

        self._grantee_id = grantee_id

    @property
    def grantee_type(self):
        """Gets the grantee_type of this GrantPermissionRequest.  # noqa: E501


        :return: The grantee_type of this GrantPermissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._grantee_type

    @grantee_type.setter
    def grantee_type(self, grantee_type):
        """Sets the grantee_type of this GrantPermissionRequest.


        :param grantee_type: The grantee_type of this GrantPermissionRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["User", "Role", "Account"]  # noqa: E501
        if (self._configuration.client_side_validation and
                grantee_type not in allowed_values):
            raise ValueError(
                "Invalid value for `grantee_type` ({0}), must be one of {1}"  # noqa: E501
                .format(grantee_type, allowed_values)
            )

        self._grantee_type = grantee_type

    @property
    def is_custom_role(self):
        """Gets the is_custom_role of this GrantPermissionRequest.  # noqa: E501


        :return: The is_custom_role of this GrantPermissionRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_custom_role

    @is_custom_role.setter
    def is_custom_role(self, is_custom_role):
        """Sets the is_custom_role of this GrantPermissionRequest.


        :param is_custom_role: The is_custom_role of this GrantPermissionRequest.  # noqa: E501
        :type: bool
        """

        self._is_custom_role = is_custom_role

    @property
    def namespace(self):
        """Gets the namespace of this GrantPermissionRequest.  # noqa: E501


        :return: The namespace of this GrantPermissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this GrantPermissionRequest.


        :param namespace: The namespace of this GrantPermissionRequest.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def role_domain(self):
        """Gets the role_domain of this GrantPermissionRequest.  # noqa: E501


        :return: The role_domain of this GrantPermissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_domain

    @role_domain.setter
    def role_domain(self, role_domain):
        """Sets the role_domain of this GrantPermissionRequest.


        :param role_domain: The role_domain of this GrantPermissionRequest.  # noqa: E501
        :type: str
        """

        self._role_domain = role_domain

    @property
    def role_name(self):
        """Gets the role_name of this GrantPermissionRequest.  # noqa: E501


        :return: The role_name of this GrantPermissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this GrantPermissionRequest.


        :param role_name: The role_name of this GrantPermissionRequest.  # noqa: E501
        :type: str
        """

        self._role_name = role_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GrantPermissionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GrantPermissionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GrantPermissionRequest):
            return True

        return self.to_dict() != other.to_dict()
