# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TrendForDescWebRespCodeOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'flow': 'list[FlowForDescWebRespCodeOutput]',
        'resp_code': 'str'
    }

    attribute_map = {
        'flow': 'Flow',
        'resp_code': 'RespCode'
    }

    def __init__(self, flow=None, resp_code=None, _configuration=None):  # noqa: E501
        """TrendForDescWebRespCodeOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._flow = None
        self._resp_code = None
        self.discriminator = None

        if flow is not None:
            self.flow = flow
        if resp_code is not None:
            self.resp_code = resp_code

    @property
    def flow(self):
        """Gets the flow of this TrendForDescWebRespCodeOutput.  # noqa: E501


        :return: The flow of this TrendForDescWebRespCodeOutput.  # noqa: E501
        :rtype: list[FlowForDescWebRespCodeOutput]
        """
        return self._flow

    @flow.setter
    def flow(self, flow):
        """Sets the flow of this TrendForDescWebRespCodeOutput.


        :param flow: The flow of this TrendForDescWebRespCodeOutput.  # noqa: E501
        :type: list[FlowForDescWebRespCodeOutput]
        """

        self._flow = flow

    @property
    def resp_code(self):
        """Gets the resp_code of this TrendForDescWebRespCodeOutput.  # noqa: E501


        :return: The resp_code of this TrendForDescWebRespCodeOutput.  # noqa: E501
        :rtype: str
        """
        return self._resp_code

    @resp_code.setter
    def resp_code(self, resp_code):
        """Sets the resp_code of this TrendForDescWebRespCodeOutput.


        :param resp_code: The resp_code of this TrendForDescWebRespCodeOutput.  # noqa: E501
        :type: str
        """

        self._resp_code = resp_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrendForDescWebRespCodeOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrendForDescWebRespCodeOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TrendForDescWebRespCodeOutput):
            return True

        return self.to_dict() != other.to_dict()
