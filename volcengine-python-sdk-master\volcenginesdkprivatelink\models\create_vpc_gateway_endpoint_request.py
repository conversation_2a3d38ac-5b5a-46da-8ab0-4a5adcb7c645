# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateVpcGatewayEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'endpoint_name': 'str',
        'project_name': 'str',
        'service_id': 'str',
        'tags': 'list[TagForCreateVpcGatewayEndpointInput]',
        'vpc_id': 'str',
        'vpc_policy': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'endpoint_name': 'EndpointName',
        'project_name': 'ProjectName',
        'service_id': 'ServiceId',
        'tags': 'Tags',
        'vpc_id': 'VpcId',
        'vpc_policy': 'VpcPolicy'
    }

    def __init__(self, client_token=None, description=None, endpoint_name=None, project_name=None, service_id=None, tags=None, vpc_id=None, vpc_policy=None, _configuration=None):  # noqa: E501
        """CreateVpcGatewayEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._endpoint_name = None
        self._project_name = None
        self._service_id = None
        self._tags = None
        self._vpc_id = None
        self._vpc_policy = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        if project_name is not None:
            self.project_name = project_name
        self.service_id = service_id
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id
        if vpc_policy is not None:
            self.vpc_policy = vpc_policy

    @property
    def client_token(self):
        """Gets the client_token of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The client_token of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateVpcGatewayEndpointRequest.


        :param client_token: The client_token of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The description of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateVpcGatewayEndpointRequest.


        :param description: The description of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The endpoint_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this CreateVpcGatewayEndpointRequest.


        :param endpoint_name: The endpoint_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def project_name(self):
        """Gets the project_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The project_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateVpcGatewayEndpointRequest.


        :param project_name: The project_name of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def service_id(self):
        """Gets the service_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The service_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this CreateVpcGatewayEndpointRequest.


        :param service_id: The service_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and service_id is None:
            raise ValueError("Invalid value for `service_id`, must not be `None`")  # noqa: E501

        self._service_id = service_id

    @property
    def tags(self):
        """Gets the tags of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The tags of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: list[TagForCreateVpcGatewayEndpointInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateVpcGatewayEndpointRequest.


        :param tags: The tags of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: list[TagForCreateVpcGatewayEndpointInput]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The vpc_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateVpcGatewayEndpointRequest.


        :param vpc_id: The vpc_id of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def vpc_policy(self):
        """Gets the vpc_policy of this CreateVpcGatewayEndpointRequest.  # noqa: E501


        :return: The vpc_policy of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_policy

    @vpc_policy.setter
    def vpc_policy(self, vpc_policy):
        """Sets the vpc_policy of this CreateVpcGatewayEndpointRequest.


        :param vpc_policy: The vpc_policy of this CreateVpcGatewayEndpointRequest.  # noqa: E501
        :type: str
        """

        self._vpc_policy = vpc_policy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateVpcGatewayEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateVpcGatewayEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateVpcGatewayEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
