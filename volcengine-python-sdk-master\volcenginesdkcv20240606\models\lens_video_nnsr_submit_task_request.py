# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LensVideoNnsrSubmitTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'req_key': 'str',
        'url': 'str',
        'vid': 'str'
    }

    attribute_map = {
        'req_key': 'req_key',
        'url': 'url',
        'vid': 'vid'
    }

    def __init__(self, req_key=None, url=None, vid=None, _configuration=None):  # noqa: E501
        """LensVideoNnsrSubmitTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._req_key = None
        self._url = None
        self._vid = None
        self.discriminator = None

        self.req_key = req_key
        if url is not None:
            self.url = url
        if vid is not None:
            self.vid = vid

    @property
    def req_key(self):
        """Gets the req_key of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501


        :return: The req_key of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this LensVideoNnsrSubmitTaskRequest.


        :param req_key: The req_key of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    @property
    def url(self):
        """Gets the url of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501


        :return: The url of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this LensVideoNnsrSubmitTaskRequest.


        :param url: The url of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :type: str
        """

        self._url = url

    @property
    def vid(self):
        """Gets the vid of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501


        :return: The vid of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this LensVideoNnsrSubmitTaskRequest.


        :param vid: The vid of this LensVideoNnsrSubmitTaskRequest.  # noqa: E501
        :type: str
        """

        self._vid = vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LensVideoNnsrSubmitTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LensVideoNnsrSubmitTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LensVideoNnsrSubmitTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
