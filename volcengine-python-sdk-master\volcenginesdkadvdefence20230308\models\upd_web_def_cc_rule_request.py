# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdWebDefCcRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accurate_group': 'AccurateGroupForUpdWebDefCcRuleInput',
        'cc_type': 'int',
        'count_time': 'int',
        'ddos_id': 'int',
        'effect_time': 'int',
        'enable': 'int',
        'field': 'str',
        'host': 'str',
        'name': 'str',
        'path_threshold': 'int',
        'prefix_switch': 'int',
        'rule_priority': 'int',
        'single_threshold': 'int',
        'url': 'str'
    }

    attribute_map = {
        'accurate_group': 'AccurateGroup',
        'cc_type': 'CCType',
        'count_time': 'CountTime',
        'ddos_id': 'DdosId',
        'effect_time': 'EffectTime',
        'enable': 'Enable',
        'field': 'Field',
        'host': 'Host',
        'name': 'Name',
        'path_threshold': 'PathThreshold',
        'prefix_switch': 'PrefixSwitch',
        'rule_priority': 'RulePriority',
        'single_threshold': 'SingleThreshold',
        'url': 'Url'
    }

    def __init__(self, accurate_group=None, cc_type=None, count_time=None, ddos_id=None, effect_time=None, enable=None, field=None, host=None, name=None, path_threshold=None, prefix_switch=None, rule_priority=None, single_threshold=None, url=None, _configuration=None):  # noqa: E501
        """UpdWebDefCcRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accurate_group = None
        self._cc_type = None
        self._count_time = None
        self._ddos_id = None
        self._effect_time = None
        self._enable = None
        self._field = None
        self._host = None
        self._name = None
        self._path_threshold = None
        self._prefix_switch = None
        self._rule_priority = None
        self._single_threshold = None
        self._url = None
        self.discriminator = None

        if accurate_group is not None:
            self.accurate_group = accurate_group
        self.cc_type = cc_type
        self.count_time = count_time
        self.ddos_id = ddos_id
        self.effect_time = effect_time
        self.enable = enable
        self.field = field
        self.host = host
        self.name = name
        self.path_threshold = path_threshold
        self.prefix_switch = prefix_switch
        self.rule_priority = rule_priority
        self.single_threshold = single_threshold
        self.url = url

    @property
    def accurate_group(self):
        """Gets the accurate_group of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The accurate_group of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: AccurateGroupForUpdWebDefCcRuleInput
        """
        return self._accurate_group

    @accurate_group.setter
    def accurate_group(self, accurate_group):
        """Sets the accurate_group of this UpdWebDefCcRuleRequest.


        :param accurate_group: The accurate_group of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: AccurateGroupForUpdWebDefCcRuleInput
        """

        self._accurate_group = accurate_group

    @property
    def cc_type(self):
        """Gets the cc_type of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The cc_type of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._cc_type

    @cc_type.setter
    def cc_type(self, cc_type):
        """Sets the cc_type of this UpdWebDefCcRuleRequest.


        :param cc_type: The cc_type of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cc_type is None:
            raise ValueError("Invalid value for `cc_type`, must not be `None`")  # noqa: E501

        self._cc_type = cc_type

    @property
    def count_time(self):
        """Gets the count_time of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The count_time of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._count_time

    @count_time.setter
    def count_time(self, count_time):
        """Sets the count_time of this UpdWebDefCcRuleRequest.


        :param count_time: The count_time of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and count_time is None:
            raise ValueError("Invalid value for `count_time`, must not be `None`")  # noqa: E501

        self._count_time = count_time

    @property
    def ddos_id(self):
        """Gets the ddos_id of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The ddos_id of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._ddos_id

    @ddos_id.setter
    def ddos_id(self, ddos_id):
        """Sets the ddos_id of this UpdWebDefCcRuleRequest.


        :param ddos_id: The ddos_id of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and ddos_id is None:
            raise ValueError("Invalid value for `ddos_id`, must not be `None`")  # noqa: E501

        self._ddos_id = ddos_id

    @property
    def effect_time(self):
        """Gets the effect_time of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The effect_time of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._effect_time

    @effect_time.setter
    def effect_time(self, effect_time):
        """Sets the effect_time of this UpdWebDefCcRuleRequest.


        :param effect_time: The effect_time of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and effect_time is None:
            raise ValueError("Invalid value for `effect_time`, must not be `None`")  # noqa: E501

        self._effect_time = effect_time

    @property
    def enable(self):
        """Gets the enable of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The enable of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this UpdWebDefCcRuleRequest.


        :param enable: The enable of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and enable is None:
            raise ValueError("Invalid value for `enable`, must not be `None`")  # noqa: E501

        self._enable = enable

    @property
    def field(self):
        """Gets the field of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The field of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._field

    @field.setter
    def field(self, field):
        """Sets the field of this UpdWebDefCcRuleRequest.


        :param field: The field of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and field is None:
            raise ValueError("Invalid value for `field`, must not be `None`")  # noqa: E501

        self._field = field

    @property
    def host(self):
        """Gets the host of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The host of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this UpdWebDefCcRuleRequest.


        :param host: The host of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and host is None:
            raise ValueError("Invalid value for `host`, must not be `None`")  # noqa: E501

        self._host = host

    @property
    def name(self):
        """Gets the name of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The name of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdWebDefCcRuleRequest.


        :param name: The name of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def path_threshold(self):
        """Gets the path_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The path_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._path_threshold

    @path_threshold.setter
    def path_threshold(self, path_threshold):
        """Sets the path_threshold of this UpdWebDefCcRuleRequest.


        :param path_threshold: The path_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and path_threshold is None:
            raise ValueError("Invalid value for `path_threshold`, must not be `None`")  # noqa: E501

        self._path_threshold = path_threshold

    @property
    def prefix_switch(self):
        """Gets the prefix_switch of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The prefix_switch of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._prefix_switch

    @prefix_switch.setter
    def prefix_switch(self, prefix_switch):
        """Sets the prefix_switch of this UpdWebDefCcRuleRequest.


        :param prefix_switch: The prefix_switch of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and prefix_switch is None:
            raise ValueError("Invalid value for `prefix_switch`, must not be `None`")  # noqa: E501

        self._prefix_switch = prefix_switch

    @property
    def rule_priority(self):
        """Gets the rule_priority of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The rule_priority of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._rule_priority

    @rule_priority.setter
    def rule_priority(self, rule_priority):
        """Sets the rule_priority of this UpdWebDefCcRuleRequest.


        :param rule_priority: The rule_priority of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and rule_priority is None:
            raise ValueError("Invalid value for `rule_priority`, must not be `None`")  # noqa: E501

        self._rule_priority = rule_priority

    @property
    def single_threshold(self):
        """Gets the single_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The single_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._single_threshold

    @single_threshold.setter
    def single_threshold(self, single_threshold):
        """Sets the single_threshold of this UpdWebDefCcRuleRequest.


        :param single_threshold: The single_threshold of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and single_threshold is None:
            raise ValueError("Invalid value for `single_threshold`, must not be `None`")  # noqa: E501

        self._single_threshold = single_threshold

    @property
    def url(self):
        """Gets the url of this UpdWebDefCcRuleRequest.  # noqa: E501


        :return: The url of this UpdWebDefCcRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this UpdWebDefCcRuleRequest.


        :param url: The url of this UpdWebDefCcRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and url is None:
            raise ValueError("Invalid value for `url`, must not be `None`")  # noqa: E501

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdWebDefCcRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdWebDefCcRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdWebDefCcRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
