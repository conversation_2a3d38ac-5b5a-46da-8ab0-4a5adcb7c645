# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProtectHostForGetTenantQuotaOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'expire_time': 'int',
        'total_count': 'int',
        'used_count': 'int',
        'version': 'int'
    }

    attribute_map = {
        'expire_time': 'ExpireTime',
        'total_count': 'TotalCount',
        'used_count': 'UsedCount',
        'version': 'Version'
    }

    def __init__(self, expire_time=None, total_count=None, used_count=None, version=None, _configuration=None):  # noqa: E501
        """ProtectHostForGetTenantQuotaOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._expire_time = None
        self._total_count = None
        self._used_count = None
        self._version = None
        self.discriminator = None

        if expire_time is not None:
            self.expire_time = expire_time
        if total_count is not None:
            self.total_count = total_count
        if used_count is not None:
            self.used_count = used_count
        if version is not None:
            self.version = version

    @property
    def expire_time(self):
        """Gets the expire_time of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501


        :return: The expire_time of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ProtectHostForGetTenantQuotaOutput.


        :param expire_time: The expire_time of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def total_count(self):
        """Gets the total_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501


        :return: The total_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ProtectHostForGetTenantQuotaOutput.


        :param total_count: The total_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def used_count(self):
        """Gets the used_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501


        :return: The used_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._used_count

    @used_count.setter
    def used_count(self, used_count):
        """Sets the used_count of this ProtectHostForGetTenantQuotaOutput.


        :param used_count: The used_count of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :type: int
        """

        self._used_count = used_count

    @property
    def version(self):
        """Gets the version of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501


        :return: The version of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this ProtectHostForGetTenantQuotaOutput.


        :param version: The version of this ProtectHostForGetTenantQuotaOutput.  # noqa: E501
        :type: int
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProtectHostForGetTenantQuotaOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProtectHostForGetTenantQuotaOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProtectHostForGetTenantQuotaOutput):
            return True

        return self.to_dict() != other.to_dict()
