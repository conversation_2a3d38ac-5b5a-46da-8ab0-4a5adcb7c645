# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusKillChainForGetHidsAlarmInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'docker': 'str',
        'external_conns': 'str',
        'pid_set': 'str',
        'ssh': 'str',
        'timestamp': 'str',
        'top_chain': 'str',
        'top_rule_chain': 'list[str]'
    }

    attribute_map = {
        'docker': 'Docker',
        'external_conns': 'ExternalConns',
        'pid_set': 'PidSet',
        'ssh': 'Ssh',
        'timestamp': 'Timestamp',
        'top_chain': 'TopChain',
        'top_rule_chain': 'TopRuleChain'
    }

    def __init__(self, docker=None, external_conns=None, pid_set=None, ssh=None, timestamp=None, top_chain=None, top_rule_chain=None, _configuration=None):  # noqa: E501
        """PlusKillChainForGetHidsAlarmInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._docker = None
        self._external_conns = None
        self._pid_set = None
        self._ssh = None
        self._timestamp = None
        self._top_chain = None
        self._top_rule_chain = None
        self.discriminator = None

        if docker is not None:
            self.docker = docker
        if external_conns is not None:
            self.external_conns = external_conns
        if pid_set is not None:
            self.pid_set = pid_set
        if ssh is not None:
            self.ssh = ssh
        if timestamp is not None:
            self.timestamp = timestamp
        if top_chain is not None:
            self.top_chain = top_chain
        if top_rule_chain is not None:
            self.top_rule_chain = top_rule_chain

    @property
    def docker(self):
        """Gets the docker of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The docker of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._docker

    @docker.setter
    def docker(self, docker):
        """Sets the docker of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param docker: The docker of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._docker = docker

    @property
    def external_conns(self):
        """Gets the external_conns of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The external_conns of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_conns

    @external_conns.setter
    def external_conns(self, external_conns):
        """Sets the external_conns of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param external_conns: The external_conns of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._external_conns = external_conns

    @property
    def pid_set(self):
        """Gets the pid_set of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The pid_set of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_set

    @pid_set.setter
    def pid_set(self, pid_set):
        """Sets the pid_set of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param pid_set: The pid_set of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid_set = pid_set

    @property
    def ssh(self):
        """Gets the ssh of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The ssh of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssh

    @ssh.setter
    def ssh(self, ssh):
        """Sets the ssh of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param ssh: The ssh of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._ssh = ssh

    @property
    def timestamp(self):
        """Gets the timestamp of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The timestamp of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param timestamp: The timestamp of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._timestamp = timestamp

    @property
    def top_chain(self):
        """Gets the top_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The top_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_chain

    @top_chain.setter
    def top_chain(self, top_chain):
        """Sets the top_chain of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param top_chain: The top_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._top_chain = top_chain

    @property
    def top_rule_chain(self):
        """Gets the top_rule_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The top_rule_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._top_rule_chain

    @top_rule_chain.setter
    def top_rule_chain(self, top_rule_chain):
        """Sets the top_rule_chain of this PlusKillChainForGetHidsAlarmInfoOutput.


        :param top_rule_chain: The top_rule_chain of this PlusKillChainForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._top_rule_chain = top_rule_chain

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusKillChainForGetHidsAlarmInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusKillChainForGetHidsAlarmInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusKillChainForGetHidsAlarmInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
