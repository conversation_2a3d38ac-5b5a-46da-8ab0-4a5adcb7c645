# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateSecretRotationPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'automatic_rotation': 'bool',
        'rotation_interval': 'str',
        'secret_name': 'str'
    }

    attribute_map = {
        'automatic_rotation': 'AutomaticRotation',
        'rotation_interval': 'RotationInterval',
        'secret_name': 'SecretName'
    }

    def __init__(self, automatic_rotation=None, rotation_interval=None, secret_name=None, _configuration=None):  # noqa: E501
        """UpdateSecretRotationPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._automatic_rotation = None
        self._rotation_interval = None
        self._secret_name = None
        self.discriminator = None

        self.automatic_rotation = automatic_rotation
        if rotation_interval is not None:
            self.rotation_interval = rotation_interval
        self.secret_name = secret_name

    @property
    def automatic_rotation(self):
        """Gets the automatic_rotation of this UpdateSecretRotationPolicyRequest.  # noqa: E501


        :return: The automatic_rotation of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._automatic_rotation

    @automatic_rotation.setter
    def automatic_rotation(self, automatic_rotation):
        """Sets the automatic_rotation of this UpdateSecretRotationPolicyRequest.


        :param automatic_rotation: The automatic_rotation of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and automatic_rotation is None:
            raise ValueError("Invalid value for `automatic_rotation`, must not be `None`")  # noqa: E501

        self._automatic_rotation = automatic_rotation

    @property
    def rotation_interval(self):
        """Gets the rotation_interval of this UpdateSecretRotationPolicyRequest.  # noqa: E501


        :return: The rotation_interval of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._rotation_interval

    @rotation_interval.setter
    def rotation_interval(self, rotation_interval):
        """Sets the rotation_interval of this UpdateSecretRotationPolicyRequest.


        :param rotation_interval: The rotation_interval of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :type: str
        """

        self._rotation_interval = rotation_interval

    @property
    def secret_name(self):
        """Gets the secret_name of this UpdateSecretRotationPolicyRequest.  # noqa: E501


        :return: The secret_name of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._secret_name

    @secret_name.setter
    def secret_name(self, secret_name):
        """Sets the secret_name of this UpdateSecretRotationPolicyRequest.


        :param secret_name: The secret_name of this UpdateSecretRotationPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and secret_name is None:
            raise ValueError("Invalid value for `secret_name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_name is not None and len(secret_name) > 128):
            raise ValueError("Invalid value for `secret_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                secret_name is not None and len(secret_name) < 2):
            raise ValueError("Invalid value for `secret_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._secret_name = secret_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateSecretRotationPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateSecretRotationPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateSecretRotationPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
