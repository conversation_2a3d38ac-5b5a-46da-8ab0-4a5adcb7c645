# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlertRuleForListAlertStrategiesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'conditions': 'list[ConditionForListAlertStrategiesOutput]',
        'enable_end_time': 'str',
        'enable_start_time': 'str',
        'frequency': 'int',
        'level': 'str',
        'logic': 'str'
    }

    attribute_map = {
        'conditions': 'Conditions',
        'enable_end_time': 'EnableEndTime',
        'enable_start_time': 'EnableStartTime',
        'frequency': 'Frequency',
        'level': 'Level',
        'logic': 'Logic'
    }

    def __init__(self, conditions=None, enable_end_time=None, enable_start_time=None, frequency=None, level=None, logic=None, _configuration=None):  # noqa: E501
        """AlertRuleForListAlertStrategiesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._conditions = None
        self._enable_end_time = None
        self._enable_start_time = None
        self._frequency = None
        self._level = None
        self._logic = None
        self.discriminator = None

        if conditions is not None:
            self.conditions = conditions
        if enable_end_time is not None:
            self.enable_end_time = enable_end_time
        if enable_start_time is not None:
            self.enable_start_time = enable_start_time
        if frequency is not None:
            self.frequency = frequency
        if level is not None:
            self.level = level
        if logic is not None:
            self.logic = logic

    @property
    def conditions(self):
        """Gets the conditions of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The conditions of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: list[ConditionForListAlertStrategiesOutput]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this AlertRuleForListAlertStrategiesOutput.


        :param conditions: The conditions of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: list[ConditionForListAlertStrategiesOutput]
        """

        self._conditions = conditions

    @property
    def enable_end_time(self):
        """Gets the enable_end_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The enable_end_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_end_time

    @enable_end_time.setter
    def enable_end_time(self, enable_end_time):
        """Sets the enable_end_time of this AlertRuleForListAlertStrategiesOutput.


        :param enable_end_time: The enable_end_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: str
        """

        self._enable_end_time = enable_end_time

    @property
    def enable_start_time(self):
        """Gets the enable_start_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The enable_start_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_start_time

    @enable_start_time.setter
    def enable_start_time(self, enable_start_time):
        """Sets the enable_start_time of this AlertRuleForListAlertStrategiesOutput.


        :param enable_start_time: The enable_start_time of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: str
        """

        self._enable_start_time = enable_start_time

    @property
    def frequency(self):
        """Gets the frequency of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The frequency of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._frequency

    @frequency.setter
    def frequency(self, frequency):
        """Sets the frequency of this AlertRuleForListAlertStrategiesOutput.


        :param frequency: The frequency of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: int
        """

        self._frequency = frequency

    @property
    def level(self):
        """Gets the level of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The level of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this AlertRuleForListAlertStrategiesOutput.


        :param level: The level of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def logic(self):
        """Gets the logic of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501


        :return: The logic of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._logic

    @logic.setter
    def logic(self, logic):
        """Sets the logic of this AlertRuleForListAlertStrategiesOutput.


        :param logic: The logic of this AlertRuleForListAlertStrategiesOutput.  # noqa: E501
        :type: str
        """

        self._logic = logic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlertRuleForListAlertStrategiesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlertRuleForListAlertStrategiesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlertRuleForListAlertStrategiesOutput):
            return True

        return self.to_dict() != other.to_dict()
