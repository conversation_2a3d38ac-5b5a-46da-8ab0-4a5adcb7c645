# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateAlarmRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_type': 'str',
        'description': 'str',
        'metric_unit': 'str',
        'rule_id': 'str',
        'rule_name': 'str',
        'silence_time': 'int',
        'threshold': 'str'
    }

    attribute_map = {
        'alarm_type': 'AlarmType',
        'description': 'Description',
        'metric_unit': 'MetricUnit',
        'rule_id': 'RuleID',
        'rule_name': 'RuleName',
        'silence_time': 'SilenceTime',
        'threshold': 'Threshold'
    }

    def __init__(self, alarm_type=None, description=None, metric_unit=None, rule_id=None, rule_name=None, silence_time=None, threshold=None, _configuration=None):  # noqa: E501
        """UpdateAlarmRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_type = None
        self._description = None
        self._metric_unit = None
        self._rule_id = None
        self._rule_name = None
        self._silence_time = None
        self._threshold = None
        self.discriminator = None

        self.alarm_type = alarm_type
        if description is not None:
            self.description = description
        self.metric_unit = metric_unit
        self.rule_id = rule_id
        self.rule_name = rule_name
        if silence_time is not None:
            self.silence_time = silence_time
        self.threshold = threshold

    @property
    def alarm_type(self):
        """Gets the alarm_type of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The alarm_type of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this UpdateAlarmRuleRequest.


        :param alarm_type: The alarm_type of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and alarm_type is None:
            raise ValueError("Invalid value for `alarm_type`, must not be `None`")  # noqa: E501

        self._alarm_type = alarm_type

    @property
    def description(self):
        """Gets the description of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The description of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateAlarmRuleRequest.


        :param description: The description of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def metric_unit(self):
        """Gets the metric_unit of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The metric_unit of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric_unit

    @metric_unit.setter
    def metric_unit(self, metric_unit):
        """Sets the metric_unit of this UpdateAlarmRuleRequest.


        :param metric_unit: The metric_unit of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric_unit is None:
            raise ValueError("Invalid value for `metric_unit`, must not be `None`")  # noqa: E501

        self._metric_unit = metric_unit

    @property
    def rule_id(self):
        """Gets the rule_id of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The rule_id of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this UpdateAlarmRuleRequest.


        :param rule_id: The rule_id of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def rule_name(self):
        """Gets the rule_name of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The rule_name of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this UpdateAlarmRuleRequest.


        :param rule_name: The rule_name of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_name is None:
            raise ValueError("Invalid value for `rule_name`, must not be `None`")  # noqa: E501

        self._rule_name = rule_name

    @property
    def silence_time(self):
        """Gets the silence_time of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The silence_time of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this UpdateAlarmRuleRequest.


        :param silence_time: The silence_time of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: int
        """

        self._silence_time = silence_time

    @property
    def threshold(self):
        """Gets the threshold of this UpdateAlarmRuleRequest.  # noqa: E501


        :return: The threshold of this UpdateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this UpdateAlarmRuleRequest.


        :param threshold: The threshold of this UpdateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and threshold is None:
            raise ValueError("Invalid value for `threshold`, must not be `None`")  # noqa: E501

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateAlarmRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateAlarmRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateAlarmRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
