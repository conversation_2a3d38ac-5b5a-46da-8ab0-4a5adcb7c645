# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateGatewayRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backend_spec': 'BackendSpecForCreateGatewayInput',
        'comments': 'str',
        'log_spec': 'LogSpecForCreateGatewayInput',
        'monitor_spec': 'MonitorSpecForCreateGatewayInput',
        'name': 'str',
        'network_spec': 'NetworkSpecForCreateGatewayInput',
        'project_name': 'str',
        'region': 'str',
        'resource_spec': 'ResourceSpecForCreateGatewayInput',
        'tags': 'list[TagForCreateGatewayInput]',
        'type': 'str'
    }

    attribute_map = {
        'backend_spec': 'BackendSpec',
        'comments': 'Comments',
        'log_spec': 'LogSpec',
        'monitor_spec': 'MonitorSpec',
        'name': 'Name',
        'network_spec': 'NetworkSpec',
        'project_name': 'ProjectName',
        'region': 'Region',
        'resource_spec': 'ResourceSpec',
        'tags': 'Tags',
        'type': 'Type'
    }

    def __init__(self, backend_spec=None, comments=None, log_spec=None, monitor_spec=None, name=None, network_spec=None, project_name=None, region=None, resource_spec=None, tags=None, type=None, _configuration=None):  # noqa: E501
        """CreateGatewayRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backend_spec = None
        self._comments = None
        self._log_spec = None
        self._monitor_spec = None
        self._name = None
        self._network_spec = None
        self._project_name = None
        self._region = None
        self._resource_spec = None
        self._tags = None
        self._type = None
        self.discriminator = None

        if backend_spec is not None:
            self.backend_spec = backend_spec
        if comments is not None:
            self.comments = comments
        if log_spec is not None:
            self.log_spec = log_spec
        if monitor_spec is not None:
            self.monitor_spec = monitor_spec
        self.name = name
        if network_spec is not None:
            self.network_spec = network_spec
        if project_name is not None:
            self.project_name = project_name
        self.region = region
        if resource_spec is not None:
            self.resource_spec = resource_spec
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type

    @property
    def backend_spec(self):
        """Gets the backend_spec of this CreateGatewayRequest.  # noqa: E501


        :return: The backend_spec of this CreateGatewayRequest.  # noqa: E501
        :rtype: BackendSpecForCreateGatewayInput
        """
        return self._backend_spec

    @backend_spec.setter
    def backend_spec(self, backend_spec):
        """Sets the backend_spec of this CreateGatewayRequest.


        :param backend_spec: The backend_spec of this CreateGatewayRequest.  # noqa: E501
        :type: BackendSpecForCreateGatewayInput
        """

        self._backend_spec = backend_spec

    @property
    def comments(self):
        """Gets the comments of this CreateGatewayRequest.  # noqa: E501


        :return: The comments of this CreateGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this CreateGatewayRequest.


        :param comments: The comments of this CreateGatewayRequest.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def log_spec(self):
        """Gets the log_spec of this CreateGatewayRequest.  # noqa: E501


        :return: The log_spec of this CreateGatewayRequest.  # noqa: E501
        :rtype: LogSpecForCreateGatewayInput
        """
        return self._log_spec

    @log_spec.setter
    def log_spec(self, log_spec):
        """Sets the log_spec of this CreateGatewayRequest.


        :param log_spec: The log_spec of this CreateGatewayRequest.  # noqa: E501
        :type: LogSpecForCreateGatewayInput
        """

        self._log_spec = log_spec

    @property
    def monitor_spec(self):
        """Gets the monitor_spec of this CreateGatewayRequest.  # noqa: E501


        :return: The monitor_spec of this CreateGatewayRequest.  # noqa: E501
        :rtype: MonitorSpecForCreateGatewayInput
        """
        return self._monitor_spec

    @monitor_spec.setter
    def monitor_spec(self, monitor_spec):
        """Sets the monitor_spec of this CreateGatewayRequest.


        :param monitor_spec: The monitor_spec of this CreateGatewayRequest.  # noqa: E501
        :type: MonitorSpecForCreateGatewayInput
        """

        self._monitor_spec = monitor_spec

    @property
    def name(self):
        """Gets the name of this CreateGatewayRequest.  # noqa: E501


        :return: The name of this CreateGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateGatewayRequest.


        :param name: The name of this CreateGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def network_spec(self):
        """Gets the network_spec of this CreateGatewayRequest.  # noqa: E501


        :return: The network_spec of this CreateGatewayRequest.  # noqa: E501
        :rtype: NetworkSpecForCreateGatewayInput
        """
        return self._network_spec

    @network_spec.setter
    def network_spec(self, network_spec):
        """Sets the network_spec of this CreateGatewayRequest.


        :param network_spec: The network_spec of this CreateGatewayRequest.  # noqa: E501
        :type: NetworkSpecForCreateGatewayInput
        """

        self._network_spec = network_spec

    @property
    def project_name(self):
        """Gets the project_name of this CreateGatewayRequest.  # noqa: E501


        :return: The project_name of this CreateGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateGatewayRequest.


        :param project_name: The project_name of this CreateGatewayRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region(self):
        """Gets the region of this CreateGatewayRequest.  # noqa: E501


        :return: The region of this CreateGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this CreateGatewayRequest.


        :param region: The region of this CreateGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region is None:
            raise ValueError("Invalid value for `region`, must not be `None`")  # noqa: E501

        self._region = region

    @property
    def resource_spec(self):
        """Gets the resource_spec of this CreateGatewayRequest.  # noqa: E501


        :return: The resource_spec of this CreateGatewayRequest.  # noqa: E501
        :rtype: ResourceSpecForCreateGatewayInput
        """
        return self._resource_spec

    @resource_spec.setter
    def resource_spec(self, resource_spec):
        """Sets the resource_spec of this CreateGatewayRequest.


        :param resource_spec: The resource_spec of this CreateGatewayRequest.  # noqa: E501
        :type: ResourceSpecForCreateGatewayInput
        """

        self._resource_spec = resource_spec

    @property
    def tags(self):
        """Gets the tags of this CreateGatewayRequest.  # noqa: E501


        :return: The tags of this CreateGatewayRequest.  # noqa: E501
        :rtype: list[TagForCreateGatewayInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateGatewayRequest.


        :param tags: The tags of this CreateGatewayRequest.  # noqa: E501
        :type: list[TagForCreateGatewayInput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this CreateGatewayRequest.  # noqa: E501


        :return: The type of this CreateGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateGatewayRequest.


        :param type: The type of this CreateGatewayRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateGatewayRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateGatewayRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateGatewayRequest):
            return True

        return self.to_dict() != other.to_dict()
