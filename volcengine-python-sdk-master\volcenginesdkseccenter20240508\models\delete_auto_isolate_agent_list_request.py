# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteAutoIsolateAgentListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'basic_query': 'BasicQueryForDeleteAutoIsolateAgentListInput'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'basic_query': 'basicQuery'
    }

    def __init__(self, agent_id_list=None, basic_query=None, _configuration=None):  # noqa: E501
        """DeleteAutoIsolateAgentListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._basic_query = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if basic_query is not None:
            self.basic_query = basic_query

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this DeleteAutoIsolateAgentListRequest.  # noqa: E501


        :return: The agent_id_list of this DeleteAutoIsolateAgentListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this DeleteAutoIsolateAgentListRequest.


        :param agent_id_list: The agent_id_list of this DeleteAutoIsolateAgentListRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def basic_query(self):
        """Gets the basic_query of this DeleteAutoIsolateAgentListRequest.  # noqa: E501


        :return: The basic_query of this DeleteAutoIsolateAgentListRequest.  # noqa: E501
        :rtype: BasicQueryForDeleteAutoIsolateAgentListInput
        """
        return self._basic_query

    @basic_query.setter
    def basic_query(self, basic_query):
        """Sets the basic_query of this DeleteAutoIsolateAgentListRequest.


        :param basic_query: The basic_query of this DeleteAutoIsolateAgentListRequest.  # noqa: E501
        :type: BasicQueryForDeleteAutoIsolateAgentListInput
        """

        self._basic_query = basic_query

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteAutoIsolateAgentListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteAutoIsolateAgentListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteAutoIsolateAgentListRequest):
            return True

        return self.to_dict() != other.to_dict()
