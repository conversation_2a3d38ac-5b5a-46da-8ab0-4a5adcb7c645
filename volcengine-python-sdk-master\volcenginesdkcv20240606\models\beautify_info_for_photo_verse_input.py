# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BeautifyInfoForPhotoVerseInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dermabrasion': 'float',
        'whitening': 'float'
    }

    attribute_map = {
        'dermabrasion': 'dermabrasion',
        'whitening': 'whitening'
    }

    def __init__(self, dermabrasion=None, whitening=None, _configuration=None):  # noqa: E501
        """BeautifyInfoForPhotoVerseInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dermabrasion = None
        self._whitening = None
        self.discriminator = None

        if dermabrasion is not None:
            self.dermabrasion = dermabrasion
        if whitening is not None:
            self.whitening = whitening

    @property
    def dermabrasion(self):
        """Gets the dermabrasion of this BeautifyInfoForPhotoVerseInput.  # noqa: E501


        :return: The dermabrasion of this BeautifyInfoForPhotoVerseInput.  # noqa: E501
        :rtype: float
        """
        return self._dermabrasion

    @dermabrasion.setter
    def dermabrasion(self, dermabrasion):
        """Sets the dermabrasion of this BeautifyInfoForPhotoVerseInput.


        :param dermabrasion: The dermabrasion of this BeautifyInfoForPhotoVerseInput.  # noqa: E501
        :type: float
        """

        self._dermabrasion = dermabrasion

    @property
    def whitening(self):
        """Gets the whitening of this BeautifyInfoForPhotoVerseInput.  # noqa: E501


        :return: The whitening of this BeautifyInfoForPhotoVerseInput.  # noqa: E501
        :rtype: float
        """
        return self._whitening

    @whitening.setter
    def whitening(self, whitening):
        """Sets the whitening of this BeautifyInfoForPhotoVerseInput.


        :param whitening: The whitening of this BeautifyInfoForPhotoVerseInput.  # noqa: E501
        :type: float
        """

        self._whitening = whitening

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BeautifyInfoForPhotoVerseInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BeautifyInfoForPhotoVerseInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BeautifyInfoForPhotoVerseInput):
            return True

        return self.to_dict() != other.to_dict()
