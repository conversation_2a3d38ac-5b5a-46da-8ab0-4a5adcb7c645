# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVulnInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'cnnvd': 'str',
        'cpe_name': 'str',
        'cpe_version': 'str',
        'cve_id': 'str',
        'cve_list': 'list[CveListForGetVulnInfoOutput]',
        'cves': 'list[str]',
        'cvss': 'str',
        'cvss_vector': 'str',
        'cwe': 'str',
        'descript': 'str',
        'descript_en': 'str',
        'exploit_link': 'str',
        'if_exp': 'int',
        'if_rasp': 'bool',
        'level': 'str',
        'md5': 'str',
        'patch_url': 'str',
        'refer_urls': 'list[str]',
        'storage_name': 'str',
        'suggest': 'str',
        'suggest_en': 'str',
        'update_time': 'int',
        'upload_at': 'int',
        'upload_name': 'str',
        'vuln_name': 'str',
        'vuln_name_en': 'str',
        'cwpp_id': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'cnnvd': 'Cnnvd',
        'cpe_name': 'CpeName',
        'cpe_version': 'CpeVersion',
        'cve_id': 'CveID',
        'cve_list': 'CveList',
        'cves': 'Cves',
        'cvss': 'Cvss',
        'cvss_vector': 'CvssVector',
        'cwe': 'Cwe',
        'descript': 'Descript',
        'descript_en': 'DescriptEn',
        'exploit_link': 'ExploitLink',
        'if_exp': 'IfExp',
        'if_rasp': 'IfRasp',
        'level': 'Level',
        'md5': 'Md5',
        'patch_url': 'PatchUrl',
        'refer_urls': 'ReferUrls',
        'storage_name': 'StorageName',
        'suggest': 'Suggest',
        'suggest_en': 'SuggestEn',
        'update_time': 'UpdateTime',
        'upload_at': 'UploadAt',
        'upload_name': 'UploadName',
        'vuln_name': 'VulnName',
        'vuln_name_en': 'VulnNameEn',
        'cwpp_id': 'cwpp_id'
    }

    def __init__(self, action=None, cnnvd=None, cpe_name=None, cpe_version=None, cve_id=None, cve_list=None, cves=None, cvss=None, cvss_vector=None, cwe=None, descript=None, descript_en=None, exploit_link=None, if_exp=None, if_rasp=None, level=None, md5=None, patch_url=None, refer_urls=None, storage_name=None, suggest=None, suggest_en=None, update_time=None, upload_at=None, upload_name=None, vuln_name=None, vuln_name_en=None, cwpp_id=None, _configuration=None):  # noqa: E501
        """GetVulnInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._cnnvd = None
        self._cpe_name = None
        self._cpe_version = None
        self._cve_id = None
        self._cve_list = None
        self._cves = None
        self._cvss = None
        self._cvss_vector = None
        self._cwe = None
        self._descript = None
        self._descript_en = None
        self._exploit_link = None
        self._if_exp = None
        self._if_rasp = None
        self._level = None
        self._md5 = None
        self._patch_url = None
        self._refer_urls = None
        self._storage_name = None
        self._suggest = None
        self._suggest_en = None
        self._update_time = None
        self._upload_at = None
        self._upload_name = None
        self._vuln_name = None
        self._vuln_name_en = None
        self._cwpp_id = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if cnnvd is not None:
            self.cnnvd = cnnvd
        if cpe_name is not None:
            self.cpe_name = cpe_name
        if cpe_version is not None:
            self.cpe_version = cpe_version
        if cve_id is not None:
            self.cve_id = cve_id
        if cve_list is not None:
            self.cve_list = cve_list
        if cves is not None:
            self.cves = cves
        if cvss is not None:
            self.cvss = cvss
        if cvss_vector is not None:
            self.cvss_vector = cvss_vector
        if cwe is not None:
            self.cwe = cwe
        if descript is not None:
            self.descript = descript
        if descript_en is not None:
            self.descript_en = descript_en
        if exploit_link is not None:
            self.exploit_link = exploit_link
        if if_exp is not None:
            self.if_exp = if_exp
        if if_rasp is not None:
            self.if_rasp = if_rasp
        if level is not None:
            self.level = level
        if md5 is not None:
            self.md5 = md5
        if patch_url is not None:
            self.patch_url = patch_url
        if refer_urls is not None:
            self.refer_urls = refer_urls
        if storage_name is not None:
            self.storage_name = storage_name
        if suggest is not None:
            self.suggest = suggest
        if suggest_en is not None:
            self.suggest_en = suggest_en
        if update_time is not None:
            self.update_time = update_time
        if upload_at is not None:
            self.upload_at = upload_at
        if upload_name is not None:
            self.upload_name = upload_name
        if vuln_name is not None:
            self.vuln_name = vuln_name
        if vuln_name_en is not None:
            self.vuln_name_en = vuln_name_en
        if cwpp_id is not None:
            self.cwpp_id = cwpp_id

    @property
    def action(self):
        """Gets the action of this GetVulnInfoResponse.  # noqa: E501


        :return: The action of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this GetVulnInfoResponse.


        :param action: The action of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def cnnvd(self):
        """Gets the cnnvd of this GetVulnInfoResponse.  # noqa: E501


        :return: The cnnvd of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cnnvd

    @cnnvd.setter
    def cnnvd(self, cnnvd):
        """Sets the cnnvd of this GetVulnInfoResponse.


        :param cnnvd: The cnnvd of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cnnvd = cnnvd

    @property
    def cpe_name(self):
        """Gets the cpe_name of this GetVulnInfoResponse.  # noqa: E501


        :return: The cpe_name of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cpe_name

    @cpe_name.setter
    def cpe_name(self, cpe_name):
        """Sets the cpe_name of this GetVulnInfoResponse.


        :param cpe_name: The cpe_name of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cpe_name = cpe_name

    @property
    def cpe_version(self):
        """Gets the cpe_version of this GetVulnInfoResponse.  # noqa: E501


        :return: The cpe_version of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cpe_version

    @cpe_version.setter
    def cpe_version(self, cpe_version):
        """Sets the cpe_version of this GetVulnInfoResponse.


        :param cpe_version: The cpe_version of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cpe_version = cpe_version

    @property
    def cve_id(self):
        """Gets the cve_id of this GetVulnInfoResponse.  # noqa: E501


        :return: The cve_id of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this GetVulnInfoResponse.


        :param cve_id: The cve_id of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def cve_list(self):
        """Gets the cve_list of this GetVulnInfoResponse.  # noqa: E501


        :return: The cve_list of this GetVulnInfoResponse.  # noqa: E501
        :rtype: list[CveListForGetVulnInfoOutput]
        """
        return self._cve_list

    @cve_list.setter
    def cve_list(self, cve_list):
        """Sets the cve_list of this GetVulnInfoResponse.


        :param cve_list: The cve_list of this GetVulnInfoResponse.  # noqa: E501
        :type: list[CveListForGetVulnInfoOutput]
        """

        self._cve_list = cve_list

    @property
    def cves(self):
        """Gets the cves of this GetVulnInfoResponse.  # noqa: E501


        :return: The cves of this GetVulnInfoResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cves

    @cves.setter
    def cves(self, cves):
        """Sets the cves of this GetVulnInfoResponse.


        :param cves: The cves of this GetVulnInfoResponse.  # noqa: E501
        :type: list[str]
        """

        self._cves = cves

    @property
    def cvss(self):
        """Gets the cvss of this GetVulnInfoResponse.  # noqa: E501


        :return: The cvss of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cvss

    @cvss.setter
    def cvss(self, cvss):
        """Sets the cvss of this GetVulnInfoResponse.


        :param cvss: The cvss of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cvss = cvss

    @property
    def cvss_vector(self):
        """Gets the cvss_vector of this GetVulnInfoResponse.  # noqa: E501


        :return: The cvss_vector of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cvss_vector

    @cvss_vector.setter
    def cvss_vector(self, cvss_vector):
        """Sets the cvss_vector of this GetVulnInfoResponse.


        :param cvss_vector: The cvss_vector of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cvss_vector = cvss_vector

    @property
    def cwe(self):
        """Gets the cwe of this GetVulnInfoResponse.  # noqa: E501


        :return: The cwe of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cwe

    @cwe.setter
    def cwe(self, cwe):
        """Sets the cwe of this GetVulnInfoResponse.


        :param cwe: The cwe of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cwe = cwe

    @property
    def descript(self):
        """Gets the descript of this GetVulnInfoResponse.  # noqa: E501


        :return: The descript of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._descript

    @descript.setter
    def descript(self, descript):
        """Sets the descript of this GetVulnInfoResponse.


        :param descript: The descript of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._descript = descript

    @property
    def descript_en(self):
        """Gets the descript_en of this GetVulnInfoResponse.  # noqa: E501


        :return: The descript_en of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._descript_en

    @descript_en.setter
    def descript_en(self, descript_en):
        """Sets the descript_en of this GetVulnInfoResponse.


        :param descript_en: The descript_en of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._descript_en = descript_en

    @property
    def exploit_link(self):
        """Gets the exploit_link of this GetVulnInfoResponse.  # noqa: E501


        :return: The exploit_link of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._exploit_link

    @exploit_link.setter
    def exploit_link(self, exploit_link):
        """Sets the exploit_link of this GetVulnInfoResponse.


        :param exploit_link: The exploit_link of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._exploit_link = exploit_link

    @property
    def if_exp(self):
        """Gets the if_exp of this GetVulnInfoResponse.  # noqa: E501


        :return: The if_exp of this GetVulnInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._if_exp

    @if_exp.setter
    def if_exp(self, if_exp):
        """Sets the if_exp of this GetVulnInfoResponse.


        :param if_exp: The if_exp of this GetVulnInfoResponse.  # noqa: E501
        :type: int
        """

        self._if_exp = if_exp

    @property
    def if_rasp(self):
        """Gets the if_rasp of this GetVulnInfoResponse.  # noqa: E501


        :return: The if_rasp of this GetVulnInfoResponse.  # noqa: E501
        :rtype: bool
        """
        return self._if_rasp

    @if_rasp.setter
    def if_rasp(self, if_rasp):
        """Sets the if_rasp of this GetVulnInfoResponse.


        :param if_rasp: The if_rasp of this GetVulnInfoResponse.  # noqa: E501
        :type: bool
        """

        self._if_rasp = if_rasp

    @property
    def level(self):
        """Gets the level of this GetVulnInfoResponse.  # noqa: E501


        :return: The level of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this GetVulnInfoResponse.


        :param level: The level of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def md5(self):
        """Gets the md5 of this GetVulnInfoResponse.  # noqa: E501


        :return: The md5 of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._md5

    @md5.setter
    def md5(self, md5):
        """Sets the md5 of this GetVulnInfoResponse.


        :param md5: The md5 of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._md5 = md5

    @property
    def patch_url(self):
        """Gets the patch_url of this GetVulnInfoResponse.  # noqa: E501


        :return: The patch_url of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._patch_url

    @patch_url.setter
    def patch_url(self, patch_url):
        """Sets the patch_url of this GetVulnInfoResponse.


        :param patch_url: The patch_url of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._patch_url = patch_url

    @property
    def refer_urls(self):
        """Gets the refer_urls of this GetVulnInfoResponse.  # noqa: E501


        :return: The refer_urls of this GetVulnInfoResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._refer_urls

    @refer_urls.setter
    def refer_urls(self, refer_urls):
        """Sets the refer_urls of this GetVulnInfoResponse.


        :param refer_urls: The refer_urls of this GetVulnInfoResponse.  # noqa: E501
        :type: list[str]
        """

        self._refer_urls = refer_urls

    @property
    def storage_name(self):
        """Gets the storage_name of this GetVulnInfoResponse.  # noqa: E501


        :return: The storage_name of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._storage_name

    @storage_name.setter
    def storage_name(self, storage_name):
        """Sets the storage_name of this GetVulnInfoResponse.


        :param storage_name: The storage_name of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._storage_name = storage_name

    @property
    def suggest(self):
        """Gets the suggest of this GetVulnInfoResponse.  # noqa: E501


        :return: The suggest of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._suggest

    @suggest.setter
    def suggest(self, suggest):
        """Sets the suggest of this GetVulnInfoResponse.


        :param suggest: The suggest of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._suggest = suggest

    @property
    def suggest_en(self):
        """Gets the suggest_en of this GetVulnInfoResponse.  # noqa: E501


        :return: The suggest_en of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._suggest_en

    @suggest_en.setter
    def suggest_en(self, suggest_en):
        """Sets the suggest_en of this GetVulnInfoResponse.


        :param suggest_en: The suggest_en of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._suggest_en = suggest_en

    @property
    def update_time(self):
        """Gets the update_time of this GetVulnInfoResponse.  # noqa: E501


        :return: The update_time of this GetVulnInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetVulnInfoResponse.


        :param update_time: The update_time of this GetVulnInfoResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def upload_at(self):
        """Gets the upload_at of this GetVulnInfoResponse.  # noqa: E501


        :return: The upload_at of this GetVulnInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._upload_at

    @upload_at.setter
    def upload_at(self, upload_at):
        """Sets the upload_at of this GetVulnInfoResponse.


        :param upload_at: The upload_at of this GetVulnInfoResponse.  # noqa: E501
        :type: int
        """

        self._upload_at = upload_at

    @property
    def upload_name(self):
        """Gets the upload_name of this GetVulnInfoResponse.  # noqa: E501


        :return: The upload_name of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._upload_name

    @upload_name.setter
    def upload_name(self, upload_name):
        """Sets the upload_name of this GetVulnInfoResponse.


        :param upload_name: The upload_name of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._upload_name = upload_name

    @property
    def vuln_name(self):
        """Gets the vuln_name of this GetVulnInfoResponse.  # noqa: E501


        :return: The vuln_name of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this GetVulnInfoResponse.


        :param vuln_name: The vuln_name of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    @property
    def vuln_name_en(self):
        """Gets the vuln_name_en of this GetVulnInfoResponse.  # noqa: E501


        :return: The vuln_name_en of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name_en

    @vuln_name_en.setter
    def vuln_name_en(self, vuln_name_en):
        """Sets the vuln_name_en of this GetVulnInfoResponse.


        :param vuln_name_en: The vuln_name_en of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._vuln_name_en = vuln_name_en

    @property
    def cwpp_id(self):
        """Gets the cwpp_id of this GetVulnInfoResponse.  # noqa: E501


        :return: The cwpp_id of this GetVulnInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._cwpp_id

    @cwpp_id.setter
    def cwpp_id(self, cwpp_id):
        """Sets the cwpp_id of this GetVulnInfoResponse.


        :param cwpp_id: The cwpp_id of this GetVulnInfoResponse.  # noqa: E501
        :type: str
        """

        self._cwpp_id = cwpp_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVulnInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVulnInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVulnInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
