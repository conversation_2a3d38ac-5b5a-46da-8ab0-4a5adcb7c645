# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VIFListForListVirtualInterfaceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_point': 'str',
        'account_id': 'int',
        'create_time': 'str',
        'cross_account': 'bool',
        'dxp_instance_id': 'str',
        'dxp_instance_name': 'str',
        'instance_id': 'str',
        'local_ip': 'str',
        'max_bandwidth': 'int',
        'name': 'str',
        'peer_ip': 'str',
        'state': 'str',
        'vifvgw_instance_id': 'str',
        'vlan_id': 'int'
    }

    attribute_map = {
        'access_point': 'AccessPoint',
        'account_id': 'AccountId',
        'create_time': 'CreateTime',
        'cross_account': 'CrossAccount',
        'dxp_instance_id': 'DXPInstanceId',
        'dxp_instance_name': 'DXPInstanceName',
        'instance_id': 'InstanceId',
        'local_ip': 'LocalIP',
        'max_bandwidth': 'MaxBandwidth',
        'name': 'Name',
        'peer_ip': 'PeerIP',
        'state': 'State',
        'vifvgw_instance_id': 'VIFVGWInstanceId',
        'vlan_id': 'VlanId'
    }

    def __init__(self, access_point=None, account_id=None, create_time=None, cross_account=None, dxp_instance_id=None, dxp_instance_name=None, instance_id=None, local_ip=None, max_bandwidth=None, name=None, peer_ip=None, state=None, vifvgw_instance_id=None, vlan_id=None, _configuration=None):  # noqa: E501
        """VIFListForListVirtualInterfaceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_point = None
        self._account_id = None
        self._create_time = None
        self._cross_account = None
        self._dxp_instance_id = None
        self._dxp_instance_name = None
        self._instance_id = None
        self._local_ip = None
        self._max_bandwidth = None
        self._name = None
        self._peer_ip = None
        self._state = None
        self._vifvgw_instance_id = None
        self._vlan_id = None
        self.discriminator = None

        if access_point is not None:
            self.access_point = access_point
        if account_id is not None:
            self.account_id = account_id
        if create_time is not None:
            self.create_time = create_time
        if cross_account is not None:
            self.cross_account = cross_account
        if dxp_instance_id is not None:
            self.dxp_instance_id = dxp_instance_id
        if dxp_instance_name is not None:
            self.dxp_instance_name = dxp_instance_name
        if instance_id is not None:
            self.instance_id = instance_id
        if local_ip is not None:
            self.local_ip = local_ip
        if max_bandwidth is not None:
            self.max_bandwidth = max_bandwidth
        if name is not None:
            self.name = name
        if peer_ip is not None:
            self.peer_ip = peer_ip
        if state is not None:
            self.state = state
        if vifvgw_instance_id is not None:
            self.vifvgw_instance_id = vifvgw_instance_id
        if vlan_id is not None:
            self.vlan_id = vlan_id

    @property
    def access_point(self):
        """Gets the access_point of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The access_point of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_point

    @access_point.setter
    def access_point(self, access_point):
        """Sets the access_point of this VIFListForListVirtualInterfaceOutput.


        :param access_point: The access_point of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._access_point = access_point

    @property
    def account_id(self):
        """Gets the account_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The account_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this VIFListForListVirtualInterfaceOutput.


        :param account_id: The account_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def create_time(self):
        """Gets the create_time of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The create_time of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this VIFListForListVirtualInterfaceOutput.


        :param create_time: The create_time of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def cross_account(self):
        """Gets the cross_account of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The cross_account of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cross_account

    @cross_account.setter
    def cross_account(self, cross_account):
        """Sets the cross_account of this VIFListForListVirtualInterfaceOutput.


        :param cross_account: The cross_account of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: bool
        """

        self._cross_account = cross_account

    @property
    def dxp_instance_id(self):
        """Gets the dxp_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The dxp_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._dxp_instance_id

    @dxp_instance_id.setter
    def dxp_instance_id(self, dxp_instance_id):
        """Sets the dxp_instance_id of this VIFListForListVirtualInterfaceOutput.


        :param dxp_instance_id: The dxp_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._dxp_instance_id = dxp_instance_id

    @property
    def dxp_instance_name(self):
        """Gets the dxp_instance_name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The dxp_instance_name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._dxp_instance_name

    @dxp_instance_name.setter
    def dxp_instance_name(self, dxp_instance_name):
        """Sets the dxp_instance_name of this VIFListForListVirtualInterfaceOutput.


        :param dxp_instance_name: The dxp_instance_name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._dxp_instance_name = dxp_instance_name

    @property
    def instance_id(self):
        """Gets the instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this VIFListForListVirtualInterfaceOutput.


        :param instance_id: The instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def local_ip(self):
        """Gets the local_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The local_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._local_ip

    @local_ip.setter
    def local_ip(self, local_ip):
        """Sets the local_ip of this VIFListForListVirtualInterfaceOutput.


        :param local_ip: The local_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._local_ip = local_ip

    @property
    def max_bandwidth(self):
        """Gets the max_bandwidth of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The max_bandwidth of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_bandwidth

    @max_bandwidth.setter
    def max_bandwidth(self, max_bandwidth):
        """Sets the max_bandwidth of this VIFListForListVirtualInterfaceOutput.


        :param max_bandwidth: The max_bandwidth of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: int
        """

        self._max_bandwidth = max_bandwidth

    @property
    def name(self):
        """Gets the name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this VIFListForListVirtualInterfaceOutput.


        :param name: The name of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def peer_ip(self):
        """Gets the peer_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The peer_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._peer_ip

    @peer_ip.setter
    def peer_ip(self, peer_ip):
        """Sets the peer_ip of this VIFListForListVirtualInterfaceOutput.


        :param peer_ip: The peer_ip of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._peer_ip = peer_ip

    @property
    def state(self):
        """Gets the state of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The state of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this VIFListForListVirtualInterfaceOutput.


        :param state: The state of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def vifvgw_instance_id(self):
        """Gets the vifvgw_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The vifvgw_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._vifvgw_instance_id

    @vifvgw_instance_id.setter
    def vifvgw_instance_id(self, vifvgw_instance_id):
        """Sets the vifvgw_instance_id of this VIFListForListVirtualInterfaceOutput.


        :param vifvgw_instance_id: The vifvgw_instance_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: str
        """

        self._vifvgw_instance_id = vifvgw_instance_id

    @property
    def vlan_id(self):
        """Gets the vlan_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501


        :return: The vlan_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :rtype: int
        """
        return self._vlan_id

    @vlan_id.setter
    def vlan_id(self, vlan_id):
        """Sets the vlan_id of this VIFListForListVirtualInterfaceOutput.


        :param vlan_id: The vlan_id of this VIFListForListVirtualInterfaceOutput.  # noqa: E501
        :type: int
        """

        self._vlan_id = vlan_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VIFListForListVirtualInterfaceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VIFListForListVirtualInterfaceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VIFListForListVirtualInterfaceOutput):
            return True

        return self.to_dict() != other.to_dict()
