# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ZoneMappingForModifyLoadBalancerTypeInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocation_id': 'str',
        'eip_type': 'str',
        'pop_locations': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'allocation_id': 'AllocationId',
        'eip_type': 'EipType',
        'pop_locations': 'PopLocations',
        'zone_id': 'ZoneId'
    }

    def __init__(self, allocation_id=None, eip_type=None, pop_locations=None, zone_id=None, _configuration=None):  # noqa: E501
        """ZoneMappingForModifyLoadBalancerTypeInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocation_id = None
        self._eip_type = None
        self._pop_locations = None
        self._zone_id = None
        self.discriminator = None

        if allocation_id is not None:
            self.allocation_id = allocation_id
        if eip_type is not None:
            self.eip_type = eip_type
        if pop_locations is not None:
            self.pop_locations = pop_locations
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def allocation_id(self):
        """Gets the allocation_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501


        :return: The allocation_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :rtype: str
        """
        return self._allocation_id

    @allocation_id.setter
    def allocation_id(self, allocation_id):
        """Sets the allocation_id of this ZoneMappingForModifyLoadBalancerTypeInput.


        :param allocation_id: The allocation_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :type: str
        """

        self._allocation_id = allocation_id

    @property
    def eip_type(self):
        """Gets the eip_type of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501


        :return: The eip_type of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :rtype: str
        """
        return self._eip_type

    @eip_type.setter
    def eip_type(self, eip_type):
        """Sets the eip_type of this ZoneMappingForModifyLoadBalancerTypeInput.


        :param eip_type: The eip_type of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :type: str
        """

        self._eip_type = eip_type

    @property
    def pop_locations(self):
        """Gets the pop_locations of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501


        :return: The pop_locations of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :rtype: str
        """
        return self._pop_locations

    @pop_locations.setter
    def pop_locations(self, pop_locations):
        """Sets the pop_locations of this ZoneMappingForModifyLoadBalancerTypeInput.


        :param pop_locations: The pop_locations of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :type: str
        """

        self._pop_locations = pop_locations

    @property
    def zone_id(self):
        """Gets the zone_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501


        :return: The zone_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ZoneMappingForModifyLoadBalancerTypeInput.


        :param zone_id: The zone_id of this ZoneMappingForModifyLoadBalancerTypeInput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ZoneMappingForModifyLoadBalancerTypeInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ZoneMappingForModifyLoadBalancerTypeInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ZoneMappingForModifyLoadBalancerTypeInput):
            return True

        return self.to_dict() != other.to_dict()
