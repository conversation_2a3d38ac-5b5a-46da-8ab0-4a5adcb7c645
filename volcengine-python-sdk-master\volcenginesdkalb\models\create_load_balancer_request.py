# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateLoadBalancerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address_ip_version': 'str',
        'bandwidth_package_id': 'str',
        'delete_protection': 'str',
        'description': 'str',
        'eip_billing_config': 'EipBillingConfigForCreateLoadBalancerInput',
        'ipv6_eip_billing_config': 'Ipv6EipBillingConfigForCreateLoadBalancerInput',
        'load_balancer_billing_type': 'int',
        'load_balancer_edition': 'str',
        'load_balancer_name': 'str',
        'modification_protection_reason': 'str',
        'modification_protection_status': 'str',
        'project_name': 'str',
        'region_id': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateLoadBalancerInput]',
        'type': 'str',
        'vpc_id': 'str',
        'zone_mappings': 'list[ZoneMappingForCreateLoadBalancerInput]'
    }

    attribute_map = {
        'address_ip_version': 'AddressIpVersion',
        'bandwidth_package_id': 'BandwidthPackageId',
        'delete_protection': 'DeleteProtection',
        'description': 'Description',
        'eip_billing_config': 'EipBillingConfig',
        'ipv6_eip_billing_config': 'Ipv6EipBillingConfig',
        'load_balancer_billing_type': 'LoadBalancerBillingType',
        'load_balancer_edition': 'LoadBalancerEdition',
        'load_balancer_name': 'LoadBalancerName',
        'modification_protection_reason': 'ModificationProtectionReason',
        'modification_protection_status': 'ModificationProtectionStatus',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'type': 'Type',
        'vpc_id': 'VpcId',
        'zone_mappings': 'ZoneMappings'
    }

    def __init__(self, address_ip_version=None, bandwidth_package_id=None, delete_protection=None, description=None, eip_billing_config=None, ipv6_eip_billing_config=None, load_balancer_billing_type=None, load_balancer_edition=None, load_balancer_name=None, modification_protection_reason=None, modification_protection_status=None, project_name=None, region_id=None, subnet_id=None, tags=None, type=None, vpc_id=None, zone_mappings=None, _configuration=None):  # noqa: E501
        """CreateLoadBalancerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address_ip_version = None
        self._bandwidth_package_id = None
        self._delete_protection = None
        self._description = None
        self._eip_billing_config = None
        self._ipv6_eip_billing_config = None
        self._load_balancer_billing_type = None
        self._load_balancer_edition = None
        self._load_balancer_name = None
        self._modification_protection_reason = None
        self._modification_protection_status = None
        self._project_name = None
        self._region_id = None
        self._subnet_id = None
        self._tags = None
        self._type = None
        self._vpc_id = None
        self._zone_mappings = None
        self.discriminator = None

        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if delete_protection is not None:
            self.delete_protection = delete_protection
        if description is not None:
            self.description = description
        if eip_billing_config is not None:
            self.eip_billing_config = eip_billing_config
        if ipv6_eip_billing_config is not None:
            self.ipv6_eip_billing_config = ipv6_eip_billing_config
        if load_balancer_billing_type is not None:
            self.load_balancer_billing_type = load_balancer_billing_type
        if load_balancer_edition is not None:
            self.load_balancer_edition = load_balancer_edition
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if modification_protection_reason is not None:
            self.modification_protection_reason = modification_protection_reason
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status
        if project_name is not None:
            self.project_name = project_name
        self.region_id = region_id
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        self.type = type
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_mappings is not None:
            self.zone_mappings = zone_mappings

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The address_ip_version of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this CreateLoadBalancerRequest.


        :param address_ip_version: The address_ip_version of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The bandwidth_package_id of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this CreateLoadBalancerRequest.


        :param bandwidth_package_id: The bandwidth_package_id of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def delete_protection(self):
        """Gets the delete_protection of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The delete_protection of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._delete_protection

    @delete_protection.setter
    def delete_protection(self, delete_protection):
        """Sets the delete_protection of this CreateLoadBalancerRequest.


        :param delete_protection: The delete_protection of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._delete_protection = delete_protection

    @property
    def description(self):
        """Gets the description of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The description of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateLoadBalancerRequest.


        :param description: The description of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def eip_billing_config(self):
        """Gets the eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: EipBillingConfigForCreateLoadBalancerInput
        """
        return self._eip_billing_config

    @eip_billing_config.setter
    def eip_billing_config(self, eip_billing_config):
        """Sets the eip_billing_config of this CreateLoadBalancerRequest.


        :param eip_billing_config: The eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501
        :type: EipBillingConfigForCreateLoadBalancerInput
        """

        self._eip_billing_config = eip_billing_config

    @property
    def ipv6_eip_billing_config(self):
        """Gets the ipv6_eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The ipv6_eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: Ipv6EipBillingConfigForCreateLoadBalancerInput
        """
        return self._ipv6_eip_billing_config

    @ipv6_eip_billing_config.setter
    def ipv6_eip_billing_config(self, ipv6_eip_billing_config):
        """Sets the ipv6_eip_billing_config of this CreateLoadBalancerRequest.


        :param ipv6_eip_billing_config: The ipv6_eip_billing_config of this CreateLoadBalancerRequest.  # noqa: E501
        :type: Ipv6EipBillingConfigForCreateLoadBalancerInput
        """

        self._ipv6_eip_billing_config = ipv6_eip_billing_config

    @property
    def load_balancer_billing_type(self):
        """Gets the load_balancer_billing_type of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The load_balancer_billing_type of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: int
        """
        return self._load_balancer_billing_type

    @load_balancer_billing_type.setter
    def load_balancer_billing_type(self, load_balancer_billing_type):
        """Sets the load_balancer_billing_type of this CreateLoadBalancerRequest.


        :param load_balancer_billing_type: The load_balancer_billing_type of this CreateLoadBalancerRequest.  # noqa: E501
        :type: int
        """

        self._load_balancer_billing_type = load_balancer_billing_type

    @property
    def load_balancer_edition(self):
        """Gets the load_balancer_edition of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The load_balancer_edition of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_edition

    @load_balancer_edition.setter
    def load_balancer_edition(self, load_balancer_edition):
        """Sets the load_balancer_edition of this CreateLoadBalancerRequest.


        :param load_balancer_edition: The load_balancer_edition of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_edition = load_balancer_edition

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The load_balancer_name of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this CreateLoadBalancerRequest.


        :param load_balancer_name: The load_balancer_name of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def modification_protection_reason(self):
        """Gets the modification_protection_reason of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The modification_protection_reason of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_reason

    @modification_protection_reason.setter
    def modification_protection_reason(self, modification_protection_reason):
        """Sets the modification_protection_reason of this CreateLoadBalancerRequest.


        :param modification_protection_reason: The modification_protection_reason of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_reason = modification_protection_reason

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The modification_protection_status of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this CreateLoadBalancerRequest.


        :param modification_protection_status: The modification_protection_status of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    @property
    def project_name(self):
        """Gets the project_name of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The project_name of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateLoadBalancerRequest.


        :param project_name: The project_name of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The region_id of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this CreateLoadBalancerRequest.


        :param region_id: The region_id of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and region_id is None:
            raise ValueError("Invalid value for `region_id`, must not be `None`")  # noqa: E501

        self._region_id = region_id

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The subnet_id of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateLoadBalancerRequest.


        :param subnet_id: The subnet_id of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The tags of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: list[TagForCreateLoadBalancerInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateLoadBalancerRequest.


        :param tags: The tags of this CreateLoadBalancerRequest.  # noqa: E501
        :type: list[TagForCreateLoadBalancerInput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The type of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateLoadBalancerRequest.


        :param type: The type of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The vpc_id of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateLoadBalancerRequest.


        :param vpc_id: The vpc_id of this CreateLoadBalancerRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_mappings(self):
        """Gets the zone_mappings of this CreateLoadBalancerRequest.  # noqa: E501


        :return: The zone_mappings of this CreateLoadBalancerRequest.  # noqa: E501
        :rtype: list[ZoneMappingForCreateLoadBalancerInput]
        """
        return self._zone_mappings

    @zone_mappings.setter
    def zone_mappings(self, zone_mappings):
        """Sets the zone_mappings of this CreateLoadBalancerRequest.


        :param zone_mappings: The zone_mappings of this CreateLoadBalancerRequest.  # noqa: E501
        :type: list[ZoneMappingForCreateLoadBalancerInput]
        """

        self._zone_mappings = zone_mappings

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateLoadBalancerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateLoadBalancerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateLoadBalancerRequest):
            return True

        return self.to_dict() != other.to_dict()
