# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DNSConfigForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_dig': 'bool',
        'record_type': 'int',
        'search_type': 'int',
        'server': 'str',
        'server_type': 'int',
        'timeout': 'int'
    }

    attribute_map = {
        'enable_dig': 'EnableDig',
        'record_type': 'RecordType',
        'search_type': 'SearchType',
        'server': 'Server',
        'server_type': 'ServerType',
        'timeout': 'Timeout'
    }

    def __init__(self, enable_dig=None, record_type=None, search_type=None, server=None, server_type=None, timeout=None, _configuration=None):  # noqa: E501
        """DNSConfigForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_dig = None
        self._record_type = None
        self._search_type = None
        self._server = None
        self._server_type = None
        self._timeout = None
        self.discriminator = None

        if enable_dig is not None:
            self.enable_dig = enable_dig
        if record_type is not None:
            self.record_type = record_type
        if search_type is not None:
            self.search_type = search_type
        if server is not None:
            self.server = server
        if server_type is not None:
            self.server_type = server_type
        if timeout is not None:
            self.timeout = timeout

    @property
    def enable_dig(self):
        """Gets the enable_dig of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The enable_dig of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_dig

    @enable_dig.setter
    def enable_dig(self, enable_dig):
        """Sets the enable_dig of this DNSConfigForCreateTaskInput.


        :param enable_dig: The enable_dig of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: bool
        """

        self._enable_dig = enable_dig

    @property
    def record_type(self):
        """Gets the record_type of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The record_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._record_type

    @record_type.setter
    def record_type(self, record_type):
        """Sets the record_type of this DNSConfigForCreateTaskInput.


        :param record_type: The record_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._record_type = record_type

    @property
    def search_type(self):
        """Gets the search_type of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The search_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._search_type

    @search_type.setter
    def search_type(self, search_type):
        """Sets the search_type of this DNSConfigForCreateTaskInput.


        :param search_type: The search_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._search_type = search_type

    @property
    def server(self):
        """Gets the server of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The server of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._server

    @server.setter
    def server(self, server):
        """Sets the server of this DNSConfigForCreateTaskInput.


        :param server: The server of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: str
        """

        self._server = server

    @property
    def server_type(self):
        """Gets the server_type of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The server_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._server_type

    @server_type.setter
    def server_type(self, server_type):
        """Sets the server_type of this DNSConfigForCreateTaskInput.


        :param server_type: The server_type of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._server_type = server_type

    @property
    def timeout(self):
        """Gets the timeout of this DNSConfigForCreateTaskInput.  # noqa: E501


        :return: The timeout of this DNSConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this DNSConfigForCreateTaskInput.


        :param timeout: The timeout of this DNSConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DNSConfigForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DNSConfigForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DNSConfigForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
