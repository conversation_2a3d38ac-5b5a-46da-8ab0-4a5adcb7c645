# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PortForGetDevInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apig_private_url': 'str',
        'apig_public_url': 'str',
        'enable_public_network_access': 'bool',
        'eni_ip': 'str',
        'eni_port': 'int',
        'external_ip': 'str',
        'external_port': 'int',
        'name': 'str',
        'status': 'StatusForGetDevInstanceOutput',
        'type': 'str'
    }

    attribute_map = {
        'apig_private_url': 'APIGPrivateUrl',
        'apig_public_url': 'APIGPublicUrl',
        'enable_public_network_access': 'EnablePublicNetworkAccess',
        'eni_ip': 'EniIp',
        'eni_port': 'EniPort',
        'external_ip': 'ExternalIp',
        'external_port': 'ExternalPort',
        'name': 'Name',
        'status': 'Status',
        'type': 'Type'
    }

    def __init__(self, apig_private_url=None, apig_public_url=None, enable_public_network_access=None, eni_ip=None, eni_port=None, external_ip=None, external_port=None, name=None, status=None, type=None, _configuration=None):  # noqa: E501
        """PortForGetDevInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apig_private_url = None
        self._apig_public_url = None
        self._enable_public_network_access = None
        self._eni_ip = None
        self._eni_port = None
        self._external_ip = None
        self._external_port = None
        self._name = None
        self._status = None
        self._type = None
        self.discriminator = None

        if apig_private_url is not None:
            self.apig_private_url = apig_private_url
        if apig_public_url is not None:
            self.apig_public_url = apig_public_url
        if enable_public_network_access is not None:
            self.enable_public_network_access = enable_public_network_access
        if eni_ip is not None:
            self.eni_ip = eni_ip
        if eni_port is not None:
            self.eni_port = eni_port
        if external_ip is not None:
            self.external_ip = external_ip
        if external_port is not None:
            self.external_port = external_port
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type

    @property
    def apig_private_url(self):
        """Gets the apig_private_url of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The apig_private_url of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._apig_private_url

    @apig_private_url.setter
    def apig_private_url(self, apig_private_url):
        """Sets the apig_private_url of this PortForGetDevInstanceOutput.


        :param apig_private_url: The apig_private_url of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._apig_private_url = apig_private_url

    @property
    def apig_public_url(self):
        """Gets the apig_public_url of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The apig_public_url of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._apig_public_url

    @apig_public_url.setter
    def apig_public_url(self, apig_public_url):
        """Sets the apig_public_url of this PortForGetDevInstanceOutput.


        :param apig_public_url: The apig_public_url of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._apig_public_url = apig_public_url

    @property
    def enable_public_network_access(self):
        """Gets the enable_public_network_access of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The enable_public_network_access of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_public_network_access

    @enable_public_network_access.setter
    def enable_public_network_access(self, enable_public_network_access):
        """Sets the enable_public_network_access of this PortForGetDevInstanceOutput.


        :param enable_public_network_access: The enable_public_network_access of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: bool
        """

        self._enable_public_network_access = enable_public_network_access

    @property
    def eni_ip(self):
        """Gets the eni_ip of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The eni_ip of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._eni_ip

    @eni_ip.setter
    def eni_ip(self, eni_ip):
        """Sets the eni_ip of this PortForGetDevInstanceOutput.


        :param eni_ip: The eni_ip of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._eni_ip = eni_ip

    @property
    def eni_port(self):
        """Gets the eni_port of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The eni_port of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: int
        """
        return self._eni_port

    @eni_port.setter
    def eni_port(self, eni_port):
        """Sets the eni_port of this PortForGetDevInstanceOutput.


        :param eni_port: The eni_port of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: int
        """

        self._eni_port = eni_port

    @property
    def external_ip(self):
        """Gets the external_ip of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The external_ip of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_ip

    @external_ip.setter
    def external_ip(self, external_ip):
        """Sets the external_ip of this PortForGetDevInstanceOutput.


        :param external_ip: The external_ip of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._external_ip = external_ip

    @property
    def external_port(self):
        """Gets the external_port of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The external_port of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: int
        """
        return self._external_port

    @external_port.setter
    def external_port(self, external_port):
        """Sets the external_port of this PortForGetDevInstanceOutput.


        :param external_port: The external_port of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: int
        """

        self._external_port = external_port

    @property
    def name(self):
        """Gets the name of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The name of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this PortForGetDevInstanceOutput.


        :param name: The name of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The status of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: StatusForGetDevInstanceOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this PortForGetDevInstanceOutput.


        :param status: The status of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: StatusForGetDevInstanceOutput
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this PortForGetDevInstanceOutput.  # noqa: E501


        :return: The type of this PortForGetDevInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this PortForGetDevInstanceOutput.


        :param type: The type of this PortForGetDevInstanceOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PortForGetDevInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PortForGetDevInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PortForGetDevInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
