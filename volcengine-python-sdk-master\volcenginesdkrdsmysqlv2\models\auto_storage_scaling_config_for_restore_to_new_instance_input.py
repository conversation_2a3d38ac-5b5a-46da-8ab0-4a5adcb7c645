# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AutoStorageScalingConfigForRestoreToNewInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_storage_auto_scale': 'bool',
        'storage_threshold': 'int',
        'storage_upper_bound': 'int'
    }

    attribute_map = {
        'enable_storage_auto_scale': 'EnableStorageAutoScale',
        'storage_threshold': 'StorageThreshold',
        'storage_upper_bound': 'StorageUpperBound'
    }

    def __init__(self, enable_storage_auto_scale=None, storage_threshold=None, storage_upper_bound=None, _configuration=None):  # noqa: E501
        """AutoStorageScalingConfigForRestoreToNewInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_storage_auto_scale = None
        self._storage_threshold = None
        self._storage_upper_bound = None
        self.discriminator = None

        if enable_storage_auto_scale is not None:
            self.enable_storage_auto_scale = enable_storage_auto_scale
        if storage_threshold is not None:
            self.storage_threshold = storage_threshold
        if storage_upper_bound is not None:
            self.storage_upper_bound = storage_upper_bound

    @property
    def enable_storage_auto_scale(self):
        """Gets the enable_storage_auto_scale of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501


        :return: The enable_storage_auto_scale of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_storage_auto_scale

    @enable_storage_auto_scale.setter
    def enable_storage_auto_scale(self, enable_storage_auto_scale):
        """Sets the enable_storage_auto_scale of this AutoStorageScalingConfigForRestoreToNewInstanceInput.


        :param enable_storage_auto_scale: The enable_storage_auto_scale of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :type: bool
        """

        self._enable_storage_auto_scale = enable_storage_auto_scale

    @property
    def storage_threshold(self):
        """Gets the storage_threshold of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501


        :return: The storage_threshold of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._storage_threshold

    @storage_threshold.setter
    def storage_threshold(self, storage_threshold):
        """Sets the storage_threshold of this AutoStorageScalingConfigForRestoreToNewInstanceInput.


        :param storage_threshold: The storage_threshold of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :type: int
        """

        self._storage_threshold = storage_threshold

    @property
    def storage_upper_bound(self):
        """Gets the storage_upper_bound of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501


        :return: The storage_upper_bound of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._storage_upper_bound

    @storage_upper_bound.setter
    def storage_upper_bound(self, storage_upper_bound):
        """Sets the storage_upper_bound of this AutoStorageScalingConfigForRestoreToNewInstanceInput.


        :param storage_upper_bound: The storage_upper_bound of this AutoStorageScalingConfigForRestoreToNewInstanceInput.  # noqa: E501
        :type: int
        """

        self._storage_upper_bound = storage_upper_bound

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AutoStorageScalingConfigForRestoreToNewInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AutoStorageScalingConfigForRestoreToNewInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AutoStorageScalingConfigForRestoreToNewInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
