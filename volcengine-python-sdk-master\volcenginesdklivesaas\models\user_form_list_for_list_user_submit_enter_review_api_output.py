# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserFormListForListUserSubmitEnterReviewAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'email': 'str',
        'enter_name': 'str',
        'enter_tel': 'str',
        'enter_time': 'int',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'is_watch_live': 'int',
        'nick_name': 'str',
        'review_status': 'int',
        'user_agent': 'str',
        'user_form': 'str',
        'user_form_id': 'int',
        'user_id': 'int',
        'user_tel': 'str',
        'watch_activity_time': 'int'
    }

    attribute_map = {
        'email': 'Email',
        'enter_name': 'EnterName',
        'enter_tel': 'EnterTel',
        'enter_time': 'EnterTime',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'is_watch_live': 'IsWatchLive',
        'nick_name': 'NickName',
        'review_status': 'ReviewStatus',
        'user_agent': 'UserAgent',
        'user_form': 'UserForm',
        'user_form_id': 'UserFormID',
        'user_id': 'UserId',
        'user_tel': 'UserTel',
        'watch_activity_time': 'WatchActivityTime'
    }

    def __init__(self, email=None, enter_name=None, enter_tel=None, enter_time=None, external_id=None, extra=None, ip=None, is_watch_live=None, nick_name=None, review_status=None, user_agent=None, user_form=None, user_form_id=None, user_id=None, user_tel=None, watch_activity_time=None, _configuration=None):  # noqa: E501
        """UserFormListForListUserSubmitEnterReviewAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._email = None
        self._enter_name = None
        self._enter_tel = None
        self._enter_time = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._is_watch_live = None
        self._nick_name = None
        self._review_status = None
        self._user_agent = None
        self._user_form = None
        self._user_form_id = None
        self._user_id = None
        self._user_tel = None
        self._watch_activity_time = None
        self.discriminator = None

        if email is not None:
            self.email = email
        if enter_name is not None:
            self.enter_name = enter_name
        if enter_tel is not None:
            self.enter_tel = enter_tel
        if enter_time is not None:
            self.enter_time = enter_time
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if is_watch_live is not None:
            self.is_watch_live = is_watch_live
        if nick_name is not None:
            self.nick_name = nick_name
        if review_status is not None:
            self.review_status = review_status
        if user_agent is not None:
            self.user_agent = user_agent
        if user_form is not None:
            self.user_form = user_form
        if user_form_id is not None:
            self.user_form_id = user_form_id
        if user_id is not None:
            self.user_id = user_id
        if user_tel is not None:
            self.user_tel = user_tel
        if watch_activity_time is not None:
            self.watch_activity_time = watch_activity_time

    @property
    def email(self):
        """Gets the email of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The email of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param email: The email of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def enter_name(self):
        """Gets the enter_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The enter_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_name

    @enter_name.setter
    def enter_name(self, enter_name):
        """Sets the enter_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param enter_name: The enter_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_name = enter_name

    @property
    def enter_tel(self):
        """Gets the enter_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The enter_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._enter_tel

    @enter_tel.setter
    def enter_tel(self, enter_tel):
        """Sets the enter_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param enter_tel: The enter_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._enter_tel = enter_tel

    @property
    def enter_time(self):
        """Gets the enter_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The enter_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._enter_time

    @enter_time.setter
    def enter_time(self, enter_time):
        """Sets the enter_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param enter_time: The enter_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._enter_time = enter_time

    @property
    def external_id(self):
        """Gets the external_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The external_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param external_id: The external_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The extra of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param extra: The extra of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The ip of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param ip: The ip of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_watch_live(self):
        """Gets the is_watch_live of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The is_watch_live of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_watch_live

    @is_watch_live.setter
    def is_watch_live(self, is_watch_live):
        """Sets the is_watch_live of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param is_watch_live: The is_watch_live of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_watch_live = is_watch_live

    @property
    def nick_name(self):
        """Gets the nick_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The nick_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param nick_name: The nick_name of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def review_status(self):
        """Gets the review_status of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The review_status of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._review_status

    @review_status.setter
    def review_status(self, review_status):
        """Sets the review_status of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param review_status: The review_status of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._review_status = review_status

    @property
    def user_agent(self):
        """Gets the user_agent of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The user_agent of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param user_agent: The user_agent of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_form(self):
        """Gets the user_form of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The user_form of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_form

    @user_form.setter
    def user_form(self, user_form):
        """Sets the user_form of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param user_form: The user_form of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_form = user_form

    @property
    def user_form_id(self):
        """Gets the user_form_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The user_form_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_form_id

    @user_form_id.setter
    def user_form_id(self, user_form_id):
        """Sets the user_form_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param user_form_id: The user_form_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_form_id = user_form_id

    @property
    def user_id(self):
        """Gets the user_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The user_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param user_id: The user_id of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_tel(self):
        """Gets the user_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The user_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param user_tel: The user_tel of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    @property
    def watch_activity_time(self):
        """Gets the watch_activity_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501


        :return: The watch_activity_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._watch_activity_time

    @watch_activity_time.setter
    def watch_activity_time(self, watch_activity_time):
        """Sets the watch_activity_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.


        :param watch_activity_time: The watch_activity_time of this UserFormListForListUserSubmitEnterReviewAPIOutput.  # noqa: E501
        :type: int
        """

        self._watch_activity_time = watch_activity_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserFormListForListUserSubmitEnterReviewAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserFormListForListUserSubmitEnterReviewAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserFormListForListUserSubmitEnterReviewAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
