# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserForListUsersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'cloud_name': 'str',
        'clusters': 'list[str]',
        'created_at': 'int',
        'description': 'str',
        'email': 'str',
        'id': 'int',
        'immutable': 'bool',
        'is_system_admin': 'bool',
        'last_login_at': 'int',
        'name': 'str',
        'readonly': 'bool',
        'status': 'str',
        'updated_at': 'int',
        'user_id': 'int',
        'user_type': 'str',
        'username': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'cloud_name': 'CloudName',
        'clusters': 'Clusters',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'email': 'Email',
        'id': 'ID',
        'immutable': 'Immutable',
        'is_system_admin': 'IsSystemAdmin',
        'last_login_at': 'LastLoginAt',
        'name': 'Name',
        'readonly': 'Readonly',
        'status': 'Status',
        'updated_at': 'UpdatedAt',
        'user_id': 'UserID',
        'user_type': 'UserType',
        'username': 'Username'
    }

    def __init__(self, account_id=None, cloud_name=None, clusters=None, created_at=None, description=None, email=None, id=None, immutable=None, is_system_admin=None, last_login_at=None, name=None, readonly=None, status=None, updated_at=None, user_id=None, user_type=None, username=None, _configuration=None):  # noqa: E501
        """UserForListUsersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._cloud_name = None
        self._clusters = None
        self._created_at = None
        self._description = None
        self._email = None
        self._id = None
        self._immutable = None
        self._is_system_admin = None
        self._last_login_at = None
        self._name = None
        self._readonly = None
        self._status = None
        self._updated_at = None
        self._user_id = None
        self._user_type = None
        self._username = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if cloud_name is not None:
            self.cloud_name = cloud_name
        if clusters is not None:
            self.clusters = clusters
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if email is not None:
            self.email = email
        if id is not None:
            self.id = id
        if immutable is not None:
            self.immutable = immutable
        if is_system_admin is not None:
            self.is_system_admin = is_system_admin
        if last_login_at is not None:
            self.last_login_at = last_login_at
        if name is not None:
            self.name = name
        if readonly is not None:
            self.readonly = readonly
        if status is not None:
            self.status = status
        if updated_at is not None:
            self.updated_at = updated_at
        if user_id is not None:
            self.user_id = user_id
        if user_type is not None:
            self.user_type = user_type
        if username is not None:
            self.username = username

    @property
    def account_id(self):
        """Gets the account_id of this UserForListUsersOutput.  # noqa: E501


        :return: The account_id of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this UserForListUsersOutput.


        :param account_id: The account_id of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def cloud_name(self):
        """Gets the cloud_name of this UserForListUsersOutput.  # noqa: E501


        :return: The cloud_name of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_name

    @cloud_name.setter
    def cloud_name(self, cloud_name):
        """Sets the cloud_name of this UserForListUsersOutput.


        :param cloud_name: The cloud_name of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._cloud_name = cloud_name

    @property
    def clusters(self):
        """Gets the clusters of this UserForListUsersOutput.  # noqa: E501


        :return: The clusters of this UserForListUsersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._clusters

    @clusters.setter
    def clusters(self, clusters):
        """Sets the clusters of this UserForListUsersOutput.


        :param clusters: The clusters of this UserForListUsersOutput.  # noqa: E501
        :type: list[str]
        """

        self._clusters = clusters

    @property
    def created_at(self):
        """Gets the created_at of this UserForListUsersOutput.  # noqa: E501


        :return: The created_at of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this UserForListUsersOutput.


        :param created_at: The created_at of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this UserForListUsersOutput.  # noqa: E501


        :return: The description of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UserForListUsersOutput.


        :param description: The description of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def email(self):
        """Gets the email of this UserForListUsersOutput.  # noqa: E501


        :return: The email of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this UserForListUsersOutput.


        :param email: The email of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def id(self):
        """Gets the id of this UserForListUsersOutput.  # noqa: E501


        :return: The id of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UserForListUsersOutput.


        :param id: The id of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def immutable(self):
        """Gets the immutable of this UserForListUsersOutput.  # noqa: E501


        :return: The immutable of this UserForListUsersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._immutable

    @immutable.setter
    def immutable(self, immutable):
        """Sets the immutable of this UserForListUsersOutput.


        :param immutable: The immutable of this UserForListUsersOutput.  # noqa: E501
        :type: bool
        """

        self._immutable = immutable

    @property
    def is_system_admin(self):
        """Gets the is_system_admin of this UserForListUsersOutput.  # noqa: E501


        :return: The is_system_admin of this UserForListUsersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_system_admin

    @is_system_admin.setter
    def is_system_admin(self, is_system_admin):
        """Sets the is_system_admin of this UserForListUsersOutput.


        :param is_system_admin: The is_system_admin of this UserForListUsersOutput.  # noqa: E501
        :type: bool
        """

        self._is_system_admin = is_system_admin

    @property
    def last_login_at(self):
        """Gets the last_login_at of this UserForListUsersOutput.  # noqa: E501


        :return: The last_login_at of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_login_at

    @last_login_at.setter
    def last_login_at(self, last_login_at):
        """Sets the last_login_at of this UserForListUsersOutput.


        :param last_login_at: The last_login_at of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._last_login_at = last_login_at

    @property
    def name(self):
        """Gets the name of this UserForListUsersOutput.  # noqa: E501


        :return: The name of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UserForListUsersOutput.


        :param name: The name of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def readonly(self):
        """Gets the readonly of this UserForListUsersOutput.  # noqa: E501


        :return: The readonly of this UserForListUsersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._readonly

    @readonly.setter
    def readonly(self, readonly):
        """Sets the readonly of this UserForListUsersOutput.


        :param readonly: The readonly of this UserForListUsersOutput.  # noqa: E501
        :type: bool
        """

        self._readonly = readonly

    @property
    def status(self):
        """Gets the status of this UserForListUsersOutput.  # noqa: E501


        :return: The status of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this UserForListUsersOutput.


        :param status: The status of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def updated_at(self):
        """Gets the updated_at of this UserForListUsersOutput.  # noqa: E501


        :return: The updated_at of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this UserForListUsersOutput.


        :param updated_at: The updated_at of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._updated_at = updated_at

    @property
    def user_id(self):
        """Gets the user_id of this UserForListUsersOutput.  # noqa: E501


        :return: The user_id of this UserForListUsersOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UserForListUsersOutput.


        :param user_id: The user_id of this UserForListUsersOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_type(self):
        """Gets the user_type of this UserForListUsersOutput.  # noqa: E501


        :return: The user_type of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_type

    @user_type.setter
    def user_type(self, user_type):
        """Sets the user_type of this UserForListUsersOutput.


        :param user_type: The user_type of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._user_type = user_type

    @property
    def username(self):
        """Gets the username of this UserForListUsersOutput.  # noqa: E501


        :return: The username of this UserForListUsersOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this UserForListUsersOutput.


        :param username: The username of this UserForListUsersOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserForListUsersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserForListUsersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserForListUsersOutput):
            return True

        return self.to_dict() != other.to_dict()
