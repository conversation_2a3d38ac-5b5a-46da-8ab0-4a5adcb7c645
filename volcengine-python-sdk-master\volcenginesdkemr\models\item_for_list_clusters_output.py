# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'charge_type': 'str',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'cluster_state': 'str',
        'cluster_type': 'str',
        'create_time': 'int',
        'create_time_str': 'str',
        'creator': 'str',
        'expire_time': 'int',
        'node_attribute': 'NodeAttributeForListClustersOutput',
        'project_name': 'str',
        'ready_time': 'int',
        'region_id': 'str',
        'release_version': 'str',
        'security_group_id': 'str',
        'state_change_reason': 'StateChangeReasonForListClustersOutput',
        'tags': 'list[TagForListClustersOutput]',
        'terminate_time': 'int',
        'vpc_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'charge_type': 'ChargeType',
        'cluster_id': 'ClusterId',
        'cluster_name': 'ClusterName',
        'cluster_state': 'ClusterState',
        'cluster_type': 'ClusterType',
        'create_time': 'CreateTime',
        'create_time_str': 'CreateTimeStr',
        'creator': 'Creator',
        'expire_time': 'ExpireTime',
        'node_attribute': 'NodeAttribute',
        'project_name': 'ProjectName',
        'ready_time': 'ReadyTime',
        'region_id': 'RegionId',
        'release_version': 'ReleaseVersion',
        'security_group_id': 'SecurityGroupId',
        'state_change_reason': 'StateChangeReason',
        'tags': 'Tags',
        'terminate_time': 'TerminateTime',
        'vpc_id': 'VpcId'
    }

    def __init__(self, account_id=None, charge_type=None, cluster_id=None, cluster_name=None, cluster_state=None, cluster_type=None, create_time=None, create_time_str=None, creator=None, expire_time=None, node_attribute=None, project_name=None, ready_time=None, region_id=None, release_version=None, security_group_id=None, state_change_reason=None, tags=None, terminate_time=None, vpc_id=None, _configuration=None):  # noqa: E501
        """ItemForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._charge_type = None
        self._cluster_id = None
        self._cluster_name = None
        self._cluster_state = None
        self._cluster_type = None
        self._create_time = None
        self._create_time_str = None
        self._creator = None
        self._expire_time = None
        self._node_attribute = None
        self._project_name = None
        self._ready_time = None
        self._region_id = None
        self._release_version = None
        self._security_group_id = None
        self._state_change_reason = None
        self._tags = None
        self._terminate_time = None
        self._vpc_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if charge_type is not None:
            self.charge_type = charge_type
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if cluster_state is not None:
            self.cluster_state = cluster_state
        if cluster_type is not None:
            self.cluster_type = cluster_type
        if create_time is not None:
            self.create_time = create_time
        if create_time_str is not None:
            self.create_time_str = create_time_str
        if creator is not None:
            self.creator = creator
        if expire_time is not None:
            self.expire_time = expire_time
        if node_attribute is not None:
            self.node_attribute = node_attribute
        if project_name is not None:
            self.project_name = project_name
        if ready_time is not None:
            self.ready_time = ready_time
        if region_id is not None:
            self.region_id = region_id
        if release_version is not None:
            self.release_version = release_version
        if security_group_id is not None:
            self.security_group_id = security_group_id
        if state_change_reason is not None:
            self.state_change_reason = state_change_reason
        if tags is not None:
            self.tags = tags
        if terminate_time is not None:
            self.terminate_time = terminate_time
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def account_id(self):
        """Gets the account_id of this ItemForListClustersOutput.  # noqa: E501


        :return: The account_id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ItemForListClustersOutput.


        :param account_id: The account_id of this ItemForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def charge_type(self):
        """Gets the charge_type of this ItemForListClustersOutput.  # noqa: E501


        :return: The charge_type of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ItemForListClustersOutput.


        :param charge_type: The charge_type of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListClustersOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListClustersOutput.


        :param cluster_id: The cluster_id of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this ItemForListClustersOutput.  # noqa: E501


        :return: The cluster_name of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this ItemForListClustersOutput.


        :param cluster_name: The cluster_name of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def cluster_state(self):
        """Gets the cluster_state of this ItemForListClustersOutput.  # noqa: E501


        :return: The cluster_state of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_state

    @cluster_state.setter
    def cluster_state(self, cluster_state):
        """Sets the cluster_state of this ItemForListClustersOutput.


        :param cluster_state: The cluster_state of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_state = cluster_state

    @property
    def cluster_type(self):
        """Gets the cluster_type of this ItemForListClustersOutput.  # noqa: E501


        :return: The cluster_type of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_type

    @cluster_type.setter
    def cluster_type(self, cluster_type):
        """Sets the cluster_type of this ItemForListClustersOutput.


        :param cluster_type: The cluster_type of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_type = cluster_type

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The create_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListClustersOutput.


        :param create_time: The create_time of this ItemForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def create_time_str(self):
        """Gets the create_time_str of this ItemForListClustersOutput.  # noqa: E501


        :return: The create_time_str of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time_str

    @create_time_str.setter
    def create_time_str(self, create_time_str):
        """Sets the create_time_str of this ItemForListClustersOutput.


        :param create_time_str: The create_time_str of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._create_time_str = create_time_str

    @property
    def creator(self):
        """Gets the creator of this ItemForListClustersOutput.  # noqa: E501


        :return: The creator of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._creator

    @creator.setter
    def creator(self, creator):
        """Sets the creator of this ItemForListClustersOutput.


        :param creator: The creator of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._creator = creator

    @property
    def expire_time(self):
        """Gets the expire_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The expire_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ItemForListClustersOutput.


        :param expire_time: The expire_time of this ItemForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def node_attribute(self):
        """Gets the node_attribute of this ItemForListClustersOutput.  # noqa: E501


        :return: The node_attribute of this ItemForListClustersOutput.  # noqa: E501
        :rtype: NodeAttributeForListClustersOutput
        """
        return self._node_attribute

    @node_attribute.setter
    def node_attribute(self, node_attribute):
        """Sets the node_attribute of this ItemForListClustersOutput.


        :param node_attribute: The node_attribute of this ItemForListClustersOutput.  # noqa: E501
        :type: NodeAttributeForListClustersOutput
        """

        self._node_attribute = node_attribute

    @property
    def project_name(self):
        """Gets the project_name of this ItemForListClustersOutput.  # noqa: E501


        :return: The project_name of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ItemForListClustersOutput.


        :param project_name: The project_name of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def ready_time(self):
        """Gets the ready_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The ready_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._ready_time

    @ready_time.setter
    def ready_time(self, ready_time):
        """Sets the ready_time of this ItemForListClustersOutput.


        :param ready_time: The ready_time of this ItemForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._ready_time = ready_time

    @property
    def region_id(self):
        """Gets the region_id of this ItemForListClustersOutput.  # noqa: E501


        :return: The region_id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this ItemForListClustersOutput.


        :param region_id: The region_id of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def release_version(self):
        """Gets the release_version of this ItemForListClustersOutput.  # noqa: E501


        :return: The release_version of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._release_version

    @release_version.setter
    def release_version(self, release_version):
        """Sets the release_version of this ItemForListClustersOutput.


        :param release_version: The release_version of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._release_version = release_version

    @property
    def security_group_id(self):
        """Gets the security_group_id of this ItemForListClustersOutput.  # noqa: E501


        :return: The security_group_id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this ItemForListClustersOutput.


        :param security_group_id: The security_group_id of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    @property
    def state_change_reason(self):
        """Gets the state_change_reason of this ItemForListClustersOutput.  # noqa: E501


        :return: The state_change_reason of this ItemForListClustersOutput.  # noqa: E501
        :rtype: StateChangeReasonForListClustersOutput
        """
        return self._state_change_reason

    @state_change_reason.setter
    def state_change_reason(self, state_change_reason):
        """Sets the state_change_reason of this ItemForListClustersOutput.


        :param state_change_reason: The state_change_reason of this ItemForListClustersOutput.  # noqa: E501
        :type: StateChangeReasonForListClustersOutput
        """

        self._state_change_reason = state_change_reason

    @property
    def tags(self):
        """Gets the tags of this ItemForListClustersOutput.  # noqa: E501


        :return: The tags of this ItemForListClustersOutput.  # noqa: E501
        :rtype: list[TagForListClustersOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListClustersOutput.


        :param tags: The tags of this ItemForListClustersOutput.  # noqa: E501
        :type: list[TagForListClustersOutput]
        """

        self._tags = tags

    @property
    def terminate_time(self):
        """Gets the terminate_time of this ItemForListClustersOutput.  # noqa: E501


        :return: The terminate_time of this ItemForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._terminate_time

    @terminate_time.setter
    def terminate_time(self, terminate_time):
        """Sets the terminate_time of this ItemForListClustersOutput.


        :param terminate_time: The terminate_time of this ItemForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._terminate_time = terminate_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this ItemForListClustersOutput.  # noqa: E501


        :return: The vpc_id of this ItemForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this ItemForListClustersOutput.


        :param vpc_id: The vpc_id of this ItemForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
