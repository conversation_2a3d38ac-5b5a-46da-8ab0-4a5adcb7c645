# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateNodeGroupChargeTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_pre_config': 'ChargePreConfigForUpdateNodeGroupChargeTypeInput',
        'charge_type': 'str',
        'client_token': 'str',
        'cluster_id': 'str',
        'node_group_ids': 'list[str]'
    }

    attribute_map = {
        'charge_pre_config': 'ChargePreConfig',
        'charge_type': 'ChargeType',
        'client_token': 'ClientToken',
        'cluster_id': 'ClusterId',
        'node_group_ids': 'NodeGroupIds'
    }

    def __init__(self, charge_pre_config=None, charge_type=None, client_token=None, cluster_id=None, node_group_ids=None, _configuration=None):  # noqa: E501
        """UpdateNodeGroupChargeTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_pre_config = None
        self._charge_type = None
        self._client_token = None
        self._cluster_id = None
        self._node_group_ids = None
        self.discriminator = None

        if charge_pre_config is not None:
            self.charge_pre_config = charge_pre_config
        self.charge_type = charge_type
        if client_token is not None:
            self.client_token = client_token
        self.cluster_id = cluster_id
        if node_group_ids is not None:
            self.node_group_ids = node_group_ids

    @property
    def charge_pre_config(self):
        """Gets the charge_pre_config of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501


        :return: The charge_pre_config of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :rtype: ChargePreConfigForUpdateNodeGroupChargeTypeInput
        """
        return self._charge_pre_config

    @charge_pre_config.setter
    def charge_pre_config(self, charge_pre_config):
        """Sets the charge_pre_config of this UpdateNodeGroupChargeTypeRequest.


        :param charge_pre_config: The charge_pre_config of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :type: ChargePreConfigForUpdateNodeGroupChargeTypeInput
        """

        self._charge_pre_config = charge_pre_config

    @property
    def charge_type(self):
        """Gets the charge_type of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501


        :return: The charge_type of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this UpdateNodeGroupChargeTypeRequest.


        :param charge_type: The charge_type of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def client_token(self):
        """Gets the client_token of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501


        :return: The client_token of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this UpdateNodeGroupChargeTypeRequest.


        :param client_token: The client_token of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_id(self):
        """Gets the cluster_id of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501


        :return: The cluster_id of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this UpdateNodeGroupChargeTypeRequest.


        :param cluster_id: The cluster_id of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def node_group_ids(self):
        """Gets the node_group_ids of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501


        :return: The node_group_ids of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_group_ids

    @node_group_ids.setter
    def node_group_ids(self, node_group_ids):
        """Sets the node_group_ids of this UpdateNodeGroupChargeTypeRequest.


        :param node_group_ids: The node_group_ids of this UpdateNodeGroupChargeTypeRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_group_ids = node_group_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateNodeGroupChargeTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateNodeGroupChargeTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateNodeGroupChargeTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
