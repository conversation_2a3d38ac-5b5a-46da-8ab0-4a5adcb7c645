# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DownloadVulListByPodRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'conditions': 'ConditionsForDownloadVulListByPodInput',
        'id_list': 'list[str]',
        'namespace': 'str',
        'status': 'list[str]',
        'vuln_type_list': 'list[str]',
        'workload_id': 'str',
        'workload_name': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'conditions': 'Conditions',
        'id_list': 'IDList',
        'namespace': 'Namespace',
        'status': 'Status',
        'vuln_type_list': 'VulnTypeList',
        'workload_id': 'WorkloadID',
        'workload_name': 'WorkloadName'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, cluster_id=None, cluster_name=None, conditions=None, id_list=None, namespace=None, status=None, vuln_type_list=None, workload_id=None, workload_name=None, _configuration=None):  # noqa: E501
        """DownloadVulListByPodRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._cluster_id = None
        self._cluster_name = None
        self._conditions = None
        self._id_list = None
        self._namespace = None
        self._status = None
        self._vuln_type_list = None
        self._workload_id = None
        self._workload_name = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        self.cluster_id = cluster_id
        self.cluster_name = cluster_name
        if conditions is not None:
            self.conditions = conditions
        if id_list is not None:
            self.id_list = id_list
        self.namespace = namespace
        if status is not None:
            self.status = status
        if vuln_type_list is not None:
            self.vuln_type_list = vuln_type_list
        self.workload_id = workload_id
        self.workload_name = workload_name

    @property
    def agent_id(self):
        """Gets the agent_id of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The agent_id of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DownloadVulListByPodRequest.


        :param agent_id: The agent_id of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The asset_id of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DownloadVulListByPodRequest.


        :param asset_id: The asset_id of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The asset_type of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this DownloadVulListByPodRequest.


        :param asset_type: The asset_type of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """

        self._asset_type = asset_type

    @property
    def cluster_id(self):
        """Gets the cluster_id of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The cluster_id of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this DownloadVulListByPodRequest.


        :param cluster_id: The cluster_id of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The cluster_name of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this DownloadVulListByPodRequest.


        :param cluster_name: The cluster_name of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_name is None:
            raise ValueError("Invalid value for `cluster_name`, must not be `None`")  # noqa: E501

        self._cluster_name = cluster_name

    @property
    def conditions(self):
        """Gets the conditions of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The conditions of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: ConditionsForDownloadVulListByPodInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this DownloadVulListByPodRequest.


        :param conditions: The conditions of this DownloadVulListByPodRequest.  # noqa: E501
        :type: ConditionsForDownloadVulListByPodInput
        """

        self._conditions = conditions

    @property
    def id_list(self):
        """Gets the id_list of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The id_list of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._id_list

    @id_list.setter
    def id_list(self, id_list):
        """Sets the id_list of this DownloadVulListByPodRequest.


        :param id_list: The id_list of this DownloadVulListByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._id_list = id_list

    @property
    def namespace(self):
        """Gets the namespace of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The namespace of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DownloadVulListByPodRequest.


        :param namespace: The namespace of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and namespace is None:
            raise ValueError("Invalid value for `namespace`, must not be `None`")  # noqa: E501

        self._namespace = namespace

    @property
    def status(self):
        """Gets the status of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The status of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DownloadVulListByPodRequest.


        :param status: The status of this DownloadVulListByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def vuln_type_list(self):
        """Gets the vuln_type_list of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The vuln_type_list of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vuln_type_list

    @vuln_type_list.setter
    def vuln_type_list(self, vuln_type_list):
        """Sets the vuln_type_list of this DownloadVulListByPodRequest.


        :param vuln_type_list: The vuln_type_list of this DownloadVulListByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._vuln_type_list = vuln_type_list

    @property
    def workload_id(self):
        """Gets the workload_id of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The workload_id of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._workload_id

    @workload_id.setter
    def workload_id(self, workload_id):
        """Sets the workload_id of this DownloadVulListByPodRequest.


        :param workload_id: The workload_id of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workload_id is None:
            raise ValueError("Invalid value for `workload_id`, must not be `None`")  # noqa: E501

        self._workload_id = workload_id

    @property
    def workload_name(self):
        """Gets the workload_name of this DownloadVulListByPodRequest.  # noqa: E501


        :return: The workload_name of this DownloadVulListByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._workload_name

    @workload_name.setter
    def workload_name(self, workload_name):
        """Sets the workload_name of this DownloadVulListByPodRequest.


        :param workload_name: The workload_name of this DownloadVulListByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workload_name is None:
            raise ValueError("Invalid value for `workload_name`, must not be `None`")  # noqa: E501

        self._workload_name = workload_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DownloadVulListByPodRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DownloadVulListByPodRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DownloadVulListByPodRequest):
            return True

        return self.to_dict() != other.to_dict()
