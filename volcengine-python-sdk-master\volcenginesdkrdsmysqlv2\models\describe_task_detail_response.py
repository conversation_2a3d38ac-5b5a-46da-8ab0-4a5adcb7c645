# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTaskDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'check_item_log': 'str',
        'check_items': 'list[CheckItemForDescribeTaskDetailOutput]',
        'task_info': 'TaskInfoForDescribeTaskDetailOutput'
    }

    attribute_map = {
        'check_item_log': 'CheckItemLog',
        'check_items': 'CheckItems',
        'task_info': 'TaskInfo'
    }

    def __init__(self, check_item_log=None, check_items=None, task_info=None, _configuration=None):  # noqa: E501
        """DescribeTaskDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._check_item_log = None
        self._check_items = None
        self._task_info = None
        self.discriminator = None

        if check_item_log is not None:
            self.check_item_log = check_item_log
        if check_items is not None:
            self.check_items = check_items
        if task_info is not None:
            self.task_info = task_info

    @property
    def check_item_log(self):
        """Gets the check_item_log of this DescribeTaskDetailResponse.  # noqa: E501


        :return: The check_item_log of this DescribeTaskDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._check_item_log

    @check_item_log.setter
    def check_item_log(self, check_item_log):
        """Sets the check_item_log of this DescribeTaskDetailResponse.


        :param check_item_log: The check_item_log of this DescribeTaskDetailResponse.  # noqa: E501
        :type: str
        """

        self._check_item_log = check_item_log

    @property
    def check_items(self):
        """Gets the check_items of this DescribeTaskDetailResponse.  # noqa: E501


        :return: The check_items of this DescribeTaskDetailResponse.  # noqa: E501
        :rtype: list[CheckItemForDescribeTaskDetailOutput]
        """
        return self._check_items

    @check_items.setter
    def check_items(self, check_items):
        """Sets the check_items of this DescribeTaskDetailResponse.


        :param check_items: The check_items of this DescribeTaskDetailResponse.  # noqa: E501
        :type: list[CheckItemForDescribeTaskDetailOutput]
        """

        self._check_items = check_items

    @property
    def task_info(self):
        """Gets the task_info of this DescribeTaskDetailResponse.  # noqa: E501


        :return: The task_info of this DescribeTaskDetailResponse.  # noqa: E501
        :rtype: TaskInfoForDescribeTaskDetailOutput
        """
        return self._task_info

    @task_info.setter
    def task_info(self, task_info):
        """Sets the task_info of this DescribeTaskDetailResponse.


        :param task_info: The task_info of this DescribeTaskDetailResponse.  # noqa: E501
        :type: TaskInfoForDescribeTaskDetailOutput
        """

        self._task_info = task_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTaskDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTaskDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTaskDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
