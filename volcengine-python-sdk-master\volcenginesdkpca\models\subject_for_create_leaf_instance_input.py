# coding: utf-8

"""
    pca

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubjectForCreateLeafInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'common_name': 'str',
        'country': 'list[str]',
        'locality': 'list[str]',
        'organization': 'list[str]',
        'organizational_unit': 'list[str]',
        'postal_code': 'list[str]',
        'province': 'list[str]',
        'serial_number': 'str',
        'street_address': 'list[str]'
    }

    attribute_map = {
        'common_name': 'common_name',
        'country': 'country',
        'locality': 'locality',
        'organization': 'organization',
        'organizational_unit': 'organizational_unit',
        'postal_code': 'postal_code',
        'province': 'province',
        'serial_number': 'serial_number',
        'street_address': 'street_address'
    }

    def __init__(self, common_name=None, country=None, locality=None, organization=None, organizational_unit=None, postal_code=None, province=None, serial_number=None, street_address=None, _configuration=None):  # noqa: E501
        """SubjectForCreateLeafInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._common_name = None
        self._country = None
        self._locality = None
        self._organization = None
        self._organizational_unit = None
        self._postal_code = None
        self._province = None
        self._serial_number = None
        self._street_address = None
        self.discriminator = None

        if common_name is not None:
            self.common_name = common_name
        if country is not None:
            self.country = country
        if locality is not None:
            self.locality = locality
        if organization is not None:
            self.organization = organization
        if organizational_unit is not None:
            self.organizational_unit = organizational_unit
        if postal_code is not None:
            self.postal_code = postal_code
        if province is not None:
            self.province = province
        if serial_number is not None:
            self.serial_number = serial_number
        if street_address is not None:
            self.street_address = street_address

    @property
    def common_name(self):
        """Gets the common_name of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The common_name of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this SubjectForCreateLeafInstanceInput.


        :param common_name: The common_name of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def country(self):
        """Gets the country of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The country of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this SubjectForCreateLeafInstanceInput.


        :param country: The country of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._country = country

    @property
    def locality(self):
        """Gets the locality of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The locality of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._locality

    @locality.setter
    def locality(self, locality):
        """Sets the locality of this SubjectForCreateLeafInstanceInput.


        :param locality: The locality of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._locality = locality

    @property
    def organization(self):
        """Gets the organization of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The organization of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._organization

    @organization.setter
    def organization(self, organization):
        """Sets the organization of this SubjectForCreateLeafInstanceInput.


        :param organization: The organization of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._organization = organization

    @property
    def organizational_unit(self):
        """Gets the organizational_unit of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The organizational_unit of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._organizational_unit

    @organizational_unit.setter
    def organizational_unit(self, organizational_unit):
        """Sets the organizational_unit of this SubjectForCreateLeafInstanceInput.


        :param organizational_unit: The organizational_unit of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._organizational_unit = organizational_unit

    @property
    def postal_code(self):
        """Gets the postal_code of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The postal_code of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._postal_code

    @postal_code.setter
    def postal_code(self, postal_code):
        """Sets the postal_code of this SubjectForCreateLeafInstanceInput.


        :param postal_code: The postal_code of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._postal_code = postal_code

    @property
    def province(self):
        """Gets the province of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The province of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this SubjectForCreateLeafInstanceInput.


        :param province: The province of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._province = province

    @property
    def serial_number(self):
        """Gets the serial_number of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The serial_number of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._serial_number

    @serial_number.setter
    def serial_number(self, serial_number):
        """Sets the serial_number of this SubjectForCreateLeafInstanceInput.


        :param serial_number: The serial_number of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: str
        """

        self._serial_number = serial_number

    @property
    def street_address(self):
        """Gets the street_address of this SubjectForCreateLeafInstanceInput.  # noqa: E501


        :return: The street_address of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._street_address

    @street_address.setter
    def street_address(self, street_address):
        """Sets the street_address of this SubjectForCreateLeafInstanceInput.


        :param street_address: The street_address of this SubjectForCreateLeafInstanceInput.  # noqa: E501
        :type: list[str]
        """

        self._street_address = street_address

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubjectForCreateLeafInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubjectForCreateLeafInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubjectForCreateLeafInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
