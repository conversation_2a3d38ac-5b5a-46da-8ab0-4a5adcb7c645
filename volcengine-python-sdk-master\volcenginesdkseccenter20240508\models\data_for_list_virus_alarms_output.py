# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListVirusAlarmsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_group': 'str',
        'agent_id': 'str',
        'alarm_handle_result': 'int',
        'alarm_hostname': 'str',
        'alarm_id': 'str',
        'alarm_time': 'int',
        'alert_tags': 'list[str]',
        'args': 'list[str]',
        'args_list': 'list[str]',
        'argv_list': 'list[str]',
        'attribution_list': 'list[AttributionListForListVirusAlarmsOutput]',
        '_class': 'str',
        'cluster': 'ClusterForListVirusAlarmsOutput',
        'data_type': 'str',
        'error_reason': 'str',
        'event_id': 'str',
        'event_name': 'str',
        'exe': 'str',
        'file_downloadable': 'bool',
        'file_hash': 'str',
        'file_path': 'str',
        'group_path': 'str',
        'handle_time': 'int',
        'host': 'HostForListVirusAlarmsOutput',
        'image_name': 'str',
        'in_docker': 'str',
        'llm_analysis_result': 'str',
        'llm_processed': 'bool',
        'level': 'str',
        'mlp_instance_id': 'str',
        'mlp_private_ip': 'str',
        'name': 'str',
        'ns_pid': 'str',
        'os_type': 'str',
        'pid': 'str',
        'probe_hook': 'str',
        'region': 'str',
        'sid': 'str',
        'stack_trace_format': 'str',
        'stack_trace_hash': 'str',
        'status': 'int',
        'trace_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'agent_group': 'AgentGroup',
        'agent_id': 'AgentID',
        'alarm_handle_result': 'AlarmHandleResult',
        'alarm_hostname': 'AlarmHostname',
        'alarm_id': 'AlarmID',
        'alarm_time': 'AlarmTime',
        'alert_tags': 'AlertTags',
        'args': 'Args',
        'args_list': 'ArgsList',
        'argv_list': 'ArgvList',
        'attribution_list': 'AttributionList',
        '_class': 'Class',
        'cluster': 'Cluster',
        'data_type': 'DataType',
        'error_reason': 'ErrorReason',
        'event_id': 'EventID',
        'event_name': 'EventName',
        'exe': 'Exe',
        'file_downloadable': 'FileDownloadable',
        'file_hash': 'FileHash',
        'file_path': 'FilePath',
        'group_path': 'GroupPath',
        'handle_time': 'HandleTime',
        'host': 'Host',
        'image_name': 'ImageName',
        'in_docker': 'InDocker',
        'llm_analysis_result': 'LLMAnalysisResult',
        'llm_processed': 'LLMProcessed',
        'level': 'Level',
        'mlp_instance_id': 'MlpInstanceID',
        'mlp_private_ip': 'MlpPrivateIP',
        'name': 'Name',
        'ns_pid': 'NsPid',
        'os_type': 'OsType',
        'pid': 'Pid',
        'probe_hook': 'ProbeHook',
        'region': 'Region',
        'sid': 'Sid',
        'stack_trace_format': 'StackTraceFormat',
        'stack_trace_hash': 'StackTraceHash',
        'status': 'Status',
        'trace_id': 'TraceID',
        'type': 'Type'
    }

    def __init__(self, agent_group=None, agent_id=None, alarm_handle_result=None, alarm_hostname=None, alarm_id=None, alarm_time=None, alert_tags=None, args=None, args_list=None, argv_list=None, attribution_list=None, _class=None, cluster=None, data_type=None, error_reason=None, event_id=None, event_name=None, exe=None, file_downloadable=None, file_hash=None, file_path=None, group_path=None, handle_time=None, host=None, image_name=None, in_docker=None, llm_analysis_result=None, llm_processed=None, level=None, mlp_instance_id=None, mlp_private_ip=None, name=None, ns_pid=None, os_type=None, pid=None, probe_hook=None, region=None, sid=None, stack_trace_format=None, stack_trace_hash=None, status=None, trace_id=None, type=None, _configuration=None):  # noqa: E501
        """DataForListVirusAlarmsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_group = None
        self._agent_id = None
        self._alarm_handle_result = None
        self._alarm_hostname = None
        self._alarm_id = None
        self._alarm_time = None
        self._alert_tags = None
        self._args = None
        self._args_list = None
        self._argv_list = None
        self._attribution_list = None
        self.__class = None
        self._cluster = None
        self._data_type = None
        self._error_reason = None
        self._event_id = None
        self._event_name = None
        self._exe = None
        self._file_downloadable = None
        self._file_hash = None
        self._file_path = None
        self._group_path = None
        self._handle_time = None
        self._host = None
        self._image_name = None
        self._in_docker = None
        self._llm_analysis_result = None
        self._llm_processed = None
        self._level = None
        self._mlp_instance_id = None
        self._mlp_private_ip = None
        self._name = None
        self._ns_pid = None
        self._os_type = None
        self._pid = None
        self._probe_hook = None
        self._region = None
        self._sid = None
        self._stack_trace_format = None
        self._stack_trace_hash = None
        self._status = None
        self._trace_id = None
        self._type = None
        self.discriminator = None

        if agent_group is not None:
            self.agent_group = agent_group
        if agent_id is not None:
            self.agent_id = agent_id
        if alarm_handle_result is not None:
            self.alarm_handle_result = alarm_handle_result
        if alarm_hostname is not None:
            self.alarm_hostname = alarm_hostname
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alarm_time is not None:
            self.alarm_time = alarm_time
        if alert_tags is not None:
            self.alert_tags = alert_tags
        if args is not None:
            self.args = args
        if args_list is not None:
            self.args_list = args_list
        if argv_list is not None:
            self.argv_list = argv_list
        if attribution_list is not None:
            self.attribution_list = attribution_list
        if _class is not None:
            self._class = _class
        if cluster is not None:
            self.cluster = cluster
        if data_type is not None:
            self.data_type = data_type
        if error_reason is not None:
            self.error_reason = error_reason
        if event_id is not None:
            self.event_id = event_id
        if event_name is not None:
            self.event_name = event_name
        if exe is not None:
            self.exe = exe
        if file_downloadable is not None:
            self.file_downloadable = file_downloadable
        if file_hash is not None:
            self.file_hash = file_hash
        if file_path is not None:
            self.file_path = file_path
        if group_path is not None:
            self.group_path = group_path
        if handle_time is not None:
            self.handle_time = handle_time
        if host is not None:
            self.host = host
        if image_name is not None:
            self.image_name = image_name
        if in_docker is not None:
            self.in_docker = in_docker
        if llm_analysis_result is not None:
            self.llm_analysis_result = llm_analysis_result
        if llm_processed is not None:
            self.llm_processed = llm_processed
        if level is not None:
            self.level = level
        if mlp_instance_id is not None:
            self.mlp_instance_id = mlp_instance_id
        if mlp_private_ip is not None:
            self.mlp_private_ip = mlp_private_ip
        if name is not None:
            self.name = name
        if ns_pid is not None:
            self.ns_pid = ns_pid
        if os_type is not None:
            self.os_type = os_type
        if pid is not None:
            self.pid = pid
        if probe_hook is not None:
            self.probe_hook = probe_hook
        if region is not None:
            self.region = region
        if sid is not None:
            self.sid = sid
        if stack_trace_format is not None:
            self.stack_trace_format = stack_trace_format
        if stack_trace_hash is not None:
            self.stack_trace_hash = stack_trace_hash
        if status is not None:
            self.status = status
        if trace_id is not None:
            self.trace_id = trace_id
        if type is not None:
            self.type = type

    @property
    def agent_group(self):
        """Gets the agent_group of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The agent_group of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_group

    @agent_group.setter
    def agent_group(self, agent_group):
        """Sets the agent_group of this DataForListVirusAlarmsOutput.


        :param agent_group: The agent_group of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._agent_group = agent_group

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The agent_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListVirusAlarmsOutput.


        :param agent_id: The agent_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def alarm_handle_result(self):
        """Gets the alarm_handle_result of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The alarm_handle_result of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_handle_result

    @alarm_handle_result.setter
    def alarm_handle_result(self, alarm_handle_result):
        """Sets the alarm_handle_result of this DataForListVirusAlarmsOutput.


        :param alarm_handle_result: The alarm_handle_result of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: int
        """

        self._alarm_handle_result = alarm_handle_result

    @property
    def alarm_hostname(self):
        """Gets the alarm_hostname of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The alarm_hostname of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_hostname

    @alarm_hostname.setter
    def alarm_hostname(self, alarm_hostname):
        """Sets the alarm_hostname of this DataForListVirusAlarmsOutput.


        :param alarm_hostname: The alarm_hostname of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._alarm_hostname = alarm_hostname

    @property
    def alarm_id(self):
        """Gets the alarm_id of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The alarm_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this DataForListVirusAlarmsOutput.


        :param alarm_id: The alarm_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alarm_time(self):
        """Gets the alarm_time of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The alarm_time of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_time

    @alarm_time.setter
    def alarm_time(self, alarm_time):
        """Sets the alarm_time of this DataForListVirusAlarmsOutput.


        :param alarm_time: The alarm_time of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: int
        """

        self._alarm_time = alarm_time

    @property
    def alert_tags(self):
        """Gets the alert_tags of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The alert_tags of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_tags

    @alert_tags.setter
    def alert_tags(self, alert_tags):
        """Sets the alert_tags of this DataForListVirusAlarmsOutput.


        :param alert_tags: The alert_tags of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._alert_tags = alert_tags

    @property
    def args(self):
        """Gets the args of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The args of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._args

    @args.setter
    def args(self, args):
        """Sets the args of this DataForListVirusAlarmsOutput.


        :param args: The args of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._args = args

    @property
    def args_list(self):
        """Gets the args_list of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The args_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._args_list

    @args_list.setter
    def args_list(self, args_list):
        """Sets the args_list of this DataForListVirusAlarmsOutput.


        :param args_list: The args_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._args_list = args_list

    @property
    def argv_list(self):
        """Gets the argv_list of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The argv_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._argv_list

    @argv_list.setter
    def argv_list(self, argv_list):
        """Sets the argv_list of this DataForListVirusAlarmsOutput.


        :param argv_list: The argv_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._argv_list = argv_list

    @property
    def attribution_list(self):
        """Gets the attribution_list of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The attribution_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: list[AttributionListForListVirusAlarmsOutput]
        """
        return self._attribution_list

    @attribution_list.setter
    def attribution_list(self, attribution_list):
        """Sets the attribution_list of this DataForListVirusAlarmsOutput.


        :param attribution_list: The attribution_list of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: list[AttributionListForListVirusAlarmsOutput]
        """

        self._attribution_list = attribution_list

    @property
    def _class(self):
        """Gets the _class of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The _class of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self.__class

    @_class.setter
    def _class(self, _class):
        """Sets the _class of this DataForListVirusAlarmsOutput.


        :param _class: The _class of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self.__class = _class

    @property
    def cluster(self):
        """Gets the cluster of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The cluster of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: ClusterForListVirusAlarmsOutput
        """
        return self._cluster

    @cluster.setter
    def cluster(self, cluster):
        """Sets the cluster of this DataForListVirusAlarmsOutput.


        :param cluster: The cluster of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: ClusterForListVirusAlarmsOutput
        """

        self._cluster = cluster

    @property
    def data_type(self):
        """Gets the data_type of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The data_type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this DataForListVirusAlarmsOutput.


        :param data_type: The data_type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def error_reason(self):
        """Gets the error_reason of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The error_reason of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_reason

    @error_reason.setter
    def error_reason(self, error_reason):
        """Sets the error_reason of this DataForListVirusAlarmsOutput.


        :param error_reason: The error_reason of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._error_reason = error_reason

    @property
    def event_id(self):
        """Gets the event_id of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The event_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_id

    @event_id.setter
    def event_id(self, event_id):
        """Sets the event_id of this DataForListVirusAlarmsOutput.


        :param event_id: The event_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._event_id = event_id

    @property
    def event_name(self):
        """Gets the event_name of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The event_name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_name

    @event_name.setter
    def event_name(self, event_name):
        """Sets the event_name of this DataForListVirusAlarmsOutput.


        :param event_name: The event_name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._event_name = event_name

    @property
    def exe(self):
        """Gets the exe of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The exe of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this DataForListVirusAlarmsOutput.


        :param exe: The exe of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def file_downloadable(self):
        """Gets the file_downloadable of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The file_downloadable of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._file_downloadable

    @file_downloadable.setter
    def file_downloadable(self, file_downloadable):
        """Sets the file_downloadable of this DataForListVirusAlarmsOutput.


        :param file_downloadable: The file_downloadable of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: bool
        """

        self._file_downloadable = file_downloadable

    @property
    def file_hash(self):
        """Gets the file_hash of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The file_hash of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_hash

    @file_hash.setter
    def file_hash(self, file_hash):
        """Sets the file_hash of this DataForListVirusAlarmsOutput.


        :param file_hash: The file_hash of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._file_hash = file_hash

    @property
    def file_path(self):
        """Gets the file_path of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The file_path of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this DataForListVirusAlarmsOutput.


        :param file_path: The file_path of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def group_path(self):
        """Gets the group_path of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The group_path of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_path

    @group_path.setter
    def group_path(self, group_path):
        """Sets the group_path of this DataForListVirusAlarmsOutput.


        :param group_path: The group_path of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._group_path = group_path

    @property
    def handle_time(self):
        """Gets the handle_time of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The handle_time of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: int
        """
        return self._handle_time

    @handle_time.setter
    def handle_time(self, handle_time):
        """Sets the handle_time of this DataForListVirusAlarmsOutput.


        :param handle_time: The handle_time of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: int
        """

        self._handle_time = handle_time

    @property
    def host(self):
        """Gets the host of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The host of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: HostForListVirusAlarmsOutput
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this DataForListVirusAlarmsOutput.


        :param host: The host of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: HostForListVirusAlarmsOutput
        """

        self._host = host

    @property
    def image_name(self):
        """Gets the image_name of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The image_name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this DataForListVirusAlarmsOutput.


        :param image_name: The image_name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def in_docker(self):
        """Gets the in_docker of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The in_docker of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._in_docker

    @in_docker.setter
    def in_docker(self, in_docker):
        """Sets the in_docker of this DataForListVirusAlarmsOutput.


        :param in_docker: The in_docker of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._in_docker = in_docker

    @property
    def llm_analysis_result(self):
        """Gets the llm_analysis_result of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The llm_analysis_result of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._llm_analysis_result

    @llm_analysis_result.setter
    def llm_analysis_result(self, llm_analysis_result):
        """Sets the llm_analysis_result of this DataForListVirusAlarmsOutput.


        :param llm_analysis_result: The llm_analysis_result of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._llm_analysis_result = llm_analysis_result

    @property
    def llm_processed(self):
        """Gets the llm_processed of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The llm_processed of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._llm_processed

    @llm_processed.setter
    def llm_processed(self, llm_processed):
        """Sets the llm_processed of this DataForListVirusAlarmsOutput.


        :param llm_processed: The llm_processed of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: bool
        """

        self._llm_processed = llm_processed

    @property
    def level(self):
        """Gets the level of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The level of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this DataForListVirusAlarmsOutput.


        :param level: The level of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def mlp_instance_id(self):
        """Gets the mlp_instance_id of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The mlp_instance_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._mlp_instance_id

    @mlp_instance_id.setter
    def mlp_instance_id(self, mlp_instance_id):
        """Sets the mlp_instance_id of this DataForListVirusAlarmsOutput.


        :param mlp_instance_id: The mlp_instance_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._mlp_instance_id = mlp_instance_id

    @property
    def mlp_private_ip(self):
        """Gets the mlp_private_ip of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The mlp_private_ip of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._mlp_private_ip

    @mlp_private_ip.setter
    def mlp_private_ip(self, mlp_private_ip):
        """Sets the mlp_private_ip of this DataForListVirusAlarmsOutput.


        :param mlp_private_ip: The mlp_private_ip of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._mlp_private_ip = mlp_private_ip

    @property
    def name(self):
        """Gets the name of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListVirusAlarmsOutput.


        :param name: The name of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def ns_pid(self):
        """Gets the ns_pid of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The ns_pid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ns_pid

    @ns_pid.setter
    def ns_pid(self, ns_pid):
        """Sets the ns_pid of this DataForListVirusAlarmsOutput.


        :param ns_pid: The ns_pid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._ns_pid = ns_pid

    @property
    def os_type(self):
        """Gets the os_type of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The os_type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_type

    @os_type.setter
    def os_type(self, os_type):
        """Sets the os_type of this DataForListVirusAlarmsOutput.


        :param os_type: The os_type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._os_type = os_type

    @property
    def pid(self):
        """Gets the pid of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The pid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForListVirusAlarmsOutput.


        :param pid: The pid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def probe_hook(self):
        """Gets the probe_hook of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The probe_hook of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._probe_hook

    @probe_hook.setter
    def probe_hook(self, probe_hook):
        """Sets the probe_hook of this DataForListVirusAlarmsOutput.


        :param probe_hook: The probe_hook of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._probe_hook = probe_hook

    @property
    def region(self):
        """Gets the region of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The region of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListVirusAlarmsOutput.


        :param region: The region of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def sid(self):
        """Gets the sid of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The sid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sid

    @sid.setter
    def sid(self, sid):
        """Sets the sid of this DataForListVirusAlarmsOutput.


        :param sid: The sid of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._sid = sid

    @property
    def stack_trace_format(self):
        """Gets the stack_trace_format of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The stack_trace_format of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace_format

    @stack_trace_format.setter
    def stack_trace_format(self, stack_trace_format):
        """Sets the stack_trace_format of this DataForListVirusAlarmsOutput.


        :param stack_trace_format: The stack_trace_format of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace_format = stack_trace_format

    @property
    def stack_trace_hash(self):
        """Gets the stack_trace_hash of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The stack_trace_hash of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace_hash

    @stack_trace_hash.setter
    def stack_trace_hash(self, stack_trace_hash):
        """Sets the stack_trace_hash of this DataForListVirusAlarmsOutput.


        :param stack_trace_hash: The stack_trace_hash of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace_hash = stack_trace_hash

    @property
    def status(self):
        """Gets the status of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The status of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListVirusAlarmsOutput.


        :param status: The status of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def trace_id(self):
        """Gets the trace_id of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The trace_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this DataForListVirusAlarmsOutput.


        :param trace_id: The trace_id of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    @property
    def type(self):
        """Gets the type of this DataForListVirusAlarmsOutput.  # noqa: E501


        :return: The type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForListVirusAlarmsOutput.


        :param type: The type of this DataForListVirusAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListVirusAlarmsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListVirusAlarmsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListVirusAlarmsOutput):
            return True

        return self.to_dict() != other.to_dict()
