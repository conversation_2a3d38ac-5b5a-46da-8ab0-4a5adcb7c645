# coding: utf-8

"""
    tis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopActionDispatchRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_name': 'str',
        'request_json': 'str'
    }

    attribute_map = {
        'action_name': 'ActionName',
        'request_json': 'RequestJson'
    }

    def __init__(self, action_name=None, request_json=None, _configuration=None):  # noqa: E501
        """TopActionDispatchRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_name = None
        self._request_json = None
        self.discriminator = None

        self.action_name = action_name
        self.request_json = request_json

    @property
    def action_name(self):
        """Gets the action_name of this TopActionDispatchRequest.  # noqa: E501


        :return: The action_name of this TopActionDispatchRequest.  # noqa: E501
        :rtype: str
        """
        return self._action_name

    @action_name.setter
    def action_name(self, action_name):
        """Sets the action_name of this TopActionDispatchRequest.


        :param action_name: The action_name of this TopActionDispatchRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and action_name is None:
            raise ValueError("Invalid value for `action_name`, must not be `None`")  # noqa: E501

        self._action_name = action_name

    @property
    def request_json(self):
        """Gets the request_json of this TopActionDispatchRequest.  # noqa: E501


        :return: The request_json of this TopActionDispatchRequest.  # noqa: E501
        :rtype: str
        """
        return self._request_json

    @request_json.setter
    def request_json(self, request_json):
        """Sets the request_json of this TopActionDispatchRequest.


        :param request_json: The request_json of this TopActionDispatchRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and request_json is None:
            raise ValueError("Invalid value for `request_json`, must not be `None`")  # noqa: E501

        self._request_json = request_json

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopActionDispatchRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopActionDispatchRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopActionDispatchRequest):
            return True

        return self.to_dict() != other.to_dict()
