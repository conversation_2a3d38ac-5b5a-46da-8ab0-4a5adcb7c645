# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetLarkSubAccountInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'departments': 'list[DepartmentForGetLarkSubAccountInfoOutput]',
        'employee_no': 'str',
        'name': 'str'
    }

    attribute_map = {
        'departments': 'Departments',
        'employee_no': 'EmployeeNo',
        'name': 'Name'
    }

    def __init__(self, departments=None, employee_no=None, name=None, _configuration=None):  # noqa: E501
        """GetLarkSubAccountInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._departments = None
        self._employee_no = None
        self._name = None
        self.discriminator = None

        if departments is not None:
            self.departments = departments
        if employee_no is not None:
            self.employee_no = employee_no
        if name is not None:
            self.name = name

    @property
    def departments(self):
        """Gets the departments of this GetLarkSubAccountInfoResponse.  # noqa: E501


        :return: The departments of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :rtype: list[DepartmentForGetLarkSubAccountInfoOutput]
        """
        return self._departments

    @departments.setter
    def departments(self, departments):
        """Sets the departments of this GetLarkSubAccountInfoResponse.


        :param departments: The departments of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :type: list[DepartmentForGetLarkSubAccountInfoOutput]
        """

        self._departments = departments

    @property
    def employee_no(self):
        """Gets the employee_no of this GetLarkSubAccountInfoResponse.  # noqa: E501


        :return: The employee_no of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._employee_no

    @employee_no.setter
    def employee_no(self, employee_no):
        """Sets the employee_no of this GetLarkSubAccountInfoResponse.


        :param employee_no: The employee_no of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :type: str
        """

        self._employee_no = employee_no

    @property
    def name(self):
        """Gets the name of this GetLarkSubAccountInfoResponse.  # noqa: E501


        :return: The name of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetLarkSubAccountInfoResponse.


        :param name: The name of this GetLarkSubAccountInfoResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetLarkSubAccountInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetLarkSubAccountInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetLarkSubAccountInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
