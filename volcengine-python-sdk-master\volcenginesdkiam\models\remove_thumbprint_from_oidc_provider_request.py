# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RemoveThumbprintFromOIDCProviderRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'oidc_provider_name': 'str',
        'thumbprint': 'str'
    }

    attribute_map = {
        'oidc_provider_name': 'OIDCProviderName',
        'thumbprint': 'Thumbprint'
    }

    def __init__(self, oidc_provider_name=None, thumbprint=None, _configuration=None):  # noqa: E501
        """RemoveThumbprintFromOIDCProviderRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._oidc_provider_name = None
        self._thumbprint = None
        self.discriminator = None

        self.oidc_provider_name = oidc_provider_name
        self.thumbprint = thumbprint

    @property
    def oidc_provider_name(self):
        """Gets the oidc_provider_name of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501


        :return: The oidc_provider_name of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501
        :rtype: str
        """
        return self._oidc_provider_name

    @oidc_provider_name.setter
    def oidc_provider_name(self, oidc_provider_name):
        """Sets the oidc_provider_name of this RemoveThumbprintFromOIDCProviderRequest.


        :param oidc_provider_name: The oidc_provider_name of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and oidc_provider_name is None:
            raise ValueError("Invalid value for `oidc_provider_name`, must not be `None`")  # noqa: E501

        self._oidc_provider_name = oidc_provider_name

    @property
    def thumbprint(self):
        """Gets the thumbprint of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501


        :return: The thumbprint of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501
        :rtype: str
        """
        return self._thumbprint

    @thumbprint.setter
    def thumbprint(self, thumbprint):
        """Sets the thumbprint of this RemoveThumbprintFromOIDCProviderRequest.


        :param thumbprint: The thumbprint of this RemoveThumbprintFromOIDCProviderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and thumbprint is None:
            raise ValueError("Invalid value for `thumbprint`, must not be `None`")  # noqa: E501

        self._thumbprint = thumbprint

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RemoveThumbprintFromOIDCProviderRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RemoveThumbprintFromOIDCProviderRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RemoveThumbprintFromOIDCProviderRequest):
            return True

        return self.to_dict() != other.to_dict()
