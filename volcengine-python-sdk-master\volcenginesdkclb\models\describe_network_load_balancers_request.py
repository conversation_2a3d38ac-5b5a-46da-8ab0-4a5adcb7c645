# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNetworkLoadBalancersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ip_address_version': 'str',
        'load_balancer_ids': 'list[str]',
        'load_balancer_name': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'status': 'str',
        'tag_filters': 'list[TagFilterForDescribeNetworkLoadBalancersInput]',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'ip_address_version': 'IpAddressVersion',
        'load_balancer_ids': 'LoadBalancerIds',
        'load_balancer_name': 'LoadBalancerName',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, ip_address_version=None, load_balancer_ids=None, load_balancer_name=None, max_results=None, next_token=None, project_name=None, status=None, tag_filters=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeNetworkLoadBalancersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ip_address_version = None
        self._load_balancer_ids = None
        self._load_balancer_name = None
        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._status = None
        self._tag_filters = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if ip_address_version is not None:
            self.ip_address_version = ip_address_version
        if load_balancer_ids is not None:
            self.load_balancer_ids = load_balancer_ids
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def ip_address_version(self):
        """Gets the ip_address_version of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The ip_address_version of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_version

    @ip_address_version.setter
    def ip_address_version(self, ip_address_version):
        """Sets the ip_address_version of this DescribeNetworkLoadBalancersRequest.


        :param ip_address_version: The ip_address_version of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._ip_address_version = ip_address_version

    @property
    def load_balancer_ids(self):
        """Gets the load_balancer_ids of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The load_balancer_ids of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._load_balancer_ids

    @load_balancer_ids.setter
    def load_balancer_ids(self, load_balancer_ids):
        """Sets the load_balancer_ids of this DescribeNetworkLoadBalancersRequest.


        :param load_balancer_ids: The load_balancer_ids of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: list[str]
        """

        self._load_balancer_ids = load_balancer_ids

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The load_balancer_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this DescribeNetworkLoadBalancersRequest.


        :param load_balancer_name: The load_balancer_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def max_results(self):
        """Gets the max_results of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The max_results of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeNetworkLoadBalancersRequest.


        :param max_results: The max_results of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The next_token of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeNetworkLoadBalancersRequest.


        :param next_token: The next_token of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The project_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeNetworkLoadBalancersRequest.


        :param project_name: The project_name of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The status of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNetworkLoadBalancersRequest.


        :param status: The status of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The tag_filters of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeNetworkLoadBalancersInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeNetworkLoadBalancersRequest.


        :param tag_filters: The tag_filters of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: list[TagFilterForDescribeNetworkLoadBalancersInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The vpc_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeNetworkLoadBalancersRequest.


        :param vpc_id: The vpc_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501


        :return: The zone_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeNetworkLoadBalancersRequest.


        :param zone_id: The zone_id of this DescribeNetworkLoadBalancersRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNetworkLoadBalancersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNetworkLoadBalancersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNetworkLoadBalancersRequest):
            return True

        return self.to_dict() != other.to_dict()
