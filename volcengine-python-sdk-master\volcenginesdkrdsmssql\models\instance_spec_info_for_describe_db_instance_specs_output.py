# coding: utf-8

"""
    rds_mssql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceSpecInfoForDescribeDBInstanceSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_engine_version': 'str',
        'generation': 'str',
        'instance_type': 'str',
        'memory': 'int',
        'spec_code': 'str',
        'spec_family': 'str',
        'spec_sale_type': 'str',
        'spec_status': 'str',
        'vcpu': 'int',
        'zone_id': 'str'
    }

    attribute_map = {
        'db_engine_version': 'DBEngineVersion',
        'generation': 'Generation',
        'instance_type': 'InstanceType',
        'memory': 'Memory',
        'spec_code': 'SpecCode',
        'spec_family': 'SpecFamily',
        'spec_sale_type': 'SpecSaleType',
        'spec_status': 'SpecStatus',
        'vcpu': 'VCPU',
        'zone_id': 'ZoneId'
    }

    def __init__(self, db_engine_version=None, generation=None, instance_type=None, memory=None, spec_code=None, spec_family=None, spec_sale_type=None, spec_status=None, vcpu=None, zone_id=None, _configuration=None):  # noqa: E501
        """InstanceSpecInfoForDescribeDBInstanceSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_engine_version = None
        self._generation = None
        self._instance_type = None
        self._memory = None
        self._spec_code = None
        self._spec_family = None
        self._spec_sale_type = None
        self._spec_status = None
        self._vcpu = None
        self._zone_id = None
        self.discriminator = None

        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if generation is not None:
            self.generation = generation
        if instance_type is not None:
            self.instance_type = instance_type
        if memory is not None:
            self.memory = memory
        if spec_code is not None:
            self.spec_code = spec_code
        if spec_family is not None:
            self.spec_family = spec_family
        if spec_sale_type is not None:
            self.spec_sale_type = spec_sale_type
        if spec_status is not None:
            self.spec_status = spec_status
        if vcpu is not None:
            self.vcpu = vcpu
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The db_engine_version of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param db_engine_version: The db_engine_version of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def generation(self):
        """Gets the generation of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The generation of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._generation

    @generation.setter
    def generation(self, generation):
        """Sets the generation of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param generation: The generation of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._generation = generation

    @property
    def instance_type(self):
        """Gets the instance_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The instance_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param instance_type: The instance_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def memory(self):
        """Gets the memory of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The memory of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param memory: The memory of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def spec_code(self):
        """Gets the spec_code of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The spec_code of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_code

    @spec_code.setter
    def spec_code(self, spec_code):
        """Sets the spec_code of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param spec_code: The spec_code of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_code = spec_code

    @property
    def spec_family(self):
        """Gets the spec_family of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The spec_family of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_family

    @spec_family.setter
    def spec_family(self, spec_family):
        """Sets the spec_family of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param spec_family: The spec_family of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_family = spec_family

    @property
    def spec_sale_type(self):
        """Gets the spec_sale_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The spec_sale_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_sale_type

    @spec_sale_type.setter
    def spec_sale_type(self, spec_sale_type):
        """Sets the spec_sale_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param spec_sale_type: The spec_sale_type of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_sale_type = spec_sale_type

    @property
    def spec_status(self):
        """Gets the spec_status of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The spec_status of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_status

    @spec_status.setter
    def spec_status(self, spec_status):
        """Sets the spec_status of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param spec_status: The spec_status of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_status = spec_status

    @property
    def vcpu(self):
        """Gets the vcpu of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The vcpu of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param vcpu: The vcpu of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def zone_id(self):
        """Gets the zone_id of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The zone_id of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.


        :param zone_id: The zone_id of this InstanceSpecInfoForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceSpecInfoForDescribeDBInstanceSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceSpecInfoForDescribeDBInstanceSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceSpecInfoForDescribeDBInstanceSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
