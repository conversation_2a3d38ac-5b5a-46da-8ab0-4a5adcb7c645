# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAccountUserTrackDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'str',
        'select_tags': 'list[SelectTagForGetAccountUserTrackDataInput]',
        'user_id': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'select_tags': 'SelectTags',
        'user_id': 'UserId'
    }

    def __init__(self, activity_id=None, select_tags=None, user_id=None, _configuration=None):  # noqa: E501
        """GetAccountUserTrackDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._select_tags = None
        self._user_id = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if select_tags is not None:
            self.select_tags = select_tags
        self.user_id = user_id

    @property
    def activity_id(self):
        """Gets the activity_id of this GetAccountUserTrackDataRequest.  # noqa: E501


        :return: The activity_id of this GetAccountUserTrackDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetAccountUserTrackDataRequest.


        :param activity_id: The activity_id of this GetAccountUserTrackDataRequest.  # noqa: E501
        :type: str
        """

        self._activity_id = activity_id

    @property
    def select_tags(self):
        """Gets the select_tags of this GetAccountUserTrackDataRequest.  # noqa: E501


        :return: The select_tags of this GetAccountUserTrackDataRequest.  # noqa: E501
        :rtype: list[SelectTagForGetAccountUserTrackDataInput]
        """
        return self._select_tags

    @select_tags.setter
    def select_tags(self, select_tags):
        """Sets the select_tags of this GetAccountUserTrackDataRequest.


        :param select_tags: The select_tags of this GetAccountUserTrackDataRequest.  # noqa: E501
        :type: list[SelectTagForGetAccountUserTrackDataInput]
        """

        self._select_tags = select_tags

    @property
    def user_id(self):
        """Gets the user_id of this GetAccountUserTrackDataRequest.  # noqa: E501


        :return: The user_id of this GetAccountUserTrackDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this GetAccountUserTrackDataRequest.


        :param user_id: The user_id of this GetAccountUserTrackDataRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user_id is None:
            raise ValueError("Invalid value for `user_id`, must not be `None`")  # noqa: E501

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAccountUserTrackDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAccountUserTrackDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAccountUserTrackDataRequest):
            return True

        return self.to_dict() != other.to_dict()
