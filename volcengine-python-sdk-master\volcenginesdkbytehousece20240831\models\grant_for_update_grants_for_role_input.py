# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GrantForUpdateGrantsForRoleInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'int',
        'grant_option': 'bool',
        'grant_to': 'str',
        'on_cluster': 'str',
        'privileges': 'list[PrivilegeForUpdateGrantsForRoleInput]',
        'raw_sql': 'str',
        'targets': 'list[TargetForUpdateGrantsForRoleInput]'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'grant_option': 'GrantOption',
        'grant_to': 'GrantTo',
        'on_cluster': 'OnCluster',
        'privileges': 'Privileges',
        'raw_sql': 'RawSql',
        'targets': 'Targets'
    }

    def __init__(self, cluster_id=None, grant_option=None, grant_to=None, on_cluster=None, privileges=None, raw_sql=None, targets=None, _configuration=None):  # noqa: E501
        """GrantForUpdateGrantsForRoleInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._grant_option = None
        self._grant_to = None
        self._on_cluster = None
        self._privileges = None
        self._raw_sql = None
        self._targets = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if grant_option is not None:
            self.grant_option = grant_option
        if grant_to is not None:
            self.grant_to = grant_to
        if on_cluster is not None:
            self.on_cluster = on_cluster
        if privileges is not None:
            self.privileges = privileges
        if raw_sql is not None:
            self.raw_sql = raw_sql
        if targets is not None:
            self.targets = targets

    @property
    def cluster_id(self):
        """Gets the cluster_id of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The cluster_id of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: int
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this GrantForUpdateGrantsForRoleInput.


        :param cluster_id: The cluster_id of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: int
        """

        self._cluster_id = cluster_id

    @property
    def grant_option(self):
        """Gets the grant_option of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The grant_option of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: bool
        """
        return self._grant_option

    @grant_option.setter
    def grant_option(self, grant_option):
        """Sets the grant_option of this GrantForUpdateGrantsForRoleInput.


        :param grant_option: The grant_option of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: bool
        """

        self._grant_option = grant_option

    @property
    def grant_to(self):
        """Gets the grant_to of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The grant_to of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: str
        """
        return self._grant_to

    @grant_to.setter
    def grant_to(self, grant_to):
        """Sets the grant_to of this GrantForUpdateGrantsForRoleInput.


        :param grant_to: The grant_to of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: str
        """

        self._grant_to = grant_to

    @property
    def on_cluster(self):
        """Gets the on_cluster of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The on_cluster of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: str
        """
        return self._on_cluster

    @on_cluster.setter
    def on_cluster(self, on_cluster):
        """Sets the on_cluster of this GrantForUpdateGrantsForRoleInput.


        :param on_cluster: The on_cluster of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: str
        """

        self._on_cluster = on_cluster

    @property
    def privileges(self):
        """Gets the privileges of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The privileges of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: list[PrivilegeForUpdateGrantsForRoleInput]
        """
        return self._privileges

    @privileges.setter
    def privileges(self, privileges):
        """Sets the privileges of this GrantForUpdateGrantsForRoleInput.


        :param privileges: The privileges of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: list[PrivilegeForUpdateGrantsForRoleInput]
        """

        self._privileges = privileges

    @property
    def raw_sql(self):
        """Gets the raw_sql of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The raw_sql of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: str
        """
        return self._raw_sql

    @raw_sql.setter
    def raw_sql(self, raw_sql):
        """Sets the raw_sql of this GrantForUpdateGrantsForRoleInput.


        :param raw_sql: The raw_sql of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: str
        """

        self._raw_sql = raw_sql

    @property
    def targets(self):
        """Gets the targets of this GrantForUpdateGrantsForRoleInput.  # noqa: E501


        :return: The targets of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :rtype: list[TargetForUpdateGrantsForRoleInput]
        """
        return self._targets

    @targets.setter
    def targets(self, targets):
        """Sets the targets of this GrantForUpdateGrantsForRoleInput.


        :param targets: The targets of this GrantForUpdateGrantsForRoleInput.  # noqa: E501
        :type: list[TargetForUpdateGrantsForRoleInput]
        """

        self._targets = targets

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GrantForUpdateGrantsForRoleInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GrantForUpdateGrantsForRoleInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GrantForUpdateGrantsForRoleInput):
            return True

        return self.to_dict() != other.to_dict()
