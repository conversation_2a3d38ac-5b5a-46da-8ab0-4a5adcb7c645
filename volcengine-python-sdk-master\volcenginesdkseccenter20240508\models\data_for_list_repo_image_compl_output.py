# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRepoImageComplOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'check_type': 'str',
        'check_type_id': 'str',
        'id': 'str',
        'rule_id': 'str',
        'section_desc': 'str',
        'section_id': 'str',
        'severity': 'str',
        'status': 'str'
    }

    attribute_map = {
        'check_type': 'CheckType',
        'check_type_id': 'CheckTypeID',
        'id': 'ID',
        'rule_id': 'RuleID',
        'section_desc': 'SectionDesc',
        'section_id': 'SectionID',
        'severity': 'Severity',
        'status': 'Status'
    }

    def __init__(self, check_type=None, check_type_id=None, id=None, rule_id=None, section_desc=None, section_id=None, severity=None, status=None, _configuration=None):  # noqa: E501
        """DataForListRepoImageComplOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._check_type = None
        self._check_type_id = None
        self._id = None
        self._rule_id = None
        self._section_desc = None
        self._section_id = None
        self._severity = None
        self._status = None
        self.discriminator = None

        if check_type is not None:
            self.check_type = check_type
        if check_type_id is not None:
            self.check_type_id = check_type_id
        if id is not None:
            self.id = id
        if rule_id is not None:
            self.rule_id = rule_id
        if section_desc is not None:
            self.section_desc = section_desc
        if section_id is not None:
            self.section_id = section_id
        if severity is not None:
            self.severity = severity
        if status is not None:
            self.status = status

    @property
    def check_type(self):
        """Gets the check_type of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The check_type of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_type

    @check_type.setter
    def check_type(self, check_type):
        """Sets the check_type of this DataForListRepoImageComplOutput.


        :param check_type: The check_type of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._check_type = check_type

    @property
    def check_type_id(self):
        """Gets the check_type_id of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The check_type_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_type_id

    @check_type_id.setter
    def check_type_id(self, check_type_id):
        """Sets the check_type_id of this DataForListRepoImageComplOutput.


        :param check_type_id: The check_type_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._check_type_id = check_type_id

    @property
    def id(self):
        """Gets the id of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The id of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRepoImageComplOutput.


        :param id: The id of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def rule_id(self):
        """Gets the rule_id of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The rule_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DataForListRepoImageComplOutput.


        :param rule_id: The rule_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def section_desc(self):
        """Gets the section_desc of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The section_desc of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._section_desc

    @section_desc.setter
    def section_desc(self, section_desc):
        """Sets the section_desc of this DataForListRepoImageComplOutput.


        :param section_desc: The section_desc of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._section_desc = section_desc

    @property
    def section_id(self):
        """Gets the section_id of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The section_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._section_id

    @section_id.setter
    def section_id(self, section_id):
        """Sets the section_id of this DataForListRepoImageComplOutput.


        :param section_id: The section_id of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._section_id = section_id

    @property
    def severity(self):
        """Gets the severity of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The severity of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._severity

    @severity.setter
    def severity(self, severity):
        """Sets the severity of this DataForListRepoImageComplOutput.


        :param severity: The severity of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._severity = severity

    @property
    def status(self):
        """Gets the status of this DataForListRepoImageComplOutput.  # noqa: E501


        :return: The status of this DataForListRepoImageComplOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListRepoImageComplOutput.


        :param status: The status of this DataForListRepoImageComplOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRepoImageComplOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRepoImageComplOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRepoImageComplOutput):
            return True

        return self.to_dict() != other.to_dict()
