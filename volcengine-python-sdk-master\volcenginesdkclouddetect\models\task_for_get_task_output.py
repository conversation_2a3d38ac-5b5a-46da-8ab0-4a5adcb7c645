# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskForGetTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'create_time': 'int',
        'enable_shuffle': 'bool',
        'finish_time': 'int',
        'http_method': 'int',
        'id': 'int',
        'interval_seconds': 'int',
        'labels': 'list[LabelForGetTaskOutput]',
        'line_id_list': 'list[int]',
        'name': 'str',
        'owner': 'str',
        'period_config': 'PeriodConfigForGetTaskOutput',
        'runner_id_list': 'list[int]',
        'status': 'int',
        'sub_task_type': 'int',
        'task_group_id': 'int',
        'task_group_name': 'str',
        'task_type': 'int',
        'update_time': 'int'
    }

    attribute_map = {
        'address': 'Address',
        'create_time': 'CreateTime',
        'enable_shuffle': 'EnableShuffle',
        'finish_time': 'FinishTime',
        'http_method': 'HTTPMethod',
        'id': 'ID',
        'interval_seconds': 'IntervalSeconds',
        'labels': 'Labels',
        'line_id_list': 'LineIDList',
        'name': 'Name',
        'owner': 'Owner',
        'period_config': 'PeriodConfig',
        'runner_id_list': 'RunnerIDList',
        'status': 'Status',
        'sub_task_type': 'SubTaskType',
        'task_group_id': 'TaskGroupID',
        'task_group_name': 'TaskGroupName',
        'task_type': 'TaskType',
        'update_time': 'UpdateTime'
    }

    def __init__(self, address=None, create_time=None, enable_shuffle=None, finish_time=None, http_method=None, id=None, interval_seconds=None, labels=None, line_id_list=None, name=None, owner=None, period_config=None, runner_id_list=None, status=None, sub_task_type=None, task_group_id=None, task_group_name=None, task_type=None, update_time=None, _configuration=None):  # noqa: E501
        """TaskForGetTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._create_time = None
        self._enable_shuffle = None
        self._finish_time = None
        self._http_method = None
        self._id = None
        self._interval_seconds = None
        self._labels = None
        self._line_id_list = None
        self._name = None
        self._owner = None
        self._period_config = None
        self._runner_id_list = None
        self._status = None
        self._sub_task_type = None
        self._task_group_id = None
        self._task_group_name = None
        self._task_type = None
        self._update_time = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if create_time is not None:
            self.create_time = create_time
        if enable_shuffle is not None:
            self.enable_shuffle = enable_shuffle
        if finish_time is not None:
            self.finish_time = finish_time
        if http_method is not None:
            self.http_method = http_method
        if id is not None:
            self.id = id
        if interval_seconds is not None:
            self.interval_seconds = interval_seconds
        if labels is not None:
            self.labels = labels
        if line_id_list is not None:
            self.line_id_list = line_id_list
        if name is not None:
            self.name = name
        if owner is not None:
            self.owner = owner
        if period_config is not None:
            self.period_config = period_config
        if runner_id_list is not None:
            self.runner_id_list = runner_id_list
        if status is not None:
            self.status = status
        if sub_task_type is not None:
            self.sub_task_type = sub_task_type
        if task_group_id is not None:
            self.task_group_id = task_group_id
        if task_group_name is not None:
            self.task_group_name = task_group_name
        if task_type is not None:
            self.task_type = task_type
        if update_time is not None:
            self.update_time = update_time

    @property
    def address(self):
        """Gets the address of this TaskForGetTaskOutput.  # noqa: E501


        :return: The address of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this TaskForGetTaskOutput.


        :param address: The address of this TaskForGetTaskOutput.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def create_time(self):
        """Gets the create_time of this TaskForGetTaskOutput.  # noqa: E501


        :return: The create_time of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this TaskForGetTaskOutput.


        :param create_time: The create_time of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def enable_shuffle(self):
        """Gets the enable_shuffle of this TaskForGetTaskOutput.  # noqa: E501


        :return: The enable_shuffle of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_shuffle

    @enable_shuffle.setter
    def enable_shuffle(self, enable_shuffle):
        """Sets the enable_shuffle of this TaskForGetTaskOutput.


        :param enable_shuffle: The enable_shuffle of this TaskForGetTaskOutput.  # noqa: E501
        :type: bool
        """

        self._enable_shuffle = enable_shuffle

    @property
    def finish_time(self):
        """Gets the finish_time of this TaskForGetTaskOutput.  # noqa: E501


        :return: The finish_time of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._finish_time

    @finish_time.setter
    def finish_time(self, finish_time):
        """Sets the finish_time of this TaskForGetTaskOutput.


        :param finish_time: The finish_time of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._finish_time = finish_time

    @property
    def http_method(self):
        """Gets the http_method of this TaskForGetTaskOutput.  # noqa: E501


        :return: The http_method of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._http_method

    @http_method.setter
    def http_method(self, http_method):
        """Sets the http_method of this TaskForGetTaskOutput.


        :param http_method: The http_method of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._http_method = http_method

    @property
    def id(self):
        """Gets the id of this TaskForGetTaskOutput.  # noqa: E501


        :return: The id of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this TaskForGetTaskOutput.


        :param id: The id of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def interval_seconds(self):
        """Gets the interval_seconds of this TaskForGetTaskOutput.  # noqa: E501


        :return: The interval_seconds of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._interval_seconds

    @interval_seconds.setter
    def interval_seconds(self, interval_seconds):
        """Sets the interval_seconds of this TaskForGetTaskOutput.


        :param interval_seconds: The interval_seconds of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._interval_seconds = interval_seconds

    @property
    def labels(self):
        """Gets the labels of this TaskForGetTaskOutput.  # noqa: E501


        :return: The labels of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: list[LabelForGetTaskOutput]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this TaskForGetTaskOutput.


        :param labels: The labels of this TaskForGetTaskOutput.  # noqa: E501
        :type: list[LabelForGetTaskOutput]
        """

        self._labels = labels

    @property
    def line_id_list(self):
        """Gets the line_id_list of this TaskForGetTaskOutput.  # noqa: E501


        :return: The line_id_list of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._line_id_list

    @line_id_list.setter
    def line_id_list(self, line_id_list):
        """Sets the line_id_list of this TaskForGetTaskOutput.


        :param line_id_list: The line_id_list of this TaskForGetTaskOutput.  # noqa: E501
        :type: list[int]
        """

        self._line_id_list = line_id_list

    @property
    def name(self):
        """Gets the name of this TaskForGetTaskOutput.  # noqa: E501


        :return: The name of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this TaskForGetTaskOutput.


        :param name: The name of this TaskForGetTaskOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner(self):
        """Gets the owner of this TaskForGetTaskOutput.  # noqa: E501


        :return: The owner of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this TaskForGetTaskOutput.


        :param owner: The owner of this TaskForGetTaskOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def period_config(self):
        """Gets the period_config of this TaskForGetTaskOutput.  # noqa: E501


        :return: The period_config of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: PeriodConfigForGetTaskOutput
        """
        return self._period_config

    @period_config.setter
    def period_config(self, period_config):
        """Sets the period_config of this TaskForGetTaskOutput.


        :param period_config: The period_config of this TaskForGetTaskOutput.  # noqa: E501
        :type: PeriodConfigForGetTaskOutput
        """

        self._period_config = period_config

    @property
    def runner_id_list(self):
        """Gets the runner_id_list of this TaskForGetTaskOutput.  # noqa: E501


        :return: The runner_id_list of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._runner_id_list

    @runner_id_list.setter
    def runner_id_list(self, runner_id_list):
        """Sets the runner_id_list of this TaskForGetTaskOutput.


        :param runner_id_list: The runner_id_list of this TaskForGetTaskOutput.  # noqa: E501
        :type: list[int]
        """

        self._runner_id_list = runner_id_list

    @property
    def status(self):
        """Gets the status of this TaskForGetTaskOutput.  # noqa: E501


        :return: The status of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TaskForGetTaskOutput.


        :param status: The status of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def sub_task_type(self):
        """Gets the sub_task_type of this TaskForGetTaskOutput.  # noqa: E501


        :return: The sub_task_type of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_type

    @sub_task_type.setter
    def sub_task_type(self, sub_task_type):
        """Sets the sub_task_type of this TaskForGetTaskOutput.


        :param sub_task_type: The sub_task_type of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_type = sub_task_type

    @property
    def task_group_id(self):
        """Gets the task_group_id of this TaskForGetTaskOutput.  # noqa: E501


        :return: The task_group_id of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._task_group_id

    @task_group_id.setter
    def task_group_id(self, task_group_id):
        """Sets the task_group_id of this TaskForGetTaskOutput.


        :param task_group_id: The task_group_id of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._task_group_id = task_group_id

    @property
    def task_group_name(self):
        """Gets the task_group_name of this TaskForGetTaskOutput.  # noqa: E501


        :return: The task_group_name of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_group_name

    @task_group_name.setter
    def task_group_name(self, task_group_name):
        """Sets the task_group_name of this TaskForGetTaskOutput.


        :param task_group_name: The task_group_name of this TaskForGetTaskOutput.  # noqa: E501
        :type: str
        """

        self._task_group_name = task_group_name

    @property
    def task_type(self):
        """Gets the task_type of this TaskForGetTaskOutput.  # noqa: E501


        :return: The task_type of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this TaskForGetTaskOutput.


        :param task_type: The task_type of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._task_type = task_type

    @property
    def update_time(self):
        """Gets the update_time of this TaskForGetTaskOutput.  # noqa: E501


        :return: The update_time of this TaskForGetTaskOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TaskForGetTaskOutput.


        :param update_time: The update_time of this TaskForGetTaskOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskForGetTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskForGetTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskForGetTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
