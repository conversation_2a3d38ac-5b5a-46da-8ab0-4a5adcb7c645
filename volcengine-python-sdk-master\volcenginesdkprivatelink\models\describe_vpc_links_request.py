# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVpcLinksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'customer_account_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'private_link_gateway_id': 'str',
        'vpc_link_ids': 'list[str]',
        'vpc_link_name': 'str'
    }

    attribute_map = {
        'customer_account_id': 'CustomerAccountId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'private_link_gateway_id': 'PrivateLinkGatewayId',
        'vpc_link_ids': 'VpcLinkIds',
        'vpc_link_name': 'VpcLinkName'
    }

    def __init__(self, customer_account_id=None, page_number=None, page_size=None, private_link_gateway_id=None, vpc_link_ids=None, vpc_link_name=None, _configuration=None):  # noqa: E501
        """DescribeVpcLinksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._customer_account_id = None
        self._page_number = None
        self._page_size = None
        self._private_link_gateway_id = None
        self._vpc_link_ids = None
        self._vpc_link_name = None
        self.discriminator = None

        if customer_account_id is not None:
            self.customer_account_id = customer_account_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if private_link_gateway_id is not None:
            self.private_link_gateway_id = private_link_gateway_id
        if vpc_link_ids is not None:
            self.vpc_link_ids = vpc_link_ids
        if vpc_link_name is not None:
            self.vpc_link_name = vpc_link_name

    @property
    def customer_account_id(self):
        """Gets the customer_account_id of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The customer_account_id of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_account_id

    @customer_account_id.setter
    def customer_account_id(self, customer_account_id):
        """Sets the customer_account_id of this DescribeVpcLinksRequest.


        :param customer_account_id: The customer_account_id of this DescribeVpcLinksRequest.  # noqa: E501
        :type: str
        """

        self._customer_account_id = customer_account_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The page_number of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeVpcLinksRequest.


        :param page_number: The page_number of this DescribeVpcLinksRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The page_size of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeVpcLinksRequest.


        :param page_size: The page_size of this DescribeVpcLinksRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def private_link_gateway_id(self):
        """Gets the private_link_gateway_id of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The private_link_gateway_id of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: str
        """
        return self._private_link_gateway_id

    @private_link_gateway_id.setter
    def private_link_gateway_id(self, private_link_gateway_id):
        """Sets the private_link_gateway_id of this DescribeVpcLinksRequest.


        :param private_link_gateway_id: The private_link_gateway_id of this DescribeVpcLinksRequest.  # noqa: E501
        :type: str
        """

        self._private_link_gateway_id = private_link_gateway_id

    @property
    def vpc_link_ids(self):
        """Gets the vpc_link_ids of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The vpc_link_ids of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_link_ids

    @vpc_link_ids.setter
    def vpc_link_ids(self, vpc_link_ids):
        """Sets the vpc_link_ids of this DescribeVpcLinksRequest.


        :param vpc_link_ids: The vpc_link_ids of this DescribeVpcLinksRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_link_ids = vpc_link_ids

    @property
    def vpc_link_name(self):
        """Gets the vpc_link_name of this DescribeVpcLinksRequest.  # noqa: E501


        :return: The vpc_link_name of this DescribeVpcLinksRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_link_name

    @vpc_link_name.setter
    def vpc_link_name(self, vpc_link_name):
        """Sets the vpc_link_name of this DescribeVpcLinksRequest.


        :param vpc_link_name: The vpc_link_name of this DescribeVpcLinksRequest.  # noqa: E501
        :type: str
        """

        self._vpc_link_name = vpc_link_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVpcLinksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVpcLinksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVpcLinksRequest):
            return True

        return self.to_dict() != other.to_dict()
