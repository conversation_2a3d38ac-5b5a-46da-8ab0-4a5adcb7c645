# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeSlowLogsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_ip': 'str',
        'db_name': 'str',
        'duration': 'int',
        'file_scan': 'int',
        'index_scan': 'int',
        '_return': 'int',
        'sql': 'str',
        'start_time': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'client_ip': 'ClientIP',
        'db_name': 'DBName',
        'duration': 'Duration',
        'file_scan': 'FileScan',
        'index_scan': 'IndexScan',
        '_return': 'Return',
        'sql': 'SQL',
        'start_time': 'StartTime',
        'user_name': 'UserName'
    }

    def __init__(self, client_ip=None, db_name=None, duration=None, file_scan=None, index_scan=None, _return=None, sql=None, start_time=None, user_name=None, _configuration=None):  # noqa: E501
        """DataForDescribeSlowLogsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_ip = None
        self._db_name = None
        self._duration = None
        self._file_scan = None
        self._index_scan = None
        self.__return = None
        self._sql = None
        self._start_time = None
        self._user_name = None
        self.discriminator = None

        if client_ip is not None:
            self.client_ip = client_ip
        if db_name is not None:
            self.db_name = db_name
        if duration is not None:
            self.duration = duration
        if file_scan is not None:
            self.file_scan = file_scan
        if index_scan is not None:
            self.index_scan = index_scan
        if _return is not None:
            self._return = _return
        if sql is not None:
            self.sql = sql
        if start_time is not None:
            self.start_time = start_time
        if user_name is not None:
            self.user_name = user_name

    @property
    def client_ip(self):
        """Gets the client_ip of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The client_ip of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._client_ip

    @client_ip.setter
    def client_ip(self, client_ip):
        """Sets the client_ip of this DataForDescribeSlowLogsOutput.


        :param client_ip: The client_ip of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._client_ip = client_ip

    @property
    def db_name(self):
        """Gets the db_name of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The db_name of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_name

    @db_name.setter
    def db_name(self, db_name):
        """Sets the db_name of this DataForDescribeSlowLogsOutput.


        :param db_name: The db_name of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._db_name = db_name

    @property
    def duration(self):
        """Gets the duration of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The duration of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this DataForDescribeSlowLogsOutput.


        :param duration: The duration of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._duration = duration

    @property
    def file_scan(self):
        """Gets the file_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The file_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_scan

    @file_scan.setter
    def file_scan(self, file_scan):
        """Sets the file_scan of this DataForDescribeSlowLogsOutput.


        :param file_scan: The file_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._file_scan = file_scan

    @property
    def index_scan(self):
        """Gets the index_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The index_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self._index_scan

    @index_scan.setter
    def index_scan(self, index_scan):
        """Sets the index_scan of this DataForDescribeSlowLogsOutput.


        :param index_scan: The index_scan of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self._index_scan = index_scan

    @property
    def _return(self):
        """Gets the _return of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The _return of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: int
        """
        return self.__return

    @_return.setter
    def _return(self, _return):
        """Sets the _return of this DataForDescribeSlowLogsOutput.


        :param _return: The _return of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: int
        """

        self.__return = _return

    @property
    def sql(self):
        """Gets the sql of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The sql of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sql

    @sql.setter
    def sql(self, sql):
        """Sets the sql of this DataForDescribeSlowLogsOutput.


        :param sql: The sql of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._sql = sql

    @property
    def start_time(self):
        """Gets the start_time of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The start_time of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForDescribeSlowLogsOutput.


        :param start_time: The start_time of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def user_name(self):
        """Gets the user_name of this DataForDescribeSlowLogsOutput.  # noqa: E501


        :return: The user_name of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this DataForDescribeSlowLogsOutput.


        :param user_name: The user_name of this DataForDescribeSlowLogsOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeSlowLogsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeSlowLogsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeSlowLogsOutput):
            return True

        return self.to_dict() != other.to_dict()
