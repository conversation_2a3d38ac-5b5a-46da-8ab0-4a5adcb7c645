# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmSourceProductForGetApiV1AlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_source_product_id': 'str',
        'alarm_source_product_name': 'str',
        'resource_vendor': 'str'
    }

    attribute_map = {
        'alarm_source_product_id': 'alarm_source_product_id',
        'alarm_source_product_name': 'alarm_source_product_name',
        'resource_vendor': 'resource_vendor'
    }

    def __init__(self, alarm_source_product_id=None, alarm_source_product_name=None, resource_vendor=None, _configuration=None):  # noqa: E501
        """AlarmSourceProductForGetApiV1AlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_source_product_id = None
        self._alarm_source_product_name = None
        self._resource_vendor = None
        self.discriminator = None

        if alarm_source_product_id is not None:
            self.alarm_source_product_id = alarm_source_product_id
        if alarm_source_product_name is not None:
            self.alarm_source_product_name = alarm_source_product_name
        if resource_vendor is not None:
            self.resource_vendor = resource_vendor

    @property
    def alarm_source_product_id(self):
        """Gets the alarm_source_product_id of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_source_product_id of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_source_product_id

    @alarm_source_product_id.setter
    def alarm_source_product_id(self, alarm_source_product_id):
        """Sets the alarm_source_product_id of this AlarmSourceProductForGetApiV1AlarmDetailOutput.


        :param alarm_source_product_id: The alarm_source_product_id of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_source_product_id = alarm_source_product_id

    @property
    def alarm_source_product_name(self):
        """Gets the alarm_source_product_name of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The alarm_source_product_name of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_source_product_name

    @alarm_source_product_name.setter
    def alarm_source_product_name(self, alarm_source_product_name):
        """Sets the alarm_source_product_name of this AlarmSourceProductForGetApiV1AlarmDetailOutput.


        :param alarm_source_product_name: The alarm_source_product_name of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_source_product_name = alarm_source_product_name

    @property
    def resource_vendor(self):
        """Gets the resource_vendor of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The resource_vendor of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_vendor

    @resource_vendor.setter
    def resource_vendor(self, resource_vendor):
        """Sets the resource_vendor of this AlarmSourceProductForGetApiV1AlarmDetailOutput.


        :param resource_vendor: The resource_vendor of this AlarmSourceProductForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._resource_vendor = resource_vendor

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmSourceProductForGetApiV1AlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmSourceProductForGetApiV1AlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmSourceProductForGetApiV1AlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
