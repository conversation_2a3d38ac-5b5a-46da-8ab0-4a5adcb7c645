# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetIntrusionRealTimeUpdatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'alarm_id': 'str',
        'alarm_time': 'int',
        'alarm_type': 'str',
        'alert_detail': 'str',
        'cluster_id': 'str',
        'data_type': 'str',
        'harm_level': 'str',
        'name': 'str',
        'status': 'int',
        'trace_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'alarm_id': 'AlarmID',
        'alarm_time': 'AlarmTime',
        'alarm_type': 'AlarmType',
        'alert_detail': 'AlertDetail',
        'cluster_id': 'ClusterID',
        'data_type': 'DataType',
        'harm_level': 'HarmLevel',
        'name': 'Name',
        'status': 'Status',
        'trace_id': 'TraceID'
    }

    def __init__(self, agent_id=None, alarm_id=None, alarm_time=None, alarm_type=None, alert_detail=None, cluster_id=None, data_type=None, harm_level=None, name=None, status=None, trace_id=None, _configuration=None):  # noqa: E501
        """DataForGetIntrusionRealTimeUpdatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._alarm_id = None
        self._alarm_time = None
        self._alarm_type = None
        self._alert_detail = None
        self._cluster_id = None
        self._data_type = None
        self._harm_level = None
        self._name = None
        self._status = None
        self._trace_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alarm_time is not None:
            self.alarm_time = alarm_time
        if alarm_type is not None:
            self.alarm_type = alarm_type
        if alert_detail is not None:
            self.alert_detail = alert_detail
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if data_type is not None:
            self.data_type = data_type
        if harm_level is not None:
            self.harm_level = harm_level
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if trace_id is not None:
            self.trace_id = trace_id

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The agent_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param agent_id: The agent_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def alarm_id(self):
        """Gets the alarm_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The alarm_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param alarm_id: The alarm_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alarm_time(self):
        """Gets the alarm_time of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The alarm_time of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_time

    @alarm_time.setter
    def alarm_time(self, alarm_time):
        """Sets the alarm_time of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param alarm_time: The alarm_time of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: int
        """

        self._alarm_time = alarm_time

    @property
    def alarm_type(self):
        """Gets the alarm_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The alarm_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param alarm_type: The alarm_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def alert_detail(self):
        """Gets the alert_detail of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The alert_detail of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_detail

    @alert_detail.setter
    def alert_detail(self, alert_detail):
        """Sets the alert_detail of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param alert_detail: The alert_detail of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._alert_detail = alert_detail

    @property
    def cluster_id(self):
        """Gets the cluster_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The cluster_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param cluster_id: The cluster_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def data_type(self):
        """Gets the data_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The data_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param data_type: The data_type of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def harm_level(self):
        """Gets the harm_level of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The harm_level of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._harm_level

    @harm_level.setter
    def harm_level(self, harm_level):
        """Sets the harm_level of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param harm_level: The harm_level of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._harm_level = harm_level

    @property
    def name(self):
        """Gets the name of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The name of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param name: The name of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The status of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param status: The status of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def trace_id(self):
        """Gets the trace_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501


        :return: The trace_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this DataForGetIntrusionRealTimeUpdatesOutput.


        :param trace_id: The trace_id of this DataForGetIntrusionRealTimeUpdatesOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetIntrusionRealTimeUpdatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetIntrusionRealTimeUpdatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetIntrusionRealTimeUpdatesOutput):
            return True

        return self.to_dict() != other.to_dict()
