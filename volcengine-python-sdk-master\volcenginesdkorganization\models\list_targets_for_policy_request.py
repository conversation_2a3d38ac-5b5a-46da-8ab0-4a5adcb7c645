# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListTargetsForPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'policy_id': 'str',
        'sort_by': 'str',
        'sort_order': 'str'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'policy_id': 'PolicyID',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder'
    }

    def __init__(self, limit=None, offset=None, policy_id=None, sort_by=None, sort_order=None, _configuration=None):  # noqa: E501
        """ListTargetsForPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._policy_id = None
        self._sort_by = None
        self._sort_order = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        self.policy_id = policy_id
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order

    @property
    def limit(self):
        """Gets the limit of this ListTargetsForPolicyRequest.  # noqa: E501


        :return: The limit of this ListTargetsForPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListTargetsForPolicyRequest.


        :param limit: The limit of this ListTargetsForPolicyRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListTargetsForPolicyRequest.  # noqa: E501


        :return: The offset of this ListTargetsForPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListTargetsForPolicyRequest.


        :param offset: The offset of this ListTargetsForPolicyRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def policy_id(self):
        """Gets the policy_id of this ListTargetsForPolicyRequest.  # noqa: E501


        :return: The policy_id of this ListTargetsForPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._policy_id

    @policy_id.setter
    def policy_id(self, policy_id):
        """Sets the policy_id of this ListTargetsForPolicyRequest.


        :param policy_id: The policy_id of this ListTargetsForPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and policy_id is None:
            raise ValueError("Invalid value for `policy_id`, must not be `None`")  # noqa: E501

        self._policy_id = policy_id

    @property
    def sort_by(self):
        """Gets the sort_by of this ListTargetsForPolicyRequest.  # noqa: E501


        :return: The sort_by of this ListTargetsForPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListTargetsForPolicyRequest.


        :param sort_by: The sort_by of this ListTargetsForPolicyRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListTargetsForPolicyRequest.  # noqa: E501


        :return: The sort_order of this ListTargetsForPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListTargetsForPolicyRequest.


        :param sort_order: The sort_order of this ListTargetsForPolicyRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListTargetsForPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListTargetsForPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListTargetsForPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
