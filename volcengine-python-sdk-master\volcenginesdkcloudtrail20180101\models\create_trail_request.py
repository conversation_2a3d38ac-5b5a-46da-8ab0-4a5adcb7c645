# coding: utf-8

"""
    cloud_trail20180101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTrailRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'event_rw': 'str',
        'event_sources': 'list[str]',
        'tls_project_name': 'str',
        'tls_project_region': 'str',
        'tls_project_topic_project': 'str',
        'tls_topic_name': 'str',
        'tos_bucket_name': 'str',
        'tos_bucket_project': 'str',
        'tos_bucket_region': 'str',
        'tos_key_prefix': 'str',
        'trail_name': 'str',
        'trail_type': 'int'
    }

    attribute_map = {
        'event_rw': 'EventRW',
        'event_sources': 'EventSources',
        'tls_project_name': 'TlsProjectName',
        'tls_project_region': 'TlsProjectRegion',
        'tls_project_topic_project': 'TlsProjectTopicProject',
        'tls_topic_name': 'TlsTopicName',
        'tos_bucket_name': 'TosBucketName',
        'tos_bucket_project': 'TosBucketProject',
        'tos_bucket_region': 'TosBucketRegion',
        'tos_key_prefix': 'TosKeyPrefix',
        'trail_name': 'TrailName',
        'trail_type': 'TrailType'
    }

    def __init__(self, event_rw=None, event_sources=None, tls_project_name=None, tls_project_region=None, tls_project_topic_project=None, tls_topic_name=None, tos_bucket_name=None, tos_bucket_project=None, tos_bucket_region=None, tos_key_prefix=None, trail_name=None, trail_type=None, _configuration=None):  # noqa: E501
        """CreateTrailRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._event_rw = None
        self._event_sources = None
        self._tls_project_name = None
        self._tls_project_region = None
        self._tls_project_topic_project = None
        self._tls_topic_name = None
        self._tos_bucket_name = None
        self._tos_bucket_project = None
        self._tos_bucket_region = None
        self._tos_key_prefix = None
        self._trail_name = None
        self._trail_type = None
        self.discriminator = None

        if event_rw is not None:
            self.event_rw = event_rw
        if event_sources is not None:
            self.event_sources = event_sources
        if tls_project_name is not None:
            self.tls_project_name = tls_project_name
        if tls_project_region is not None:
            self.tls_project_region = tls_project_region
        if tls_project_topic_project is not None:
            self.tls_project_topic_project = tls_project_topic_project
        if tls_topic_name is not None:
            self.tls_topic_name = tls_topic_name
        if tos_bucket_name is not None:
            self.tos_bucket_name = tos_bucket_name
        if tos_bucket_project is not None:
            self.tos_bucket_project = tos_bucket_project
        if tos_bucket_region is not None:
            self.tos_bucket_region = tos_bucket_region
        if tos_key_prefix is not None:
            self.tos_key_prefix = tos_key_prefix
        if trail_name is not None:
            self.trail_name = trail_name
        if trail_type is not None:
            self.trail_type = trail_type

    @property
    def event_rw(self):
        """Gets the event_rw of this CreateTrailRequest.  # noqa: E501


        :return: The event_rw of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_rw

    @event_rw.setter
    def event_rw(self, event_rw):
        """Sets the event_rw of this CreateTrailRequest.


        :param event_rw: The event_rw of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._event_rw = event_rw

    @property
    def event_sources(self):
        """Gets the event_sources of this CreateTrailRequest.  # noqa: E501


        :return: The event_sources of this CreateTrailRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_sources

    @event_sources.setter
    def event_sources(self, event_sources):
        """Sets the event_sources of this CreateTrailRequest.


        :param event_sources: The event_sources of this CreateTrailRequest.  # noqa: E501
        :type: list[str]
        """

        self._event_sources = event_sources

    @property
    def tls_project_name(self):
        """Gets the tls_project_name of this CreateTrailRequest.  # noqa: E501


        :return: The tls_project_name of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tls_project_name

    @tls_project_name.setter
    def tls_project_name(self, tls_project_name):
        """Sets the tls_project_name of this CreateTrailRequest.


        :param tls_project_name: The tls_project_name of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tls_project_name = tls_project_name

    @property
    def tls_project_region(self):
        """Gets the tls_project_region of this CreateTrailRequest.  # noqa: E501


        :return: The tls_project_region of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tls_project_region

    @tls_project_region.setter
    def tls_project_region(self, tls_project_region):
        """Sets the tls_project_region of this CreateTrailRequest.


        :param tls_project_region: The tls_project_region of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tls_project_region = tls_project_region

    @property
    def tls_project_topic_project(self):
        """Gets the tls_project_topic_project of this CreateTrailRequest.  # noqa: E501


        :return: The tls_project_topic_project of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tls_project_topic_project

    @tls_project_topic_project.setter
    def tls_project_topic_project(self, tls_project_topic_project):
        """Sets the tls_project_topic_project of this CreateTrailRequest.


        :param tls_project_topic_project: The tls_project_topic_project of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tls_project_topic_project = tls_project_topic_project

    @property
    def tls_topic_name(self):
        """Gets the tls_topic_name of this CreateTrailRequest.  # noqa: E501


        :return: The tls_topic_name of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tls_topic_name

    @tls_topic_name.setter
    def tls_topic_name(self, tls_topic_name):
        """Sets the tls_topic_name of this CreateTrailRequest.


        :param tls_topic_name: The tls_topic_name of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tls_topic_name = tls_topic_name

    @property
    def tos_bucket_name(self):
        """Gets the tos_bucket_name of this CreateTrailRequest.  # noqa: E501


        :return: The tos_bucket_name of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tos_bucket_name

    @tos_bucket_name.setter
    def tos_bucket_name(self, tos_bucket_name):
        """Sets the tos_bucket_name of this CreateTrailRequest.


        :param tos_bucket_name: The tos_bucket_name of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tos_bucket_name = tos_bucket_name

    @property
    def tos_bucket_project(self):
        """Gets the tos_bucket_project of this CreateTrailRequest.  # noqa: E501


        :return: The tos_bucket_project of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tos_bucket_project

    @tos_bucket_project.setter
    def tos_bucket_project(self, tos_bucket_project):
        """Sets the tos_bucket_project of this CreateTrailRequest.


        :param tos_bucket_project: The tos_bucket_project of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tos_bucket_project = tos_bucket_project

    @property
    def tos_bucket_region(self):
        """Gets the tos_bucket_region of this CreateTrailRequest.  # noqa: E501


        :return: The tos_bucket_region of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tos_bucket_region

    @tos_bucket_region.setter
    def tos_bucket_region(self, tos_bucket_region):
        """Sets the tos_bucket_region of this CreateTrailRequest.


        :param tos_bucket_region: The tos_bucket_region of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tos_bucket_region = tos_bucket_region

    @property
    def tos_key_prefix(self):
        """Gets the tos_key_prefix of this CreateTrailRequest.  # noqa: E501


        :return: The tos_key_prefix of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._tos_key_prefix

    @tos_key_prefix.setter
    def tos_key_prefix(self, tos_key_prefix):
        """Sets the tos_key_prefix of this CreateTrailRequest.


        :param tos_key_prefix: The tos_key_prefix of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._tos_key_prefix = tos_key_prefix

    @property
    def trail_name(self):
        """Gets the trail_name of this CreateTrailRequest.  # noqa: E501


        :return: The trail_name of this CreateTrailRequest.  # noqa: E501
        :rtype: str
        """
        return self._trail_name

    @trail_name.setter
    def trail_name(self, trail_name):
        """Sets the trail_name of this CreateTrailRequest.


        :param trail_name: The trail_name of this CreateTrailRequest.  # noqa: E501
        :type: str
        """

        self._trail_name = trail_name

    @property
    def trail_type(self):
        """Gets the trail_type of this CreateTrailRequest.  # noqa: E501


        :return: The trail_type of this CreateTrailRequest.  # noqa: E501
        :rtype: int
        """
        return self._trail_type

    @trail_type.setter
    def trail_type(self, trail_type):
        """Sets the trail_type of this CreateTrailRequest.


        :param trail_type: The trail_type of this CreateTrailRequest.  # noqa: E501
        :type: int
        """

        self._trail_type = trail_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTrailRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTrailRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTrailRequest):
            return True

        return self.to_dict() != other.to_dict()
