# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MainForGetAllStreamPullInfoAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'size': 'str',
        'url': 'list[UrlForGetAllStreamPullInfoAPIOutput]',
        'v_codec': 'str'
    }

    attribute_map = {
        'size': 'Size',
        'url': 'Url',
        'v_codec': 'VCodec'
    }

    def __init__(self, size=None, url=None, v_codec=None, _configuration=None):  # noqa: E501
        """MainForGetAllStreamPullInfoAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._size = None
        self._url = None
        self._v_codec = None
        self.discriminator = None

        if size is not None:
            self.size = size
        if url is not None:
            self.url = url
        if v_codec is not None:
            self.v_codec = v_codec

    @property
    def size(self):
        """Gets the size of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The size of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this MainForGetAllStreamPullInfoAPIOutput.


        :param size: The size of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._size = size

    @property
    def url(self):
        """Gets the url of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The url of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: list[UrlForGetAllStreamPullInfoAPIOutput]
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this MainForGetAllStreamPullInfoAPIOutput.


        :param url: The url of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: list[UrlForGetAllStreamPullInfoAPIOutput]
        """

        self._url = url

    @property
    def v_codec(self):
        """Gets the v_codec of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The v_codec of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._v_codec

    @v_codec.setter
    def v_codec(self, v_codec):
        """Sets the v_codec of this MainForGetAllStreamPullInfoAPIOutput.


        :param v_codec: The v_codec of this MainForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._v_codec = v_codec

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MainForGetAllStreamPullInfoAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MainForGetAllStreamPullInfoAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MainForGetAllStreamPullInfoAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
