# coding: utf-8

"""
    dataleap

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfForDTSOpenDescribeResourceGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bucket_name': 'str',
        'security_group_id_list': 'list[str]',
        'subnet_id_list': 'list[str]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'bucket_name': 'BucketName',
        'security_group_id_list': 'SecurityGroupIdList',
        'subnet_id_list': 'SubnetIdList',
        'vpc_id': 'VpcId'
    }

    def __init__(self, bucket_name=None, security_group_id_list=None, subnet_id_list=None, vpc_id=None, _configuration=None):  # noqa: E501
        """ConfForDTSOpenDescribeResourceGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bucket_name = None
        self._security_group_id_list = None
        self._subnet_id_list = None
        self._vpc_id = None
        self.discriminator = None

        if bucket_name is not None:
            self.bucket_name = bucket_name
        if security_group_id_list is not None:
            self.security_group_id_list = security_group_id_list
        if subnet_id_list is not None:
            self.subnet_id_list = subnet_id_list
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def bucket_name(self):
        """Gets the bucket_name of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The bucket_name of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_name

    @bucket_name.setter
    def bucket_name(self, bucket_name):
        """Sets the bucket_name of this ConfForDTSOpenDescribeResourceGroupsOutput.


        :param bucket_name: The bucket_name of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._bucket_name = bucket_name

    @property
    def security_group_id_list(self):
        """Gets the security_group_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The security_group_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_id_list

    @security_group_id_list.setter
    def security_group_id_list(self, security_group_id_list):
        """Sets the security_group_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.


        :param security_group_id_list: The security_group_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_id_list = security_group_id_list

    @property
    def subnet_id_list(self):
        """Gets the subnet_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The subnet_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_id_list

    @subnet_id_list.setter
    def subnet_id_list(self, subnet_id_list):
        """Sets the subnet_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.


        :param subnet_id_list: The subnet_id_list of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_id_list = subnet_id_list

    @property
    def vpc_id(self):
        """Gets the vpc_id of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The vpc_id of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this ConfForDTSOpenDescribeResourceGroupsOutput.


        :param vpc_id: The vpc_id of this ConfForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfForDTSOpenDescribeResourceGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfForDTSOpenDescribeResourceGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfForDTSOpenDescribeResourceGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
