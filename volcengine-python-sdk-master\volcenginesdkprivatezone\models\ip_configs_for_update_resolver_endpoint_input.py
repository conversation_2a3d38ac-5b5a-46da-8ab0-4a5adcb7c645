# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IpConfigsForUpdateResolverEndpointInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'az_id': 'str',
        'ip': 'str',
        'ipv6': 'str',
        'subnet_id': 'str'
    }

    attribute_map = {
        'az_id': 'AzID',
        'ip': 'IP',
        'ipv6': 'IPv6',
        'subnet_id': 'SubnetID'
    }

    def __init__(self, az_id=None, ip=None, ipv6=None, subnet_id=None, _configuration=None):  # noqa: E501
        """IpConfigsForUpdateResolverEndpointInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._az_id = None
        self._ip = None
        self._ipv6 = None
        self._subnet_id = None
        self.discriminator = None

        if az_id is not None:
            self.az_id = az_id
        if ip is not None:
            self.ip = ip
        if ipv6 is not None:
            self.ipv6 = ipv6
        if subnet_id is not None:
            self.subnet_id = subnet_id

    @property
    def az_id(self):
        """Gets the az_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501


        :return: The az_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :rtype: str
        """
        return self._az_id

    @az_id.setter
    def az_id(self, az_id):
        """Sets the az_id of this IpConfigsForUpdateResolverEndpointInput.


        :param az_id: The az_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :type: str
        """

        self._az_id = az_id

    @property
    def ip(self):
        """Gets the ip of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501


        :return: The ip of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this IpConfigsForUpdateResolverEndpointInput.


        :param ip: The ip of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def ipv6(self):
        """Gets the ipv6 of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501


        :return: The ipv6 of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this IpConfigsForUpdateResolverEndpointInput.


        :param ipv6: The ipv6 of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :type: str
        """

        self._ipv6 = ipv6

    @property
    def subnet_id(self):
        """Gets the subnet_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501


        :return: The subnet_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this IpConfigsForUpdateResolverEndpointInput.


        :param subnet_id: The subnet_id of this IpConfigsForUpdateResolverEndpointInput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IpConfigsForUpdateResolverEndpointInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpConfigsForUpdateResolverEndpointInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpConfigsForUpdateResolverEndpointInput):
            return True

        return self.to_dict() != other.to_dict()
