# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeResourcePoolResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_type': 'str',
        'create_time': 'str',
        'name': 'str',
        'project_id': 'str',
        'region_id': 'str',
        'resource_pool_trn': 'str',
        'status': 'str',
        'status_message': 'str',
        'update_time': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'billing_type': 'BillingType',
        'create_time': 'CreateTime',
        'name': 'Name',
        'project_id': 'ProjectId',
        'region_id': 'RegionId',
        'resource_pool_trn': 'ResourcePoolTrn',
        'status': 'Status',
        'status_message': 'StatusMessage',
        'update_time': 'UpdateTime',
        'zone_id': 'ZoneId'
    }

    def __init__(self, billing_type=None, create_time=None, name=None, project_id=None, region_id=None, resource_pool_trn=None, status=None, status_message=None, update_time=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeResourcePoolResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_type = None
        self._create_time = None
        self._name = None
        self._project_id = None
        self._region_id = None
        self._resource_pool_trn = None
        self._status = None
        self._status_message = None
        self._update_time = None
        self._zone_id = None
        self.discriminator = None

        if billing_type is not None:
            self.billing_type = billing_type
        if create_time is not None:
            self.create_time = create_time
        if name is not None:
            self.name = name
        if project_id is not None:
            self.project_id = project_id
        if region_id is not None:
            self.region_id = region_id
        if resource_pool_trn is not None:
            self.resource_pool_trn = resource_pool_trn
        if status is not None:
            self.status = status
        if status_message is not None:
            self.status_message = status_message
        if update_time is not None:
            self.update_time = update_time
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The billing_type of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeResourcePoolResponse.


        :param billing_type: The billing_type of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._billing_type = billing_type

    @property
    def create_time(self):
        """Gets the create_time of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The create_time of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeResourcePoolResponse.


        :param create_time: The create_time of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def name(self):
        """Gets the name of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The name of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeResourcePoolResponse.


        :param name: The name of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_id(self):
        """Gets the project_id of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The project_id of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this DescribeResourcePoolResponse.


        :param project_id: The project_id of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def region_id(self):
        """Gets the region_id of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The region_id of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this DescribeResourcePoolResponse.


        :param region_id: The region_id of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def resource_pool_trn(self):
        """Gets the resource_pool_trn of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The resource_pool_trn of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_pool_trn

    @resource_pool_trn.setter
    def resource_pool_trn(self, resource_pool_trn):
        """Sets the resource_pool_trn of this DescribeResourcePoolResponse.


        :param resource_pool_trn: The resource_pool_trn of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._resource_pool_trn = resource_pool_trn

    @property
    def status(self):
        """Gets the status of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The status of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeResourcePoolResponse.


        :param status: The status of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def status_message(self):
        """Gets the status_message of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The status_message of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._status_message

    @status_message.setter
    def status_message(self, status_message):
        """Sets the status_message of this DescribeResourcePoolResponse.


        :param status_message: The status_message of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._status_message = status_message

    @property
    def update_time(self):
        """Gets the update_time of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The update_time of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeResourcePoolResponse.


        :param update_time: The update_time of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeResourcePoolResponse.  # noqa: E501


        :return: The zone_id of this DescribeResourcePoolResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeResourcePoolResponse.


        :param zone_id: The zone_id of this DescribeResourcePoolResponse.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeResourcePoolResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeResourcePoolResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeResourcePoolResponse):
            return True

        return self.to_dict() != other.to_dict()
