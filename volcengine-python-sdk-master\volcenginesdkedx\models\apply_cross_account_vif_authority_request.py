# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplyCrossAccountVIFAuthorityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'vgw_account_id': 'int',
        'vif_instance_id': 'str'
    }

    attribute_map = {
        'vgw_account_id': 'VGWAccountId',
        'vif_instance_id': 'VIFInstanceId'
    }

    def __init__(self, vgw_account_id=None, vif_instance_id=None, _configuration=None):  # noqa: E501
        """ApplyCrossAccountVIFAuthorityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._vgw_account_id = None
        self._vif_instance_id = None
        self.discriminator = None

        self.vgw_account_id = vgw_account_id
        self.vif_instance_id = vif_instance_id

    @property
    def vgw_account_id(self):
        """Gets the vgw_account_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The vgw_account_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: int
        """
        return self._vgw_account_id

    @vgw_account_id.setter
    def vgw_account_id(self, vgw_account_id):
        """Sets the vgw_account_id of this ApplyCrossAccountVIFAuthorityRequest.


        :param vgw_account_id: The vgw_account_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and vgw_account_id is None:
            raise ValueError("Invalid value for `vgw_account_id`, must not be `None`")  # noqa: E501

        self._vgw_account_id = vgw_account_id

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501


        :return: The vif_instance_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this ApplyCrossAccountVIFAuthorityRequest.


        :param vif_instance_id: The vif_instance_id of this ApplyCrossAccountVIFAuthorityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vif_instance_id is None:
            raise ValueError("Invalid value for `vif_instance_id`, must not be `None`")  # noqa: E501

        self._vif_instance_id = vif_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplyCrossAccountVIFAuthorityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplyCrossAccountVIFAuthorityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplyCrossAccountVIFAuthorityRequest):
            return True

        return self.to_dict() != other.to_dict()
