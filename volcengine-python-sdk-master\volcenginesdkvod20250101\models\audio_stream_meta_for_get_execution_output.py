# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AudioStreamMetaForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bitrate': 'int',
        'codec': 'str',
        'duration': 'float',
        'quality': 'str',
        'sample_rate': 'int'
    }

    attribute_map = {
        'bitrate': 'Bitrate',
        'codec': 'Codec',
        'duration': 'Duration',
        'quality': 'Quality',
        'sample_rate': 'SampleRate'
    }

    def __init__(self, bitrate=None, codec=None, duration=None, quality=None, sample_rate=None, _configuration=None):  # noqa: E501
        """AudioStreamMetaForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bitrate = None
        self._codec = None
        self._duration = None
        self._quality = None
        self._sample_rate = None
        self.discriminator = None

        if bitrate is not None:
            self.bitrate = bitrate
        if codec is not None:
            self.codec = codec
        if duration is not None:
            self.duration = duration
        if quality is not None:
            self.quality = quality
        if sample_rate is not None:
            self.sample_rate = sample_rate

    @property
    def bitrate(self):
        """Gets the bitrate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The bitrate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._bitrate

    @bitrate.setter
    def bitrate(self, bitrate):
        """Sets the bitrate of this AudioStreamMetaForGetExecutionOutput.


        :param bitrate: The bitrate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._bitrate = bitrate

    @property
    def codec(self):
        """Gets the codec of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The codec of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._codec

    @codec.setter
    def codec(self, codec):
        """Sets the codec of this AudioStreamMetaForGetExecutionOutput.


        :param codec: The codec of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._codec = codec

    @property
    def duration(self):
        """Gets the duration of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The duration of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this AudioStreamMetaForGetExecutionOutput.


        :param duration: The duration of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def quality(self):
        """Gets the quality of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The quality of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._quality

    @quality.setter
    def quality(self, quality):
        """Sets the quality of this AudioStreamMetaForGetExecutionOutput.


        :param quality: The quality of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._quality = quality

    @property
    def sample_rate(self):
        """Gets the sample_rate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501


        :return: The sample_rate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :rtype: int
        """
        return self._sample_rate

    @sample_rate.setter
    def sample_rate(self, sample_rate):
        """Sets the sample_rate of this AudioStreamMetaForGetExecutionOutput.


        :param sample_rate: The sample_rate of this AudioStreamMetaForGetExecutionOutput.  # noqa: E501
        :type: int
        """

        self._sample_rate = sample_rate

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AudioStreamMetaForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AudioStreamMetaForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AudioStreamMetaForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
