# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApplicationConfigForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'component_instance_name': 'str',
        'component_name': 'str',
        'config_file_name': 'str',
        'config_item_key': 'str',
        'config_item_value': 'str',
        'deleted': 'bool',
        'effective_scope': 'EffectiveScopeForCreateClusterInput'
    }

    attribute_map = {
        'component_instance_name': 'ComponentInstanceName',
        'component_name': 'ComponentName',
        'config_file_name': 'ConfigFileName',
        'config_item_key': 'ConfigItemKey',
        'config_item_value': 'ConfigItemValue',
        'deleted': 'Deleted',
        'effective_scope': 'EffectiveScope'
    }

    def __init__(self, component_instance_name=None, component_name=None, config_file_name=None, config_item_key=None, config_item_value=None, deleted=None, effective_scope=None, _configuration=None):  # noqa: E501
        """ApplicationConfigForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._component_instance_name = None
        self._component_name = None
        self._config_file_name = None
        self._config_item_key = None
        self._config_item_value = None
        self._deleted = None
        self._effective_scope = None
        self.discriminator = None

        if component_instance_name is not None:
            self.component_instance_name = component_instance_name
        if component_name is not None:
            self.component_name = component_name
        if config_file_name is not None:
            self.config_file_name = config_file_name
        if config_item_key is not None:
            self.config_item_key = config_item_key
        if config_item_value is not None:
            self.config_item_value = config_item_value
        if deleted is not None:
            self.deleted = deleted
        if effective_scope is not None:
            self.effective_scope = effective_scope

    @property
    def component_instance_name(self):
        """Gets the component_instance_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The component_instance_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._component_instance_name

    @component_instance_name.setter
    def component_instance_name(self, component_instance_name):
        """Sets the component_instance_name of this ApplicationConfigForCreateClusterInput.


        :param component_instance_name: The component_instance_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._component_instance_name = component_instance_name

    @property
    def component_name(self):
        """Gets the component_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The component_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._component_name

    @component_name.setter
    def component_name(self, component_name):
        """Sets the component_name of this ApplicationConfigForCreateClusterInput.


        :param component_name: The component_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._component_name = component_name

    @property
    def config_file_name(self):
        """Gets the config_file_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The config_file_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._config_file_name

    @config_file_name.setter
    def config_file_name(self, config_file_name):
        """Sets the config_file_name of this ApplicationConfigForCreateClusterInput.


        :param config_file_name: The config_file_name of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._config_file_name = config_file_name

    @property
    def config_item_key(self):
        """Gets the config_item_key of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The config_item_key of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._config_item_key

    @config_item_key.setter
    def config_item_key(self, config_item_key):
        """Sets the config_item_key of this ApplicationConfigForCreateClusterInput.


        :param config_item_key: The config_item_key of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._config_item_key = config_item_key

    @property
    def config_item_value(self):
        """Gets the config_item_value of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The config_item_value of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._config_item_value

    @config_item_value.setter
    def config_item_value(self, config_item_value):
        """Sets the config_item_value of this ApplicationConfigForCreateClusterInput.


        :param config_item_value: The config_item_value of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._config_item_value = config_item_value

    @property
    def deleted(self):
        """Gets the deleted of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The deleted of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: bool
        """
        return self._deleted

    @deleted.setter
    def deleted(self, deleted):
        """Sets the deleted of this ApplicationConfigForCreateClusterInput.


        :param deleted: The deleted of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: bool
        """

        self._deleted = deleted

    @property
    def effective_scope(self):
        """Gets the effective_scope of this ApplicationConfigForCreateClusterInput.  # noqa: E501


        :return: The effective_scope of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :rtype: EffectiveScopeForCreateClusterInput
        """
        return self._effective_scope

    @effective_scope.setter
    def effective_scope(self, effective_scope):
        """Sets the effective_scope of this ApplicationConfigForCreateClusterInput.


        :param effective_scope: The effective_scope of this ApplicationConfigForCreateClusterInput.  # noqa: E501
        :type: EffectiveScopeForCreateClusterInput
        """

        self._effective_scope = effective_scope

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApplicationConfigForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApplicationConfigForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApplicationConfigForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
