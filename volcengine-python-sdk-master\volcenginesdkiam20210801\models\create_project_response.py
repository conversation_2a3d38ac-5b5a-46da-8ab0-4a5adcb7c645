# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateProjectResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'create_date': 'str',
        'description': 'str',
        'display_name': 'str',
        'parent_project_name': 'str',
        'path': 'str',
        'project_name': 'str',
        'status': 'str',
        'update_date': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'create_date': 'CreateDate',
        'description': 'Description',
        'display_name': 'DisplayName',
        'parent_project_name': 'ParentProjectName',
        'path': 'Path',
        'project_name': 'ProjectName',
        'status': 'Status',
        'update_date': 'UpdateDate'
    }

    def __init__(self, account_id=None, create_date=None, description=None, display_name=None, parent_project_name=None, path=None, project_name=None, status=None, update_date=None, _configuration=None):  # noqa: E501
        """CreateProjectResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._create_date = None
        self._description = None
        self._display_name = None
        self._parent_project_name = None
        self._path = None
        self._project_name = None
        self._status = None
        self._update_date = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if create_date is not None:
            self.create_date = create_date
        if description is not None:
            self.description = description
        if display_name is not None:
            self.display_name = display_name
        if parent_project_name is not None:
            self.parent_project_name = parent_project_name
        if path is not None:
            self.path = path
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if update_date is not None:
            self.update_date = update_date

    @property
    def account_id(self):
        """Gets the account_id of this CreateProjectResponse.  # noqa: E501


        :return: The account_id of this CreateProjectResponse.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this CreateProjectResponse.


        :param account_id: The account_id of this CreateProjectResponse.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def create_date(self):
        """Gets the create_date of this CreateProjectResponse.  # noqa: E501


        :return: The create_date of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_date

    @create_date.setter
    def create_date(self, create_date):
        """Sets the create_date of this CreateProjectResponse.


        :param create_date: The create_date of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._create_date = create_date

    @property
    def description(self):
        """Gets the description of this CreateProjectResponse.  # noqa: E501


        :return: The description of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateProjectResponse.


        :param description: The description of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def display_name(self):
        """Gets the display_name of this CreateProjectResponse.  # noqa: E501


        :return: The display_name of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this CreateProjectResponse.


        :param display_name: The display_name of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def parent_project_name(self):
        """Gets the parent_project_name of this CreateProjectResponse.  # noqa: E501


        :return: The parent_project_name of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._parent_project_name

    @parent_project_name.setter
    def parent_project_name(self, parent_project_name):
        """Sets the parent_project_name of this CreateProjectResponse.


        :param parent_project_name: The parent_project_name of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._parent_project_name = parent_project_name

    @property
    def path(self):
        """Gets the path of this CreateProjectResponse.  # noqa: E501


        :return: The path of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this CreateProjectResponse.


        :param path: The path of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def project_name(self):
        """Gets the project_name of this CreateProjectResponse.  # noqa: E501


        :return: The project_name of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateProjectResponse.


        :param project_name: The project_name of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this CreateProjectResponse.  # noqa: E501


        :return: The status of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CreateProjectResponse.


        :param status: The status of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_date(self):
        """Gets the update_date of this CreateProjectResponse.  # noqa: E501


        :return: The update_date of this CreateProjectResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this CreateProjectResponse.


        :param update_date: The update_date of this CreateProjectResponse.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateProjectResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateProjectResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateProjectResponse):
            return True

        return self.to_dict() != other.to_dict()
