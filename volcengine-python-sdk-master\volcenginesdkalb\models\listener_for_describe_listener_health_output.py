# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListenerForDescribeListenerHealthOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'listener_id': 'str',
        'results': 'list[ResultForDescribeListenerHealthOutput]',
        'status': 'str',
        'total_backend_server_count': 'int',
        'un_healthy_count': 'int'
    }

    attribute_map = {
        'listener_id': 'ListenerId',
        'results': 'Results',
        'status': 'Status',
        'total_backend_server_count': 'TotalBackendServerCount',
        'un_healthy_count': 'UnHealthyCount'
    }

    def __init__(self, listener_id=None, results=None, status=None, total_backend_server_count=None, un_healthy_count=None, _configuration=None):  # noqa: E501
        """ListenerForDescribeListenerHealthOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._listener_id = None
        self._results = None
        self._status = None
        self._total_backend_server_count = None
        self._un_healthy_count = None
        self.discriminator = None

        if listener_id is not None:
            self.listener_id = listener_id
        if results is not None:
            self.results = results
        if status is not None:
            self.status = status
        if total_backend_server_count is not None:
            self.total_backend_server_count = total_backend_server_count
        if un_healthy_count is not None:
            self.un_healthy_count = un_healthy_count

    @property
    def listener_id(self):
        """Gets the listener_id of this ListenerForDescribeListenerHealthOutput.  # noqa: E501


        :return: The listener_id of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this ListenerForDescribeListenerHealthOutput.


        :param listener_id: The listener_id of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    @property
    def results(self):
        """Gets the results of this ListenerForDescribeListenerHealthOutput.  # noqa: E501


        :return: The results of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: list[ResultForDescribeListenerHealthOutput]
        """
        return self._results

    @results.setter
    def results(self, results):
        """Sets the results of this ListenerForDescribeListenerHealthOutput.


        :param results: The results of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :type: list[ResultForDescribeListenerHealthOutput]
        """

        self._results = results

    @property
    def status(self):
        """Gets the status of this ListenerForDescribeListenerHealthOutput.  # noqa: E501


        :return: The status of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListenerForDescribeListenerHealthOutput.


        :param status: The status of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def total_backend_server_count(self):
        """Gets the total_backend_server_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501


        :return: The total_backend_server_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_backend_server_count

    @total_backend_server_count.setter
    def total_backend_server_count(self, total_backend_server_count):
        """Sets the total_backend_server_count of this ListenerForDescribeListenerHealthOutput.


        :param total_backend_server_count: The total_backend_server_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :type: int
        """

        self._total_backend_server_count = total_backend_server_count

    @property
    def un_healthy_count(self):
        """Gets the un_healthy_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501


        :return: The un_healthy_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: int
        """
        return self._un_healthy_count

    @un_healthy_count.setter
    def un_healthy_count(self, un_healthy_count):
        """Sets the un_healthy_count of this ListenerForDescribeListenerHealthOutput.


        :param un_healthy_count: The un_healthy_count of this ListenerForDescribeListenerHealthOutput.  # noqa: E501
        :type: int
        """

        self._un_healthy_count = un_healthy_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListenerForDescribeListenerHealthOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListenerForDescribeListenerHealthOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListenerForDescribeListenerHealthOutput):
            return True

        return self.to_dict() != other.to_dict()
