# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigForGetDeploymentOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cfs': 'CfsForGetDeploymentOutput',
        'nas': 'NasForGetDeploymentOutput',
        'sfcs': 'SfcsForGetDeploymentOutput',
        'tos': 'TosForGetDeploymentOutput',
        'vepfs': 'VepfsForGetDeploymentOutput'
    }

    attribute_map = {
        'cfs': 'Cfs',
        'nas': 'Nas',
        'sfcs': 'Sfcs',
        'tos': 'Tos',
        'vepfs': 'Vepfs'
    }

    def __init__(self, cfs=None, nas=None, sfcs=None, tos=None, vepfs=None, _configuration=None):  # noqa: E501
        """ConfigForGetDeploymentOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cfs = None
        self._nas = None
        self._sfcs = None
        self._tos = None
        self._vepfs = None
        self.discriminator = None

        if cfs is not None:
            self.cfs = cfs
        if nas is not None:
            self.nas = nas
        if sfcs is not None:
            self.sfcs = sfcs
        if tos is not None:
            self.tos = tos
        if vepfs is not None:
            self.vepfs = vepfs

    @property
    def cfs(self):
        """Gets the cfs of this ConfigForGetDeploymentOutput.  # noqa: E501


        :return: The cfs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :rtype: CfsForGetDeploymentOutput
        """
        return self._cfs

    @cfs.setter
    def cfs(self, cfs):
        """Sets the cfs of this ConfigForGetDeploymentOutput.


        :param cfs: The cfs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :type: CfsForGetDeploymentOutput
        """

        self._cfs = cfs

    @property
    def nas(self):
        """Gets the nas of this ConfigForGetDeploymentOutput.  # noqa: E501


        :return: The nas of this ConfigForGetDeploymentOutput.  # noqa: E501
        :rtype: NasForGetDeploymentOutput
        """
        return self._nas

    @nas.setter
    def nas(self, nas):
        """Sets the nas of this ConfigForGetDeploymentOutput.


        :param nas: The nas of this ConfigForGetDeploymentOutput.  # noqa: E501
        :type: NasForGetDeploymentOutput
        """

        self._nas = nas

    @property
    def sfcs(self):
        """Gets the sfcs of this ConfigForGetDeploymentOutput.  # noqa: E501


        :return: The sfcs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :rtype: SfcsForGetDeploymentOutput
        """
        return self._sfcs

    @sfcs.setter
    def sfcs(self, sfcs):
        """Sets the sfcs of this ConfigForGetDeploymentOutput.


        :param sfcs: The sfcs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :type: SfcsForGetDeploymentOutput
        """

        self._sfcs = sfcs

    @property
    def tos(self):
        """Gets the tos of this ConfigForGetDeploymentOutput.  # noqa: E501


        :return: The tos of this ConfigForGetDeploymentOutput.  # noqa: E501
        :rtype: TosForGetDeploymentOutput
        """
        return self._tos

    @tos.setter
    def tos(self, tos):
        """Sets the tos of this ConfigForGetDeploymentOutput.


        :param tos: The tos of this ConfigForGetDeploymentOutput.  # noqa: E501
        :type: TosForGetDeploymentOutput
        """

        self._tos = tos

    @property
    def vepfs(self):
        """Gets the vepfs of this ConfigForGetDeploymentOutput.  # noqa: E501


        :return: The vepfs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :rtype: VepfsForGetDeploymentOutput
        """
        return self._vepfs

    @vepfs.setter
    def vepfs(self, vepfs):
        """Sets the vepfs of this ConfigForGetDeploymentOutput.


        :param vepfs: The vepfs of this ConfigForGetDeploymentOutput.  # noqa: E501
        :type: VepfsForGetDeploymentOutput
        """

        self._vepfs = vepfs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigForGetDeploymentOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigForGetDeploymentOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigForGetDeploymentOutput):
            return True

        return self.to_dict() != other.to_dict()
