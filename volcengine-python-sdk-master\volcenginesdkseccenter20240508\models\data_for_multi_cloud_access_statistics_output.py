# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForMultiCloudAccessStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'can_install_agent': 'bool',
        'can_sync': 'bool',
        'cloud_platform': 'str',
        'count': 'int',
        'is_active': 'bool',
        'last_sync_time': 'int',
        'name': 'str'
    }

    attribute_map = {
        'can_install_agent': 'CanInstallAgent',
        'can_sync': 'CanSync',
        'cloud_platform': 'CloudPlatform',
        'count': 'Count',
        'is_active': 'IsActive',
        'last_sync_time': 'LastSyncTime',
        'name': 'Name'
    }

    def __init__(self, can_install_agent=None, can_sync=None, cloud_platform=None, count=None, is_active=None, last_sync_time=None, name=None, _configuration=None):  # noqa: E501
        """DataForMultiCloudAccessStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._can_install_agent = None
        self._can_sync = None
        self._cloud_platform = None
        self._count = None
        self._is_active = None
        self._last_sync_time = None
        self._name = None
        self.discriminator = None

        if can_install_agent is not None:
            self.can_install_agent = can_install_agent
        if can_sync is not None:
            self.can_sync = can_sync
        if cloud_platform is not None:
            self.cloud_platform = cloud_platform
        if count is not None:
            self.count = count
        if is_active is not None:
            self.is_active = is_active
        if last_sync_time is not None:
            self.last_sync_time = last_sync_time
        if name is not None:
            self.name = name

    @property
    def can_install_agent(self):
        """Gets the can_install_agent of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The can_install_agent of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._can_install_agent

    @can_install_agent.setter
    def can_install_agent(self, can_install_agent):
        """Sets the can_install_agent of this DataForMultiCloudAccessStatisticsOutput.


        :param can_install_agent: The can_install_agent of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: bool
        """

        self._can_install_agent = can_install_agent

    @property
    def can_sync(self):
        """Gets the can_sync of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The can_sync of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._can_sync

    @can_sync.setter
    def can_sync(self, can_sync):
        """Sets the can_sync of this DataForMultiCloudAccessStatisticsOutput.


        :param can_sync: The can_sync of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: bool
        """

        self._can_sync = can_sync

    @property
    def cloud_platform(self):
        """Gets the cloud_platform of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The cloud_platform of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_platform

    @cloud_platform.setter
    def cloud_platform(self, cloud_platform):
        """Sets the cloud_platform of this DataForMultiCloudAccessStatisticsOutput.


        :param cloud_platform: The cloud_platform of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._cloud_platform = cloud_platform

    @property
    def count(self):
        """Gets the count of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The count of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._count

    @count.setter
    def count(self, count):
        """Sets the count of this DataForMultiCloudAccessStatisticsOutput.


        :param count: The count of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._count = count

    @property
    def is_active(self):
        """Gets the is_active of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The is_active of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_active

    @is_active.setter
    def is_active(self, is_active):
        """Sets the is_active of this DataForMultiCloudAccessStatisticsOutput.


        :param is_active: The is_active of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: bool
        """

        self._is_active = is_active

    @property
    def last_sync_time(self):
        """Gets the last_sync_time of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The last_sync_time of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_sync_time

    @last_sync_time.setter
    def last_sync_time(self, last_sync_time):
        """Sets the last_sync_time of this DataForMultiCloudAccessStatisticsOutput.


        :param last_sync_time: The last_sync_time of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._last_sync_time = last_sync_time

    @property
    def name(self):
        """Gets the name of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501


        :return: The name of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForMultiCloudAccessStatisticsOutput.


        :param name: The name of this DataForMultiCloudAccessStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForMultiCloudAccessStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForMultiCloudAccessStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForMultiCloudAccessStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
