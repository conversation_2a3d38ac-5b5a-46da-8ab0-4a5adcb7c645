# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HTTPDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_cost': 'int',
        'diagnose_detail': 'DiagnoseDetailForGetTaskResultOutput',
        'http_code': 'int',
        'http_request_header': 'str',
        'http_response_body': 'str',
        'http_response_header': 'str',
        'http_version': 'str',
        'location_detail': 'list[LocationDetailForGetTaskResultOutput]',
        'recv_cost': 'int',
        'redirect_cost': 'int',
        'redirect_nums': 'int',
        'send_cost': 'int',
        'ssl_cost': 'int',
        'tcp_cost': 'int',
        'total_cost': 'int',
        'ttfb_cost': 'int'
    }

    attribute_map = {
        'dns_cost': 'DNSCost',
        'diagnose_detail': 'DiagnoseDetail',
        'http_code': 'HTTPCode',
        'http_request_header': 'HTTPRequestHeader',
        'http_response_body': 'HTTPResponseBody',
        'http_response_header': 'HTTPResponseHeader',
        'http_version': 'HTTPVersion',
        'location_detail': 'LocationDetail',
        'recv_cost': 'RecvCost',
        'redirect_cost': 'RedirectCost',
        'redirect_nums': 'RedirectNums',
        'send_cost': 'SendCost',
        'ssl_cost': 'SslCost',
        'tcp_cost': 'TCPCost',
        'total_cost': 'TotalCost',
        'ttfb_cost': 'TtfbCost'
    }

    def __init__(self, dns_cost=None, diagnose_detail=None, http_code=None, http_request_header=None, http_response_body=None, http_response_header=None, http_version=None, location_detail=None, recv_cost=None, redirect_cost=None, redirect_nums=None, send_cost=None, ssl_cost=None, tcp_cost=None, total_cost=None, ttfb_cost=None, _configuration=None):  # noqa: E501
        """HTTPDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_cost = None
        self._diagnose_detail = None
        self._http_code = None
        self._http_request_header = None
        self._http_response_body = None
        self._http_response_header = None
        self._http_version = None
        self._location_detail = None
        self._recv_cost = None
        self._redirect_cost = None
        self._redirect_nums = None
        self._send_cost = None
        self._ssl_cost = None
        self._tcp_cost = None
        self._total_cost = None
        self._ttfb_cost = None
        self.discriminator = None

        if dns_cost is not None:
            self.dns_cost = dns_cost
        if diagnose_detail is not None:
            self.diagnose_detail = diagnose_detail
        if http_code is not None:
            self.http_code = http_code
        if http_request_header is not None:
            self.http_request_header = http_request_header
        if http_response_body is not None:
            self.http_response_body = http_response_body
        if http_response_header is not None:
            self.http_response_header = http_response_header
        if http_version is not None:
            self.http_version = http_version
        if location_detail is not None:
            self.location_detail = location_detail
        if recv_cost is not None:
            self.recv_cost = recv_cost
        if redirect_cost is not None:
            self.redirect_cost = redirect_cost
        if redirect_nums is not None:
            self.redirect_nums = redirect_nums
        if send_cost is not None:
            self.send_cost = send_cost
        if ssl_cost is not None:
            self.ssl_cost = ssl_cost
        if tcp_cost is not None:
            self.tcp_cost = tcp_cost
        if total_cost is not None:
            self.total_cost = total_cost
        if ttfb_cost is not None:
            self.ttfb_cost = ttfb_cost

    @property
    def dns_cost(self):
        """Gets the dns_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The dns_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._dns_cost

    @dns_cost.setter
    def dns_cost(self, dns_cost):
        """Sets the dns_cost of this HTTPDetailForGetTaskResultOutput.


        :param dns_cost: The dns_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._dns_cost = dns_cost

    @property
    def diagnose_detail(self):
        """Gets the diagnose_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The diagnose_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: DiagnoseDetailForGetTaskResultOutput
        """
        return self._diagnose_detail

    @diagnose_detail.setter
    def diagnose_detail(self, diagnose_detail):
        """Sets the diagnose_detail of this HTTPDetailForGetTaskResultOutput.


        :param diagnose_detail: The diagnose_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: DiagnoseDetailForGetTaskResultOutput
        """

        self._diagnose_detail = diagnose_detail

    @property
    def http_code(self):
        """Gets the http_code of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_code of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._http_code

    @http_code.setter
    def http_code(self, http_code):
        """Sets the http_code of this HTTPDetailForGetTaskResultOutput.


        :param http_code: The http_code of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._http_code = http_code

    @property
    def http_request_header(self):
        """Gets the http_request_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_request_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_request_header

    @http_request_header.setter
    def http_request_header(self, http_request_header):
        """Sets the http_request_header of this HTTPDetailForGetTaskResultOutput.


        :param http_request_header: The http_request_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_request_header = http_request_header

    @property
    def http_response_body(self):
        """Gets the http_response_body of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_response_body of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_response_body

    @http_response_body.setter
    def http_response_body(self, http_response_body):
        """Sets the http_response_body of this HTTPDetailForGetTaskResultOutput.


        :param http_response_body: The http_response_body of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_response_body = http_response_body

    @property
    def http_response_header(self):
        """Gets the http_response_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_response_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_response_header

    @http_response_header.setter
    def http_response_header(self, http_response_header):
        """Sets the http_response_header of this HTTPDetailForGetTaskResultOutput.


        :param http_response_header: The http_response_header of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_response_header = http_response_header

    @property
    def http_version(self):
        """Gets the http_version of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The http_version of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_version

    @http_version.setter
    def http_version(self, http_version):
        """Sets the http_version of this HTTPDetailForGetTaskResultOutput.


        :param http_version: The http_version of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._http_version = http_version

    @property
    def location_detail(self):
        """Gets the location_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The location_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: list[LocationDetailForGetTaskResultOutput]
        """
        return self._location_detail

    @location_detail.setter
    def location_detail(self, location_detail):
        """Sets the location_detail of this HTTPDetailForGetTaskResultOutput.


        :param location_detail: The location_detail of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: list[LocationDetailForGetTaskResultOutput]
        """

        self._location_detail = location_detail

    @property
    def recv_cost(self):
        """Gets the recv_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The recv_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._recv_cost

    @recv_cost.setter
    def recv_cost(self, recv_cost):
        """Sets the recv_cost of this HTTPDetailForGetTaskResultOutput.


        :param recv_cost: The recv_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._recv_cost = recv_cost

    @property
    def redirect_cost(self):
        """Gets the redirect_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The redirect_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._redirect_cost

    @redirect_cost.setter
    def redirect_cost(self, redirect_cost):
        """Sets the redirect_cost of this HTTPDetailForGetTaskResultOutput.


        :param redirect_cost: The redirect_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._redirect_cost = redirect_cost

    @property
    def redirect_nums(self):
        """Gets the redirect_nums of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The redirect_nums of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._redirect_nums

    @redirect_nums.setter
    def redirect_nums(self, redirect_nums):
        """Sets the redirect_nums of this HTTPDetailForGetTaskResultOutput.


        :param redirect_nums: The redirect_nums of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._redirect_nums = redirect_nums

    @property
    def send_cost(self):
        """Gets the send_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The send_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_cost

    @send_cost.setter
    def send_cost(self, send_cost):
        """Sets the send_cost of this HTTPDetailForGetTaskResultOutput.


        :param send_cost: The send_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._send_cost = send_cost

    @property
    def ssl_cost(self):
        """Gets the ssl_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ssl_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ssl_cost

    @ssl_cost.setter
    def ssl_cost(self, ssl_cost):
        """Sets the ssl_cost of this HTTPDetailForGetTaskResultOutput.


        :param ssl_cost: The ssl_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ssl_cost = ssl_cost

    @property
    def tcp_cost(self):
        """Gets the tcp_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_cost

    @tcp_cost.setter
    def tcp_cost(self, tcp_cost):
        """Sets the tcp_cost of this HTTPDetailForGetTaskResultOutput.


        :param tcp_cost: The tcp_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_cost = tcp_cost

    @property
    def total_cost(self):
        """Gets the total_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The total_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_cost

    @total_cost.setter
    def total_cost(self, total_cost):
        """Sets the total_cost of this HTTPDetailForGetTaskResultOutput.


        :param total_cost: The total_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._total_cost = total_cost

    @property
    def ttfb_cost(self):
        """Gets the ttfb_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ttfb_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._ttfb_cost

    @ttfb_cost.setter
    def ttfb_cost(self, ttfb_cost):
        """Sets the ttfb_cost of this HTTPDetailForGetTaskResultOutput.


        :param ttfb_cost: The ttfb_cost of this HTTPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._ttfb_cost = ttfb_cost

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HTTPDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HTTPDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HTTPDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
