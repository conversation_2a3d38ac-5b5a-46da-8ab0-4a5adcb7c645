# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaseInfoForGetOneRaspAlarmOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'hostname': 'str',
        'in_ip_list': 'list[str]',
        'os': 'str',
        'os_platform': 'str',
        'out_ip_list': 'list[str]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'hostname': 'Hostname',
        'in_ip_list': 'InIPList',
        'os': 'Os',
        'os_platform': 'OsPlatform',
        'out_ip_list': 'OutIPList'
    }

    def __init__(self, agent_id=None, hostname=None, in_ip_list=None, os=None, os_platform=None, out_ip_list=None, _configuration=None):  # noqa: E501
        """BaseInfoForGetOneRaspAlarmOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._hostname = None
        self._in_ip_list = None
        self._os = None
        self._os_platform = None
        self._out_ip_list = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if hostname is not None:
            self.hostname = hostname
        if in_ip_list is not None:
            self.in_ip_list = in_ip_list
        if os is not None:
            self.os = os
        if os_platform is not None:
            self.os_platform = os_platform
        if out_ip_list is not None:
            self.out_ip_list = out_ip_list

    @property
    def agent_id(self):
        """Gets the agent_id of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The agent_id of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this BaseInfoForGetOneRaspAlarmOutput.


        :param agent_id: The agent_id of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def hostname(self):
        """Gets the hostname of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The hostname of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this BaseInfoForGetOneRaspAlarmOutput.


        :param hostname: The hostname of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def in_ip_list(self):
        """Gets the in_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The in_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._in_ip_list

    @in_ip_list.setter
    def in_ip_list(self, in_ip_list):
        """Sets the in_ip_list of this BaseInfoForGetOneRaspAlarmOutput.


        :param in_ip_list: The in_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: list[str]
        """

        self._in_ip_list = in_ip_list

    @property
    def os(self):
        """Gets the os of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The os of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this BaseInfoForGetOneRaspAlarmOutput.


        :param os: The os of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def os_platform(self):
        """Gets the os_platform of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The os_platform of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_platform

    @os_platform.setter
    def os_platform(self, os_platform):
        """Sets the os_platform of this BaseInfoForGetOneRaspAlarmOutput.


        :param os_platform: The os_platform of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._os_platform = os_platform

    @property
    def out_ip_list(self):
        """Gets the out_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The out_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._out_ip_list

    @out_ip_list.setter
    def out_ip_list(self, out_ip_list):
        """Sets the out_ip_list of this BaseInfoForGetOneRaspAlarmOutput.


        :param out_ip_list: The out_ip_list of this BaseInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: list[str]
        """

        self._out_ip_list = out_ip_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaseInfoForGetOneRaspAlarmOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaseInfoForGetOneRaspAlarmOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaseInfoForGetOneRaspAlarmOutput):
            return True

        return self.to_dict() != other.to_dict()
