# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ChannelForGetDeviceChannelsV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_at': 'str',
        'device_item': 'list[DeviceItemForGetDeviceChannelsV2Output]',
        'stream_id': 'str'
    }

    attribute_map = {
        'create_at': 'CreateAt',
        'device_item': 'DeviceItem',
        'stream_id': 'StreamID'
    }

    def __init__(self, create_at=None, device_item=None, stream_id=None, _configuration=None):  # noqa: E501
        """ChannelForGetDeviceChannelsV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_at = None
        self._device_item = None
        self._stream_id = None
        self.discriminator = None

        if create_at is not None:
            self.create_at = create_at
        if device_item is not None:
            self.device_item = device_item
        if stream_id is not None:
            self.stream_id = stream_id

    @property
    def create_at(self):
        """Gets the create_at of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The create_at of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this ChannelForGetDeviceChannelsV2Output.


        :param create_at: The create_at of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._create_at = create_at

    @property
    def device_item(self):
        """Gets the device_item of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The device_item of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: list[DeviceItemForGetDeviceChannelsV2Output]
        """
        return self._device_item

    @device_item.setter
    def device_item(self, device_item):
        """Sets the device_item of this ChannelForGetDeviceChannelsV2Output.


        :param device_item: The device_item of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :type: list[DeviceItemForGetDeviceChannelsV2Output]
        """

        self._device_item = device_item

    @property
    def stream_id(self):
        """Gets the stream_id of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The stream_id of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this ChannelForGetDeviceChannelsV2Output.


        :param stream_id: The stream_id of this ChannelForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._stream_id = stream_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ChannelForGetDeviceChannelsV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ChannelForGetDeviceChannelsV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ChannelForGetDeviceChannelsV2Output):
            return True

        return self.to_dict() != other.to_dict()
