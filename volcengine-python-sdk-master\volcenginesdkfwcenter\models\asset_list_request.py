# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssetListRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset': 'str',
        'asset_type': 'list[str]',
        'current_page': 'int',
        'order_dir': 'str',
        'page_size': 'int',
        'stat': 'int'
    }

    attribute_map = {
        'asset': 'asset',
        'asset_type': 'asset_type',
        'current_page': 'current_page',
        'order_dir': 'order_dir',
        'page_size': 'page_size',
        'stat': 'stat'
    }

    def __init__(self, asset=None, asset_type=None, current_page=None, order_dir=None, page_size=None, stat=None, _configuration=None):  # noqa: E501
        """AssetListRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset = None
        self._asset_type = None
        self._current_page = None
        self._order_dir = None
        self._page_size = None
        self._stat = None
        self.discriminator = None

        if asset is not None:
            self.asset = asset
        if asset_type is not None:
            self.asset_type = asset_type
        if current_page is not None:
            self.current_page = current_page
        if order_dir is not None:
            self.order_dir = order_dir
        if page_size is not None:
            self.page_size = page_size
        if stat is not None:
            self.stat = stat

    @property
    def asset(self):
        """Gets the asset of this AssetListRequest.  # noqa: E501


        :return: The asset of this AssetListRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset

    @asset.setter
    def asset(self, asset):
        """Sets the asset of this AssetListRequest.


        :param asset: The asset of this AssetListRequest.  # noqa: E501
        :type: str
        """

        self._asset = asset

    @property
    def asset_type(self):
        """Gets the asset_type of this AssetListRequest.  # noqa: E501


        :return: The asset_type of this AssetListRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this AssetListRequest.


        :param asset_type: The asset_type of this AssetListRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_type = asset_type

    @property
    def current_page(self):
        """Gets the current_page of this AssetListRequest.  # noqa: E501


        :return: The current_page of this AssetListRequest.  # noqa: E501
        :rtype: int
        """
        return self._current_page

    @current_page.setter
    def current_page(self, current_page):
        """Sets the current_page of this AssetListRequest.


        :param current_page: The current_page of this AssetListRequest.  # noqa: E501
        :type: int
        """

        self._current_page = current_page

    @property
    def order_dir(self):
        """Gets the order_dir of this AssetListRequest.  # noqa: E501


        :return: The order_dir of this AssetListRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_dir

    @order_dir.setter
    def order_dir(self, order_dir):
        """Sets the order_dir of this AssetListRequest.


        :param order_dir: The order_dir of this AssetListRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["asc", "desc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_dir not in allowed_values):
            raise ValueError(
                "Invalid value for `order_dir` ({0}), must be one of {1}"  # noqa: E501
                .format(order_dir, allowed_values)
            )

        self._order_dir = order_dir

    @property
    def page_size(self):
        """Gets the page_size of this AssetListRequest.  # noqa: E501


        :return: The page_size of this AssetListRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this AssetListRequest.


        :param page_size: The page_size of this AssetListRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 1000000):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `1000000`")  # noqa: E501

        self._page_size = page_size

    @property
    def stat(self):
        """Gets the stat of this AssetListRequest.  # noqa: E501


        :return: The stat of this AssetListRequest.  # noqa: E501
        :rtype: int
        """
        return self._stat

    @stat.setter
    def stat(self, stat):
        """Sets the stat of this AssetListRequest.


        :param stat: The stat of this AssetListRequest.  # noqa: E501
        :type: int
        """

        self._stat = stat

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssetListRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssetListRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssetListRequest):
            return True

        return self.to_dict() != other.to_dict()
