# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthInfoForGetMultiLevelInstitutionDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'app_sec_open': 'bool',
        'cluster_sec_open': 'bool',
        'expire_time': 'int',
        'last_active_time': 'int',
        'protect_host': 'ProtectHostForGetMultiLevelInstitutionDetailOutput'
    }

    attribute_map = {
        'app_sec_open': 'AppSecOpen',
        'cluster_sec_open': 'ClusterSecOpen',
        'expire_time': 'ExpireTime',
        'last_active_time': 'LastActiveTime',
        'protect_host': 'ProtectHost'
    }

    def __init__(self, app_sec_open=None, cluster_sec_open=None, expire_time=None, last_active_time=None, protect_host=None, _configuration=None):  # noqa: E501
        """AuthInfoForGetMultiLevelInstitutionDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._app_sec_open = None
        self._cluster_sec_open = None
        self._expire_time = None
        self._last_active_time = None
        self._protect_host = None
        self.discriminator = None

        if app_sec_open is not None:
            self.app_sec_open = app_sec_open
        if cluster_sec_open is not None:
            self.cluster_sec_open = cluster_sec_open
        if expire_time is not None:
            self.expire_time = expire_time
        if last_active_time is not None:
            self.last_active_time = last_active_time
        if protect_host is not None:
            self.protect_host = protect_host

    @property
    def app_sec_open(self):
        """Gets the app_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The app_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._app_sec_open

    @app_sec_open.setter
    def app_sec_open(self, app_sec_open):
        """Sets the app_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.


        :param app_sec_open: The app_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: bool
        """

        self._app_sec_open = app_sec_open

    @property
    def cluster_sec_open(self):
        """Gets the cluster_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The cluster_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cluster_sec_open

    @cluster_sec_open.setter
    def cluster_sec_open(self, cluster_sec_open):
        """Sets the cluster_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.


        :param cluster_sec_open: The cluster_sec_open of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: bool
        """

        self._cluster_sec_open = cluster_sec_open

    @property
    def expire_time(self):
        """Gets the expire_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The expire_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.


        :param expire_time: The expire_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def last_active_time(self):
        """Gets the last_active_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The last_active_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_active_time

    @last_active_time.setter
    def last_active_time(self, last_active_time):
        """Sets the last_active_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.


        :param last_active_time: The last_active_time of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._last_active_time = last_active_time

    @property
    def protect_host(self):
        """Gets the protect_host of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The protect_host of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: ProtectHostForGetMultiLevelInstitutionDetailOutput
        """
        return self._protect_host

    @protect_host.setter
    def protect_host(self, protect_host):
        """Sets the protect_host of this AuthInfoForGetMultiLevelInstitutionDetailOutput.


        :param protect_host: The protect_host of this AuthInfoForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: ProtectHostForGetMultiLevelInstitutionDetailOutput
        """

        self._protect_host = protect_host

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthInfoForGetMultiLevelInstitutionDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthInfoForGetMultiLevelInstitutionDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthInfoForGetMultiLevelInstitutionDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
