# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetLiveTrafficPostPayDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'actual_pre_pay_result': 'list[ActualPrePayResultForGetLiveTrafficPostPayDataOutput]',
        'actual_result': 'list[ActualResultForGetLiveTrafficPostPayDataOutput]',
        'estimated_result': 'list[EstimatedResultForGetLiveTrafficPostPayDataOutput]'
    }

    attribute_map = {
        'actual_pre_pay_result': 'ActualPrePayResult',
        'actual_result': 'ActualResult',
        'estimated_result': 'EstimatedResult'
    }

    def __init__(self, actual_pre_pay_result=None, actual_result=None, estimated_result=None, _configuration=None):  # noqa: E501
        """GetLiveTrafficPostPayDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._actual_pre_pay_result = None
        self._actual_result = None
        self._estimated_result = None
        self.discriminator = None

        if actual_pre_pay_result is not None:
            self.actual_pre_pay_result = actual_pre_pay_result
        if actual_result is not None:
            self.actual_result = actual_result
        if estimated_result is not None:
            self.estimated_result = estimated_result

    @property
    def actual_pre_pay_result(self):
        """Gets the actual_pre_pay_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501


        :return: The actual_pre_pay_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :rtype: list[ActualPrePayResultForGetLiveTrafficPostPayDataOutput]
        """
        return self._actual_pre_pay_result

    @actual_pre_pay_result.setter
    def actual_pre_pay_result(self, actual_pre_pay_result):
        """Sets the actual_pre_pay_result of this GetLiveTrafficPostPayDataResponse.


        :param actual_pre_pay_result: The actual_pre_pay_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :type: list[ActualPrePayResultForGetLiveTrafficPostPayDataOutput]
        """

        self._actual_pre_pay_result = actual_pre_pay_result

    @property
    def actual_result(self):
        """Gets the actual_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501


        :return: The actual_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :rtype: list[ActualResultForGetLiveTrafficPostPayDataOutput]
        """
        return self._actual_result

    @actual_result.setter
    def actual_result(self, actual_result):
        """Sets the actual_result of this GetLiveTrafficPostPayDataResponse.


        :param actual_result: The actual_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :type: list[ActualResultForGetLiveTrafficPostPayDataOutput]
        """

        self._actual_result = actual_result

    @property
    def estimated_result(self):
        """Gets the estimated_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501


        :return: The estimated_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :rtype: list[EstimatedResultForGetLiveTrafficPostPayDataOutput]
        """
        return self._estimated_result

    @estimated_result.setter
    def estimated_result(self, estimated_result):
        """Sets the estimated_result of this GetLiveTrafficPostPayDataResponse.


        :param estimated_result: The estimated_result of this GetLiveTrafficPostPayDataResponse.  # noqa: E501
        :type: list[EstimatedResultForGetLiveTrafficPostPayDataOutput]
        """

        self._estimated_result = estimated_result

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetLiveTrafficPostPayDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetLiveTrafficPostPayDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetLiveTrafficPostPayDataResponse):
            return True

        return self.to_dict() != other.to_dict()
