# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetLoadBalancerRenewalRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'load_balancer_id': 'str',
        'remain_renew_times': 'int',
        'renew_period_times': 'int',
        'renew_type': 'int'
    }

    attribute_map = {
        'load_balancer_id': 'LoadBalancerId',
        'remain_renew_times': 'RemainRenewTimes',
        'renew_period_times': 'RenewPeriodTimes',
        'renew_type': 'RenewType'
    }

    def __init__(self, load_balancer_id=None, remain_renew_times=None, renew_period_times=None, renew_type=None, _configuration=None):  # noqa: E501
        """SetLoadBalancerRenewalRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._load_balancer_id = None
        self._remain_renew_times = None
        self._renew_period_times = None
        self._renew_type = None
        self.discriminator = None

        self.load_balancer_id = load_balancer_id
        if remain_renew_times is not None:
            self.remain_renew_times = remain_renew_times
        if renew_period_times is not None:
            self.renew_period_times = renew_period_times
        self.renew_type = renew_type

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this SetLoadBalancerRenewalRequest.  # noqa: E501


        :return: The load_balancer_id of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this SetLoadBalancerRenewalRequest.


        :param load_balancer_id: The load_balancer_id of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def remain_renew_times(self):
        """Gets the remain_renew_times of this SetLoadBalancerRenewalRequest.  # noqa: E501


        :return: The remain_renew_times of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :rtype: int
        """
        return self._remain_renew_times

    @remain_renew_times.setter
    def remain_renew_times(self, remain_renew_times):
        """Sets the remain_renew_times of this SetLoadBalancerRenewalRequest.


        :param remain_renew_times: The remain_renew_times of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :type: int
        """

        self._remain_renew_times = remain_renew_times

    @property
    def renew_period_times(self):
        """Gets the renew_period_times of this SetLoadBalancerRenewalRequest.  # noqa: E501


        :return: The renew_period_times of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :rtype: int
        """
        return self._renew_period_times

    @renew_period_times.setter
    def renew_period_times(self, renew_period_times):
        """Sets the renew_period_times of this SetLoadBalancerRenewalRequest.


        :param renew_period_times: The renew_period_times of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :type: int
        """

        self._renew_period_times = renew_period_times

    @property
    def renew_type(self):
        """Gets the renew_type of this SetLoadBalancerRenewalRequest.  # noqa: E501


        :return: The renew_type of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this SetLoadBalancerRenewalRequest.


        :param renew_type: The renew_type of this SetLoadBalancerRenewalRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and renew_type is None:
            raise ValueError("Invalid value for `renew_type`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                renew_type is not None and renew_type > 2):  # noqa: E501
            raise ValueError("Invalid value for `renew_type`, must be a value less than or equal to `2`")  # noqa: E501
        if (self._configuration.client_side_validation and
                renew_type is not None and renew_type < 1):  # noqa: E501
            raise ValueError("Invalid value for `renew_type`, must be a value greater than or equal to `1`")  # noqa: E501

        self._renew_type = renew_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetLoadBalancerRenewalRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetLoadBalancerRenewalRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetLoadBalancerRenewalRequest):
            return True

        return self.to_dict() != other.to_dict()
