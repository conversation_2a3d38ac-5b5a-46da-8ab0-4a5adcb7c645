# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListParameterTemplatesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'project_name': 'str',
        'template_category': 'str',
        'template_name': 'str',
        'template_source': 'str',
        'template_type': 'str',
        'template_type_version': 'str'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'project_name': 'ProjectName',
        'template_category': 'TemplateCategory',
        'template_name': 'TemplateName',
        'template_source': 'TemplateSource',
        'template_type': 'TemplateType',
        'template_type_version': 'TemplateTypeVersion'
    }

    def __init__(self, limit=None, offset=None, project_name=None, template_category=None, template_name=None, template_source=None, template_type=None, template_type_version=None, _configuration=None):  # noqa: E501
        """ListParameterTemplatesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._project_name = None
        self._template_category = None
        self._template_name = None
        self._template_source = None
        self._template_type = None
        self._template_type_version = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if project_name is not None:
            self.project_name = project_name
        if template_category is not None:
            self.template_category = template_category
        if template_name is not None:
            self.template_name = template_name
        if template_source is not None:
            self.template_source = template_source
        if template_type is not None:
            self.template_type = template_type
        if template_type_version is not None:
            self.template_type_version = template_type_version

    @property
    def limit(self):
        """Gets the limit of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The limit of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListParameterTemplatesRequest.


        :param limit: The limit of this ListParameterTemplatesRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The offset of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListParameterTemplatesRequest.


        :param offset: The offset of this ListParameterTemplatesRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def project_name(self):
        """Gets the project_name of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The project_name of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListParameterTemplatesRequest.


        :param project_name: The project_name of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def template_category(self):
        """Gets the template_category of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The template_category of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_category

    @template_category.setter
    def template_category(self, template_category):
        """Sets the template_category of this ListParameterTemplatesRequest.


        :param template_category: The template_category of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._template_category = template_category

    @property
    def template_name(self):
        """Gets the template_name of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The template_name of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_name

    @template_name.setter
    def template_name(self, template_name):
        """Sets the template_name of this ListParameterTemplatesRequest.


        :param template_name: The template_name of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._template_name = template_name

    @property
    def template_source(self):
        """Gets the template_source of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The template_source of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_source

    @template_source.setter
    def template_source(self, template_source):
        """Sets the template_source of this ListParameterTemplatesRequest.


        :param template_source: The template_source of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._template_source = template_source

    @property
    def template_type(self):
        """Gets the template_type of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The template_type of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_type

    @template_type.setter
    def template_type(self, template_type):
        """Sets the template_type of this ListParameterTemplatesRequest.


        :param template_type: The template_type of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._template_type = template_type

    @property
    def template_type_version(self):
        """Gets the template_type_version of this ListParameterTemplatesRequest.  # noqa: E501


        :return: The template_type_version of this ListParameterTemplatesRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_type_version

    @template_type_version.setter
    def template_type_version(self, template_type_version):
        """Sets the template_type_version of this ListParameterTemplatesRequest.


        :param template_type_version: The template_type_version of this ListParameterTemplatesRequest.  # noqa: E501
        :type: str
        """

        self._template_type_version = template_type_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListParameterTemplatesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListParameterTemplatesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListParameterTemplatesRequest):
            return True

        return self.to_dict() != other.to_dict()
