# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuestionnaireForListQuestionnaireAnswerDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'question_answers': 'list[QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output]',
        'send_time': 'int',
        'user_info': 'list[UserInfoForListQuestionnaireAnswerDataAPIV2Output]'
    }

    attribute_map = {
        'question_answers': 'QuestionAnswers',
        'send_time': 'SendTime',
        'user_info': 'UserInfo'
    }

    def __init__(self, question_answers=None, send_time=None, user_info=None, _configuration=None):  # noqa: E501
        """QuestionnaireForListQuestionnaireAnswerDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._question_answers = None
        self._send_time = None
        self._user_info = None
        self.discriminator = None

        if question_answers is not None:
            self.question_answers = question_answers
        if send_time is not None:
            self.send_time = send_time
        if user_info is not None:
            self.user_info = user_info

    @property
    def question_answers(self):
        """Gets the question_answers of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The question_answers of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: list[QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output]
        """
        return self._question_answers

    @question_answers.setter
    def question_answers(self, question_answers):
        """Sets the question_answers of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.


        :param question_answers: The question_answers of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: list[QuestionAnswerForListQuestionnaireAnswerDataAPIV2Output]
        """

        self._question_answers = question_answers

    @property
    def send_time(self):
        """Gets the send_time of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The send_time of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.


        :param send_time: The send_time of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def user_info(self):
        """Gets the user_info of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The user_info of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: list[UserInfoForListQuestionnaireAnswerDataAPIV2Output]
        """
        return self._user_info

    @user_info.setter
    def user_info(self, user_info):
        """Sets the user_info of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.


        :param user_info: The user_info of this QuestionnaireForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: list[UserInfoForListQuestionnaireAnswerDataAPIV2Output]
        """

        self._user_info = user_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuestionnaireForListQuestionnaireAnswerDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuestionnaireForListQuestionnaireAnswerDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuestionnaireForListQuestionnaireAnswerDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
