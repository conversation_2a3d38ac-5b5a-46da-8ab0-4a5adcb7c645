# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListJobInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_ids': 'list[str]',
        'job_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'state': 'str'
    }

    attribute_map = {
        'instance_ids': 'InstanceIds',
        'job_id': 'JobId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'state': 'State'
    }

    def __init__(self, instance_ids=None, job_id=None, page_number=None, page_size=None, sort_by=None, sort_order=None, state=None, _configuration=None):  # noqa: E501
        """ListJobInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_ids = None
        self._job_id = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._state = None
        self.discriminator = None

        if instance_ids is not None:
            self.instance_ids = instance_ids
        self.job_id = job_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if state is not None:
            self.state = state

    @property
    def instance_ids(self):
        """Gets the instance_ids of this ListJobInstancesRequest.  # noqa: E501


        :return: The instance_ids of this ListJobInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this ListJobInstancesRequest.


        :param instance_ids: The instance_ids of this ListJobInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def job_id(self):
        """Gets the job_id of this ListJobInstancesRequest.  # noqa: E501


        :return: The job_id of this ListJobInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._job_id

    @job_id.setter
    def job_id(self, job_id):
        """Sets the job_id of this ListJobInstancesRequest.


        :param job_id: The job_id of this ListJobInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and job_id is None:
            raise ValueError("Invalid value for `job_id`, must not be `None`")  # noqa: E501

        self._job_id = job_id

    @property
    def page_number(self):
        """Gets the page_number of this ListJobInstancesRequest.  # noqa: E501


        :return: The page_number of this ListJobInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListJobInstancesRequest.


        :param page_number: The page_number of this ListJobInstancesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListJobInstancesRequest.  # noqa: E501


        :return: The page_size of this ListJobInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListJobInstancesRequest.


        :param page_size: The page_size of this ListJobInstancesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size < 10):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value greater than or equal to `10`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListJobInstancesRequest.  # noqa: E501


        :return: The sort_by of this ListJobInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListJobInstancesRequest.


        :param sort_by: The sort_by of this ListJobInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreateTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListJobInstancesRequest.  # noqa: E501


        :return: The sort_order of this ListJobInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListJobInstancesRequest.


        :param sort_order: The sort_order of this ListJobInstancesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Ascend", "Descend"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def state(self):
        """Gets the state of this ListJobInstancesRequest.  # noqa: E501


        :return: The state of this ListJobInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListJobInstancesRequest.


        :param state: The state of this ListJobInstancesRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListJobInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListJobInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListJobInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
