# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListOrdersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time_end': 'str',
        'create_time_start': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'order_type': 'str',
        'status': 'str'
    }

    attribute_map = {
        'create_time_end': 'CreateTimeEnd',
        'create_time_start': 'CreateTimeStart',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'order_type': 'OrderType',
        'status': 'Status'
    }

    def __init__(self, create_time_end=None, create_time_start=None, max_results=None, next_token=None, order_type=None, status=None, _configuration=None):  # noqa: E501
        """ListOrdersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time_end = None
        self._create_time_start = None
        self._max_results = None
        self._next_token = None
        self._order_type = None
        self._status = None
        self.discriminator = None

        self.create_time_end = create_time_end
        self.create_time_start = create_time_start
        self.max_results = max_results
        self.next_token = next_token
        self.order_type = order_type
        self.status = status

    @property
    def create_time_end(self):
        """Gets the create_time_end of this ListOrdersRequest.  # noqa: E501


        :return: The create_time_end of this ListOrdersRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_end

    @create_time_end.setter
    def create_time_end(self, create_time_end):
        """Sets the create_time_end of this ListOrdersRequest.


        :param create_time_end: The create_time_end of this ListOrdersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and create_time_end is None:
            raise ValueError("Invalid value for `create_time_end`, must not be `None`")  # noqa: E501

        self._create_time_end = create_time_end

    @property
    def create_time_start(self):
        """Gets the create_time_start of this ListOrdersRequest.  # noqa: E501


        :return: The create_time_start of this ListOrdersRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_start

    @create_time_start.setter
    def create_time_start(self, create_time_start):
        """Sets the create_time_start of this ListOrdersRequest.


        :param create_time_start: The create_time_start of this ListOrdersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and create_time_start is None:
            raise ValueError("Invalid value for `create_time_start`, must not be `None`")  # noqa: E501

        self._create_time_start = create_time_start

    @property
    def max_results(self):
        """Gets the max_results of this ListOrdersRequest.  # noqa: E501


        :return: The max_results of this ListOrdersRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListOrdersRequest.


        :param max_results: The max_results of this ListOrdersRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and max_results is None:
            raise ValueError("Invalid value for `max_results`, must not be `None`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListOrdersRequest.  # noqa: E501


        :return: The next_token of this ListOrdersRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListOrdersRequest.


        :param next_token: The next_token of this ListOrdersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and next_token is None:
            raise ValueError("Invalid value for `next_token`, must not be `None`")  # noqa: E501

        self._next_token = next_token

    @property
    def order_type(self):
        """Gets the order_type of this ListOrdersRequest.  # noqa: E501


        :return: The order_type of this ListOrdersRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_type

    @order_type.setter
    def order_type(self, order_type):
        """Sets the order_type of this ListOrdersRequest.


        :param order_type: The order_type of this ListOrdersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and order_type is None:
            raise ValueError("Invalid value for `order_type`, must not be `None`")  # noqa: E501

        self._order_type = order_type

    @property
    def status(self):
        """Gets the status of this ListOrdersRequest.  # noqa: E501


        :return: The status of this ListOrdersRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListOrdersRequest.


        :param status: The status of this ListOrdersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListOrdersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListOrdersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListOrdersRequest):
            return True

        return self.to_dict() != other.to_dict()
