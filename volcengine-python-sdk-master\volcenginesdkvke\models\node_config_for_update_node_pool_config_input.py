# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeConfigForUpdateNodePoolConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_container_storage_enabled': 'bool',
        'auto_renew': 'bool',
        'auto_renew_period': 'int',
        'data_volumes': 'list[DataVolumeForUpdateNodePoolConfigInput]',
        'hpc_cluster_ids': 'list[str]',
        'image_id': 'str',
        'initialize_script': 'str',
        'instance_type_ids': 'list[str]',
        'name_prefix': 'str',
        'period': 'int',
        'project_name': 'str',
        'public_access_config': 'PublicAccessConfigForUpdateNodePoolConfigInput',
        'public_access_enabled': 'bool',
        'security': 'SecurityForUpdateNodePoolConfigInput',
        'subnet_ids': 'list[str]',
        'system_volume': 'SystemVolumeForUpdateNodePoolConfigInput',
        'tags': 'list[TagForUpdateNodePoolConfigInput]'
    }

    attribute_map = {
        'additional_container_storage_enabled': 'AdditionalContainerStorageEnabled',
        'auto_renew': 'AutoRenew',
        'auto_renew_period': 'AutoRenewPeriod',
        'data_volumes': 'DataVolumes',
        'hpc_cluster_ids': 'HpcClusterIds',
        'image_id': 'ImageId',
        'initialize_script': 'InitializeScript',
        'instance_type_ids': 'InstanceTypeIds',
        'name_prefix': 'NamePrefix',
        'period': 'Period',
        'project_name': 'ProjectName',
        'public_access_config': 'PublicAccessConfig',
        'public_access_enabled': 'PublicAccessEnabled',
        'security': 'Security',
        'subnet_ids': 'SubnetIds',
        'system_volume': 'SystemVolume',
        'tags': 'Tags'
    }

    def __init__(self, additional_container_storage_enabled=None, auto_renew=None, auto_renew_period=None, data_volumes=None, hpc_cluster_ids=None, image_id=None, initialize_script=None, instance_type_ids=None, name_prefix=None, period=None, project_name=None, public_access_config=None, public_access_enabled=None, security=None, subnet_ids=None, system_volume=None, tags=None, _configuration=None):  # noqa: E501
        """NodeConfigForUpdateNodePoolConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_container_storage_enabled = None
        self._auto_renew = None
        self._auto_renew_period = None
        self._data_volumes = None
        self._hpc_cluster_ids = None
        self._image_id = None
        self._initialize_script = None
        self._instance_type_ids = None
        self._name_prefix = None
        self._period = None
        self._project_name = None
        self._public_access_config = None
        self._public_access_enabled = None
        self._security = None
        self._subnet_ids = None
        self._system_volume = None
        self._tags = None
        self.discriminator = None

        if additional_container_storage_enabled is not None:
            self.additional_container_storage_enabled = additional_container_storage_enabled
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_renew_period is not None:
            self.auto_renew_period = auto_renew_period
        if data_volumes is not None:
            self.data_volumes = data_volumes
        if hpc_cluster_ids is not None:
            self.hpc_cluster_ids = hpc_cluster_ids
        if image_id is not None:
            self.image_id = image_id
        if initialize_script is not None:
            self.initialize_script = initialize_script
        if instance_type_ids is not None:
            self.instance_type_ids = instance_type_ids
        if name_prefix is not None:
            self.name_prefix = name_prefix
        if period is not None:
            self.period = period
        if project_name is not None:
            self.project_name = project_name
        if public_access_config is not None:
            self.public_access_config = public_access_config
        if public_access_enabled is not None:
            self.public_access_enabled = public_access_enabled
        if security is not None:
            self.security = security
        if subnet_ids is not None:
            self.subnet_ids = subnet_ids
        if system_volume is not None:
            self.system_volume = system_volume
        if tags is not None:
            self.tags = tags

    @property
    def additional_container_storage_enabled(self):
        """Gets the additional_container_storage_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The additional_container_storage_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._additional_container_storage_enabled

    @additional_container_storage_enabled.setter
    def additional_container_storage_enabled(self, additional_container_storage_enabled):
        """Sets the additional_container_storage_enabled of this NodeConfigForUpdateNodePoolConfigInput.


        :param additional_container_storage_enabled: The additional_container_storage_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: bool
        """

        self._additional_container_storage_enabled = additional_container_storage_enabled

    @property
    def auto_renew(self):
        """Gets the auto_renew of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The auto_renew of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this NodeConfigForUpdateNodePoolConfigInput.


        :param auto_renew: The auto_renew of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_renew_period(self):
        """Gets the auto_renew_period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The auto_renew_period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._auto_renew_period

    @auto_renew_period.setter
    def auto_renew_period(self, auto_renew_period):
        """Sets the auto_renew_period of this NodeConfigForUpdateNodePoolConfigInput.


        :param auto_renew_period: The auto_renew_period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._auto_renew_period = auto_renew_period

    @property
    def data_volumes(self):
        """Gets the data_volumes of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The data_volumes of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: list[DataVolumeForUpdateNodePoolConfigInput]
        """
        return self._data_volumes

    @data_volumes.setter
    def data_volumes(self, data_volumes):
        """Sets the data_volumes of this NodeConfigForUpdateNodePoolConfigInput.


        :param data_volumes: The data_volumes of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: list[DataVolumeForUpdateNodePoolConfigInput]
        """

        self._data_volumes = data_volumes

    @property
    def hpc_cluster_ids(self):
        """Gets the hpc_cluster_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The hpc_cluster_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._hpc_cluster_ids

    @hpc_cluster_ids.setter
    def hpc_cluster_ids(self, hpc_cluster_ids):
        """Sets the hpc_cluster_ids of this NodeConfigForUpdateNodePoolConfigInput.


        :param hpc_cluster_ids: The hpc_cluster_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._hpc_cluster_ids = hpc_cluster_ids

    @property
    def image_id(self):
        """Gets the image_id of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The image_id of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this NodeConfigForUpdateNodePoolConfigInput.


        :param image_id: The image_id of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def initialize_script(self):
        """Gets the initialize_script of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The initialize_script of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._initialize_script

    @initialize_script.setter
    def initialize_script(self, initialize_script):
        """Sets the initialize_script of this NodeConfigForUpdateNodePoolConfigInput.


        :param initialize_script: The initialize_script of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._initialize_script = initialize_script

    @property
    def instance_type_ids(self):
        """Gets the instance_type_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The instance_type_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_type_ids

    @instance_type_ids.setter
    def instance_type_ids(self, instance_type_ids):
        """Sets the instance_type_ids of this NodeConfigForUpdateNodePoolConfigInput.


        :param instance_type_ids: The instance_type_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._instance_type_ids = instance_type_ids

    @property
    def name_prefix(self):
        """Gets the name_prefix of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The name_prefix of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._name_prefix

    @name_prefix.setter
    def name_prefix(self, name_prefix):
        """Sets the name_prefix of this NodeConfigForUpdateNodePoolConfigInput.


        :param name_prefix: The name_prefix of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._name_prefix = name_prefix

    @property
    def period(self):
        """Gets the period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this NodeConfigForUpdateNodePoolConfigInput.


        :param period: The period of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def project_name(self):
        """Gets the project_name of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The project_name of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this NodeConfigForUpdateNodePoolConfigInput.


        :param project_name: The project_name of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def public_access_config(self):
        """Gets the public_access_config of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The public_access_config of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: PublicAccessConfigForUpdateNodePoolConfigInput
        """
        return self._public_access_config

    @public_access_config.setter
    def public_access_config(self, public_access_config):
        """Sets the public_access_config of this NodeConfigForUpdateNodePoolConfigInput.


        :param public_access_config: The public_access_config of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: PublicAccessConfigForUpdateNodePoolConfigInput
        """

        self._public_access_config = public_access_config

    @property
    def public_access_enabled(self):
        """Gets the public_access_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The public_access_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._public_access_enabled

    @public_access_enabled.setter
    def public_access_enabled(self, public_access_enabled):
        """Sets the public_access_enabled of this NodeConfigForUpdateNodePoolConfigInput.


        :param public_access_enabled: The public_access_enabled of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: bool
        """

        self._public_access_enabled = public_access_enabled

    @property
    def security(self):
        """Gets the security of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The security of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: SecurityForUpdateNodePoolConfigInput
        """
        return self._security

    @security.setter
    def security(self, security):
        """Sets the security of this NodeConfigForUpdateNodePoolConfigInput.


        :param security: The security of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: SecurityForUpdateNodePoolConfigInput
        """

        self._security = security

    @property
    def subnet_ids(self):
        """Gets the subnet_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The subnet_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_ids

    @subnet_ids.setter
    def subnet_ids(self, subnet_ids):
        """Sets the subnet_ids of this NodeConfigForUpdateNodePoolConfigInput.


        :param subnet_ids: The subnet_ids of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_ids = subnet_ids

    @property
    def system_volume(self):
        """Gets the system_volume of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The system_volume of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: SystemVolumeForUpdateNodePoolConfigInput
        """
        return self._system_volume

    @system_volume.setter
    def system_volume(self, system_volume):
        """Sets the system_volume of this NodeConfigForUpdateNodePoolConfigInput.


        :param system_volume: The system_volume of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: SystemVolumeForUpdateNodePoolConfigInput
        """

        self._system_volume = system_volume

    @property
    def tags(self):
        """Gets the tags of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The tags of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: list[TagForUpdateNodePoolConfigInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this NodeConfigForUpdateNodePoolConfigInput.


        :param tags: The tags of this NodeConfigForUpdateNodePoolConfigInput.  # noqa: E501
        :type: list[TagForUpdateNodePoolConfigInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeConfigForUpdateNodePoolConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeConfigForUpdateNodePoolConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeConfigForUpdateNodePoolConfigInput):
            return True

        return self.to_dict() != other.to_dict()
