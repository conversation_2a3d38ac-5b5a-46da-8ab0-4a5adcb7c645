# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRepoImagePackageInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'license_list': 'list[str]',
        'pkg_name': 'str',
        'pkg_type': 'str'
    }

    attribute_map = {
        'license_list': 'LicenseList',
        'pkg_name': 'PkgName',
        'pkg_type': 'PkgType'
    }

    def __init__(self, license_list=None, pkg_name=None, pkg_type=None, _configuration=None):  # noqa: E501
        """FilterForListRepoImagePackageInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._license_list = None
        self._pkg_name = None
        self._pkg_type = None
        self.discriminator = None

        if license_list is not None:
            self.license_list = license_list
        if pkg_name is not None:
            self.pkg_name = pkg_name
        if pkg_type is not None:
            self.pkg_type = pkg_type

    @property
    def license_list(self):
        """Gets the license_list of this FilterForListRepoImagePackageInput.  # noqa: E501


        :return: The license_list of this FilterForListRepoImagePackageInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._license_list

    @license_list.setter
    def license_list(self, license_list):
        """Sets the license_list of this FilterForListRepoImagePackageInput.


        :param license_list: The license_list of this FilterForListRepoImagePackageInput.  # noqa: E501
        :type: list[str]
        """

        self._license_list = license_list

    @property
    def pkg_name(self):
        """Gets the pkg_name of this FilterForListRepoImagePackageInput.  # noqa: E501


        :return: The pkg_name of this FilterForListRepoImagePackageInput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, pkg_name):
        """Sets the pkg_name of this FilterForListRepoImagePackageInput.


        :param pkg_name: The pkg_name of this FilterForListRepoImagePackageInput.  # noqa: E501
        :type: str
        """

        self._pkg_name = pkg_name

    @property
    def pkg_type(self):
        """Gets the pkg_type of this FilterForListRepoImagePackageInput.  # noqa: E501


        :return: The pkg_type of this FilterForListRepoImagePackageInput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_type

    @pkg_type.setter
    def pkg_type(self, pkg_type):
        """Sets the pkg_type of this FilterForListRepoImagePackageInput.


        :param pkg_type: The pkg_type of this FilterForListRepoImagePackageInput.  # noqa: E501
        :type: str
        """

        self._pkg_type = pkg_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRepoImagePackageInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRepoImagePackageInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRepoImagePackageInput):
            return True

        return self.to_dict() != other.to_dict()
