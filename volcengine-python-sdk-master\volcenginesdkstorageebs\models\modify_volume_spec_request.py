# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyVolumeSpecRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'target_volume_type': 'str',
        'volume_id': 'str'
    }

    attribute_map = {
        'target_volume_type': 'TargetVolumeType',
        'volume_id': 'VolumeId'
    }

    def __init__(self, target_volume_type=None, volume_id=None, _configuration=None):  # noqa: E501
        """ModifyVolumeSpecRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._target_volume_type = None
        self._volume_id = None
        self.discriminator = None

        self.target_volume_type = target_volume_type
        self.volume_id = volume_id

    @property
    def target_volume_type(self):
        """Gets the target_volume_type of this ModifyVolumeSpecRequest.  # noqa: E501


        :return: The target_volume_type of this ModifyVolumeSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._target_volume_type

    @target_volume_type.setter
    def target_volume_type(self, target_volume_type):
        """Sets the target_volume_type of this ModifyVolumeSpecRequest.


        :param target_volume_type: The target_volume_type of this ModifyVolumeSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and target_volume_type is None:
            raise ValueError("Invalid value for `target_volume_type`, must not be `None`")  # noqa: E501

        self._target_volume_type = target_volume_type

    @property
    def volume_id(self):
        """Gets the volume_id of this ModifyVolumeSpecRequest.  # noqa: E501


        :return: The volume_id of this ModifyVolumeSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_id

    @volume_id.setter
    def volume_id(self, volume_id):
        """Sets the volume_id of this ModifyVolumeSpecRequest.


        :param volume_id: The volume_id of this ModifyVolumeSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and volume_id is None:
            raise ValueError("Invalid value for `volume_id`, must not be `None`")  # noqa: E501

        self._volume_id = volume_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyVolumeSpecRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyVolumeSpecRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyVolumeSpecRequest):
            return True

        return self.to_dict() != other.to_dict()
