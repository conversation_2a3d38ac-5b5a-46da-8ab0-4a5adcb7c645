# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRaspProcessesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'cmdline': 'str',
        'exe_name': 'str',
        'hostname': 'str',
        'id': 'str',
        'inner_ip_list': 'list[str]',
        'inspect_port': 'int',
        'outer_ip_list': 'list[str]',
        'pid': 'str',
        'platform': 'str',
        'platform_family': 'str',
        'rasp_config_name': 'str',
        'reason': 'str',
        'related_config_id': 'str',
        'runtime': 'str',
        'start_time_unix': 'int',
        'status': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'cmdline': 'Cmdline',
        'exe_name': 'ExeName',
        'hostname': 'Hostname',
        'id': 'ID',
        'inner_ip_list': 'InnerIPList',
        'inspect_port': 'InspectPort',
        'outer_ip_list': 'OuterIPList',
        'pid': 'Pid',
        'platform': 'Platform',
        'platform_family': 'PlatformFamily',
        'rasp_config_name': 'RaspConfigName',
        'reason': 'Reason',
        'related_config_id': 'RelatedConfigID',
        'runtime': 'Runtime',
        'start_time_unix': 'StartTimeUnix',
        'status': 'Status'
    }

    def __init__(self, agent_id=None, agent_tags=None, cmdline=None, exe_name=None, hostname=None, id=None, inner_ip_list=None, inspect_port=None, outer_ip_list=None, pid=None, platform=None, platform_family=None, rasp_config_name=None, reason=None, related_config_id=None, runtime=None, start_time_unix=None, status=None, _configuration=None):  # noqa: E501
        """DataForListRaspProcessesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._cmdline = None
        self._exe_name = None
        self._hostname = None
        self._id = None
        self._inner_ip_list = None
        self._inspect_port = None
        self._outer_ip_list = None
        self._pid = None
        self._platform = None
        self._platform_family = None
        self._rasp_config_name = None
        self._reason = None
        self._related_config_id = None
        self._runtime = None
        self._start_time_unix = None
        self._status = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if cmdline is not None:
            self.cmdline = cmdline
        if exe_name is not None:
            self.exe_name = exe_name
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if inner_ip_list is not None:
            self.inner_ip_list = inner_ip_list
        if inspect_port is not None:
            self.inspect_port = inspect_port
        if outer_ip_list is not None:
            self.outer_ip_list = outer_ip_list
        if pid is not None:
            self.pid = pid
        if platform is not None:
            self.platform = platform
        if platform_family is not None:
            self.platform_family = platform_family
        if rasp_config_name is not None:
            self.rasp_config_name = rasp_config_name
        if reason is not None:
            self.reason = reason
        if related_config_id is not None:
            self.related_config_id = related_config_id
        if runtime is not None:
            self.runtime = runtime
        if start_time_unix is not None:
            self.start_time_unix = start_time_unix
        if status is not None:
            self.status = status

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The agent_id of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListRaspProcessesOutput.


        :param agent_id: The agent_id of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The agent_tags of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForListRaspProcessesOutput.


        :param agent_tags: The agent_tags of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def cmdline(self):
        """Gets the cmdline of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The cmdline of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this DataForListRaspProcessesOutput.


        :param cmdline: The cmdline of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def exe_name(self):
        """Gets the exe_name of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The exe_name of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe_name

    @exe_name.setter
    def exe_name(self, exe_name):
        """Sets the exe_name of this DataForListRaspProcessesOutput.


        :param exe_name: The exe_name of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._exe_name = exe_name

    @property
    def hostname(self):
        """Gets the hostname of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The hostname of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForListRaspProcessesOutput.


        :param hostname: The hostname of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The id of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListRaspProcessesOutput.


        :param id: The id of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def inner_ip_list(self):
        """Gets the inner_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The inner_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._inner_ip_list

    @inner_ip_list.setter
    def inner_ip_list(self, inner_ip_list):
        """Sets the inner_ip_list of this DataForListRaspProcessesOutput.


        :param inner_ip_list: The inner_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: list[str]
        """

        self._inner_ip_list = inner_ip_list

    @property
    def inspect_port(self):
        """Gets the inspect_port of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The inspect_port of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: int
        """
        return self._inspect_port

    @inspect_port.setter
    def inspect_port(self, inspect_port):
        """Sets the inspect_port of this DataForListRaspProcessesOutput.


        :param inspect_port: The inspect_port of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: int
        """

        self._inspect_port = inspect_port

    @property
    def outer_ip_list(self):
        """Gets the outer_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The outer_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._outer_ip_list

    @outer_ip_list.setter
    def outer_ip_list(self, outer_ip_list):
        """Sets the outer_ip_list of this DataForListRaspProcessesOutput.


        :param outer_ip_list: The outer_ip_list of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: list[str]
        """

        self._outer_ip_list = outer_ip_list

    @property
    def pid(self):
        """Gets the pid of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The pid of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForListRaspProcessesOutput.


        :param pid: The pid of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def platform(self):
        """Gets the platform of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The platform of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DataForListRaspProcessesOutput.


        :param platform: The platform of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def platform_family(self):
        """Gets the platform_family of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The platform_family of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform_family

    @platform_family.setter
    def platform_family(self, platform_family):
        """Sets the platform_family of this DataForListRaspProcessesOutput.


        :param platform_family: The platform_family of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._platform_family = platform_family

    @property
    def rasp_config_name(self):
        """Gets the rasp_config_name of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The rasp_config_name of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._rasp_config_name

    @rasp_config_name.setter
    def rasp_config_name(self, rasp_config_name):
        """Sets the rasp_config_name of this DataForListRaspProcessesOutput.


        :param rasp_config_name: The rasp_config_name of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._rasp_config_name = rasp_config_name

    @property
    def reason(self):
        """Gets the reason of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The reason of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this DataForListRaspProcessesOutput.


        :param reason: The reason of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def related_config_id(self):
        """Gets the related_config_id of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The related_config_id of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._related_config_id

    @related_config_id.setter
    def related_config_id(self, related_config_id):
        """Sets the related_config_id of this DataForListRaspProcessesOutput.


        :param related_config_id: The related_config_id of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._related_config_id = related_config_id

    @property
    def runtime(self):
        """Gets the runtime of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The runtime of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this DataForListRaspProcessesOutput.


        :param runtime: The runtime of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._runtime = runtime

    @property
    def start_time_unix(self):
        """Gets the start_time_unix of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The start_time_unix of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time_unix

    @start_time_unix.setter
    def start_time_unix(self, start_time_unix):
        """Sets the start_time_unix of this DataForListRaspProcessesOutput.


        :param start_time_unix: The start_time_unix of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: int
        """

        self._start_time_unix = start_time_unix

    @property
    def status(self):
        """Gets the status of this DataForListRaspProcessesOutput.  # noqa: E501


        :return: The status of this DataForListRaspProcessesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListRaspProcessesOutput.


        :param status: The status of this DataForListRaspProcessesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRaspProcessesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRaspProcessesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRaspProcessesOutput):
            return True

        return self.to_dict() != other.to_dict()
