# coding: utf-8

"""
    flink20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DependencyForListApplicationInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'jars': 'list[str]',
        'py_libraries': 'list[str]'
    }

    attribute_map = {
        'jars': 'Jars',
        'py_libraries': 'PyLibraries'
    }

    def __init__(self, jars=None, py_libraries=None, _configuration=None):  # noqa: E501
        """DependencyForListApplicationInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._jars = None
        self._py_libraries = None
        self.discriminator = None

        if jars is not None:
            self.jars = jars
        if py_libraries is not None:
            self.py_libraries = py_libraries

    @property
    def jars(self):
        """Gets the jars of this DependencyForListApplicationInstanceOutput.  # noqa: E501


        :return: The jars of this DependencyForListApplicationInstanceOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._jars

    @jars.setter
    def jars(self, jars):
        """Sets the jars of this DependencyForListApplicationInstanceOutput.


        :param jars: The jars of this DependencyForListApplicationInstanceOutput.  # noqa: E501
        :type: list[str]
        """

        self._jars = jars

    @property
    def py_libraries(self):
        """Gets the py_libraries of this DependencyForListApplicationInstanceOutput.  # noqa: E501


        :return: The py_libraries of this DependencyForListApplicationInstanceOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._py_libraries

    @py_libraries.setter
    def py_libraries(self, py_libraries):
        """Sets the py_libraries of this DependencyForListApplicationInstanceOutput.


        :param py_libraries: The py_libraries of this DependencyForListApplicationInstanceOutput.  # noqa: E501
        :type: list[str]
        """

        self._py_libraries = py_libraries

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DependencyForListApplicationInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DependencyForListApplicationInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DependencyForListApplicationInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
