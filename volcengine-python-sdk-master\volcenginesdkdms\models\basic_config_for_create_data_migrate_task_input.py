# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BasicConfigForCreateDataMigrateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'enable_range_check': 'bool',
        'failed_num_to_abort': 'int',
        'overwrite_policy': 'str',
        'source_type': 'str',
        'storage_class': 'str',
        'task_name': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'enable_range_check': 'EnableRangeCheck',
        'failed_num_to_abort': 'FailedNumToAbort',
        'overwrite_policy': 'OverwritePolicy',
        'source_type': 'SourceType',
        'storage_class': 'StorageClass',
        'task_name': 'TaskName'
    }

    def __init__(self, bandwidth=None, enable_range_check=None, failed_num_to_abort=None, overwrite_policy=None, source_type=None, storage_class=None, task_name=None, _configuration=None):  # noqa: E501
        """BasicConfigForCreateDataMigrateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._enable_range_check = None
        self._failed_num_to_abort = None
        self._overwrite_policy = None
        self._source_type = None
        self._storage_class = None
        self._task_name = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if enable_range_check is not None:
            self.enable_range_check = enable_range_check
        if failed_num_to_abort is not None:
            self.failed_num_to_abort = failed_num_to_abort
        if overwrite_policy is not None:
            self.overwrite_policy = overwrite_policy
        if source_type is not None:
            self.source_type = source_type
        if storage_class is not None:
            self.storage_class = storage_class
        if task_name is not None:
            self.task_name = task_name

    @property
    def bandwidth(self):
        """Gets the bandwidth of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The bandwidth of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this BasicConfigForCreateDataMigrateTaskInput.


        :param bandwidth: The bandwidth of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                bandwidth is not None and bandwidth > 1073741824):  # noqa: E501
            raise ValueError("Invalid value for `bandwidth`, must be a value less than or equal to `1073741824`")  # noqa: E501
        if (self._configuration.client_side_validation and
                bandwidth is not None and bandwidth < 1):  # noqa: E501
            raise ValueError("Invalid value for `bandwidth`, must be a value greater than or equal to `1`")  # noqa: E501

        self._bandwidth = bandwidth

    @property
    def enable_range_check(self):
        """Gets the enable_range_check of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The enable_range_check of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_range_check

    @enable_range_check.setter
    def enable_range_check(self, enable_range_check):
        """Sets the enable_range_check of this BasicConfigForCreateDataMigrateTaskInput.


        :param enable_range_check: The enable_range_check of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: bool
        """

        self._enable_range_check = enable_range_check

    @property
    def failed_num_to_abort(self):
        """Gets the failed_num_to_abort of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The failed_num_to_abort of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._failed_num_to_abort

    @failed_num_to_abort.setter
    def failed_num_to_abort(self, failed_num_to_abort):
        """Sets the failed_num_to_abort of this BasicConfigForCreateDataMigrateTaskInput.


        :param failed_num_to_abort: The failed_num_to_abort of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                failed_num_to_abort is not None and failed_num_to_abort < -1):  # noqa: E501
            raise ValueError("Invalid value for `failed_num_to_abort`, must be a value greater than or equal to `-1`")  # noqa: E501

        self._failed_num_to_abort = failed_num_to_abort

    @property
    def overwrite_policy(self):
        """Gets the overwrite_policy of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The overwrite_policy of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._overwrite_policy

    @overwrite_policy.setter
    def overwrite_policy(self, overwrite_policy):
        """Sets the overwrite_policy of this BasicConfigForCreateDataMigrateTaskInput.


        :param overwrite_policy: The overwrite_policy of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Force", "None", "LastModify"]  # noqa: E501
        if (self._configuration.client_side_validation and
                overwrite_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `overwrite_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(overwrite_policy, allowed_values)
            )

        self._overwrite_policy = overwrite_policy

    @property
    def source_type(self):
        """Gets the source_type of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The source_type of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this BasicConfigForCreateDataMigrateTaskInput.


        :param source_type: The source_type of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["StorageTypeObject", "StorageTypeUrl"]  # noqa: E501
        if (self._configuration.client_side_validation and
                source_type not in allowed_values):
            raise ValueError(
                "Invalid value for `source_type` ({0}), must be one of {1}"  # noqa: E501
                .format(source_type, allowed_values)
            )

        self._source_type = source_type

    @property
    def storage_class(self):
        """Gets the storage_class of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The storage_class of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._storage_class

    @storage_class.setter
    def storage_class(self, storage_class):
        """Sets the storage_class of this BasicConfigForCreateDataMigrateTaskInput.


        :param storage_class: The storage_class of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Standard", "InheritSource", "Ia", "ArchiveFr", "IntelligentTiering", "ColdArchive", "Archive", "DeepColdArchive"]  # noqa: E501
        if (self._configuration.client_side_validation and
                storage_class not in allowed_values):
            raise ValueError(
                "Invalid value for `storage_class` ({0}), must be one of {1}"  # noqa: E501
                .format(storage_class, allowed_values)
            )

        self._storage_class = storage_class

    @property
    def task_name(self):
        """Gets the task_name of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501


        :return: The task_name of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this BasicConfigForCreateDataMigrateTaskInput.


        :param task_name: The task_name of this BasicConfigForCreateDataMigrateTaskInput.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                task_name is not None and len(task_name) > 32):
            raise ValueError("Invalid value for `task_name`, length must be less than or equal to `32`")  # noqa: E501
        if (self._configuration.client_side_validation and
                task_name is not None and len(task_name) < 3):
            raise ValueError("Invalid value for `task_name`, length must be greater than or equal to `3`")  # noqa: E501

        self._task_name = task_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BasicConfigForCreateDataMigrateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BasicConfigForCreateDataMigrateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BasicConfigForCreateDataMigrateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
