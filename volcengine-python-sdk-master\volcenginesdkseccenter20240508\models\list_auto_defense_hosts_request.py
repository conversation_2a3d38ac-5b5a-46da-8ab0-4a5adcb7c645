# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAutoDefenseHostsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'rule_id': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'rule_id': 'RuleID',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id_list=None, leaf_group_ids=None, page_number=None, page_size=None, rule_id=None, sort_by=None, sort_order=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListAutoDefenseHostsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._rule_id = None
        self._sort_by = None
        self._sort_order = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if rule_id is not None:
            self.rule_id = rule_id
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The agent_id_list of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this ListAutoDefenseHostsRequest.


        :param agent_id_list: The agent_id_list of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListAutoDefenseHostsRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The page_number of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListAutoDefenseHostsRequest.


        :param page_number: The page_number of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The page_size of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAutoDefenseHostsRequest.


        :param page_size: The page_size of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def rule_id(self):
        """Gets the rule_id of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The rule_id of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this ListAutoDefenseHostsRequest.


        :param rule_id: The rule_id of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    @property
    def sort_by(self):
        """Gets the sort_by of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The sort_by of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListAutoDefenseHostsRequest.


        :param sort_by: The sort_by of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The sort_order of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListAutoDefenseHostsRequest.


        :param sort_order: The sort_order of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListAutoDefenseHostsRequest.  # noqa: E501


        :return: The top_group_id of this ListAutoDefenseHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListAutoDefenseHostsRequest.


        :param top_group_id: The top_group_id of this ListAutoDefenseHostsRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAutoDefenseHostsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAutoDefenseHostsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAutoDefenseHostsRequest):
            return True

        return self.to_dict() != other.to_dict()
