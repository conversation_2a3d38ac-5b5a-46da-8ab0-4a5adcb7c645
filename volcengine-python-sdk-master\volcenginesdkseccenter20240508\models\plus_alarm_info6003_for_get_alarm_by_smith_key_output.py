# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        '_class': 'str',
        'create_at': 'str',
        'file_hash': 'str',
        'modify_at': 'str',
        'name': 'str',
        'static_file': 'str',
        'timestamp': 'str',
        'types': 'str'
    }

    attribute_map = {
        '_class': 'Class',
        'create_at': 'CreateAt',
        'file_hash': 'FileHash',
        'modify_at': 'ModifyAt',
        'name': 'Name',
        'static_file': 'StaticFile',
        'timestamp': 'Timestamp',
        'types': 'Types'
    }

    def __init__(self, _class=None, create_at=None, file_hash=None, modify_at=None, name=None, static_file=None, timestamp=None, types=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self.__class = None
        self._create_at = None
        self._file_hash = None
        self._modify_at = None
        self._name = None
        self._static_file = None
        self._timestamp = None
        self._types = None
        self.discriminator = None

        if _class is not None:
            self._class = _class
        if create_at is not None:
            self.create_at = create_at
        if file_hash is not None:
            self.file_hash = file_hash
        if modify_at is not None:
            self.modify_at = modify_at
        if name is not None:
            self.name = name
        if static_file is not None:
            self.static_file = static_file
        if timestamp is not None:
            self.timestamp = timestamp
        if types is not None:
            self.types = types

    @property
    def _class(self):
        """Gets the _class of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The _class of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self.__class

    @_class.setter
    def _class(self, _class):
        """Sets the _class of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param _class: The _class of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self.__class = _class

    @property
    def create_at(self):
        """Gets the create_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The create_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param create_at: The create_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._create_at = create_at

    @property
    def file_hash(self):
        """Gets the file_hash of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The file_hash of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_hash

    @file_hash.setter
    def file_hash(self, file_hash):
        """Sets the file_hash of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param file_hash: The file_hash of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._file_hash = file_hash

    @property
    def modify_at(self):
        """Gets the modify_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The modify_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._modify_at

    @modify_at.setter
    def modify_at(self, modify_at):
        """Sets the modify_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param modify_at: The modify_at of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._modify_at = modify_at

    @property
    def name(self):
        """Gets the name of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The name of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param name: The name of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def static_file(self):
        """Gets the static_file of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The static_file of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._static_file

    @static_file.setter
    def static_file(self, static_file):
        """Sets the static_file of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param static_file: The static_file of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._static_file = static_file

    @property
    def timestamp(self):
        """Gets the timestamp of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The timestamp of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param timestamp: The timestamp of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._timestamp = timestamp

    @property
    def types(self):
        """Gets the types of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The types of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.


        :param types: The types of this PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._types = types

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo6003ForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
