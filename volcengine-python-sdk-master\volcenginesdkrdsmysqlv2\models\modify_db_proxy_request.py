# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBProxyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'convert_default_endpoint': 'bool',
        'enable_db_proxy': 'bool',
        'instance_id': 'str',
        'proxy_node_custom': 'ProxyNodeCustomForModifyDBProxyInput'
    }

    attribute_map = {
        'convert_default_endpoint': 'ConvertDefaultEndpoint',
        'enable_db_proxy': 'EnableDBProxy',
        'instance_id': 'InstanceId',
        'proxy_node_custom': 'ProxyNodeCustom'
    }

    def __init__(self, convert_default_endpoint=None, enable_db_proxy=None, instance_id=None, proxy_node_custom=None, _configuration=None):  # noqa: E501
        """ModifyDBProxyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._convert_default_endpoint = None
        self._enable_db_proxy = None
        self._instance_id = None
        self._proxy_node_custom = None
        self.discriminator = None

        if convert_default_endpoint is not None:
            self.convert_default_endpoint = convert_default_endpoint
        if enable_db_proxy is not None:
            self.enable_db_proxy = enable_db_proxy
        self.instance_id = instance_id
        if proxy_node_custom is not None:
            self.proxy_node_custom = proxy_node_custom

    @property
    def convert_default_endpoint(self):
        """Gets the convert_default_endpoint of this ModifyDBProxyRequest.  # noqa: E501


        :return: The convert_default_endpoint of this ModifyDBProxyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._convert_default_endpoint

    @convert_default_endpoint.setter
    def convert_default_endpoint(self, convert_default_endpoint):
        """Sets the convert_default_endpoint of this ModifyDBProxyRequest.


        :param convert_default_endpoint: The convert_default_endpoint of this ModifyDBProxyRequest.  # noqa: E501
        :type: bool
        """

        self._convert_default_endpoint = convert_default_endpoint

    @property
    def enable_db_proxy(self):
        """Gets the enable_db_proxy of this ModifyDBProxyRequest.  # noqa: E501


        :return: The enable_db_proxy of this ModifyDBProxyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_db_proxy

    @enable_db_proxy.setter
    def enable_db_proxy(self, enable_db_proxy):
        """Sets the enable_db_proxy of this ModifyDBProxyRequest.


        :param enable_db_proxy: The enable_db_proxy of this ModifyDBProxyRequest.  # noqa: E501
        :type: bool
        """

        self._enable_db_proxy = enable_db_proxy

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBProxyRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBProxyRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBProxyRequest.


        :param instance_id: The instance_id of this ModifyDBProxyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def proxy_node_custom(self):
        """Gets the proxy_node_custom of this ModifyDBProxyRequest.  # noqa: E501


        :return: The proxy_node_custom of this ModifyDBProxyRequest.  # noqa: E501
        :rtype: ProxyNodeCustomForModifyDBProxyInput
        """
        return self._proxy_node_custom

    @proxy_node_custom.setter
    def proxy_node_custom(self, proxy_node_custom):
        """Sets the proxy_node_custom of this ModifyDBProxyRequest.


        :param proxy_node_custom: The proxy_node_custom of this ModifyDBProxyRequest.  # noqa: E501
        :type: ProxyNodeCustomForModifyDBProxyInput
        """

        self._proxy_node_custom = proxy_node_custom

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBProxyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBProxyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBProxyRequest):
            return True

        return self.to_dict() != other.to_dict()
