# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetFingerprintCronOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'checksum': 'str',
        'command': 'str',
        'eip_address': 'str',
        'hostname': 'str',
        'id': 'str',
        'path': 'str',
        'primary_ip_address': 'str',
        'schedule': 'str',
        'start_time': 'int',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'checksum': 'Checksum',
        'command': 'Command',
        'eip_address': 'EipAddress',
        'hostname': 'Hostname',
        'id': 'ID',
        'path': 'Path',
        'primary_ip_address': 'PrimaryIpAddress',
        'schedule': 'Schedule',
        'start_time': 'StartTime',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, agent_id=None, agent_tags=None, checksum=None, command=None, eip_address=None, hostname=None, id=None, path=None, primary_ip_address=None, schedule=None, start_time=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetFingerprintCronOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._checksum = None
        self._command = None
        self._eip_address = None
        self._hostname = None
        self._id = None
        self._path = None
        self._primary_ip_address = None
        self._schedule = None
        self._start_time = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if checksum is not None:
            self.checksum = checksum
        if command is not None:
            self.command = command
        if eip_address is not None:
            self.eip_address = eip_address
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if path is not None:
            self.path = path
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if schedule is not None:
            self.schedule = schedule
        if start_time is not None:
            self.start_time = start_time
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The agent_id of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetFingerprintCronOutput.


        :param agent_id: The agent_id of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The agent_tags of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForGetFingerprintCronOutput.


        :param agent_tags: The agent_tags of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def checksum(self):
        """Gets the checksum of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The checksum of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._checksum

    @checksum.setter
    def checksum(self, checksum):
        """Sets the checksum of this DataForGetFingerprintCronOutput.


        :param checksum: The checksum of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._checksum = checksum

    @property
    def command(self):
        """Gets the command of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The command of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._command

    @command.setter
    def command(self, command):
        """Sets the command of this DataForGetFingerprintCronOutput.


        :param command: The command of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._command = command

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The eip_address of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForGetFingerprintCronOutput.


        :param eip_address: The eip_address of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The hostname of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetFingerprintCronOutput.


        :param hostname: The hostname of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The id of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetFingerprintCronOutput.


        :param id: The id of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def path(self):
        """Gets the path of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The path of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this DataForGetFingerprintCronOutput.


        :param path: The path of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForGetFingerprintCronOutput.


        :param primary_ip_address: The primary_ip_address of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def schedule(self):
        """Gets the schedule of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The schedule of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._schedule

    @schedule.setter
    def schedule(self, schedule):
        """Sets the schedule of this DataForGetFingerprintCronOutput.


        :param schedule: The schedule of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._schedule = schedule

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The start_time of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetFingerprintCronOutput.


        :param start_time: The start_time of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The update_time of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetFingerprintCronOutput.


        :param update_time: The update_time of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetFingerprintCronOutput.  # noqa: E501


        :return: The username of this DataForGetFingerprintCronOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetFingerprintCronOutput.


        :param username: The username of this DataForGetFingerprintCronOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetFingerprintCronOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetFingerprintCronOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetFingerprintCronOutput):
            return True

        return self.to_dict() != other.to_dict()
