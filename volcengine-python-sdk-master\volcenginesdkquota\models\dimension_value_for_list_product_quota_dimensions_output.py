# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DimensionValueForListProductQuotaDimensionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dimension_value_code': 'str',
        'dimension_value_name': 'str'
    }

    attribute_map = {
        'dimension_value_code': 'DimensionValueCode',
        'dimension_value_name': 'DimensionValueName'
    }

    def __init__(self, dimension_value_code=None, dimension_value_name=None, _configuration=None):  # noqa: E501
        """DimensionValueForListProductQuotaDimensionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dimension_value_code = None
        self._dimension_value_name = None
        self.discriminator = None

        if dimension_value_code is not None:
            self.dimension_value_code = dimension_value_code
        if dimension_value_name is not None:
            self.dimension_value_name = dimension_value_name

    @property
    def dimension_value_code(self):
        """Gets the dimension_value_code of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501


        :return: The dimension_value_code of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._dimension_value_code

    @dimension_value_code.setter
    def dimension_value_code(self, dimension_value_code):
        """Sets the dimension_value_code of this DimensionValueForListProductQuotaDimensionsOutput.


        :param dimension_value_code: The dimension_value_code of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501
        :type: str
        """

        self._dimension_value_code = dimension_value_code

    @property
    def dimension_value_name(self):
        """Gets the dimension_value_name of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501


        :return: The dimension_value_name of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._dimension_value_name

    @dimension_value_name.setter
    def dimension_value_name(self, dimension_value_name):
        """Sets the dimension_value_name of this DimensionValueForListProductQuotaDimensionsOutput.


        :param dimension_value_name: The dimension_value_name of this DimensionValueForListProductQuotaDimensionsOutput.  # noqa: E501
        :type: str
        """

        self._dimension_value_name = dimension_value_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DimensionValueForListProductQuotaDimensionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DimensionValueForListProductQuotaDimensionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DimensionValueForListProductQuotaDimensionsOutput):
            return True

        return self.to_dict() != other.to_dict()
