# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnAccessLogResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'domain_log_details': 'list[DomainLogDetailForDescribeCdnAccessLogOutput]',
        'page_num': 'int',
        'page_size': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'domain': 'Domain',
        'domain_log_details': 'DomainLogDetails',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'total_count': 'TotalCount'
    }

    def __init__(self, domain=None, domain_log_details=None, page_num=None, page_size=None, total_count=None, _configuration=None):  # noqa: E501
        """DescribeCdnAccessLogResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._domain_log_details = None
        self._page_num = None
        self._page_size = None
        self._total_count = None
        self.discriminator = None

        if domain is not None:
            self.domain = domain
        if domain_log_details is not None:
            self.domain_log_details = domain_log_details
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if total_count is not None:
            self.total_count = total_count

    @property
    def domain(self):
        """Gets the domain of this DescribeCdnAccessLogResponse.  # noqa: E501


        :return: The domain of this DescribeCdnAccessLogResponse.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeCdnAccessLogResponse.


        :param domain: The domain of this DescribeCdnAccessLogResponse.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def domain_log_details(self):
        """Gets the domain_log_details of this DescribeCdnAccessLogResponse.  # noqa: E501


        :return: The domain_log_details of this DescribeCdnAccessLogResponse.  # noqa: E501
        :rtype: list[DomainLogDetailForDescribeCdnAccessLogOutput]
        """
        return self._domain_log_details

    @domain_log_details.setter
    def domain_log_details(self, domain_log_details):
        """Sets the domain_log_details of this DescribeCdnAccessLogResponse.


        :param domain_log_details: The domain_log_details of this DescribeCdnAccessLogResponse.  # noqa: E501
        :type: list[DomainLogDetailForDescribeCdnAccessLogOutput]
        """

        self._domain_log_details = domain_log_details

    @property
    def page_num(self):
        """Gets the page_num of this DescribeCdnAccessLogResponse.  # noqa: E501


        :return: The page_num of this DescribeCdnAccessLogResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this DescribeCdnAccessLogResponse.


        :param page_num: The page_num of this DescribeCdnAccessLogResponse.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this DescribeCdnAccessLogResponse.  # noqa: E501


        :return: The page_size of this DescribeCdnAccessLogResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeCdnAccessLogResponse.


        :param page_size: The page_size of this DescribeCdnAccessLogResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def total_count(self):
        """Gets the total_count of this DescribeCdnAccessLogResponse.  # noqa: E501


        :return: The total_count of this DescribeCdnAccessLogResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this DescribeCdnAccessLogResponse.


        :param total_count: The total_count of this DescribeCdnAccessLogResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnAccessLogResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnAccessLogResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnAccessLogResponse):
            return True

        return self.to_dict() != other.to_dict()
