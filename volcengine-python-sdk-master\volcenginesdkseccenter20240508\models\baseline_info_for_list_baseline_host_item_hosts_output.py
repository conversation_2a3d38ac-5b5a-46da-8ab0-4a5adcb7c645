# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaselineInfoForListBaselineHostItemHostsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'asset_id': 'str',
        'asset_name': 'str',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'detect_status': 'str',
        'eip_address': 'str',
        'error_detail': 'str',
        'hostname': 'str',
        'node_id': 'str',
        'node_ip': 'str',
        'node_name': 'str',
        'pass_num': 'int',
        'primary_ip_address': 'str',
        'region': 'str',
        'risk_num': 'RiskNumForListBaselineHostItemHostsOutput'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'asset_id': 'AssetID',
        'asset_name': 'AssetName',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'detect_status': 'DetectStatus',
        'eip_address': 'EipAddress',
        'error_detail': 'ErrorDetail',
        'hostname': 'Hostname',
        'node_id': 'NodeID',
        'node_ip': 'NodeIP',
        'node_name': 'NodeName',
        'pass_num': 'PassNum',
        'primary_ip_address': 'PrimaryIpAddress',
        'region': 'Region',
        'risk_num': 'RiskNum'
    }

    def __init__(self, agent_id=None, agent_tags=None, asset_id=None, asset_name=None, cluster_id=None, cluster_name=None, detect_status=None, eip_address=None, error_detail=None, hostname=None, node_id=None, node_ip=None, node_name=None, pass_num=None, primary_ip_address=None, region=None, risk_num=None, _configuration=None):  # noqa: E501
        """BaselineInfoForListBaselineHostItemHostsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._asset_id = None
        self._asset_name = None
        self._cluster_id = None
        self._cluster_name = None
        self._detect_status = None
        self._eip_address = None
        self._error_detail = None
        self._hostname = None
        self._node_id = None
        self._node_ip = None
        self._node_name = None
        self._pass_num = None
        self._primary_ip_address = None
        self._region = None
        self._risk_num = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if detect_status is not None:
            self.detect_status = detect_status
        if eip_address is not None:
            self.eip_address = eip_address
        if error_detail is not None:
            self.error_detail = error_detail
        if hostname is not None:
            self.hostname = hostname
        if node_id is not None:
            self.node_id = node_id
        if node_ip is not None:
            self.node_ip = node_ip
        if node_name is not None:
            self.node_name = node_name
        if pass_num is not None:
            self.pass_num = pass_num
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if region is not None:
            self.region = region
        if risk_num is not None:
            self.risk_num = risk_num

    @property
    def agent_id(self):
        """Gets the agent_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The agent_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param agent_id: The agent_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The agent_tags of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param agent_tags: The agent_tags of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def asset_id(self):
        """Gets the asset_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The asset_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param asset_id: The asset_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The asset_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param asset_name: The asset_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def cluster_id(self):
        """Gets the cluster_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The cluster_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param cluster_id: The cluster_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The cluster_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param cluster_name: The cluster_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def detect_status(self):
        """Gets the detect_status of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The detect_status of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_status

    @detect_status.setter
    def detect_status(self, detect_status):
        """Sets the detect_status of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param detect_status: The detect_status of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._detect_status = detect_status

    @property
    def eip_address(self):
        """Gets the eip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The eip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param eip_address: The eip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def error_detail(self):
        """Gets the error_detail of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The error_detail of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_detail

    @error_detail.setter
    def error_detail(self, error_detail):
        """Sets the error_detail of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param error_detail: The error_detail of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._error_detail = error_detail

    @property
    def hostname(self):
        """Gets the hostname of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The hostname of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param hostname: The hostname of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def node_id(self):
        """Gets the node_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The node_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param node_id: The node_id of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def node_ip(self):
        """Gets the node_ip of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The node_ip of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_ip

    @node_ip.setter
    def node_ip(self, node_ip):
        """Sets the node_ip of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param node_ip: The node_ip of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_ip = node_ip

    @property
    def node_name(self):
        """Gets the node_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The node_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param node_name: The node_name of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def pass_num(self):
        """Gets the pass_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The pass_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._pass_num

    @pass_num.setter
    def pass_num(self, pass_num):
        """Sets the pass_num of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param pass_num: The pass_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: int
        """

        self._pass_num = pass_num

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The primary_ip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param primary_ip_address: The primary_ip_address of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def region(self):
        """Gets the region of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The region of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param region: The region of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def risk_num(self):
        """Gets the risk_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501


        :return: The risk_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :rtype: RiskNumForListBaselineHostItemHostsOutput
        """
        return self._risk_num

    @risk_num.setter
    def risk_num(self, risk_num):
        """Sets the risk_num of this BaselineInfoForListBaselineHostItemHostsOutput.


        :param risk_num: The risk_num of this BaselineInfoForListBaselineHostItemHostsOutput.  # noqa: E501
        :type: RiskNumForListBaselineHostItemHostsOutput
        """

        self._risk_num = risk_num

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaselineInfoForListBaselineHostItemHostsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaselineInfoForListBaselineHostItemHostsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaselineInfoForListBaselineHostItemHostsOutput):
            return True

        return self.to_dict() != other.to_dict()
