# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EcsInfoForDescribeDiagnosticsInfosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ecs_instance_id': 'str',
        'ecs_instance_name': 'str',
        'ecs_private_ipv4_address': 'str',
        'ecs_private_ipv6_address': 'list[str]',
        'ecs_vpc_id': 'str',
        'ecs_vpc_name': 'str'
    }

    attribute_map = {
        'ecs_instance_id': 'EcsInstanceId',
        'ecs_instance_name': 'EcsInstanceName',
        'ecs_private_ipv4_address': 'EcsPrivateIpv4Address',
        'ecs_private_ipv6_address': 'EcsPrivateIpv6Address',
        'ecs_vpc_id': 'EcsVpcId',
        'ecs_vpc_name': 'EcsVpcName'
    }

    def __init__(self, ecs_instance_id=None, ecs_instance_name=None, ecs_private_ipv4_address=None, ecs_private_ipv6_address=None, ecs_vpc_id=None, ecs_vpc_name=None, _configuration=None):  # noqa: E501
        """EcsInfoForDescribeDiagnosticsInfosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ecs_instance_id = None
        self._ecs_instance_name = None
        self._ecs_private_ipv4_address = None
        self._ecs_private_ipv6_address = None
        self._ecs_vpc_id = None
        self._ecs_vpc_name = None
        self.discriminator = None

        if ecs_instance_id is not None:
            self.ecs_instance_id = ecs_instance_id
        if ecs_instance_name is not None:
            self.ecs_instance_name = ecs_instance_name
        if ecs_private_ipv4_address is not None:
            self.ecs_private_ipv4_address = ecs_private_ipv4_address
        if ecs_private_ipv6_address is not None:
            self.ecs_private_ipv6_address = ecs_private_ipv6_address
        if ecs_vpc_id is not None:
            self.ecs_vpc_id = ecs_vpc_id
        if ecs_vpc_name is not None:
            self.ecs_vpc_name = ecs_vpc_name

    @property
    def ecs_instance_id(self):
        """Gets the ecs_instance_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_instance_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_instance_id

    @ecs_instance_id.setter
    def ecs_instance_id(self, ecs_instance_id):
        """Sets the ecs_instance_id of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_instance_id: The ecs_instance_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._ecs_instance_id = ecs_instance_id

    @property
    def ecs_instance_name(self):
        """Gets the ecs_instance_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_instance_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_instance_name

    @ecs_instance_name.setter
    def ecs_instance_name(self, ecs_instance_name):
        """Sets the ecs_instance_name of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_instance_name: The ecs_instance_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._ecs_instance_name = ecs_instance_name

    @property
    def ecs_private_ipv4_address(self):
        """Gets the ecs_private_ipv4_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_private_ipv4_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_private_ipv4_address

    @ecs_private_ipv4_address.setter
    def ecs_private_ipv4_address(self, ecs_private_ipv4_address):
        """Sets the ecs_private_ipv4_address of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_private_ipv4_address: The ecs_private_ipv4_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._ecs_private_ipv4_address = ecs_private_ipv4_address

    @property
    def ecs_private_ipv6_address(self):
        """Gets the ecs_private_ipv6_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_private_ipv6_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ecs_private_ipv6_address

    @ecs_private_ipv6_address.setter
    def ecs_private_ipv6_address(self, ecs_private_ipv6_address):
        """Sets the ecs_private_ipv6_address of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_private_ipv6_address: The ecs_private_ipv6_address of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: list[str]
        """

        self._ecs_private_ipv6_address = ecs_private_ipv6_address

    @property
    def ecs_vpc_id(self):
        """Gets the ecs_vpc_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_vpc_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_vpc_id

    @ecs_vpc_id.setter
    def ecs_vpc_id(self, ecs_vpc_id):
        """Sets the ecs_vpc_id of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_vpc_id: The ecs_vpc_id of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._ecs_vpc_id = ecs_vpc_id

    @property
    def ecs_vpc_name(self):
        """Gets the ecs_vpc_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501


        :return: The ecs_vpc_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_vpc_name

    @ecs_vpc_name.setter
    def ecs_vpc_name(self, ecs_vpc_name):
        """Sets the ecs_vpc_name of this EcsInfoForDescribeDiagnosticsInfosOutput.


        :param ecs_vpc_name: The ecs_vpc_name of this EcsInfoForDescribeDiagnosticsInfosOutput.  # noqa: E501
        :type: str
        """

        self._ecs_vpc_name = ecs_vpc_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EcsInfoForDescribeDiagnosticsInfosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EcsInfoForDescribeDiagnosticsInfosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EcsInfoForDescribeDiagnosticsInfosOutput):
            return True

        return self.to_dict() != other.to_dict()
