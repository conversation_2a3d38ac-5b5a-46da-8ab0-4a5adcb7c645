# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConstructionInfoForGetDXPInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'construction_end_date': 'str',
        'construction_start_date': 'str',
        'contact_name': 'str',
        'contact_phone': 'str',
        'device_info': 'str',
        'idc_addr': 'str',
        'odf_info': 'str'
    }

    attribute_map = {
        'construction_end_date': 'ConstructionEndDate',
        'construction_start_date': 'ConstructionStartDate',
        'contact_name': 'ContactName',
        'contact_phone': 'ContactPhone',
        'device_info': 'DeviceInfo',
        'idc_addr': 'IDCAddr',
        'odf_info': 'ODFInfo'
    }

    def __init__(self, construction_end_date=None, construction_start_date=None, contact_name=None, contact_phone=None, device_info=None, idc_addr=None, odf_info=None, _configuration=None):  # noqa: E501
        """ConstructionInfoForGetDXPInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._construction_end_date = None
        self._construction_start_date = None
        self._contact_name = None
        self._contact_phone = None
        self._device_info = None
        self._idc_addr = None
        self._odf_info = None
        self.discriminator = None

        if construction_end_date is not None:
            self.construction_end_date = construction_end_date
        if construction_start_date is not None:
            self.construction_start_date = construction_start_date
        if contact_name is not None:
            self.contact_name = contact_name
        if contact_phone is not None:
            self.contact_phone = contact_phone
        if device_info is not None:
            self.device_info = device_info
        if idc_addr is not None:
            self.idc_addr = idc_addr
        if odf_info is not None:
            self.odf_info = odf_info

    @property
    def construction_end_date(self):
        """Gets the construction_end_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The construction_end_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._construction_end_date

    @construction_end_date.setter
    def construction_end_date(self, construction_end_date):
        """Sets the construction_end_date of this ConstructionInfoForGetDXPInstanceOutput.


        :param construction_end_date: The construction_end_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._construction_end_date = construction_end_date

    @property
    def construction_start_date(self):
        """Gets the construction_start_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The construction_start_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._construction_start_date

    @construction_start_date.setter
    def construction_start_date(self, construction_start_date):
        """Sets the construction_start_date of this ConstructionInfoForGetDXPInstanceOutput.


        :param construction_start_date: The construction_start_date of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._construction_start_date = construction_start_date

    @property
    def contact_name(self):
        """Gets the contact_name of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The contact_name of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._contact_name

    @contact_name.setter
    def contact_name(self, contact_name):
        """Sets the contact_name of this ConstructionInfoForGetDXPInstanceOutput.


        :param contact_name: The contact_name of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._contact_name = contact_name

    @property
    def contact_phone(self):
        """Gets the contact_phone of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The contact_phone of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._contact_phone

    @contact_phone.setter
    def contact_phone(self, contact_phone):
        """Sets the contact_phone of this ConstructionInfoForGetDXPInstanceOutput.


        :param contact_phone: The contact_phone of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._contact_phone = contact_phone

    @property
    def device_info(self):
        """Gets the device_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The device_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_info

    @device_info.setter
    def device_info(self, device_info):
        """Sets the device_info of this ConstructionInfoForGetDXPInstanceOutput.


        :param device_info: The device_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._device_info = device_info

    @property
    def idc_addr(self):
        """Gets the idc_addr of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The idc_addr of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._idc_addr

    @idc_addr.setter
    def idc_addr(self, idc_addr):
        """Sets the idc_addr of this ConstructionInfoForGetDXPInstanceOutput.


        :param idc_addr: The idc_addr of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._idc_addr = idc_addr

    @property
    def odf_info(self):
        """Gets the odf_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501


        :return: The odf_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._odf_info

    @odf_info.setter
    def odf_info(self, odf_info):
        """Sets the odf_info of this ConstructionInfoForGetDXPInstanceOutput.


        :param odf_info: The odf_info of this ConstructionInfoForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._odf_info = odf_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConstructionInfoForGetDXPInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConstructionInfoForGetDXPInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConstructionInfoForGetDXPInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
