# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TargetInfoForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'city': 'str',
        'detect_url': 'str',
        'ip': 'str',
        'ipv6': 'str',
        'isp': 'str',
        'region': 'str'
    }

    attribute_map = {
        'city': 'City',
        'detect_url': 'DetectURL',
        'ip': 'IP',
        'ipv6': 'Ipv6',
        'isp': 'Isp',
        'region': 'Region'
    }

    def __init__(self, city=None, detect_url=None, ip=None, ipv6=None, isp=None, region=None, _configuration=None):  # noqa: E501
        """TargetInfoForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._city = None
        self._detect_url = None
        self._ip = None
        self._ipv6 = None
        self._isp = None
        self._region = None
        self.discriminator = None

        if city is not None:
            self.city = city
        if detect_url is not None:
            self.detect_url = detect_url
        if ip is not None:
            self.ip = ip
        if ipv6 is not None:
            self.ipv6 = ipv6
        if isp is not None:
            self.isp = isp
        if region is not None:
            self.region = region

    @property
    def city(self):
        """Gets the city of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The city of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this TargetInfoForGetTaskResultOutput.


        :param city: The city of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def detect_url(self):
        """Gets the detect_url of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The detect_url of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_url

    @detect_url.setter
    def detect_url(self, detect_url):
        """Sets the detect_url of this TargetInfoForGetTaskResultOutput.


        :param detect_url: The detect_url of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._detect_url = detect_url

    @property
    def ip(self):
        """Gets the ip of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The ip of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this TargetInfoForGetTaskResultOutput.


        :param ip: The ip of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def ipv6(self):
        """Gets the ipv6 of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The ipv6 of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this TargetInfoForGetTaskResultOutput.


        :param ipv6: The ipv6 of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._ipv6 = ipv6

    @property
    def isp(self):
        """Gets the isp of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The isp of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this TargetInfoForGetTaskResultOutput.


        :param isp: The isp of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def region(self):
        """Gets the region of this TargetInfoForGetTaskResultOutput.  # noqa: E501


        :return: The region of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this TargetInfoForGetTaskResultOutput.


        :param region: The region of this TargetInfoForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TargetInfoForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TargetInfoForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TargetInfoForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
