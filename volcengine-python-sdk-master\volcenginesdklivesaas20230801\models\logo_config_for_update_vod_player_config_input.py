# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LogoConfigForUpdateVodPlayerConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_logo_enable': 'int',
        'logo_position': 'int',
        'logo_url': 'str'
    }

    attribute_map = {
        'is_logo_enable': 'IsLogoEnable',
        'logo_position': 'LogoPosition',
        'logo_url': 'LogoUrl'
    }

    def __init__(self, is_logo_enable=None, logo_position=None, logo_url=None, _configuration=None):  # noqa: E501
        """LogoConfigForUpdateVodPlayerConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_logo_enable = None
        self._logo_position = None
        self._logo_url = None
        self.discriminator = None

        if is_logo_enable is not None:
            self.is_logo_enable = is_logo_enable
        if logo_position is not None:
            self.logo_position = logo_position
        if logo_url is not None:
            self.logo_url = logo_url

    @property
    def is_logo_enable(self):
        """Gets the is_logo_enable of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The is_logo_enable of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_logo_enable

    @is_logo_enable.setter
    def is_logo_enable(self, is_logo_enable):
        """Sets the is_logo_enable of this LogoConfigForUpdateVodPlayerConfigInput.


        :param is_logo_enable: The is_logo_enable of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._is_logo_enable = is_logo_enable

    @property
    def logo_position(self):
        """Gets the logo_position of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The logo_position of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._logo_position

    @logo_position.setter
    def logo_position(self, logo_position):
        """Sets the logo_position of this LogoConfigForUpdateVodPlayerConfigInput.


        :param logo_position: The logo_position of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._logo_position = logo_position

    @property
    def logo_url(self):
        """Gets the logo_url of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The logo_url of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._logo_url

    @logo_url.setter
    def logo_url(self, logo_url):
        """Sets the logo_url of this LogoConfigForUpdateVodPlayerConfigInput.


        :param logo_url: The logo_url of this LogoConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: str
        """

        self._logo_url = logo_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LogoConfigForUpdateVodPlayerConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LogoConfigForUpdateVodPlayerConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LogoConfigForUpdateVodPlayerConfigInput):
            return True

        return self.to_dict() != other.to_dict()
