# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebAtkTopUrlResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attack_count': 'int',
        'host': 'str',
        'path': 'str',
        'percentage': 'float'
    }

    attribute_map = {
        'attack_count': 'AttackCount',
        'host': 'Host',
        'path': 'Path',
        'percentage': 'Percentage'
    }

    def __init__(self, attack_count=None, host=None, path=None, percentage=None, _configuration=None):  # noqa: E501
        """DescWebAtkTopUrlResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attack_count = None
        self._host = None
        self._path = None
        self._percentage = None
        self.discriminator = None

        if attack_count is not None:
            self.attack_count = attack_count
        if host is not None:
            self.host = host
        if path is not None:
            self.path = path
        if percentage is not None:
            self.percentage = percentage

    @property
    def attack_count(self):
        """Gets the attack_count of this DescWebAtkTopUrlResponse.  # noqa: E501


        :return: The attack_count of this DescWebAtkTopUrlResponse.  # noqa: E501
        :rtype: int
        """
        return self._attack_count

    @attack_count.setter
    def attack_count(self, attack_count):
        """Sets the attack_count of this DescWebAtkTopUrlResponse.


        :param attack_count: The attack_count of this DescWebAtkTopUrlResponse.  # noqa: E501
        :type: int
        """

        self._attack_count = attack_count

    @property
    def host(self):
        """Gets the host of this DescWebAtkTopUrlResponse.  # noqa: E501


        :return: The host of this DescWebAtkTopUrlResponse.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this DescWebAtkTopUrlResponse.


        :param host: The host of this DescWebAtkTopUrlResponse.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def path(self):
        """Gets the path of this DescWebAtkTopUrlResponse.  # noqa: E501


        :return: The path of this DescWebAtkTopUrlResponse.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this DescWebAtkTopUrlResponse.


        :param path: The path of this DescWebAtkTopUrlResponse.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def percentage(self):
        """Gets the percentage of this DescWebAtkTopUrlResponse.  # noqa: E501


        :return: The percentage of this DescWebAtkTopUrlResponse.  # noqa: E501
        :rtype: float
        """
        return self._percentage

    @percentage.setter
    def percentage(self, percentage):
        """Sets the percentage of this DescWebAtkTopUrlResponse.


        :param percentage: The percentage of this DescWebAtkTopUrlResponse.  # noqa: E501
        :type: float
        """

        self._percentage = percentage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebAtkTopUrlResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebAtkTopUrlResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebAtkTopUrlResponse):
            return True

        return self.to_dict() != other.to_dict()
