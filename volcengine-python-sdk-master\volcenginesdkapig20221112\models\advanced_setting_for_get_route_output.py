# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AdvancedSettingForGetRouteOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cors_policy_setting': 'CorsPolicySettingForGetRouteOutput',
        'header_operations': 'list[HeaderOperationForGetRouteOutput]',
        'mirror_policies': 'list[MirrorPolicyForGetRouteOutput]',
        'retry_policy_setting': 'RetryPolicySettingForGetRouteOutput',
        'timeout_setting': 'TimeoutSettingForGetRouteOutput',
        'url_rewrite_setting': 'URLRewriteSettingForGetRouteOutput'
    }

    attribute_map = {
        'cors_policy_setting': 'CorsPolicySetting',
        'header_operations': 'HeaderOperations',
        'mirror_policies': 'MirrorPolicies',
        'retry_policy_setting': 'RetryPolicySetting',
        'timeout_setting': 'TimeoutSetting',
        'url_rewrite_setting': 'URLRewriteSetting'
    }

    def __init__(self, cors_policy_setting=None, header_operations=None, mirror_policies=None, retry_policy_setting=None, timeout_setting=None, url_rewrite_setting=None, _configuration=None):  # noqa: E501
        """AdvancedSettingForGetRouteOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cors_policy_setting = None
        self._header_operations = None
        self._mirror_policies = None
        self._retry_policy_setting = None
        self._timeout_setting = None
        self._url_rewrite_setting = None
        self.discriminator = None

        if cors_policy_setting is not None:
            self.cors_policy_setting = cors_policy_setting
        if header_operations is not None:
            self.header_operations = header_operations
        if mirror_policies is not None:
            self.mirror_policies = mirror_policies
        if retry_policy_setting is not None:
            self.retry_policy_setting = retry_policy_setting
        if timeout_setting is not None:
            self.timeout_setting = timeout_setting
        if url_rewrite_setting is not None:
            self.url_rewrite_setting = url_rewrite_setting

    @property
    def cors_policy_setting(self):
        """Gets the cors_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The cors_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: CorsPolicySettingForGetRouteOutput
        """
        return self._cors_policy_setting

    @cors_policy_setting.setter
    def cors_policy_setting(self, cors_policy_setting):
        """Sets the cors_policy_setting of this AdvancedSettingForGetRouteOutput.


        :param cors_policy_setting: The cors_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: CorsPolicySettingForGetRouteOutput
        """

        self._cors_policy_setting = cors_policy_setting

    @property
    def header_operations(self):
        """Gets the header_operations of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The header_operations of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: list[HeaderOperationForGetRouteOutput]
        """
        return self._header_operations

    @header_operations.setter
    def header_operations(self, header_operations):
        """Sets the header_operations of this AdvancedSettingForGetRouteOutput.


        :param header_operations: The header_operations of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: list[HeaderOperationForGetRouteOutput]
        """

        self._header_operations = header_operations

    @property
    def mirror_policies(self):
        """Gets the mirror_policies of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The mirror_policies of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: list[MirrorPolicyForGetRouteOutput]
        """
        return self._mirror_policies

    @mirror_policies.setter
    def mirror_policies(self, mirror_policies):
        """Sets the mirror_policies of this AdvancedSettingForGetRouteOutput.


        :param mirror_policies: The mirror_policies of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: list[MirrorPolicyForGetRouteOutput]
        """

        self._mirror_policies = mirror_policies

    @property
    def retry_policy_setting(self):
        """Gets the retry_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The retry_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: RetryPolicySettingForGetRouteOutput
        """
        return self._retry_policy_setting

    @retry_policy_setting.setter
    def retry_policy_setting(self, retry_policy_setting):
        """Sets the retry_policy_setting of this AdvancedSettingForGetRouteOutput.


        :param retry_policy_setting: The retry_policy_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: RetryPolicySettingForGetRouteOutput
        """

        self._retry_policy_setting = retry_policy_setting

    @property
    def timeout_setting(self):
        """Gets the timeout_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The timeout_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: TimeoutSettingForGetRouteOutput
        """
        return self._timeout_setting

    @timeout_setting.setter
    def timeout_setting(self, timeout_setting):
        """Sets the timeout_setting of this AdvancedSettingForGetRouteOutput.


        :param timeout_setting: The timeout_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: TimeoutSettingForGetRouteOutput
        """

        self._timeout_setting = timeout_setting

    @property
    def url_rewrite_setting(self):
        """Gets the url_rewrite_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501


        :return: The url_rewrite_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :rtype: URLRewriteSettingForGetRouteOutput
        """
        return self._url_rewrite_setting

    @url_rewrite_setting.setter
    def url_rewrite_setting(self, url_rewrite_setting):
        """Sets the url_rewrite_setting of this AdvancedSettingForGetRouteOutput.


        :param url_rewrite_setting: The url_rewrite_setting of this AdvancedSettingForGetRouteOutput.  # noqa: E501
        :type: URLRewriteSettingForGetRouteOutput
        """

        self._url_rewrite_setting = url_rewrite_setting

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AdvancedSettingForGetRouteOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AdvancedSettingForGetRouteOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AdvancedSettingForGetRouteOutput):
            return True

        return self.to_dict() != other.to_dict()
