# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListJobInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'created_by': 'str',
        'debug_server': 'DebugServerForListJobInstancesOutput',
        'description': 'str',
        'id': 'str',
        'index': 'str',
        'ips': 'IpsForListJobInstancesOutput',
        'name': 'str',
        'resource_claim': 'ResourceClaimForListJobInstancesOutput',
        'status': 'StatusForListJobInstancesOutput',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'created_by': 'CreatedBy',
        'debug_server': 'DebugServer',
        'description': 'Description',
        'id': 'Id',
        'index': 'Index',
        'ips': 'Ips',
        'name': 'Name',
        'resource_claim': 'ResourceClaim',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, created_by=None, debug_server=None, description=None, id=None, index=None, ips=None, name=None, resource_claim=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListJobInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._created_by = None
        self._debug_server = None
        self._description = None
        self._id = None
        self._index = None
        self._ips = None
        self._name = None
        self._resource_claim = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if created_by is not None:
            self.created_by = created_by
        if debug_server is not None:
            self.debug_server = debug_server
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if index is not None:
            self.index = index
        if ips is not None:
            self.ips = ips
        if name is not None:
            self.name = name
        if resource_claim is not None:
            self.resource_claim = resource_claim
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The create_time of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListJobInstancesOutput.


        :param create_time: The create_time of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def created_by(self):
        """Gets the created_by of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The created_by of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this ItemForListJobInstancesOutput.


        :param created_by: The created_by of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def debug_server(self):
        """Gets the debug_server of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The debug_server of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: DebugServerForListJobInstancesOutput
        """
        return self._debug_server

    @debug_server.setter
    def debug_server(self, debug_server):
        """Sets the debug_server of this ItemForListJobInstancesOutput.


        :param debug_server: The debug_server of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: DebugServerForListJobInstancesOutput
        """

        self._debug_server = debug_server

    @property
    def description(self):
        """Gets the description of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The description of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListJobInstancesOutput.


        :param description: The description of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The id of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListJobInstancesOutput.


        :param id: The id of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def index(self):
        """Gets the index of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The index of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this ItemForListJobInstancesOutput.


        :param index: The index of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._index = index

    @property
    def ips(self):
        """Gets the ips of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The ips of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: IpsForListJobInstancesOutput
        """
        return self._ips

    @ips.setter
    def ips(self, ips):
        """Sets the ips of this ItemForListJobInstancesOutput.


        :param ips: The ips of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: IpsForListJobInstancesOutput
        """

        self._ips = ips

    @property
    def name(self):
        """Gets the name of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The name of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListJobInstancesOutput.


        :param name: The name of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def resource_claim(self):
        """Gets the resource_claim of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The resource_claim of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: ResourceClaimForListJobInstancesOutput
        """
        return self._resource_claim

    @resource_claim.setter
    def resource_claim(self, resource_claim):
        """Sets the resource_claim of this ItemForListJobInstancesOutput.


        :param resource_claim: The resource_claim of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: ResourceClaimForListJobInstancesOutput
        """

        self._resource_claim = resource_claim

    @property
    def status(self):
        """Gets the status of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The status of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: StatusForListJobInstancesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListJobInstancesOutput.


        :param status: The status of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: StatusForListJobInstancesOutput
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListJobInstancesOutput.  # noqa: E501


        :return: The update_time of this ItemForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListJobInstancesOutput.


        :param update_time: The update_time of this ItemForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListJobInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListJobInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListJobInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
