# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsistentHashConfigForGetServiceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hash_key_type': 'str',
        'http_header_name': 'str'
    }

    attribute_map = {
        'hash_key_type': 'HashKeyType',
        'http_header_name': 'HttpHeaderName'
    }

    def __init__(self, hash_key_type=None, http_header_name=None, _configuration=None):  # noqa: E501
        """ConsistentHashConfigForGetServiceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hash_key_type = None
        self._http_header_name = None
        self.discriminator = None

        if hash_key_type is not None:
            self.hash_key_type = hash_key_type
        if http_header_name is not None:
            self.http_header_name = http_header_name

    @property
    def hash_key_type(self):
        """Gets the hash_key_type of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501


        :return: The hash_key_type of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._hash_key_type

    @hash_key_type.setter
    def hash_key_type(self, hash_key_type):
        """Sets the hash_key_type of this ConsistentHashConfigForGetServiceOutput.


        :param hash_key_type: The hash_key_type of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._hash_key_type = hash_key_type

    @property
    def http_header_name(self):
        """Gets the http_header_name of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501


        :return: The http_header_name of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501
        :rtype: str
        """
        return self._http_header_name

    @http_header_name.setter
    def http_header_name(self, http_header_name):
        """Sets the http_header_name of this ConsistentHashConfigForGetServiceOutput.


        :param http_header_name: The http_header_name of this ConsistentHashConfigForGetServiceOutput.  # noqa: E501
        :type: str
        """

        self._http_header_name = http_header_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsistentHashConfigForGetServiceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsistentHashConfigForGetServiceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsistentHashConfigForGetServiceOutput):
            return True

        return self.to_dict() != other.to_dict()
