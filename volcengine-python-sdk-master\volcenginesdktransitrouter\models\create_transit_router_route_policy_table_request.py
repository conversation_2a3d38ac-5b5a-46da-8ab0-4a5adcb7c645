# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTransitRouterRoutePolicyTableRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'direction': 'str',
        'transit_router_id': 'str',
        'transit_router_route_policy_table_name': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'direction': 'Direction',
        'transit_router_id': 'TransitRouterId',
        'transit_router_route_policy_table_name': 'TransitRouterRoutePolicyTableName'
    }

    def __init__(self, client_token=None, description=None, direction=None, transit_router_id=None, transit_router_route_policy_table_name=None, _configuration=None):  # noqa: E501
        """CreateTransitRouterRoutePolicyTableRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._direction = None
        self._transit_router_id = None
        self._transit_router_route_policy_table_name = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        self.direction = direction
        self.transit_router_id = transit_router_id
        if transit_router_route_policy_table_name is not None:
            self.transit_router_route_policy_table_name = transit_router_route_policy_table_name

    @property
    def client_token(self):
        """Gets the client_token of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501


        :return: The client_token of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTransitRouterRoutePolicyTableRequest.


        :param client_token: The client_token of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501


        :return: The description of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTransitRouterRoutePolicyTableRequest.


        :param description: The description of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direction(self):
        """Gets the direction of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501


        :return: The direction of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this CreateTransitRouterRoutePolicyTableRequest.


        :param direction: The direction of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direction is None:
            raise ValueError("Invalid value for `direction`, must not be `None`")  # noqa: E501

        self._direction = direction

    @property
    def transit_router_id(self):
        """Gets the transit_router_id of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501


        :return: The transit_router_id of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_id

    @transit_router_id.setter
    def transit_router_id(self, transit_router_id):
        """Sets the transit_router_id of this CreateTransitRouterRoutePolicyTableRequest.


        :param transit_router_id: The transit_router_id of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_id is None:
            raise ValueError("Invalid value for `transit_router_id`, must not be `None`")  # noqa: E501

        self._transit_router_id = transit_router_id

    @property
    def transit_router_route_policy_table_name(self):
        """Gets the transit_router_route_policy_table_name of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501


        :return: The transit_router_route_policy_table_name of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_policy_table_name

    @transit_router_route_policy_table_name.setter
    def transit_router_route_policy_table_name(self, transit_router_route_policy_table_name):
        """Sets the transit_router_route_policy_table_name of this CreateTransitRouterRoutePolicyTableRequest.


        :param transit_router_route_policy_table_name: The transit_router_route_policy_table_name of this CreateTransitRouterRoutePolicyTableRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_route_policy_table_name = transit_router_route_policy_table_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTransitRouterRoutePolicyTableRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTransitRouterRoutePolicyTableRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTransitRouterRoutePolicyTableRequest):
            return True

        return self.to_dict() != other.to_dict()
