# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class KibanaConfigForDescribeInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'request_timeout': 'int',
        'session_keep_alive': 'bool',
        'session_ttl': 'int'
    }

    attribute_map = {
        'request_timeout': 'RequestTimeout',
        'session_keep_alive': 'SessionKeepAlive',
        'session_ttl': 'SessionTTL'
    }

    def __init__(self, request_timeout=None, session_keep_alive=None, session_ttl=None, _configuration=None):  # noqa: E501
        """KibanaConfigForDescribeInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._request_timeout = None
        self._session_keep_alive = None
        self._session_ttl = None
        self.discriminator = None

        if request_timeout is not None:
            self.request_timeout = request_timeout
        if session_keep_alive is not None:
            self.session_keep_alive = session_keep_alive
        if session_ttl is not None:
            self.session_ttl = session_ttl

    @property
    def request_timeout(self):
        """Gets the request_timeout of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501


        :return: The request_timeout of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :rtype: int
        """
        return self._request_timeout

    @request_timeout.setter
    def request_timeout(self, request_timeout):
        """Sets the request_timeout of this KibanaConfigForDescribeInstanceOutput.


        :param request_timeout: The request_timeout of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :type: int
        """

        self._request_timeout = request_timeout

    @property
    def session_keep_alive(self):
        """Gets the session_keep_alive of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501


        :return: The session_keep_alive of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._session_keep_alive

    @session_keep_alive.setter
    def session_keep_alive(self, session_keep_alive):
        """Sets the session_keep_alive of this KibanaConfigForDescribeInstanceOutput.


        :param session_keep_alive: The session_keep_alive of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :type: bool
        """

        self._session_keep_alive = session_keep_alive

    @property
    def session_ttl(self):
        """Gets the session_ttl of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501


        :return: The session_ttl of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :rtype: int
        """
        return self._session_ttl

    @session_ttl.setter
    def session_ttl(self, session_ttl):
        """Sets the session_ttl of this KibanaConfigForDescribeInstanceOutput.


        :param session_ttl: The session_ttl of this KibanaConfigForDescribeInstanceOutput.  # noqa: E501
        :type: int
        """

        self._session_ttl = session_ttl

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(KibanaConfigForDescribeInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, KibanaConfigForDescribeInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, KibanaConfigForDescribeInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
