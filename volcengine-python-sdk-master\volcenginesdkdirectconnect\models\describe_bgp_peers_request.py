# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBgpPeersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bgp_peer_ids': 'list[str]',
        'bgp_peer_name': 'str',
        'direct_connect_gateway_id': 'str',
        'ip_version': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'remote_asn': 'int',
        'virtual_interface_id': 'str'
    }

    attribute_map = {
        'bgp_peer_ids': 'BgpPeerIds',
        'bgp_peer_name': 'BgpPeerName',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'ip_version': 'IpVersion',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'remote_asn': 'RemoteAsn',
        'virtual_interface_id': 'VirtualInterfaceId'
    }

    def __init__(self, bgp_peer_ids=None, bgp_peer_name=None, direct_connect_gateway_id=None, ip_version=None, page_number=None, page_size=None, remote_asn=None, virtual_interface_id=None, _configuration=None):  # noqa: E501
        """DescribeBgpPeersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bgp_peer_ids = None
        self._bgp_peer_name = None
        self._direct_connect_gateway_id = None
        self._ip_version = None
        self._page_number = None
        self._page_size = None
        self._remote_asn = None
        self._virtual_interface_id = None
        self.discriminator = None

        if bgp_peer_ids is not None:
            self.bgp_peer_ids = bgp_peer_ids
        if bgp_peer_name is not None:
            self.bgp_peer_name = bgp_peer_name
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if ip_version is not None:
            self.ip_version = ip_version
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if remote_asn is not None:
            self.remote_asn = remote_asn
        if virtual_interface_id is not None:
            self.virtual_interface_id = virtual_interface_id

    @property
    def bgp_peer_ids(self):
        """Gets the bgp_peer_ids of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The bgp_peer_ids of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._bgp_peer_ids

    @bgp_peer_ids.setter
    def bgp_peer_ids(self, bgp_peer_ids):
        """Sets the bgp_peer_ids of this DescribeBgpPeersRequest.


        :param bgp_peer_ids: The bgp_peer_ids of this DescribeBgpPeersRequest.  # noqa: E501
        :type: list[str]
        """

        self._bgp_peer_ids = bgp_peer_ids

    @property
    def bgp_peer_name(self):
        """Gets the bgp_peer_name of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The bgp_peer_name of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: str
        """
        return self._bgp_peer_name

    @bgp_peer_name.setter
    def bgp_peer_name(self, bgp_peer_name):
        """Sets the bgp_peer_name of this DescribeBgpPeersRequest.


        :param bgp_peer_name: The bgp_peer_name of this DescribeBgpPeersRequest.  # noqa: E501
        :type: str
        """

        self._bgp_peer_name = bgp_peer_name

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The direct_connect_gateway_id of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this DescribeBgpPeersRequest.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this DescribeBgpPeersRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def ip_version(self):
        """Gets the ip_version of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The ip_version of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this DescribeBgpPeersRequest.


        :param ip_version: The ip_version of this DescribeBgpPeersRequest.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def page_number(self):
        """Gets the page_number of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The page_number of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeBgpPeersRequest.


        :param page_number: The page_number of this DescribeBgpPeersRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The page_size of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeBgpPeersRequest.


        :param page_size: The page_size of this DescribeBgpPeersRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def remote_asn(self):
        """Gets the remote_asn of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The remote_asn of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: int
        """
        return self._remote_asn

    @remote_asn.setter
    def remote_asn(self, remote_asn):
        """Sets the remote_asn of this DescribeBgpPeersRequest.


        :param remote_asn: The remote_asn of this DescribeBgpPeersRequest.  # noqa: E501
        :type: int
        """

        self._remote_asn = remote_asn

    @property
    def virtual_interface_id(self):
        """Gets the virtual_interface_id of this DescribeBgpPeersRequest.  # noqa: E501


        :return: The virtual_interface_id of this DescribeBgpPeersRequest.  # noqa: E501
        :rtype: str
        """
        return self._virtual_interface_id

    @virtual_interface_id.setter
    def virtual_interface_id(self, virtual_interface_id):
        """Sets the virtual_interface_id of this DescribeBgpPeersRequest.


        :param virtual_interface_id: The virtual_interface_id of this DescribeBgpPeersRequest.  # noqa: E501
        :type: str
        """

        self._virtual_interface_id = virtual_interface_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBgpPeersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBgpPeersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBgpPeersRequest):
            return True

        return self.to_dict() != other.to_dict()
