# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBProxyConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binlog_dump': 'bool',
        'check_modify_db_proxy_allowed': 'CheckModifyDBProxyAllowedForDescribeDBProxyConfigOutput',
        'check_modify_db_proxy_allowed_v2': 'list[CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput]',
        'connection_pool_type': 'str',
        'db_proxy_status': 'str',
        'feature_states': 'list[FeatureStateForDescribeDBProxyConfigOutput]',
        'global_read_only': 'bool',
        'instance_id': 'str',
        'proxy_resource_info': 'ProxyResourceInfoForDescribeDBProxyConfigOutput'
    }

    attribute_map = {
        'binlog_dump': 'BinlogDump',
        'check_modify_db_proxy_allowed': 'CheckModifyDBProxyAllowed',
        'check_modify_db_proxy_allowed_v2': 'CheckModifyDBProxyAllowedV2',
        'connection_pool_type': 'ConnectionPoolType',
        'db_proxy_status': 'DBProxyStatus',
        'feature_states': 'FeatureStates',
        'global_read_only': 'GlobalReadOnly',
        'instance_id': 'InstanceId',
        'proxy_resource_info': 'ProxyResourceInfo'
    }

    def __init__(self, binlog_dump=None, check_modify_db_proxy_allowed=None, check_modify_db_proxy_allowed_v2=None, connection_pool_type=None, db_proxy_status=None, feature_states=None, global_read_only=None, instance_id=None, proxy_resource_info=None, _configuration=None):  # noqa: E501
        """DescribeDBProxyConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binlog_dump = None
        self._check_modify_db_proxy_allowed = None
        self._check_modify_db_proxy_allowed_v2 = None
        self._connection_pool_type = None
        self._db_proxy_status = None
        self._feature_states = None
        self._global_read_only = None
        self._instance_id = None
        self._proxy_resource_info = None
        self.discriminator = None

        if binlog_dump is not None:
            self.binlog_dump = binlog_dump
        if check_modify_db_proxy_allowed is not None:
            self.check_modify_db_proxy_allowed = check_modify_db_proxy_allowed
        if check_modify_db_proxy_allowed_v2 is not None:
            self.check_modify_db_proxy_allowed_v2 = check_modify_db_proxy_allowed_v2
        if connection_pool_type is not None:
            self.connection_pool_type = connection_pool_type
        if db_proxy_status is not None:
            self.db_proxy_status = db_proxy_status
        if feature_states is not None:
            self.feature_states = feature_states
        if global_read_only is not None:
            self.global_read_only = global_read_only
        if instance_id is not None:
            self.instance_id = instance_id
        if proxy_resource_info is not None:
            self.proxy_resource_info = proxy_resource_info

    @property
    def binlog_dump(self):
        """Gets the binlog_dump of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The binlog_dump of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_dump

    @binlog_dump.setter
    def binlog_dump(self, binlog_dump):
        """Sets the binlog_dump of this DescribeDBProxyConfigResponse.


        :param binlog_dump: The binlog_dump of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: bool
        """

        self._binlog_dump = binlog_dump

    @property
    def check_modify_db_proxy_allowed(self):
        """Gets the check_modify_db_proxy_allowed of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The check_modify_db_proxy_allowed of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: CheckModifyDBProxyAllowedForDescribeDBProxyConfigOutput
        """
        return self._check_modify_db_proxy_allowed

    @check_modify_db_proxy_allowed.setter
    def check_modify_db_proxy_allowed(self, check_modify_db_proxy_allowed):
        """Sets the check_modify_db_proxy_allowed of this DescribeDBProxyConfigResponse.


        :param check_modify_db_proxy_allowed: The check_modify_db_proxy_allowed of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: CheckModifyDBProxyAllowedForDescribeDBProxyConfigOutput
        """

        self._check_modify_db_proxy_allowed = check_modify_db_proxy_allowed

    @property
    def check_modify_db_proxy_allowed_v2(self):
        """Gets the check_modify_db_proxy_allowed_v2 of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The check_modify_db_proxy_allowed_v2 of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: list[CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput]
        """
        return self._check_modify_db_proxy_allowed_v2

    @check_modify_db_proxy_allowed_v2.setter
    def check_modify_db_proxy_allowed_v2(self, check_modify_db_proxy_allowed_v2):
        """Sets the check_modify_db_proxy_allowed_v2 of this DescribeDBProxyConfigResponse.


        :param check_modify_db_proxy_allowed_v2: The check_modify_db_proxy_allowed_v2 of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: list[CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput]
        """

        self._check_modify_db_proxy_allowed_v2 = check_modify_db_proxy_allowed_v2

    @property
    def connection_pool_type(self):
        """Gets the connection_pool_type of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The connection_pool_type of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._connection_pool_type

    @connection_pool_type.setter
    def connection_pool_type(self, connection_pool_type):
        """Sets the connection_pool_type of this DescribeDBProxyConfigResponse.


        :param connection_pool_type: The connection_pool_type of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: str
        """

        self._connection_pool_type = connection_pool_type

    @property
    def db_proxy_status(self):
        """Gets the db_proxy_status of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The db_proxy_status of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._db_proxy_status

    @db_proxy_status.setter
    def db_proxy_status(self, db_proxy_status):
        """Sets the db_proxy_status of this DescribeDBProxyConfigResponse.


        :param db_proxy_status: The db_proxy_status of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: str
        """

        self._db_proxy_status = db_proxy_status

    @property
    def feature_states(self):
        """Gets the feature_states of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The feature_states of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: list[FeatureStateForDescribeDBProxyConfigOutput]
        """
        return self._feature_states

    @feature_states.setter
    def feature_states(self, feature_states):
        """Sets the feature_states of this DescribeDBProxyConfigResponse.


        :param feature_states: The feature_states of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: list[FeatureStateForDescribeDBProxyConfigOutput]
        """

        self._feature_states = feature_states

    @property
    def global_read_only(self):
        """Gets the global_read_only of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The global_read_only of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: bool
        """
        return self._global_read_only

    @global_read_only.setter
    def global_read_only(self, global_read_only):
        """Sets the global_read_only of this DescribeDBProxyConfigResponse.


        :param global_read_only: The global_read_only of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: bool
        """

        self._global_read_only = global_read_only

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The instance_id of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBProxyConfigResponse.


        :param instance_id: The instance_id of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def proxy_resource_info(self):
        """Gets the proxy_resource_info of this DescribeDBProxyConfigResponse.  # noqa: E501


        :return: The proxy_resource_info of this DescribeDBProxyConfigResponse.  # noqa: E501
        :rtype: ProxyResourceInfoForDescribeDBProxyConfigOutput
        """
        return self._proxy_resource_info

    @proxy_resource_info.setter
    def proxy_resource_info(self, proxy_resource_info):
        """Sets the proxy_resource_info of this DescribeDBProxyConfigResponse.


        :param proxy_resource_info: The proxy_resource_info of this DescribeDBProxyConfigResponse.  # noqa: E501
        :type: ProxyResourceInfoForDescribeDBProxyConfigOutput
        """

        self._proxy_resource_info = proxy_resource_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBProxyConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBProxyConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBProxyConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
