# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteMediaLibraryVideoAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fail_video_ids': 'list[str]',
        'success_video_ids': 'list[str]'
    }

    attribute_map = {
        'fail_video_ids': 'FailVideoIds',
        'success_video_ids': 'SuccessVideoIds'
    }

    def __init__(self, fail_video_ids=None, success_video_ids=None, _configuration=None):  # noqa: E501
        """DeleteMediaLibraryVideoAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fail_video_ids = None
        self._success_video_ids = None
        self.discriminator = None

        if fail_video_ids is not None:
            self.fail_video_ids = fail_video_ids
        if success_video_ids is not None:
            self.success_video_ids = success_video_ids

    @property
    def fail_video_ids(self):
        """Gets the fail_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501


        :return: The fail_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._fail_video_ids

    @fail_video_ids.setter
    def fail_video_ids(self, fail_video_ids):
        """Sets the fail_video_ids of this DeleteMediaLibraryVideoAPIResponse.


        :param fail_video_ids: The fail_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501
        :type: list[str]
        """

        self._fail_video_ids = fail_video_ids

    @property
    def success_video_ids(self):
        """Gets the success_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501


        :return: The success_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._success_video_ids

    @success_video_ids.setter
    def success_video_ids(self, success_video_ids):
        """Sets the success_video_ids of this DeleteMediaLibraryVideoAPIResponse.


        :param success_video_ids: The success_video_ids of this DeleteMediaLibraryVideoAPIResponse.  # noqa: E501
        :type: list[str]
        """

        self._success_video_ids = success_video_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteMediaLibraryVideoAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteMediaLibraryVideoAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteMediaLibraryVideoAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
