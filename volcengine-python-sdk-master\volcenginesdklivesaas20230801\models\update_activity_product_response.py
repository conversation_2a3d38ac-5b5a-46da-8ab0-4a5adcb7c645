# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateActivityProductResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'product_config': 'ProductConfigForUpdateActivityProductOutput',
        'vertical_icon_url': 'str'
    }

    attribute_map = {
        'product_config': 'ProductConfig',
        'vertical_icon_url': 'VerticalIconUrl'
    }

    def __init__(self, product_config=None, vertical_icon_url=None, _configuration=None):  # noqa: E501
        """UpdateActivityProductResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._product_config = None
        self._vertical_icon_url = None
        self.discriminator = None

        if product_config is not None:
            self.product_config = product_config
        if vertical_icon_url is not None:
            self.vertical_icon_url = vertical_icon_url

    @property
    def product_config(self):
        """Gets the product_config of this UpdateActivityProductResponse.  # noqa: E501


        :return: The product_config of this UpdateActivityProductResponse.  # noqa: E501
        :rtype: ProductConfigForUpdateActivityProductOutput
        """
        return self._product_config

    @product_config.setter
    def product_config(self, product_config):
        """Sets the product_config of this UpdateActivityProductResponse.


        :param product_config: The product_config of this UpdateActivityProductResponse.  # noqa: E501
        :type: ProductConfigForUpdateActivityProductOutput
        """

        self._product_config = product_config

    @property
    def vertical_icon_url(self):
        """Gets the vertical_icon_url of this UpdateActivityProductResponse.  # noqa: E501


        :return: The vertical_icon_url of this UpdateActivityProductResponse.  # noqa: E501
        :rtype: str
        """
        return self._vertical_icon_url

    @vertical_icon_url.setter
    def vertical_icon_url(self, vertical_icon_url):
        """Sets the vertical_icon_url of this UpdateActivityProductResponse.


        :param vertical_icon_url: The vertical_icon_url of this UpdateActivityProductResponse.  # noqa: E501
        :type: str
        """

        self._vertical_icon_url = vertical_icon_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateActivityProductResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateActivityProductResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateActivityProductResponse):
            return True

        return self.to_dict() != other.to_dict()
