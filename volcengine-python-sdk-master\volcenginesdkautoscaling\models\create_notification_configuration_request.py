# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNotificationConfigurationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'event_types': 'list[str]',
        'notification_type': 'str',
        'scaling_group_id': 'str'
    }

    attribute_map = {
        'event_types': 'EventTypes',
        'notification_type': 'NotificationType',
        'scaling_group_id': 'ScalingGroupId'
    }

    def __init__(self, event_types=None, notification_type=None, scaling_group_id=None, _configuration=None):  # noqa: E501
        """CreateNotificationConfigurationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._event_types = None
        self._notification_type = None
        self._scaling_group_id = None
        self.discriminator = None

        if event_types is not None:
            self.event_types = event_types
        self.notification_type = notification_type
        self.scaling_group_id = scaling_group_id

    @property
    def event_types(self):
        """Gets the event_types of this CreateNotificationConfigurationRequest.  # noqa: E501


        :return: The event_types of this CreateNotificationConfigurationRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_types

    @event_types.setter
    def event_types(self, event_types):
        """Sets the event_types of this CreateNotificationConfigurationRequest.


        :param event_types: The event_types of this CreateNotificationConfigurationRequest.  # noqa: E501
        :type: list[str]
        """

        self._event_types = event_types

    @property
    def notification_type(self):
        """Gets the notification_type of this CreateNotificationConfigurationRequest.  # noqa: E501


        :return: The notification_type of this CreateNotificationConfigurationRequest.  # noqa: E501
        :rtype: str
        """
        return self._notification_type

    @notification_type.setter
    def notification_type(self, notification_type):
        """Sets the notification_type of this CreateNotificationConfigurationRequest.


        :param notification_type: The notification_type of this CreateNotificationConfigurationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and notification_type is None:
            raise ValueError("Invalid value for `notification_type`, must not be `None`")  # noqa: E501

        self._notification_type = notification_type

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this CreateNotificationConfigurationRequest.  # noqa: E501


        :return: The scaling_group_id of this CreateNotificationConfigurationRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this CreateNotificationConfigurationRequest.


        :param scaling_group_id: The scaling_group_id of this CreateNotificationConfigurationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scaling_group_id is None:
            raise ValueError("Invalid value for `scaling_group_id`, must not be `None`")  # noqa: E501

        self._scaling_group_id = scaling_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNotificationConfigurationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNotificationConfigurationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNotificationConfigurationRequest):
            return True

        return self.to_dict() != other.to_dict()
