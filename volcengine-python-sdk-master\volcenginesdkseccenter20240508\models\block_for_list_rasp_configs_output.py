# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BlockForListRaspConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hook_func': 'list[str]',
        'hook_param': 'int',
        'rules': 'list[RuleForListRaspConfigsOutput]',
        'runtime': 'str'
    }

    attribute_map = {
        'hook_func': 'HookFunc',
        'hook_param': 'HookParam',
        'rules': 'Rules',
        'runtime': 'Runtime'
    }

    def __init__(self, hook_func=None, hook_param=None, rules=None, runtime=None, _configuration=None):  # noqa: E501
        """BlockForListRaspConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hook_func = None
        self._hook_param = None
        self._rules = None
        self._runtime = None
        self.discriminator = None

        if hook_func is not None:
            self.hook_func = hook_func
        if hook_param is not None:
            self.hook_param = hook_param
        if rules is not None:
            self.rules = rules
        if runtime is not None:
            self.runtime = runtime

    @property
    def hook_func(self):
        """Gets the hook_func of this BlockForListRaspConfigsOutput.  # noqa: E501


        :return: The hook_func of this BlockForListRaspConfigsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._hook_func

    @hook_func.setter
    def hook_func(self, hook_func):
        """Sets the hook_func of this BlockForListRaspConfigsOutput.


        :param hook_func: The hook_func of this BlockForListRaspConfigsOutput.  # noqa: E501
        :type: list[str]
        """

        self._hook_func = hook_func

    @property
    def hook_param(self):
        """Gets the hook_param of this BlockForListRaspConfigsOutput.  # noqa: E501


        :return: The hook_param of this BlockForListRaspConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._hook_param

    @hook_param.setter
    def hook_param(self, hook_param):
        """Sets the hook_param of this BlockForListRaspConfigsOutput.


        :param hook_param: The hook_param of this BlockForListRaspConfigsOutput.  # noqa: E501
        :type: int
        """

        self._hook_param = hook_param

    @property
    def rules(self):
        """Gets the rules of this BlockForListRaspConfigsOutput.  # noqa: E501


        :return: The rules of this BlockForListRaspConfigsOutput.  # noqa: E501
        :rtype: list[RuleForListRaspConfigsOutput]
        """
        return self._rules

    @rules.setter
    def rules(self, rules):
        """Sets the rules of this BlockForListRaspConfigsOutput.


        :param rules: The rules of this BlockForListRaspConfigsOutput.  # noqa: E501
        :type: list[RuleForListRaspConfigsOutput]
        """

        self._rules = rules

    @property
    def runtime(self):
        """Gets the runtime of this BlockForListRaspConfigsOutput.  # noqa: E501


        :return: The runtime of this BlockForListRaspConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this BlockForListRaspConfigsOutput.


        :param runtime: The runtime of this BlockForListRaspConfigsOutput.  # noqa: E501
        :type: str
        """

        self._runtime = runtime

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BlockForListRaspConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BlockForListRaspConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BlockForListRaspConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
