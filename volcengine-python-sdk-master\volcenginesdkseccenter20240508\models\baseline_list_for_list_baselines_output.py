# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaselineListForListBaselinesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affected_host': 'int',
        'baseline_id': 'int',
        'baseline_name': 'str',
        'baseline_name_en': 'str',
        'check_list_num': 'int',
        'detect_progress': 'int',
        'detect_status': 'str',
        'last_detected_time': 'int',
        'risk_num': 'RiskNumForListBaselinesOutput'
    }

    attribute_map = {
        'affected_host': 'AffectedHost',
        'baseline_id': 'BaselineID',
        'baseline_name': 'BaselineName',
        'baseline_name_en': 'BaselineNameEn',
        'check_list_num': 'CheckListNum',
        'detect_progress': 'DetectProgress',
        'detect_status': 'DetectStatus',
        'last_detected_time': 'LastDetectedTime',
        'risk_num': 'RiskNum'
    }

    def __init__(self, affected_host=None, baseline_id=None, baseline_name=None, baseline_name_en=None, check_list_num=None, detect_progress=None, detect_status=None, last_detected_time=None, risk_num=None, _configuration=None):  # noqa: E501
        """BaselineListForListBaselinesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affected_host = None
        self._baseline_id = None
        self._baseline_name = None
        self._baseline_name_en = None
        self._check_list_num = None
        self._detect_progress = None
        self._detect_status = None
        self._last_detected_time = None
        self._risk_num = None
        self.discriminator = None

        if affected_host is not None:
            self.affected_host = affected_host
        if baseline_id is not None:
            self.baseline_id = baseline_id
        if baseline_name is not None:
            self.baseline_name = baseline_name
        if baseline_name_en is not None:
            self.baseline_name_en = baseline_name_en
        if check_list_num is not None:
            self.check_list_num = check_list_num
        if detect_progress is not None:
            self.detect_progress = detect_progress
        if detect_status is not None:
            self.detect_status = detect_status
        if last_detected_time is not None:
            self.last_detected_time = last_detected_time
        if risk_num is not None:
            self.risk_num = risk_num

    @property
    def affected_host(self):
        """Gets the affected_host of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The affected_host of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._affected_host

    @affected_host.setter
    def affected_host(self, affected_host):
        """Sets the affected_host of this BaselineListForListBaselinesOutput.


        :param affected_host: The affected_host of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._affected_host = affected_host

    @property
    def baseline_id(self):
        """Gets the baseline_id of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The baseline_id of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this BaselineListForListBaselinesOutput.


        :param baseline_id: The baseline_id of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def baseline_name(self):
        """Gets the baseline_name of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The baseline_name of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._baseline_name

    @baseline_name.setter
    def baseline_name(self, baseline_name):
        """Sets the baseline_name of this BaselineListForListBaselinesOutput.


        :param baseline_name: The baseline_name of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: str
        """

        self._baseline_name = baseline_name

    @property
    def baseline_name_en(self):
        """Gets the baseline_name_en of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The baseline_name_en of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._baseline_name_en

    @baseline_name_en.setter
    def baseline_name_en(self, baseline_name_en):
        """Sets the baseline_name_en of this BaselineListForListBaselinesOutput.


        :param baseline_name_en: The baseline_name_en of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: str
        """

        self._baseline_name_en = baseline_name_en

    @property
    def check_list_num(self):
        """Gets the check_list_num of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The check_list_num of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_list_num

    @check_list_num.setter
    def check_list_num(self, check_list_num):
        """Sets the check_list_num of this BaselineListForListBaselinesOutput.


        :param check_list_num: The check_list_num of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._check_list_num = check_list_num

    @property
    def detect_progress(self):
        """Gets the detect_progress of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The detect_progress of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._detect_progress

    @detect_progress.setter
    def detect_progress(self, detect_progress):
        """Sets the detect_progress of this BaselineListForListBaselinesOutput.


        :param detect_progress: The detect_progress of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._detect_progress = detect_progress

    @property
    def detect_status(self):
        """Gets the detect_status of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The detect_status of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_status

    @detect_status.setter
    def detect_status(self, detect_status):
        """Sets the detect_status of this BaselineListForListBaselinesOutput.


        :param detect_status: The detect_status of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: str
        """

        self._detect_status = detect_status

    @property
    def last_detected_time(self):
        """Gets the last_detected_time of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The last_detected_time of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_detected_time

    @last_detected_time.setter
    def last_detected_time(self, last_detected_time):
        """Sets the last_detected_time of this BaselineListForListBaselinesOutput.


        :param last_detected_time: The last_detected_time of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: int
        """

        self._last_detected_time = last_detected_time

    @property
    def risk_num(self):
        """Gets the risk_num of this BaselineListForListBaselinesOutput.  # noqa: E501


        :return: The risk_num of this BaselineListForListBaselinesOutput.  # noqa: E501
        :rtype: RiskNumForListBaselinesOutput
        """
        return self._risk_num

    @risk_num.setter
    def risk_num(self, risk_num):
        """Sets the risk_num of this BaselineListForListBaselinesOutput.


        :param risk_num: The risk_num of this BaselineListForListBaselinesOutput.  # noqa: E501
        :type: RiskNumForListBaselinesOutput
        """

        self._risk_num = risk_num

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaselineListForListBaselinesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaselineListForListBaselinesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaselineListForListBaselinesOutput):
            return True

        return self.to_dict() != other.to_dict()
