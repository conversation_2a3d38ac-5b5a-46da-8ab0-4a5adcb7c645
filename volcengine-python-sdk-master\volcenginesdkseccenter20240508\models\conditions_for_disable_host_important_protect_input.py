# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForDisableHostImportantProtectInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_id_list': 'list[str]',
        'agent_status': 'list[str]',
        'all_host': 'bool',
        'cloud_providers': 'list[str]',
        'hostname': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'platforms': 'list[str]',
        'proxy_name': 'str',
        'proxy_server_name': 'str',
        'regions': 'list[str]',
        'risk': 'RiskForDisableHostImportantProtectInput',
        'security_enhancement': 'bool',
        'status': 'list[str]',
        'tags': 'list[str]',
        'top_group_id': 'str',
        'virus_file_auto_isolate': 'bool',
        'visibility': 'list[str]',
        'vpc_ids': 'list[str]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_id_list': 'AgentIDList',
        'agent_status': 'AgentStatus',
        'all_host': 'AllHost',
        'cloud_providers': 'CloudProviders',
        'hostname': 'Hostname',
        'ip': 'Ip',
        'leaf_group_ids': 'LeafGroupIDs',
        'platforms': 'Platforms',
        'proxy_name': 'ProxyName',
        'proxy_server_name': 'ProxyServerName',
        'regions': 'Regions',
        'risk': 'Risk',
        'security_enhancement': 'SecurityEnhancement',
        'status': 'Status',
        'tags': 'Tags',
        'top_group_id': 'TopGroupID',
        'virus_file_auto_isolate': 'VirusFileAutoIsolate',
        'visibility': 'Visibility',
        'vpc_ids': 'VpcIds'
    }

    def __init__(self, agent_id=None, agent_id_list=None, agent_status=None, all_host=None, cloud_providers=None, hostname=None, ip=None, leaf_group_ids=None, platforms=None, proxy_name=None, proxy_server_name=None, regions=None, risk=None, security_enhancement=None, status=None, tags=None, top_group_id=None, virus_file_auto_isolate=None, visibility=None, vpc_ids=None, _configuration=None):  # noqa: E501
        """ConditionsForDisableHostImportantProtectInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_id_list = None
        self._agent_status = None
        self._all_host = None
        self._cloud_providers = None
        self._hostname = None
        self._ip = None
        self._leaf_group_ids = None
        self._platforms = None
        self._proxy_name = None
        self._proxy_server_name = None
        self._regions = None
        self._risk = None
        self._security_enhancement = None
        self._status = None
        self._tags = None
        self._top_group_id = None
        self._virus_file_auto_isolate = None
        self._visibility = None
        self._vpc_ids = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if agent_status is not None:
            self.agent_status = agent_status
        if all_host is not None:
            self.all_host = all_host
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if platforms is not None:
            self.platforms = platforms
        if proxy_name is not None:
            self.proxy_name = proxy_name
        if proxy_server_name is not None:
            self.proxy_server_name = proxy_server_name
        if regions is not None:
            self.regions = regions
        if risk is not None:
            self.risk = risk
        if security_enhancement is not None:
            self.security_enhancement = security_enhancement
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if virus_file_auto_isolate is not None:
            self.virus_file_auto_isolate = virus_file_auto_isolate
        if visibility is not None:
            self.visibility = visibility
        if vpc_ids is not None:
            self.vpc_ids = vpc_ids

    @property
    def agent_id(self):
        """Gets the agent_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The agent_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ConditionsForDisableHostImportantProtectInput.


        :param agent_id: The agent_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The agent_id_list of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this ConditionsForDisableHostImportantProtectInput.


        :param agent_id_list: The agent_id_list of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def agent_status(self):
        """Gets the agent_status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The agent_status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_status

    @agent_status.setter
    def agent_status(self, agent_status):
        """Sets the agent_status of this ConditionsForDisableHostImportantProtectInput.


        :param agent_status: The agent_status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._agent_status = agent_status

    @property
    def all_host(self):
        """Gets the all_host of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The all_host of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: bool
        """
        return self._all_host

    @all_host.setter
    def all_host(self, all_host):
        """Sets the all_host of this ConditionsForDisableHostImportantProtectInput.


        :param all_host: The all_host of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: bool
        """

        self._all_host = all_host

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The cloud_providers of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ConditionsForDisableHostImportantProtectInput.


        :param cloud_providers: The cloud_providers of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def hostname(self):
        """Gets the hostname of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The hostname of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ConditionsForDisableHostImportantProtectInput.


        :param hostname: The hostname of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The ip of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ConditionsForDisableHostImportantProtectInput.


        :param ip: The ip of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The leaf_group_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ConditionsForDisableHostImportantProtectInput.


        :param leaf_group_ids: The leaf_group_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def platforms(self):
        """Gets the platforms of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The platforms of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._platforms

    @platforms.setter
    def platforms(self, platforms):
        """Sets the platforms of this ConditionsForDisableHostImportantProtectInput.


        :param platforms: The platforms of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._platforms = platforms

    @property
    def proxy_name(self):
        """Gets the proxy_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The proxy_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._proxy_name

    @proxy_name.setter
    def proxy_name(self, proxy_name):
        """Sets the proxy_name of this ConditionsForDisableHostImportantProtectInput.


        :param proxy_name: The proxy_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._proxy_name = proxy_name

    @property
    def proxy_server_name(self):
        """Gets the proxy_server_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The proxy_server_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._proxy_server_name

    @proxy_server_name.setter
    def proxy_server_name(self, proxy_server_name):
        """Sets the proxy_server_name of this ConditionsForDisableHostImportantProtectInput.


        :param proxy_server_name: The proxy_server_name of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._proxy_server_name = proxy_server_name

    @property
    def regions(self):
        """Gets the regions of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The regions of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this ConditionsForDisableHostImportantProtectInput.


        :param regions: The regions of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def risk(self):
        """Gets the risk of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The risk of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: RiskForDisableHostImportantProtectInput
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this ConditionsForDisableHostImportantProtectInput.


        :param risk: The risk of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: RiskForDisableHostImportantProtectInput
        """

        self._risk = risk

    @property
    def security_enhancement(self):
        """Gets the security_enhancement of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The security_enhancement of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: bool
        """
        return self._security_enhancement

    @security_enhancement.setter
    def security_enhancement(self, security_enhancement):
        """Sets the security_enhancement of this ConditionsForDisableHostImportantProtectInput.


        :param security_enhancement: The security_enhancement of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: bool
        """

        self._security_enhancement = security_enhancement

    @property
    def status(self):
        """Gets the status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ConditionsForDisableHostImportantProtectInput.


        :param status: The status of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The tags of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ConditionsForDisableHostImportantProtectInput.


        :param tags: The tags of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The top_group_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ConditionsForDisableHostImportantProtectInput.


        :param top_group_id: The top_group_id of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def virus_file_auto_isolate(self):
        """Gets the virus_file_auto_isolate of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The virus_file_auto_isolate of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: bool
        """
        return self._virus_file_auto_isolate

    @virus_file_auto_isolate.setter
    def virus_file_auto_isolate(self, virus_file_auto_isolate):
        """Sets the virus_file_auto_isolate of this ConditionsForDisableHostImportantProtectInput.


        :param virus_file_auto_isolate: The virus_file_auto_isolate of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: bool
        """

        self._virus_file_auto_isolate = virus_file_auto_isolate

    @property
    def visibility(self):
        """Gets the visibility of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The visibility of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._visibility

    @visibility.setter
    def visibility(self, visibility):
        """Sets the visibility of this ConditionsForDisableHostImportantProtectInput.


        :param visibility: The visibility of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._visibility = visibility

    @property
    def vpc_ids(self):
        """Gets the vpc_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501


        :return: The vpc_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_ids

    @vpc_ids.setter
    def vpc_ids(self, vpc_ids):
        """Sets the vpc_ids of this ConditionsForDisableHostImportantProtectInput.


        :param vpc_ids: The vpc_ids of this ConditionsForDisableHostImportantProtectInput.  # noqa: E501
        :type: list[str]
        """

        self._vpc_ids = vpc_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForDisableHostImportantProtectInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForDisableHostImportantProtectInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForDisableHostImportantProtectInput):
            return True

        return self.to_dict() != other.to_dict()
