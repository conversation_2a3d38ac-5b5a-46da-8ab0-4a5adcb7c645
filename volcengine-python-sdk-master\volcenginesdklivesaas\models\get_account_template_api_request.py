# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAccountTemplateAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'keyword': 'str',
        'page_item_count': 'int',
        'page_no': 'int',
        'status': 'int'
    }

    attribute_map = {
        'keyword': 'Keyword',
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo',
        'status': 'Status'
    }

    def __init__(self, keyword=None, page_item_count=None, page_no=None, status=None, _configuration=None):  # noqa: E501
        """GetAccountTemplateAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._keyword = None
        self._page_item_count = None
        self._page_no = None
        self._status = None
        self.discriminator = None

        if keyword is not None:
            self.keyword = keyword
        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no
        if status is not None:
            self.status = status

    @property
    def keyword(self):
        """Gets the keyword of this GetAccountTemplateAPIRequest.  # noqa: E501


        :return: The keyword of this GetAccountTemplateAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._keyword

    @keyword.setter
    def keyword(self, keyword):
        """Sets the keyword of this GetAccountTemplateAPIRequest.


        :param keyword: The keyword of this GetAccountTemplateAPIRequest.  # noqa: E501
        :type: str
        """

        self._keyword = keyword

    @property
    def page_item_count(self):
        """Gets the page_item_count of this GetAccountTemplateAPIRequest.  # noqa: E501


        :return: The page_item_count of this GetAccountTemplateAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this GetAccountTemplateAPIRequest.


        :param page_item_count: The page_item_count of this GetAccountTemplateAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this GetAccountTemplateAPIRequest.  # noqa: E501


        :return: The page_no of this GetAccountTemplateAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this GetAccountTemplateAPIRequest.


        :param page_no: The page_no of this GetAccountTemplateAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def status(self):
        """Gets the status of this GetAccountTemplateAPIRequest.  # noqa: E501


        :return: The status of this GetAccountTemplateAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetAccountTemplateAPIRequest.


        :param status: The status of this GetAccountTemplateAPIRequest.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAccountTemplateAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAccountTemplateAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAccountTemplateAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
