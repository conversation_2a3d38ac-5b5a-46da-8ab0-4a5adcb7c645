# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateEvaluationJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'dry_run': 'bool',
        'endpoint_id': 'str',
        'evaluation_datasets': 'list[EvaluationDatasetForCreateEvaluationJobInput]',
        'name': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateEvaluationJobInput]'
    }

    attribute_map = {
        'description': 'Description',
        'dry_run': 'DryRun',
        'endpoint_id': 'EndpointId',
        'evaluation_datasets': 'EvaluationDatasets',
        'name': 'Name',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, description=None, dry_run=None, endpoint_id=None, evaluation_datasets=None, name=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """CreateEvaluationJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._dry_run = None
        self._endpoint_id = None
        self._evaluation_datasets = None
        self._name = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        self.endpoint_id = endpoint_id
        if evaluation_datasets is not None:
            self.evaluation_datasets = evaluation_datasets
        self.name = name
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def description(self):
        """Gets the description of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The description of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateEvaluationJobRequest.


        :param description: The description of this CreateEvaluationJobRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The dry_run of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateEvaluationJobRequest.


        :param dry_run: The dry_run of this CreateEvaluationJobRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The endpoint_id of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this CreateEvaluationJobRequest.


        :param endpoint_id: The endpoint_id of this CreateEvaluationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and endpoint_id is None:
            raise ValueError("Invalid value for `endpoint_id`, must not be `None`")  # noqa: E501

        self._endpoint_id = endpoint_id

    @property
    def evaluation_datasets(self):
        """Gets the evaluation_datasets of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The evaluation_datasets of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: list[EvaluationDatasetForCreateEvaluationJobInput]
        """
        return self._evaluation_datasets

    @evaluation_datasets.setter
    def evaluation_datasets(self, evaluation_datasets):
        """Sets the evaluation_datasets of this CreateEvaluationJobRequest.


        :param evaluation_datasets: The evaluation_datasets of this CreateEvaluationJobRequest.  # noqa: E501
        :type: list[EvaluationDatasetForCreateEvaluationJobInput]
        """

        self._evaluation_datasets = evaluation_datasets

    @property
    def name(self):
        """Gets the name of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The name of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateEvaluationJobRequest.


        :param name: The name of this CreateEvaluationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The project_name of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateEvaluationJobRequest.


        :param project_name: The project_name of this CreateEvaluationJobRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateEvaluationJobRequest.  # noqa: E501


        :return: The tags of this CreateEvaluationJobRequest.  # noqa: E501
        :rtype: list[TagForCreateEvaluationJobInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateEvaluationJobRequest.


        :param tags: The tags of this CreateEvaluationJobRequest.  # noqa: E501
        :type: list[TagForCreateEvaluationJobInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateEvaluationJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateEvaluationJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateEvaluationJobRequest):
            return True

        return self.to_dict() != other.to_dict()
