# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IpsForListJobInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'host_ip': 'str',
        'primary_ip': 'str'
    }

    attribute_map = {
        'host_ip': 'HostIp',
        'primary_ip': 'PrimaryIp'
    }

    def __init__(self, host_ip=None, primary_ip=None, _configuration=None):  # noqa: E501
        """IpsForListJobInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._host_ip = None
        self._primary_ip = None
        self.discriminator = None

        if host_ip is not None:
            self.host_ip = host_ip
        if primary_ip is not None:
            self.primary_ip = primary_ip

    @property
    def host_ip(self):
        """Gets the host_ip of this IpsForListJobInstancesOutput.  # noqa: E501


        :return: The host_ip of this IpsForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_ip

    @host_ip.setter
    def host_ip(self, host_ip):
        """Sets the host_ip of this IpsForListJobInstancesOutput.


        :param host_ip: The host_ip of this IpsForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._host_ip = host_ip

    @property
    def primary_ip(self):
        """Gets the primary_ip of this IpsForListJobInstancesOutput.  # noqa: E501


        :return: The primary_ip of this IpsForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip

    @primary_ip.setter
    def primary_ip(self, primary_ip):
        """Sets the primary_ip of this IpsForListJobInstancesOutput.


        :param primary_ip: The primary_ip of this IpsForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip = primary_ip

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IpsForListJobInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IpsForListJobInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IpsForListJobInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
