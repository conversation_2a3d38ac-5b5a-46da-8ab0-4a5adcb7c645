# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListComponentsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_names': 'list[str]',
        'cluster_id': 'str',
        'component_names': 'list[str]'
    }

    attribute_map = {
        'application_names': 'ApplicationNames',
        'cluster_id': 'ClusterId',
        'component_names': 'ComponentNames'
    }

    def __init__(self, application_names=None, cluster_id=None, component_names=None, _configuration=None):  # noqa: E501
        """ListComponentsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_names = None
        self._cluster_id = None
        self._component_names = None
        self.discriminator = None

        if application_names is not None:
            self.application_names = application_names
        self.cluster_id = cluster_id
        if component_names is not None:
            self.component_names = component_names

    @property
    def application_names(self):
        """Gets the application_names of this ListComponentsRequest.  # noqa: E501


        :return: The application_names of this ListComponentsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._application_names

    @application_names.setter
    def application_names(self, application_names):
        """Sets the application_names of this ListComponentsRequest.


        :param application_names: The application_names of this ListComponentsRequest.  # noqa: E501
        :type: list[str]
        """

        self._application_names = application_names

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListComponentsRequest.  # noqa: E501


        :return: The cluster_id of this ListComponentsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListComponentsRequest.


        :param cluster_id: The cluster_id of this ListComponentsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def component_names(self):
        """Gets the component_names of this ListComponentsRequest.  # noqa: E501


        :return: The component_names of this ListComponentsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._component_names

    @component_names.setter
    def component_names(self, component_names):
        """Sets the component_names of this ListComponentsRequest.


        :param component_names: The component_names of this ListComponentsRequest.  # noqa: E501
        :type: list[str]
        """

        self._component_names = component_names

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListComponentsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListComponentsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListComponentsRequest):
            return True

        return self.to_dict() != other.to_dict()
