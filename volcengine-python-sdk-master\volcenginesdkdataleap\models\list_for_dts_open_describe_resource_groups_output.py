# coding: utf-8

"""
    dataleap

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForDTSOpenDescribeResourceGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'conf': 'ConfForDTSOpenDescribeResourceGroupsOutput',
        'create_time': 'str',
        'description': 'str',
        'instance_no': 'str',
        'name': 'str',
        'project_name': 'str',
        'resource_type': 'str',
        'status': 'str',
        'tags': 'list[TagForDTSOpenDescribeResourceGroupsOutput]',
        'tenant_id': 'int'
    }

    attribute_map = {
        'conf': 'Conf',
        'create_time': 'CreateTime',
        'description': 'Description',
        'instance_no': 'InstanceNo',
        'name': 'Name',
        'project_name': 'ProjectName',
        'resource_type': 'ResourceType',
        'status': 'Status',
        'tags': 'Tags',
        'tenant_id': 'TenantId'
    }

    def __init__(self, conf=None, create_time=None, description=None, instance_no=None, name=None, project_name=None, resource_type=None, status=None, tags=None, tenant_id=None, _configuration=None):  # noqa: E501
        """ListForDTSOpenDescribeResourceGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._conf = None
        self._create_time = None
        self._description = None
        self._instance_no = None
        self._name = None
        self._project_name = None
        self._resource_type = None
        self._status = None
        self._tags = None
        self._tenant_id = None
        self.discriminator = None

        if conf is not None:
            self.conf = conf
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if instance_no is not None:
            self.instance_no = instance_no
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if resource_type is not None:
            self.resource_type = resource_type
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if tenant_id is not None:
            self.tenant_id = tenant_id

    @property
    def conf(self):
        """Gets the conf of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The conf of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: ConfForDTSOpenDescribeResourceGroupsOutput
        """
        return self._conf

    @conf.setter
    def conf(self, conf):
        """Sets the conf of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param conf: The conf of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: ConfForDTSOpenDescribeResourceGroupsOutput
        """

        self._conf = conf

    @property
    def create_time(self):
        """Gets the create_time of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The create_time of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param create_time: The create_time of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The description of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param description: The description of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_no(self):
        """Gets the instance_no of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The instance_no of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param instance_no: The instance_no of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def name(self):
        """Gets the name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param name: The name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The project_name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param project_name: The project_name of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_type(self):
        """Gets the resource_type of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The resource_type of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param resource_type: The resource_type of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def status(self):
        """Gets the status of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The status of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param status: The status of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The tags of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: list[TagForDTSOpenDescribeResourceGroupsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param tags: The tags of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: list[TagForDTSOpenDescribeResourceGroupsOutput]
        """

        self._tags = tags

    @property
    def tenant_id(self):
        """Gets the tenant_id of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501


        :return: The tenant_id of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._tenant_id

    @tenant_id.setter
    def tenant_id(self, tenant_id):
        """Sets the tenant_id of this ListForDTSOpenDescribeResourceGroupsOutput.


        :param tenant_id: The tenant_id of this ListForDTSOpenDescribeResourceGroupsOutput.  # noqa: E501
        :type: int
        """

        self._tenant_id = tenant_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForDTSOpenDescribeResourceGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForDTSOpenDescribeResourceGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForDTSOpenDescribeResourceGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
