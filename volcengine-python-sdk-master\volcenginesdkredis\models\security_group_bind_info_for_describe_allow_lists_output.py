# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SecurityGroupBindInfoForDescribeAllowListsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_mode': 'str',
        'ip_list': 'list[str]',
        'security_group_id': 'str',
        'security_group_name': 'str'
    }

    attribute_map = {
        'bind_mode': 'BindMode',
        'ip_list': 'IpList',
        'security_group_id': 'SecurityGroupId',
        'security_group_name': 'SecurityGroupName'
    }

    def __init__(self, bind_mode=None, ip_list=None, security_group_id=None, security_group_name=None, _configuration=None):  # noqa: E501
        """SecurityGroupBindInfoForDescribeAllowListsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_mode = None
        self._ip_list = None
        self._security_group_id = None
        self._security_group_name = None
        self.discriminator = None

        if bind_mode is not None:
            self.bind_mode = bind_mode
        if ip_list is not None:
            self.ip_list = ip_list
        if security_group_id is not None:
            self.security_group_id = security_group_id
        if security_group_name is not None:
            self.security_group_name = security_group_name

    @property
    def bind_mode(self):
        """Gets the bind_mode of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501


        :return: The bind_mode of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._bind_mode

    @bind_mode.setter
    def bind_mode(self, bind_mode):
        """Sets the bind_mode of this SecurityGroupBindInfoForDescribeAllowListsOutput.


        :param bind_mode: The bind_mode of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._bind_mode = bind_mode

    @property
    def ip_list(self):
        """Gets the ip_list of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501


        :return: The ip_list of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this SecurityGroupBindInfoForDescribeAllowListsOutput.


        :param ip_list: The ip_list of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :type: list[str]
        """

        self._ip_list = ip_list

    @property
    def security_group_id(self):
        """Gets the security_group_id of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501


        :return: The security_group_id of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this SecurityGroupBindInfoForDescribeAllowListsOutput.


        :param security_group_id: The security_group_id of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    @property
    def security_group_name(self):
        """Gets the security_group_name of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501


        :return: The security_group_name of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_name

    @security_group_name.setter
    def security_group_name(self, security_group_name):
        """Sets the security_group_name of this SecurityGroupBindInfoForDescribeAllowListsOutput.


        :param security_group_name: The security_group_name of this SecurityGroupBindInfoForDescribeAllowListsOutput.  # noqa: E501
        :type: str
        """

        self._security_group_name = security_group_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SecurityGroupBindInfoForDescribeAllowListsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SecurityGroupBindInfoForDescribeAllowListsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SecurityGroupBindInfoForDescribeAllowListsOutput):
            return True

        return self.to_dict() != other.to_dict()
