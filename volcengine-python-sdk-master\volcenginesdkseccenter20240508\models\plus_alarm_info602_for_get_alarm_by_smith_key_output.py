# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo602ForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'connect_info': 'str',
        'file_path': 'str',
        'pid_tree': 'str',
        'socket_argv': 'str'
    }

    attribute_map = {
        'connect_info': 'ConnectInfo',
        'file_path': 'FilePath',
        'pid_tree': 'PidTree',
        'socket_argv': 'SocketArgv'
    }

    def __init__(self, connect_info=None, file_path=None, pid_tree=None, socket_argv=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo602ForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._connect_info = None
        self._file_path = None
        self._pid_tree = None
        self._socket_argv = None
        self.discriminator = None

        if connect_info is not None:
            self.connect_info = connect_info
        if file_path is not None:
            self.file_path = file_path
        if pid_tree is not None:
            self.pid_tree = pid_tree
        if socket_argv is not None:
            self.socket_argv = socket_argv

    @property
    def connect_info(self):
        """Gets the connect_info of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The connect_info of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._connect_info

    @connect_info.setter
    def connect_info(self, connect_info):
        """Sets the connect_info of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.


        :param connect_info: The connect_info of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._connect_info = connect_info

    @property
    def file_path(self):
        """Gets the file_path of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The file_path of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.


        :param file_path: The file_path of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def pid_tree(self):
        """Gets the pid_tree of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The pid_tree of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_tree

    @pid_tree.setter
    def pid_tree(self, pid_tree):
        """Sets the pid_tree of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.


        :param pid_tree: The pid_tree of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._pid_tree = pid_tree

    @property
    def socket_argv(self):
        """Gets the socket_argv of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The socket_argv of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._socket_argv

    @socket_argv.setter
    def socket_argv(self, socket_argv):
        """Sets the socket_argv of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.


        :param socket_argv: The socket_argv of this PlusAlarmInfo602ForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._socket_argv = socket_argv

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo602ForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo602ForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo602ForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
