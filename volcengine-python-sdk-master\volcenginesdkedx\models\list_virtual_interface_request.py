# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListVirtualInterfaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dxp_instance_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'state': 'str',
        'vif_instance_id': 'str',
        'vifvgw_instance_id': 'str'
    }

    attribute_map = {
        'dxp_instance_id': 'DXPInstanceID',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'state': 'State',
        'vif_instance_id': 'VIFInstanceID',
        'vifvgw_instance_id': 'VIFVGWInstanceID'
    }

    def __init__(self, dxp_instance_id=None, page_number=None, page_size=None, sort_by=None, sort_order=None, state=None, vif_instance_id=None, vifvgw_instance_id=None, _configuration=None):  # noqa: E501
        """ListVirtualInterfaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dxp_instance_id = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._state = None
        self._vif_instance_id = None
        self._vifvgw_instance_id = None
        self.discriminator = None

        if dxp_instance_id is not None:
            self.dxp_instance_id = dxp_instance_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if state is not None:
            self.state = state
        if vif_instance_id is not None:
            self.vif_instance_id = vif_instance_id
        if vifvgw_instance_id is not None:
            self.vifvgw_instance_id = vifvgw_instance_id

    @property
    def dxp_instance_id(self):
        """Gets the dxp_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The dxp_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._dxp_instance_id

    @dxp_instance_id.setter
    def dxp_instance_id(self, dxp_instance_id):
        """Sets the dxp_instance_id of this ListVirtualInterfaceRequest.


        :param dxp_instance_id: The dxp_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._dxp_instance_id = dxp_instance_id

    @property
    def page_number(self):
        """Gets the page_number of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The page_number of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListVirtualInterfaceRequest.


        :param page_number: The page_number of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The page_size of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListVirtualInterfaceRequest.


        :param page_size: The page_size of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The sort_by of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListVirtualInterfaceRequest.


        :param sort_by: The sort_by of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The sort_order of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListVirtualInterfaceRequest.


        :param sort_order: The sort_order of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def state(self):
        """Gets the state of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The state of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListVirtualInterfaceRequest.


        :param state: The state of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The vif_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this ListVirtualInterfaceRequest.


        :param vif_instance_id: The vif_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._vif_instance_id = vif_instance_id

    @property
    def vifvgw_instance_id(self):
        """Gets the vifvgw_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501


        :return: The vifvgw_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vifvgw_instance_id

    @vifvgw_instance_id.setter
    def vifvgw_instance_id(self, vifvgw_instance_id):
        """Sets the vifvgw_instance_id of this ListVirtualInterfaceRequest.


        :param vifvgw_instance_id: The vifvgw_instance_id of this ListVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._vifvgw_instance_id = vifvgw_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListVirtualInterfaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListVirtualInterfaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListVirtualInterfaceRequest):
            return True

        return self.to_dict() != other.to_dict()
