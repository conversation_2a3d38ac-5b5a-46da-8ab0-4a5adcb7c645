# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StreamForListStreamsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ai': 'AIForListStreamsOutput',
        'channel_id': 'str',
        'created_at': 'str',
        'description': 'str',
        'device_id': 'str',
        'device_nsid': 'str',
        'pull_urls': 'list[str]',
        'recent_push_ts': 'str',
        'record': 'RecordForListStreamsOutput',
        'resolution': 'str',
        'rtp_transport_tcp': 'bool',
        'sipid': 'str',
        'screenshot': 'ScreenshotForListStreamsOutput',
        'space_access_type': 'str',
        'space_id': 'str',
        'status': 'str',
        'stream_id': 'str',
        'stream_name': 'str',
        'streaming_type': 'str',
        'trans_pull_urls': 'TransPullUrlsForListStreamsOutput',
        'updated_at': 'str'
    }

    attribute_map = {
        'ai': 'AI',
        'channel_id': 'ChannelID',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'device_id': 'DeviceID',
        'device_nsid': 'DeviceNSID',
        'pull_urls': 'PullUrls',
        'recent_push_ts': 'RecentPushTs',
        'record': 'Record',
        'resolution': 'Resolution',
        'rtp_transport_tcp': 'RtpTransportTcp',
        'sipid': 'SIPID',
        'screenshot': 'Screenshot',
        'space_access_type': 'SpaceAccessType',
        'space_id': 'SpaceID',
        'status': 'Status',
        'stream_id': 'StreamID',
        'stream_name': 'StreamName',
        'streaming_type': 'StreamingType',
        'trans_pull_urls': 'TransPullUrls',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, ai=None, channel_id=None, created_at=None, description=None, device_id=None, device_nsid=None, pull_urls=None, recent_push_ts=None, record=None, resolution=None, rtp_transport_tcp=None, sipid=None, screenshot=None, space_access_type=None, space_id=None, status=None, stream_id=None, stream_name=None, streaming_type=None, trans_pull_urls=None, updated_at=None, _configuration=None):  # noqa: E501
        """StreamForListStreamsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ai = None
        self._channel_id = None
        self._created_at = None
        self._description = None
        self._device_id = None
        self._device_nsid = None
        self._pull_urls = None
        self._recent_push_ts = None
        self._record = None
        self._resolution = None
        self._rtp_transport_tcp = None
        self._sipid = None
        self._screenshot = None
        self._space_access_type = None
        self._space_id = None
        self._status = None
        self._stream_id = None
        self._stream_name = None
        self._streaming_type = None
        self._trans_pull_urls = None
        self._updated_at = None
        self.discriminator = None

        if ai is not None:
            self.ai = ai
        if channel_id is not None:
            self.channel_id = channel_id
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if device_id is not None:
            self.device_id = device_id
        if device_nsid is not None:
            self.device_nsid = device_nsid
        if pull_urls is not None:
            self.pull_urls = pull_urls
        if recent_push_ts is not None:
            self.recent_push_ts = recent_push_ts
        if record is not None:
            self.record = record
        if resolution is not None:
            self.resolution = resolution
        if rtp_transport_tcp is not None:
            self.rtp_transport_tcp = rtp_transport_tcp
        if sipid is not None:
            self.sipid = sipid
        if screenshot is not None:
            self.screenshot = screenshot
        if space_access_type is not None:
            self.space_access_type = space_access_type
        if space_id is not None:
            self.space_id = space_id
        if status is not None:
            self.status = status
        if stream_id is not None:
            self.stream_id = stream_id
        if stream_name is not None:
            self.stream_name = stream_name
        if streaming_type is not None:
            self.streaming_type = streaming_type
        if trans_pull_urls is not None:
            self.trans_pull_urls = trans_pull_urls
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def ai(self):
        """Gets the ai of this StreamForListStreamsOutput.  # noqa: E501


        :return: The ai of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: AIForListStreamsOutput
        """
        return self._ai

    @ai.setter
    def ai(self, ai):
        """Sets the ai of this StreamForListStreamsOutput.


        :param ai: The ai of this StreamForListStreamsOutput.  # noqa: E501
        :type: AIForListStreamsOutput
        """

        self._ai = ai

    @property
    def channel_id(self):
        """Gets the channel_id of this StreamForListStreamsOutput.  # noqa: E501


        :return: The channel_id of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_id

    @channel_id.setter
    def channel_id(self, channel_id):
        """Sets the channel_id of this StreamForListStreamsOutput.


        :param channel_id: The channel_id of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._channel_id = channel_id

    @property
    def created_at(self):
        """Gets the created_at of this StreamForListStreamsOutput.  # noqa: E501


        :return: The created_at of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this StreamForListStreamsOutput.


        :param created_at: The created_at of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this StreamForListStreamsOutput.  # noqa: E501


        :return: The description of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this StreamForListStreamsOutput.


        :param description: The description of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def device_id(self):
        """Gets the device_id of this StreamForListStreamsOutput.  # noqa: E501


        :return: The device_id of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this StreamForListStreamsOutput.


        :param device_id: The device_id of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._device_id = device_id

    @property
    def device_nsid(self):
        """Gets the device_nsid of this StreamForListStreamsOutput.  # noqa: E501


        :return: The device_nsid of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this StreamForListStreamsOutput.


        :param device_nsid: The device_nsid of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def pull_urls(self):
        """Gets the pull_urls of this StreamForListStreamsOutput.  # noqa: E501


        :return: The pull_urls of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._pull_urls

    @pull_urls.setter
    def pull_urls(self, pull_urls):
        """Sets the pull_urls of this StreamForListStreamsOutput.


        :param pull_urls: The pull_urls of this StreamForListStreamsOutput.  # noqa: E501
        :type: list[str]
        """

        self._pull_urls = pull_urls

    @property
    def recent_push_ts(self):
        """Gets the recent_push_ts of this StreamForListStreamsOutput.  # noqa: E501


        :return: The recent_push_ts of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._recent_push_ts

    @recent_push_ts.setter
    def recent_push_ts(self, recent_push_ts):
        """Sets the recent_push_ts of this StreamForListStreamsOutput.


        :param recent_push_ts: The recent_push_ts of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._recent_push_ts = recent_push_ts

    @property
    def record(self):
        """Gets the record of this StreamForListStreamsOutput.  # noqa: E501


        :return: The record of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: RecordForListStreamsOutput
        """
        return self._record

    @record.setter
    def record(self, record):
        """Sets the record of this StreamForListStreamsOutput.


        :param record: The record of this StreamForListStreamsOutput.  # noqa: E501
        :type: RecordForListStreamsOutput
        """

        self._record = record

    @property
    def resolution(self):
        """Gets the resolution of this StreamForListStreamsOutput.  # noqa: E501


        :return: The resolution of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this StreamForListStreamsOutput.


        :param resolution: The resolution of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._resolution = resolution

    @property
    def rtp_transport_tcp(self):
        """Gets the rtp_transport_tcp of this StreamForListStreamsOutput.  # noqa: E501


        :return: The rtp_transport_tcp of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._rtp_transport_tcp

    @rtp_transport_tcp.setter
    def rtp_transport_tcp(self, rtp_transport_tcp):
        """Sets the rtp_transport_tcp of this StreamForListStreamsOutput.


        :param rtp_transport_tcp: The rtp_transport_tcp of this StreamForListStreamsOutput.  # noqa: E501
        :type: bool
        """

        self._rtp_transport_tcp = rtp_transport_tcp

    @property
    def sipid(self):
        """Gets the sipid of this StreamForListStreamsOutput.  # noqa: E501


        :return: The sipid of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._sipid

    @sipid.setter
    def sipid(self, sipid):
        """Sets the sipid of this StreamForListStreamsOutput.


        :param sipid: The sipid of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._sipid = sipid

    @property
    def screenshot(self):
        """Gets the screenshot of this StreamForListStreamsOutput.  # noqa: E501


        :return: The screenshot of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: ScreenshotForListStreamsOutput
        """
        return self._screenshot

    @screenshot.setter
    def screenshot(self, screenshot):
        """Sets the screenshot of this StreamForListStreamsOutput.


        :param screenshot: The screenshot of this StreamForListStreamsOutput.  # noqa: E501
        :type: ScreenshotForListStreamsOutput
        """

        self._screenshot = screenshot

    @property
    def space_access_type(self):
        """Gets the space_access_type of this StreamForListStreamsOutput.  # noqa: E501


        :return: The space_access_type of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_access_type

    @space_access_type.setter
    def space_access_type(self, space_access_type):
        """Sets the space_access_type of this StreamForListStreamsOutput.


        :param space_access_type: The space_access_type of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._space_access_type = space_access_type

    @property
    def space_id(self):
        """Gets the space_id of this StreamForListStreamsOutput.  # noqa: E501


        :return: The space_id of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this StreamForListStreamsOutput.


        :param space_id: The space_id of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def status(self):
        """Gets the status of this StreamForListStreamsOutput.  # noqa: E501


        :return: The status of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this StreamForListStreamsOutput.


        :param status: The status of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stream_id(self):
        """Gets the stream_id of this StreamForListStreamsOutput.  # noqa: E501


        :return: The stream_id of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this StreamForListStreamsOutput.


        :param stream_id: The stream_id of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._stream_id = stream_id

    @property
    def stream_name(self):
        """Gets the stream_name of this StreamForListStreamsOutput.  # noqa: E501


        :return: The stream_name of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stream_name

    @stream_name.setter
    def stream_name(self, stream_name):
        """Sets the stream_name of this StreamForListStreamsOutput.


        :param stream_name: The stream_name of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._stream_name = stream_name

    @property
    def streaming_type(self):
        """Gets the streaming_type of this StreamForListStreamsOutput.  # noqa: E501


        :return: The streaming_type of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._streaming_type

    @streaming_type.setter
    def streaming_type(self, streaming_type):
        """Sets the streaming_type of this StreamForListStreamsOutput.


        :param streaming_type: The streaming_type of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._streaming_type = streaming_type

    @property
    def trans_pull_urls(self):
        """Gets the trans_pull_urls of this StreamForListStreamsOutput.  # noqa: E501


        :return: The trans_pull_urls of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: TransPullUrlsForListStreamsOutput
        """
        return self._trans_pull_urls

    @trans_pull_urls.setter
    def trans_pull_urls(self, trans_pull_urls):
        """Sets the trans_pull_urls of this StreamForListStreamsOutput.


        :param trans_pull_urls: The trans_pull_urls of this StreamForListStreamsOutput.  # noqa: E501
        :type: TransPullUrlsForListStreamsOutput
        """

        self._trans_pull_urls = trans_pull_urls

    @property
    def updated_at(self):
        """Gets the updated_at of this StreamForListStreamsOutput.  # noqa: E501


        :return: The updated_at of this StreamForListStreamsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this StreamForListStreamsOutput.


        :param updated_at: The updated_at of this StreamForListStreamsOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StreamForListStreamsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StreamForListStreamsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StreamForListStreamsOutput):
            return True

        return self.to_dict() != other.to_dict()
