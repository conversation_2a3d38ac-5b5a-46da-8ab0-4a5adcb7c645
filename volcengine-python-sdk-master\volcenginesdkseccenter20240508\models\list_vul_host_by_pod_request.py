# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListVulHostByPodRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_providers': 'list[str]',
        'cluster_id': 'str',
        'cwpp_id': 'str',
        'host_name': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[str]',
        'top_group_id': 'str',
        'workload_id': 'str'
    }

    attribute_map = {
        'cloud_providers': 'CloudProviders',
        'cluster_id': 'ClusterID',
        'cwpp_id': 'CwppID',
        'host_name': 'HostName',
        'ip': 'IP',
        'leaf_group_ids': 'LeafGroupIDs',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'top_group_id': 'TopGroupID',
        'workload_id': 'WorkloadID'
    }

    def __init__(self, cloud_providers=None, cluster_id=None, cwpp_id=None, host_name=None, ip=None, leaf_group_ids=None, page_number=None, page_size=None, sort_by=None, sort_order=None, status=None, top_group_id=None, workload_id=None, _configuration=None):  # noqa: E501
        """ListVulHostByPodRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_providers = None
        self._cluster_id = None
        self._cwpp_id = None
        self._host_name = None
        self._ip = None
        self._leaf_group_ids = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._top_group_id = None
        self._workload_id = None
        self.discriminator = None

        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        self.cluster_id = cluster_id
        if cwpp_id is not None:
            self.cwpp_id = cwpp_id
        if host_name is not None:
            self.host_name = host_name
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if top_group_id is not None:
            self.top_group_id = top_group_id
        self.workload_id = workload_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListVulHostByPodRequest.  # noqa: E501


        :return: The cloud_providers of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListVulHostByPodRequest.


        :param cloud_providers: The cloud_providers of this ListVulHostByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListVulHostByPodRequest.  # noqa: E501


        :return: The cluster_id of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListVulHostByPodRequest.


        :param cluster_id: The cluster_id of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def cwpp_id(self):
        """Gets the cwpp_id of this ListVulHostByPodRequest.  # noqa: E501


        :return: The cwpp_id of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._cwpp_id

    @cwpp_id.setter
    def cwpp_id(self, cwpp_id):
        """Sets the cwpp_id of this ListVulHostByPodRequest.


        :param cwpp_id: The cwpp_id of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._cwpp_id = cwpp_id

    @property
    def host_name(self):
        """Gets the host_name of this ListVulHostByPodRequest.  # noqa: E501


        :return: The host_name of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this ListVulHostByPodRequest.


        :param host_name: The host_name of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def ip(self):
        """Gets the ip of this ListVulHostByPodRequest.  # noqa: E501


        :return: The ip of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListVulHostByPodRequest.


        :param ip: The ip of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListVulHostByPodRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListVulHostByPodRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListVulHostByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def page_number(self):
        """Gets the page_number of this ListVulHostByPodRequest.  # noqa: E501


        :return: The page_number of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListVulHostByPodRequest.


        :param page_number: The page_number of this ListVulHostByPodRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListVulHostByPodRequest.  # noqa: E501


        :return: The page_size of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListVulHostByPodRequest.


        :param page_size: The page_size of this ListVulHostByPodRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListVulHostByPodRequest.  # noqa: E501


        :return: The sort_by of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListVulHostByPodRequest.


        :param sort_by: The sort_by of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListVulHostByPodRequest.  # noqa: E501


        :return: The sort_order of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListVulHostByPodRequest.


        :param sort_order: The sort_order of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListVulHostByPodRequest.  # noqa: E501


        :return: The status of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListVulHostByPodRequest.


        :param status: The status of this ListVulHostByPodRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListVulHostByPodRequest.  # noqa: E501


        :return: The top_group_id of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListVulHostByPodRequest.


        :param top_group_id: The top_group_id of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def workload_id(self):
        """Gets the workload_id of this ListVulHostByPodRequest.  # noqa: E501


        :return: The workload_id of this ListVulHostByPodRequest.  # noqa: E501
        :rtype: str
        """
        return self._workload_id

    @workload_id.setter
    def workload_id(self, workload_id):
        """Sets the workload_id of this ListVulHostByPodRequest.


        :param workload_id: The workload_id of this ListVulHostByPodRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workload_id is None:
            raise ValueError("Invalid value for `workload_id`, must not be `None`")  # noqa: E501

        self._workload_id = workload_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListVulHostByPodRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListVulHostByPodRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListVulHostByPodRequest):
            return True

        return self.to_dict() != other.to_dict()
