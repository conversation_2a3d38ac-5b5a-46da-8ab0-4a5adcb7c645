# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListPublicBandwidthPackagesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'bandwidth_package_id': 'str',
        'bandwidth_type': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'resource_tag_filter': 'ResourceTagFilterForListPublicBandwidthPackagesInput',
        'state': 'str',
        'tags': 'list[TagForListPublicBandwidthPackagesInput]'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'bandwidth_package_id': 'BandwidthPackageId',
        'bandwidth_type': 'BandwidthType',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'resource_tag_filter': 'ResourceTagFilter',
        'state': 'State',
        'tags': 'Tags'
    }

    def __init__(self, accelerator_id=None, bandwidth_package_id=None, bandwidth_type=None, page_num=None, page_size=None, project_name=None, resource_tag_filter=None, state=None, tags=None, _configuration=None):  # noqa: E501
        """ListPublicBandwidthPackagesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._bandwidth_package_id = None
        self._bandwidth_type = None
        self._page_num = None
        self._page_size = None
        self._project_name = None
        self._resource_tag_filter = None
        self._state = None
        self._tags = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if bandwidth_package_id is not None:
            self.bandwidth_package_id = bandwidth_package_id
        if bandwidth_type is not None:
            self.bandwidth_type = bandwidth_type
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if resource_tag_filter is not None:
            self.resource_tag_filter = resource_tag_filter
        if state is not None:
            self.state = state
        if tags is not None:
            self.tags = tags

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The accelerator_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this ListPublicBandwidthPackagesRequest.


        :param accelerator_id: The accelerator_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def bandwidth_package_id(self):
        """Gets the bandwidth_package_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The bandwidth_package_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_package_id

    @bandwidth_package_id.setter
    def bandwidth_package_id(self, bandwidth_package_id):
        """Sets the bandwidth_package_id of this ListPublicBandwidthPackagesRequest.


        :param bandwidth_package_id: The bandwidth_package_id of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_package_id = bandwidth_package_id

    @property
    def bandwidth_type(self):
        """Gets the bandwidth_type of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The bandwidth_type of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_type

    @bandwidth_type.setter
    def bandwidth_type(self, bandwidth_type):
        """Sets the bandwidth_type of this ListPublicBandwidthPackagesRequest.


        :param bandwidth_type: The bandwidth_type of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_type = bandwidth_type

    @property
    def page_num(self):
        """Gets the page_num of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The page_num of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListPublicBandwidthPackagesRequest.


        :param page_num: The page_num of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The page_size of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListPublicBandwidthPackagesRequest.


        :param page_size: The page_size of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The project_name of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListPublicBandwidthPackagesRequest.


        :param project_name: The project_name of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_tag_filter(self):
        """Gets the resource_tag_filter of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The resource_tag_filter of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: ResourceTagFilterForListPublicBandwidthPackagesInput
        """
        return self._resource_tag_filter

    @resource_tag_filter.setter
    def resource_tag_filter(self, resource_tag_filter):
        """Sets the resource_tag_filter of this ListPublicBandwidthPackagesRequest.


        :param resource_tag_filter: The resource_tag_filter of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: ResourceTagFilterForListPublicBandwidthPackagesInput
        """

        self._resource_tag_filter = resource_tag_filter

    @property
    def state(self):
        """Gets the state of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The state of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListPublicBandwidthPackagesRequest.


        :param state: The state of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def tags(self):
        """Gets the tags of this ListPublicBandwidthPackagesRequest.  # noqa: E501


        :return: The tags of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :rtype: list[TagForListPublicBandwidthPackagesInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListPublicBandwidthPackagesRequest.


        :param tags: The tags of this ListPublicBandwidthPackagesRequest.  # noqa: E501
        :type: list[TagForListPublicBandwidthPackagesInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListPublicBandwidthPackagesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListPublicBandwidthPackagesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListPublicBandwidthPackagesRequest):
            return True

        return self.to_dict() != other.to_dict()
