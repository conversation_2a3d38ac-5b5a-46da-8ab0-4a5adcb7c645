# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDiagnosticsInfosResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnostics_infos': 'list[DiagnosticsInfoForDescribeDiagnosticsInfosOutput]',
        'diagnostics_task_status': 'str',
        'last_diagnostics_time': 'str',
        'total': 'int'
    }

    attribute_map = {
        'diagnostics_infos': 'DiagnosticsInfos',
        'diagnostics_task_status': 'DiagnosticsTaskStatus',
        'last_diagnostics_time': 'LastDiagnosticsTime',
        'total': 'Total'
    }

    def __init__(self, diagnostics_infos=None, diagnostics_task_status=None, last_diagnostics_time=None, total=None, _configuration=None):  # noqa: E501
        """DescribeDiagnosticsInfosResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnostics_infos = None
        self._diagnostics_task_status = None
        self._last_diagnostics_time = None
        self._total = None
        self.discriminator = None

        if diagnostics_infos is not None:
            self.diagnostics_infos = diagnostics_infos
        if diagnostics_task_status is not None:
            self.diagnostics_task_status = diagnostics_task_status
        if last_diagnostics_time is not None:
            self.last_diagnostics_time = last_diagnostics_time
        if total is not None:
            self.total = total

    @property
    def diagnostics_infos(self):
        """Gets the diagnostics_infos of this DescribeDiagnosticsInfosResponse.  # noqa: E501


        :return: The diagnostics_infos of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :rtype: list[DiagnosticsInfoForDescribeDiagnosticsInfosOutput]
        """
        return self._diagnostics_infos

    @diagnostics_infos.setter
    def diagnostics_infos(self, diagnostics_infos):
        """Sets the diagnostics_infos of this DescribeDiagnosticsInfosResponse.


        :param diagnostics_infos: The diagnostics_infos of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :type: list[DiagnosticsInfoForDescribeDiagnosticsInfosOutput]
        """

        self._diagnostics_infos = diagnostics_infos

    @property
    def diagnostics_task_status(self):
        """Gets the diagnostics_task_status of this DescribeDiagnosticsInfosResponse.  # noqa: E501


        :return: The diagnostics_task_status of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :rtype: str
        """
        return self._diagnostics_task_status

    @diagnostics_task_status.setter
    def diagnostics_task_status(self, diagnostics_task_status):
        """Sets the diagnostics_task_status of this DescribeDiagnosticsInfosResponse.


        :param diagnostics_task_status: The diagnostics_task_status of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :type: str
        """

        self._diagnostics_task_status = diagnostics_task_status

    @property
    def last_diagnostics_time(self):
        """Gets the last_diagnostics_time of this DescribeDiagnosticsInfosResponse.  # noqa: E501


        :return: The last_diagnostics_time of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_diagnostics_time

    @last_diagnostics_time.setter
    def last_diagnostics_time(self, last_diagnostics_time):
        """Sets the last_diagnostics_time of this DescribeDiagnosticsInfosResponse.


        :param last_diagnostics_time: The last_diagnostics_time of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :type: str
        """

        self._last_diagnostics_time = last_diagnostics_time

    @property
    def total(self):
        """Gets the total of this DescribeDiagnosticsInfosResponse.  # noqa: E501


        :return: The total of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeDiagnosticsInfosResponse.


        :param total: The total of this DescribeDiagnosticsInfosResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDiagnosticsInfosResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDiagnosticsInfosResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDiagnosticsInfosResponse):
            return True

        return self.to_dict() != other.to_dict()
