# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyUserAuthorityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all_authority': 'bool',
        'instance_id': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'all_authority': 'AllAuthority',
        'instance_id': 'InstanceId',
        'user_name': 'UserName'
    }

    def __init__(self, all_authority=None, instance_id=None, user_name=None, _configuration=None):  # noqa: E501
        """ModifyUserAuthorityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all_authority = None
        self._instance_id = None
        self._user_name = None
        self.discriminator = None

        self.all_authority = all_authority
        self.instance_id = instance_id
        self.user_name = user_name

    @property
    def all_authority(self):
        """Gets the all_authority of this ModifyUserAuthorityRequest.  # noqa: E501


        :return: The all_authority of this ModifyUserAuthorityRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this ModifyUserAuthorityRequest.


        :param all_authority: The all_authority of this ModifyUserAuthorityRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and all_authority is None:
            raise ValueError("Invalid value for `all_authority`, must not be `None`")  # noqa: E501

        self._all_authority = all_authority

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyUserAuthorityRequest.  # noqa: E501


        :return: The instance_id of this ModifyUserAuthorityRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyUserAuthorityRequest.


        :param instance_id: The instance_id of this ModifyUserAuthorityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def user_name(self):
        """Gets the user_name of this ModifyUserAuthorityRequest.  # noqa: E501


        :return: The user_name of this ModifyUserAuthorityRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ModifyUserAuthorityRequest.


        :param user_name: The user_name of this ModifyUserAuthorityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and user_name is None:
            raise ValueError("Invalid value for `user_name`, must not be `None`")  # noqa: E501

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyUserAuthorityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyUserAuthorityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyUserAuthorityRequest):
            return True

        return self.to_dict() != other.to_dict()
