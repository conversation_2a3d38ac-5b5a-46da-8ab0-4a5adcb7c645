# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RebroadcastActivityForGetActivityPartnerRebroadcastOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'cover_image': 'str',
        'name': 'str',
        'view_page_url': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'cover_image': 'CoverImage',
        'name': 'Name',
        'view_page_url': 'ViewPageUrl'
    }

    def __init__(self, activity_id=None, cover_image=None, name=None, view_page_url=None, _configuration=None):  # noqa: E501
        """RebroadcastActivityForGetActivityPartnerRebroadcastOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._cover_image = None
        self._name = None
        self._view_page_url = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if cover_image is not None:
            self.cover_image = cover_image
        if name is not None:
            self.name = name
        if view_page_url is not None:
            self.view_page_url = view_page_url

    @property
    def activity_id(self):
        """Gets the activity_id of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501


        :return: The activity_id of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.


        :param activity_id: The activity_id of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def cover_image(self):
        """Gets the cover_image of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501


        :return: The cover_image of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.


        :param cover_image: The cover_image of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def name(self):
        """Gets the name of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501


        :return: The name of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.


        :param name: The name of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def view_page_url(self):
        """Gets the view_page_url of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501


        :return: The view_page_url of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :rtype: str
        """
        return self._view_page_url

    @view_page_url.setter
    def view_page_url(self, view_page_url):
        """Sets the view_page_url of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.


        :param view_page_url: The view_page_url of this RebroadcastActivityForGetActivityPartnerRebroadcastOutput.  # noqa: E501
        :type: str
        """

        self._view_page_url = view_page_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RebroadcastActivityForGetActivityPartnerRebroadcastOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RebroadcastActivityForGetActivityPartnerRebroadcastOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RebroadcastActivityForGetActivityPartnerRebroadcastOutput):
            return True

        return self.to_dict() != other.to_dict()
