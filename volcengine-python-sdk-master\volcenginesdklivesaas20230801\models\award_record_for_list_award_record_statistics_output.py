# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AwardRecordForListAwardRecordStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_address': 'str',
        'award_id': 'int',
        'award_item_name': 'str',
        'award_name': 'str',
        'award_result': 'int',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'is_priority_user': 'int',
        'login_tel': 'str',
        'notify_result': 'int',
        'open_award_time': 'int',
        'receiver_name': 'str',
        'receiver_tel': 'str',
        'user_agent': 'str',
        'user_id': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_address': 'AwardAddress',
        'award_id': 'AwardId',
        'award_item_name': 'AwardItemName',
        'award_name': 'AwardName',
        'award_result': 'AwardResult',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'is_priority_user': 'IsPriorityUser',
        'login_tel': 'LoginTel',
        'notify_result': 'NotifyResult',
        'open_award_time': 'OpenAwardTime',
        'receiver_name': 'ReceiverName',
        'receiver_tel': 'ReceiverTel',
        'user_agent': 'UserAgent',
        'user_id': 'UserId',
        'user_name': 'UserName'
    }

    def __init__(self, activity_id=None, award_address=None, award_id=None, award_item_name=None, award_name=None, award_result=None, email=None, external_id=None, extra=None, ip=None, is_priority_user=None, login_tel=None, notify_result=None, open_award_time=None, receiver_name=None, receiver_tel=None, user_agent=None, user_id=None, user_name=None, _configuration=None):  # noqa: E501
        """AwardRecordForListAwardRecordStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_address = None
        self._award_id = None
        self._award_item_name = None
        self._award_name = None
        self._award_result = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._is_priority_user = None
        self._login_tel = None
        self._notify_result = None
        self._open_award_time = None
        self._receiver_name = None
        self._receiver_tel = None
        self._user_agent = None
        self._user_id = None
        self._user_name = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if award_address is not None:
            self.award_address = award_address
        if award_id is not None:
            self.award_id = award_id
        if award_item_name is not None:
            self.award_item_name = award_item_name
        if award_name is not None:
            self.award_name = award_name
        if award_result is not None:
            self.award_result = award_result
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if is_priority_user is not None:
            self.is_priority_user = is_priority_user
        if login_tel is not None:
            self.login_tel = login_tel
        if notify_result is not None:
            self.notify_result = notify_result
        if open_award_time is not None:
            self.open_award_time = open_award_time
        if receiver_name is not None:
            self.receiver_name = receiver_name
        if receiver_tel is not None:
            self.receiver_tel = receiver_tel
        if user_agent is not None:
            self.user_agent = user_agent
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name

    @property
    def activity_id(self):
        """Gets the activity_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The activity_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this AwardRecordForListAwardRecordStatisticsOutput.


        :param activity_id: The activity_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def award_address(self):
        """Gets the award_address of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The award_address of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_address

    @award_address.setter
    def award_address(self, award_address):
        """Sets the award_address of this AwardRecordForListAwardRecordStatisticsOutput.


        :param award_address: The award_address of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._award_address = award_address

    @property
    def award_id(self):
        """Gets the award_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The award_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_id

    @award_id.setter
    def award_id(self, award_id):
        """Sets the award_id of this AwardRecordForListAwardRecordStatisticsOutput.


        :param award_id: The award_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._award_id = award_id

    @property
    def award_item_name(self):
        """Gets the award_item_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The award_item_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_name

    @award_item_name.setter
    def award_item_name(self, award_item_name):
        """Sets the award_item_name of this AwardRecordForListAwardRecordStatisticsOutput.


        :param award_item_name: The award_item_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._award_item_name = award_item_name

    @property
    def award_name(self):
        """Gets the award_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The award_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_name

    @award_name.setter
    def award_name(self, award_name):
        """Sets the award_name of this AwardRecordForListAwardRecordStatisticsOutput.


        :param award_name: The award_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._award_name = award_name

    @property
    def award_result(self):
        """Gets the award_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The award_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_result

    @award_result.setter
    def award_result(self, award_result):
        """Sets the award_result of this AwardRecordForListAwardRecordStatisticsOutput.


        :param award_result: The award_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._award_result = award_result

    @property
    def email(self):
        """Gets the email of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The email of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this AwardRecordForListAwardRecordStatisticsOutput.


        :param email: The email of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The external_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this AwardRecordForListAwardRecordStatisticsOutput.


        :param external_id: The external_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The extra of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this AwardRecordForListAwardRecordStatisticsOutput.


        :param extra: The extra of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The ip of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this AwardRecordForListAwardRecordStatisticsOutput.


        :param ip: The ip of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_priority_user(self):
        """Gets the is_priority_user of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The is_priority_user of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_priority_user

    @is_priority_user.setter
    def is_priority_user(self, is_priority_user):
        """Sets the is_priority_user of this AwardRecordForListAwardRecordStatisticsOutput.


        :param is_priority_user: The is_priority_user of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._is_priority_user = is_priority_user

    @property
    def login_tel(self):
        """Gets the login_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The login_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._login_tel

    @login_tel.setter
    def login_tel(self, login_tel):
        """Sets the login_tel of this AwardRecordForListAwardRecordStatisticsOutput.


        :param login_tel: The login_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._login_tel = login_tel

    @property
    def notify_result(self):
        """Gets the notify_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The notify_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._notify_result

    @notify_result.setter
    def notify_result(self, notify_result):
        """Sets the notify_result of this AwardRecordForListAwardRecordStatisticsOutput.


        :param notify_result: The notify_result of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._notify_result = notify_result

    @property
    def open_award_time(self):
        """Gets the open_award_time of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The open_award_time of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._open_award_time

    @open_award_time.setter
    def open_award_time(self, open_award_time):
        """Sets the open_award_time of this AwardRecordForListAwardRecordStatisticsOutput.


        :param open_award_time: The open_award_time of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._open_award_time = open_award_time

    @property
    def receiver_name(self):
        """Gets the receiver_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The receiver_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._receiver_name

    @receiver_name.setter
    def receiver_name(self, receiver_name):
        """Sets the receiver_name of this AwardRecordForListAwardRecordStatisticsOutput.


        :param receiver_name: The receiver_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._receiver_name = receiver_name

    @property
    def receiver_tel(self):
        """Gets the receiver_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The receiver_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._receiver_tel

    @receiver_tel.setter
    def receiver_tel(self, receiver_tel):
        """Sets the receiver_tel of this AwardRecordForListAwardRecordStatisticsOutput.


        :param receiver_tel: The receiver_tel of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._receiver_tel = receiver_tel

    @property
    def user_agent(self):
        """Gets the user_agent of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The user_agent of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this AwardRecordForListAwardRecordStatisticsOutput.


        :param user_agent: The user_agent of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_id(self):
        """Gets the user_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The user_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this AwardRecordForListAwardRecordStatisticsOutput.


        :param user_id: The user_id of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501


        :return: The user_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this AwardRecordForListAwardRecordStatisticsOutput.


        :param user_name: The user_name of this AwardRecordForListAwardRecordStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AwardRecordForListAwardRecordStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AwardRecordForListAwardRecordStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AwardRecordForListAwardRecordStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
