# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListRegistriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'create_time': 'str',
        'expire_time': 'str',
        'name': 'str',
        'project': 'str',
        'proxy_cache': 'ProxyCacheForListRegistriesOutput',
        'proxy_cache_enabled': 'bool',
        'renew_type': 'str',
        'resource_tags': 'list[ResourceTagForListRegistriesOutput]',
        'status': 'StatusForListRegistriesOutput',
        'type': 'str'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'create_time': 'CreateTime',
        'expire_time': 'ExpireTime',
        'name': 'Name',
        'project': 'Project',
        'proxy_cache': 'ProxyCache',
        'proxy_cache_enabled': 'ProxyCacheEnabled',
        'renew_type': 'RenewType',
        'resource_tags': 'ResourceTags',
        'status': 'Status',
        'type': 'Type'
    }

    def __init__(self, charge_type=None, create_time=None, expire_time=None, name=None, project=None, proxy_cache=None, proxy_cache_enabled=None, renew_type=None, resource_tags=None, status=None, type=None, _configuration=None):  # noqa: E501
        """ItemForListRegistriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._create_time = None
        self._expire_time = None
        self._name = None
        self._project = None
        self._proxy_cache = None
        self._proxy_cache_enabled = None
        self._renew_type = None
        self._resource_tags = None
        self._status = None
        self._type = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if create_time is not None:
            self.create_time = create_time
        if expire_time is not None:
            self.expire_time = expire_time
        if name is not None:
            self.name = name
        if project is not None:
            self.project = project
        if proxy_cache is not None:
            self.proxy_cache = proxy_cache
        if proxy_cache_enabled is not None:
            self.proxy_cache_enabled = proxy_cache_enabled
        if renew_type is not None:
            self.renew_type = renew_type
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type

    @property
    def charge_type(self):
        """Gets the charge_type of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The charge_type of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ItemForListRegistriesOutput.


        :param charge_type: The charge_type of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The create_time of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListRegistriesOutput.


        :param create_time: The create_time of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def expire_time(self):
        """Gets the expire_time of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The expire_time of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ItemForListRegistriesOutput.


        :param expire_time: The expire_time of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def name(self):
        """Gets the name of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The name of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListRegistriesOutput.


        :param name: The name of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project(self):
        """Gets the project of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The project of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ItemForListRegistriesOutput.


        :param project: The project of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def proxy_cache(self):
        """Gets the proxy_cache of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The proxy_cache of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: ProxyCacheForListRegistriesOutput
        """
        return self._proxy_cache

    @proxy_cache.setter
    def proxy_cache(self, proxy_cache):
        """Sets the proxy_cache of this ItemForListRegistriesOutput.


        :param proxy_cache: The proxy_cache of this ItemForListRegistriesOutput.  # noqa: E501
        :type: ProxyCacheForListRegistriesOutput
        """

        self._proxy_cache = proxy_cache

    @property
    def proxy_cache_enabled(self):
        """Gets the proxy_cache_enabled of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The proxy_cache_enabled of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._proxy_cache_enabled

    @proxy_cache_enabled.setter
    def proxy_cache_enabled(self, proxy_cache_enabled):
        """Sets the proxy_cache_enabled of this ItemForListRegistriesOutput.


        :param proxy_cache_enabled: The proxy_cache_enabled of this ItemForListRegistriesOutput.  # noqa: E501
        :type: bool
        """

        self._proxy_cache_enabled = proxy_cache_enabled

    @property
    def renew_type(self):
        """Gets the renew_type of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The renew_type of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this ItemForListRegistriesOutput.


        :param renew_type: The renew_type of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._renew_type = renew_type

    @property
    def resource_tags(self):
        """Gets the resource_tags of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The resource_tags of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: list[ResourceTagForListRegistriesOutput]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this ItemForListRegistriesOutput.


        :param resource_tags: The resource_tags of this ItemForListRegistriesOutput.  # noqa: E501
        :type: list[ResourceTagForListRegistriesOutput]
        """

        self._resource_tags = resource_tags

    @property
    def status(self):
        """Gets the status of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The status of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: StatusForListRegistriesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListRegistriesOutput.


        :param status: The status of this ItemForListRegistriesOutput.  # noqa: E501
        :type: StatusForListRegistriesOutput
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this ItemForListRegistriesOutput.  # noqa: E501


        :return: The type of this ItemForListRegistriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListRegistriesOutput.


        :param type: The type of this ItemForListRegistriesOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListRegistriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListRegistriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListRegistriesOutput):
            return True

        return self.to_dict() != other.to_dict()
