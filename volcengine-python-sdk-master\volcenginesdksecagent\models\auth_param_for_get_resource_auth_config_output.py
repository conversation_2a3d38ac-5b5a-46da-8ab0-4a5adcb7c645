# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthParamForGetResourceAuthConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enums': 'list[str]',
        'param_key': 'str',
        'param_name': 'str',
        'param_tips': 'str'
    }

    attribute_map = {
        'enums': 'Enums',
        'param_key': 'Param<PERSON><PERSON>',
        'param_name': 'ParamName',
        'param_tips': 'ParamTips'
    }

    def __init__(self, enums=None, param_key=None, param_name=None, param_tips=None, _configuration=None):  # noqa: E501
        """AuthParamForGetResourceAuthConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enums = None
        self._param_key = None
        self._param_name = None
        self._param_tips = None
        self.discriminator = None

        if enums is not None:
            self.enums = enums
        if param_key is not None:
            self.param_key = param_key
        if param_name is not None:
            self.param_name = param_name
        if param_tips is not None:
            self.param_tips = param_tips

    @property
    def enums(self):
        """Gets the enums of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The enums of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._enums

    @enums.setter
    def enums(self, enums):
        """Sets the enums of this AuthParamForGetResourceAuthConfigOutput.


        :param enums: The enums of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :type: list[str]
        """

        self._enums = enums

    @property
    def param_key(self):
        """Gets the param_key of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The param_key of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._param_key

    @param_key.setter
    def param_key(self, param_key):
        """Sets the param_key of this AuthParamForGetResourceAuthConfigOutput.


        :param param_key: The param_key of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :type: str
        """

        self._param_key = param_key

    @property
    def param_name(self):
        """Gets the param_name of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The param_name of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._param_name

    @param_name.setter
    def param_name(self, param_name):
        """Sets the param_name of this AuthParamForGetResourceAuthConfigOutput.


        :param param_name: The param_name of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :type: str
        """

        self._param_name = param_name

    @property
    def param_tips(self):
        """Gets the param_tips of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501


        :return: The param_tips of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._param_tips

    @param_tips.setter
    def param_tips(self, param_tips):
        """Sets the param_tips of this AuthParamForGetResourceAuthConfigOutput.


        :param param_tips: The param_tips of this AuthParamForGetResourceAuthConfigOutput.  # noqa: E501
        :type: str
        """

        self._param_tips = param_tips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthParamForGetResourceAuthConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthParamForGetResourceAuthConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthParamForGetResourceAuthConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
