# coding: utf-8

"""
    flink20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetApplicationInstanceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'application_id': 'str',
        'args': 'str',
        'complete_rest_url': 'str',
        'conf': 'str',
        'dependency': 'DependencyForGetApplicationInstanceOutput',
        'deploy_request': 'DeployRequestForGetApplicationInstanceOutput',
        'deployment_id': 'str',
        'end_time': 'str',
        'engine_version': 'str',
        'id': 'str',
        'jar': 'str',
        'job_id': 'str',
        'job_name': 'str',
        'job_type': 'str',
        'main_class': 'str',
        'project_id': 'str',
        'sql_text': 'str',
        'start_time': 'str',
        'state': 'str',
        'user_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'application_id': 'ApplicationId',
        'args': 'Args',
        'complete_rest_url': 'CompleteRestUrl',
        'conf': 'Conf',
        'dependency': 'Dependency',
        'deploy_request': 'DeployRequest',
        'deployment_id': 'DeploymentId',
        'end_time': 'EndTime',
        'engine_version': 'EngineVersion',
        'id': 'Id',
        'jar': 'Jar',
        'job_id': 'JobId',
        'job_name': 'JobName',
        'job_type': 'JobType',
        'main_class': 'MainClass',
        'project_id': 'ProjectId',
        'sql_text': 'SqlText',
        'start_time': 'StartTime',
        'state': 'State',
        'user_id': 'UserId'
    }

    def __init__(self, account_id=None, application_id=None, args=None, complete_rest_url=None, conf=None, dependency=None, deploy_request=None, deployment_id=None, end_time=None, engine_version=None, id=None, jar=None, job_id=None, job_name=None, job_type=None, main_class=None, project_id=None, sql_text=None, start_time=None, state=None, user_id=None, _configuration=None):  # noqa: E501
        """GetApplicationInstanceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._application_id = None
        self._args = None
        self._complete_rest_url = None
        self._conf = None
        self._dependency = None
        self._deploy_request = None
        self._deployment_id = None
        self._end_time = None
        self._engine_version = None
        self._id = None
        self._jar = None
        self._job_id = None
        self._job_name = None
        self._job_type = None
        self._main_class = None
        self._project_id = None
        self._sql_text = None
        self._start_time = None
        self._state = None
        self._user_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if application_id is not None:
            self.application_id = application_id
        if args is not None:
            self.args = args
        if complete_rest_url is not None:
            self.complete_rest_url = complete_rest_url
        if conf is not None:
            self.conf = conf
        if dependency is not None:
            self.dependency = dependency
        if deploy_request is not None:
            self.deploy_request = deploy_request
        if deployment_id is not None:
            self.deployment_id = deployment_id
        if end_time is not None:
            self.end_time = end_time
        if engine_version is not None:
            self.engine_version = engine_version
        if id is not None:
            self.id = id
        if jar is not None:
            self.jar = jar
        if job_id is not None:
            self.job_id = job_id
        if job_name is not None:
            self.job_name = job_name
        if job_type is not None:
            self.job_type = job_type
        if main_class is not None:
            self.main_class = main_class
        if project_id is not None:
            self.project_id = project_id
        if sql_text is not None:
            self.sql_text = sql_text
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state
        if user_id is not None:
            self.user_id = user_id

    @property
    def account_id(self):
        """Gets the account_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The account_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this GetApplicationInstanceResponse.


        :param account_id: The account_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def application_id(self):
        """Gets the application_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The application_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._application_id

    @application_id.setter
    def application_id(self, application_id):
        """Sets the application_id of this GetApplicationInstanceResponse.


        :param application_id: The application_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._application_id = application_id

    @property
    def args(self):
        """Gets the args of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The args of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._args

    @args.setter
    def args(self, args):
        """Sets the args of this GetApplicationInstanceResponse.


        :param args: The args of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._args = args

    @property
    def complete_rest_url(self):
        """Gets the complete_rest_url of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The complete_rest_url of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._complete_rest_url

    @complete_rest_url.setter
    def complete_rest_url(self, complete_rest_url):
        """Sets the complete_rest_url of this GetApplicationInstanceResponse.


        :param complete_rest_url: The complete_rest_url of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._complete_rest_url = complete_rest_url

    @property
    def conf(self):
        """Gets the conf of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The conf of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._conf

    @conf.setter
    def conf(self, conf):
        """Sets the conf of this GetApplicationInstanceResponse.


        :param conf: The conf of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._conf = conf

    @property
    def dependency(self):
        """Gets the dependency of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The dependency of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: DependencyForGetApplicationInstanceOutput
        """
        return self._dependency

    @dependency.setter
    def dependency(self, dependency):
        """Sets the dependency of this GetApplicationInstanceResponse.


        :param dependency: The dependency of this GetApplicationInstanceResponse.  # noqa: E501
        :type: DependencyForGetApplicationInstanceOutput
        """

        self._dependency = dependency

    @property
    def deploy_request(self):
        """Gets the deploy_request of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The deploy_request of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: DeployRequestForGetApplicationInstanceOutput
        """
        return self._deploy_request

    @deploy_request.setter
    def deploy_request(self, deploy_request):
        """Sets the deploy_request of this GetApplicationInstanceResponse.


        :param deploy_request: The deploy_request of this GetApplicationInstanceResponse.  # noqa: E501
        :type: DeployRequestForGetApplicationInstanceOutput
        """

        self._deploy_request = deploy_request

    @property
    def deployment_id(self):
        """Gets the deployment_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The deployment_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._deployment_id

    @deployment_id.setter
    def deployment_id(self, deployment_id):
        """Sets the deployment_id of this GetApplicationInstanceResponse.


        :param deployment_id: The deployment_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._deployment_id = deployment_id

    @property
    def end_time(self):
        """Gets the end_time of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The end_time of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetApplicationInstanceResponse.


        :param end_time: The end_time of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def engine_version(self):
        """Gets the engine_version of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The engine_version of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._engine_version

    @engine_version.setter
    def engine_version(self, engine_version):
        """Sets the engine_version of this GetApplicationInstanceResponse.


        :param engine_version: The engine_version of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._engine_version = engine_version

    @property
    def id(self):
        """Gets the id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetApplicationInstanceResponse.


        :param id: The id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def jar(self):
        """Gets the jar of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The jar of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._jar

    @jar.setter
    def jar(self, jar):
        """Sets the jar of this GetApplicationInstanceResponse.


        :param jar: The jar of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._jar = jar

    @property
    def job_id(self):
        """Gets the job_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The job_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._job_id

    @job_id.setter
    def job_id(self, job_id):
        """Sets the job_id of this GetApplicationInstanceResponse.


        :param job_id: The job_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._job_id = job_id

    @property
    def job_name(self):
        """Gets the job_name of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The job_name of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._job_name

    @job_name.setter
    def job_name(self, job_name):
        """Sets the job_name of this GetApplicationInstanceResponse.


        :param job_name: The job_name of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._job_name = job_name

    @property
    def job_type(self):
        """Gets the job_type of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The job_type of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._job_type

    @job_type.setter
    def job_type(self, job_type):
        """Sets the job_type of this GetApplicationInstanceResponse.


        :param job_type: The job_type of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._job_type = job_type

    @property
    def main_class(self):
        """Gets the main_class of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The main_class of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._main_class

    @main_class.setter
    def main_class(self, main_class):
        """Sets the main_class of this GetApplicationInstanceResponse.


        :param main_class: The main_class of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._main_class = main_class

    @property
    def project_id(self):
        """Gets the project_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The project_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this GetApplicationInstanceResponse.


        :param project_id: The project_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def sql_text(self):
        """Gets the sql_text of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The sql_text of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._sql_text

    @sql_text.setter
    def sql_text(self, sql_text):
        """Sets the sql_text of this GetApplicationInstanceResponse.


        :param sql_text: The sql_text of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._sql_text = sql_text

    @property
    def start_time(self):
        """Gets the start_time of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The start_time of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GetApplicationInstanceResponse.


        :param start_time: The start_time of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The state of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this GetApplicationInstanceResponse.


        :param state: The state of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def user_id(self):
        """Gets the user_id of this GetApplicationInstanceResponse.  # noqa: E501


        :return: The user_id of this GetApplicationInstanceResponse.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this GetApplicationInstanceResponse.


        :param user_id: The user_id of this GetApplicationInstanceResponse.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetApplicationInstanceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetApplicationInstanceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetApplicationInstanceResponse):
            return True

        return self.to_dict() != other.to_dict()
