# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EipForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eip_address': 'str',
        'eip_id': 'str',
        'isp': 'int'
    }

    attribute_map = {
        'eip_address': 'EipAddress',
        'eip_id': 'EipId',
        'isp': 'Isp'
    }

    def __init__(self, eip_address=None, eip_id=None, isp=None, _configuration=None):  # noqa: E501
        """EipForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eip_address = None
        self._eip_id = None
        self._isp = None
        self.discriminator = None

        if eip_address is not None:
            self.eip_address = eip_address
        if eip_id is not None:
            self.eip_id = eip_id
        if isp is not None:
            self.isp = isp

    @property
    def eip_address(self):
        """Gets the eip_address of this EipForListPodOutput.  # noqa: E501


        :return: The eip_address of this EipForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this EipForListPodOutput.


        :param eip_address: The eip_address of this EipForListPodOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def eip_id(self):
        """Gets the eip_id of this EipForListPodOutput.  # noqa: E501


        :return: The eip_id of this EipForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_id

    @eip_id.setter
    def eip_id(self, eip_id):
        """Sets the eip_id of this EipForListPodOutput.


        :param eip_id: The eip_id of this EipForListPodOutput.  # noqa: E501
        :type: str
        """

        self._eip_id = eip_id

    @property
    def isp(self):
        """Gets the isp of this EipForListPodOutput.  # noqa: E501


        :return: The isp of this EipForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this EipForListPodOutput.


        :param isp: The isp of this EipForListPodOutput.  # noqa: E501
        :type: int
        """

        self._isp = isp

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EipForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EipForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EipForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
