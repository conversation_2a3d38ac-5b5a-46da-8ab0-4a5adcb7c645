# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyTransitRouterForwardPolicyEntryAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'priority': 'int',
        'source_cidr_block': 'str',
        'transit_router_forward_policy_entry_id': 'str',
        'transit_router_route_table_id': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'priority': 'Priority',
        'source_cidr_block': 'SourceCidrBlock',
        'transit_router_forward_policy_entry_id': 'TransitRouterForwardPolicyEntryId',
        'transit_router_route_table_id': 'TransitRouterRouteTableId'
    }

    def __init__(self, description=None, priority=None, source_cidr_block=None, transit_router_forward_policy_entry_id=None, transit_router_route_table_id=None, _configuration=None):  # noqa: E501
        """ModifyTransitRouterForwardPolicyEntryAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._priority = None
        self._source_cidr_block = None
        self._transit_router_forward_policy_entry_id = None
        self._transit_router_route_table_id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if priority is not None:
            self.priority = priority
        if source_cidr_block is not None:
            self.source_cidr_block = source_cidr_block
        self.transit_router_forward_policy_entry_id = transit_router_forward_policy_entry_id
        if transit_router_route_table_id is not None:
            self.transit_router_route_table_id = transit_router_route_table_id

    @property
    def description(self):
        """Gets the description of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501


        :return: The description of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.


        :param description: The description of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def priority(self):
        """Gets the priority of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501


        :return: The priority of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.


        :param priority: The priority of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def source_cidr_block(self):
        """Gets the source_cidr_block of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501


        :return: The source_cidr_block of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_cidr_block

    @source_cidr_block.setter
    def source_cidr_block(self, source_cidr_block):
        """Sets the source_cidr_block of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.


        :param source_cidr_block: The source_cidr_block of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._source_cidr_block = source_cidr_block

    @property
    def transit_router_forward_policy_entry_id(self):
        """Gets the transit_router_forward_policy_entry_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501


        :return: The transit_router_forward_policy_entry_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_forward_policy_entry_id

    @transit_router_forward_policy_entry_id.setter
    def transit_router_forward_policy_entry_id(self, transit_router_forward_policy_entry_id):
        """Sets the transit_router_forward_policy_entry_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.


        :param transit_router_forward_policy_entry_id: The transit_router_forward_policy_entry_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_forward_policy_entry_id is None:
            raise ValueError("Invalid value for `transit_router_forward_policy_entry_id`, must not be `None`")  # noqa: E501

        self._transit_router_forward_policy_entry_id = transit_router_forward_policy_entry_id

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501


        :return: The transit_router_route_table_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.


        :param transit_router_route_table_id: The transit_router_route_table_id of this ModifyTransitRouterForwardPolicyEntryAttributesRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_route_table_id = transit_router_route_table_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyTransitRouterForwardPolicyEntryAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyTransitRouterForwardPolicyEntryAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyTransitRouterForwardPolicyEntryAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
