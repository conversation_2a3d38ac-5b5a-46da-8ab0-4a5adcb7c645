# coding: utf-8

"""
    cloud_trail

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LookupConditionForLookupEventsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'lookup_condition_key': 'str',
        'lookup_condition_value': 'str'
    }

    attribute_map = {
        'lookup_condition_key': 'LookupConditionKey',
        'lookup_condition_value': 'LookupConditionValue'
    }

    def __init__(self, lookup_condition_key=None, lookup_condition_value=None, _configuration=None):  # noqa: E501
        """LookupConditionForLookupEventsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._lookup_condition_key = None
        self._lookup_condition_value = None
        self.discriminator = None

        if lookup_condition_key is not None:
            self.lookup_condition_key = lookup_condition_key
        if lookup_condition_value is not None:
            self.lookup_condition_value = lookup_condition_value

    @property
    def lookup_condition_key(self):
        """Gets the lookup_condition_key of this LookupConditionForLookupEventsInput.  # noqa: E501


        :return: The lookup_condition_key of this LookupConditionForLookupEventsInput.  # noqa: E501
        :rtype: str
        """
        return self._lookup_condition_key

    @lookup_condition_key.setter
    def lookup_condition_key(self, lookup_condition_key):
        """Sets the lookup_condition_key of this LookupConditionForLookupEventsInput.


        :param lookup_condition_key: The lookup_condition_key of this LookupConditionForLookupEventsInput.  # noqa: E501
        :type: str
        """

        self._lookup_condition_key = lookup_condition_key

    @property
    def lookup_condition_value(self):
        """Gets the lookup_condition_value of this LookupConditionForLookupEventsInput.  # noqa: E501


        :return: The lookup_condition_value of this LookupConditionForLookupEventsInput.  # noqa: E501
        :rtype: str
        """
        return self._lookup_condition_value

    @lookup_condition_value.setter
    def lookup_condition_value(self, lookup_condition_value):
        """Sets the lookup_condition_value of this LookupConditionForLookupEventsInput.


        :param lookup_condition_value: The lookup_condition_value of this LookupConditionForLookupEventsInput.  # noqa: E501
        :type: str
        """

        self._lookup_condition_value = lookup_condition_value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LookupConditionForLookupEventsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LookupConditionForLookupEventsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LookupConditionForLookupEventsInput):
            return True

        return self.to_dict() != other.to_dict()
