# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MemoryForDescribeInstanceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'encrypted_size': 'int',
        'size': 'int'
    }

    attribute_map = {
        'encrypted_size': 'EncryptedSize',
        'size': 'Size'
    }

    def __init__(self, encrypted_size=None, size=None, _configuration=None):  # noqa: E501
        """MemoryForDescribeInstanceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._encrypted_size = None
        self._size = None
        self.discriminator = None

        if encrypted_size is not None:
            self.encrypted_size = encrypted_size
        if size is not None:
            self.size = size

    @property
    def encrypted_size(self):
        """Gets the encrypted_size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The encrypted_size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._encrypted_size

    @encrypted_size.setter
    def encrypted_size(self, encrypted_size):
        """Sets the encrypted_size of this MemoryForDescribeInstanceTypesOutput.


        :param encrypted_size: The encrypted_size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._encrypted_size = encrypted_size

    @property
    def size(self):
        """Gets the size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this MemoryForDescribeInstanceTypesOutput.


        :param size: The size of this MemoryForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MemoryForDescribeInstanceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MemoryForDescribeInstanceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MemoryForDescribeInstanceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
